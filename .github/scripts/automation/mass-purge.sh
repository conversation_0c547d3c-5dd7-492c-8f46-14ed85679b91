

#!/bin/bash

# This script is used to purge devices in balena cloud.
#
# Usage: ./.github/scripts/automation/mass-purge.sh <device_id_file>
#
# The device_id_file is a file that contains a list of device ids to purge.
# Each line in the file should contain a single device id.
#
# IMPORTANT NOTE: You must delete the generated joblog file before running the script
# again if you want to start from scratch.
#
# Example (purge_ids.txt):
#
# ```
# abc1234567890
# bcd1234567891
# # Comment line.
# efg1234567892
# ```
#
# Example usage:
#
# ```
# ./mass-purge.sh purge_ids.txt
# ```
#
# Resulting files:
#
# * `purge_ids.txt.logs/`: Logs from each device purge operation.
# * `purge_ids.txt.joblog`: This is used to resume a partially completed run;
#   any completed jobs will be stored in this file and will not be re-run.
#
# NOTE: You can run the script multiple times; it will only purge devices that
# have not yet sucessfully been purged.
#
# Optional arguments (env variables):
# - PARALLEL_JOBS: The number of parallel jobs to run. Default is 10.
# - DEVICE_ID_FILE: The file that contains the list of device ids to purge.
#   Default is "devices.txt".
# - FLEET: The fleet to purge devices from. Any device not matching this fleet
#   will error out. Default is "round2pos/hyperion-develop".
# - LOGS_DIR: The directory to store the logs. Default is a variant of the
#   device_id_file name.
#
# Related documentation: https://docs.balena.io/reference/balena-cli/latest/


# https://gist.github.com/mohanpedala/1e2ff5661761d3abd0385e8223e16425
set -e -x -v -u -o pipefail

(
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

usage() {
  echo -e "${RED}Usage: ${0} <device_id_file>${NC}"
  echo -e "${RED}Example: ${0} devices.txt${NC}"
}
################################################################################
DEVICE_ID_FILE=${DEVICE_ID_FILE:-"${1:-}"}
PARALLEL_JOBS=${PARALLEL_JOBS:-"10"}
FLEET=${FLEET:-"round2pos/hyperion-develop"}
LOGS_DIR=${LOGS_DIR:-"$(dirname "${DEVICE_ID_FILE}")/$(basename "${DEVICE_ID_FILE}.logs")"}
################################################################################
if [ -z "${DEVICE_ID_FILE}" ]; then
  echo -e "${RED}DEVICE_ID_FILE is not set.${NC}"
  usage
  exit 1
fi

if [ ! -f "${DEVICE_ID_FILE}" ]; then
  echo -e "${RED}DEVICE_ID_FILE (${DEVICE_ID_FILE}) does not exist.${NC}"
  echo -e "${RED}Please provide a valid device_id_file.${NC}"
  exit 1
fi

if ! command -v parallel &> /dev/null; then
  echo -e "${RED}parallel could not be found.${NC}"
  echo -e "${RED}Please install parallel.${NC}"
  echo -e "${RED}For example, on Ubuntu, you can install it with:${NC}"
  echo -e "${RED}sudo apt-get install parallel${NC}"
  exit 1
fi


if ! command -v jq &> /dev/null; then
  echo -e "${RED}jq could not be found.${NC}"
  echo -e "${RED}Please install jq.${NC}"
  echo -e "${RED}For example, on Ubuntu, you can install it with:${NC}"
  echo -e "${RED}sudo apt-get install jq${NC}"
  exit 1
fi
################################################################################
if ! balena whoami &> /dev/null; then
  echo -e "${YELLOW}Logging in to balena...${NC}"
  balena login
fi
################################################################################

LOGS_DIR=$(realpath "${LOGS_DIR}")


mkdir -p "${LOGS_DIR}"

purge () {
  (
    set -e -u -o pipefail

    GREEN='\033[0;32m'
    YELLOW='\033[0;33m'
    RED='\033[0;31m'
    BLUE='\033[0;34m'
    NC='\033[0m' # No Color

    local DEVICE_ID=$1
    local LOG_FILE="${LOGS_DIR}/${DEVICE_ID}.log"

    DEVICE_FLEET=$(balena device $DEVICE_ID --json | jq -r '.fleet')
    # Check if fleet is not round2pos/hyperion-develop
    if [ "${DEVICE_FLEET}" != "${FLEET}" ]; then
      echo -e "${RED}Skipping device ${DEVICE_ID}, fleet ${DEVICE_FLEET} != ${FLEET}.${NC}"
      return 1
    fi

    balena device purge "${DEVICE_ID}" > "${LOG_FILE}" 2>&1
    echo -e "${GREEN}Purged device ${DEVICE_ID}.${NC}"
  )
}

export -f purge
export FLEET
export LOGS_DIR

echo -e "${BLUE}Purging devices...${NC}"
parallel --eta --tag --results "${LOGS_DIR}" \
  -j ${PARALLEL_JOBS} --resume-failed \
  --joblog ${DEVICE_ID_FILE}.joblog \
  purge {} \
  :::: <(grep -v -E '^\s*$|^\s*#' "${DEVICE_ID_FILE}")


echo -e "${GREEN}All done.${NC}"
)