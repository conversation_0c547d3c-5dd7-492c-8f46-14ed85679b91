#!/bin/bash

(
  # https://gist.github.com/mohanpedala/1e2ff5661761d3abd0385e8223e16425
  set -E -e -u -o pipefail
  shopt -s inherit_errexit

  if tput setaf 1 &>/dev/null; then
    RED=$(tput setaf 1)
    GREEN=$(tput setaf 2)
    BLUE=$(tput setaf 4)
    NC=$(tput sgr0)
  else
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    NC='\033[0m'
  fi

  PROJ_PATH=$(pwd)
  TMP_DIR=$(mktemp -d)
  function cleanup {
    rm -rf "${TMP_DIR}"
  }
  trap cleanup EXIT TERM

  set +u
  source ~/.bashrc
  source ~/.profile
  set -u
  ################################################################################
  # To clean the build caches
  # rm -Rf "${PROJ_PATH}/terminal/gui/front-of-house/build"
  # rm -Rf "${PROJ_PATH}/terminal/gui/back-of-house/build"
  # rm -Rf "${PROJ_PATH}/terminal/gui/activation/build"
  # rm -Rf ~/flutter/cache
  ################################################################################
  # Build the front-of-house and back-of-house.
  BuildFrontOfHouse() {
    cd "${TEST_PROJ_PATH}/terminal/gui/front-of-house"
    flutter clean && flutter pub get --offline || flutter pub get
    flutter build linux --color --debug --no-track-widget-creation
  }
  BuildBackOfHouse() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/terminal/gui/back-of-house"
    flutter clean && flutter pub get --offline || flutter pub get
    flutter build linux --color --debug --no-track-widget-creation
  }
  BuildActivation() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/terminal/gui/activation"
    flutter clean && flutter pub get --offline || flutter pub get
    flutter build linux --color --debug --no-track-widget-creation
  }
  BuildRemoteDeployTool() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/common/tools/deploy"
    go build -o bin/remote-deploy cmd/remote/main.go
    ./bin/remote-deploy --help

    # Start a Balena VM and run the deploy tool against it.
  }
  BuildLocalDeployTool() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/common/tools/deploy"
    go build -o bin/local-deploy cmd/local/main.go
    ./bin/local-deploy --help


  }
  ################################################################################
  # Test model generation.
  TestModelGeneration() {
    set -euo pipefail
    echo -e "${BLUE}Cleaning and getting dependencies${NC}"
    cd "${TEST_PROJ_PATH}/terminal/gui/front-of-house"
    flutter clean && flutter pub get --offline || flutter pub get
    cd "${TEST_PROJ_PATH}/terminal/gui/back-of-house"
    flutter clean && flutter pub get --offline || flutter pub get

    echo -e "${BLUE}Saving git status${NC}"
    cd "${TEST_PROJ_PATH}"
    git status --porcelain >"${TMP_DIR}/git-status-before.txt"

    echo -e "${BLUE}Running model generation${NC}"
    cd "${TEST_PROJ_PATH}"
    parallel -j2 ::: \
      "cd terminal/gui/front-of-house; make run-model-gen" \
      "cd terminal/gui/back-of-house; make run-model-gen"

    echo -e "${BLUE}Checking git status${NC}"
    cd "${TEST_PROJ_PATH}"
    git status --porcelain >"${TMP_DIR}/git-status-after.txt"

    # Error if anything has been modified in the git repo after run-model-gen.
    #
    # Note: if you are doing this on a repo that you are actively changing while
    # this test is running, then this test will be flaky.
    if diff --ignore-space-change \
      --unchanged-line-format="" \
      --old-line-format="" \
      --new-line-format="${RED}%L${NC}" \
      "${TMP_DIR}/git-status-before.txt" \
      "${TMP_DIR}/git-status-after.txt"; then
      echo -e "${GREEN}Success: Files have not been modified after run-model-gen.${NC}"
    else
      echo -e "${RED}Error: Files have been modified after run-model-gen.${NC}"
      echo -e "${RED}Did you forget to run run-model-gen?${NC}"
      echo -e "${BLUE}cd terminal/gui/front-of-house && flutter pub run build_runner build --delete-conflicting-outputs${NC}"
      echo -e "${BLUE}  or${NC}"
      echo -e "${BLUE}cd terminal/gui/front-of-house && make run-model-gen${NC}"
      exit 1
    fi
  }
  ################################################################################
  TestFrontOfHouse() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/terminal/gui/front-of-house"
    echo -e "${BLUE}Cleaning and getting dependencies${NC}"
    flutter clean && flutter pub get --offline || flutter pub get
    flutter test -r expanded --color
  }
  TestBackOfHouse() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}/terminal/gui/back-of-house"
    echo -e "${BLUE}Cleaning and getting dependencies${NC}"
    flutter clean && flutter pub get --offline || flutter pub get
    flutter test -r expanded --color
  }
  BuildBalena() {
    set -euo pipefail
    cd "${TEST_PROJ_PATH}"

    balena build ./terminal \
      --deviceType genericx86-64 \
      --arch amd64 \
      --registry-secrets ./terminal/registry-secrets.yml \
      -m --debug
  }
  RunPrecommit() {
    set -euo pipefail
    echo -e "${BLUE}Running pre-commit${NC}"
    cd "${TEST_PROJ_PATH}"
    pre-commit
  }
  ################################################################################
  # Run all the tests in parallel.

  # This is a utility function that copies the source git repo into a temporary
  # directory and then runs the given job in that temporary directory.
  #
  # This is useful for running tests in parallel without having to worry about
  # the tests modifying the source code, interfering with each other etc.
  RunInWorktree() {
    set -euo pipefail

    local JOB="$1"
    export TEST_PROJ_PATH="${TMP_DIR}/${JOB}-test"
    local TEST_CACHE_PATH="${TMP_DIR}/${JOB}-cache/"
    local ORIGINAL_FLUTTER_ROOT="${FLUTTER_ROOT:-${HOME}/flutter}"
    cd "${TMP_PROJ_PATH}"
    git worktree add --quiet "${TEST_PROJ_PATH}" "${CI_SNAPSHOT_COMMIT}"
    {
      # Remove the test worktree when the script exits.
      trap "git worktree remove --force \"${TEST_PROJ_PATH}\"" RETURN

      # Don't share the flutter pub cache between jobs, because they'll all
      # lock the same directory.
      # export PUB_CACHE="${TEST_CACHE_PATH}/.pub-cache"
      # mkdir -p "${PUB_CACHE}"
      # timed rsync -a --links "${HOME}/.pub-cache/" "${PUB_CACHE}/"

      # Don't share the flutter sdk between jobs, because they'll all lock the
      # same directory.
      # export FLUTTER_ROOT="${TEST_CACHE_PATH}/flutter"
      # mkdir -p "${FLUTTER_ROOT}"
      # echo "Copying flutter sdk to ${FLUTTER_ROOT}"
      # timed rsync -a --links "${ORIGINAL_FLUTTER_ROOT}/" "${FLUTTER_ROOT}/"
      # # time cp -al "${ORIGINAL_FLUTTER_ROOT}/" "${FLUTTER_ROOT}/"
      # export PATH="${FLUTTER_ROOT}/bin:${PATH}"

      cd "${TEST_PROJ_PATH}"
      "${JOB}"
    }
  }

  timed() {
    set -euo pipefail
    /usr/bin/time -f $'COMMAND: %C\nreal %e  user %U  sys %S\n' bash -c "$*"
  }

  TMP_PROJ_PATH="${TMP_DIR}/project-copy"
  
  # Copy everything in the git repo to the temporary directory.
  rsync -a "${PROJ_PATH}/" "${TMP_PROJ_PATH}"
  cd "${TMP_PROJ_PATH}"
  # We need to commit any staged changes to the git repo, so that we can use git
  # worktrees to run the tests in parallel. (`git worktree add ...` will not
  # account for staged changes).
  if ! git diff --cached --quiet; then
    TREE_SHA=$(git write-tree)
    CI_SNAPSHOT_COMMIT=$(git -c user.name=ci -c user.email=ci@nowhere \
                          commit-tree \
                          ${TREE_SHA} \
                          -p HEAD \
                          -m "ci-temp $(date +%s)")
  else
    CI_SNAPSHOT_COMMIT=$(git rev-parse HEAD)
  fi
  export CI_SNAPSHOT_COMMIT          # visible to all parallel jobs

  # Export functions so they're available to parallel subshells
  export -f timed RunInWorktree BuildFrontOfHouse BuildBackOfHouse \
    BuildActivation TestFrontOfHouse TestBackOfHouse TestModelGeneration \
    BuildLocalDeployTool BuildRemoteDeployTool BuildBalena RunPrecommit
  export PROJ_PATH TMP_DIR TMP_PROJ_PATH CI_SNAPSHOT_COMMIT RED GREEN BLUE NC

  echo -e "${BLUE}Precaching${NC}"
  timed flutter --disable-analytics --no-version-check precache --linux --web -v

  echo -e "${BLUE}Precaching some dependencies${NC}"
  for SUBPROJECT in front-of-house back-of-house activation; do
    (cd "${PROJ_PATH}/terminal/gui/${SUBPROJECT}" && flutter pub get)
  done

  # Now run with parallel.
  #
  # params:
  # - `--line-buffer` is used to ensure that the output is not interleaved.
  # - `-j 200%` is the number of jobs to run in parallel, which is 2x the number
  #   of cores on the machine.
  # - `--halt now,fail=1` is used to stop the script if any of the jobs fail.
  # - `--tagstring '| {1} | '` is used to add a tag to the output of each job.
  #   This is useful for debugging.
  # - `--joblog "~/joblog"` is used to save the job log to a file.
  #   This is useful for debugging.
  parallel --tagstring '| {1} | ' --line-buffer --joblog "${HOME}/build-and-test-joblog" \
    --halt now,fail=1 -j 200% RunInWorktree ::: \
    BuildFrontOfHouse \
    BuildBackOfHouse \
    BuildActivation \
    BuildLocalDeployTool \
    BuildRemoteDeployTool \
    TestFrontOfHouse \
    TestBackOfHouse \
    TestModelGeneration \
    RunPrecommit \
    BuildBalena

  cat "${HOME}/build-and-test-joblog"
  ################################################################################
  echo -e "${GREEN}Success: all tests have passed.${NC}"
  ################################################################################
)
