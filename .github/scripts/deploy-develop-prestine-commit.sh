#!/bin/bash
# Make a balena develop deployment from a remote branch; useful because it is a
# clean clone.

# Usage: Commit your work, push it. Then run this script:
#
# ```bash
# BRANCH=branch-name-here COMMIT=commit-sha-here \
#   ./.github/scripts/deploy-develop-prestine-commit.sh
#
# # This will checkout from yourlocal (possibly unpushed) repo.
# REPO=local BRANCH=branch-name-here COMMIT=commit-sha-here \
#   ./.github/scripts/deploy-develop-prestine-commit.sh
# ```

# https://gist.github.com/mohanpedala/1e2ff5661761d3abd0385e8223e16425
set -e -x -v -u -o pipefail


RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m'

BRANCH=${BRANCH:-}
COMMIT=${COMMIT:-}
REPO=${REPO:-**************:Round2POS/hyperion.git}


if [[ -z "$BRANCH" ]]; then
  echo -e "${RED}BRANCH is not set${NC}"

  exit 1
fi

if [[ -z "$COMMIT" ]]; then
  echo -e "${RED}COMMIT is not set${NC}"
  
  exit 1
fi

if [[ "$REPO" == "local" ]]; then
  echo -e "${BLUE}Using current directory as the repository${NC}"
  
  REPO=$(pwd)
elif [[ "$REPO" == "remote" ]]; then
  echo -e "${BLUE}Using current directory's remote as the repository${NC}"
  
  REPO=$(git remote get-url origin)
fi

if [[ -z "$REPO" ]]; then
  echo -e "${RED}REPO is not set${NC}"
  
  exit 1
fi

REPO_NAME=$(basename $REPO .git)

if ! balena whoami &> /dev/null; then
  balena login
fi

TMP_DIR=$(mktemp -d)

trap 'rm -rf $TMP_DIR' EXIT

cd $TMP_DIR

echo -e "${BLUE}Cloning repository${NC}"

git clone --branch "$BRANCH" --single-branch "$REPO"
cd $REPO_NAME

git checkout "${BRANCH}"
git reset --hard "${COMMIT}"

git log -1

echo -e "${BLUE}Setting up git${NC}"

make deploy-develop

# Get the last balena image URL with the  tag realz:me

RELEASE_ID=$(balena release list round2pos/hyperion-develop --json | jq '.[0].id')
URL=$(balena release ${RELEASE_ID} --json | jq '. | "https://dashboard.balena-cloud.com/fleets/\(.belongs_to__application.__id)/releases/\(.id)"')
COMMIT=$(balena release ${RELEASE_ID} --json | jq '.commit')
COMMIT_SHORT=$(echo $COMMIT | cut -c1-8)

echo -e "${GREEN}Deployed ${COMMIT} (${COMMIT_SHORT}) to ${URL}${NC}"
