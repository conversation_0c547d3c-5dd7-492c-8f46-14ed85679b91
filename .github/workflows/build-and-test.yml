# This is a GH action that runs whenever you push. See the Actions tab on the GH
# repo to see the results.
name: Build and Test

on:
  push:
    branches:
      - "**"

jobs:
  build-and-test:
    runs-on: ubuntu-v22.04-c16-m64
    defaults:
      run:
        # This is necessary to load /root/.profile, which has PATH vars e.g for
        # Balena CLI.
        shell: bash --login -eo pipefail {0}
    steps:
      - uses: actions/checkout@v4

      # Disabling this because:
      # 1. It doesn't run faster on GH than just installing it ourselves.
      # 2. We want GH to test our install script so that it remains relevant.
      # - name: Setup Go
      #   uses: actions/setup-go@v5
      #   with:
      #     go-version: 1.23.1

      - name: Add /usr/local/go/bin to PATH
        run: echo "/usr/local/go/bin" >> $GITHUB_PATH

      # Disabling this because:
      # 1. It doesn't run faster on GH than just installing it ourselves.
      # 2. We want GH to test our install script so that it remains relevant.
      # - name: Install Flutter 3.19.4
      #   uses: subosito/flutter-action@v2
      #   with:
      #     flutter-version: '3.19.4'

      # Note: If {Go, Flutter, etc.} are already installed, then this will be a no-op.
      # Note: This simultaneously sets up the test environment and tests our own setup script.
      - name: Setup dev machine
        run: |
          set -e -x -v -u -o pipefail
          TARGET_USER=${USER} bash .github/scripts/setup-dev-machine.sh

      - name: Build and Test
        run: |
          set -e -x -v -u -o pipefail
          bash .github/scripts/build-and-test.sh