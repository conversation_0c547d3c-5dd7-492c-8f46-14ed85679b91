name: "[ELK-STAGING]"

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to run on'
        required: true
        default: develop

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

env:
  ELK_DIRECTORY: deployment/k8s/elk/staging

jobs:
  # This takes the docker image (from the DO docker registry) and deploys it to the DO kubernetes cluster.
  deploy-elk:
    runs-on: ubuntu-v22.04-c16-m64
    steps:
      # setup
      - name: Checkout
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITAL_OCEAN_KUBERNETES_TOKEN}}

      # deploy elk to Kubernetes
      - name: Save DigitalOcean kubeconfig with short-lived credentials
        # This gets a kubeconfig with short-lived credentials.
        #
        # We need such credentials to deploy to the DO kubernetes cluster.
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ vars.DIGITALOCEAN_STAGING_CLUSTER }}
      - name: Deploy elk to DigitalOcean Kubernetes
        # Send the deployment file to the DO kubernetes cluster.
        run: kubectl apply -f ${{ env.ELK_DIRECTORY }}
      - name: Verify elk deployment
        # Wait for the deployment to be ready.
        run: kubectl rollout status deployment/elk --timeout=5m
