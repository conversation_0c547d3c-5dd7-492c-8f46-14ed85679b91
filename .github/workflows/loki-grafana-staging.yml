name: "[LOKI-GRAFANA-STAGING]"

on:
  push:
    branches:
      - develop
    paths:
      - deployment/k8s/loki-grafana/staging/**
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to run on'
        required: true
        default: develop

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

env:
  LOKI_GRAFANA_DEPLOYMENT_FILE: deployment/k8s/loki-grafana/staging/deployment.yml
  LOKI_GRAFANA_INGRESS_FILE: deployment/k8s/loki-grafana/staging/ingress.yml

jobs:
  # This takes the docker image (from the DO docker registry) and deploys it to the DO kubernetes cluster.
  deploy-loki-grafana:
    runs-on: ubuntu-v22.04-c16-m64
    steps:
      # setup
      - name: Checkout
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITAL_OCEAN_KUBERNETES_TOKEN}}

      # deploy loki-grafana to Kubernetes
      - name: Save DigitalOcean kubeconfig with short-lived credentials
        # This gets a kubeconfig with short-lived credentials.
        #
        # We need such credentials to deploy to the DO kubernetes cluster.
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ vars.DIGITALOCEAN_STAGING_CLUSTER }}
      - name: Deploy Loki-Grafana to DigitalOcean Kubernetes
        # Send the deployment file to the DO kubernetes cluster.
        run: |
          kubectl apply -f ${{ env.LOKI_GRAFANA_DEPLOYMENT_FILE }}
          kubectl apply -f ${{ env.LOKI_GRAFANA_INGRESS_FILE }}
      - name: Verify Loki-Grafana deployment
        # Wait for the deployment to be ready.
        run: kubectl rollout status deployment/loki-grafana --timeout=5m
