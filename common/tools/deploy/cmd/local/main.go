package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/r2pos/common/tools/deploy/internal/db"
	"github.com/r2pos/common/tools/deploy/internal/tools"
	"github.com/rs/zerolog/log"
)

const (
	pgRestoreBinPath = "/usr/bin/pg_restore"
	pgIsReadyBinPath = "/usr/bin/pg_isready"
)

type config struct {
	curlBinPath string
	spec        spec

	localPGPort     int
	pgPassword      string
	localHasuraPort int
}

type spec struct {
	dumpPath            string
	netName             string
	terminalSelection   db.TerminalSelection
	keepOtherTerminals  bool
	hyperionProjectPath string
}

func main() {

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	config := parseFlagsOrDie()

	checkDependenciesOrDie(config)
	localDeployOrDie(ctx, config)

}

func parseFlagsOrDie() config {
	config := config{}

	flag.StringVar(&config.curlBinPath, "curl", "/usr/bin/curl", "Path to curl binary")
	flag.StringVar(&config.spec.dumpPath, "dump", "", "Path to dump file")
	flag.StringVar(&config.spec.netName, "net", "", "Network name (e.g. eth0, use ifconfig to list) (required)")
	flag.Var(&config.spec.terminalSelection, "terminal", "Terminal selection")
	flag.BoolVar(&config.spec.keepOtherTerminals, "keep-other-terminals", false, "Keep other terminals")
	flag.StringVar(&config.spec.hyperionProjectPath, "hasura-project-path", "", "Path to hasura directory. If not provided, it will be inferred from the current working directory.")
	flag.IntVar(&config.localPGPort, "local-pg-port", 5432, "Local postgres port")
	flag.StringVar(&config.pgPassword, "pg-password", "asdf", "Postgres password")
	flag.IntVar(&config.localHasuraPort, "local-hasura-port", 8080, "Local hasura port")

	defaultUsage := flag.Usage
	flag.Usage = func() {
		defaultUsage()
		fmt.Fprintf(flag.CommandLine.Output(), "\nExample:\n")
		fmt.Fprintf(flag.CommandLine.Output(), "  go run cmd/main.go \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -dump s3://r2pos-stores/devices/dfbfe3bfad78c7c4ee44fe45fbcafe15/database/backups/2025-05-07T05:00:00.dump \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -terminal leader\n")
	}

	flag.Parse()

	if config.spec.terminalSelection == db.TerminalSelectionNone {
		color.Red("-terminal is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	if config.spec.netName == "" {
		color.Red("-net is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	return config
}

func checkDependenciesOrDie(config config) {
	dependencies := map[string]string{
		"pg_restore": pgRestoreBinPath,
		"pg_isready": pgIsReadyBinPath,
		"curl":       config.curlBinPath,
	}

	for depName, depPath := range dependencies {
		if _, err := exec.LookPath(depPath); err != nil {
			color.Red("%s could not be found at %s. Please install it before continuing.", depName, depPath)
			log.Fatal().Msg("Failed to check dependencies")
		}
	}
}

func localDeployOrDie(ctx context.Context, config config) {
	var err error

	rateLimiter := &tools.RateLimiter{
		Interval: 5 * time.Second,
	}

	localMachineInfo := &tools.MachineInfo{}
	hyperionProjectPath := config.spec.hyperionProjectPath
	if hyperionProjectPath == "" {
		hyperionProjectPath, err = findHyperionProjectPath(ctx)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to find hasura project path")
		}
	}

	deviceInfo, err := tools.GetLocalDeviceInfo(config.spec.netName)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get local device info")
	}

	hasSchema, err := db.DumpHasSchema(
		ctx,
		localMachineInfo,
		config.spec.dumpPath,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to check if dump has schema")
	}

	db.QuickPurgeDatabaseOrDie(
		ctx,
		rateLimiter,
		config.localPGPort,
		config.pgPassword,
		config.localHasuraPort,
		time.Now().Add(5*time.Minute),
	)

	if !hasSchema {
		// We need Hasura to run the migrations to get the schema up to speed.
		db.ApplyHasuraMigrationsViaDockerOrDie(
			ctx,
			localMachineInfo,
			"http://localhost:8080",
			hyperionProjectPath,
			config.localHasuraPort,
			rateLimiter,
			time.Now().Add(5*time.Minute),
		)
	}

	db.RestoreDatabaseOrDie(
		ctx,
		config.spec.dumpPath,
		localMachineInfo,
		time.Now().Add(20*time.Minute),
	)

	db.UpdateSystemDeviceRecordOrDie(
		ctx,
		config.localPGPort,
		deviceInfo,
		config.pgPassword,
		config.spec.terminalSelection,
		config.spec.keepOtherTerminals,
	)
}

func findHyperionProjectPath(ctx context.Context) (string, error) {
	cwd, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to find hyperion path: failed to get current working directory: %w", err)
	}
	// Get the git root directory of cwd
	_, hyperionPath, err := tools.RunLocalCommand(
		ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			Cwd:        cwd,
		},
		[]string{"git", "rev-parse", "--show-toplevel"},
	)
	if err != nil {
		return "", fmt.Errorf("failed to find hyperion path: failed to get git root directory: %w", err)
	}

	hyperionPath = strings.TrimSpace(hyperionPath)

	if hyperionPath == "" {
		return "", fmt.Errorf("failed to find hyperion path: git failed to find root directory")
	}

	if _, err := os.Stat(hyperionPath); os.IsNotExist(err) {
		return "", fmt.Errorf("failed to find hyperion path: %s does not exist", hyperionPath)
	}

	return hyperionPath, nil
}
