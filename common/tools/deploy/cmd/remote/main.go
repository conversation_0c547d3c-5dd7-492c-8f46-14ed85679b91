package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"regexp"
	"strings"
	"syscall"
	"time"

	"github.com/google/uuid"
	"github.com/kballard/go-shellquote"
	"github.com/kr/pretty"
	"github.com/r2pos/common/tools/deploy/internal/db"
	"github.com/r2pos/common/tools/deploy/internal/tools"
	"github.com/rs/zerolog/log"

	"github.com/fatih/color"
)

const (
	postgresServiceName           = "postgres-service"
	hasuraServiceName             = "hasura-service"
	bridgeServiceName             = "bridge-service"
	terminalGUIServiceName        = "terminal-gui"
	replicationServiceName        = "replication-service"
	transferURL                   = "https://transfer.round2pos.com"
	balenaAddSSHKeyDuplicateError = "BalenaRequestError: Request error: \"user\" and \"public_key\" must be unique."
	pgRestoreBinPath              = "/usr/bin/pg_restore"
	pgIsReadyBinPath              = "/usr/bin/pg_isready"
	sshKeygenBinPath              = "/usr/bin/ssh-keygen"
)

type config struct {
	spec             spec
	sshPublicKeyPath string
	curlBinPath      string
}

type spec struct {
	fullFleetSlug string
	balenaID      string

	releaseID string
	dumpURI   *url.URL
	netName   string

	terminalSelection  db.TerminalSelection
	keepOtherTerminals bool
}

// Tunnels represents the active SSH tunnels
type Tunnels struct {
	PostgresCmd *exec.Cmd
	HasuraCmd   *exec.Cmd
	SSHCmd      *exec.Cmd
	PgPort      int
	HasuraPort  int
	SSHPort     int
}

func main() {

	config := parseFlagsOrDie()

	rateLimiter := &tools.RateLimiter{
		Interval: 5 * time.Second,
	}

	// Check required dependencies
	checkDependenciesOrDie(config)

	// Set up context with cancellation for cleanup
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle signals for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// This goroutine should exit with the program, so we don't need to wait for
	// it to finish.
	go func() {
		<-sigChan
		color.Blue("Received termination signal")
		cancel()
	}()

	t0 := time.Now()
	timings := map[string]time.Duration{}

	// Ensure user is logged in
	balenaUser := ensureBalenaLoginOrDie(
		ctx, rateLimiter, time.Now().Add(1*time.Minute),
	)

	balenaToken := getBalenaTokenOrDie()

	config.spec.balenaID = resolveBalenaID(
		ctx,
		rateLimiter,
		config.spec.balenaID,
		balenaToken,
		time.Now().Add(1*time.Minute),
	)

	log.Info().Msgf("Resolved Balena ID: %s", config.spec.balenaID)

	config.spec.releaseID = resolveReleaseID(
		ctx,
		rateLimiter,
		config.spec.releaseID,
		config.spec.fullFleetSlug,
		balenaToken,
		time.Now().Add(1*time.Minute),
	)

	log.Info().Msgf("Resolved Release ID: %s", config.spec.releaseID)

	registerSSHKeyOrDie(
		ctx,
		rateLimiter,
		"DeployKey",
		config.sshPublicKeyPath,
		time.Now().Add(1*time.Minute),
	)

	envVars0, err := getEnvVars(
		ctx,
		rateLimiter,
		config.spec.balenaID,
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get initial environment variables")
	}

	if config.spec.netName == "" {
		var ok bool
		config.spec.netName, ok = envVars0["NETNAME"]
		if !ok {
			log.Fatal().Msg("Failed to get Balena Device Environment Variable NETNAME")
		}
	}

	// Check device exists and get info
	deviceInfo := getDeviceInfoOrDie(
		ctx,
		rateLimiter,
		config.spec.balenaID,
		config.spec.netName,
		time.Now().Add(1*time.Minute),
	)
	log.Printf("Device IP: %s, MAC: %s", deviceInfo.IPAddress, deviceInfo.MACAddress)

	// Set up tunnels
	tunnels := setupTunnelsOrDie(ctx, config.spec.balenaID)
	defer closeTunnels(tunnels)

	//////////////////////////////////////////////////////////////////////////////
	hostConfig, err := getHostConfig(
		ctx,
		rateLimiter,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: "",
			DockerBin:         "balena-engine",
		},
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get host config")
	}

	color.Green("Host config:")
	pretty.Println(hostConfig)

	//////////////////////////////////////////////////////////////////////////////
	err = installBashViaAPK(
		ctx,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: bridgeServiceName,
			DockerBin:         "balena-engine",
		},
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to install bash via APK")
	}

	err = installBestCurl(ctx,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: bridgeServiceName,
			DockerBin:         "balena-engine",
		},
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to install best curl")
	}

	balenaSupervisorAddress, err := getSupervisorAPIServer(
		ctx, rateLimiter,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: bridgeServiceName,
			DockerBin:         "balena-engine",
		},
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get supervisor API server")
	}

	balenaSupervisorAPIKey, err := getSupervisorAPIKey(
		ctx, rateLimiter,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: bridgeServiceName,
			DockerBin:         "balena-engine",
		},
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get supervisor API key")
	}

	//////////////////////////////////////////////////////////////////////////////
	sDeviceInfoMap, err := getSupervisorDeviceInfoMap(
		ctx, rateLimiter,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: "",
			DockerBin:         "balena-engine",
		},
		balenaSupervisorAPIKey,
		balenaSupervisorAddress,
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get supervisor device info")
	}

	color.Blue("sDeviceInfoMap:")
	pretty.Println(sDeviceInfoMap)
	//////////////////////////////////////////////////////////////////////////////
	status, err := getStateEngineStatus(
		ctx, rateLimiter,
		&tools.MachineInfo{
			SSHPort:           tunnels.SSHPort,
			SSHUser:           balenaUser,
			DockerServiceName: "",
			DockerBin:         "balena-engine",
		},
		balenaSupervisorAddress,
		balenaSupervisorAPIKey,
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get state engine status")
	}
	color.Blue("State engine status:")
	pretty.Println(status)
	//////////////////////////////////////////////////////////////////////////////
	// Set device environment variables
	setDeviceEnvironmentVarsOrDie(
		ctx, rateLimiter, config.spec.balenaID, config.spec.netName, time.Now().Add(1*time.Minute),
	)
	if config.spec.releaseID != "" {
		pinT0 := time.Now()
		timings["pinRelease.Start"] = pinT0.Sub(t0)
		pinReleaseOrDie(
			ctx, rateLimiter, config.spec.balenaID, config.spec.releaseID,
			time.Now().Add(1*time.Minute),
		)
		waitForReleaseOrDie(
			ctx, rateLimiter, config.spec.balenaID, tunnels.SSHPort, balenaUser,
			balenaSupervisorAPIKey,
			balenaSupervisorAddress,
			config.spec.releaseID,
			time.Now().Add(15*time.Minute),
		)
		timings["pinRelease.End"] = time.Since(t0)
		timings["pinRelease.Duration"] = time.Now().Sub(pinT0)
	}

	envVars, err := getEnvVars(
		ctx,
		rateLimiter,
		config.spec.balenaID,
		time.Now().Add(1*time.Minute),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get environment variables")
	}

	pgPassword, ok := envVars["POSTGRES_PASSWORD"]
	if !ok {
		log.Fatal().Msg("Failed to get Balena Device Environment Variable POSTGRES_PASSWORD")
	}

	AWSAccessKey, ok := envVars["AWS_ACCESS_KEY"]
	if !ok {
		log.Fatal().Msg("Failed to get Balena Device Environment Variable AWS_ACCESS_KEY")
	}
	AWSSecretKey, ok := envVars["AWS_SECRET_KEY"]
	if !ok {
		log.Fatal().Msg("Failed to get Balena Device Environment Variable AWS_SECRET_KEY")
	}

	// Purge device and restart services
	if config.spec.dumpURI != nil {
		{
			purgeDeviceT0 := time.Now()
			timings["purgeDevice.Start"] = purgeDeviceT0.Sub(t0)
			purgeDeviceOrDie(
				ctx,
				rateLimiter,
				config.spec.balenaID,
				time.Now().Add(1*time.Minute),
			)
			waitForDeviceLockOrDie(
				ctx,
				rateLimiter,
				&tools.MachineInfo{
					SSHPort:           tunnels.SSHPort,
					SSHUser:           balenaUser,
					DockerServiceName: "",
					DockerBin:         "balena-engine",
				},
				balenaSupervisorAPIKey,
				time.Now().Add(1*time.Minute),
			)
			time.Sleep(5 * time.Second)
			timings["purgeDevice.End"] = time.Since(t0)
			timings["purgeDevice.Duration"] = time.Now().Sub(purgeDeviceT0)
		}
		{
			restartServicesT0 := time.Now()
			timings["restartServices.Start"] = restartServicesT0.Sub(t0)
			restartServicesOrDie(
				ctx,
				rateLimiter,
				config.spec.balenaID,
				[]string{postgresServiceName, hasuraServiceName},
				time.Now().Add(1*time.Minute),
			)
			waitForDeviceLockOrDie(
				ctx,
				rateLimiter,
				&tools.MachineInfo{
					SSHPort:           tunnels.SSHPort,
					SSHUser:           balenaUser,
					DockerServiceName: "",
					DockerBin:         "balena-engine",
				},
				balenaSupervisorAPIKey,
				time.Now().Add(1*time.Minute),
			)
			timings["restartServices.End"] = time.Since(t0)
			timings["restartServices.Duration"] = time.Now().Sub(restartServicesT0)
		}
	} // end if config.spec.dumpURI != nil

	{
		waitForPostgresT0 := time.Now()
		timings["waitForPostgres.Start"] = waitForPostgresT0.Sub(t0)
		pgDeadline := time.Now().Add(2 * time.Minute)
		waitForPostgresOrDie(ctx, rateLimiter, tunnels.PgPort, pgDeadline)
		timings["waitForPostgres.End"] = time.Since(t0)
		timings["waitForPostgres.Duration"] = time.Now().Sub(waitForPostgresT0)
	}

	{
		waitForHasuraT0 := time.Now()
		timings["waitForHasura.Start"] = waitForHasuraT0.Sub(t0)
		hasuraDeadline := time.Now().Add(2 * time.Minute)
		db.WaitForHasuraOrDie(ctx, rateLimiter, tunnels.HasuraPort, hasuraDeadline)
		timings["waitForHasura.End"] = time.Since(t0)
		timings["waitForHasura.Duration"] = time.Now().Sub(waitForHasuraT0)
	}

	// Restore database
	if config.spec.dumpURI != nil {
		restoreDatabaseT0 := time.Now()
		timings["restoreDatabase.Start"] = restoreDatabaseT0.Sub(t0)

		dumpURI := *config.spec.dumpURI
		// Copy the dump file to the device
		dstPath := fmt.Sprintf(
			"/root/%s",
			filepath.Base(dumpURI.Path),
		)

		err := transferFile(
			ctx,
			config.curlBinPath,
			dumpURI,
			&tools.MachineInfo{
				SSHPort:           tunnels.SSHPort,
				SSHUser:           balenaUser,
				DockerServiceName: postgresServiceName,
				DockerBin:         "balena-engine",
			},
			dstPath,
			AWSAccessKey,
			AWSSecretKey,
			time.Now().Add(20*time.Minute),
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to transfer database dump file to device")
		}

		// Install pg client 17
		err = db.InstallPGClient17(ctx,
			&tools.MachineInfo{
				SSHPort:           tunnels.SSHPort,
				SSHUser:           balenaUser,
				DockerServiceName: postgresServiceName,
				DockerBin:         "balena-engine",
			},
			time.Now().Add(20*time.Minute),
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to install pg client 17")
		}

		db.RestoreDatabaseOrDie(
			ctx,
			dstPath,
			&tools.MachineInfo{
				SSHPort:           tunnels.SSHPort,
				SSHUser:           balenaUser,
				DockerServiceName: postgresServiceName,
				DockerBin:         "balena-engine",
			},
			time.Now().Add(20*time.Minute),
		)
		timings["restoreDatabase.End"] = time.Since(t0)
		timings["restoreDatabase.Duration"] = time.Now().Sub(restoreDatabaseT0)
	}

	{
		updateSystemDeviceRecordT0 := time.Now()
		timings["updateSystemDeviceRecord.Start"] = updateSystemDeviceRecordT0.Sub(t0)
		// Update system device record
		db.UpdateSystemDeviceRecordOrDie(
			ctx,
			tunnels.PgPort,
			deviceInfo,
			pgPassword,
			config.spec.terminalSelection,
			config.spec.keepOtherTerminals,
		)
		timings["updateSystemDeviceRecord.End"] = time.Since(t0)
		timings["updateSystemDeviceRecord.Duration"] = time.Now().Sub(updateSystemDeviceRecordT0)
	}

	{
		restartConfigDependentServicesT0 := time.Now()
		timings["restartConfigDependentServices.Start"] = restartConfigDependentServicesT0.Sub(t0)
		// Restart config-dependent services
		restartServicesOrDie(ctx, rateLimiter, config.spec.balenaID,
			[]string{
				terminalGUIServiceName,
				replicationServiceName,
				bridgeServiceName,
			},
			time.Now().Add(1*time.Minute),
		)
		timings["restartConfigDependentServices.End"] = time.Since(t0)
		timings["restartConfigDependentServices.Duration"] = time.Now().Sub(restartConfigDependentServicesT0)
	}

	color.Green("Done. Took %s", time.Since(t0))

	for k, v := range timings {
		color.Green("%s: %s", k, v)
	}
}

func parseFlagsOrDie() config {
	config := config{}

	var dumpURI string
	flag.StringVar(&config.spec.fullFleetSlug, "fleet", "", "Full fleet slug, e.g. round2pos/hyperion-develop (required)")
	flag.StringVar(&config.spec.balenaID, "device", "", "Balena device ID (required)")
	flag.StringVar(&config.spec.releaseID, "release", "", "Release ID to set on device (Get this from the Balena release page) (default is to not set a release)")
	flag.StringVar(&dumpURI, "dump", "", "Path to database dump file (can be an s3 link or a local file path) (default is to not restore the database)")
	flag.StringVar(&config.curlBinPath, "curl", "/usr/bin/curl", "Path to curl binary")
	flag.StringVar(&config.sshPublicKeyPath, "ssh-key", "", "Path to SSH public key (required)")
	flag.Var(&config.spec.terminalSelection, "terminal", "Terminal selection (first, leader) (required)")
	flag.BoolVar(&config.spec.keepOtherTerminals, "keep-other-terminals", false, "Keep other terminals (default is to clear them)")
	flag.StringVar(&config.spec.netName, "net", "", "Network name (default is to not set a network name)")

	defaultUsage := flag.Usage
	flag.Usage = func() {
		defaultUsage()
		fmt.Fprintf(flag.CommandLine.Output(), "\nExample:\n")
		fmt.Fprintf(flag.CommandLine.Output(), "  go run cmd/main.go \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -device a1ac3b3e204bf8b0370bd3502659e5f2 \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -release 8973391d788f7768fc2e129672dbd62e \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -dump s3://r2pos-stores/devices/dfbfe3bfad78c7c4ee44fe45fbcafe15/database/backups/2025-05-07T05:00:00.dump \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -ssh-key ~/.ssh/id_ed25519.pub \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -terminal leader \\\n")
		fmt.Fprintf(flag.CommandLine.Output(), "    -keep-other-terminals\n")
	}

	// Parse the flags
	flag.Parse()

	if config.spec.fullFleetSlug == "" {
		color.Red("-fleet is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	// Check required flags
	if config.spec.balenaID == "" {
		color.Red("-device is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	if config.sshPublicKeyPath == "" {
		color.Red("-ssh-key is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	if config.spec.terminalSelection == db.TerminalSelectionNone {
		color.Red("-terminal is required")
		flag.Usage()
		log.Fatal().Msg("Failed to parse flags")
	}

	if dumpURI != "" {
		dumpURI, err := url.Parse(dumpURI)
		if err != nil {
			color.Red("-dump must be a valid URL or local file path")
			flag.Usage()
			log.Fatal().Msg("Failed to parse flags")
		}
		if dumpURI.Scheme == "" {
			dumpURI.Scheme = "file"
		}
		config.spec.dumpURI = dumpURI
	}

	return config
}

type deviceResponseStruct struct {
	D []struct {
		ID   int    `json:"id"`
		UUID string `json:"uuid"`
	} `json:"d"`
}

func resolveBalenaID(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	balenaToken string,
	deadline time.Time,
) string {

	// First check if balenaID is a uuid (no dashes)
	if _, err := uuid.Parse(balenaID); err == nil {
		return balenaID
	}

	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	// curl -H "Authorization: Bearer $TOKEN" \
	// "https://api.balena-cloud.com/v7/device?\$filter=device_name%20eq%20'aa_R2-TD-TABLET-DEV-3-AF'&\$select=id,uuid"

	// Use url library to parse the url
	u, err := url.Parse("https://api.balena-cloud.com/v7/device")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to parse url")
	}

	rawQuery := fmt.Sprintf(
		"$filter=device_name%%20eq%%20'%s'&$select=id,uuid",
		// only escapes quotes, spaces, etc.—*not* the $
		url.PathEscape(balenaID),
	)

	u.RawQuery = rawQuery

	// Use http library to make the request
	req, err := http.NewRequestWithContext(
		cmdCtx,
		"GET",
		u.String(),
		nil,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create request")
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", balenaToken))

	// Make the request
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to make request")
	}

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to read response body")
	}
	// {
	// 	"d": [
	// 		{
	// 			"id": 12981109,
	// 			"uuid": "9e2aeb9653823a3cff9bf86ee545e176"
	// 		}
	// 	]
	// }
	// Parse the response body
	var devices deviceResponseStruct
	err = json.Unmarshal(body, &devices)
	if err != nil {
		log.Fatal().Err(err).
			Str("body", string(body)).
			Msg("Failed to resolve Balena ID: Failed to parse response body")
	}

	if len(devices.D) == 0 {
		log.Fatal().Msg("Failed to resolve Balena ID: No devices found")
	}

	if len(devices.D) > 1 {
		log.Fatal().Msgf("Failed to resolve Balena ID: Found %d devices for %s", len(devices.D), balenaID)
	}

	return devices.D[0].UUID
}

func escapeODataValue(v string) string {
	// 1) QueryEscape gives us %2B for “+”
	// 2) Change its “+” (spaces) back to %20
	return strings.ReplaceAll(url.QueryEscape(v), "+", "%20")
}

// Helps build queries for the Balena API filters.
//
// The Balena API uses OData queries, which are a bit awkward to work with.
// This function helps build them.
//
// Example url:
//
//	https://api.balena-cloud.com/v7/device?$filter=device_name%20eq%20'aa_R2-TD-TABLET-DEV-3-AF'&$select=id,uuid
//
// becomes:
//
//	buildODataQuery(map[string]string{"$filter": "device_name eq 'aa_R2-TD-TABLET-DEV-3-AF'", "$select": "id,uuid"})
//
// which becomes:
//
//	"$filter=device_name%%20eq%%20'aa_R2-TD-TABLET-DEV-3-AF'&$select=id,uuid"
func buildODataQuery(kvs map[string]string) string {
	first := true
	var b strings.Builder
	for k, v := range kvs {
		if !first {
			b.WriteByte('&')
		}
		first = false
		b.WriteString(k) // keep $filter / $select exactly as given
		b.WriteByte('=')
		b.WriteString(url.PathEscape(v)) // encode *only* the value
	}
	return b.String()
}

type releaseResponseStruct struct {
	D []struct {
		Commit     string `json:"commit"`
		RawVersion string `json:"raw_version"`
	} `json:"d"`
}

func resolveReleaseID(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	releaseID string,
	fullFleetSlug string,
	balenaToken string,
	deadline time.Time,
) string {

	if releaseID == "" {
		return ""
	}

	// first check if releaseID is a hash (e.g 8a6563fdc2595d0133832abd8800f972).
	//
	// if caller already passed a 32-char commit hash, just return it
	if ok, _ := regexp.MatchString(`^[0-9a-f]{32}$`, releaseID); ok {
		return releaseID
	}

	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	// FULL_SLUG='round2pos/hyperion-develop'
	// curl -s -H "Authorization: Bearer $TOKEN" \
	// "https://api.balena-cloud.com/v7/release?\
	// \$filter=belongs_to__application/slug%20eq%20%27${FULL_SLUG}%27%20and%20raw_version%20eq%20%27${VER}%27&\
	// \$select=commit,raw_version" \
	// | jq .

	u, err := url.Parse("https://api.balena-cloud.com/v7/release")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to resolve Release ID: Failed to parse url")
	}

	filter := fmt.Sprintf(
		"belongs_to__application/slug eq '%s' and raw_version eq '%s'",
		url.PathEscape(fullFleetSlug),
		url.PathEscape(releaseID), // “+” ⇒ %2B automatically
	)

	// *** hand-assemble, $filter first ***
	u.RawQuery = "$filter=" + filter + "&$select=commit,raw_version"

	// Use http library to make the request
	req, err := http.NewRequestWithContext(
		cmdCtx,
		"GET",
		u.String(),
		nil,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to resolve Release ID: Failed to create request")
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", balenaToken))

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to resolve Release ID: Failed to make request")
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to resolve Release ID: Failed to read response body")
	}
	// {
	// 	"d": [
	// 		{
	// 			"commit": "8a6563fdc2595d0133832abd8800f972",
	// 			"raw_version": "0.0.0+rev2006"
	// 		}
	// 	]
	// }

	var releases releaseResponseStruct
	err = json.Unmarshal(body, &releases)
	if err != nil {
		log.Fatal().Err(err).
			Str("body", string(body)).
			Msg("Failed to resolve Release ID: Failed to parse response body")
	}

	if len(releases.D) == 0 {
		log.Fatal().Msg("Failed to resolve Release ID: No releases found")
	}

	if len(releases.D) > 1 {
		log.Fatal().Msgf("Failed to resolve Release ID: Found %d releases for %s", len(releases.D), releaseID)
	}

	return releases.D[0].Commit
}

func checkDependenciesOrDie(config config) {
	dependencies := map[string]string{
		"pg_restore": pgRestoreBinPath,
		"pg_isready": pgIsReadyBinPath,
		"curl":       config.curlBinPath,
		"ssh-keygen": sshKeygenBinPath,
	}

	for depName, depPath := range dependencies {
		if _, err := exec.LookPath(depPath); err != nil {
			color.Red("%s could not be found at %s. Please install it before continuing.", depName, depPath)
			log.Fatal().Msg("Failed to check dependencies")
		}
	}
}

func ensureBalenaLoginOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	deadline time.Time,
) string {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	// Example whoami output:
	// ```
	// $ balena whoami
	// == ACCOUNT INFORMATION
	// USERNAME: g_my_name
	// EMAIL:    <EMAIL>
	// URL:      balena-cloud.com
	// ```
	//
	// It fails with non-zero exit code if not logged in.
	code, output, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0, 1},
		},
		[]string{"balena", "whoami"},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to check if balena is logged in")
	}
	if code == 0 {
		username, err := extractBalenaUsername(output)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to extract Balena username")
		}
		return username
	}
	if code == 1 {
		color.Red("You need to run `balena login` to continue.")
		log.Fatal().Msg("Please run `balena login` to continue.")
	}
	log.Fatal().Msg("Failed to check if balena is logged in")
	return ""
}

func getBalenaTokenOrDie() string {
	// Get the token from ~/.balena/token
	token, err := os.ReadFile(filepath.Join(os.Getenv("HOME"), ".balena", "token"))
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to read balena token")
	}
	tokenStr := strings.TrimSpace(string(token))
	if tokenStr == "" {
		log.Fatal().Msg("Balena token is empty")
	}
	return tokenStr
}

// Gets the supervisor API server address from the device.
//
// The supervisor is a hidden service that runs on the Balena device, and it
// has an API to control the device.
//
// Balena defines BALENA_SUPERVISOR_ADDRESS in certain services that are given
// access to the supervisor.
//
// We use the bridge service to get the supervisor API server address.
func getSupervisorAPIServer(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	deadline time.Time,
) (string, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, output, err := tools.RunBash(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		"echo $BALENA_SUPERVISOR_ADDRESS",
	)
	if err != nil {
		return "", fmt.Errorf("failed to get supervisor API server: %w", err)
	}
	output = strings.TrimSpace(output)
	return output, nil
}

// Gets the supervisor API key from the device.
//
// The supervisor API key is used to authenticate requests to the supervisor API.
//
// We use the bridge service to get the supervisor API key.
//
// See getSupervisorAPIServer for more details.
func getSupervisorAPIKey(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	deadline time.Time,
) (string, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, output, err := tools.RunBash(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		"echo $BALENA_SUPERVISOR_API_KEY",
	)
	if err != nil {
		return "", fmt.Errorf("failed to get supervisor API key: %w", err)
	}
	output = strings.TrimSpace(output)
	return output, nil
}

// Gets the device info from the supervisor API.
//
// The device info is a map of device properties.
//
// We use the bridge service to get the device info.
//
// See getSupervisorAPIServer for more details.
//
// See <https://www.balena.io/docs/reference/supervisor-api/> for more details.
func getSupervisorDeviceInfoMap(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	balenaSupervisorAPIKey string,
	balenaSupervisorAddress string,
	deadline time.Time,
) (map[string]interface{}, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, output, err := tools.RunCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		[]string{
			"curl",
			"-X",
			"GET",
			"--header",
			"Content-Type:application/json",
			fmt.Sprintf("%s/v1/device?apikey=%s",
				balenaSupervisorAddress,
				balenaSupervisorAPIKey,
			),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get device info: %w", err)
	}

	var deviceInfoMap map[string]interface{}
	if err := json.Unmarshal([]byte(output), &deviceInfoMap); err != nil {
		log.Error().
			Err(err).
			Str("output", output).
			Msg("Failed to parse device info")
		return nil, fmt.Errorf("failed to parse device info: %w", err)
	}
	return deviceInfoMap, nil
}

// Gets the device info from the device.
//
// The device info is a map of device properties.
//
// We use the balena CLI to get the device info.
func getDeviceInfoMap(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	deadline time.Time,
) (map[string]interface{}, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, output, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{"balena", "device", balenaID, "--json"},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get device info: %w", err)
	}

	var deviceInfoMap map[string]interface{}
	if err := json.Unmarshal([]byte(output), &deviceInfoMap); err != nil {
		log.Error().
			Err(err).
			Str("output", output).
			Msg("Failed to parse device info")
		return nil, fmt.Errorf("failed to parse device info: %w", err)
	}
	return deviceInfoMap, nil
}

type deviceInfoResp struct {
	IPAddress  string `json:"ip_address"`
	MACAddress string `json:"mac_address"`
}

func getDeviceInfoOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	netName string,
	deadline time.Time,
) tools.DeviceInfo {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, output, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{"balena", "device", balenaID, "--json"},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get device info")
	}

	var deviceInfo deviceInfoResp
	if err := json.Unmarshal([]byte(output), &deviceInfo); err != nil {
		log.Error().
			Err(err).
			Str("output", output).
			Msg("Failed to parse device info")
		log.Fatal().Err(err).Msg("Failed to parse device info")
	}

	// Convert MAC address to lowercase
	deviceInfo.MACAddress = strings.ToLower(deviceInfo.MACAddress)

	return tools.DeviceInfo{
		IPAddress:  deviceInfo.IPAddress,
		MACAddress: deviceInfo.MACAddress,
		NetName:    netName,
	}
}

// Sets a device environment variable.
//
// We use the balena CLI to set the environment variable.
func setDeviceEnvironmentVarOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	key, value, balenaID string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, _, err := tools.RunLocalCommand(cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstStreamStderr,
				tools.OutSrcStdout: tools.OutDstStreamStdout,
			},
			ValidCodes: []int{0},
		},
		[]string{"balena", "env", "set", key, value, "--device", balenaID},
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to set environment variable %s", key)
	}
}

// Sets a device environment variables.
//
// We use the balena CLI to set the environment variables.
func setDeviceEnvironmentVarsOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	netName string,
	deadline time.Time,
) {
	// Get device info
	deviceInfo := getDeviceInfoOrDie(
		ctx,
		rateLimiter,
		balenaID,
		netName,
		deadline,
	)

	if deviceInfo.IPAddress == "" {
		log.Fatal().Msg("Safety check failed: IP address is empty")
	}

	if deviceInfo.MACAddress == "" {
		log.Fatal().Msg("Safety check failed: MAC address is empty")
	}

	if deviceInfo.NetName == "" {
		log.Fatal().Msg("Safety check failed: net name is empty")
	}

	envVars := map[string]string{
		"NETIP":   deviceInfo.IPAddress,
		"NETMAC":  deviceInfo.MACAddress,
		"NETNAME": deviceInfo.NetName,
	}

	for key, value := range envVars {
		setDeviceEnvironmentVarOrDie(
			ctx,
			rateLimiter,
			key,
			value,
			balenaID,
			deadline,
		)
	}
}

// Pins a release to a device.
//
// We use the balena CLI to pin the release.
func pinReleaseOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID, releaseID string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, _, err := tools.RunLocalCommand(cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{"balena", "device", "pin", balenaID, releaseID},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to pin release")
	}
}

// Waits for a release to be pinned to a device.
//
// We use the balena CLI to wait for the release to be pinned.
func waitForReleaseOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	localSSHPort int,
	balenaUser string,
	balenaSupervisorAPIKey string,
	balenaSupervisorAddress string,
	releaseID string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	color.Blue("Waiting for release to be pinned...")
	t0 := time.Now()

	for time.Now().Before(deadline) {
		infoMap, err := getDeviceInfoMap(cmdCtx, rateLimiter, balenaID, deadline)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to get device info")
		}
		commit := infoMap["commit"].(string)

		if commit == releaseID {
			color.Green("Release pinned successfully in %s", time.Since(t0))
			return
		}

		color.Blue("Waiting for release to be pinned... %s", time.Since(t0))
		pretty.Println(infoMap)

		time.Sleep(1 * time.Second)
	}

	color.Red("Timeout waiting for release to be pinned in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for release to be pinned")
}

// Purges a device.
//
// We use the balena CLI to purge the device.
func purgeDeviceOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	t0 := time.Now()
	for time.Now().Before(deadline) {
		color.Blue("Purging device...")
		cmdCtx, cancel := context.WithDeadline(ctx, deadline)
		defer cancel()
		code, output, err := tools.RunLocalCommand(
			cmdCtx,
			tools.RunCommandOptions{
				Output: map[tools.OutSrc]tools.OutDst{
					tools.OutSrcStderr: tools.OutDstStreamStderr,
					tools.OutSrcStdout: tools.OutDstStreamStdout,
				},
				ValidCodes: []int{0, 1},
			},
			[]string{"balena", "device", "purge", "--debug", balenaID},
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to purge device")
		}
		if code == 1 {
			if strings.Contains(output, "BalenaSupervisorLockedError: Supervisor locked") {
				color.Red("Device is locked, trying again")
				time.Sleep(1 * time.Second)
				continue
			}
			log.Fatal().Msgf("Failed to purge device: %s", output)
		}
		color.Green("Purged device in %s", time.Since(t0))
		return
	}
	color.Red("Timeout waiting for device to be purged in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for device to be purged")
}

// Gets the host config from the device.
//
// We use the balena CLI to get the host config.
func getHostConfig(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	deadline time.Time,
) (map[string]interface{}, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, configJSON, err := tools.RunCommand(cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		[]string{"cat", "/mnt/boot/config.json"},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get host config: %w", err)
	}

	var hostConfig map[string]interface{}
	if err := json.Unmarshal([]byte(configJSON), &hostConfig); err != nil {
		log.Error().
			Err(err).
			Str("output", configJSON).
			Msg("Failed to parse host config as JSON")
		return nil, fmt.Errorf("failed to get host config: failed to parse host config as JSON: %w", err)
	}
	return hostConfig, nil
}

// Gets the state engine status from the device.
//
// We use the balena CLI to get the state engine status.
func getStateEngineStatus(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	balenaSupervisorAddress string,
	balenaSupervisorAPIKey string,
	deadline time.Time,
) (map[string]interface{}, error) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, output, err := tools.RunCommand(cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstStreamStderr,
				tools.OutSrcStdout: tools.OutDstReturn,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		[]string{
			"curl",
			"-fs",
			fmt.Sprintf("%s/v2/state/status?apikey=%s",
				balenaSupervisorAddress,
				balenaSupervisorAPIKey,
			),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to check device state engine lock: %w", err)
	}

	var status map[string]interface{}
	if err := json.Unmarshal([]byte(output), &status); err != nil {
		log.Error().
			Err(err).
			Str("output", output).
			Msg("Failed to parse device state engine status")
		return nil, fmt.Errorf("failed to parse device state engine status: %w", err)
	}

	return status, nil
}

// Checks if the device is state engine locked.
//
// We use the supervisor API to check if the device is state engine locked.
func checkDeviceStateEngineLock(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	balenaSupervisorAddress string,
	balenaSupervisorAPIKey string,
	deadline time.Time,
) (bool, error) {
	rateLimiter.Wait()

	status, err := getStateEngineStatus(
		ctx,
		rateLimiter,
		m,
		balenaSupervisorAddress,
		balenaSupervisorAPIKey,
		deadline,
	)
	if err != nil {
		return false, fmt.Errorf("failed to get device state engine status: %w", err)
	}

	appState, ok := status["appState"].(string)
	if !ok {
		return false, fmt.Errorf("failed to get device state engine status: device state engine status is not a string")
	}

	return appState == "applied", nil
}

// Checks if the device is file locked.
//
// We use the balena CLI to check if the device is file locked.
func checkDeviceFileLock(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	deadline time.Time,
) (bool, error) {
	rateLimiter.Wait()

	// balena ssh "$uuid" 'test -e /tmp/balena/updates.lock'
	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	code, _, err := tools.RunCommand(cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstStreamStderr,
				tools.OutSrcStdout: tools.OutDstStreamStdout,
			},
			ValidCodes: []int{0, 1},
			RetryCodes: []int{255},
		},
		m,
		[]string{"test", "-e", "/tmp/balena/updates.lock"},
	)
	if err != nil {
		return false, fmt.Errorf("failed to check device lock: %w", err)
	}
	if code == 0 {
		return true, nil
	}
	return false, nil
}

// Waits for a device lock.
//
// We use the balena CLI to check if the device is file locked.
func waitForDeviceLockOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	m *tools.MachineInfo,
	balenaSupervisorAPIKey string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	color.Blue("Waiting for device lock...")
	t0 := time.Now()
	for time.Now().Before(deadline) {
		fileLocked, err := checkDeviceFileLock(
			ctx,
			rateLimiter,
			m,
			deadline,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to check device lock")
		}
		if fileLocked {
			color.Red("Device is file locked at %s", time.Since(t0))
			time.Sleep(1 * time.Second)
			continue
		}
		// TODO: try reenabling this
		// stateEngineLocked, err := checkDeviceStateEngineLock(ctx, balenaSupervisorAPIKey, sshPort, sshUser, deadline)
		// if err != nil {
		// 	log.Fatal().Err(err).Msg("Failed to check device state engine lock")
		// }
		// if stateEngineLocked {
		// 	color.Red("Device is engine locked at %s", time.Since(t0))
		// 	time.Sleep(1 * time.Second)
		// 	continue
		// }
		color.Green("Device is unlocked in %s", time.Since(t0))
		return
	}
	color.Red("Timeout waiting for device lock in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for device lock")
}

// Restarts services on a device.
//
// We use the balena CLI to restart the services.
func restartServicesOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	services []string,
	deadline time.Time,
) {
	rateLimiter.Wait()

	t0 := time.Now()
	for time.Now().Before(deadline) {
		servicesArg := strings.Join(services, ",")
		color.Blue("Restarting services: %s", servicesArg)
		cmdCtx, cancel := context.WithDeadline(ctx, deadline)
		defer cancel()
		code, output, err := tools.RunLocalCommand(cmdCtx,
			tools.RunCommandOptions{
				Output: map[tools.OutSrc]tools.OutDst{
					tools.OutSrcStderr: tools.OutDstReturn,
					tools.OutSrcStdout: tools.OutDstStreamStdout,
				},
				ValidCodes: []int{0, 1},
			},
			[]string{"balena", "device", "restart", "--debug", balenaID, "--service", servicesArg},
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to restart services")
		}
		if code == 1 {
			if strings.Contains(output, "BalenaSupervisorLockedError: Supervisor locked") {
				color.Red("Device is locked, trying again")
				time.Sleep(1 * time.Second)
				continue
			}
			color.Red("Failed to restart services: %s", output)
			log.Fatal().Msg("Failed to restart services")
		}

		color.Green("Restarted services in %s", time.Since(t0))
		return
	}
	color.Red("Timeout waiting for services to restart in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for services to restart")
}

func findFreePort() (int, error) {
	addr, err := net.ResolveTCPAddr("tcp", "localhost:0")
	if err != nil {
		return 0, err
	}

	l, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return 0, err
	}
	defer l.Close()
	return l.Addr().(*net.TCPAddr).Port, nil
}

// Forward a bunch of ports from the device to the local machine.
func setupTunnelsOrDie(
	ctx context.Context,
	balenaID string,
) Tunnels {
	color.Blue("Setting up tunnels...")
	t0 := time.Now()

	pgPort, err := findFreePort()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to find free port for Postgres")
	}

	hasuraPort, err := findFreePort()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to find free port for Hasura")
	}

	sshPort, err := findFreePort()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to find free port for SSH")
	}

	// Start Postgres tunnel
	pgCmdArgs := []string{"balena", "device", "tunnel", balenaID, "-p", fmt.Sprintf("5432:%d", pgPort)}
	color.Blue("Starting Postgres tunnel: %s", shellquote.Join(pgCmdArgs...))
	pgCmd := exec.CommandContext(ctx, pgCmdArgs[0], pgCmdArgs[1:]...)
	pgCmd.Stderr = os.Stderr
	if err := pgCmd.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start Postgres tunnel")
	}

	// Start Hasura tunnel
	hasuraCmdArgs := []string{"balena", "device", "tunnel", balenaID, "-p", fmt.Sprintf("8080:%d", hasuraPort)}
	color.Blue("Starting Hasura tunnel: %s", shellquote.Join(hasuraCmdArgs...))
	hasuraCmd := exec.CommandContext(ctx, hasuraCmdArgs[0], hasuraCmdArgs[1:]...)
	hasuraCmd.Stderr = os.Stderr
	if err := hasuraCmd.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start Hasura tunnel")
	}

	// Start SSH tunnel
	sshCmdArgs := []string{"balena", "device", "tunnel", balenaID, "-p", fmt.Sprintf("22222:%d", sshPort)}
	color.Blue("Starting SSH tunnel: %s", shellquote.Join(sshCmdArgs...))
	sshCmd := exec.CommandContext(ctx, sshCmdArgs[0], sshCmdArgs[1:]...)
	sshCmd.Stderr = os.Stderr
	if err := sshCmd.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start SSH tunnel")
	}

	// Give tunnels time to establish
	time.Sleep(5 * time.Second)
	color.Green("Tunnels established in %s", time.Since(t0))
	return Tunnels{
		PostgresCmd: pgCmd,
		HasuraCmd:   hasuraCmd,
		SSHCmd:      sshCmd,
		PgPort:      pgPort,
		HasuraPort:  hasuraPort,
		SSHPort:     sshPort,
	}
}

func closeTunnels(tunnels Tunnels) {
	if tunnels.PostgresCmd != nil && tunnels.PostgresCmd.Process != nil {
		tunnels.PostgresCmd.Process.Signal(syscall.SIGTERM)
	}
	if tunnels.HasuraCmd != nil && tunnels.HasuraCmd.Process != nil {
		tunnels.HasuraCmd.Process.Signal(syscall.SIGTERM)
	}
	if tunnels.SSHCmd != nil && tunnels.SSHCmd.Process != nil {
		tunnels.SSHCmd.Process.Signal(syscall.SIGTERM)
	}
}

// We want to be able to SSH into the device, so we need to register the local
// SSH key with Balena.
func registerSSHKeyOrDie(ctx context.Context, rateLimiter *tools.RateLimiter, sshKeyName, sshKeyPath string, deadline time.Time) {
	rateLimiter.Wait()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	code, output, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstReturn,
				tools.OutSrcStdout: tools.OutDstStreamStdout,
			},
			ValidCodes: []int{0, 1},
		},
		[]string{"balena", "ssh-key", "add", sshKeyName, sshKeyPath},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to register SSH key")
	}
	if code == 0 {
		color.Green("SSH key registered successfully")
		return
	}

	// 'BalenaRequestError: Request error: "user" and "public_key" must be unique.'
	if strings.Contains(output, balenaAddSSHKeyDuplicateError) {
		color.Yellow("SSH key already registered")
	} else {
		log.Fatal().
			Err(err).
			Str("output", output).
			Msg("Failed to register SSH key")
	}
}

// Checks if Postgres is ready.
//
// We use the pg_isready command to check if Postgres is ready.
func isPostgresReadyOrDie(ctx context.Context, rateLimiter *tools.RateLimiter, pgLocalPort int, deadline time.Time) bool {
	rateLimiter.Wait()

	// From <https://www.docs4dev.com/docs/postgresql/13/app-pg-isready.html>:
	//
	// pg_isready returns 0 to the shell if the server is accepting connections
	// normally, 1 if the server is rejecting connections (for example during
	// startup), 2 if there was no response to the connection attempt, and 3 if
	// no attempt was made (for example due to invalid parameters).
	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	code, _, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstStreamStderr,
				tools.OutSrcStdout: tools.OutDstStreamStdout,
			},
			ValidCodes: []int{0, 1, 2, 3},
		},
		[]string{"pg_isready", "-U", "postgres", "-p", fmt.Sprintf("%d", pgLocalPort), "-h", "localhost"},
	)
	switch code {
	case 0:
		return true
	case 1:
		return false
	case 2:
		return false
	}
	log.Fatal().Err(err).Msg("Failed to check Postgres readiness")
	return false
}

// Waits for Postgres to be ready.
//
// We use the pg_isready command to check if Postgres is ready.
func waitForPostgresOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	pgLocalPort int,
	deadline time.Time,
) {
	rateLimiter.Wait()
	color.Blue("Waiting for Postgres to be ready...")
	t0 := time.Now()
	for time.Now().Before(deadline) {
		if isPostgresReadyOrDie(ctx, rateLimiter, pgLocalPort, deadline) {
			color.Green("Postgres is ready in %s", time.Since(t0))
			return
		}
		time.Sleep(1 * time.Second)
	}

	color.Red("Timeout waiting for Postgres in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for Postgres")
}

// We sometimes use our internal pastebin to transfer files to devices instead
// of directly copying them.
//
// See <https://transfer.round2pos.com/> for more details.
func uploadToTransferSite(ctx context.Context, curlBinPath, dumpPath string) (string, error) {
	filename := filepath.Base(dumpPath)
	color.Blue("Uploading dump file to transfer site...")
	t0 := time.Now()
	_, curlVersionInfo, err := tools.RunLocalCommand(ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{curlBinPath, "-V"},
	)
	if err != nil {
		return "", fmt.Errorf("failed to get curl version: %w", err)
	}
	pretty.Println(curlVersionInfo)

	httpKFlags := []string{}
	// check if HTTP3 is supported
	if strings.Contains(curlVersionInfo, "HTTP3") {
		httpKFlags = append(httpKFlags, "--http3")
	} else if strings.Contains(curlVersionInfo, "HTTP2") {
		httpKFlags = append(httpKFlags, "--http2")
	}

	uploadURL := fmt.Sprintf("%s/%s", transferURL, filename)
	curlArgs := []string{curlBinPath}
	curlArgs = append(curlArgs,
		"--upload-file", dumpPath,
		// Some speed flags.
		"--tcp-fastopen",
	)
	curlArgs = append(curlArgs, httpKFlags...)
	curlArgs = append(curlArgs,
		"--expect100-timeout", "0",
		// non-zero if HTTP status ≥ 400
		"--fail",
		"--retry", "5",
		"--retry-delay", "2",
		"--retry-all-errors",
		"--progress-meter",
		"--show-error",
		uploadURL,
	)
	_, output, err := tools.RunLocalCommand(ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		curlArgs,
	)

	if err != nil {
		return "", fmt.Errorf("failed to upload file to transfer site: %w", err)
	}
	color.Green("Uploaded dump file to transfer site in %s", time.Since(t0))

	return output, nil
}

func copyFileViaTransferSite(
	ctx context.Context,
	curlBinPath string,
	srcURI url.URL,
	m *tools.MachineInfo,
	dstPath string,
	deadline time.Time,
) error {
	color.Blue("Transferring file to service...")
	t0 := time.Now()

	if srcURI.Scheme != "file" {
		return fmt.Errorf("failed to transfer file to service: unsupported scheme: %s, must be 'file'", srcURI.Scheme)
	}

	log.Info().
		Str("filePath", srcURI.String()).
		Str("machineInfo", m.URI()).
		Msg("transferFile")

	if _, err := os.Stat(srcURI.Path); os.IsNotExist(err) {
		return fmt.Errorf("failed to transfer file to service: file not found: %s", srcURI.Path)
	}

	// Get file size
	srcFileInfo, err := os.Stat(srcURI.Path)
	if err != nil {
		return fmt.Errorf("failed to transfer file to service: failed to get file info: %w", err)
	}
	srcFileSizeMB := srcFileInfo.Size() / (1024 * 1024)
	log.Printf("File size: %d MiB", srcFileSizeMB)

	log.Info().
		Msg("Uploading file to transfer site")
	uploadURL, err := uploadToTransferSite(ctx, curlBinPath, srcURI.Path)
	if err != nil {
		return fmt.Errorf("failed to transfer file to service: failed to upload dump file to transfer: %w", err)
	}

	log.Info().
		Str("uploadURL", uploadURL).
		Msg("Got upload URL")

	err = installBestCurl(ctx, m, deadline)
	if err != nil {
		return fmt.Errorf("failed to transfer file to service: failed to install best curl: %w", err)
	}
	curlArgs := []string{
		"/usr/local/bin/curl",
		"--fail",
		"--retry", "5",
		"--retry-delay", "2",
		"--retry-all-errors",
		"--progress-meter",
		"--show-error",
		uploadURL,
		"-o",
		dstPath,
		"--continue-at", "-",
		// Speed flags
		"--tcp-fastopen",
		"--expect100-timeout", "0",
		"--http3",
	}

	_, _, err = tools.RunCommand(
		ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		curlArgs,
	)
	if err != nil {
		return fmt.Errorf("failed to transfer file to service: failed to download file from transfer service: %w", err)
	}
	color.Green("Transferred file to service in %s", time.Since(t0))
	return nil
}

func downloadS3File(
	ctx context.Context,
	s3URI url.URL,
	m *tools.MachineInfo,
	dstPath string,
	AWSAccessKey,
	AWSSecretKey string,
	deadline time.Time,
) error {
	color.Blue("Downloading file from S3 (%s) to %s at %s", s3URI.String(), m.URI(), dstPath)
	t0 := time.Now()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	err := installAWSCLI(ctx, m, deadline)
	if err != nil {
		return fmt.Errorf("failed to copy file from S3 to service: failed to install AWS CLI: %w", err)
	}

	downloadArgs := []string{
		"aws",
		"s3",
		"cp",
		s3URI.String(),
		dstPath,
	}
	script := fmt.Sprintf(`
set -xeEuo pipefail

export AWS_ACCESS_KEY_ID=%s
export AWS_SECRET_ACCESS_KEY=%s
%s
	`, AWSAccessKey, AWSSecretKey, shellquote.Join(downloadArgs...))

	_, _, err = tools.RunBash(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		m,
		script,
	)
	if err != nil {
		return fmt.Errorf("failed to copy file from S3 to service: %w", err)
	}
	color.Green("Copied file from S3 to service in %s", time.Since(t0))
	return nil
}

func transferFile(
	ctx context.Context,
	curlBinPath string,
	srcURI url.URL,
	m *tools.MachineInfo,
	dstPath string,
	AWSAccessKey,
	AWSSecretKey string,
	deadline time.Time,
) error {

	if srcURI.Scheme == "s3" {
		err := downloadS3File(
			ctx,
			srcURI,
			m,
			dstPath,
			AWSAccessKey,
			AWSSecretKey,
			deadline,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to copy dump file from S3 to service")
		}
	} else if srcURI.Scheme == "file" {
		err := copyFileViaTransferSite(
			ctx,
			curlBinPath,
			srcURI,
			m,
			dstPath,
			deadline,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to copy dump file to service")
		}
	} else {
		return fmt.Errorf("failed to transfer file to service: unsupported scheme: %s, must be 's3' or 'file'", srcURI.Scheme)
	}

	return nil
}

// Extract username from output of "balena whoami".

// Example whoami output:
// ```
// $ balena whoami
// == ACCOUNT INFORMATION
// USERNAME: g_my_name
// EMAIL:    <EMAIL>
// URL:      balena-cloud.com
// ```
//
// extractBalenaUsername will return "g_my_name".
func extractBalenaUsername(output string) (string, error) {
	for _, line := range strings.Split(output, "\n") {
		if strings.HasPrefix(line, "USERNAME:") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				return parts[1], nil
			}
		}
	}
	return "", fmt.Errorf("could not find username in output: %s", output)
}

// Get all the Balena environment variables for a device.
func getEnvVars(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	balenaID string,
	deadline time.Time,
) (map[string]string, error) {
	rateLimiter.Wait()

	// POSTGRES_PASSWORD=$(balena env list --device "${BALENA_ID}" --json \
	// | jq -jr '.[] | select(.name=="POSTGRES_PASSWORD") | .value')
	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()
	_, output, err := tools.RunLocalCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{"balena", "env", "list", "--device", balenaID, "--json"},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get environment variables: %w", err)
	}

	var envVars []map[string]interface{}
	if err := json.Unmarshal([]byte(output), &envVars); err != nil {
		log.Error().
			Err(err).
			Str("output", output).
			Msg("failed to get environment variables: error parsing environment variables")
		return nil, fmt.Errorf("failed to get environment variables: error parsing environment variables: %w", err)
	}

	envVarsMap := make(map[string]string)
	for _, envVarInfo := range envVars {
		name, ok := envVarInfo["name"].(string)
		if !ok {
			return nil, fmt.Errorf("failed to get environment variables: name is not a string")
		}

		value, ok := envVarInfo["value"].(string)
		if !ok {
			return nil, fmt.Errorf("failed to get environment variables: value is not a string")
		}

		envVarsMap[name] = value
	}

	return envVarsMap, nil
}

func installBashViaAPK(
	ctx context.Context,
	m *tools.MachineInfo,
	deadline time.Time,
) error {
	color.Blue("Installing bash via APK on %s", m.DockerServiceName)
	t0 := time.Now()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, _, err := tools.RunCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			// Retry if ssh itself fails.
			RetryCodes: []int{255},
		},
		m,
		[]string{"apk", "add", "--no-cache", "bash"},
	)
	if err != nil {
		return fmt.Errorf("failed to install bash via APK: %w", err)
	}
	color.Green(fmt.Sprintf("Bash installed successfully in %s", time.Since(t0)))
	return nil
}

func installBestCurl(
	ctx context.Context,
	m *tools.MachineInfo,
	deadline time.Time,
) error {
	color.Blue("Installing best curl...")
	t0 := time.Now()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	script := fmt.Sprintf(`
set -xeEuo pipefail

cd ~
rm -f curl
rm -f curl-linux-x86_64-musl-8.13.0.tar.xz
wget https://github.com/stunnel/static-curl/releases/download/8.13.0/curl-linux-x86_64-musl-8.13.0.tar.xz
tar xf curl-linux-x86_64-musl-8.13.0.tar.xz
mv curl /usr/local/bin/curl
`)
	_, _, err := tools.RunBash(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			// Retry if ssh itself fails.
			RetryCodes: []int{255},
		},
		m,
		script,
	)
	if err != nil {
		return fmt.Errorf("failed to install best curl: %w", err)
	}
	color.Green(fmt.Sprintf("Best curl installed successfully in %s", time.Since(t0)))
	return nil
}

func installAWSCLI(
	ctx context.Context,
	m *tools.MachineInfo,
	deadline time.Time,
) error {
	color.Blue("Installing AWS CLI...")
	t0 := time.Now()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, _, err := tools.RunCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			// Retry if ssh itself fails.
			RetryCodes: []int{255},
		},
		m,
		[]string{"apk", "add", "--no-cache", "aws-cli"},
	)
	if err != nil {
		return fmt.Errorf("failed to install AWS CLI: %w", err)
	}
	color.Green(fmt.Sprintf("AWS CLI installed successfully in %s", time.Since(t0)))
	return nil
}
