package db

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/jackc/pgx/v4"
	"github.com/r2pos/common/tools/deploy/internal/tools"
	"github.com/rs/zerolog/log"
)

const (
	LeaderTerminalIndex = 98
)

// Terminal selection strategy: Which terminal index in the DB settings to
// overwrite with the deployed machine's info.
type TerminalSelection int

const (
	TerminalSelectionNone TerminalSelection = iota
	TerminalSelectionFirst
	TerminalSelectionLeader
)

func (t *TerminalSelection) String() string {
	switch *t {
	case TerminalSelectionFirst:
		return "first"
	case TerminalSelectionLeader:
		return "leader"
	case TerminalSelectionNone:
		return "none"
	default:
		return fmt.Sprintf("TerminalSelection(%d)", *t)
	}
}

func (t *TerminalSelection) Set(value string) error {
	switch value {
	case "first":
		*t = TerminalSelectionFirst
	case "leader":
		*t = TerminalSelectionLeader
	case "none":
		*t = TerminalSelectionNone
	default:
		return fmt.Errorf("invalid terminal selection: %s", value)
	}
	return nil
}

// Shortcut to run psql locally and return the output as a slice of maps.
func psql(ctx context.Context, pgDSN *url.URL, sql string) ([]map[string]interface{}, error) {
	sql = fmt.Sprintf("SELECT JSON_AGG(t) FROM (%s) AS t", sql)
	_, stdout, err := tools.RunLocalCommand(
		ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstReturn,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
		},
		[]string{"psql", "-qAtX", pgDSN.String(), "-c", sql},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to run psql: %w", err)
	}

	stdout = strings.TrimSpace(stdout)
	if stdout == "" {
		return []map[string]interface{}{}, nil
	}

	var rows []map[string]interface{}
	err = json.Unmarshal([]byte(stdout), &rows)
	if err != nil {
		log.Error().
			Err(err).
			Str("stdout", stdout).
			Msg("failed to unmarshal psql output")
		return nil, fmt.Errorf("failed to run psql: failed to unmarshal psql output: %w", err)
	}
	return rows, nil
}

// Debugging tool to find all databases on the server and print their tables'
// row counts.
func dbInventoryOrDie(ctx context.Context, baseDSN *url.URL) {

	pgDSN := *baseDSN
	pgDSN.Path = "postgres"

	dbs, err := psql(ctx, &pgDSN, `
SELECT datname
FROM pg_database
WHERE datistemplate = false
	AND datallowconn  = true
	AND datname <> 'postgres'
`)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get database inventory")
	}

	log.Info().
		Interface("dbs", dbs).
		Msg("Database inventory")

	for _, db := range dbs {
		log.Info().
			Str("db", db["datname"].(string)).
			Msg("Database")

		dbDSN := *baseDSN
		dbName, ok := db["datname"].(string)
		if !ok {
			log.Fatal().
				Interface("db", db).
				Msg("Database name is not a string")
		}
		dbDSN.Path = dbName
		tables, err := psql(ctx, &dbDSN, `
SELECT relname  AS table,
				n_live_tup AS rows
FROM   pg_stat_user_tables
WHERE  schemaname = 'public'
ORDER  BY rows DESC
`)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to get tables")
		}
		log.Info().
			Interface("tables", tables).
			Msg("Tables")
		for _, table := range tables {
			color.Green(fmt.Sprintf("Database %s has table %s with %d rows", dbName, table["table"], table["rows"]))
		}
	}
}

// This is used to purge a DB by dropping it and recreating it instead of the
// normal Balena/Docker volume purge which can take a long time.
func QuickPurgeDatabaseOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	localPGPort int,
	pgPassword string,
	localHasuraPort int,
	deadline time.Time,
) {
	t0 := time.Now()
	ctx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	psqlOrDie := func(dbName string, sql string) {
		dsn, err := url.Parse(fmt.Sprintf("postgresql://localhost:%d/postgres", localPGPort))
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to parse dsn")
		}

		dsn.User = url.UserPassword("postgres", pgPassword)
		dsn.Path = dbName
		_, _, err = tools.RunLocalCommand(
			ctx,
			tools.RunCommandOptions{
				Output: map[tools.OutSrc]tools.OutDst{
					tools.OutSrcStdout: tools.OutDstStreamStdout,
					tools.OutSrcStderr: tools.OutDstStreamStderr,
				},
				ValidCodes: []int{0},
			},
			[]string{"psql", "-U", "postgres", "-d", dsn.String(), "-c", sql},
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to purge database")
		}
	}

	psqlOrDie("postgres", `DROP DATABASE IF EXISTS r2pos WITH (FORCE)`)
	psqlOrDie("postgres", `CREATE DATABASE r2pos OWNER postgres`)
	psqlOrDie("r2pos", `
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;
`)

	color.Green(fmt.Sprintf("Database quick-purged successfully in %s", time.Since(t0)))
}

// This is used to check if Hasura is ready by hitting the healthz endpoint.
func IsHasuraReady(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	hasuraPort int,
	deadline time.Time,
) bool {
	rateLimiter.Wait()

	url := fmt.Sprintf("http://localhost:%d/healthz", hasuraPort)
	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	// Use http get to check if Hasura is ready.
	req, err := http.NewRequestWithContext(cmdCtx, "GET", url, nil)
	if err != nil {
		log.Warn().Err(err).Msg("Hasura health check failed: Failed to create HTTP request")
		return false
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Warn().Err(err).Msg("Hasura health check failed: Failed to send HTTP request")
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warn().Err(err).Msg("Hasura health check failed: Failed to read HTTP response body")
		return false
	}

	log.Info().
		Str("body", string(body)).
		Int("status", resp.StatusCode).
		Msg("Hasura health check response")

	return resp.StatusCode == 200
}

// This is used to wait for Hasura to be ready by hitting the healthz endpoint.
func WaitForHasuraOrDie(
	ctx context.Context,
	rateLimiter *tools.RateLimiter,
	hasuraPort int,
	deadline time.Time,
) {
	rateLimiter.Wait()

	color.Blue("Waiting for Hasura to be ready...")
	t0 := time.Now()
	for time.Now().Before(deadline) {
		if IsHasuraReady(ctx, rateLimiter, hasuraPort, deadline) {
			color.Green("Hasura is ready in %s", time.Since(t0))
			return
		}
		time.Sleep(1 * time.Second)
	}

	color.Red("Timeout waiting for Hasura in %s", time.Since(t0))
	log.Fatal().Msg("Timeout waiting for Hasura")
}

// This is used to install the pg client 17 on the deployed machine (assumes
// Alpine/apk).
func InstallPGClient17(
	ctx context.Context,
	m *tools.MachineInfo,
	deadline time.Time,
) error {
	color.Blue("Installing pg client 17...")
	t0 := time.Now()

	cmdCtx, cancel := context.WithDeadline(ctx, deadline)
	defer cancel()

	_, _, err := tools.RunCommand(
		cmdCtx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			// Retry if ssh itself fails.
			RetryCodes: []int{255},
		},
		m,
		[]string{"apk", "add", "--no-cache", "postgresql17-client"},
	)
	if err != nil {
		return fmt.Errorf("failed to install pg client 17: %w", err)
	}
	color.Green(fmt.Sprintf("Pg client 17 installed successfully in %s", time.Since(t0)))
	return nil
}

// This is used to restore a database from a dump file.
func RestoreDatabaseOrDie(
	ctx context.Context,
	dumpPath string,
	m *tools.MachineInfo,
	deadline time.Time,
) {
	t0 := time.Now()

	// Check if dumpPath exists.
	if _, err := os.Stat(dumpPath); os.IsNotExist(err) {
		log.Fatal().
			Str("dumpPath", dumpPath).
			Msg("Database dump file does not exist")
	}

	// host := "/var/run/postgresql"
	host := "localhost"

	script := fmt.Sprintf(`
set -xeEuo pipefail

set -v

#CONF="$PGDATA/postgresql.conf"
#BACK="$CONF.bak"
DUMP_PATH="%s"
HOST=%s

###############################################################################
# always restore the original file, even on Ctrl-C or pg_restore failure
###############################################################################
# restore_conf() {
# 	set +e
#   mv -f "$BACK" "$CONF" || true
#   su -s /bin/sh postgres -c "pg_ctl -D \"$PGDATA\" restart" || true
# }
# trap restore_conf EXIT INT TERM

###############################################################################
# fast-restore settings
###############################################################################
#cp -f "$CONF" "$BACK"


# disable sync & fsync
# sed -Ei '
#   s/^\s*#?\s*synchronous_commit\s*=.*/synchronous_commit = off/;
#   s/^\s*#?\s*full_page_writes\s*=.*/full_page_writes = off/;
#   s/^\s*#?\s*fsync\s*=.*/fsync = off/;
#   s/^\s*#?\s*wal_level\s*=.*/wal_level = minimal/;
# ' "$CONF"

# cat "$CONF"
# su -s /bin/sh postgres -c "pg_ctl -D \"$PGDATA\" restart"

CORES=$(nproc)

pg_restore --version

psql -d r2pos -U postgres -h $HOST -p 5432 -c \
"SELECT relname  AS table,
        n_live_tup AS rows
 FROM   pg_stat_user_tables
 WHERE  schemaname = 'public'
 ORDER  BY rows DESC;"


#time pg_restore -Fc \
#	--exit-on-error --verbose \
#	--jobs=$CORES \
#	-d r2pos -U postgres -h $HOST -p 5432  "$DUMP_PATH"


time pg_restore -Fc --section=pre-data  \
	--exit-on-error --verbose \
	-d r2pos -U postgres -h $HOST -p 5432 "$DUMP_PATH"

psql -d r2pos -U postgres -h $HOST -p 5432 -c \
	"SELECT relname  AS table,
					n_live_tup AS rows
	 FROM   pg_stat_user_tables
	 WHERE  schemaname = 'public'
	 ORDER  BY rows DESC;"

# Temporarily disable triggers & FKs
psql r2pos -U postgres -h $HOST -p 5432 -c "ALTER DATABASE r2pos SET session_replication_role = replica;"

time pg_restore -Fc \
	--exit-on-error --verbose \
	--jobs=$CORES \
	-d r2pos -U postgres -h $HOST -p 5432 --section=data "$DUMP_PATH"

# Re-enable triggers & FKs
psql r2pos -U postgres -h $HOST -p 5432 -c "ALTER DATABASE r2pos SET session_replication_role = DEFAULT;"

time pg_restore -Fc \
	--exit-on-error --verbose \
	--jobs=$CORES \
	-d r2pos -U postgres -h $HOST -p 5432 --section=post-data "$DUMP_PATH"

#su -s /bin/sh postgres -c 'vacuumdb -d r2pos \
#  -U postgres \
#  -h $HOST \
#  -p 5432 \
#  --analyze-in-stages'
`,
		dumpPath,
		host,
	)

	// pv -f -p -t -e -r -b -v -cN "dump" %s

	_, _, err := tools.RunBash(
		ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStdout: tools.OutDstStreamStdout,
				tools.OutSrcStderr: tools.OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			// Retry if ssh itself fails.
			RetryCodes: []int{255},
		},
		m,
		script,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to restore database")
	}

	color.Green(fmt.Sprintf("Database restored successfully in %s", time.Since(t0)))
}

// Sets up the terminal settings (in the DB) to be ready-to-go.
func UpdateSystemDeviceRecordOrDie(
	ctx context.Context,
	localPGPort int,
	deviceInfo tools.DeviceInfo,
	pgPassword string,
	terminalSelection TerminalSelection,
	keepOtherTerminals bool,
) {
	color.Blue("Updating system device record...")
	t0 := time.Now()
	dsn := fmt.Sprintf("postgresql://postgres:%s@localhost:%d/r2pos", pgPassword, localPGPort)
	conn, err := pgx.Connect(ctx, dsn)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}
	defer conn.Close(ctx)

	// Get system device record
	var systemDeviceRecordJSON string
	err = conn.QueryRow(ctx,
		"SELECT document FROM json_record WHERE record_key = 'systemDevice'").Scan(&systemDeviceRecordJSON)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get system device record")
	}

	// Parse the JSON
	var systemDeviceMap map[string]interface{}
	if err := json.Unmarshal([]byte(systemDeviceRecordJSON), &systemDeviceMap); err != nil {
		log.Error().
			Err(err).
			Str("json", systemDeviceRecordJSON).
			Msg("Failed to parse system device record")
		log.Fatal().Err(err).Msg("Failed to parse system device record")
	}

	terminals, ok := systemDeviceMap["terminal"].([]interface{})
	if !ok {
		log.Fatal().
			Str("json", systemDeviceRecordJSON).
			Msg("jsonRecord.systemDevice.terminal is not an array of maps")
	}

	if len(terminals) == 0 {
		log.Fatal().
			Str("json", systemDeviceRecordJSON).
			Msg("jsonRecord.systemDevice.terminal is empty")
	}

	terminalListIndex := -1
	switch terminalSelection {
	case TerminalSelectionFirst:
		terminalListIndex = 0
	case TerminalSelectionLeader:
		for i, terminal := range terminals {
			terminalMap, ok := terminal.(map[string]interface{})
			if !ok {
				log.Fatal().
					Interface("terminals", terminals).
					Msgf("jsonRecord.systemDevice.terminal[%d] is not a map", i)
			}
			terminalIndex, ok := terminalMap["idx"].(float64)
			if !ok {
				log.Fatal().
					Interface("terminalMap", terminalMap).
					Str("type", fmt.Sprintf("%T", terminalMap["idx"])).
					Msgf("jsonRecord.systemDevice.terminal[%d].idx is not an int", i)
			}
			if terminalIndex == LeaderTerminalIndex {
				terminalListIndex = i
				break
			}
		}
	}

	if terminalListIndex == -1 {
		log.Fatal().
			Str("json", systemDeviceRecordJSON).
			Msg("No existing terminal found")
	}

	log.Info().
		Int("terminalListIndex", terminalListIndex).
		Msg("Selected terminal")

	terminal, ok := terminals[terminalListIndex].(map[string]interface{})
	if !ok {
		log.Fatal().
			Str("json", systemDeviceRecordJSON).
			Msgf("jsonRecord.systemDevice.terminal[%d] is not a map", terminalListIndex)
	}
	log.Info().
		Interface("terminal", terminal).
		Float64("idx", terminal["idx"].(float64)).
		Msg("Selected terminal")
	terminal["IP"] = deviceInfo.IPAddress
	terminal["MAC"] = deviceInfo.MACAddress

	if !keepOtherTerminals {
		systemDeviceMap["terminal"] = []interface{}{terminal}
	}

	// Convert back to JSON
	updatedRecord, err := json.Marshal(systemDeviceMap)
	if err != nil {
		log.Error().
			Err(err).
			Str("output", systemDeviceRecordJSON).
			Msg("Failed to marshal updated system device record")
		log.Fatal().Err(err).Msg("Failed to marshal updated system device record")
	}

	// Update record in database
	_, err = conn.Exec(ctx,
		"UPDATE json_record SET document = $1 WHERE record_key = 'systemDevice'",
		string(updatedRecord))
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to update system device record in database")
	}

	color.Green(fmt.Sprintf("System device record updated successfully in %s", time.Since(t0)))
}

// Check if a dump has a schema or if it is `--data-only/-a`.
func DumpHasSchema(
	ctx context.Context,
	m *tools.MachineInfo,
	dumpPath string,
) (bool, error) {
	_, output, err := tools.RunCommand(
		ctx,
		tools.RunCommandOptions{
			Output: map[tools.OutSrc]tools.OutDst{
				tools.OutSrcStderr: tools.OutDstReturn,
				tools.OutSrcStdout: tools.OutDstStreamStdout,
			},
			ValidCodes: []int{0},
		},
		m,
		[]string{"pg_restore", "-l", dumpPath},
	)
	if err != nil {
		return false, fmt.Errorf("failed to check has schema:failed to inspect pg_dump dump file: %w", err)
	}
	log.Info().
		Str("output", output).
		Msg("pg_restore -l output")

	// Example output:
	//
	// ```
	// pg_restore -l ~/Downloads/2025-05-19T05_00_00.dump
	// ;
	// ; Archive created at 2025-05-19 06:00:00 EDT
	// ;     dbname: r2pos
	// ;     TOC Entries: 40
	// ;     Compression: gzip
	// ;     Dump Version: 1.15-0
	// ;     Format: CUSTOM
	// ;     Integer: 4 bytes
	// ;     Offset: 8 bytes
	// ;     Dumped from database version: 16.9
	// ;     Dumped by pg_dump version: 16.8
	// ;
	// ;
	// ; Selected TOC Entries:
	// ;
	// 4013; 0 24837 TABLE DATA public _batch postgres
	// 4014; 0 24843 TABLE DATA public activity postgres
	// 4030; 0 25215 TABLE DATA public customer postgres
	// 4031; 0 25223 TABLE DATA public day_total postgres
	// 4028; 0 25185 TABLE DATA public db_update postgres
	// 4005; 0 24676 TABLE DATA public department postgres
	// 4027; 0 25085 TABLE DATA public department_minor postgres
	// 4010; 0 24801 TABLE DATA public discount postgres
	// 4011; 0 24814 TABLE DATA public employee_class postgres
	// 4006; 0 24686 TABLE DATA public employee postgres
	// 4009; 0 24736 TABLE DATA public job_code postgres
	// 4015; 0 24865 TABLE DATA public employee_job_code postgres
	// 4032; 0 25228 TABLE DATA public employee_tip postgres
	// 4016; 0 24875 TABLE DATA public epson_graphics postgres
	// 4012; 0 24824 TABLE DATA public item postgres
	// 4017; 0 24880 TABLE DATA public json_record postgres
	// 4018; 0 24886 TABLE DATA public known_proc postgres
	// 4040; 0 25667 TABLE DATA public liq_ctl_plu_desc postgres
	// 4008; 0 24707 TABLE DATA public sale postgres
	// 4039; 0 25641 TABLE DATA public liquor_to_pour postgres
	// 4019; 0 24889 TABLE DATA public logging postgres
	// 4022; 0 24918 TABLE DATA public permission_type postgres
	// 4020; 0 24900 TABLE DATA public permission_action postgres
	// 4021; 0 24909 TABLE DATA public permission_object postgres
	// 4007; 0 24697 TABLE DATA public permission postgres
	// 4023; 0 24927 TABLE DATA public pj_trigger postgres
	// 4035; 0 25518 TABLE DATA public pre_auth postgres
	// 4024; 0 24931 TABLE DATA public print_job postgres
	// 4038; 0 25617 TABLE DATA public recipe postgres
	// 4029; 0 25194 TABLE DATA public sale_number postgres
	// 4037; 0 25568 TABLE DATA public sale_payment_session postgres
	// 4036; 0 25540 TABLE DATA public sale_updated postgres
	// 4026; 0 24945 TABLE DATA public tax postgres
	// 4033; 0 25238 TABLE DATA public timecard postgres
	// 4034; 0 25243 TABLE DATA public total postgres
	// 4047; 0 0 SEQUENCE SET public sale_sale_number_seq postgres
	// ```

	// If it has a TABLE DEF, then it has a schema.

	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "TABLE DEF") {
			return true, nil
		}
	}
	return false, nil
}

// Applies the Hasura migrations to the database.
func ApplyHasuraMigrationsViaDockerOrDie(
	ctx context.Context,
	hasuraClientMachine *tools.MachineInfo,
	hasuraEndpoint string,
	hyperionProjectPath string,
	localHasuraPort int,
	rateLimiter *tools.RateLimiter,
	deadline time.Time,
) {
	color.Blue("Applying Hasura migrations...")
	t0 := time.Now()

	run := func(args ...string) {
		_, _, err := tools.RunCommand(
			ctx,
			tools.RunCommandOptions{
				Output: map[tools.OutSrc]tools.OutDst{
					tools.OutSrcStdout: tools.OutDstStreamStdout,
					tools.OutSrcStderr: tools.OutDstStreamStderr,
				},
				ValidCodes: []int{0},
				Cwd:        hyperionProjectPath,
			},
			hasuraClientMachine,
			args,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to apply Hasura migrations")
		}
	}

	run(
		"docker",
		"compose",
		"-f", "./terminal/docker-compose.yml",
		"stop",
		"hasura-service",
	)
	run(
		"docker",
		"compose",
		"-f", "./terminal/docker-compose.yml",
		"up",
		"-d",
		"--build",
		"hasura-service",
	)

	WaitForHasuraOrDie(ctx, rateLimiter, localHasuraPort, deadline)
	// Sleep for 10 seconds to ensure that the hasura service is ready
	time.Sleep(10 * time.Second)

	color.Green(fmt.Sprintf("Hasura migrations applied successfully in %s", time.Since(t0)))
}
