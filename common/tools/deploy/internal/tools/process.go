package tools

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/fatih/color"
	"github.com/go-resty/resty/v2/shellescape"
	"github.com/kballard/go-shellquote"
	"github.com/kr/pretty"
	"github.com/rs/zerolog/log"
)

type OutSrc int

const (
	OutSrcStderr OutSrc = iota
	OutSrcStdout
)

// ToString converts an OutSrc to a string
func (o OutSrc) String() string {
	switch o {
	case OutSrcStderr:
		return "OutSrcStderr"
	case OutSrcStdout:
		return "OutSrcStdout"
	}
	return fmt.Sprintf("OutSrc(%d)", o)
}

type OutDst int

const (
	OutDstReturn OutDst = iota
	OutDstStreamStdout
	OutDstStreamStderr
)

func (o OutDst) String() string {
	switch o {
	case OutDstReturn:
		return "OutDstReturn"
	case OutDstStreamStdout:
		return "OutDstStreamStdout"
	case OutDstStreamStderr:
		return "OutDstStreamStderr"
	}
	return fmt.Sprintf("OutDst(%d)", o)
}

type RunCommandOptions struct {
	Output     map[OutSrc]OutDst
	ValidCodes []int
	RetryCodes []int
	Cwd        string
}

type MachineInfo struct {
	// If 0, run the command directly.
	SSHPort int
	// If empty, SSHPort must be 0.
	SSHUser string

	// Target the given container. Alternatively, specify DockerServiceName.
	DockerContainerName string
	// The service name of the container to target. Alternatively, specify
	// DockerContainerName.
	DockerServiceName string

	// Docker binary path. Not needed if DockerContainerName or DockerServiceName
	// is specified.
	DockerBin string
}

func (m *MachineInfo) URI() string {
	if m == nil || *m == (MachineInfo{}) {
		return "localhost"
	}

	serviceName := m.DockerServiceName
	if serviceName == "" {
		serviceName = "host"
	}

	return fmt.Sprintf("%s@localhost:%d/%s", m.SSHUser, m.SSHPort, serviceName)
}

func RunLocalCommand(
	ctx context.Context,
	opts RunCommandOptions,
	args []string,
) (int, string, error) {
	var wg sync.WaitGroup
	defer wg.Wait()
	for {
		// Shlex it
		cmdStr := shellquote.Join(args...)
		color.Blue("Running command: %s", cmdStr)
		cmd := exec.CommandContext(ctx, args[0], args[1:]...)
		if opts.Cwd != "" {
			cmd.Dir = opts.Cwd
		}
		var b bytes.Buffer

		getSystemFile := func(outputDestination OutDst) *os.File {
			switch outputDestination {
			case OutDstStreamStderr:
				return os.Stderr
			case OutDstStreamStdout:
				return os.Stdout
			}
			return nil
		}

		getCmdPipe := func(outputSource OutSrc) (io.ReadCloser, error) {
			switch outputSource {
			case OutSrcStderr:
				return cmd.StderrPipe()
			case OutSrcStdout:
				return cmd.StdoutPipe()
			}
			return nil, fmt.Errorf("invalid output source: %d", outputSource)
		}

		for outputSource, outputDestination := range opts.Output {
			switch outputDestination {
			case OutDstStreamStderr:
				fallthrough
			case OutDstStreamStdout:
				pipe, err := getCmdPipe(outputSource)
				if err != nil {
					return 1, "", fmt.Errorf("failed to create %s pipe: %w", outputDestination, err)
				}
				// Make a copy of the loop variables for the closure
				dstFile := getSystemFile(outputDestination)
				go func(dst *os.File, src io.Reader) {
					wg.Add(1)
					defer wg.Done()
					io.Copy(dst, src)
				}(dstFile, pipe)
			case OutDstReturn:
				switch outputSource {
				case OutSrcStderr:
					cmd.Stderr = &b
				case OutSrcStdout:
					cmd.Stdout = &b
				}
			}
		}

		var cmdCode *int
		var cmdOutput string
		var cmdErr error

		err := cmd.Run()
		if err == nil {
			cmdCode = ptr(cmd.ProcessState.ExitCode())
			cmdOutput = b.String()
			cmdErr = nil
		} else if ee, ok := err.(*exec.ExitError); ok {
			cmdCode = ptr(ee.ExitCode())
			cmdOutput = b.String()
			cmdErr = ee
		} else {
			cmdCode = nil
			cmdOutput = b.String()
			cmdErr = err
		}

		var cmdCodeAny any
		if cmdCode != nil {
			cmdCodeAny = *cmdCode
		}

		if cmdCode != nil && slices.Contains(opts.RetryCodes, *cmdCode) {
			log.Warn().Msgf("Command failed with a retryable exit status=%v\ncmdErr=%v\ncmdOutput=%s", cmdCodeAny, cmdErr, cmdOutput)
			continue
		}

		if cmdCode != nil && slices.Contains(opts.ValidCodes, *cmdCode) {
			return *cmdCode, cmdOutput, nil
		}

		return 0, "", fmt.Errorf("command failed with invalid code: %v\ncmdErr=%v\ncmdOutput=%s", cmdCodeAny, cmdErr, cmdOutput)
	}
}

func GetContainerName(
	ctx context.Context,
	m *MachineInfo,
) (string, error) {
	if m.DockerServiceName == "" {
		return "", fmt.Errorf("cannot get container name: no service name provided")
	}

	hostOSMachineInfo := &MachineInfo{
		SSHPort:           m.SSHPort,
		SSHUser:           m.SSHUser,
		DockerServiceName: "",
		DockerBin:         m.DockerBin,
	}
	_, output, err := RunCommand(
		ctx,
		RunCommandOptions{
			Output: map[OutSrc]OutDst{
				OutSrcStdout: OutDstReturn,
				OutSrcStderr: OutDstStreamStderr,
			},
			ValidCodes: []int{0},
			RetryCodes: []int{255},
		},
		hostOSMachineInfo,
		[]string{
			m.DockerBin,
			"ps",
			"-q",
			"--filter",
			fmt.Sprintf("label=io.balena.service-name=%s", m.DockerServiceName),
		},
	)
	if err != nil {
		return "", err
	}
	output = strings.TrimSpace(output)
	if output == "" {
		return "", fmt.Errorf("no container found for service %s", m.DockerServiceName)
	}
	return output, nil
}

// Runs a command on the remote device.
func RunCommand(
	ctx context.Context,
	opts RunCommandOptions,
	m *MachineInfo,
	args []string,
) (int, string, error) {
	var err error
	if m.SSHPort == 0 {
		return RunLocalCommand(ctx, opts, args)
	}

	color.Blue("Running command on %s: %s", m.DockerServiceName, args[0])
	color.Blue("Args:")
	pretty.Println(shellquote.Join(args...))
	// Flush stdout
	os.Stdout.Sync()
	t0 := time.Now()

	if m.DockerServiceName == "" {
		// Just run the command directly
	} else {
		containerName := m.DockerContainerName
		if containerName == "" {
			containerName, err = GetContainerName(ctx, m)
			if err != nil {
				return 0, "", err
			}
		}

		args = append([]string{m.DockerBin, "exec", containerName}, args...)
	}

	// ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null \
	// -o ServerAliveInterval=10 -o ServerAliveCountMax=6 \
	// -o ConnectTimeout=15 -o ConnectionAttempts=10 -o TCPKeepAlive=yes \
	// -o StrictHostKeyChecking=no \
	// -p "${LOCAL_SSH_PORT}" "${MY_BALENA_USER}@localhost" \
	// "$1"

	for i := range args {
		args[i] = shellescape.Quote(args[i])
	}
	sshArgs := []string{
		"ssh",
		"-o", "StrictHostKeyChecking=no",
		"-o", "UserKnownHostsFile=/dev/null",
		"-o", "ServerAliveInterval=10",
		"-o", "ServerAliveCountMax=6",
		"-o", "ConnectTimeout=15",
		"-o", "ConnectionAttempts=10",
		"-o", "TCPKeepAlive=yes",
		"-p", fmt.Sprintf("%d", m.SSHPort),
		fmt.Sprintf("%s@localhost", m.SSHUser),
	}
	sshArgs = append(sshArgs, args...)

	sshCode, sshOutput, sshErr := RunLocalCommand(
		ctx,
		RunCommandOptions{
			Output:     opts.Output,
			ValidCodes: opts.ValidCodes,
			// Catch any errors with ssh itself.
			RetryCodes: append(opts.RetryCodes, 255),
			Cwd:        opts.Cwd,
		},
		sshArgs,
	)
	if sshErr != nil {
		return 0, "", fmt.Errorf("failed to run command: %w", sshErr)
	}
	color.Green("Ran remote command in %s", time.Since(t0))
	return sshCode, sshOutput, nil
}

// Runs an entire bash script on the remote device.
func RunBash(
	ctx context.Context,
	opts RunCommandOptions,
	m *MachineInfo,
	script string,
) (int, string, error) {

	return RunCommand(ctx, opts, m, []string{"/bin/bash", "-c", script})
}
