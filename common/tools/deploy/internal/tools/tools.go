package tools

import (
	"fmt"
	"net"
	"sync"
	"time"
)

type DeviceInfo struct {
	IPAddress  string
	MACAddress string
	NetName    string
}

func getLocalNetIFace(netName string) (*net.Interface, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("failed to get network interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name != netName {
			continue
		}
		return &iface, nil
	}
	return nil, fmt.<PERSON>rrorf("failed to find network interface %s", netName)
}

func getLocalIP(iface *net.Interface) (net.IP, error) {
	addrs, err := iface.Addrs()
	if err != nil {
		return nil, fmt.Errorf("failed to get addresses for interface %s: %w", iface.Name, err)
	}

	for _, addr := range addrs {
		ip, ok := addr.(*net.IPNet)
		if !ok {
			continue
		}

		if ip.IP.IsLoopback() {
			continue
		}

		// Check if the IP is an IPv4 address
		if ip.IP.To4() == nil {
			continue
		}

		return ip.IP, nil
	}
	return nil, fmt.Errorf("failed to get local ip for interface %s", iface.Name)
}

func GetLocalDeviceInfo(netName string) (DeviceInfo, error) {

	iface, err := getLocalNetIFace(netName)
	if err != nil {
		return DeviceInfo{}, fmt.Errorf("failed to get local device info: failed to get network interface: %w", err)
	}

	ip, err := getLocalIP(iface)
	if err != nil {
		return DeviceInfo{}, fmt.Errorf("failed to get local device info: failed to get local ip: %w", err)
	}

	return DeviceInfo{
		IPAddress:  ip.String(),
		MACAddress: iface.HardwareAddr.String(),
		NetName:    iface.Name,
	}, nil
}

// RateLimiter is a simple rate limiter that limits the number of requests per
// second.
//
// See Wait() for more details.
type RateLimiter struct {
	mu       sync.Mutex
	last     time.Time
	Interval time.Duration
}

func (r *RateLimiter) Wait() {
	r.mu.Lock()
	defer r.mu.Unlock()

	if time.Since(r.last) < r.Interval {
		time.Sleep(r.Interval - time.Since(r.last))
	}
	r.last = time.Now()
}

func ptr[T any](v T) *T {
	return &v
}
