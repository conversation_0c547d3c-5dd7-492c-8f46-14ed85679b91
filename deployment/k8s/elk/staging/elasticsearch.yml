apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: es
  namespace: elk
spec:
  serviceName: es
  replicas: 1
  selector:
    matchLabels: { app: es }
  template:
    metadata: { labels: { app: es } }
    spec:
      containers:
        - name: es
          image: docker.elastic.co/elasticsearch/elasticsearch:8.13.0
          env:
            - { name: discovery.type,       value: single-node }
            - { name: ES_JAVA_OPTS,         value: "-Xms512m -Xmx512m" }
          ports:
            - containerPort: 9200
            - containerPort: 9300
---
apiVersion: v1
kind: Service
metadata: { name: es, namespace: elk }
spec:
  ports:
    - { port: 9200, targetPort: 9200, name: http }
  selector: { app: es }
