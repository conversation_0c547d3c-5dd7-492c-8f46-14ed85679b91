apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: filebeat
  namespace: elk
spec:
  selector:
    matchLabels: { app: filebeat }
  template:
    metadata: { labels: { app: filebeat } }
    spec:
      serviceAccountName: filebeat
      tolerations: [ { operator: "Exists" } ]   # run on all nodes
      containers:
        - name: filebeat
          image: docker.elastic.co/beats/filebeat:8.13.0
          args: [ "-e", "-E", "output.logstash.hosts=[\"logstash:5044\"]" ]
          securityContext:
            runAsUser: 0     # needs host log access; tighten later
          volumeMounts:
            - { name: varlog, mountPath: /var/log }
            - { name: containerdsock, mountPath: /var/run/containerd/containerd.sock }
      volumes:
        - { name: varlog, hostPath: { path: /var/log } }
        - { name: containerdsock, hostPath: { path: /run/containerd/containerd.sock } }
