apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: elk
spec:
  replicas: 1
  selector:
    matchLabels: { app: kibana }
  template:
    metadata: { labels: { app: kibana } }
    spec:
      containers:
        - name: kibana
          image: docker.elastic.co/kibana/kibana:8.13.0
          env:
            - { name: ELASTICSEARCH_HOSTS, value: "http://es:9200" }
          ports: [ { containerPort: 5601 } ]
---
apiVersion: v1
kind: Service
metadata: { name: kibana, namespace: elk }
spec:
  type: NodePort   # change to ClusterIP + Ingress for prod
  ports: [ { port: 5601, targetPort: 5601 } ]
  selector: { app: kibana }
