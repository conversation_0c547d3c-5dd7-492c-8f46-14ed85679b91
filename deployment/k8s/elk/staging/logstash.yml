apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: elk
spec:
  replicas: 1
  selector:
    matchLabels: { app: logstash }
  template:
    metadata: { labels: { app: logstash } }
    spec:
      containers:
        - name: logstash
          image: docker.elastic.co/logstash/logstash:8.13.0
          ports: [ { containerPort: 5044, name: beats } ]
          env:
            - { name: LS_JAVA_OPTS, value: "-Xms256m -Xmx256m" }
          volumeMounts:
            - { name: pipeline, mountPath: /usr/share/logstash/pipeline }
      volumes:
        - name: pipeline
          configMap:
            name: logstash-pipeline
---
apiVersion: v1
kind: ConfigMap
metadata: { name: logstash-pipeline, namespace: elk }
data:
  logstash.conf: |
    input { beats { port => 5044 } }
    output {
      elasticsearch { hosts => ["http://es:9200"] }
      stdout { codec => rubydebug }
    }
---
apiVersion: v1
kind: Service
metadata: { name: logstash, namespace: elk }
spec:
  ports: [ { port: 5044, targetPort: 5044, name: beats } ]
  selector: { app: logstash }
