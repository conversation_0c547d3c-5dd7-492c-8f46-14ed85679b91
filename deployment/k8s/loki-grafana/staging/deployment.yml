############################################
# 1) Loki ConfigMap: holds Loki's config
############################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-config
  labels:
    app: loki
data:
  loki-config.yaml: |
    server:
      log_level: info
      http_listen_port: 3100
      # http_path_prefix: /loki

    common:
      path_prefix: /var/loki/data

    limits_config:
      ingestion_rate_mb: 4
      ingestion_burst_size_mb: 6
      max_concurrent_tail_requests: 20
      volume_enabled: true

    schema_config:
      configs:
        - from: 2020-10-24
          store: tsdb
          object_store: filesystem
          schema: v13
          index:
            prefix: index_
            period: 24h
          chunks:
            prefix: chunks_

    storage_config:
      # Just define 'filesystem:' at the top level (no 'tsdb:' key!)
      filesystem:
        directory: /var/loki/data/tsdb


    compactor:
      working_directory: /var/loki/data/compactor

    distributor:
      ring:
        kvstore:
          store: inmemory
    # AKA multi-tenancy.
    #
    # If this is true, we have to pass tenant, AKA org id AKA X-Grafana-Org-Id everywhere.
    auth_enabled: false

    ingester:
      wal:
        enabled: false
      lifecycler:
        ring:
          kvstore:
            store: inmemory
          replication_factor: 1

    query_range:
      max_retries: 5
---
############################################
# 2) Loki Deployment
############################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loki
  labels:
    app: loki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: loki
  template:
    metadata:
      labels:
        app: loki
    spec:
      containers:
        - name: loki
          image: grafana/loki:3.4.2
          args:
            - "-config.file=/etc/loki/loki-config.yaml"
          ports:
            - containerPort: 3100
          volumeMounts:
            - name: loki-config-volume
              mountPath: /etc/loki
            - name: loki-storage
              mountPath: /var/loki/data
          lifecycle:
            postStart:
              exec:
                command:
                  [
                    "/bin/sh",
                    "-c",
                    "mkdir -p /var/loki/data/chunks /var/loki/data/rules /var/loki/data/index",
                  ]
          livenessProbe:
            httpGet:
              path: /ready
              port: 3100
            initialDelaySeconds: 15
            periodSeconds: 15
          readinessProbe:
            httpGet:
              path: /ready
              port: 3100
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: loki-config-volume
          configMap:
            name: loki-config
            items:
              - key: loki-config.yaml
                path: loki-config.yaml
        - name: loki-storage
          emptyDir: {}
          # For an actual persistent setup, use a PersistentVolumeClaim.

---
############################################
# 3) Loki Service
############################################
apiVersion: v1
kind: Service
metadata:
  name: loki
  labels:
    app: loki
spec:
  type: ClusterIP
  selector:
    app: loki
  ports:
    - name: http
      protocol: TCP
      port: 3100
      targetPort: 3100

---
############################################
# 4) Prometheus ConfigMap
############################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  labels:
    app: prometheus
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
      # Scrape Prometheus itself
      - job_name: "prometheus"
        metrics_path: /prom/metrics
        static_configs:
          - targets: ["localhost:9090"]

---
############################################
# 5) Prometheus Deployment
############################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
        - name: prometheus
          image: prom/prometheus:v2.53.4
          args:
            - "--config.file=/etc/prometheus/prometheus.yml"
            - "--storage.tsdb.path=/prometheus"
            - "--storage.tsdb.retention.time=30d"
            - "--web.listen-address=:9090"
            - "--web.external-url=https://metrics.staging.round2pos.com/prom"
            - "--web.route-prefix=/prom"
            - "--web.enable-remote-write-receiver"
          ports:
            - containerPort: 9090
          volumeMounts:
            - name: prometheus-config-volume
              mountPath: /etc/prometheus
              readOnly: true
            - name: prometheus-storage
              mountPath: /prometheus
          livenessProbe:
            httpGet:
              path: /prom/-/healthy
              port: 9090
            initialDelaySeconds: 15
            periodSeconds: 15
          readinessProbe:
            httpGet:
              path: /prom/-/ready
              port: 9090
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: prometheus-config-volume
          configMap:
            name: prometheus-config
            items:
              - key: prometheus.yml
                path: prometheus.yml
        - name: prometheus-storage
          emptyDir: {}
          # For a real setup, replace emptyDir with a PVC for persistence.
---
############################################
# 6) Prometheus Service
############################################
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  labels:
    app: prometheus
spec:
  type: ClusterIP
  selector:
    app: prometheus
  ports:
    - name: http
      protocol: TCP
      port: 9090
      targetPort: 9090

---
############################################
# 7) Grafana Data Source ConfigMap
#    - Tells Grafana how to connect to Loki
############################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasource
  labels:
    app: grafana
data:
  datasource.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus.default.svc.cluster.local:9090/prom
        isDefault: true

      - name: Loki
        type: loki
        access: proxy
        url: "http://loki.default.svc.cluster.local:3100"
        isDefault: false
        jsonData:
          timeout: 60

---
############################################
# 8) Grafana Deployment
############################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
        - name: grafana
          image: grafana/grafana:11.5.3-ubuntu
          ports:
            - containerPort: 3000
          volumeMounts:
            - name: grafana-provision-datasource
              mountPath: /etc/grafana/provisioning/datasources
              readOnly: true
          env:
            # Optional: default login. DO NOT USE THIS IN PROD
            - name: GF_SECURITY_ADMIN_USER
              value: "admin"
            - name: GF_SECURITY_ADMIN_PASSWORD
              value: "admin"
            # Tells Grafana to pick up provisioned config from the default path
            - name: GF_PATHS_PROVISIONING
              value: "/etc/grafana/provisioning"
            # Use the full domain for Grafana
            - name: GF_SERVER_ROOT_URL
              value: "https://metrics.staging.round2pos.com"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 15
            periodSeconds: 15
          readinessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5

      volumes:
        - name: grafana-provision-datasource
          configMap:
            name: grafana-datasource
            items:
              - key: datasource.yaml
                path: datasource.yaml

---
############################################
# 9) Grafana Service
############################################
apiVersion: v1
kind: Service
metadata:
  name: grafana
  labels:
    app: grafana
spec:
  type: ClusterIP
  selector:
    app: grafana
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000

---
############################################
# 10) Grafana Alloy ServiceAccount
############################################
apiVersion: v1
kind: ServiceAccount
metadata:
  name: grafana-alloy
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: grafana-alloy
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/log", "events", "namespaces"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-alloy
subjects:
  - kind: ServiceAccount
    name: grafana-alloy
    namespace: default
roleRef:
  kind: ClusterRole
  name: grafana-alloy
  apiGroup: rbac.authorization.k8s.io

---
############################################
# 11) Grafana Alloy ConfigMap
############################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-alloy-config
  labels:
    app: grafana
data:
  grafana-alloy-config.alloy: |
    logging {
      level = "debug"
    }
    loki.source.kubernetes_events "example" {
      // Only watch for events in the kube-system namespace.
      namespaces = ["kube-system", "default"]

      forward_to = [loki.write.local.receiver]
    }

    discovery.kubernetes "pods" {
      role = "pod"
    }

    loki.source.kubernetes "pods" {
      targets    = discovery.kubernetes.pods.targets

      forward_to = [loki.write.local.receiver]
    }

    loki.write "local" {
      endpoint {
        url = sys.env("LOKI_TARGET")
      }
    }
---
############################################
# 12) Grafana Alloy Deployment
############################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-alloy
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      serviceAccountName: grafana-alloy
      containers:
        - name: grafana-alloy
          image: grafana/alloy:v1.7.5
          args:
            - run
            - "--server.http.listen-addr=0.0.0.0:12345"
            - "--storage.path=/var/lib/grafana-alloy/data"
            - "/etc/grafana/grafana-alloy-config.alloy"
          ports:
            - containerPort: 12345
          env:
            - name: LOKI_TARGET
              value: "http://loki.default.svc.cluster.local:3100/loki/api/v1/push"
          volumeMounts:
            - name: grafana-alloy-config-volume
              mountPath: /etc/grafana
            - name: grafana-alloy-storage
              mountPath: /var/lib/grafana-alloy/data
      volumes:
        - name: grafana-alloy-config-volume
          configMap:
            name: grafana-alloy-config
            items:
              - key: grafana-alloy-config.alloy
                path: grafana-alloy-config.alloy
        - name: grafana-alloy-storage
          emptyDir: {}

---
############################################
# 13) Grafana Alloy Service
############################################
apiVersion: v1
kind: Service
metadata:
  name: grafana-alloy
  labels:
    app: grafana
spec:
  type: ClusterIP
  selector:
    app: grafana
  ports:
    - name: http
      protocol: TCP
      port: 12345
      targetPort: 12345
