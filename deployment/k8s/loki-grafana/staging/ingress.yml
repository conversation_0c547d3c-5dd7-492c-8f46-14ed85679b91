apiVersion: v1
kind: Secret
metadata:
  name: loki-grafana-staging-basic-auth
type: Opaque
data:
  # Generated with `echo -n "$(htpasswd -nbB staging 'hand-sanitizer')" | base64 --wrap 0`.
  auth: c3RhZ2luZzokMnkkMDUkVm5udHUwcHI0ak9pRzFnanB3b0VzLlJkVzNvV2V3MURFUk55SjVhLmd5eHd3QVdtRVduU1M=
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loki-grafana-staging-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # This is the basic auth for the ingress, since we don't want outsiders to
    # see our staging environment.
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: loki-grafana-staging-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Restricted Access"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - metrics.staging.round2pos.com
      secretName: loki-grafana-staging-round2pos-tls
  rules:
    - host: metrics.staging.round2pos.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: grafana
                port:
                  number: 3000
          - pathType: Prefix
            path: "/loki"
            backend:
              service:
                name: loki
                port:
                  number: 3100
          - pathType: Prefix
            path: "/prom"
            backend:
              service:
                name: prometheus
                port:
                  number: 9090
---
# Another ingress for Lets Encrypt with a narrower path prefix.
#
# This is necessary because we are using basic auth on the main ingress, and
# the ACME challenge path cannot be subject to auth.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: loki-grafana-acme-only-ingress
spec:
  ingressClassName: nginx
  rules:
    - host: metrics.staging.round2pos.com
      http:
        paths:
          - path: "/.well-known/acme-challenge"
            pathType: ImplementationSpecific
            backend:
              service:
                name: loki-grafana-staging-dummy-service
                port:
                  number: 80
