// ignore_for_file: non_constant_identifier_names

import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'emp_tip_breakdown.g.dart';

@JsonSerializable()
@DateTimeSerializer()
class EmployeeTipBreakdown {
  EmployeeTipBreakdown({
    required this.emp_id,
    required this.tender_media,
    required this.grat_amount,
    required this.tip_amount,
  });

  factory EmployeeTipBreakdown.fromJson(Map<String, dynamic> json) => _$EmployeeTipBreakdownFromJson(json);

  EmployeeTipBreakdown.empty();

  int emp_id = 0;
  int tender_media = 0;
  int grat_amount = 0;
  int tip_amount = 0;

  @override
  bool operator ==(Object other) =>
      other is EmployeeTipBreakdown &&
      other.emp_id == emp_id &&
      other.tender_media == tender_media &&
      other.grat_amount == grat_amount &&
      other.tip_amount == tip_amount;

  @override
  int get hashCode => hashValues(
        emp_id,
        tender_media,
        grat_amount,
        tip_amount,
      );

  Map<String, dynamic> toJson() => _$EmployeeTipBreakdownToJson(this);
}
