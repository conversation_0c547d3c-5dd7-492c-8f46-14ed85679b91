import 'package:backoffice/app/data/models/cumulative_gift_sales.dart';
import 'package:backoffice/app/data/models/cumulative_media.dart';
import 'package:backoffice/app/data/models/cumulative_sales.dart';
import 'package:backoffice/app/data/models/cumulative_sales_tax.dart';
import 'package:backoffice/app/data/models/cumulative_stats.dart';
import 'package:backoffice/app/data/models/cumulative_tip_grat.dart';
import 'package:backoffice/app/data/models/emp_cash_discount.dart';
import 'package:backoffice/app/data/models/emp_discounts.dart';
import 'package:backoffice/app/data/models/emp_sales_by_dept.dart';
import 'package:backoffice/app/data/models/emp_sales_tax.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/major_report.dart';
import 'package:backoffice/app/data/models/media_report.dart';
import 'package:backoffice/app/data/models/movers.dart';
import 'package:backoffice/app/data/models/order_type_report.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/models/sales_by_dept.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/view_models/hourly_report.dart';
import 'package:backoffice/app/data/view_models/monthly_report.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('PDFService');

class BohReportService extends GetxService {
  final GraphqlService _graphqlService = Get.find();
  final IdentityService _identityService = Get.find();

  Future<Either<ServiceError, List<MonthlyReport>>> getMonthlySalesBreakdown(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object?> getMonthlyBreakdownResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(
            '''
           mutation GET_MONTHLY_SALES_VIEW(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_monthly_sales_view(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                base_price
                botday
                day
                taxpricetotal
                topday
                totaldailysales
                transactions
              }
            }

          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getMonthlyBreakdownResult.hasException) throw getMonthlyBreakdownResult.exception.toString();
      final List<Report> reports = (getMonthlyBreakdownResult.data!['get_monthly_sales_view'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();
      final List<MonthlyReport> monthReports = <MonthlyReport>[];
      for (final Report element in reports) {
        monthReports.add(MonthlyReport.monthlyReportFromReport(element));
      }

      return Right(monthReports);
    } catch (err, stack) {
      _logger.shout("error fetching Monthly reports", err, stack);
      return Left(ServiceError("Failed to fetch Monthly reports"));
    }
  }
  //test

  Future<Either<ServiceError, List<HourlyReport>>> getHourlySalesBreakdown(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object?> getHourlyBreakdownResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(
            '''
           mutation GET_HOURLY_SALES(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_hourly_sales(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                base_price
                bothour
                hour
                tax_price
                tophour
                totalhourlysales
                transactions
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getHourlyBreakdownResult.hasException) throw getHourlyBreakdownResult.exception.toString();

      final List<Report> reports = (getHourlyBreakdownResult.data!['get_hourly_sales'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();
      final List<HourlyReport> hourlyReports = <HourlyReport>[];
      for (final Report report in reports) {
        final HourlyReport hourlyReport = HourlyReport.hourlyReportFromReport(report);
        hourlyReports.add(hourlyReport);
      }

      return Right(hourlyReports);
    } catch (err, stack) {
      _logger.shout("error fetching Hourly reports", err, stack);
      return Left(ServiceError("Failed to fetch Hourly reports"));
    }
  }

  Future<Either<ServiceError, List<GiftReport>>> getGiftCardSales(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getGiftSalesResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
          mutation GET_GIFT_SALES(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
            get_gift_card_sales(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
              end_at
              gross_price
              item
              sale_number
              actualprice
              original_price
              base_price
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getGiftSalesResult.hasException) throw getGiftSalesResult.exception.toString();

      final List<GiftReport> giftSales = (getGiftSalesResult.data!['get_gift_card_sales'] as List<dynamic>)
          .map((dynamic sale) => GiftReport.fromJson(sale as Map<String, dynamic>))
          .toList();

      return Right(giftSales);
    } catch (err, stack) {
      _logger.shout("error fetching Gift Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Gift Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getPaidOut(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getPaidOutResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_PAID_OUT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
            get_paid_out(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
              created_at
              created_by
              document
              end_at
              sale
              sale_number
              suspended
              updated_at
              updated_by
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getPaidOutResult.hasException) throw getPaidOutResult.exception.toString();

      final List<Sale> paidOutSales =
          (getPaidOutResult.data!['get_paid_out'] as List<dynamic>).map((dynamic sale) => Sale.fromJson(sale as Map<String, dynamic>)).toList();

      return Right(paidOutSales);
    } catch (err, stack) {
      _logger.shout("error fetching Paid Out Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Gift Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<EmployeeTipBreakdown>>> getTipTotal(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> tipBreakdownRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_TIP(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_tip_breakdown(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  emp_id
                  tender_media
                  grat_amount
                  tip_amount
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": null,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (tipBreakdownRes.hasException) throw tipBreakdownRes.exception.toString();

      final List<EmployeeTipBreakdown> tipList = (tipBreakdownRes.data!['get_employee_tip_breakdown'] as List<dynamic>)
          .map((dynamic bd) => EmployeeTipBreakdown.fromJson(bd as Map<String, dynamic>))
          .toList();

      return Right(tipList);
    } catch (err, stack) {
      _logger.shout("error fetching Tip Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Tip Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getHouseCharges(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getHouseSalesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_HOUSE_CHARGE_SALES(\$end_date: timestamp, \$start_date: timestamp, \$media_type: Int, \$terminal_index: Int) {
              get_sale_by_media_tender(args: {end_date: \$end_date, start_date: \$start_date, media_type: \$media_type, terminal_index: \$terminal_index}) {
                sale
                document
                created_by
                created_at
                end_at
                sale_number
                suspended
                updated_at
                updated_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
            "media_type": PaymentMediaType.House.index,
          },
        ),
      );

      if (getHouseSalesResult.hasException) throw getHouseSalesResult.exception.toString();
      final List<Sale> houseChargeSales = (getHouseSalesResult.data!['get_sale_by_media_tender'] as List<dynamic>)
          .map((dynamic sale) => Sale.fromJson(sale as Map<String, dynamic>))
          .toList();
      return Right(houseChargeSales);
    } catch (err, stack) {
      _logger.shout("error fetching House Charge Report Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch House Charge Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<SalesByDeptSummaryReport>>> getSalesByDeptSum(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getSalesByDeptSumres = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_SALES_BY_DEPT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_sales_by_department(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                department
                deptactualpricetotals
                gross_price_total
                itemsperdepartment
                percentage
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getSalesByDeptSumres.hasException) throw getSalesByDeptSumres.exception.toString();
      final List<Report> reports = (getSalesByDeptSumres.data!['get_sales_by_department'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();
      final List<SalesByDeptSummaryReport> reportList = <SalesByDeptSummaryReport>[];
      for (final Report report in reports) {
        reportList.add(SalesByDeptSummaryReport.salesByDeptSummaryReportFromReport(report));
      }
      return Right(reportList);
    } catch (err, stack) {
      _logger.shout("error fetching House Charge Report Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch House Charge Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<TaxReport>>> getTaxReports(String startDate, String endDate, int? selectedTerminal, int? employeeId) async {
    try {
      final QueryResult<Object> getTaxTotalResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_SALES_TAX(\$terminal_index: Int, \$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int) {
              get_sales_tax(args: {terminal_index: \$terminal_index, start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id}) {
                index
                description
                tax_amount
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeId,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getTaxTotalResult.hasException) throw getTaxTotalResult.exception.toString();

      final List<TaxReport> reports = (getTaxTotalResult.data!['get_sales_tax'] as List<dynamic>)
          .map((dynamic report) => TaxReport.fromJson(report as Map<String, dynamic>))
          .toList();
      return Right(reports);
    } catch (err, stack) {
      _logger.shout("error fetching Tax Totals (Service)", err, stack);
      return Left(ServiceError("Failed to fetch Tax Totals Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getDualPriceTotal(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeId,
  ) async {
    try {
      final QueryResult<Object> getDualPricingResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_CASH_DISCOUNT_TOTAL(\$end_date: timestamp, \$start_date: timestamp,\$terminal_index: Int, \$employee_id: Int) {
            get_cash_discount_total(args: {end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                sale
                sale_number
                document
                end_at
                suspended
                updated_at
                updated_by
                created_by
                created_at
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
            "employee_id": null,
          },
        ),
      );

      if (getDualPricingResult.hasException) throw getDualPricingResult.exception.toString();

      final List<Sale> dualPriceSales = (getDualPricingResult.data!['get_cash_discount_total'] as List<dynamic>)
          .map((dynamic sale) => Sale.fromJson(sale as Map<String, dynamic>))
          .toList();
      return Right(dualPriceSales);
    } catch (err, stack) {
      _logger.shout("error fetching Dual Pricing data(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Dual Pricing data(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<String>>> getTaxList() async {
    try {
      final QueryResult<Object> getTaxesRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_SALES_TAXES {
              json_record(where: {record_key: {_eq: "salesTax"}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
        ),
      );

      if (getTaxesRes.hasException) throw getTaxesRes.exception.toString();

      final SalesTaxJsonRecord taxObs = SalesTaxJsonRecord.fromJson(getTaxesRes.data!['json_record'][0] as Map<String, dynamic>);

      final List<String> taxTitles = <String>[];
      for (final Tax element in taxObs.document.taxes) {
        taxTitles.add(element.desc ?? "No Description");
      }
      return Right(taxTitles);
    } catch (err, stack) {
      _logger.shout("error fetching Tax List(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Tax List(Serivce)"));
    }
  }

  Future<Either<ServiceError, SystemSettingJsonRecord>> getSystemSettings() async {
    try {
      final QueryResult<Object> getSysSetting = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_SALES_TAXES {
              json_record(where: {record_key: {_eq: "systemSetting"}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
        ),
      );

      if (getSysSetting.hasException) throw getSysSetting.exception.toString();

      final SystemSettingJsonRecord taxObs = SystemSettingJsonRecord.fromJson(getSysSetting.data!['json_record'][0] as Map<String, dynamic>);

      return Right(taxObs);
    } catch (err, stack) {
      _logger.shout(err, stack);
      return Left(ServiceError("Failed to fetch systemSettings(Serivce)"));
    }
  }

  Future<Either<ServiceError, JsonRecordReports>> getJsonRecordReport() async {
    try {
      final QueryResult<Object> getReportRecordRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_REPORT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "reports"}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
        ),
      );

      if (getReportRecordRes.hasException) throw getReportRecordRes.exception.toString();

      final JsonRecordReports reportRecord = JsonRecordReports.fromJson(getReportRecordRes.data!['json_record'][0] as Map<String, dynamic>);

      return Right(reportRecord);
    } catch (err, stack) {
      _logger.shout(err, stack);
      return Left(ServiceError("Failed to fetch Report Record"));
    }
  }

  Future<Either<ServiceError, List<SalesByDeptReport>>> getSalesByRange(
    String startDate,
    String endDate,
    List<int>? selectedTerminals,
  ) async {
    try {
      final List<int> terminalList =
          // ignore: always_specify_types
          (selectedTerminals ?? <int>[]).isEmpty ? _identityService.terminalList.map((t) => t.idx).toList() : selectedTerminals!;
      final QueryResult<Object> getSalesByRangeRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_SALES_BY_RANGE(\$start_date: timestamp, \$end_date: timestamp, \$terminal_ids: _int4) {
              get_sales_by_range(args: {start_date: \$start_date, end_date: \$end_date, terminal_ids: \$terminal_ids}) {
                    count
                    department
                    gross_price
                    item
                    percentage
                    price
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            // ignore: always_specify_types
            "terminal_ids": '{${terminalList.join(',')}}',
          },
        ),
      );

      if (getSalesByRangeRes.hasException) throw getSalesByRangeRes.exception.toString();
      final List<Report> data = (getSalesByRangeRes.data!['get_sales_by_range'] as List<dynamic>)
          .map((dynamic sale) => Report.fromJson(sale as Map<String, dynamic>))
          .toList();

      final List<SalesByDeptReport> convertDataList = <SalesByDeptReport>[];
      for (final Report element in data) {
        convertDataList.add(SalesByDeptReport.salesByDeptReportFromReport(element));
      }
      return Right(convertDataList);
    } catch (err, stack) {
      _logger.shout("error fetching SalesByRange(Service)", err, stack);
      return Left(ServiceError("Failed to fetch SalesByRange(Service)"));
    }
  }

  Future<Either<ServiceError, List<SalesByDeptReport>>> getSalesByDeptFull(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getSalesByDeptSumRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_SALES_BY_DEPT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_sales_by_department(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                department
                deptactualpricetotals
                gross_price_total
                itemsperdepartment
                percentage
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getSalesByDeptSumRes.hasException) throw getSalesByDeptSumRes.exception.toString();
      final List<Report> reports = (getSalesByDeptSumRes.data!['get_sales_by_department'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();
      final List<SalesByDeptReport> reportList = <SalesByDeptReport>[];
      for (final Report report in reports) {
        reportList.add(SalesByDeptReport.salesByDeptReportFromReport(report));
      }
      return Right(reportList);
    } catch (err, stack) {
      _logger.shout("error fetching Sales by Dept data(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Sales by Dept data(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<MediaReport>>> getMediaBreakdown(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getMediaRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_MEDIA_BREAKDOWN(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_media_breakdown(args: {start_date: \$start_date, end_date: \$end_date,, terminal_index: \$terminal_index}) {
                  count
                  media
                  tips
                  amount
                  net_total
                  refunded_sum
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getMediaRes.hasException) throw getMediaRes.exception.toString();
      final List<MediaReport> reports = (getMediaRes.data!['get_media_breakdown'] as List<dynamic>)
          .map((dynamic report) => MediaReport.fromJson(report as Map<String, dynamic>))
          .toList();

      return Right(reports);
    } catch (err, stack) {
      _logger.shout("error fetching Media Report(Service)", err, stack);
      return Left(ServiceError("Failed to fetch Media Report(Service)"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getSaleWindow(String startDate, String endDate) async {
    try {
      final QueryResult<Object> getWindowRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_SALE_WINDOW(\$_lt: timestamptz, \$_gte: timestamptz) {
              sale(
                where: {
                  end_at: {_gte: \$_gte}, 
                  _and: {
                    end_at: {_lt: \$_lt}
                  }
                }
              ) {
                sale
                sale_number
                document
                end_at
                created_by
                created_at
                suspended
                updated_at
                updated_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "_gte": startDate,
            "_lt": endDate,
          },
        ),
      );

      if (getWindowRes.hasException) throw getWindowRes.exception.toString();
      final List<Sale> reports =
          (getWindowRes.data!['sale'] as List<dynamic>).map((dynamic report) => Sale.fromJson(report as Map<String, dynamic>)).toList();

      return Right(reports);
    } catch (err, stack) {
      _logger.shout("error fetching Sale Window(Service)", err, stack);
      return Left(ServiceError("Failed to Sale Window(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getGiftReport(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> getGiftTransactionsResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_GIFT_TRANSACTIONS(\$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
              get_gift_transactions(args: {end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
                created_at
                created_by
                document
                end_at
                sale
                sale_number
                suspended
                updated_at
                updated_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (getGiftTransactionsResult.hasException) throw getGiftTransactionsResult.exception.toString();
      final List<Sale> reportRows = (getGiftTransactionsResult.data!['get_gift_transactions'] as List<dynamic>)
          .map((dynamic report) => Sale.fromJson(report as Map<String, dynamic>))
          .toList();
      return Right(reportRows);
    } catch (err, stack) {
      _logger.shout("error fetching Gift Report(Service)", err, stack);
      return Left(ServiceError("Failed to Gift Report(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<Movers>>> getMovers(
    String startDate,
    String endDate,
    int? selectedTerminal,
    String orderBy,
  ) async {
    try {
      final QueryResult<Object> moversResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_MOVERS(\$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int, \$terminal_index: Int, \$qty: order_by) {
                get_movers(args: {start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id, terminal_index: \$terminal_index}, order_by: {qty: \$qty}) {
                  item
                  long_desc
                  end_at
                  amount
                  qty
                }
              }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "employee_id": null,
            "terminal_index": selectedTerminal,
            "qty": orderBy,
          },
        ),
      );

      if (moversResult.hasException) throw moversResult.exception.toString();
      final List<Movers> reportRows =
          (moversResult.data!['get_movers'] as List<dynamic>).map((dynamic report) => Movers.fromJson(report as Map<String, dynamic>)).toList();
      return Right(reportRows);
    } catch (err, stack) {
      _logger.shout("error fetching Movers(Service)", err, stack);
      return Left(ServiceError("Failed to Movers(Serivce)"));
    }
  }

  Future<Either<ServiceError, List<EmployeeSalesByDepartment>>> getServerDeptReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empSalesByDeptRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMP_SALES_BY_DEPT(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_sales_by_dept(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  department
                  count
                  actual_price
                  gross_price
                  id
                }
              }

            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empSalesByDeptRes.hasException) throw empSalesByDeptRes.exception.toString();

      final List<EmployeeSalesByDepartment> empSalesByDeptList = (empSalesByDeptRes.data!['get_employee_sales_by_dept'] as List<dynamic>)
          .map((dynamic jc) => EmployeeSalesByDepartment.fromJson(jc as Map<String, dynamic>))
          .toList();
      return Right(empSalesByDeptList);
    } catch (e, stack) {
      _logger.shout(e.toString(), e, stack);
      return Left(ServiceError('Failed to employee sales by department(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeStatistics>>> getServerStatsReportList(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empStatsRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_STATS(\$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int , \$terminal_index: Int ) {
                get_employee_stats(args: {start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id, terminal_index: \$terminal_index}) {
                  id
                  guest_count
                  guest_average
                  check_count
                  check_average
                  cancel_sale_count
                  cancel_sale_amount
                  duration
                  no_sale_count
                  refunded_amount
                  refunded_count
                  reopened_total
                  reopened_count
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empStatsRes.hasException) throw empStatsRes.exception.toString();

      final List<EmployeeStatistics> employeeBreakdownList = (empStatsRes.data!['get_employee_stats'] as List<dynamic>)
          .map((dynamic bd) => EmployeeStatistics.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(employeeBreakdownList);
    } catch (e, stack) {
      _logger.shout("Error fecthing Employee Stats", e, stack);
      return Left(ServiceError('Failed to Server Stats(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeTipBreakdown>>> getServerTipReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empTipRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_TIP(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_tip_breakdown(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  emp_id
                  tender_media
                  grat_amount
                  tip_amount
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
          },
        ),
      );
      if (empTipRes.hasException) throw empTipRes.exception.toString();

      final List<EmployeeTipBreakdown> employeeTipList = (empTipRes.data!['get_employee_tip_breakdown'] as List<dynamic>)
          .map((dynamic bd) => EmployeeTipBreakdown.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(employeeTipList);
    } catch (e, stack) {
      _logger.shout("error fetching tip Report(service)", e, stack);
      return Left(ServiceError('Failed to Tip Report (Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeCashDiscount>>> getEmployeeDualPrice(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empCDRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_EMPLOYEE_CASH_DSCOUNT(\$employee_id: Int, \$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
            get_employee_cash_discount(args: {employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
              emp_id
              amount
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
          },
        ),
      );
      if (empCDRes.hasException) throw empCDRes.exception.toString();

      final List<EmployeeCashDiscount> employeeCDList = (empCDRes.data!['get_employee_cash_discount'] as List<dynamic>)
          .map((dynamic bd) => EmployeeCashDiscount.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(employeeCDList);
    } catch (e, stack) {
      _logger.shout("error fecthing employe Dual Pricing", e, stack);
      return Left(ServiceError('Failed to Employee Dual Pricing Discounts(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeSalesTax>>> getEmployeeSalesTax(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empSalesTaxRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_EMPLOYEE_SALES_TAX(\$employee_id: Int, \$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
            get_employee_sales_tax(args: {employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
              emp_id
              description
              index
              tax_amount
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
          },
        ),
      );
      if (empSalesTaxRes.hasException) throw empSalesTaxRes.exception.toString();

      final List<EmployeeSalesTax> employeeCDList = (empSalesTaxRes.data!['get_employee_sales_tax'] as List<dynamic>)
          .map((dynamic bd) => EmployeeSalesTax.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(employeeCDList);
    } catch (e, stack) {
      _logger.shout("error fecthing employee sales tax (service)", e, stack);
      return Left(ServiceError('Failed to Employee Sales Tax(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Employee>>> getActiveEmployees() async {
    try {
      final QueryResult<Object> activeEmpRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_ACTIVE_EMPS(\$_eq: Boolean) {
                employee(where: {is_active: {_eq: \$_eq}}, order_by: {employee_full_name: asc}) {
                  created_at
                  created_by
                  document
                  employee
                  employee_class
                  employee_full_name
                  id
                  is_active
                  password
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: const <String, dynamic>{
            "_eq": true,
          },
        ),
      );
      if (activeEmpRes.hasException) throw activeEmpRes.exception.toString();

      final List<Employee> employeeList =
          (activeEmpRes.data!['employee'] as List<dynamic>).map((dynamic bd) => Employee.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(employeeList);
    } catch (e, stack) {
      _logger.shout("error fetching employee list(service)", e, stack);
      return Left(ServiceError('Failed to Employee List(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Employee>>> getEmployees() async {
    try {
      final QueryResult<Object> activeEmpRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_ACTIVE_EMPS {
                employee(order_by: {employee_full_name: asc}) {
                  created_at
                  created_by
                  document
                  employee
                  employee_class
                  employee_full_name
                  id
                  is_active
                  password
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
        ),
      );
      if (activeEmpRes.hasException) throw activeEmpRes.exception.toString();

      final List<Employee> employeeList =
          (activeEmpRes.data!['employee'] as List<dynamic>).map((dynamic bd) => Employee.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(employeeList);
    } catch (e, stack) {
      _logger.shout("error fetching employee list(service)", e, stack);
      return Left(ServiceError('Failed to Employee List(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getReportTransHisrtoy(String startDate, String endDate, int? selectedTerminal) async {
    try {
      final QueryResult<Object> transRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_TRANSACTION_HISTORY(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_transaction_history(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  created_at
                  created_by
                  document
                  end_at
                  sale
                  sale_number
                  suspended
                  updated_at
                  updated_by
                }
              }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (transRes.hasException) throw transRes.exception.toString();

      final List<Sale> saleList =
          (transRes.data!['get_transaction_history'] as List<dynamic>).map((dynamic bd) => Sale.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(saleList);
    } catch (e, stack) {
      _logger.shout("error fetching transaction history(service)", e, stack);
      return Left(ServiceError('Failed to fetch Transaction History(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Activity>>> getReportActivity(String startDate, String endDate, int? selectedTerminal, int? empID) async {
    try {
      final QueryResult<Object> activityRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_ACTIVITES(\$activity_flag: Int, \$employee_id: Int, \$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
              get_activities(args: {activity_flag: \$activity_flag, employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
                activity
                created_at
                data1
                data2
                emp_id
                sale_num
                short_data
                str_data
                super_id
                term_num
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "activity_flag": null,
            "employee_id": empID,
            "terminal_index": selectedTerminal,
            "end_date": endDate,
            "start_date": startDate
          },
        ),
      );
      if (activityRes.hasException) throw activityRes.exception.toString();

      final List<Activity> activityList =
          (activityRes.data!['get_activities'] as List<dynamic>).map((dynamic bd) => Activity.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(activityList);
    } catch (e, stack) {
      _logger.shout("error fecthig activity report(service)", e, stack);
      return Left(ServiceError('Failed to fetch Activty Report(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Employee>>> getAllEmployees() async {
    try {
      final QueryResult<Object> employeeRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_EMPLOYEES {
                employee {
                  updated_by
                  updated_at
                  password
                  is_active
                  id
                  employee_full_name
                  employee_class
                  employee
                  document
                  created_by
                  created_at
                  employeeClassByEmployeeClass {
                    clearance
                    created_at
                    created_by
                    document
                    employee_class
                    title
                    updated_at
                    updated_by
                  }
                }
              }
          ''',
          ),
        ),
      );
      if (employeeRes.hasException) throw employeeRes.exception.toString();

      final List<Employee> activityList =
          (employeeRes.data!['employee'] as List<dynamic>).map((dynamic bd) => Employee.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(activityList);
    } catch (e, stack) {
      _logger.shout("error fetching all employees(service)", e, stack);
      return Left(ServiceError('Failed to fetch Employee List(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<CumulativeSalesTax>>> getCumulativeSalesTax({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      List<CumulativeSalesTax> taxList = <CumulativeSalesTax>[];
      final QueryResult<Object> saleTaxRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_CUMULATIVE_SALES_TAX(\$start_date: timestamp , \$end_date: timestamp , \$start_week: timestamp , \$end_week: timestamp , \$start_month: timestamp , \$end_month: timestamp , \$start_year: timestamp , \$end_year: timestamp , \$terminal_index: Int , \$employee_id: Int ) {
                get_cumulative_sale_tax(args: {start_date: \$start_date, end_date: \$end_date, start_week: \$start_week, end_week: \$end_week, start_month: \$start_month, end_month: \$end_month, start_year: \$start_year, end_year: \$end_year, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                  day_count
                  day_amount
                  description
                  month_amount
                  month_count
                  week_amount
                  week_count
                  year_amount
                  year_count
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
            "terminal_index": selectedTerminal,
            "employee_id": null
          },
        ),
      );
      if (saleTaxRes.hasException) throw saleTaxRes.exception.toString();

      taxList = (saleTaxRes.data!['get_cumulative_sale_tax'] as List<dynamic>)
          .map((dynamic bd) => CumulativeSalesTax.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(taxList);
    } catch (e, stack) {
      _logger.shout("error fetching cumulative sales tax(service)", e, stack);
      return Left(ServiceError("Error fetching Cumulative Sales Tax"));
    }
  }

  Future<Either<ServiceError, List<CumulativeSalesReport>>> getCumulativeDept({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> saleDeptRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            mutation GET_CUMULATIVE_SALES(\$start_date: timestamp, \$end_date: timestamp, \$start_week: timestamp, \$end_week: timestamp, \$start_month: timestamp, \$end_month: timestamp, \$start_year: timestamp, \$end_year: timestamp, \$terminal_index: Int) {
              get_cumulative_sales(args: {start_date: \$start_date, end_date: \$end_date, start_week: \$start_week, end_week: \$end_week, start_month: \$start_month, end_month: \$end_month, start_year: \$start_year, end_year: \$end_year, terminal_index: \$terminal_index}) {
                department
                day_actual_total
                day_gross_total
                day_item_count
                week_actual_total
                week_gross_total
                week_item_count
                month_actual_total
                month_gross_total
                month_item_count
                year_actual_total
                year_gross_total
                year_item_count
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
            "terminal_index": selectedTerminal
          },
        ),
      );
      if (saleDeptRes.hasException) throw saleDeptRes.exception.toString();

      final List<CumulativeSalesReport> deptRows = (saleDeptRes.data!['get_cumulative_sales'] as List<dynamic>)
          .map((dynamic i) => CumulativeSalesReport.fromJson(i as Map<String, dynamic>))
          .toList();
      return Right(deptRows);
    } catch (e, stack) {
      _logger.shout("error fetching cumulate sales by depatment(service)", e, stack);
      return Left(ServiceError("Error fetching Cumulative Sales by Department"));
    }
  }

  Future<Either<ServiceError, List<CumulativeTipAndGrat>>> getCumulativeTipGrat({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> tipRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             mutation GET_TIP_AND_GRAT(\$start_date: timestamp, \$end_date: timestamp, \$start_week: timestamp, \$end_week: timestamp, \$start_month: timestamp, \$end_month: timestamp, \$start_year: timestamp, \$end_year: timestamp, \$terminal_index: Int, \$employee_id: Int) {
              get_cumulative_tip_and_gratuity(args: {start_date: \$start_date, end_date: \$end_date, start_week: \$start_week, end_week: \$end_week, start_month: \$start_month, end_month: \$end_month, start_year: \$start_year, end_year: \$end_year, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                day_grat_amount
                day_tip_amount
                month_grat_amount
                month_tip_amount
                week_grat_amount
                week_tip_amount
                year_grat_amount
                year_tip_amount
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
            "terminal_index": selectedTerminal,
            "employee_id": employeeId,
          },
        ),
      );
      if (tipRes.hasException) throw tipRes.exception.toString();

      final List<CumulativeTipAndGrat> tipList = (tipRes.data!['get_cumulative_tip_and_gratuity'] as List<dynamic>)
          .map((dynamic bd) => CumulativeTipAndGrat.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<CumulativeTipAndGrat>>(tipList);
    } catch (e, stack) {
      _logger.shout("error fetching cumulate Tips and Gratuity(service)", e, stack);
      return Left<ServiceError, List<CumulativeTipAndGrat>>(ServiceError("Error fetching Tips and Gratuity"));
    }
  }

  Future<Either<ServiceError, List<CumulativeGiftSales>>> getCumulativeGiftSales({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> giftSalesRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             mutation MyMutation(\$start_date: timestamp , \$end_date: timestamp , \$start_week: timestamp, \$end_week: timestamp, \$start_month: timestamp, \$end_month: timestamp, \$start_year: timestamp, \$end_year: timestamp, \$terminal_index: Int) {
              get_cumulative_gift_sales(args: {start_date: \$start_date, end_date: \$end_date, start_week: \$start_week, end_week: \$end_week, start_month: \$start_month, end_month: \$end_month, start_year: \$start_year, end_year: \$end_year, terminal_index: \$terminal_index}) {
                item
                day_amount
                month_amount
                week_amount
                year_amount
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (giftSalesRes.hasException) throw giftSalesRes.exception.toString();

      final List<CumulativeGiftSales> giftSales = (giftSalesRes.data!['get_cumulative_gift_sales'] as List<dynamic>)
          .map((dynamic gs) => CumulativeGiftSales.fromJson(gs as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<CumulativeGiftSales>>(giftSales);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, List<CumulativeGiftSales>>(ServiceError("Error fetching Gift Sales"));
    }
  }

  Future<Either<ServiceError, List<CumulativeMedia>>> getCumulativeMedia({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> cumMediaRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_CUMULATIVE_MEDIA(\$start_date: timestamp , \$end_date: timestamp , \$start_month: timestamp , \$end_month: timestamp , \$start_week: timestamp, \$end_week: timestamp , \$start_year: timestamp , \$end_year: timestamp , \$terminal_index: Int ) {
                get_cumulative_media(args: {start_date: \$start_date, end_date: \$end_date, start_month: \$start_month, end_month: \$end_month, start_week: \$start_week, end_week: \$end_week, start_year: \$start_year, end_year: \$end_year, terminal_index: \$terminal_index}) {
                  day_count
                  day_amount
                  media
                  month_amount
                  month_count
                  week_amount
                  week_count
                  year_amount
                  year_count
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (cumMediaRes.hasException) throw cumMediaRes.exception.toString();

      final List<CumulativeMedia> mediaSales = (cumMediaRes.data!['get_cumulative_media'] as List<dynamic>)
          .map((dynamic gs) => CumulativeMedia.fromJson(gs as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<CumulativeMedia>>(mediaSales);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, List<CumulativeMedia>>(ServiceError("Error fetching Gift Sales"));
    }
  }

  Future<Either<ServiceError, CumulativeStats>> getCumulativeStats({
    required String startDate,
    required String endDate,
    required String startWeek,
    required String endWeek,
    required String startMonth,
    required String endMonth,
    required String startYear,
    required String endYear,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> cumStatsRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_CUMULATIVE_STATS(\$employee_id: Int , \$end_date: timestamp , \$end_month: timestamp , \$end_week: timestamp , \$end_year: timestamp , \$start_date: timestamp , \$start_month: timestamp , \$start_week: timestamp , \$start_year: timestamp , \$terminal_index: Int ) {
                get_cumulative_stats(args: {employee_id: \$employee_id, end_date: \$end_date, end_month: \$end_month, end_week: \$end_week, end_year: \$end_year, start_date: \$start_date, start_month: \$start_month, start_week: \$start_week, start_year: \$start_year, terminal_index: \$terminal_index}) {
                  day_cancel_sale_amount
                  day_cancel_sale_count
                  day_no_sale_count
                  day_refunded_amount
                  day_refunded_count
                  day_reopened_count
                  day_reopened_total
                  month_cancel_sale_amount
                  month_cancel_sale_count
                  month_no_sale_count
                  month_refunded_amount
                  month_refunded_count
                  month_reopened_count
                  month_reopened_total
                  week_cancel_sale_amount
                  week_cancel_sale_count
                  week_no_sale_count
                  week_refunded_amount
                  week_refunded_count
                  week_reopened_count
                  week_reopened_total
                  year_cancel_sale_amount
                  year_cancel_sale_count
                  year_no_sale_count
                  year_refunded_amount
                  year_refunded_count
                  year_reopened_count
                  year_reopened_total
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "start_week": startWeek,
            "end_week": endWeek,
            "start_month": startMonth,
            "end_month": endMonth,
            "start_year": startYear,
            "end_year": endYear,
          },
        ),
      );
      if (cumStatsRes.hasException) throw cumStatsRes.exception.toString();

      final List<CumulativeStats> statsSales = (cumStatsRes.data!['get_cumulative_stats'] as List<dynamic>)
          .map((dynamic gs) => CumulativeStats.fromJson(gs as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, CumulativeStats>(statsSales.first);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, CumulativeStats>(ServiceError("Error fetching Gift Sales"));
    }
  }

  Future<Either<ServiceError, List<EmployeeDiscount>>> getEmployeeDiscount({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> empDiscRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_DISCOUNT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int, \$employee_id: Int) {
                get_employee_discount(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                  title
                  discount_total
                  employee_id
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "employee_id": employeeId,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empDiscRes.hasException) throw empDiscRes.exception.toString();

      final List<EmployeeDiscount> discounts = (empDiscRes.data!['get_employee_discount'] as List<dynamic>)
          .map((dynamic gs) => EmployeeDiscount.fromJson(gs as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<EmployeeDiscount>>(discounts);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, List<EmployeeDiscount>>(ServiceError("Error fetching Employee Discounts"));
    }
  }

  Future<Either<ServiceError, List<OrderTypeReport>>> getOrderTypeReport({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> empOrderTypeRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_ORDER_BREAKDOWN(\$employee_id: Int, \$end_date: timestamp , \$start_date: timestamp , \$terminal_index: Int ) {
                get_employee_order_breakdown(args: {employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
                  type
                  qty
                  total
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "employee_id": employeeId,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empOrderTypeRes.hasException) throw empOrderTypeRes.exception.toString();

      final List<OrderTypeReport> discounts = (empOrderTypeRes.data!['get_employee_order_breakdown'] as List<dynamic>)
          .map((dynamic gs) => OrderTypeReport.fromJson(gs as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<OrderTypeReport>>(discounts);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, List<OrderTypeReport>>(ServiceError("Error fetching Order Type Report"));
    }
  }

  Future<Either<ServiceError, List<MajorReport>>> getMajorReport({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> majorRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation MyMutation(\$end_date: timestamp, \$start_date: timestamp , \$terminal_index: Int ) {
                get_major_summary(args: {end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
                  major_actual_total
                  major_gross_total
                  major_group
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (majorRes.hasException) throw majorRes.exception.toString();

      final List<MajorReport> discounts =
          (majorRes.data!['get_major_summary'] as List<dynamic>).map((dynamic gs) => MajorReport.fromJson(gs as Map<String, dynamic>)).toList();
      return Right<ServiceError, List<MajorReport>>(discounts);
    } catch (e, stack) {
      _logger.shout("Error fetching Gift Sales(service)", e, stack);
      return Left<ServiceError, List<MajorReport>>(ServiceError("Error fetching Order Type Report"));
    }
  }

  Future<Either<ServiceError, int>> getTakeOutFee({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    int? employee_id,
  }) async {
    try {
      final QueryResult<Object> takeoutRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             mutation GET_TAKEOUT_FEE_TOTAL(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int, \$employee_id: Int) {
              get_surcharge_total(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                total
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
            "employee_id": employee_id,
          },
        ),
      );
      if (takeoutRes.hasException) throw takeoutRes.exception.toString();

      final int? discounts = int.tryParse(
          // ignore: avoid_dynamic_calls
          (takeoutRes.data!['get_surcharge_total'][0]['total'] as dynamic).toString());
      return Right<ServiceError, int>(discounts ?? 0);
    } catch (e, stack) {
      _logger.shout("Error fetching takeout fee(service)", e, stack);
      return Left<ServiceError, int>(ServiceError("Error fetching takeout fee"));
    }
  }

  Future<Either<ServiceError, bool>> getDualPricing() async {
    try {
      final QueryResult<Object> getDualPrice = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_DUAL_PRICE {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                document
                record_key
                updated_at
              }
            }
            ''',
          ),
        ),
      );
      if (getDualPrice.hasException) throw getDualPrice.exception.toString();

      // ignore: sdk_version_since, avoid_dynamic_calls
      final bool? dualPrice = bool.tryParse((getDualPrice.data!['json_record']['document']['dualPricing']! as dynamic).toString());
      return Right<ServiceError, bool>(dualPrice!);
    } catch (e, stack) {
      _logger.shout("Error fetching takeout fee(service)", e, stack);
      return Left<ServiceError, bool>(
        ServiceError("Error fetching takeout fee"),
      );
    }
  }

  Future<bool> getMajorChildren(String department, String major) async {
    try {
      ///case to catch default non existant departments
      if (major == 'No Major Assigned' && (department == 'Gift Cards' || department == 'COMMENT')) {
        return true;
      }
      final QueryResult<Object> getMajorResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           query getDepartment(\$_eq: String) {
              department(where: {title: {_eq: \$_eq}}) {
                title
                updated_at
                updated_by
                document
                department_order
                department
                created_at
                created_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "_eq": department,
          },
        ),
      );

      if (getMajorResult.hasException) throw getMajorResult.exception.toString();

      final List<Department> departmentRes =
          (getMajorResult.data!['department'] as List<dynamic>).map((dynamic report) => Department.fromJson(report as Map<String, dynamic>)).toList();

      if (departmentRes.isEmpty && major == 'No Major Assigned') {
        return true;
      }

      /// return true if the current major matches the deparment report major
      if (departmentRes.first.document.majorGroup == major) {
        return true;
      } else {
        /// case to handle null and unassigned majors
        /// can probably be cleaned up
        if (departmentRes.first.document.majorGroup == null && major == 'No Major Assigned') {
          return true;
        }
      }
      if (departmentRes.first.document.majorGroup == null && major == 'No Major Assigned') {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> updateReportRecord(JsonRecordReports reportRecord) async {
    try {
      final QueryResult<Object> updateReportsResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            mutation UPDATE_REPORTS(\$document: jsonb) {
              update_json_record(where: {record_key: {_eq: "reports"}}, _append: {document: \$document}) {
                returning {
                  document
                  record_key
                  updated_at
                }
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "document": reportRecord.document,
          },
        ),
      );

      if (updateReportsResult.hasException) {
        throw updateReportsResult.exception.toString();
      }
    } catch (err, stack) {
      _logger.severe('Error update Reports', err, stack);
    }
  }
}
