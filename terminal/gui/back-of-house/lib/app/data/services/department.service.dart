import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';

class DepartmentService extends GetxService {
  final GraphqlService _graphqlService = Get.find();

  Future<DepartmentService> init() async {
    return this;
  }

  Future<int> getDepartmentCount() async {
    final QueryResult<Object> departmentCountResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_DEPARTMENT_COUNT {
              department_aggregate {
                aggregate {
                  count
                }
              }
            }
            ''',
        ),
      ),
    );

    if (departmentCountResult.hasException) {
      throw departmentCountResult.exception!;
    }

    final Map<String, dynamic> departmentAggregate = departmentCountResult.data!['department_aggregate'] as Map<String, dynamic>;
    final Map<String, dynamic> aggregate = departmentAggregate['aggregate'] as Map<String, dynamic>;
    return aggregate['count'] as int;
  }

  Future<Department> createDepartment({
    required String title,
    required String creator,
  }) async {
    final Department dep = Department.empty();
    final int depCount0 = await getDepartmentCount();
    // This is a UUID, we can leave it blank; the DB will fill it.
    dep.department = "";
    dep.created_at = DateTime.now().toUtc();
    dep.created_by = creator;
    dep.updated_at = DateTime.now().toUtc();
    dep.updated_by = creator;
    dep.document.isTaxable = false;
    dep.title = title;
    // Note: This is a race condition, but we don't care because:
    // A. The order is only for presentation.
    // B. We will assume that only one person is importing/editing at a time.
    dep.document.order = depCount0 + 1;

    final Map<String, dynamic> depDict = Helpers.sanitizeEntity(
      dep.toJson(),
      <String>[
        // Get rid of any UUID, so that it is created by the DB.
        'department',
        'created_at',
        'updated_at',
      ],
    );

    final QueryResult<Object> addDepartmentResult = await _graphqlService.client.mutate(
      MutationOptions<Object>(
        document: g.parseString(
          '''
          mutation ADD_DEPARTMENT(\$department: department_insert_input!) {
            insert_department_one(object: \$department) {
                department
                title
                document
                created_at
                created_by
                updated_at
                updated_by
              }
            }
            ''',
        ),
        variables: <String, dynamic>{
          "department": depDict,
        },
      ),
    );

    if (addDepartmentResult.hasException) {
      throw addDepartmentResult.exception!;
    }

    // Get the department back from the result.
    return Department.fromJson(
      addDepartmentResult.data!['insert_department_one'] as Map<String, dynamic>,
    );
  }

  // Get Department by title or by UUID.
  Future<Department?> getDepartmentByTitle({String? title, String? uuid}) async {
    final QueryResult<Object> departmentCheckResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
          query GET_DEPARTMENT_CHECK(\$title: String) {
            department(where: 
              {title: {_eq: \$title}}
            ) {
              department
              title
              document
              created_at
              created_by
              updated_at
              updated_by
            }
          }
          ''',
        ),
        variables: <String, dynamic>{
          "title": title,
        },
      ),
    );

    if (departmentCheckResult.hasException) {
      throw departmentCheckResult.exception!;
    }

    final List<dynamic> departmentCheckList = departmentCheckResult.data!['department'] as List<dynamic>;

    // create department if it does not exist
    if (departmentCheckList.isEmpty) {
      return null;
    }
    return Department.fromJson(
      departmentCheckList.first as Map<String, dynamic>,
    );
  }

  // Get Department by title or by UUID.
  Future<Department?> getDepartmentByUUID({String? uuid}) async {
    final QueryResult<Object> departmentCheckResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
          query GET_DEPARTMENT_CHECK(\$uuid: uuid) {
            department(where:
              {department: {_eq: \$uuid}}
            ) {
              department
              title
              document
              created_at
              created_by
              updated_at
              updated_by
            }
          }
          ''',
        ),
        variables: <String, dynamic>{
          "uuid": uuid,
        },
      ),
    );

    if (departmentCheckResult.hasException) {
      throw departmentCheckResult.exception!;
    }

    final List<dynamic> departmentCheckList = departmentCheckResult.data!['department'] as List<dynamic>;

    // create department if it does not exist
    if (departmentCheckList.isEmpty) {
      return null;
    }
    return Department.fromJson(
      departmentCheckList.first as Map<String, dynamic>,
    );
  }

  // Get All Departments.
  Future<List<Department>> getDepartments() async {
    final QueryResult<Object> departmentResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
          query GET_DEPARTMENT_CHECK {
            department {
              department
              title
              document
              created_at
              created_by
              updated_at
              updated_by
            }
          }
          ''',
        ),
      ),
    );

    if (departmentResult.hasException) {
      throw departmentResult.exception!;
    }

    final List<dynamic> departmentList = departmentResult.data!['department'] as List<dynamic>;

    return departmentList.map((dynamic e) => Department.fromJson(e as Map<String, dynamic>)).toList();
  }

  // Update Department.
  Future<void> updateDepartment(Department dept) async {
    final QueryResult<Object> updateResult = await _graphqlService.client.mutate(
      MutationOptions<Object>(
        document: g.parseString(
          '''
           mutation UPDATE_DEPARTMENT(\$department: uuid!, \$document: jsonb) {
            update_department_by_pk(pk_columns: {department: \$department}, _append: {document: \$document}) {
              department
              document
            }
          }
          ''',
        ),
        variables: <String, dynamic>{
          "department": dept.department,
          "document": dept.document,
        },
      ),
    );

    if (updateResult.hasException) {
      throw updateResult.exception.toString();
    }
  }
}
