import 'dart:io';

import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:mailer/src/entities/problem.dart';

final Logger _logger = Logger('PDFService');

class EmailService extends GetxService {
  Future<bool> sendReport(List<FileAttachment> reportAttachments, List<String> recipients) async {
    try {
      const String username = '<EMAIL>';
      final String password = Platform.environment["_REPORT_EMAIL_APPKEY"] ?? "";

      if (password.isEmpty) throw "Cannot find report email appkey in environment variables";

      final SmtpServer smtpServer = gmail(username, password);

      final Message message = Message()
        ..from = const Address(username, 'R2 Reports')
        ..recipients.addAll(recipients)
        ..subject = 'Reports'
        ..text = 'This is the plain text.\nThis is line 2 of the text part.'
        ..html = "<h1>R2 Reports</h1>\n<p>Here is your report</p>"
        ..attachments.addAll(reportAttachments);

      await send(message, smtpServer);

      return true;
    } on MailerException catch (e, stack) {
      _logger.shout('Email failed to send', e.message, stack);

      for (final Problem p in e.problems) {
        _logger.shout("Problem: ${p.code}: ${p.msg}");
      }
      return false;
    }
  }
}
