import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';

class ItemService extends GetxService {
  final GraphqlService _graphqlService = Get.find();

  Future<ItemService> init() async {
    return this;
  }

  Future<Item?> _getItem({required String? longDesc, required String? item}) async {
    if (longDesc == null && item == null) {
      throw Exception("Either longDesc or item must be provided");
    }
    if (longDesc != null && item != null) {
      throw Exception("Only one of longDesc or item must be provided");
    }

    final List<String> schemas = <String>[];
    final List<String> wheres = <String>[];
    final Map<String, dynamic> variables = <String, dynamic>{};
    if (longDesc != null) {
      schemas.add("\$long_desc: String");
      wheres.add("{long_desc: {_eq: \$long_desc}}");
      variables["long_desc"] = longDesc;
    }
    if (item != null) {
      schemas.add("\$item: uuid");
      wheres.add("{item: {_eq: \$item}}");
      variables["item"] = item;
    }

    final String where = wheres.join(',\n                ');
    final String schema = schemas.join(", ");

    final String query = '''
          query ITEM_CHECK($schema) {
            item(where: {
              _or: [
                $where
              ]
            }) {
              item
              long_desc
              upc
              department
              document
              updated_at
              updated_by
              created_at
              created_by
              departmentByDepartment {
                department
                title
                document
                created_at
                created_by
                updated_at
                updated_by
              }
            }
          }
          ''';
    final QueryResult<Object> itemCheckResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(query),
        variables: variables,
      ),
    );

    if (itemCheckResult.hasException) {
      throw itemCheckResult.exception!;
    }

    final List<Item> itemCheckList =
        (itemCheckResult.data!['item'] as List<dynamic>).map((dynamic i) => Item.fromJson(i as Map<String, dynamic>)).toList();
    if (itemCheckList.isEmpty) {
      return null;
    }
    return itemCheckList.first;
  }

  Future<Item?> getItemByLongDesc({required String longDesc}) async {
    return _getItem(longDesc: longDesc, item: null);
  }

  Future<Item?> getItemByUUID({required String uuid}) async {
    return _getItem(longDesc: null, item: uuid);
  }

  Future<Item> createItem({required Item newItem, required String creator}) async {
    newItem.created_at = DateTime.now().toUtc();
    newItem.created_by = creator;
    newItem.updated_at = DateTime.now().toUtc();
    newItem.updated_by = creator;

    final Map<String, dynamic> itemDict = Helpers.sanitizeEntity(
      newItem.toJson(),
      <String>[
        if (newItem.item == "") "item",
        'created_at',
        'updated_at',
      ],
    );

    final QueryResult<Object> insertItemOneResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
          mutation INSERT_ITEM_ONE(\$item: item_insert_input!) {
          insert_item_one(object: \$item) {
            item
            long_desc
            upc
            department
            document
            created_at
            created_by
            updated_at
            updated_by
            departmentByDepartment {
              department
              title
              document
              created_at
              created_by
              updated_at
              updated_by
            }
          }
        }
      ''',
        ),
        variables: <String, dynamic>{
          "item": itemDict,
        },
      ),
    );

    if (insertItemOneResult.hasException) {
      throw insertItemOneResult.exception!;
    }

    return Item.fromJson(insertItemOneResult.data!['insert_item_one'] as Map<String, dynamic>);
  }

  Future<Item> updateItemByUUID({
    required String uuid,
    required Item updated,
    required String updator,
  }) async {
    updated.updated_at = DateTime.now().toUtc();
    updated.updated_by = updator;

    final Map<String, dynamic> itemDict = Helpers.sanitizeEntity(
      updated.toJson(),
      <String>[
        // Don't allow updating the uuid.
        'item',
        // Don't allow updating the created_at.
        'created_at',
        // This is an implicit field, we can't update it.
        'departmentByDepartment',
      ],
    );
    final QueryResult<Object> updateExistingItemResult = await _graphqlService.client.mutate(
      MutationOptions<Object>(
        document: g.parseString(
          '''
          mutation UPDATE_IMPORT_ITEMS
          (
            \$uuid: uuid,
            \$itemDict: item_set_input!) {
              update_item(
                where: {item: {_eq: \$uuid}},
                _set: \$itemDict) {
                affected_rows
                returning {
                  item
                  long_desc
                  upc
                  department
                  document
                  created_at
                  created_by
                  updated_at
                  updated_by
                  departmentByDepartment {
                    department
                    title
                    document
                    created_at
                    created_by
                    updated_at
                    updated_by
                  }
                }
              }
            }
          ''',
        ),
        variables: <String, dynamic>{
          "uuid": uuid,
          "itemDict": itemDict,
        },
      ),
    );

    if (updateExistingItemResult.hasException) {
      throw updateExistingItemResult.exception!;
    }

    final Map<String, dynamic> updatedItem = updateExistingItemResult.data!['update_item'] as Map<String, dynamic>;
    final List<dynamic> returning = updatedItem['returning'] as List<dynamic>;

    if (returning.isEmpty) {
      throw Exception("Item not found");
    }
    return Item.fromJson(returning[0] as Map<String, dynamic>);
  }

  Future<List<Item>> getAllItems() async {
    final QueryResult<Object> getAllItems = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
          query GET_ALL_ITEMS {
              item {
                item
                long_desc
                upc
                department
                document
                created_by
                created_at
                updated_by
                updated_at
                departmentByDepartment {
                  department
                  title
                  document
                  created_at
                  created_by
                  updated_at
                  updated_by
                }
              }
            }
            ''',
        ),
      ),
    );

    if (getAllItems.hasException) {
      throw getAllItems.exception!;
    }

    final List<dynamic> itemDicts = getAllItems.data!['item'] as List<dynamic>;
    return itemDicts.map((dynamic item) => Item.fromJson(item as Map<String, dynamic>)).toList();
  }

  // Update Item.
  Future<void> updateItem(Item item) async {
    final QueryResult<Object> updateResult = await _graphqlService.client.mutate(
      MutationOptions<Object>(
        document: g.parseString(
          '''
           mutation UPDATE_ITEM(\$item: uuid!, \$document: jsonb) {
            update_item_by_pk(pk_columns: {item: \$item}, _append: {document: \$document}) {
              item
              document
            }
          }
          ''',
        ),
        variables: <String, dynamic>{
          "item": item.item,
          "document": item.document,
        },
      ),
    );

    if (updateResult.hasException) {
      throw updateResult.exception.toString();
    }
  }
}
