import 'dart:io';

import 'package:desktop/app/data/models/service_error.dart';
import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:mailer/src/entities/problem.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

final Logger _logger = Logger('PDFService');

class PDFService extends GetxService {
  Future<void> printPDF(List<pw.TableRow> rows) async {
    try {
      final int pageNumbers = (rows.length / 45).ceil();
      int currentIndex = 0;
      final pw.Document pdf = pw.Document();
      for (int i = 0; i < pageNumbers; i++) {
        final List<pw.TableRow> rowsSection =
            rows.getRange(currentIndex, (currentIndex + 45 > rows.length) ? rows.length : currentIndex + 45).toList();
        pdf.addPage(
          pw.Page(
            clip: true,
            pageFormat: PdfPageFormat.standard,
            build: (pw.Context context) {
              return pw.Table(children: rowsSection);
            },
          ),
        );
        currentIndex = currentIndex + 45;
      }

      final File file = File("example.pdf");
      await file.writeAsBytes(await pdf.save());
      await sendEmail();
      await file.delete();
    } catch (err, stack) {
      _logger.shout("error fetching activites", err, stack);
    }
  }

  Future<void> sendEmail() async {
    const String username = '<EMAIL>';
    const String password = 'm8Wpzl5QS6oA';

    final SmtpServer smtpServer = gmail(username, password);

    final Message message = Message()
      ..from = const Address(username, 'R2 Reports')
      ..recipients.add('<EMAIL>')
      ..subject = 'Reports'
      ..text = 'This is the plain text.\nThis is line 2 of the text part.'
      ..html = "<h1>R2 Reports</h1>\n<p>Here is your report</p>"
      ..attachments.add(FileAttachment(File('example.pdf')));

    try {
      final SendReport sendReport = await send(message, smtpServer);
      _logger.info('');
      print('Message sent: $sendReport');
    } on MailerException catch (e) {
      print('Message not sent.');
      print(e.message);
      for (final Problem p in e.problems) {
        print('Problem: ${p.code}: ${p.msg}');
      }
      throw e.toString();
    }
  }

  Future<Either<ServiceError, File>> generatePDF(List<pw.TableRow> data, String title, String start, String end) async {
    try {
      final int pageNumbers = (data.length / 45).ceil();
      int currentIndex = 0;
      final pw.Document pdf = pw.Document();

      final ByteData img = await rootBundle.load("assets/r2_color_symbol.png");
      final Uint8List imageBytes = img.buffer.asUint8List();

      for (int i = 0; i < pageNumbers; i++) {
        final List<pw.TableRow> rowsSection =
            data.getRange(currentIndex, (currentIndex + 45 > data.length) ? data.length : currentIndex + 45).toList();
        pdf.addPage(
          pw.MultiPage(
            margin: const pw.EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 20),
            pageFormat: PdfPageFormat.standard,
            build: (pw.Context context) {
              return <pw.Widget>[
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                  children: <pw.Widget>[
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(bottom: 10),
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: <pw.Widget>[
                          pw.Expanded(
                            child: pw.Image(
                              pw.MemoryImage(imageBytes),
                              height: 50,
                              width: 50,
                            ),
                          ),
                          pw.Column(
                            children: <pw.Widget>[
                              pw.Text(title),
                              pw.Align(
                                alignment: pw.Alignment.centerLeft,
                                child: pw.Text(
                                  "from: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(start).toLocal())} ",
                                ),
                              ),
                              pw.Align(
                                alignment: pw.Alignment.centerLeft,
                                child: pw.Text(
                                  "to: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(end).toLocal())}",
                                ),
                              ),
                            ],
                          ),
                          pw.Column(
                            children: <pw.Widget>[],
                          ),
                        ],
                      ),
                    ),
                    pw.Table(
                      children: rowsSection,
                    )
                  ],
                ),
              ];
            },
          ),
        );
        currentIndex = currentIndex + 45;
      }

      final File file = File("$title.pdf");
      await file.writeAsBytes(await pdf.save());
      return Right<ServiceError, File>(file);
    } catch (e) {
      return Left<ServiceError, File>(ServiceError(e.toString()));
    }
  }

  // Generate PDF function used for wage report without signature lines
  Future<Either<ServiceError, File>> generateMuliPagePDF(
    List<pw.TableRow> data,
    String title,
    String start,
    String end,
  ) async {
    try {
      final pw.Document pdf = pw.Document();

      final ByteData logoBD = await rootBundle.load('assets/r2_color_symbol.png');
      final Uint8List logo = logoBD.buffer.asUint8List();

      pw.Widget pageHeader(pw.Context ctx) {
        final DateFormat dateFmt = DateFormat('yMMMd').add_jm();
        return pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: <pw.Widget>[
            pw.Image(pw.MemoryImage(logo), height: 50, width: 50),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: <pw.Text>[
                pw.Text(title, style: const pw.TextStyle(fontSize: 16)),
                pw.Text('from: ${dateFmt.format(DateTime.parse(start).toLocal())}'),
                pw.Text('to:   ${dateFmt.format(DateTime.parse(end).toLocal())}'),
              ],
            ),
            pw.SizedBox(width: 50),
          ],
        );
      }

      pdf.addPage(
        pw.MultiPage(
          margin: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 20),
          pageFormat: PdfPageFormat.standard,
          header: pageHeader,
          build: (pw.Context ctx) => [
            pw.Table(children: data),
          ],
        ),
      );

      final File file = File('$title.pdf');
      await file.writeAsBytes(await pdf.save());
      return Right<ServiceError, File>(file);
    } catch (e) {
      return Left<ServiceError, File>(ServiceError(e.toString()));
    }
  }

  Future<Either<ServiceError, File>> generatePDFWageReport(List<List<pw.TableRow>> data, String title, String start, String end) async {
    try {
      final int pageNumbers = data.length;

      final pw.Document pdf = pw.Document();

      final ByteData img = await rootBundle.load("assets/r2_color_symbol.png");
      final Uint8List imageBytes = img.buffer.asUint8List();

      for (int i = 0; i < pageNumbers; i++) {
        final List<pw.TableRow> rowsSection = data[i];
        pdf.addPage(
          pw.MultiPage(
            margin: const pw.EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 20),
            pageFormat: PdfPageFormat.standard,
            build: (pw.Context context) {
              return <pw.Widget>[
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                  children: <pw.Widget>[
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(bottom: 10),
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: <pw.Widget>[
                          pw.Expanded(
                            child: pw.Image(
                              pw.MemoryImage(imageBytes),
                              height: 50,
                              width: 50,
                            ),
                          ),
                          pw.Column(
                            children: <pw.Widget>[
                              pw.Text(title),
                              pw.Align(
                                alignment: pw.Alignment.centerLeft,
                                child: pw.Text(
                                  "from: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(start).toLocal())} ",
                                ),
                              ),
                              pw.Align(
                                alignment: pw.Alignment.centerLeft,
                                child: pw.Text(
                                  "to: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(end).toLocal())}",
                                ),
                              ),
                            ],
                          ),
                          pw.Column(
                            children: <pw.Widget>[],
                          ),
                        ],
                      ),
                    ),
                    pw.Table(
                      children: rowsSection,
                    ),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Align(
                      alignment: pw.Alignment.centerRight,
                      child: pw.Text(
                        "I have reviewed this time report and have noted, initialed and dated any corrections.",
                      ),
                    ),
                    pw.Align(
                      alignment: pw.Alignment.centerRight,
                      child: pw.Text(
                        "This report is accurate record of my hours and pay for the reported date range.",
                      ),
                    ),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Align(
                      alignment: pw.Alignment.centerRight,
                      child: pw.Text(
                        "___________________________     ___________________________     ______________",
                      ),
                    ),
                    pw.Align(
                      alignment: pw.Alignment.centerRight,
                      child: pw.Text(
                        "Signature                                            Printed Name                                     Date                    ",
                      ),
                    ),
                  ],
                ),
              ];
            },
          ),
        );
      }

      final File file = File("$title.pdf");
      await file.writeAsBytes(await pdf.save());
      return Right(file);
    } catch (e) {
      return Left(ServiceError(e.toString()));
    }
  }

  Future<Either<ServiceError, File>> generateMultiPagePDF(List<pw.TableRow> data, String title, String start, String end) async {
    try {
      final pw.Document pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.standard,
          build: (pw.Context context) {
            return <pw.Column>[
              pw.Column(
                children: <pw.Widget>[
                  pw.Text(title),
                  pw.Text(
                    "from: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(start).toLocal())} to: ${DateFormat('yMMMd').add_jm().format(DateTime.parse(end).toLocal())}",
                  ),
                  pw.Table(
                    children: data,
                  )
                ],
              )
            ];
          },
        ),
      );

      final File file = File("$title.pdf");
      await file.writeAsBytes(await pdf.save());
      return Right(file);
    } catch (e) {
      return Left(ServiceError(e.toString()));
    }
  }
}
