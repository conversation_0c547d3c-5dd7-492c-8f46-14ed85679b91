
// class CustomDatePicker extends StatelessWidget {
//   CustomDatePicker({
//     required this.textEditingController,
//     required this.confirm,
//     // required this.refresh,
//   });

//   TextEditingController textEditingController;
//   Function() confirm;
//   // Function() refresh;

//   RxString currentDate = DateFormat('yMMMd').format(DateTime.now()).obs;
//   DateTime selectedDate = DateTime.now();

//   @override
//   Widget build(BuildContext context) {
//     textEditingController.text = DateFormat('yMMMd').format(selectedDate);
//     final RxBool isToday = true.obs;

//     return Row(
//       children: <Widget>[
//         Container(
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(10),
//             border: Border.all(
//               color: R2Colors.neutral300,
//               width: 2,
//             ),
//           ),
//           child: ConstrainedBox(
//             constraints: const BoxConstraints(maxWidth: 250),
//             child: TextFormField(
//               controller: textEditingController,
//               decoration: const InputDecoration(
//                 hintText: 'Date',
//                 border: InputBorder.none,
//                 contentPadding: EdgeInsets.fromLTRB(10, 0, 10, 0),
//               ),
//               onTap: () {
//                 showModalBottomSheet(
//                   shape: const RoundedRectangleBorder(
//                     borderRadius: BorderRadius.only(
//                       topLeft: Radius.circular(20),
//                       topRight: Radius.circular(20),
//                     ),
//                   ),
//                   context: context,
//                   builder: (_) => Column(
//                     children: <Widget>[
//                       const Padding(
//                         padding: EdgeInsets.only(top: 15),
//                         child: Text(
//                           'Select Date',
//                           style: TextStyle(fontSize: 24),
//                         ),
//                       ),
//                       ConstrainedBox(
//                         constraints: const BoxConstraints(maxWidth: 300, maxHeight: 250, minHeight: 200),
//                         child: CupertinoDatePicker(
//                           mode: CupertinoDatePickerMode.date,
//                           onDateTimeChanged: (DateTime dateTime) {
//                             textEditingController.text = DateFormat('yMMMd').format(dateTime);
//                             selectedDate = dateTime;
//                             if (textEditingController.text != currentDate.value) {
//                               isToday.value = false;
//                             }
//                           },
//                           initialDateTime: DateTime.now(),
//                         ),
//                       ),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: <Widget>[
//                           Padding(
//                             padding: const EdgeInsets.all(20),
//                             child: DialogButton(
//                               buttonType: EDialogButtonType.ADD,
//                               onTapped: confirm,
//                               buttonText: "Select",
//                             ),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ).whenComplete(() {
//                   if (textEditingController.text == currentDate.value) {
//                     isToday.value = true;
//                   }
//                 });
//               },
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
