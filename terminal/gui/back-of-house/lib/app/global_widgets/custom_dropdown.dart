import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomDropdown<T> extends StatelessWidget {
  CustomDropdown({
    this.onChanged,
    required this.items,
    required this.value,
    this.validator,
    this.borderColor,
    this.color,
    this.hint,
    this.hintColor,
    this.padding,
    this.margin,
    this.showArrow = true,
  });

  final ValueChanged<T?>? onChanged;
  List<DropdownMenuItem<T>>? items;
  T? value;
  String? hint;
  FormFieldValidator? validator;
  Color? borderColor;
  Color? color;
  Color? hintColor;
  EdgeInsetsGeometry? margin;
  EdgeInsetsGeometry? padding;
  bool showArrow;

  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      margin: margin,
      color: color ?? R2Colors.neutral100,
      shape: RoundedRectangleBorder(
        side: BorderSide(
          color: borderColor ?? R2Colors.neutral300,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(10),
        child: DropdownButtonFormField<T>(
          iconSize: showArrow ? 20 : 0,
          focusColor: Colors.transparent,
          style: const TextStyle(
            overflow: TextOverflow.visible,
            color: R2Colors.neutral600,
          ),
          isExpanded: true,
          icon: showArrow
              ? const Icon(
                  Icons.arrow_downward,
                )
              : null,
          hint: hint != null ? Text(hint!) : const Text("Select"),
          decoration: InputDecoration(
            border: const OutlineInputBorder(borderSide: BorderSide.none),
            contentPadding: const EdgeInsets.all(8),
            hintStyle: TextStyle(color: hintColor ?? Colors.black),
            isDense: true,
          ),
          onChanged: onChanged,
          items: items,
          validator: validator,
          value: value,
        ),
      ),
    );
  }
}
