import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// ignore: must_be_immutable
class CustomFormField extends StatelessWidget {
  CustomFormField(
      {required this.controller,
      this.key,
      this.enabled,
      this.validator,
      this.borderColor,
      this.onChanged,
      this.hint,
      this.prefixIcon,
      this.maxLength,
      this.suffixIcon,
      this.textAlign,
      this.suffixText,
      this.prefixText,
      this.inputFormatters,
      this.suffix,
      this.inputPadding,
      this.onConfirm,
      this.isObscure});

  TextEditingController controller;
  Key? key;
  String? hint;
  Function(String)? onChanged;
  bool? enabled;
  Color? borderColor;
  Icon? prefixIcon;
  IconButton? suffixIcon;
  bool? isObscure;
  int? maxLength;
  String? prefixText;
  String? suffixText;
  List<TextInputFormatter>? inputFormatters;
  FormFieldValidator? validator;
  TextAlign? textAlign;
  Widget? suffix;
  EdgeInsets? inputPadding;
  final Function(String)? onConfirm;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        border: Border.all(color: R2Colors.neutral300),
        borderRadius: BorderRadius.circular(10),
        color: R2Colors.neutral100,
      ),
      child: VirtualKeyboardWrapper(
        textEditingController: controller,
        onConfirm: onConfirm,
        inputFormatters: inputFormatters,
        maxLength: maxLength,
        isObscure: isObscure ?? false,
        child: TextFormField(
          decoration: InputDecoration(
            contentPadding: inputPadding ?? const EdgeInsets.only(left: 10),
            border: InputBorder.none,
            counterText: "",
            hintText: hint,
            hintStyle: const TextStyle(color: R2Colors.neutral300),
            prefixIcon: prefixIcon,
            prefixIconColor: R2Colors.neutral300,
            suffixIcon: suffixIcon,
            prefixText: prefixText,
            suffix: suffix,
            suffixText: suffixText,
          ),
          inputFormatters: inputFormatters,
          textAlign: textAlign ?? TextAlign.end,
          controller: controller,
          obscureText: isObscure ?? false,
          enabled: enabled,
          style: const TextStyle(color: R2Colors.neutral600),
          maxLength: maxLength,
          maxLines: 1,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
