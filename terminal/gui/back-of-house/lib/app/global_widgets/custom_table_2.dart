import 'package:backoffice/app/global_widgets/custom_checkbox_tile.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum CustomRowStyle { basic, subtotal, total }

enum TextCellStyle {
  basic,
  indent,
  subheader,
}

// ignore: must_be_immutable
class CustomTable2 extends StatelessWidget {
  CustomTable2({
    required this.columnConfig,
    required this.rows,
    this.showCheckBox = false,
    this.onSelectAll,
  }) : assert(
          () {
            if (rows.isNotEmpty) {
              final int cellCount = columnConfig.length;
              if (rows.any((CustomTableRow2 row) => row.children.length != cellCount)) {
                throw FlutterError(
                  'Table contains irregular row lengths.\n'
                  'Every TableRow in a Table must have the same number of children, so that every cell is filled. '
                  'Otherwise, the table will contain holes.',
                );
              }
            }
            return true;
          }(),
        );

  List<ColumnConfig2> columnConfig;
  List<CustomTableRow2> rows;
  void Function(bool)? onSelectAll;
  bool showCheckBox;
  RxBool? selectAllCheck;

  Row generateHeaderCells() {
    final UniqueKey key = UniqueKey();

    final List<Widget> cells = <Widget>[];
    for (int i = 0; i < columnConfig.length; i++) {
      cells.add(
        Expanded(
          child: Align(
            alignment: columnConfig[i].alignment,
            child: Text(
              columnConfig[i].title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: R2Colors.neutral600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      );
    }
    bool selectAllCheck() {
      for (final CustomTableRow2 row in rows) {
        if (row.rowCheckValue!) {
          continue;
        } else {
          return false;
        }
      }
      return true;
    }

    return Row(
      children: <Widget>[
        if (showCheckBox)
          Checkbox(
            key: key,
            value: selectAllCheck(),
            onChanged: (bool? value) {
              onSelectAll!(value!);
            },
          ),
        ...cells,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController controller = ScrollController();
    return Row(
      children: <Widget>[
        Expanded(
          child: Column(
            children: <Widget>[
              Expanded(child: generateHeaderCells()),
              Expanded(
                flex: 8,
                child: SingleChildScrollView(
                  controller: controller,
                  child: Column(
                    children: rows,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class ColumnConfig2 {
  const ColumnConfig2(
    this.title, {
    this.alignment = Alignment.centerLeft,
  });

  final String title;
  final AlignmentGeometry alignment;
}

// ignore: must_be_immutable
class CustomTableRow2 extends StatelessWidget {
  CustomTableRow2({
    required this.children,
    this.style = CustomRowStyle.basic,
    this.padding = EdgeInsets.zero,
    this.onTapped,
    this.divider = false,
    this.dividerColor,
    this.rowHeight,
    this.showCheckBox = false,
    this.lastRowDivider = false,
    this.rowCheckValue,
    this.onSelectChanged,
  });

  List<Widget> children;
  CustomRowStyle style = CustomRowStyle.basic;
  EdgeInsetsGeometry padding;
  void Function()? onTapped;
  void Function(bool?)? onSelectChanged;
  bool divider;
  Color? dividerColor;
  double? rowHeight;
  bool lastRowDivider;
  bool showCheckBox;
  bool? rowCheckValue;
  RxBool checkBoxValue = false.obs;

  Map<CustomRowStyle, TextStyle> rowStyleKey = <CustomRowStyle, TextStyle>{
    CustomRowStyle.basic: const TextStyle(),
    CustomRowStyle.subtotal: const TextStyle(
      fontWeight: FontWeight.w600,
      color: R2Colors.neutral800,
    ),
    CustomRowStyle.total: const TextStyle(fontWeight: FontWeight.bold, color: R2Colors.neutral700)
  };

  List<Widget> alignChildren(
    List<Widget> children,
    List<ColumnConfig2> columnConfig,
  ) {
    final List<Widget> alignedChildren = <Widget>[];

    for (int i = 0; i < children.length; i++) {
      alignedChildren.add(
        DefaultTextStyle.merge(
          style: rowStyleKey[style],
          child: Flexible(
            child: Align(
              alignment: columnConfig[i].alignment,
              child: children[i],
            ),
          ),
        ),
      );
    }

    return alignedChildren;
  }

  @override
  Widget build(BuildContext context) {
    final CustomTable2? table = context.findAncestorWidgetOfExactType<CustomTable2>();
    final List<ColumnConfig2>? columnConfig = table?.columnConfig;

    assert(
      columnConfig != null,
      "Custom table row must be a child of a custom table",
    );

    if (divider) {
      return Column(
        children: <Widget>[
          Divider(
            color: dividerColor ?? Colors.black,
          ),
          Container(
            height: rowHeight,
            child: CustomCheckboxListTile(
              onChanged: onSelectChanged,
              iconSize: rowHeight,
              value: rowCheckValue ?? false,
              title: Row(
                children: <Widget>[
                  ...alignChildren(children, columnConfig!),
                ],
              ),
            ),
          ),
          if (lastRowDivider)
            Divider(
              color: dividerColor ?? Colors.black,
            )
          else
            Container(),
        ],
      );
    } else {
      return Padding(
        padding: padding,
        child: Row(
          children: alignChildren(children, columnConfig!),
        ),
      );
    }
  }
}

// ignore: must_be_immutable
class CustomTextCell2 extends StatelessWidget {
  CustomTextCell2(
    this.text, {
    this.style = TextCellStyle.basic,
  });

  TextCellStyle style;
  String text;

  @override
  Widget build(BuildContext context) {
    if (style == TextCellStyle.subheader) {
      return Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      );
    } else if (style == TextCellStyle.indent) {
      return Padding(
        padding: const EdgeInsets.only(left: 12),
        child: Text(
          text,
        ),
      );
    } else {
      return Align(
        child: Text(
          text,
        ),
      );
    }
  }
}
