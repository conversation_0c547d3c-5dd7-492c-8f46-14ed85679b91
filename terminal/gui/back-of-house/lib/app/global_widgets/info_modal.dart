import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class InfoModal extends StatelessWidget {
  InfoModal({
    required this.title,
    required this.content,
    this.toolTip,
    this.buttonSize,
    this.buttonTapSize,
  });

  String title;
  String? toolTip;
  Widget content;
  double? buttonSize;
  double? buttonTapSize;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      padding: EdgeInsets.zero,
      splashRadius: 10,
      constraints: const BoxConstraints(),
      icon: Icon(
        Icons.info,
        size: buttonSize ?? 24,
      ),
      onPressed: () {
        showCupertinoDialog(
          barrierDismissible: true,
          context: context,
          builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text(title),
            content: Column(
              children: <Widget>[content],
            ),
            actions: <CupertinoDialogAction>[
              CupertinoDialogAction(
                child: const Text(
                  "Close",
                  style: TextStyle(color: R2Colors.primary500),
                ),
                onPressed: () {
                  Get.back();
                },
              )
            ],
          ),
        );
      },
      tooltip: toolTip,
      color: R2Colors.neutral500,
    );
  }
}
