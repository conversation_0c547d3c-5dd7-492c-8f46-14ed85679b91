import 'package:backoffice/app/modules/dashboard/controller.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class LargeWidgetTemplate extends StatelessWidget {
  LargeWidgetTemplate({
    required this.child,
    this.title,
    this.icon,
    this.hasSwitch = false,
  });
  final DashboardController _dashboardController = Get.find<DashboardController>();

  Widget child;
  String? title = "";
  Widget? icon;
  bool hasSwitch;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 450),
        child: AspectRatio(
          aspectRatio: 1,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              border: Border.all(color: R2Colors.neutral300),
              color: R2Colors.neutral100,
            ),
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Padding(
                              padding: const EdgeInsets.all(5), child: icon ?? const SizedBox(height: 24, width: 24),
                              // FaIcon(
                              //   FontAwesomeIcons.fileInvoiceDollar,
                              //   color: R2Colors.neutral500,
                              //   size: 35,
                              // ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(5),
                              child: Text(
                                title ?? "",
                                style: const TextStyle(
                                  color: R2Colors.neutral500,
                                  fontSize: 30,
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (hasSwitch)
                          Transform.scale(
                            scaleX: 1.75,
                            scaleY: 1.50,
                            child: Obx(
                              () => Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: Switch(
                                  value: _dashboardController.switchBool.value,
                                  splashRadius: 0,
                                  onChanged: (bool value) {
                                    _dashboardController.switchBool.value = value;
                                  },
                                ),
                              ),
                            ),
                          )
                      ],
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: child,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
