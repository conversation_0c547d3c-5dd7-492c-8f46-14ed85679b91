import 'package:backoffice/app/global_widgets/info_modal.dart';
import 'package:flutter/material.dart';

Widget makeHintIcon({
  required String hintTitle,
  required Widget hintContent,
  double? buttonSize,
}) {
  return Padding(
    padding: const EdgeInsets.only(left: 8),
    child: InfoModal(
      title: hintTitle,
      content: hintContent,
      buttonSize: buttonSize,
    ),
  );
}

Widget wrapWithPostHint(
  Widget child, {
  required String hint,
  required String hintTitle,
  required Widget hintContent,
  double? buttonSize,
}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.end,
    children: <Widget>[
      child,
      Text(
        hint,
        style: const TextStyle(fontSize: 14),
      ),
      makeHintIcon(hintTitle: hintTitle, hintContent: hintContent, buttonSize: buttonSize),
    ],
  );
}
