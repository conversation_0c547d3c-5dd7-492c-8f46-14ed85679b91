import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/picker.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/global_widgets/custom_time_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class MenuTimePicker extends StatelessWidget {
  MenuTimePicker({
    required this.milliseconds,
    this.useDeptTimeString = false,
    this.onChanged,
    this.title,
    this.leadingIcon,
  });

  String? title;
  RxInt milliseconds;
  RxInt hourController = RxInt(12);
  RxInt minuteController = RxInt(0);
  RxBool isPmController = RxBool(true);
  Widget? leadingIcon;

  /// Variables for the department add/update time picker.
  /// This allows for specific logic used in the department add/update view.
  bool useDeptTimeString;
  void Function(int)? onChanged;

  Future<void> onTapTime() async {
    final int hour = hourController.value;
    final int min = minuteController.value;
    final bool pm = isPmController.value;
    await Get.defaultDialog(
      title: "Start Time",
      content: Column(
        children: <Widget>[
          CustomTimePicker(
            hourController: hourController,
            minuteController: minuteController,
            pmTime: isPmController,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () {
                  Get.back(result: false);
                },
              ),
              DialogButton(
                buttonType: EDialogButtonType.AFFIRMATIVE,
                onTapped: () {
                  Get.back(result: true);
                },
              ),
            ],
          ),
        ],
      ),
    ).then((dynamic value) {
      if (value != true) {
        hourController.value = hour;
        minuteController.value = min;
        isPmController.value = pm;
      }
      milliseconds.value = intToMilliseconds(
        hour: hourController.value,
        minute: minuteController.value,
        pm: isPmController.value,
      );
    });
  }

  String get getTimeString {
    final int hour = hourController.value;
    final int minute = minuteController.value;
    return "$hour:${minute < 10 ? "0" : ""}$minute ${isPmController.value ? "PM" : "AM"}";
  }

  void millisecondsToInts(int milliseconds) {
    final int minutesFromMS = Duration(milliseconds: milliseconds).inMinutes;

    minuteController.value = minutesFromMS % 60;

    int hour = minutesFromMS ~/ 60;

    if (hour > 11) {
      isPmController.value = true;
    } else {
      isPmController.value = false;
    }
    if (hour > 12) hour -= 12;

    hourController.value = hour == 0 ? 12 : hour;
  }

  /// Converts milliseconds to hour:minute with AM/PM for departments.
  void deptMillisecondsToInts(int milliseconds) {
    /// counting total ms from midnight.
    final TimeOfDay time = TimeOfDay.fromDateTime(
      DateTime(0).add(Duration(milliseconds: milliseconds)),
    );

    hourController.value = time.hourOfPeriod;
    if (time.hourOfPeriod == 0) {
      // When the 24-hour time is 00:00 (midnight), we display it as 12:00 AM
      hourController.value = 12;
    }

    minuteController.value = time.minute;

    isPmController.value = false;
    if (time.period == DayPeriod.pm) {
      isPmController.value = true;
    }
  }

  int intToMilliseconds({
    required int hour,
    required int minute,
    required bool pm,
  }) {
    final bool addHours = pm && hour != 12;
    if (hour == 12 && !pm) hour = 0;
    final int hours = addHours ? hour + 12 : hour;
    return Duration(minutes: (hours * 60) + minute).inMilliseconds;
  }

  @override
  Widget build(BuildContext context) {
    // if loading for department times use depMillisecondsToInts()
    useDeptTimeString ? deptMillisecondsToInts(milliseconds.value) : millisecondsToInts(milliseconds.value);
    return MenuPicker(
      leadingIcon: leadingIcon,
      title: title,
      child: GestureDetector(
        onTap: onTapTime,
        child: Container(
          width: 100,
          height: 30,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(
                4,
              ),
            ),
            color: R2Colors.neutral200,
          ),
          child: Center(
            child: Obx(
              () => Text(
                getTimeString,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
