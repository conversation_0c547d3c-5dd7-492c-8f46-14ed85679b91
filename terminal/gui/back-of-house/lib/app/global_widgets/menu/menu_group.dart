import 'package:backoffice/app/global_widgets/info_modal.dart';
import 'package:backoffice/app/global_widgets/menu/components/hint.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';

class MenuGroup extends StatelessWidget {
  /// Children must be a MenuComponent or Obx MenuComponent. Type checked at runtime.
  const MenuGroup({
    required this.title,
    required this.children,
    this.isList = false,
    this.hintButton = false,
    this.hintTitle,
    this.hintContent,
    this.hint,
    this.buttonSize,
    this.buttonTapSize,
    this.transparent = false,
  });

  final String title;
  final List<Widget> children;
  final bool isList;
  final bool hintButton;
  final String? hintTitle;
  final String? hint;
  final Widget? hintContent;
  final double? buttonSize;
  final double? buttonTapSize;
  final bool transparent;

  @override
  Widget build(BuildContext context) {
    final List<Widget> group = <Widget>[];

    for (int i = 0; i < children.length; i++) {
      if (i != 0) group.add(MenuComponent.divider);
      group.add(children[i]);
    }

    return IntrinsicWidth(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          MenuGroupLabel(
            title,
            hintButton: hintButton,
            hintTitle: hintTitle,
            hintContent: hintContent,
            hint: hint,
            buttonSize: buttonSize,
            buttonTapSize: buttonTapSize,
          ),
          Container(
            decoration: BoxDecoration(
              color: transparent ? Colors.transparent : R2Colors.neutral100,
              border: Border.all(color: R2Colors.neutral300),
              borderRadius: const BorderRadius.all(
                Radius.circular(8),
              ),
            ),
            child: Column(
              children: group,
            ),
          ),
        ],
      ),
    );
  }
}

class MenuGroupLabel extends StatelessWidget {
  const MenuGroupLabel(
    this.text, {
    this.hintButton = false,
    this.hintTitle,
    this.hintContent,
    this.hint,
    this.buttonSize,
    this.buttonTapSize,
  });

  final String text;
  final bool hintButton;
  final String? hintTitle;
  final String? hint;
  final Widget? hintContent;
  final double? buttonSize;
  final double? buttonTapSize;

  @override
  Widget build(BuildContext context) {
    final Widget label = Text(
      text,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 18,
        color: R2Colors.neutral700,
      ),
    );

    if (hintButton) {
      return wrapWithPostHint(
        label,
        hint: hint!,
        hintTitle: hintTitle!,
        hintContent: hintContent!,
        buttonSize: buttonSize,
      );
    }
    return label;
  }
}
