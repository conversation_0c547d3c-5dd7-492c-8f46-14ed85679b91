import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/prep_device_select/dialog.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/enums/prep_device_type.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';

const String GET_SYSTEM_DEVICES_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

class PrepDeviceSelect extends StatelessWidget {
  PrepDeviceSelect({required this.selectedDevices});
  final RxInt selectedDevices;
  final RxList<PrepDevice> deviceList = <PrepDevice>[].obs;
  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        ClipRRect(
          clipBehavior: Clip.hardEdge,
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: R2Colors.neutral100,
              border: Border.all(
                color: R2Colors.neutral100,
              ),
              borderRadius: const BorderRadius.all(Radius.circular(5)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(2),
              child: Subscription<Obx>(
                options: SubscriptionOptions<Obx>(
                  document: g.parseString(
                    GET_SYSTEM_DEVICES_QUERY,
                  ),
                ),
                builder: (
                  QueryResult<dynamic> result, {
                  dynamic refetch,
                  dynamic fetchMore,
                }) {
                  if (result.isLoading) {
                    return const Flex(
                      direction: Axis.vertical,
                      children: <Widget>[
                        Center(
                          child: CircularProgressIndicator(),
                        ),
                      ],
                    );
                  }
                  if (result.hasException) {
                    return const Flex(
                      direction: Axis.vertical,
                      children: <Widget>[
                        Center(
                          child: Text("Failed to load Devices"),
                        ),
                      ],
                    );
                  }

                  final List<SystemDeviceJsonRecord> systemDeviceJsonRecord = (result.data!['json_record'] as List<dynamic>)
                      .map(
                        (dynamic systemDeviceRecord) => SystemDeviceJsonRecord.fromJson(
                          systemDeviceRecord as Map<String, dynamic>,
                        ),
                      )
                      .toList();

                  deviceList.value = systemDeviceJsonRecord.first.document.prep;

                  return Obx(
                    () {
                      final List<PrepDevice> filteredDevices = deviceList.filter((PrepDevice dev) {
                        return (selectedDevices.value & (1 << dev.idx)) == (1 << dev.idx);
                      }).toList();

                      return selectedDevices.value < 1
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Padding(
                                  padding: EdgeInsets.fromLTRB(0, 20, 0, 10),
                                  child: Text(
                                    "No Prep Devices",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: R2Colors.neutral500,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : DataTable(
                              columnSpacing: 0,
                              dataRowColor: MaterialStateProperty.all(
                                Colors.white,
                              ),
                              headingRowColor: MaterialStateProperty.all(
                                R2Colors.neutral100,
                              ),
                              showCheckboxColumn: false,
                              columns: const <DataColumn>[
                                DataColumn(
                                  label: Expanded(child: Text("Device Description")),
                                ),
                                DataColumn(
                                  label: Expanded(
                                    child: Align(alignment: Alignment.centerLeft, child: Text("Device IP/Terminal")),
                                  ),
                                ),
                              ],
                              rows: List<DataRow>.generate(filteredDevices.length, (int index) {
                                final PrepDevice device = filteredDevices[index];

                                return DataRow(
                                  cells: <DataCell>[
                                    DataCell(
                                      Row(
                                        children: <Widget>[
                                          Text(
                                            device.desc,
                                          ),
                                        ],
                                      ),
                                    ),
                                    DataCell(
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          Text(
                                            device.type != PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex
                                                ? device.IP
                                                : device.rcptTerm.toString(),
                                          ),
                                          TextButton(
                                            style: ButtonStyle(
                                              padding: MaterialStateProperty.all(EdgeInsets.zero),
                                              backgroundColor: MaterialStateProperty.all(Colors.transparent),
                                            ),
                                            onPressed: () {
                                              selectedDevices.value = selectedDevices.value ^ (1 << device.idx);
                                            },
                                            child: const Text(
                                              'Remove',
                                              style: TextStyle(fontSize: 12, color: R2Colors.red500),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                );
                              }),
                            );
                    },
                  );
                },
              ),
            ),
          ),
        ),
        TextButton(
          onPressed: () async {
            await Get.defaultDialog(
              title: "Add Device",
              content: PrepDeviceSelectDialog(
                selectedDevices: selectedDevices,
                deviceList: deviceList,
              ),
              cancel: DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                buttonText: "Close",
                onTapped: Get.back,
              ),
            );
          },
          child: const Padding(
            padding: EdgeInsets.all(15),
            child: Text(
              "Add Device",
              style: TextStyle(color: R2Colors.primary500),
            ),
          ),
        )
      ],
    );
  }
}
