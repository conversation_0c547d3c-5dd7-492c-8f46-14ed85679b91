import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class R2<PERSON>rapper extends StatelessWidget {
  R2Wrapper({Key? key, required this.child, this.height}) : super(key: key);

  Widget child;
  double? height;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: R2Colors.neutral100,
        borderRadius: const BorderRadius.all(
          Radius.circular(12),
        ),
        border: Border.all(color: R2Colors.neutral300),
      ),
      child: child,
    );
  }
}
