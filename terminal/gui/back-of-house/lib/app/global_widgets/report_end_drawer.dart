// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_datepicker.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class ReportEndDrawer extends StatelessWidget {
  const ReportEndDrawer({
    this.title,
    required this.formKey,
    this.dateController,
    required this.terminalValue,
    required this.terminalOnChanged,
    required this.scaffoldKey,
    required this.terminalList,
    required this.isLoading,
    this.dateOnChanged,
    // this.enablePrinting = false,
    // this.printFunction,
    this.employeeList = const <Employee>[],
    this.selectedEmployee,
    this.employeeOnChanged,
    this.choiceChildren,
    this.choiceOnChanged,
    this.choiceValue,
    this.dateRangeController,
    this.rangeOnChanged,
    this.rangeValue,
    this.rangeOptions,
  });

  //general variables
  final String? title;
  final GlobalKey<FormState> formKey;
  final RxBool isLoading;
  final GlobalKey<ScaffoldState> scaffoldKey;

  //terminal dropdown variables
  final List<dynamic> terminalList;
  final Rxn<int> terminalValue;
  final void Function(dynamic)? terminalOnChanged;

  //employee dropdown variables
  final List<Employee> employeeList;
  final Rxn<int>? selectedEmployee;
  final void Function(dynamic)? employeeOnChanged;

  // date picker (NOT date range picker)
  final TextEditingController? dateController;
  final void Function(String)? dateOnChanged;

  //Rage dropdown variables
  final RxInt? rangeValue;
  final List<String>? rangeOptions;

  // choice selector variables
  final Map<String, Widget>? choiceChildren;
  final void Function(String)? choiceOnChanged;
  final RxString? choiceValue;

  /// date range selector variables
  final void Function(dynamic)? rangeOnChanged;
  final DateRangePickerController? dateRangeController;

  //commented out for now
  // final bool? enablePrinting;
  // final void Function()? printFunction;

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Drawer(
        width: 400,
        backgroundColor: R2Colors.white,
        child: Obx(
          () => Stack(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: <Widget>[
                    Text(
                      title ?? "",
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 10, bottom: 10),
                        child: FormWrapper(
                          formKey: formKey,
                          children: <Widget>[
                            MenuGroup(
                              title: "Filters",
                              children: <Widget>[
                                if (dateController != null)
                                  MenuDatePicker(
                                    title: "Date",
                                    controller: dateController,
                                    onChanged: (String value) {
                                      if (dateRangeController != null) {
                                        dateRangeController!.selectedRange = PickerDateRange(
                                          DateTime.parse(value),
                                          null,
                                        );
                                      }
                                      dateOnChanged?.call(value);
                                    },
                                  ),
                                if (rangeValue != null)
                                  Obx(
                                    () => MenuDropdown<int>(
                                      title: "Range",
                                      value: rangeValue!.value,
                                      onChanged: (int? value) {
                                        if (rangeValue != null) rangeValue!.value = value ?? 0;
                                        rangeOnChanged?.call(value);
                                      },
                                      items: List<DropdownMenuItem<int>>.generate(
                                        rangeOptions!.length,
                                        (int index) => DropdownMenuItem<int>(
                                          value: index,
                                          child: Text(rangeOptions![index]),
                                        ),
                                      ),
                                    ),
                                  ),
                                Obx(
                                  () => MenuDropdown<dynamic>(
                                    title: "Scope",
                                    value: terminalValue.value,
                                    onChanged: terminalOnChanged,
                                    items: <DropdownMenuItem<int>>[
                                      const DropdownMenuItem<int>(
                                        child: Text("Store"),
                                      ),
                                      ...terminalList.map(
                                        (dynamic e) => DropdownMenuItem<int>(
                                          value: int.parse(e["idx"].toString()),
                                          child: Text(e["desc"].toString()),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (employeeList.isNotEmpty)
                                  Obx(
                                    () => MenuDropdown<dynamic>(
                                      title: "Employee",
                                      value: selectedEmployee?.value,
                                      onChanged: employeeOnChanged,
                                      items: <DropdownMenuItem<int?>>[
                                        const DropdownMenuItem<int?>(
                                          child: Text("All Employees"),
                                        ),
                                        ...List<DropdownMenuItem<int>>.generate(
                                          employeeList.length,
                                          (int index) => DropdownMenuItem<int>(
                                            value: employeeList[index].id,
                                            child: Text(employeeList[index].employee_full_name ?? "No Name"),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (choiceChildren != null && choiceOnChanged != null && choiceValue != null)
                                  Row(
                                    children: <Widget>[
                                      const Expanded(child: MenuLabel(text: "Order")),
                                      Obx(
                                        () => CupertinoSegmentedControl<String>(
                                          children: choiceChildren!,
                                          onValueChanged: choiceOnChanged!,
                                          borderColor: R2Colors.primary500,
                                          groupValue: choiceValue!.value,
                                          selectedColor: R2Colors.primary500,
                                          unselectedColor: R2Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                if (dateRangeController != null)
                                  SfDateRangePicker(
                                    backgroundColor: R2Colors.white,
                                    headerStyle: const DateRangePickerHeaderStyle(
                                      backgroundColor: R2Colors.white,
                                    ),
                                    selectionColor: R2Colors.primary500,
                                    rangeSelectionColor: R2Colors.primary300,
                                    startRangeSelectionColor: R2Colors.primary500,
                                    endRangeSelectionColor: R2Colors.primary500,
                                    todayHighlightColor: R2Colors.primary500,
                                    controller: dateRangeController,
                                    selectionMode: DateRangePickerSelectionMode.range,
                                    monthViewSettings: const DateRangePickerMonthViewSettings(
                                      enableSwipeSelection: false,
                                    ),
                                    onSelectionChanged: rangeOnChanged,
                                  ),
                              ],
                            ),
                            // if (enablePrinting!)
                            //   MenuGroup(
                            //     title: "Print",
                            //     children: <MenuButton>[
                            //       MenuButton(
                            //         title: 'test',
                            //         onPressed: printFunction,
                            //       ),
                            //     ],
                            //   ),
                          ],
                        ),
                      ),
                    ),
                    DialogButton(
                      buttonType: EDialogButtonType.DESTRUCTIVE,
                      buttonText: "Close",
                      onTapped: () => scaffoldKey.currentState!.closeEndDrawer(),
                    ),
                  ],
                ),
              ),
              if (isLoading.value)
                Column(
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        color: R2Colors.neutral100.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class DailySalesReportDrawer extends StatelessWidget {
  const DailySalesReportDrawer({
    required this.formKey,
    required this.isLoading,
    required this.scaffoldKey,
    required this.terminalList,
    required this.employeeList,
    this.terminalOnChanged,
    this.dateController,
    this.dateOnChanged,
    this.rangeValue,
    this.rangeOnChanged,
    this.dateRangeController,
    this.choiceChildren,
    this.choiceOnChanged,
    this.choiceValue,
    this.selectedEmployee,
    this.employeeOnChanged,
    this.title,
    this.rangeOptions,
    this.selectedTerminals,
  });

  // general variables
  final String? title;
  final GlobalKey<FormState> formKey;
  final RxBool isLoading;
  final GlobalKey<ScaffoldState> scaffoldKey;

  // employee dropdown variables
  final List<Employee> employeeList;
  final Rxn<int>? selectedEmployee;
  final void Function(dynamic)? employeeOnChanged;

  // date picker (NOT date range picker)
  final TextEditingController? dateController;
  final void Function(String)? dateOnChanged;

  //Rage dropdown variables
  final RxInt? rangeValue;
  final List<String>? rangeOptions;

  // choice selector variables
  final Map<String, Widget>? choiceChildren;
  final void Function(String)? choiceOnChanged;
  final RxString? choiceValue;

  // date range selector variables
  final void Function(dynamic)? rangeOnChanged;
  final DateRangePickerController? dateRangeController;

  // for terminal checkboxes
  final List<dynamic> terminalList;
  final void Function(dynamic)? terminalOnChanged;
  final RxList<int>? selectedTerminals;

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Drawer(
        width: 400,
        backgroundColor: R2Colors.white,
        child: Obx(
          () => Stack(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: <Widget>[
                    Text(
                      title ?? "",
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 10, bottom: 10),
                        child: FormWrapper(
                          formKey: formKey,
                          children: <Widget>[
                            MenuGroup(
                              title: "Filters",
                              children: <Widget>[
                                if (dateController != null)
                                  MenuDatePicker(
                                    title: "Date",
                                    controller: dateController,
                                    onChanged: (String value) {
                                      if (dateRangeController != null) {
                                        dateRangeController!.selectedRange = PickerDateRange(
                                          DateTime.parse(value),
                                          null,
                                        );
                                      }
                                      dateOnChanged?.call(value);
                                    },
                                  ),
                                if (rangeOptions != null) // don't show dropdown if rangeOptions is null
                                  Obx(
                                    () => MenuDropdown<int>(
                                      title: "Range",
                                      value: rangeValue!.value,
                                      onChanged: (int? value) {
                                        if (rangeValue != null) rangeValue!.value = value ?? 0;
                                        rangeOnChanged?.call(value);
                                      },
                                      items: List<DropdownMenuItem<int>>.generate(
                                        rangeOptions!.length,
                                        (int index) => DropdownMenuItem<int>(
                                          value: index,
                                          child: Text(rangeOptions![index]),
                                        ),
                                      ),
                                    ),
                                  ),
                                Obx(
                                  () => Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      CheckboxListTile(
                                        title: const Text("Store"),
                                        value: selectedTerminals?.contains(-1),
                                        onChanged: (bool? selected) {
                                          if (selected == true) {
                                            selectedTerminals?.clear();
                                            selectedTerminals?.add(-1);
                                          } else {
                                            selectedTerminals?.remove(-1);
                                            if (selectedTerminals?.isEmpty ?? true) {
                                              selectedTerminals?.add(0);
                                            }
                                          }
                                          terminalOnChanged?.call(selectedTerminals);
                                        },
                                      ),
                                      ...terminalList.map((dynamic e) {
                                        final int id = int.parse(e["idx"].toString());
                                        return CheckboxListTile(
                                          title: Text(e["desc"].toString()),
                                          value: selectedTerminals?.contains(id),
                                          onChanged: (bool? selected) {
                                            if (selected == true) {
                                              selectedTerminals?.remove(-1);
                                              selectedTerminals?.add(id);
                                            } else {
                                              selectedTerminals?.remove(id);
                                              if (selectedTerminals?.isEmpty ?? true) {
                                                selectedTerminals?.add(-1);
                                              }
                                            }
                                            terminalOnChanged?.call(selectedTerminals);
                                          },
                                        );
                                      }),
                                    ],
                                  ),
                                ),
                                if (employeeList.isNotEmpty)
                                  Obx(
                                    () => MenuDropdown<dynamic>(
                                      title: "Employee",
                                      value: selectedEmployee?.value,
                                      onChanged: employeeOnChanged,
                                      items: <DropdownMenuItem<int?>>[
                                        const DropdownMenuItem<int?>(
                                          child: Text("All Employees"),
                                        ),
                                        ...List<DropdownMenuItem<int>>.generate(
                                          employeeList.length,
                                          (int index) => DropdownMenuItem<int>(
                                            value: employeeList[index].id,
                                            child: Text(employeeList[index].employee_full_name ?? "No Name"),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (choiceChildren != null && choiceOnChanged != null && choiceValue != null)
                                  Row(
                                    children: <Widget>[
                                      const Expanded(child: MenuLabel(text: "Order")),
                                      Obx(
                                        () => CupertinoSegmentedControl<String>(
                                          children: choiceChildren!,
                                          onValueChanged: choiceOnChanged!,
                                          borderColor: R2Colors.primary500,
                                          groupValue: choiceValue!.value,
                                          selectedColor: R2Colors.primary500,
                                          unselectedColor: R2Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                if (dateRangeController != null)
                                  SfDateRangePicker(
                                    backgroundColor: R2Colors.white,
                                    headerStyle: const DateRangePickerHeaderStyle(
                                      backgroundColor: R2Colors.white,
                                    ),
                                    selectionColor: R2Colors.primary500,
                                    rangeSelectionColor: R2Colors.primary300,
                                    startRangeSelectionColor: R2Colors.primary500,
                                    endRangeSelectionColor: R2Colors.primary500,
                                    todayHighlightColor: R2Colors.primary500,
                                    controller: dateRangeController,
                                    selectionMode: DateRangePickerSelectionMode.range,
                                    monthViewSettings: const DateRangePickerMonthViewSettings(
                                      enableSwipeSelection: false,
                                    ),
                                    onSelectionChanged: rangeOnChanged,
                                  ),
                              ],
                            ),
                            DialogButton(
                              mainAxisAlignment: MainAxisAlignment.center,
                              buttonType: EDialogButtonType.DESTRUCTIVE,
                              buttonText: "Close",
                              onTapped: () => scaffoldKey.currentState!.closeEndDrawer(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (isLoading.value)
                Column(
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        color: R2Colors.neutral100.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
