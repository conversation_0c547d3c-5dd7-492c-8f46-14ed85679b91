import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/Items/children/departments/dialogs/add_department/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class DepartmentController extends GetxController {
  DepartmentController();
  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();
  final Logger _logger = Logger('DepartmenController');

  Future<void> createDepartmentPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Departments",
      _graphqlService,
    );
    if (create) {
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} accessed add departments dialog');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddDepartmentDialog(),
        ),
        isScrollControlled: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewDepartmentPermission(Department department) async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Departments",
      _graphqlService,
    );
    if (view) {
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} viewing ${department.title}');
      await Get.toNamed(
        AppRoutes.ITEMS_DEPARTMENTS_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "department": department,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
