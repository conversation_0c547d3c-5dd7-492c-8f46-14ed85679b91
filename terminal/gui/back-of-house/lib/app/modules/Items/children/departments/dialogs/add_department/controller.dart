// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddDepartmentDialogController');

class RxHours {
  RxHours({required this.open, required this.close});

  RxInt open;
  RxInt close;
}

final List<String> weekDays = [
  'mon',
  'tue',
  'wed',
  'thu',
  'fri',
  'sat',
  'sun',
];

class AddDepartmentDialogController extends GetxController {
  AddDepartmentDialogController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> addDepartmentFormKey = GlobalKey<FormState>();

  final Rx<Department> newDepartment = Department.empty().obs;

  int departmentCount = 0;
  TextEditingController titleController = TextEditingController();
  TextEditingController friendlyTitleController = TextEditingController();
  TextEditingController majorGroup = TextEditingController();

  Rx<SalesTaxDocument> salesTaxDocument = SalesTaxDocument.empty().obs;

  Rxn<String> selectedMajorGroup = Rxn<String>();
  RxString newMajor = "".obs;

  Rxn<String> majorDepartment = Rxn<String>();
  RxList<String> departmentMajorList = <String>[].obs;

  RxBool printInRed = false.obs;

  RxInt deptDevices = 0.obs;

  RxBool isLoading = true.obs;
  RxBool slideSwitch = true.obs;
  RxBool showOnline = false.obs;

  RxBool timeRestrict = false.obs;
  RxMap<String, RxHours> dayHours = <String, RxHours>{}.obs;

  // Manager approval required for voiding
  RxBool needsVoidPermission = false.obs;

  ScrollController taxScrollController = ScrollController();

  RxList<TaxObject> taxList = <TaxObject>[].obs;

  List<String> binaryList = <String>[];

  // for (var i = 0; i < 8; i++) {
  //   // i would be the index of the bit
  //   // x would be the true or false
  //   int x = 0;
  //   newDepartment.value.document.taxFlags |= x << i;
  // }

  @override
  Future<void> onInit() async {
    await getSalesTax();
    await getMajors();

    // Initialize time restriction hours for each day of the week
    for (final String day in weekDays) {
      final RxHours hoursForDay = RxHours(open: (-1).obs, close: (-1).obs);

      dayHours[day] = hoursForDay;
    }

    isLoading.value = false;
    super.onInit();
  }

  Future<void> getDepartmentCount() async {
    try {
      final QueryResult<Object> departmentCountResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_DEPARTMENT_COUNT {
              department_aggregate {
                aggregate {
                  count
                }
              }
            }
            ''',
          ),
        ),
      );

      if (departmentCountResult.hasException) {
        return _notificationService.error(departmentCountResult.exception!.graphqlErrors.toString());
      }

      departmentCount = departmentCountResult.data!['department_aggregate']['aggregate']['count'] as int;
      departmentCount = departmentCount + 1;
    } catch (err) {
      _logger.severe('Get Department Count error');
    }
  }

  Future<void> getSalesTax() async {
    try {
      final QueryResult<Object> salesTaxResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_SALES_TAX(\$_eq: String) {
                json_record(where: {record_key: {_eq: \$_eq}}) {
                  record_key
                  document
                  updated_at
                }
              }
            ''',
          ),
          variables: const <String, dynamic>{"_eq": "salesTax"},
        ),
      );

      if (salesTaxResult.hasException) {
        return _notificationService.error(salesTaxResult.exception!.graphqlErrors.toString());
      }

      salesTaxDocument.value = SalesTaxDocument.fromJson(
        salesTaxResult.data!['json_record'][0]['document'] as Map<String, dynamic>,
      );

      for (int i = 0; i < salesTaxDocument.value.taxes.length; i++) {
        taxList.add(
          TaxObject(
            applyTax: 0.obs,
            taxAmount: salesTaxDocument.value.taxes[i].taxPercent,
            title: salesTaxDocument.value.taxes[i].desc,
          ),
        );
      }
    } catch (err, stack) {
      _logger.severe(
        "Error getting price schedules.",
        err,
        stack,
      );
    }
  }

  Future<void> getMajors() async {
    try {
      final QueryResult<Object> majorGroupResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_DEPARTMENTS {
              department {  
                updated_by
                updated_at
                title
                document
                department_order
                department
                created_by
                created_at
              }
            }
            ''',
          ),
        ),
      );

      if (majorGroupResult.hasException) {
        return _notificationService.error(majorGroupResult.exception!.graphqlErrors.toString());
      }

      final RxList<Department> departmentList = (majorGroupResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic report) => Department.fromJson(report as Map<String, dynamic>),
          )
          .toList()
          .obs;

      for (int i = 0; i < departmentList.length; i++) {
        if (departmentList[i].document.majorGroup != null && departmentList[i].document.majorGroup != "") {
          departmentMajorList.addIf(
            !departmentMajorList.contains(departmentList[i].document.majorGroup),
            departmentList[i].document.majorGroup!,
          );
        }
      }
    } catch (err, stack) {
      _notificationService.error("Error loading Major Groups");
      _logger.severe(
        "Error getting price schedules.",
        err,
        stack,
      );
    }
  }

  Future<void> addDepartment() async {
    try {
      if (!addDepartmentFormKey.currentState!.validate()) throw "Invalid form";
      await getDepartmentCount();

      final RxInt binaryAsDecimal = 0.obs;

      if (taxList.isNotEmpty) {
        for (int i = 0; i < taxList.length; i++) {
          binaryList.add(taxList[i].applyTax.value.toString());
        }
        binaryAsDecimal.value = int.parse(binaryList.reversed.join(), radix: 2);
      }

      newDepartment.value.department = "";
      newDepartment.value.document.taxFlags = binaryAsDecimal.value;
      newDepartment.value.title = titleController.text;
      newDepartment.value.document.friendlyTitle = friendlyTitleController.text;
      newDepartment.value.created_by = CURRENT_EMPLOYEE.value.employee;
      newDepartment.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      newDepartment.value.document.isTaxable = binaryAsDecimal.value != 0;
      newDepartment.value.document.order = departmentCount;
      newDepartment.value.document.majorGroup = majorDepartment.value;
      newDepartment.value.document.printInRed = printInRed.value;
      newDepartment.value.document.prep = deptDevices.value;
      newDepartment.value.document.needsVoidPermission = needsVoidPermission.value;
      newDepartment.value.document.showOnline = showOnline.value;

      // Save time restriction settings
      newDepartment.value.document.restrictEnabled = false;

      if (timeRestrict.value) {
        final Map<String, ActiveWindow> windows = dayHours.map(
          (String day, RxHours hrs) => MapEntry(
            day,
            ActiveWindow(
              open: hrs.open.value,
              close: hrs.close.value,
            ),
          ),
        );

        newDepartment.value.document.restrictEnabled = true;
        newDepartment.value.document.timeWindows = windows;
      }

      final Map<String, dynamic> santizedJobCode = Helpers.sanitizeEntity(
        newDepartment.value.toJson(),
        <String>[
          'department',
          'created_at',
          'updated_at',
        ],
      );

      final QueryResult<Object> addDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation ADD_DEPARTMENT(\$department: department_insert_input!) {
                insert_department_one(object: \$department) {
                  department
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "department": santizedJobCode,
          },
        ),
      );

      if (addDepartmentResult.hasException) {
        return _notificationService.error(addDepartmentResult.exception.toString());
      }

      final List<String> activeTaxes = taxList
          .where((TaxObject tax) => tax.applyTax.value == 1) // Filter only where applyTax is 1
          .map((TaxObject tax) => tax.title) // Map to title
          .whereType<String>() // Ensure title is not null
          .toList();
      _logger.info(
        "${CURRENT_EMPLOYEE.value.employee_full_name} created department - ${newDepartment.value.title}. Taxes set as ${newDepartment.value.document.taxFlags} $activeTaxes",
      );
      _notificationService.success('Add department success');
      Get.back();
    } catch (err, stack) {
      _logger.severe(
        "Error adding department",
        err,
        stack,
      );
    }
  }

  Future<void> updateMajors(String major, String? editedMajor) async {
    try {
      final QueryResult<Object> deleteDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
                mutation EDIT_MAJOR_GORUPS(\$_contains: jsonb, \$document: jsonb) {
                  update_department(where: {document: {_contains: \$_contains}}, _append: {document: \$document}) {
                    affected_rows
                  }
                }

              ''',
          ),
          variables: <String, dynamic>{
            "_contains": <String, dynamic>{"majorGroup": major},
            "document": <String, dynamic>{"majorGroup": editedMajor},
          },
        ),
      );
      if (deleteDepartmentResult.hasException) {
        return _notificationService.error(deleteDepartmentResult.exception!.graphqlErrors.toString());
      }
      _notificationService.success("Update major Success");
    } catch (err, stack) {
      _logger.severe(
        "Error getting price schedules.",
        err,
        stack,
      );
    }
  }

  void flipDevice(PrepDevice device) {
    deptDevices.value = deptDevices.value ^ (1 << device.idx);
  }
}

class TaxObject {
  TaxObject({
    this.title,
    required this.applyTax,
    this.taxAmount,
  });
  TaxObject.empty();
  String? title;
  RxInt applyTax = 0.obs;
  int? taxAmount;
}
