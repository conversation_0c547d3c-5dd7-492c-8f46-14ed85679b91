import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/drag_handle.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_timepicker.dart';
import 'package:backoffice/app/global_widgets/menu/components/scroll_list.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/global_widgets/prep_device_select/widget.dart';
import 'package:backoffice/app/modules/Items/children/departments/dialogs/add_department/controller.dart';
import 'package:backoffice/app/modules/Items/children/departments/dialogs/view_department/children/major_modal/major_modal.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddDepartmentDialog extends GetView<AddDepartmentDialogController> {
  final Map<String, String> weekdays = <String, String>{
    "mon": 'Monday',
    "tue": 'Tuesday',
    "wed": 'Wednesday',
    "thu": 'Thursday',
    "fri": 'Friday',
    "sat": 'Saturday',
    "sun": 'Sunday',
  };

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddDepartmentDialogController>(
      init: AddDepartmentDialogController(),
      builder: (AddDepartmentDialogController controller) {
        return Column(
          children: <Widget>[
            Column(
              children: <Widget>[
                const DragHandle(),
                Header(
                  title: "Add Department",
                  leftButton: DialogButton(
                    buttonType: EDialogButtonType.CANCEL,
                    onTapped: () {
                      Get.back();
                    },
                  ),
                  transparentBackground: false,
                ),
              ],
            ),
            Expanded(
              child: ClipRect(
                child: FormWrapper(
                  formKey: controller.addDepartmentFormKey,
                  children: <Widget>[
                    MenuGroup(
                      title: "Department Info",
                      children: <Widget>[
                        MenuTextField(
                          label: "Title",
                          controller: controller.titleController,
                          maxLength: 30,
                          validator: (String? value) {
                            if (value == null || value.isEmpty) {
                              return '';
                            }
                            return null;
                          },
                        ),
                        MenuTextField(
                          label: "Friendly Title",
                          controller: controller.friendlyTitleController,
                          maxLength: 30,
                          validator: (String? value) {
                            if (value == null || value.isEmpty) {
                              return '';
                            }
                            return null;
                          },
                        ),
                        Row(
                          children: <Widget>[
                            const Expanded(
                              child: MenuLabel(text: "Major Group"),
                            ),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: MaterialButton(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  elevation: 0,
                                  focusElevation: 0,
                                  hoverElevation: 0,
                                  highlightElevation: 0,
                                  hoverColor: R2Colors.neutral200,
                                  color: R2Colors.neutral200,
                                  onPressed: () {
                                    Get.dialog(
                                      MajorModal(
                                        slideSwitch: controller.slideSwitch,
                                        departmentMajorList: controller.departmentMajorList,
                                        majorDepartment: controller.majorDepartment,
                                        updateMajorTitle: (
                                          String oldTitle,
                                          String? newTitle,
                                        ) async {
                                          await controller.updateMajors(
                                            oldTitle,
                                            newTitle,
                                          );
                                        },
                                        deleteMajor: (
                                          String deleteMajor,
                                          String? nullMajor,
                                        ) async {
                                          await controller.updateMajors(
                                            deleteMajor,
                                            nullMajor,
                                          );
                                          await controller.getMajors();
                                        },
                                      ),
                                    );
                                  },
                                  child: Obx(
                                    () => Text(
                                      controller.majorDepartment.value ?? 'Unassigned',
                                      style: TextStyle(
                                        color: controller.majorDepartment.value == null ? R2Colors.red500 : null,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Show Online",
                            value: controller.showOnline.value,
                            onChanged: (bool? value) {
                              controller.showOnline.value = value ?? false;
                            },
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Print in Red",
                            value: controller.printInRed.value,
                            onChanged: (bool? value) {
                              controller.printInRed.value = value ?? false;
                            },
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Applicable Taxes",
                      children: <Widget>[
                        Obx(
                          () => MenuScrollWindow(
                            scrollController: controller.taxScrollController,
                            listView: ListView.separated(
                              controller: controller.taxScrollController,
                              separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              itemCount: controller.taxList.length,
                              itemBuilder: (BuildContext context, int index) {
                                return Obx(
                                  () => ListTile(
                                    selectedTileColor: R2Colors.primary100,
                                    trailing: controller.taxList[index].applyTax.value == 1
                                        ? const Icon(
                                            Icons.check,
                                            color: R2Colors.primary500,
                                          )
                                        : null,
                                    leading: Obx(
                                      () => Text(
                                        "${controller.taxList[index].title} :  ${controller.taxList[index].taxAmount! / 10000}%",
                                        style: TextStyle(
                                          color: controller.taxList[index].taxAmount != 0 ? null : R2Colors.neutral400,
                                        ),
                                      ),
                                    ),
                                    onTap: controller.taxList[index].taxAmount != 0
                                        ? () {
                                            if (controller.taxList[index].applyTax.value == 0) {
                                              controller.taxList[index].applyTax.value = 1;
                                            } else {
                                              controller.taxList[index].applyTax.value = 0;
                                            }
                                          }
                                        : null,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Prep Devices",
                      children: <Widget>[
                        PrepDeviceSelect(
                          selectedDevices: controller.deptDevices,
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Manager Approval Required for Voiding",
                      children: <Widget>[
                        Obx(
                          () => MenuCheckbox(
                            text: 'Active',
                            value: controller.needsVoidPermission.value,
                            onChanged: (bool? v) {
                              controller.needsVoidPermission.value = v ?? false;
                            },
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Department Operating Hours",
                      children: <Widget>[
                        Obx(
                          () => MenuCheckbox(
                            text: 'Active',
                            value: controller.timeRestrict.value,
                            onChanged: (bool? v) {
                              controller.timeRestrict.value = v ?? false;
                            },
                          ),
                        ),
                        Obx(
                          () => controller.timeRestrict.value
                              ? Column(
                                  children: controller.dayHours.entries.map((MapEntry<String, RxHours> entry) {
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: <Widget>[
                                        // Always show the day's label
                                        Row(
                                          children: <Widget>[
                                            Expanded(
                                              child: MenuLabel(
                                                text: weekdays[entry.key]!,
                                              ),
                                            ),
                                            IconButton(
                                              onPressed: () {
                                                /// Set default hours for the department to 9am and 9pm
                                                controller.dayHours[entry.key] = RxHours(open: RxInt(32400000), close: RxInt(75600000));
                                              },
                                              icon: const Icon(
                                                Icons.add,
                                                color: R2Colors.primary500,
                                              ),
                                            ),
                                          ],
                                        ),
                                        // Only show the time pickers row if hours are set (not -1)
                                        if (entry.value.open.value != -1 && entry.value.close.value != -1)
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                            children: <Widget>[
                                              Expanded(
                                                child: MenuTimePicker(
                                                  useDeptTimeString: true,
                                                  milliseconds: entry.value.open,
                                                  onChanged: (int? newMs) {
                                                    entry.value.open.value = newMs!;
                                                  },
                                                ),
                                              ),
                                              const Text("to"),
                                              Expanded(
                                                child: MenuTimePicker(
                                                  useDeptTimeString: true,
                                                  milliseconds: entry.value.close,
                                                  onChanged: (int? newMs) {
                                                    entry.value.close.value = newMs!;
                                                  },
                                                ),
                                              ),
                                              IconButton(
                                                onPressed: () {
                                                  // Set the values to -1 to indicate hours are not set
                                                  controller.dayHours[entry.key] = RxHours(open: RxInt(-1), close: RxInt(-1));
                                                },
                                                icon: const Icon(
                                                  Icons.close,
                                                  color: R2Colors.red500,
                                                ),
                                              ),
                                            ],
                                          )
                                        else
                                          Container(),
                                      ],
                                    );
                                  }).toList(),
                                )
                              : Container(),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 25),
                      child: Listener(
                        onPointerDown: (_) => controller.addDepartment(),
                        child: MaterialButton(
                          height: 70,
                          minWidth: 400,
                          color: R2Colors.primary500,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          onPressed: () {},
                          child: const Text(
                            "Add Department",
                            style: TextStyle(
                              color: R2Colors.white,
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
