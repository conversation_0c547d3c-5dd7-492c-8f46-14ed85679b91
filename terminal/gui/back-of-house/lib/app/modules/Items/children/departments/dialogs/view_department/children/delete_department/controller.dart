import 'package:backoffice/app/routes/app_pages.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class DeleteDepartmentDialogController extends GetxController {
  DeleteDepartmentDialogController(this.currentDepartment);

  final NotificationService _notificationService = Get.find();

  final GraphqlService _graphqlService = Get.find();
  final Logger _logger = Logger('Delete Department Controller');

  Department currentDepartment;

  RxList<Department> availableDepartments = <Department>[].obs;

  Rx<String> assignedDepartmentId = "".obs;

  Rx<bool> departmentSelected = true.obs;

  @override
  Future<void> onInit() async {
    await loadDepartments();
    super.onInit();
  }

  Future<void> loadDepartments() async {
    try {
      final QueryResult departmentResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(
            '''
             query MyQuery {
                department {
                  created_at
                  created_by
                  department
                  document
                  title
                  updated_at
                  updated_by
                }
              }

            ''',
          ),
        ),
      );

      if (departmentResult.hasException) return _notificationService.error(departmentResult.exception!.graphqlErrors.toString());

      availableDepartments.value =
          (departmentResult.data!['department'] as List).map((department) => Department.fromJson(department as Map<String, dynamic>)).toList();
      availableDepartments.removeWhere((Department department) => department.department == currentDepartment.department);
    } catch (err, stack) {
      _logger.severe('Get Department Count error', err, stack);
    }
  }

  Future<void> reassignDepartmentItems() async {
    try {
      final QueryResult reassignDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
          mutation UPDATE_ITEM_DEPARTMENT(\$currentDepartment: uuid, \$newDepartment: uuid) {
              update_item(where: {department: {_eq: \$currentDepartment}}, _set: {department: \$newDepartment}) {
                affected_rows
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "currentDepartment": currentDepartment.department,
            "newDepartment": assignedDepartmentId.value,
          },
        ),
      );
      if (reassignDepartmentResult.hasException) return _notificationService.error(reassignDepartmentResult.exception.toString());
      _notificationService.success("Successfully updated Department");
      await deleteDepartment();
    } catch (err, stack) {
      _logger.severe('Get Department Count error', err, stack);
    }
  }

  Future<void> deleteDepartment() async {
    try {
      final QueryResult deleteDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
          mutation DELETE_DEPARTMENT(\$department: uuid!) {
              delete_department_by_pk(department: \$department) {
                department
                document
                title
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "department": currentDepartment.department,
          },
        ),
      );
      if (deleteDepartmentResult.hasException) return _notificationService.error(deleteDepartmentResult.exception!.graphqlErrors.toString());
      _notificationService.success("Successfully updated Department");
      Get.back();
      await Get.offNamed(AppRoutes.ITEMS_DEPARTMENTS, id: AppRoutes.id);
    } catch (err, stack) {
      _logger.severe('Get Department Count error', err, stack);
    }
  }

  Future<void> deleteCheck() async {
    if (assignedDepartmentId.value == "") {
      departmentSelected.value = true;
    } else {
      departmentSelected.value = false;
    }
  }
}
