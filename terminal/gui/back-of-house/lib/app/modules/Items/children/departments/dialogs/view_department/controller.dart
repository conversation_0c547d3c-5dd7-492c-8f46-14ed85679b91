// ignore_for_file: non_constant_identifier_names

import 'package:backoffice/app/modules/Items/children/departments/dialogs/view_department/children/delete_department/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

class RxHours {
  RxHours({required this.open, required this.close});

  RxInt open;
  RxInt close;
}

final List<String> weekDays = <String>[
  'mon',
  'tue',
  'wed',
  'thu',
  'fri',
  'sat',
  'sun',
];

class ViewDepartmentDialogController extends GetxController {
  ViewDepartmentDialogController(
    this.department,
  );

  final Logger _logger = Logger('ViewDepartmentController');

  static const List<String> weekDays = <String>['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

  final String GET_SALES_TAX_RECORD = '''
      query GET_SALES_TAX_RECORD(\$_eq: String) {
        json_record(where: {record_key: {_eq: \$_eq}}) {
          updated_at
          record_key
          document
        }
      }
      ''';

  final Department department;
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final GlobalKey<FormState> viewDepartmentFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> majorFormKey = GlobalKey<FormState>();
  final Rx<Department> currentDepartment = Department.empty().obs;
  TextEditingController titleController = TextEditingController();
  TextEditingController friendlyTitleController = TextEditingController();
  SalesTaxDocument salesTaxDocument = SalesTaxDocument.empty();

  RxBool canEdit = false.obs;
  RxBool canDelete = false.obs;
  RxBool canUpdateDepartment = false.obs;
  RxBool slideSwitch = true.obs;
  Rxn<String> majorDepartment = Rxn<String>();
  RxList<String> departmentMajorList = <String>[].obs;
  RxString newMajor = "".obs;
  Rxn<String>? tempMajor = Rxn<String>();
  RxBool printInRed = false.obs;
  RxBool showOnline = false.obs;
  RxInt getTaxCount = 0.obs;
  RxInt deptDevices = 0.obs;
  RxList<TaxObject> taxList = <TaxObject>[].obs;
  List<String> binaryList = <String>[];

  ScrollController scrollController = ScrollController();

  int preserveTaxFlags = 0;

  RxBool needsVoidPermission = false.obs;

  RxBool timeRestrict = false.obs;

  //RxMap<String, RxList<RxHours>> openHours = <String, RxList<RxHours>>{}.obs;
  RxMap<String, RxHours> dayHours = <String, RxHours>{}.obs;
  Rx<EcomSettingJsonRecordDocument> depRestrict = EcomSettingJsonRecordDocument.empty().obs;

  @override
  Future<void> onInit() async {
    currentDepartment.value = department;
    titleController.text = currentDepartment.value.title;
    friendlyTitleController.text = currentDepartment.value.document.friendlyTitle ?? "";
    majorDepartment.value = currentDepartment.value.document.majorGroup;
    printInRed.value = currentDepartment.value.document.printInRed ?? false;
    showOnline.value = currentDepartment.value.document.showOnline;
    deptDevices.value = currentDepartment.value.document.prep;
    preserveTaxFlags = currentDepartment.value.document.taxFlags;
    needsVoidPermission.value = currentDepartment.value.document.needsVoidPermission;

    // Create a blank entry for every weekday so the UI never sees nulls.
    for (final String day in weekDays) {
      dayHours[day] = RxHours(open: (-1).obs, close: (-1).obs);
    }

    // Overlay the real values from the DB
    currentDepartment.value.document.timeWindows.forEach((String day, ActiveWindow tw) {
      dayHours[day]!.open.value = tw.open;
      dayHours[day]!.close.value = tw.close;
    });

    timeRestrict.value = currentDepartment.value.document.restrictEnabled;

    await editPermission();
    await deletePermission();
    await getMajors();
    await getSalesTax();
    super.onInit();
  }

  Future<void> updateDepartment() async {
    try {
      if (!viewDepartmentFormKey.currentState!.validate()) throw "Invalid form";
      final RxInt binaryAsDecimal = 0.obs;

      if (taxList.isNotEmpty) {
        for (int i = 0; i < taxList.length; i++) {
          binaryList.add(taxList[i].applyTax.value.toString());
        }
        binaryAsDecimal.value = int.parse(binaryList.reversed.join(), radix: 2);
      }

      currentDepartment.value.document.taxFlags = binaryAsDecimal.value;
      currentDepartment.value.title = titleController.text;
      currentDepartment.value.document.friendlyTitle = friendlyTitleController.text;
      currentDepartment.value.document.isTaxable = binaryAsDecimal.value != 0;
      currentDepartment.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentDepartment.value.document.order = department.document.order;
      currentDepartment.value.document.colorHash = department.document.colorHash;
      currentDepartment.value.document.majorGroup = majorDepartment.value;
      currentDepartment.value.document.printInRed = printInRed.value;
      currentDepartment.value.document.showOnline = showOnline.value;
      currentDepartment.value.document.prep = deptDevices.value;
      currentDepartment.value.updated_at = DateTime.now();
      currentDepartment.value.document.needsVoidPermission = needsVoidPermission.value;

      // Department time restriction:
      currentDepartment.value.document.restrictEnabled = false;
      //currentDepartment.value.document.timeWindows = null;

      if (timeRestrict.value) {
        final Map<String, ActiveWindow> windows = dayHours.map(
          (String day, RxHours hrs) => MapEntry(
            day,
            ActiveWindow(
              open: hrs.open.value,
              close: hrs.close.value,
            ),
          ),
        );

        currentDepartment.value.document.restrictEnabled = true;
        currentDepartment.value.document.timeWindows = windows;
      }

      final QueryResult<Object> updateDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString('''
           mutation UPDATED_DEPARTMENT(\$department: uuid!, \$document: jsonb, \$title: String, \$updated_by: uuid, \$updated_at: timestamptz) {
              update_department_by_pk(pk_columns: {department: \$department}, _set: {document: \$document, title: \$title, updated_by: \$updated_by, updated_at: \$updated_at}) {
                department
                document
                title
                updated_by
              }
            }
          '''),
          variables: <String, dynamic>{
            "department": currentDepartment.value.department,
            "title": currentDepartment.value.title,
            "document": currentDepartment.value.document,
            "updated_by": CURRENT_EMPLOYEE.value.employee,
            "updated_at": currentDepartment.value.updated_at.toString(),
          },
        ),
      );

      if (updateDepartmentResult.hasException) {
        return _notificationService.error(
          updateDepartmentResult.exception.toString(),
        );
      }

      final List<String> activeTaxes = taxList
          .where(
            (TaxObject tax) => tax.applyTax.value == 1,
          ) // Filter only where applyTax is 1
          .map((TaxObject tax) => tax.title) // Map to title
          .whereType<String>() // Ensure title is not null
          .toList();
      if (preserveTaxFlags != currentDepartment.value.document.taxFlags) {
        _logger.info(
          '${CURRENT_EMPLOYEE.value.employee_full_name} changed taxes from $preserveTaxFlags to ${currentDepartment.value.document.taxFlags} on department - ${currentDepartment.value.title}. Active taxes $activeTaxes',
        );
      }
      _notificationService.success("Successfully updated Department");
      _logger.info(
        '${CURRENT_EMPLOYEE.value.employee_full_name} updated department - ${currentDepartment.value.title}',
      );
      await Get.offNamed(AppRoutes.ITEMS_DEPARTMENTS, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Department Update failed!");
      _logger.info('Error deleting department', err, stack);
    }
  }

  Future<void> getMajors() async {
    try {
      final QueryResult<Object> majorGroupResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString('''
           query GET_DEPARTMENTS {
              department {
                updated_by
                updated_at
                title
                document
                department_order
                department
                created_by
                created_at
              }
            }
            '''),
        ),
      );

      if (majorGroupResult.hasException) {
        return _notificationService.error(
          majorGroupResult.exception!.graphqlErrors.toString(),
        );
      }

      final RxList<Department> departmentList = (majorGroupResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic report) => Department.fromJson(report as Map<String, dynamic>),
          )
          .toList()
          .obs;

      for (int i = 0; i < departmentList.length; i++) {
        if (departmentList[i].document.majorGroup != null && departmentList[i].document.majorGroup != "") {
          departmentMajorList.addIf(
            !departmentMajorList.contains(
              departmentList[i].document.majorGroup,
            ),
            departmentList[i].document.majorGroup!,
          );
        }
      }
    } catch (err, stack) {
      _notificationService.error("Error loading Major Groups");
      _logger.info('Error loading Major Groups', err, stack);
    }
  }

  Future<void> deleteDepartmentCheck() async {
    final QueryResult<Object> itemCheckResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString('''
           query ITEM_CHECK(\$department: uuid) {
              item(where: {department: {_eq: \$department}}) {
                long_desc
              }
            }
            '''),
        variables: <String, dynamic>{
          "department": currentDepartment.value.department,
        },
      ),
    );

    if (itemCheckResult.hasException) {
      return _notificationService.error(
        itemCheckResult.exception!.graphqlErrors.toString(),
      );
    }
    // ignore: always_specify_types
    if ((itemCheckResult.data!['item']! as List).isNotEmpty) {
      await Get.defaultDialog<Department>(
        title: "Warning!",
        content: DeleteDepartmentDialog(department: currentDepartment.value),
        barrierDismissible: false,
      );
    } else {
      try {
        final QueryResult<Object> deleteDepartmentResult = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString('''
          mutation DELETE_DEPARTMENT(\$department: uuid!) {
              delete_department_by_pk(department: \$department) {
                department
                document
                title
              }
            }
          '''),
            variables: <String, dynamic>{
              "department": currentDepartment.value.department,
            },
          ),
        );
        if (deleteDepartmentResult.hasException) {
          return _notificationService.error(
            deleteDepartmentResult.exception!.graphqlErrors.toString(),
          );
        }
        _logger.info(
          '${CURRENT_EMPLOYEE.value.employee_full_name} deleted department - ${currentDepartment.value.title}',
        );
        _notificationService.success("Department Deleted!");
        await Get.offNamed(AppRoutes.ITEMS_DEPARTMENTS, id: AppRoutes.id);
      } catch (err, stack) {
        _notificationService.error("Error Deleting Department");
        _logger.info('Error loading Major Groups', err, stack);
      }
    }
  }

  Future<void> updateMajors(String major, String? editedMajor) async {
    try {
      final QueryResult<Object> deleteDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString('''
                mutation EDIT_MAJOR_GORUPS(\$_contains: jsonb, \$document: jsonb) {
                  update_department(where: {document: {_contains: \$_contains}}, _append: {document: \$document}) {
                    affected_rows
                  }
                }

              '''),
          variables: <String, dynamic>{
            "_contains": <String, dynamic>{"majorGroup": major},
            "document": <String, dynamic>{"majorGroup": editedMajor},
          },
        ),
      );
      if (deleteDepartmentResult.hasException) {
        return _notificationService.error(
          deleteDepartmentResult.exception!.graphqlErrors.toString(),
        );
      }

      _logger.info(
        '${CURRENT_EMPLOYEE.value.employee_full_name} updated major from $major to $editedMajor',
      );
      _notificationService.success("Update major Success");
    } catch (err, stack) {
      _logger.shout('Error updating major', err, stack);
      _notificationService.error("Error updating Major");
    }
  }

  Future<void> getSalesTax() async {
    try {
      final QueryResult<Object> salesTaxResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString('''
            query GET_SALES_TAX(\$_eq: String) {
                json_record(where: {record_key: {_eq: \$_eq}}) {
                  record_key
                  document
                  updated_at
                }
              }
            '''),
          variables: const <String, dynamic>{"_eq": "salesTax"},
        ),
      );

      if (salesTaxResult.hasException) {
        return _notificationService.error(
          salesTaxResult.exception!.graphqlErrors.toString(),
        );
      }

      salesTaxDocument = SalesTaxDocument.fromJson(
        // ignore: avoid_dynamic_calls
        salesTaxResult.data!['json_record'][0]['document'] as Map<String, dynamic>,
      );

      for (int i = 0; i < salesTaxDocument.taxes.length; i++) {
        final Tax currentTax = salesTaxDocument.taxes[i];
        final int checkDigit = 1 << i;
        taxList.add(
          TaxObject(
            title: currentTax.desc,
            applyTax: currentDepartment.value.document.taxFlags & checkDigit == checkDigit ? 1.obs : 0.obs,
            taxAmount: currentTax.taxPercent,
          ),
        );
      }
    } catch (err, stack) {
      _logger.shout('Error getting sales taxes', err, stack);
      _notificationService.error("Error getting sales taxes");
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Departments",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateDepartment.value = !edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Departments",
      _graphqlService,
    );
    canDelete.value = !delete;
  }
}

class TaxObject {
  TaxObject({this.title, required this.applyTax, this.taxAmount});
  TaxObject.empty();
  String? title;
  RxInt applyTax = 0.obs;
  int? taxAmount;
}
