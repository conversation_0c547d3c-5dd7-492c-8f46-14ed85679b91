import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_timepicker.dart';
import 'package:backoffice/app/global_widgets/menu/components/scroll_list.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/global_widgets/prep_device_select/widget.dart';
import 'package:backoffice/app/modules/Items/children/departments/dialogs/view_department/children/major_modal/major_modal.dart';
import 'package:backoffice/app/modules/Items/children/departments/dialogs/view_department/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ViewDepartmentDialog extends GetView<ViewDepartmentDialogController> {
  final Map<String, String> weekdays = <String, String>{
    "mon": 'Monday',
    "tue": 'Tuesday',
    "wed": 'Wednesday',
    "thu": 'Thursday',
    "fri": 'Friday',
    "sat": 'Saturday',
    "sun": 'Sunday',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(400),
        child: Header(
          transparentBackground: false,
          title: "Department View",
          leftButton: DialogButton(
            buttonType: EDialogButtonType.BACK,
            buttonText: "Departments",
            onTapped: () {
              Get.back(id: AppRoutes.id);
            },
          ),
          rightButton: Obx(
            () => DialogButton(
              buttonType: EDialogButtonType.ADD,
              disabled: controller.canUpdateDepartment.value,
              onTapped: () async => controller.updateDepartment(),
              buttonText: "Update Department",
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            FormWrapper(
              formKey: controller.viewDepartmentFormKey,
              children: <Widget>[
                MenuGroup(
                  title: "Department",
                  children: <Widget>[
                    Obx(
                      () => MenuTextField(
                        label: "Title",
                        controller: controller.titleController,
                        enabled: controller.canEdit.value,
                        maxLength: 30,
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                      ),
                    ),
                    Obx(
                      () => MenuTextField(
                        label: "Friendly Title",
                        controller: controller.friendlyTitleController,
                        enabled: controller.canEdit.value,
                        maxLength: 30,
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        const Expanded(child: MenuLabel(text: "Major Group")),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 10),
                            child: MaterialButton(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                              elevation: 0,
                              focusElevation: 0,
                              hoverElevation: 0,
                              highlightElevation: 0,
                              hoverColor: R2Colors.neutral200,
                              color: R2Colors.neutral200,
                              onPressed: () async {
                                await Get.dialog(
                                  MajorModal(
                                    slideSwitch: controller.slideSwitch,
                                    departmentMajorList: controller.departmentMajorList,
                                    majorDepartment: controller.majorDepartment,
                                    updateMajorTitle: (
                                      String oldMajor,
                                      String? updatedMajor,
                                    ) async {
                                      await controller.updateMajors(
                                        oldMajor,
                                        updatedMajor,
                                      );
                                    },
                                    deleteMajor: (
                                      String deleteMajor,
                                      String? nullTitle,
                                    ) async {
                                      await controller.updateMajors(
                                        deleteMajor,
                                        nullTitle,
                                      );
                                      await controller.getMajors();
                                    },
                                  ),
                                  barrierDismissible: false,
                                ).then((dynamic value) async {
                                  Future<dynamic>.delayed(
                                    const Duration(seconds: 1),
                                    () => controller.slideSwitch.value = true,
                                  );
                                });
                              },
                              child: Obx(
                                () => Text(
                                  controller.majorDepartment.value ?? 'Unassigned',
                                  style: TextStyle(
                                    color: controller.majorDepartment.value == null ? R2Colors.red500 : null,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Obx(
                      () => MenuCheckbox(
                        text: "Show Online",
                        value: controller.showOnline.value,
                        onChanged: (bool? value) {
                          controller.showOnline.value = value ?? false;
                        },
                      ),
                    ),
                    Obx(
                      () => MenuCheckbox(
                        text: "Print in Red",
                        value: controller.printInRed.value,
                        onChanged: (bool? value) {
                          controller.printInRed.value = value ?? false;
                        },
                      ),
                    ),
                    PrepDeviceSelect(
                      selectedDevices: controller.deptDevices,
                    ),
                  ],
                ),
                Obx(
                  () => MenuGroup(
                    title: "Applicable Taxes",
                    children: <Widget>[
                      MenuScrollWindow(
                        scrollController: controller.scrollController,
                        listView: ListView.separated(
                          controller: controller.scrollController,
                          separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                          shrinkWrap: true,
                          itemCount: controller.taxList.length,
                          itemBuilder: (BuildContext context, int index) {
                            return Obx(
                              () => ListTile(
                                leading: Text(
                                  "${controller.taxList[index].title}",
                                  style: TextStyle(
                                    color: controller.taxList[index].taxAmount != 0 ? null : R2Colors.neutral400,
                                  ),
                                ),
                                selectedColor: R2Colors.primary500,
                                trailing: controller.taxList[index].applyTax.value == 1
                                    ? const Icon(
                                        Icons.check,
                                        color: R2Colors.primary500,
                                      )
                                    : null,
                                onTap: controller.taxList[index].taxAmount != 0
                                    ? () {
                                        if (controller.taxList[index].applyTax.value == 1) {
                                          controller.taxList[index].applyTax.value = 0;
                                        } else {
                                          controller.taxList[index].applyTax.value = 1;
                                        }
                                      }
                                    : null,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                MenuGroup(
                  title: "Manager Approval Required for Voiding",
                  children: <Widget>[
                    Obx(
                      () => MenuCheckbox(
                        text: 'Active',
                        value: controller.needsVoidPermission.value,
                        onChanged: (bool? v) {
                          controller.needsVoidPermission.value = v ?? false;
                        },
                      ),
                    ),
                  ],
                ),
                MenuGroup(
                  title: "Department Operating Hours",
                  children: <Widget>[
                    Obx(
                      () => MenuCheckbox(
                        text: 'Active',
                        value: controller.timeRestrict.value,
                        onChanged: (bool? v) {
                          controller.timeRestrict.value = v ?? false;
                        },
                      ),
                    ),
                    Obx(
                      () => controller.timeRestrict.value
                          ? Column(
                              children: controller.dayHours.entries.map((MapEntry<String, RxHours> entry) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    // Always show the day's label
                                    Row(
                                      children: <Widget>[
                                        Expanded(
                                          child: MenuLabel(
                                            text: weekdays[entry.key]!,
                                          ),
                                        ),
                                        IconButton(
                                          onPressed: () {
                                            /// Set default hours for the department to 9am and 9pm
                                            controller.dayHours[entry.key] = RxHours(open: RxInt(32400000), close: RxInt(75600000));
                                          },
                                          icon: const Icon(
                                            Icons.add,
                                            color: R2Colors.primary500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    // Only show the time pickers row if hours are set (not -1)
                                    if (entry.value.open.value != -1 && entry.value.close.value != -1)
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                        children: <Widget>[
                                          Expanded(
                                            child: MenuTimePicker(
                                              useDeptTimeString: true,
                                              milliseconds: entry.value.open,
                                              onChanged: (int? newMs) {
                                                entry.value.open.value = newMs!;
                                              },
                                            ),
                                          ),
                                          const Text("to"),
                                          Expanded(
                                            child: MenuTimePicker(
                                              useDeptTimeString: true,
                                              milliseconds: entry.value.close,
                                              onChanged: (int? newMs) {
                                                entry.value.close.value = newMs!;
                                              },
                                            ),
                                          ),
                                          IconButton(
                                            onPressed: () {
                                              // Set the values to -1 to indicate hours are not set
                                              controller.dayHours[entry.key] = RxHours(open: RxInt(-1), close: RxInt(-1));
                                            },
                                            icon: const Icon(
                                              Icons.close,
                                              color: R2Colors.red500,
                                            ),
                                          ),
                                        ],
                                      )
                                    else
                                      Container(),
                                  ],
                                );
                              }).toList(),
                            )
                          : Container(),
                    ),
                  ],
                ),
                Obx(
                  () => DialogButton(
                    mainAxisAlignment: MainAxisAlignment.center,
                    buttonType: EDialogButtonType.DESTRUCTIVE,
                    buttonText: "Delete Department",
                    disabled: controller.canDelete.value,
                    onTapped: () {
                      controller.deleteDepartmentCheck();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
