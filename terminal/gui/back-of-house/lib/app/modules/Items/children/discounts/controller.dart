import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/Items/children/discounts/dialogs/add_discount/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/discount.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class DiscountController extends GetxController {
  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();
  final Logger _logger = Logger('DiscountController');
  Future<void> createDiscountPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Discount",
      _graphqlService,
    );
    if (create) {
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} accessing create discount');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddDiscountDialog(),
        ),
        isScrollControlled: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewDiscountPermission(Discount discount) async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Discount",
      _graphqlService,
    );
    if (create) {
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} viewing discount ${discount.title}');

      await Get.toNamed(
        AppRoutes.ITEMS_DISCOUNTS_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "discount": discount,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
