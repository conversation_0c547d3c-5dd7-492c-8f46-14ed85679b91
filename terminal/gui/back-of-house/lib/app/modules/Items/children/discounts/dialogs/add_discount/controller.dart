import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/discount.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class AddDiscountDialogController extends GetxController {
  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final Logger _logger = Logger('Add Discount Controller');
  // final SyncService _syncService = Get.find();

  final GlobalKey<FormState> addDiscountFormKey = GlobalKey<FormState>();

  final Rx<Discount> newDiscount = Discount.empty().obs;
  RxBool isSwitched = false.obs;
  RxBool timedDiscount = false.obs;
  // 0 == percent, 1 == dollar amount
  RxInt discountType = 0.obs;

  RxString typeTitle = "Percent".obs;
  RxString selectedValue = "Percent".obs;
  String itemOne = "Percent";
  String itemTwo = "Dollar";

  TextEditingController titleController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController startDateController = TextEditingController();
  TextEditingController percentageController = TextEditingController();
  TextEditingController dollarOffController = TextEditingController();
  TextEditingController maxAmountController = TextEditingController();

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  Future<void> addDiscount() async {
    try {
      if (!addDiscountFormKey.currentState!.validate()) throw "Invalid form";

      if (dollarOffController.text == "") {
        dollarOffController.text = 0.toString();
      }
      if (percentageController.text == "") {
        percentageController.text = 0.toString();
      }
      if (startDateController.text == "") {
        startDateController.text = DateTime.now().toString();
      }
      if (endDateController.text == "") {
        endDateController.text = DateTime.now().toString();
      }

      newDiscount.value.created_by = CURRENT_EMPLOYEE.value.employee;
      newDiscount.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      newDiscount.value.document.type = discountType.value;
      newDiscount.value.document.timedDiscount = timedDiscount.value;
      newDiscount.value.title = titleController.text;
      newDiscount.value.document.dollarOff = int.parse(dollarOffController.text.replaceAll(",", "").replaceAll(".", ""));
      newDiscount.value.document.percentOff = int.parse(percentageController.text);
      newDiscount.value.document.maxAmount = int.parse(maxAmountController.text.replaceAll(",", "").replaceAll(".", ""));
      newDiscount.value.start_at = DateTime.parse(startDateController.text).toUtc();
      newDiscount.value.end_at = DateTime.parse(endDateController.text).toUtc();

      final Map<String, dynamic> santizedDiscount = Helpers.sanitizeEntity(
        newDiscount.value.toJson(),
        [
          'discount',
          'created_at',
          'updated_at',
        ],
      );

      final QueryResult<Object> addDiscountResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation ADD_DISCOUNT(\$discount: discount_insert_input!) {
                insert_discount_one(object: \$discount) {
                  discount
                  created_at
                  created_by
                  document
                  end_at
                  start_at
                  title
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: {
            "discount": santizedDiscount,
          },
        ),
      );

      if (addDiscountResult.hasException) return _notificationService.error(addDiscountResult.exception.toString());

      _notificationService.success("Discount added!");
      Get.back();
    } catch (err, stack) {
      _logger.severe('Error Adding Discount', err, stack);
    }
  }

  void Function(bool?)? typeSwitch() => (bool? value) {
        isSwitched.value = !isSwitched.value;
        if (isSwitched.value) {
          discountType.value = 1;
          typeTitle.value = "Dollar";
        } else {
          discountType.value = 0;
          typeTitle.value = "Percent";
        }
        print(discountType.value);
      };

  void Function(bool?)? timedDiscountSwitch() => (bool? value) {
        timedDiscount.value = !timedDiscount.value;
      };

  void Function(String?) choiceSwitch(String value) => (String? value) {
        selectedValue.value = value!;
        selectedValue.value == "Percent" ? discountType.value = 0 : discountType.value = 1;
      };
}
