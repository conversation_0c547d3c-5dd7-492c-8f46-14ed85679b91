// ignore_for_file: depend_on_referenced_packages, avoid_dynamic_calls

import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/discount.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class ViewDiscountDialogController extends GetxController {
  ViewDiscountDialogController(this.discount);

  final Discount discount;

  final Logger _logger = Logger('View Discount Controller');

  // final HttpProvider _http = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  // final SyncService _syncService = Get.find();

  final GlobalKey<FormState> viewDiscountFormKey = GlobalKey<FormState>();

  final Rx<Discount> currentDiscount = Discount.empty().obs;

  RxBool canEdit = false.obs;
  RxBool canDelete = false.obs;
  RxBool canUpdateDiscount = false.obs;

  // 0 == percent, 1 == dollars off
  RxInt discountType = 0.obs;
  RxString typeTitle = "Percent".obs;

  RxBool timedDiscount = false.obs;

  TextEditingController titleController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController startDateController = TextEditingController();
  TextEditingController percentageController = TextEditingController();
  TextEditingController dollarOffController = TextEditingController();
  TextEditingController maxAmountController = TextEditingController();

  @override
  Future<void> onInit() async {
    await editPermission();
    await deletePermission();

    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await loadControllers();

    super.onReady();
  }

  Future<void> loadControllers() async {
    try {
      currentDiscount.value = discount;

      titleController.text = currentDiscount.value.title;
      endDateController.text = currentDiscount.value.end_at!.toLocal().toString();
      startDateController.text = currentDiscount.value.start_at!.toLocal().toString();
      percentageController.text = currentDiscount.value.document.percentOff.toString();
      dollarOffController.text = Helpers.formatCurrency(currentDiscount.value.document.dollarOff!);
      maxAmountController.text = Helpers.formatCurrency(currentDiscount.value.document.maxAmount!);
      timedDiscount.value = currentDiscount.value.document.timedDiscount!;
      discountType.value = currentDiscount.value.document.type!;
      if (discountType.value == 0) {
        typeTitle.value = "Percent";
      } else {
        typeTitle.value = "Dollar";
      }
      // isTaxable.value = currentDiscount.value.document.isTaxable!;
    } catch (err, stack) {
      _notificationService.error("Failed to load discount!");

      _logger.severe('Error setting discount', err, stack);
    }
  }

  Future<void> updateDiscount() async {
    try {
      if (!viewDiscountFormKey.currentState!.validate()) throw "Invalid form";

      currentDiscount.value.discount = discount.discount;
      currentDiscount.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentDiscount.value.updated_at = DateTime.now().toUtc();
      currentDiscount.value.title = titleController.text;
      currentDiscount.value.document.type = discountType.value;
      currentDiscount.value.document.timedDiscount = timedDiscount.value;
      currentDiscount.value.document.dollarOff = int.parse(
        dollarOffController.text.replaceAll(",", "").replaceAll(".", ""),
      );
      currentDiscount.value.document.percentOff = int.parse(percentageController.text);
      currentDiscount.value.document.maxAmount = int.parse(
        maxAmountController.text.replaceAll(",", "").replaceAll(".", ""),
      );
      currentDiscount.value.end_at = DateTime.parse(endDateController.text).toUtc();
      currentDiscount.value.start_at = DateTime.parse(startDateController.text).toUtc();

      final QueryResult<Object> updateDiscountResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_DISCOUNT(\$discount: uuid = "", \$title: String, \$document: jsonb, \$start_at: timestamptz, \$end_at: timestamptz, \$updated_by: uuid, \$updated_at: timestamptz,) {
            update_discount_by_pk(pk_columns: {discount: \$discount}, _set: {title: \$title, document: \$document, start_at: \$start_at, end_at: \$end_at, updated_by: \$updated_by, updated_at: \$updated_at}) {
              discount
              title
              start_at
              end_at
              document
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "discount": currentDiscount.value.discount,
            "title": currentDiscount.value.title,
            "start_at": currentDiscount.value.start_at.toString(),
            "end_at": currentDiscount.value.end_at.toString(),
            "updated_by": currentDiscount.value.updated_by,
            "updated_at": currentDiscount.value.updated_at.toString(),
            "document": <String, dynamic>{
              "type": currentDiscount.value.document.type,
              "timedDiscount": currentDiscount.value.document.timedDiscount,
              "dollarOff": currentDiscount.value.document.dollarOff,
              "percentOff": currentDiscount.value.document.percentOff,
              "maxAmount": currentDiscount.value.document.maxAmount
            }
          },
        ),
      );

      if (updateDiscountResult.hasException) {
        return _notificationService.error(updateDiscountResult.exception.toString());
      }

      _notificationService.success("Update discount success");

      await Get.offNamed(AppRoutes.ITEMS_DISCOUNTS, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Discount Update failed!");
      _logger.severe('Error updating discount', err, stack);
    }
  }

  Future<void> deleteDiscount() async {
    try {
      final QueryResult<Object> deleteDiscountResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
          mutation DELETE_DISCOUNT(\$discount: uuid!) {
              delete_discount_by_pk(discount: \$discount) {
                discount
                created_at
                created_by
                document
                end_at
                start_at
                title
                updated_at
                updated_by
              }
            }

          ''',
          ),
          variables: <String, dynamic>{"discount": currentDiscount.value.discount},
        ),
      );

      if (deleteDiscountResult.hasException) {
        return _notificationService.error(deleteDiscountResult.exception.toString());
      }
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name}");
      _notificationService.success("Discount deleted");
      await Get.offNamed(AppRoutes.ITEMS_DISCOUNTS, id: AppRoutes.id);
    } catch (err, stack) {
      _logger.severe('Error deleting discount', err, stack);
    }
  }

  void Function(bool?)? timedDiscountSwitch() => (bool? value) {
        timedDiscount.value = !timedDiscount.value;
      };

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Discount",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateDiscount.value = !edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Discount",
      _graphqlService,
    );
    canDelete.value = delete;
  }

  void Function(String?) choiceSwitch(String value) => (String? value) {
        typeTitle.value = value!;
        if (typeTitle.value == "Percent") {
          discountType.value = 0;
        } else {
          discountType.value = 1;
        }
      };
}
