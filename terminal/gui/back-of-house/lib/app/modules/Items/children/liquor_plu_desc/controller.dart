// ignore_for_file: depend_on_referenced_packages

import 'package:backoffice/app/data/services/liquor_plu_desc_service.dart';
import 'package:backoffice/app/modules/_root/controller.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/liquor_plu_desc.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

enum PluSort { PLU_ASC, PLU_DESC, DESC_ASC, DESC_DESC }

final Logger _logger = Logger('ConfigService');

class LiquorPluController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  final LiquorPluDescService _liquorPluDescService = Get.find();

  final RootController _rootController = Get.find();

  RxString param = "".obs;

  RxInt offset = 0.obs;
  RxInt pageSize = 10.obs;

  PluSort currentSort = PluSort.PLU_DESC;
  RxBool loading = true.obs;
  RxBool queryError = false.obs;
  RxBool editing = false.obs;
  RxBool importPermission = false.obs;

  RxString selectedPluDesc = "".obs;

  RxList<LiquorPluDesc> pluDescList = <LiquorPluDesc>[].obs;
  TextEditingController pluController = TextEditingController();
  TextEditingController descController = TextEditingController();

  @override
  Future<void> onInit() async {
    pluDescList.value = await getLiquorPluDescs();
    sortPluDescs(PluSort.PLU_ASC);
    editing = _rootController.disabled;
    loading.value = false;
    await getImportPermission();
    super.onInit();
  }

  void clearSelection() {
    selectedPluDesc.value = "";
    pluController.text = "";
    descController.text = "";
  }

  Future<List<LiquorPluDesc>> getLiquorPluDescs() async {
    return (await _liquorPluDescService.getAll()).match(
      (ServiceError l) {
        _notificationService.error(l.message);
        return <LiquorPluDesc>[];
      },
      (List<LiquorPluDesc> r) => r,
    );
  }

  Future<void> getImportPermission() async {
    importPermission.value = await PermissionService.enforce(
          CURRENT_EMPLOYEE.value.employee_class,
          "create",
          "Liquor PLU Descriptions",
          _graphqlService,
        ) &&
        await PermissionService.enforce(
          CURRENT_EMPLOYEE.value.employee_class,
          "edit",
          "Liquor PLU Descriptions",
          _graphqlService,
        );
  }

  Future<void> createPluDescPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Liquor PLU Descriptions",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate();
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> updatePluDescPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Liquor PLU Descriptions",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate();
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> deletePluDescPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Liquor PLU Descriptions",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate(
        delete: true,
      );
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  void sortPluDescs(PluSort sortType) {
    final bool isAsc = sortType.name.contains("ASC");
    if (sortType.name.contains("PLU")) {
      if (isAsc) {
        pluDescList.sort((LiquorPluDesc a, LiquorPluDesc b) => b.liq_ctl_plu.compareTo(a.liq_ctl_plu));
      } else {
        pluDescList.sort((LiquorPluDesc a, LiquorPluDesc b) => a.liq_ctl_plu.compareTo(b.liq_ctl_plu));
      }
    } else {
      if (isAsc) {
        pluDescList.sort((LiquorPluDesc a, LiquorPluDesc b) => b.desc.compareTo(a.desc));
      } else {
        pluDescList.sort((LiquorPluDesc a, LiquorPluDesc b) => a.desc.compareTo(b.desc));
      }
    }
  }

  List<DataRow> generateDataRows() {
    final List<DataRow> rows = <DataRow>[];
    for (int i = 0; i < pluDescList.length; i++) {
      rows.add(
        DataRow.byIndex(
          selected: selectedPluDesc.value == pluDescList[i].liq_ctl_plu_desc,
          onSelectChanged: (_) {
            if (editing.value) return;

            if (selectedPluDesc.value == pluDescList[i].liq_ctl_plu_desc) {
              return clearSelection();
            }
            selectedPluDesc.value = pluDescList[i].liq_ctl_plu_desc;
            descController.text = pluDescList[i].desc;
            pluController.text = pluDescList[i].liq_ctl_plu.toString();
          },
          index: i,
          cells: <DataCell>[
            DataCell(
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      pluDescList[i].liq_ctl_plu.toString(),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            DataCell(
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      pluDescList[i].desc,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return rows;
  }

  Future<void> confirmUpdate({bool delete = false}) async {
    try {
      final bool inserting = selectedPluDesc.value.isEmpty;

      if (delete && !inserting) {
        final List<LiquorPluDesc> deletedList = (await _liquorPluDescService.delete(selectedPluDesc.value)).match(
          (ServiceError l) {
            _notificationService.error(l.message);
            return <LiquorPluDesc>[];
          },
          (List<LiquorPluDesc> r) => r,
        );
        if (deletedList.isEmpty) {
          _notificationService.error("Failed to find PLU Description for deletion");
          return;
        }

        clearSelection();
      } else {
        if (pluController.text.isEmpty) return _notificationService.error("PLU is required");
        final int pluValue = int.tryParse(pluController.text) ?? 0;
        if (pluValue < 1000) return _notificationService.error("PLU must have 4 digits and cannot start with 0");

        if (inserting) {
          final List<LiquorPluDesc> existingList = (await _liquorPluDescService.findExisting(pluValue)).match(
            (ServiceError l) {
              _notificationService.error(l.message);
              return <LiquorPluDesc>[];
            },
            (List<LiquorPluDesc> r) => r,
          );
          if (existingList.isNotEmpty) {
            _notificationService.error("PLU must be unique");
            return;
          }
        }

        (await _liquorPluDescService.upsert(
          LiquorPluDesc(
            desc: descController.text,
            liq_ctl_plu: pluValue,
            liq_ctl_plu_desc: selectedPluDesc.value,
          ),
        ))
            .match(
          (ServiceError l) => _notificationService.error(l.message),
          (LiquorPluDesc r) => null,
        );
      }

      pluDescList.value = await getLiquorPluDescs();
      sortPluDescs(currentSort);
      if (inserting) clearSelection();

      _notificationService.success("PLU Description ${delete ? "deleted" : inserting ? "inserted" : "updated"}!");
    } catch (err, stack) {
      _logger.severe(
        "Error updating PLU Descriptions.",
        err,
        stack,
      );
      _notificationService.error("Error updating PLU Descriptionss.");
    }
  }
}
