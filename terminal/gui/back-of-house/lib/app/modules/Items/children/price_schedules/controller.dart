// ignore_for_file: depend_on_referenced_packages

import 'package:backoffice/app/modules/Items/children/price_schedules/dialog/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('ConfigService');

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

class PriceSchedulesController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  RxString param = "".obs;

  RxInt offset = 0.obs;
  RxInt pageSize = 10.obs;

  RxBool sortAsc = false.obs;
  RxBool loading = true.obs;
  RxBool queryError = false.obs;

  List<PriceSchedule> scheduleList = <PriceSchedule>[];
  RxList<PriceSchedule> filteredList = <PriceSchedule>[].obs;

  TextEditingController paramController = TextEditingController();

  @override
  Future<void> onInit() async {
    final PriceScheduleJsonRecord scheduleRecord = await getPriceSchedules();
    scheduleList = scheduleRecord.document.discs;
    sortSchedules();
    loading.value = false;
    super.onInit();
  }

  Future<PriceScheduleJsonRecord> getPriceSchedules() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'priceSchedule',
          },
        ),
      );

      // ignore: always_specify_types
      if ((configResult.data!['json_record']! as List).isEmpty) {
        return PriceScheduleJsonRecord.empty();
      }

      return PriceScheduleJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
    } catch (err, stack) {
      _logger.severe(
        "Error getting price schedules.",
        err,
        stack,
      );
      queryError.value = true;
      return PriceScheduleJsonRecord.empty();
    }
  }

  Future<void> createSchedulePermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Create",
      "Price Schedule",
      _graphqlService,
    );
    if (create) {
      Get.bottomSheet(
        PriceScheduleDialog(),
        enableDrag: false,
        isScrollControlled: true,
        isDismissible: false,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewSchedulePermission(int index) async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Price Schedule",
      _graphqlService,
    );
    if (view) {
      Get.toNamed(
        AppRoutes.PRICE_SCHEDULES_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "schedule": filteredList[index],
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  void filterSchedules() {
    if (param.value == "") {
      filteredList.value = scheduleList;
    } else {
      filteredList.value = scheduleList
          .where(
            (PriceSchedule row) => row.desc.toLowerCase().contains(param.value.toLowerCase()),
          )
          .toList();
    }
    loading.refresh();
  }

  void sortSchedules() {
    if (sortAsc.value) {
      scheduleList.sort((PriceSchedule a, PriceSchedule b) => b.desc.compareTo(a.desc));
    } else {
      scheduleList.sort((PriceSchedule a, PriceSchedule b) => a.desc.compareTo(b.desc));
    }
    filterSchedules();
  }

  List<DataRow> generateDataRows() {
    final List<DataRow> rows = <DataRow>[];
    final int start = offset.value < 10 ? 0 : offset.value;
    final int end = (start + pageSize.value) < filteredList.length ? start + pageSize.value : filteredList.length;
    for (int i = start; i < end; i++) {
      rows.add(
        DataRow.byIndex(
          onSelectChanged: (_) {
            viewSchedulePermission(i);
          },
          index: i,
          cells: <DataCell>[
            DataCell(
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      filteredList[i].desc,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return rows;
  }

  void farLeftArrowPress() {
    if (offset.value != 0) {
      offset.value = 0;
    }
  }

  void middleLeftArrowPress() {
    if (offset.value - pageSize.value >= 0) {
      offset.value = offset.value - pageSize.value;
    }
  }

  void middleRightArrowPress() {
    if (offset.value + pageSize.value < filteredList.length) {
      offset.value = offset.value + pageSize.value;
    }
  }

  void farRightArrowPress() {
    if (offset.value != filteredList.length - pageSize.value) {
      final int pagesIn = filteredList.length - (filteredList.length % pageSize.value);
      if (pagesIn < filteredList.length) {
        offset.value = pagesIn;
      } else {
        offset.value = pagesIn - pageSize.value;
      }
    }
  }
}
