import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/modules/Items/children/price_schedules/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/app/global_widgets/custom_time_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      record_key
      updated_at
      document
    }
  }
''';

final Logger _logger = Logger('ConfigService');

class PriceScheduleDialogController extends GetxController {
  PriceScheduleDialogController({this.schedule});
  PriceSchedule? schedule;

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  final PriceSchedulesController _priceSchedulesController = Get.find();

  final GlobalKey<FormState> priceScheduleFormKey = GlobalKey<FormState>();

  final TextEditingController titleController = TextEditingController();

  final RxBool newSchedule = false.obs;
  final RxBool isSeasonal = false.obs;
  final RxBool editing = false.obs;
  final RxInt startMonth = 1.obs;
  final RxInt startDay = 1.obs;
  final RxInt endMonth = 1.obs;
  final RxInt endDay = 1.obs;

  final RxInt startHourController = 1.obs;
  final RxInt startMinuteController = 0.obs;
  final RxBool startPmTime = false.obs;
  final RxInt endHourController = 1.obs;
  final RxInt endMinuteController = 0.obs;
  final RxBool endPmTime = false.obs;

  final Map<String, int> week = <String, int>{"monday": 64, "tuesday": 32, "wednesday": 16, "thursday": 8, "friday": 4, "saturday": 2, "sunday": 1};

  final Map<String, int> months = <String, int>{
    "Janurary": 1,
    "February": 2,
    "March": 3,
    "April": 4,
    "May": 5,
    "June": 6,
    "July": 7,
    "August": 8,
    "September": 9,
    "October": 10,
    "November": 11,
    "December": 12
  };

  final RxInt days = 127.obs;

  @override
  Future<void> onInit() async {
    if (schedule == null) {
      newSchedule.value = true;
      schedule = PriceSchedule.empty();
      final int milli = const Duration(hours: 12).inMilliseconds;
      schedule!.startTime = milli;
      schedule!.endTime = milli;
    }
    titleController.text = schedule!.desc;
    days.value = schedule!.days;
    millisecondsToTimeInts(schedule!);
    isSeasonal.value = schedule!.isSeasonal;
    startMonth.value = 1;
    startDay.value = schedule!.seasStDay == 0 ? 1 : schedule!.seasStDay;
    endMonth.value = schedule!.seasEndMonth == 0 ? 1 : schedule!.seasEndMonth;
    endDay.value = schedule!.seasEndDay == 0 ? 1 : schedule!.seasEndDay;

    super.onInit();
  }

  Future<void> addSchedule() async {
    editing.value = true;
    await confirmUpdate();
    editing.value = false;
    Get.back();
  }

  Future<void> updateSchedulePermission() async {
    final bool update = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Edit",
      "Price Schedule",
      _graphqlService,
    );
    if (update) {
      editing.value = true;
      await confirmUpdate();
      editing.value = false;
      Get.offNamed(
        AppRoutes.ITEMS_PRICE_SCHEDULES,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> deleteSchedulePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Delete",
      "Price Schedule",
      _graphqlService,
    );
    if (delete) {
      editing.value = true;
      await confirmUpdate(
        delete: true,
      );
      editing.value = false;
      Get.offNamed(
        AppRoutes.ITEMS_PRICE_SCHEDULES,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> confirmUpdate({bool delete = false}) async {
    try {
      final PriceScheduleJsonRecord schedulesConfig = await _priceSchedulesController.getPriceSchedules();

      final List<PriceSchedule> configList = schedulesConfig.document.discs;

      configList.sort((PriceSchedule a, PriceSchedule b) => a.idx.compareTo(b.idx));

      final PriceSchedule createdSchedule = PriceSchedule(
        idx: newSchedule.value ? configList.length + 1 : schedule!.idx,
        desc: titleController.text,
        days: days.value,
        startTime: intsToMilliseconds(
          hour: startHourController.value,
          minute: startMinuteController.value,
          pm: startPmTime.value,
        ),
        endTime: intsToMilliseconds(
          hour: endHourController.value,
          minute: endMinuteController.value,
          pm: endPmTime.value,
        ),
        isSeasonal: isSeasonal.value,
        seasStMonth: startMonth.value,
        seasStDay: startDay.value,
        seasEndMonth: endMonth.value,
        seasEndDay: endDay.value,
      );

      if (newSchedule.value) {
        final PriceSchedule? existing = configList.firstWhereOrNull(
          (PriceSchedule ps) => ps.desc == titleController.text,
        );

        if (existing != null) {
          _notificationService.error("Price Schedule title must be unique");
          return;
        }

        configList.add(createdSchedule);
      } else if (delete) {
        await updateItemsForDelete(configList.length);

        configList.removeWhere(
          (PriceSchedule ps) => ps.idx == schedule!.idx,
        );

        for (final PriceSchedule ps in configList) {
          if (ps.idx > schedule!.idx) {
            ps.idx = ps.idx - 1;
          }
        }
      } else {
        final int idx = configList.indexWhere((PriceSchedule ps) => ps.idx == createdSchedule.idx);

        configList[idx] = createdSchedule;
      }

      final Map<String, dynamic> jsonObject = schedulesConfig.toJson();

      jsonObject.removeWhere((String key, dynamic value) => key == "updated_at");

      final QueryResult<Object?> objectResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": jsonObject,
          },
        ),
      );

      if (objectResult.hasException) {
        throw objectResult.exception.toString();
      }

      _priceSchedulesController.scheduleList = configList;
      _priceSchedulesController.sortSchedules();
      _notificationService.success("Price schedules updated!");
    } catch (err, stack) {
      _logger.severe(
        "Error updating price schedules.",
        err,
        stack,
      );
      _notificationService.error("Error updating price schedules.");
    }
  }

  Future<void> updateItemsForDelete(int schedulesLength) async {
    final StringBuffer buffer = StringBuffer();
    for (int i = schedule!.idx; i < schedulesLength + 1; i++) {
      buffer.write('{document: {_cast: {String: {_like: "%S${i}L_%"}}}}, ');
    }

    // ignore: non_constant_identifier_names
    final String GET_ITEMS = '''
      query GET_ITEMS {
        item(where: {_or: [$buffer]}) {
          item
          long_desc
          upc
          department
          created_at
          created_by
          updated_at
          updated_by
          document
          liq_ctl_plu
        }
      }
    ''';

    final QueryResult<Object?> itemsResult = await _graphqlService.client.query(
      QueryOptions<Object?>(
        document: g.parseString(GET_ITEMS),
      ),
    );

    if (itemsResult.hasException) {
      throw itemsResult.exception.toString();
    }

    if ((itemsResult.data!['item'] as List<dynamic>).isEmpty) return;

    final List<Item> itemList = (itemsResult.data!['item'] as List<dynamic>)
        .map(
          (dynamic config) => Item.fromJson(
            config as Map<String, dynamic>,
          ),
        )
        .toList();

    for (final Item item in itemList) {
      final Map<String, int> pricingObject = item.document.pricing;
      final List<String> pricingKeys = pricingObject.keys.toList();
      final Map<String, int> newObject = <String, int>{};

      for (final String key in pricingKeys) {
        String newKey = key;
        final String subStr = key.substring(1);
        final List<String> splitStr = subStr.split("L");
        final int parsedInt = int.parse(splitStr[0]);

        if (splitStr[0] != schedule!.idx.toString()) {
          if (parsedInt > schedule!.idx) {
            newKey = "S${parsedInt - 1}L${splitStr[1]}";
          }

          newObject[newKey] = pricingObject[key] ?? 0;
        }
      }
      item.document.pricing = newObject;

      final QueryResult<Object> updateItemResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_ITEM_BY_PK(\$item: uuid!, \$document: jsonb) {
              update_item_by_pk(pk_columns: {item: \$item}, _set: {document: \$document}) {
                department
                document
                item
                long_desc
                upc
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "item": item.item,
            "document": item.document.toJson(),
          },
        ),
      );

      if (updateItemResult.hasException) {
        return _notificationService.error(updateItemResult.exception.toString());
      }
    }
  }

  int intsToMilliseconds({
    required int hour,
    required int minute,
    required bool pm,
  }) {
    final bool addHours = pm && hour != 12;
    if (hour == 12 && !pm) hour = 0;
    final int hours = addHours ? hour + 12 : hour;
    return Duration(minutes: (hours * 60) + minute).inMilliseconds;
  }

  void millisecondsToTimeInts(PriceSchedule schedule) {
    final int startMinutesFromMS = Duration(milliseconds: schedule.startTime).inMinutes;
    final int endMinutesFromMS = Duration(milliseconds: schedule.endTime).inMinutes;

    startMinuteController.value = startMinutesFromMS % 60;
    endMinuteController.value = endMinutesFromMS % 60;

    int startHour = startMinutesFromMS ~/ 60;
    int endHour = endMinutesFromMS ~/ 60;

    if (startHour > 11) startPmTime.value = true;
    if (startHour > 12) startHour -= 12;

    if (endHour > 11) endPmTime.value = true;
    if (endHour > 12) endHour -= 12;

    startHourController.value = startHour == 0 ? 12 : startHour;
    endHourController.value = endHour == 0 ? 12 : endHour;
  }

  int getDaysInMonth(int month) {
    const List<int> daysInMonth = <int>[31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    return daysInMonth[month - 1];
  }

  Future<void> onTapStartTime() async {
    final int hour = startHourController.value;
    final int min = startMinuteController.value;
    final bool pm = startPmTime.value;
    await Get.defaultDialog(
      title: "Start Time",
      content: Column(
        children: <Widget>[
          CustomTimePicker(
            hourController: startHourController,
            minuteController: startMinuteController,
            pmTime: startPmTime,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () {
                  Get.back(result: false);
                },
              ),
              DialogButton(
                buttonType: EDialogButtonType.AFFIRMATIVE,
                onTapped: () {
                  Get.back(result: true);
                },
              ),
            ],
          ),
        ],
      ),
    ).then((dynamic value) {
      if (value != true) {
        startHourController.value = hour;
        startMinuteController.value = min;
        startPmTime.value = pm;
      }
    });
  }

  Future<void> onTapEndTime() async {
    final int hour = endHourController.value;
    final int min = endMinuteController.value;
    final bool pm = endPmTime.value;
    await Get.defaultDialog(
      title: "End Time",
      content: Column(
        children: <Widget>[
          CustomTimePicker(
            hourController: endHourController,
            minuteController: endMinuteController,
            pmTime: endPmTime,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () {
                  Get.back(result: false);
                },
              ),
              DialogButton(
                buttonType: EDialogButtonType.AFFIRMATIVE,
                onTapped: () {
                  Get.back(result: true);
                },
              ),
            ],
          ),
        ],
      ),
    ).then((dynamic value) {
      if (value != true) {
        endHourController.value = hour;
        endMinuteController.value = min;
        endPmTime.value = pm;
      }
    });
  }

  String getStartTimeString() {
    final int hour = startHourController.value;
    final int minute = startMinuteController.value;
    return "$hour:${minute < 10 ? "0" : ""}$minute ${startPmTime.value ? "PM" : "AM"}";
  }

  String getEndTimeString() {
    final int hour = endHourController.value;
    final int minute = endMinuteController.value;
    return "$hour:${minute < 10 ? "0" : ""}$minute ${endPmTime.value ? "PM" : "AM"}";
  }
}
