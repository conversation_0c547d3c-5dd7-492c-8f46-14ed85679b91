// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddDepartmentDialogController');

class AddRecipeController extends GetxController {
  AddRecipeController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> addRecipeFormKey = GlobalKey<FormState>();

  final RxBool isLoading = true.obs;

  TextEditingController titleController = TextEditingController();
  TextEditingController itemController = TextEditingController();
  TextEditingController recipeController = TextEditingController();
  RxBool isTableLoading = true.obs;
  Rxn<Item> selectedItem = Rxn<Item>();

  @override
  Future<void> onInit() async {
    isLoading.value = false;
    super.onInit();
  }

  Future<void> addRecipe() async {
    try {
      if (!addRecipeFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult<Object> addRecipeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation MyMutation(\$title: String, \$item: uuid, \$directions: String) {
                insert_recipe_one(object: {title: \$title, item: \$item, directions: \$directions}) {
                  recipe
                  title
                  item
                  directions
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "title": titleController.text,
            "item": selectedItem.value?.item,
            "directions": recipeController.text,
          },
        ),
      );

      if (addRecipeResult.hasException) {
        return _notificationService.error(addRecipeResult.exception.toString());
      }
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} added discount - ${titleController.text}');
      _notificationService.success('Add Recipe success');
      await Get.offNamed(AppRoutes.ITEMS_RECIPES, id: AppRoutes.id);
    } catch (err, stack) {
      _logger.severe('Error Adding Recipe', err, stack);
    }
  }
}
