// ignore_for_file: non_constant_identifier_names

import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/recipe.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class ViewRecipeController extends GetxController {
  ViewRecipeController(
    this.recipe,
  );

  final Logger _logger = Logger('ViewRecipeController');

  final Recipe recipe;
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final GlobalKey<FormState> viewRecipeFormKey = GlobalKey<FormState>();

  TextEditingController titleController = TextEditingController();
  TextEditingController directionsController = TextEditingController();

  RxBool canEdit = false.obs;
  RxBool canDelete = false.obs;
  RxBool canUpdateDepartment = false.obs;
  Rxn<Item> selectedItem = Rxn<Item>();
  TextEditingController itemController = TextEditingController();

  ScrollController scrollController = ScrollController();

  @override
  Future<void> onInit() async {
    titleController.text = recipe.title;
    directionsController.text = recipe.directions;
    if (recipe.item != null) {
      await setItem(recipe.item!);
    }

    await editPermission();
    await deletePermission();

    super.onInit();
  }

  Future<void> updateRecipe() async {
    try {
      if (!viewRecipeFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult<Object> updateRecipeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_RECIPE_BY_PK(\$recipe: uuid!, \$title: String, \$item: uuid, \$directions: String) {
              update_recipe_by_pk(pk_columns: {recipe: \$recipe}, _set: {title: \$title, item: \$item, directions: \$directions}) {
                title
                recipe
                item
                directions
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "recipe": recipe.recipe,
            "title": titleController.text,
            "directions": directionsController.text,
            "item": selectedItem.value?.item,
          },
        ),
      );

      if (updateRecipeResult.hasException) {
        return _notificationService.error(updateRecipeResult.exception.toString());
      }
      _notificationService.success("Successfully updated Recipe");
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} updated Recipe - ${titleController.text}");
      await Get.offNamed(AppRoutes.ITEMS_RECIPES, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Recipe Update failed!");
      _logger.severe('Error updating Recipe', err, stack);
    }
  }

  Future<void> deleteRecipe() async {
    try {
      final QueryResult<Object> deleteRecipeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation MyMutation(\$recipe: uuid!) {
              delete_recipe_by_pk(recipe: \$recipe){
                recipe
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "recipe": recipe.recipe,
          },
        ),
      );

      if (deleteRecipeResult.hasException) {
        return _notificationService.error(deleteRecipeResult.exception.toString());
      }
      _notificationService.success("Successfully deleted Recipe");
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} deleted recipe");
      await Get.offNamed(AppRoutes.ITEMS_RECIPES, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Recipe Delete failed!");
      _logger.severe('Error deleting Recipe', err, stack);
    }
  }

  Future<void> setItem(String item) async {
    try {
      final QueryResult<Object> setItemRes = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           query GET_ITEM_BY_PK(\$item: uuid!) {
            item_by_pk(item: \$item) {
              department
              created_by
              created_at
              document
              updated_by
              updated_at
              upc
              long_desc
              item
              liq_ctl_plu
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "item": item,
          },
        ),
      );

      if (setItemRes.hasException) {
        return _notificationService.error(setItemRes.exception.toString());
      }
      selectedItem.value = Item.fromJson(setItemRes.data!['item_by_pk'] as Map<String, dynamic>);
      if (selectedItem.value != null) {
        itemController.text = selectedItem.value!.long_desc;
      }
    } catch (err, stack) {
      _notificationService.error("Error fetching Item");
      _logger.severe("Error setting Item on Recipe", err, stack);
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Departments",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateDepartment.value = !edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Departments",
      _graphqlService,
    );
    canDelete.value = !delete;
  }
}
