import 'dart:async';

import 'package:backoffice/app/data/services/department.service.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/Items/dialogs/view_item/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class ItemsController extends GetxController {
  ItemsController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final DepartmentService _departmentService = Get.find();
  final Logger itemLogger = Logger('ItemLogger');
  final RxList<Department> departments = <Department>[].obs;
  final RxString departmentFilter = "".obs;

  @override
  Future<void> onInit() async {
    departments.value = await _departmentService.getDepartments();
    super.onInit();
  }

  Future<void> viewItemPermission(Item item) async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Items",
      _graphqlService,
    );
    if (create) {
      itemLogger.info("${CURRENT_EMPLOYEE.value.employee_full_name} viewing item ${item.long_desc}");
      await Get.toNamed(
        AppRoutes.ITEMS_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "item": item,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> createItemPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    if (create) {
      await Get.bottomSheet<Item?>(
        ThinBottomSheet(
          child: ViewItemDialog(),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> createMultipleItemPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    if (create) {
      await Get.bottomSheet(
        ThinBottomSheet(
          child: ViewItemDialog(
            addMultiple: true,
          ),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> importItemPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    if (create) {
      Get.back();
      await Get.toNamed(
        AppRoutes.ITEMS_IMPORT,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> exportItemPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Items",
      _graphqlService,
    );
    if (create) {
      Get.back();
      await Get.toNamed(
        AppRoutes.ITEMS_EXPORT,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> massAttributePermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    if (create) {
      Get.back();
      await Get.toNamed(
        AppRoutes.ITEMS_MASS_ATTRIBUTE,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> massDeletePermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    if (create) {
      Get.back();
      await Get.toNamed(
        AppRoutes.ITEMS_MASS_DELETE,
        id: AppRoutes.id,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
