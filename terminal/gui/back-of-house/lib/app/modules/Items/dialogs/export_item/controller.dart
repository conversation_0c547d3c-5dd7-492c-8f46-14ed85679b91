import 'package:backoffice/app/data/enums/import_export_type.dart';
import 'package:backoffice/app/data/services/item_service.dart';
import 'package:backoffice/app/data/services/liquor_plu_desc_service.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/liquor_plu_desc.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

enum ExportingStep { TO_FILE_BYTES }

class ExportItemDialogController extends GetxController {
  ExportItemDialogController(this.type);

  final ImportExportType type;

  // Block: Used for the progress bar.
  final RxInt isExporting = 0.obs;
  final Rx<ExportingStep> exportingStep = ExportingStep.values[0].obs;
  final RxInt exportingStepInt = 0.obs;
  final RxDouble exportingProgress = 0.0.obs;
  final RxInt isExportingFile = 0.obs;

  final ItemService _itemService = Get.find();
  final LiquorPluDescService _liquorPluDescService = Get.find();
  final NotificationService notificationService = Get.find();

  @override
  Future<void> onInit() async {
    super.onInit();
    // This sets exportingStepInt to observe exportingStep, and updates it when exportingStep changes.
    ever(exportingStep, (ExportingStep step) {
      exportingStepInt.value = step.index;
    });
    ever(exportingStepInt, (int step) {
      exportingStep.value = ExportingStep.values[step];
    });
    // Compute exportingStepInt for the initial value.
    exportingStep.trigger(exportingStep.value);
  }

  Item? _findItem(List<Item> items, String itemUUID) {
    return items.firstWhereOrNull((Item element) => element.item == itemUUID);
  }

  Future<Excel> toItemSheet() async {
    final Excel excelSheet = Excel.createExcel();
    final Sheet itemSheet = excelSheet['Item Sheet'];
    final Sheet modSheet = excelSheet['Mod Sheet'];
    final Sheet modDataSheet = excelSheet['Mod Data Sheet'];
    excelSheet.delete('Sheet1');

    final List<CellValue?> itemHeaderRow = <CellValue>[
      TextCellValue("Department(text)"),
      TextCellValue("ItemDesc(text)"),
      TextCellValue("Receipt Description(text)"),
      TextCellValue("Detailed Description(text)"),
      TextCellValue("ItemUUID(text)"),
      TextCellValue("Price(int)"),
      TextCellValue("UPC(text)"),
      TextCellValue("isWeighted(bool)"),
      TextCellValue("UOM"),
      TextCellValue("allowEbt(bool)"),
      TextCellValue("promptForPrice(bool)"),
      TextCellValue("pinToTop(bool)"),
      TextCellValue("negativeItem(bool)"),
      TextCellValue("count(int)"),
      TextCellValue("modDes(text)"),
      TextCellValue("modMaxSel(int)"),
      TextCellValue("modMinSel(int)"),
      TextCellValue("overridePricing(bool)"),
      TextCellValue("passDesc(bool)"),
      TextCellValue("isModifier(bool)"),
      TextCellValue("multiModLists(bool)"),
      TextCellValue("defModPricing(int)"),
    ];
    itemSheet.insertRowIterables(itemHeaderRow, 0);

    final List<CellValue?> modHeaderRow = <CellValue>[
      TextCellValue("ModDesc(text)"),
      TextCellValue("ModItemUUID(text)"),
      TextCellValue("ParentDesc(text)"),
      TextCellValue("ParentUUID(text)"),
      TextCellValue("idx(int)"),
      TextCellValue("modPricing(int)"),
    ];
    modSheet.insertRowIterables(modHeaderRow, 0);

    final List<CellValue?> modDataHeaderRow = <CellValue>[
      TextCellValue("ModDesc(text)"),
      TextCellValue("ModItemUUID(text)"),
      TextCellValue("ParentDesc(text)"),
      TextCellValue("ParentUUID(text)"),
      TextCellValue("Schedule(text)"),
      TextCellValue("Price(int)"),
    ];
    modDataSheet.insertRowIterables(modDataHeaderRow, 0);

    final List<Item> itemList = await _itemService.getAllItems();

    for (int i = 0; i < itemList.length; i++) {
      final Item item = itemList[i];
      exportingProgress.value = i / itemList.length;
      final List<CellValue?> items = <CellValue?>[
        TextCellValue(item.departmentByDepartment!.title),
        TextCellValue(item.long_desc),
        TextCellValue(item.document.receiptDesc ?? ""),
        TextCellValue(item.document.detailedDesc ?? ""),
        TextCellValue(item.item),
        TextCellValue((item.document.pricing["S0L0C0"] ?? 0).toString()),
        TextCellValue(item.upc),
        TextCellValue(item.document.isWeighted.toString()),
        TextCellValue((item.document.UOM ?? 0).toString()),
        TextCellValue(item.document.allowEbt.toString()),
        TextCellValue(item.document.promptForPrice.toString()),
        TextCellValue(item.document.pinToTop.toString()),
        TextCellValue(item.document.negativeItem.toString()),
        TextCellValue(item.document.count.toString()),
        TextCellValue(item.document.modifierDesc.toString()),
        TextCellValue(item.document.modMinSel.toString()),
        TextCellValue(item.document.modMaxSel.toString()),
        TextCellValue(item.document.overridePricing.toString()),
        TextCellValue(item.document.passDesc.toString()),
        TextCellValue(item.document.isModifier.toString()),
        TextCellValue(item.document.multiModLists.toString()),
        TextCellValue(item.document.defModPricing['S0L0C0'].toString()),
      ];
      itemSheet.insertRowIterables(items, i + 1);

      for (final String mod in item.document.modifiers.keys) {
        final Item parentItem = item;
        final Item? modItem = _findItem(itemList, mod);

        if (modItem == null) {
          continue;
        }

        final ModData modData = item.document.modifiers[mod]!;

        final List<CellValue?> mods = <CellValue?>[
          TextCellValue(modItem.long_desc),
          TextCellValue(modItem.item),
          TextCellValue(parentItem.long_desc),
          TextCellValue(parentItem.item),
          IntCellValue(modData.idx),
          IntCellValue(modData.pricing['S0L0C0'] ?? 0),
        ];
        modSheet.appendRow(mods);

        for (final String schedule in modData.pricing.keys) {
          final List<CellValue?> modDatas = <CellValue?>[
            TextCellValue(modItem.long_desc),
            TextCellValue(modItem.item),
            TextCellValue(parentItem.long_desc),
            TextCellValue(parentItem.item),
            TextCellValue(schedule),
            IntCellValue(modData.pricing[schedule] ?? 0),
          ];
          modDataSheet.appendRow(modDatas);
        }
      }
    }

    return excelSheet;
  }

  Future<Excel> toPluSheet() async {
    final Excel excelSheet = Excel.createExcel();
    final Sheet liquorPluSheet = excelSheet['Liquor PLU Sheet'];
    excelSheet.delete('Sheet1');

    final List<CellValue?> liquorPluHeaderRow = <CellValue>[
      TextCellValue("PLU(int)"),
      TextCellValue("Description(text)"),
    ];
    liquorPluSheet.insertRowIterables(liquorPluHeaderRow, 0);

    final List<LiquorPluDesc> liquorPluList = (await _liquorPluDescService.getAll()).match(
      (ServiceError l) => <LiquorPluDesc>[],
      (List<LiquorPluDesc> r) => r,
    );

    for (int i = 0; i < liquorPluList.length; i++) {
      final LiquorPluDesc liquorPluDesc = liquorPluList[i];

      final List<CellValue?> pluDescs = <CellValue?>[
        IntCellValue(liquorPluDesc.liq_ctl_plu),
        TextCellValue(liquorPluDesc.desc),
      ];
      liquorPluSheet.insertRowIterables(pluDescs, i + 1);
    }
    return excelSheet;
  }

  Future<Uint8List> toFileBytes() async {
    final Excel excelSheet = type == ImportExportType.ITEMS ? await toItemSheet() : await toPluSheet();
    final List<int>? fileBytes = await compute(
      (Excel excelSheet_) => excelSheet_.save(fileName: 'My_Excel_File_Name.xlsx'),
      excelSheet,
    );
    if (fileBytes == null) {
      throw Exception("Inernal error with excel library save(): File bytes are null");
    }
    return Uint8List.fromList(fileBytes);
  }
}
