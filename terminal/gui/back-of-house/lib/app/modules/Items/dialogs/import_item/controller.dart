import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:backoffice/app/data/enums/import_export_type.dart';
import 'package:backoffice/app/data/services/department.service.dart';
import 'package:backoffice/app/data/services/item_service.dart';
import 'package:backoffice/app/data/services/liquor_plu_desc_service.dart';
import 'package:backoffice/main.dart';
import 'package:dartz/dartz.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/liquor_plu_desc.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:excel/excel.dart' as excel;
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:path/path.dart' as path;
import 'package:pool/pool.dart';
import 'package:synchronized/synchronized.dart';

final Logger _logger = Logger('ImportItemDialogController');

// Associate a column name with an observable index integer, and whether it is optional.
class ColumnInfo extends Object {
  ColumnInfo(
    this.name,
    this.columnIndex,
    this.optional,
  );
  String name;
  RxnInt columnIndex;
  bool optional;
}

// The main form has a bunch of steps that the user goes through.
enum FormStepType {
  SELECT_FILE,
  CONFIGURE_ITEMS,
  CONFIGURE_MODIFIERS,
  CONFIGURE_MOD_DATA,
  CONFIGURE_LIQUOR_PLUS,
  CONFIRM_CHANGES,
  FINISH,
}

// This is used to track the progress of annotating the sheet, for the progress bar.
enum AnnotatingStep {
  CLEARING_ISSUE_MESSAGES,
  COMPUTING_ROW_LEVELS,
  ANNOTATING_COLORS,
  ANNOTATING_ISSUES,
}

// This is used to track the progress of importing the sheet, for the progress bar.
// Use to be called ImportStep, but made it more generalized for additional use cases
enum SheetType {
  ITEM,
  MOD,
  MOD_DATA,
  LIQUOR_PLU,
}

extension Stringify on SheetType {
  String get sheetName {
    switch (this) {
      case SheetType.LIQUOR_PLU:
        return "Liquor PLU Sheet";
      case SheetType.MOD_DATA:
        return "Mod Data Sheet";
      default:
        return "${name.toLowerCase().capitalize!} Sheet";
    }
  }
}

// Source of truth for the sheet data.
class SheetInfo {
  SheetInfo({
    required this.type,
    this.sheet,
  });
  SheetType type;
  Sheet? sheet;
}

bool isBlankRow({required Sheet sheet, required int row}) {
  final List<Data?> rowCells = sheet.rows[row];
  for (final Data? cell in rowCells) {
    if (cell?.value == null) {
      continue;
    }
    final String cellValue = cell!.value.toString().trim().toLowerCase();
    if (cellValue.isEmpty) {
      continue;
    }
    if (cellValue == "null") {
      continue;
    }
    return false;
  }
  return true;
}

// Delay for UI, but don't do it every frame, but rather only delay at most every `interval`.
class _DelayEvery {
  _DelayEvery(this._interval);

  final Duration _interval;
  DateTime? _lastDelay;
  Future<void> delay() {
    final DateTime? lastDelay = _lastDelay;
    final DateTime now = DateTime.now();
    Duration? delta;

    if (lastDelay != null) {
      delta = now.difference(lastDelay);
    }
    if (delta == null || delta > _interval) {
      _lastDelay = now;
      return Future<void>.delayed(Duration.zero);
    }
    return Future<void>.value();
  }
}

/// Handles the creation of departments, caches them, and avoids race conditions between the threads by locking.
class _DepartmentCache {
  _DepartmentCache({required this.departmentService, required this.insertedDepartments});

  final DepartmentService departmentService;
  final RxInt insertedDepartments;
  final Map<String, Department> _departmentsCache = <String, Department>{};
  final Lock _lock = Lock();

  FutureOr<Department> _getOrCreateDepartmentImpl(String title) async {
    Department? department = _departmentsCache[title];
    if (department != null) {
      return department;
    }

    department = await departmentService.getDepartmentByTitle(title: title);
    if (department == null) {
      department = await departmentService.createDepartment(
        title: title,
        creator: CURRENT_EMPLOYEE.value.employee,
      );
      insertedDepartments.value += 1;
    }
    _departmentsCache[title] = department;
    return department;
  }

  Future<Department> getOrCreateDepartment(String title) async {
    return await _lock.synchronized(() async => _getOrCreateDepartmentImpl(title));
  }
}

class ImportItemDialogController extends GetxController {
  ImportItemDialogController(this.type);

  final ImportExportType type;

  final RxInt currStep = 0.obs;

  // Selected file path.
  final RxnString fileString = RxnString();

  // The Excel object.
  Rxn<Excel> excelObject = Rxn<Excel>();
  // {sheet name => sheet}.
  final RxMap<String, Sheet> sheetOptions = <String, Sheet>{}.obs;

  // Used for loading animation.
  final RxInt isLoading = 0.obs;
  // Used for loading animation.
  final RxInt isPopulating = 0.obs;

  // Block: Used for the progress bar.
  final Rx<SheetType> importStep = SheetType.values[0].obs;
  // Observes importStep, do not set. See `ever()` callback in `onInit()`.
  final RxInt importStepInt = SheetType.ITEM.index.obs;
  final RxInt _itemsProcessed = 0.obs;
  final RxInt _modsProcessed = 0.obs;
  final RxInt _modDataProcessed = 0.obs;
  final RxInt _liquorPluProcessed = 0.obs;
  final RxDouble importProgress = 0.0.obs;
  final RxInt isImporting = 0.obs;

  // Block: Used for the progress bar.
  final Rx<AnnotatingStep> annotatingStep = AnnotatingStep.values[0].obs;
  // Observes annotatingStep, do not set. See `ever()` callback in `onInit()`.
  final RxInt annotatingStepInt = SheetType.ITEM.index.obs;
  final RxDouble annotatingProgress = 0.0.obs;
  final RxInt isAnnotating = 0.obs;

  // Used for loading animation.
  final RxInt isSaving = 0.obs;

  // Block: Source of truth itemSheet column indices/dropdowns.
  final RxnInt selectedItemUUID = RxnInt();
  final RxnInt selectedDescription = RxnInt();
  final RxnInt selectedPrice = RxnInt();
  final RxnInt selectedUpc = RxnInt();
  final RxnInt selectedDepartment = RxnInt();
  final RxnInt selectedIsWeighted = RxnInt();
  // TODO: Add UOM back in here.
  final RxnInt selectedReceiptDesc = RxnInt();
  final RxnInt selectedDetailedDesc = RxnInt();
  final RxnInt selectedAllowEbt = RxnInt();
  final RxnInt selectedPinToTop = RxnInt();
  final RxnInt selectedPromptForPrice = RxnInt();
  final RxnInt selectedIsNegativePrice = RxnInt();
  final RxnInt selectedDefModPricing = RxnInt();
  final RxnInt selectedIsModifier = RxnInt();
  final RxnInt selectedMultiModList = RxnInt();
  final RxnInt selectedMinMod = RxnInt();
  final RxnInt selectedMaxMod = RxnInt();
  final RxnInt selectedPassDesc = RxnInt();
  final RxnInt selectedOverridePricing = RxnInt();
  final RxnInt selectedItemCount = RxnInt();
  final RxnInt selectedItemModDesc = RxnInt();

  // Block: Source of truth modTable column indices/dropdowns.
  //modTable dropdowns
  final RxnInt selectedModDesc = RxnInt();
  final RxnInt selectedModItemUUID = RxnInt();
  final RxnInt selectedModParent = RxnInt();
  final RxnInt selectedModParentUUID = RxnInt();
  final RxnInt selectedModIdx = RxnInt();
  final RxnInt selectedModPricing = RxnInt();

  // Block: Source of truth modDataTable column indices/dropdowns.
  final RxnInt selectedModDataDesc = RxnInt();
  final RxnInt selectedModDataModUUID = RxnInt();
  final RxnInt selectedModDataParent = RxnInt();
  final RxnInt selectedModDataParentUUID = RxnInt();
  final RxnInt selectedModDataSchedule = RxnInt();
  final RxnInt selectedModDataPrice = RxnInt();

  final RxnInt selectedLiquorPLU = RxnInt();
  final RxnInt selectedLiquorDesc = RxnInt();

  // Track the info of all the sheets we are interested in potentially loading.
  final List<SheetInfo> sheetInfos = <SheetInfo>[];

  // Track the info of all the sheets we are interested in potentially loading.
  final List<FormStepType> formSteps = <FormStepType>[];

  // Quick way to find proper sheet info in list
  SheetInfo? _getInfo(SheetType t) => sheetInfos.firstWhereOrNull((SheetInfo info) => info.type == t);

  // This is used for the step ImportStep.ITEMS_PROCESSING.
  Sheet? get itemTable => _getInfo(SheetType.ITEM)?.sheet;

  set itemTable(Sheet? value) {
    final SheetInfo? info = _getInfo(SheetType.ITEM);
    if (info != null) info.sheet = value;
  }

  // This is used for the step ImportStep.MODIFIERS_PROCESSING.
  Sheet? get modTable => _getInfo(SheetType.MOD)?.sheet;

  set modTable(Sheet? value) {
    final SheetInfo? info = _getInfo(SheetType.MOD);
    if (info != null) info.sheet = value;
  }

  // This is used for the step ImportStep.MOD_PARENTS_UPDATE.
  final List<Tuple2<Item, int>> _modifiedParents = <Tuple2<Item, int>>[];

  // This is used for the step ImportStep.MOD_DATA_PROCESSING.
  Sheet? get modDataTable => _getInfo(SheetType.MOD_DATA)?.sheet;

  set modDataTable(Sheet? value) {
    final SheetInfo? info = _getInfo(SheetType.MOD_DATA);
    if (info != null) info.sheet = value;
  }

  // This is used for the step ImportStep.MOD_DATA_PROCESSING.
  Sheet? get liquorPluTable => _getInfo(SheetType.LIQUOR_PLU)?.sheet;

  set liquorPluTable(Sheet? value) {
    final SheetInfo? info = _getInfo(SheetType.LIQUOR_PLU);
    if (info != null) info.sheet = value;
  }

  // Initialized in onInit().
  final List<ColumnInfo> itemColumnsList = <ColumnInfo>[];
  final List<ColumnInfo> modColumnsList = <ColumnInfo>[];
  final List<ColumnInfo> modDataColumnsList = <ColumnInfo>[];
  final List<ColumnInfo> liquorPluColumnsList = <ColumnInfo>[];

  // Items import stats.
  final RxInt insertedItems = 0.obs;
  final RxInt updatedItems = 0.obs;
  final RxInt erroredItemsCount = 0.obs;
  final RxInt warningItemsCount = 0.obs;
  final RxInt ignoredItemsCount = 0.obs;
  final RxList<Issue> itemIssues = <Issue>[].obs;
  int get issuesItemCount => erroredItemsCount.value + warningItemsCount.value;

  // Department import stats.
  final RxInt insertedDepartments = 0.obs;

  // Mod import stats.
  final RxInt updatedModParents = 0.obs;
  final RxInt erroredModsCount = 0.obs;
  final RxInt warningModsCount = 0.obs;
  final RxInt ignoredModsCount = 0.obs;
  final RxList<Issue> modIssues = <Issue>[].obs;
  int get issuesModsCount => erroredModsCount.value + warningModsCount.value;

  // Mod Data import stats.
  final RxInt updatedModData = 0.obs;
  final RxInt erroredModDataCount = 0.obs;
  final RxInt warningModDataCount = 0.obs;
  final RxInt ignoredModDataCount = 0.obs;
  final RxList<Issue> modDataIssues = <Issue>[].obs;
  int get issuesModDataCount => erroredModDataCount.value + warningModDataCount.value;

  // Liquor Plu import stats.
  final RxInt insertedPlu = 0.obs;
  final RxInt updatedPlu = 0.obs;
  final RxInt erroredPluCount = 0.obs;
  final RxInt warningPluCount = 0.obs;
  final RxInt ignoredPluCount = 0.obs;
  final RxList<Issue> pluIssues = <Issue>[].obs;
  int get issuesPluCount => erroredItemsCount.value + warningItemsCount.value;

  int get issuesCount => itemIssues.length + modIssues.length + modDataIssues.length;
  List<List<Issue>> sheetsIssues = <List<Issue>>[];

  // Issues that occur during the auto-population of the columns.
  // {sheet name => list of issues}.
  final RxMap<String, List<String>> autoPopulateIssues = <String, List<String>>{}.obs;

  // Validation keys needed to allow Form() to validate the form.
  final GlobalKey<FormState> validateConfigureItemCols = GlobalKey<FormState>();
  final GlobalKey<FormState> validateConfigureModCols = GlobalKey<FormState>();
  final GlobalKey<FormState> validateConfigureModDataCols = GlobalKey<FormState>();
  final GlobalKey<FormState> validateConfigureLiquorPluCols = GlobalKey<FormState>();

  // Services used by this class.
  final DepartmentService _departmentService = Get.find();
  final ItemService _itemService = Get.find();
  final LiquorPluDescService _liquorPluDescService = Get.find();
  final NotificationService notificationService = Get.find();

  static const int MAX_CONCURRENT = 5;

  static const ExcelColor _errorBGColor = ExcelColor.red100;
  static const ExcelColor _errorBorderColor = ExcelColor.red400;
  static const ExcelColor _warningBGColor = ExcelColor.yellow100;
  static const ExcelColor _warningBorderColor = ExcelColor.orange100;

  static const Map<ErrorLevel, ExcelColor> _issueBGColors = <ErrorLevel, ExcelColor>{
    ErrorLevel.WARNING: _warningBGColor,
    ErrorLevel.ERROR: _errorBGColor,
  };
  static const Map<ErrorLevel, ExcelColor> _issueBorderColors = <ErrorLevel, ExcelColor>{
    ErrorLevel.WARNING: _warningBorderColor,
    ErrorLevel.ERROR: _errorBorderColor,
  };
  static final Map<ErrorLevel, String> _issueBorderHexColors =
      _issueBorderColors.map((ErrorLevel e, ExcelColor c) => MapEntry<ErrorLevel, String>(e, c.colorHex));

  @override
  void onInit() {
    super.onInit();

    formSteps.add(FormStepType.SELECT_FILE);
    if (type == ImportExportType.ITEMS) {
      sheetInfos.addAll(<SheetInfo>[
        SheetInfo(type: SheetType.ITEM),
        SheetInfo(type: SheetType.MOD),
        SheetInfo(type: SheetType.MOD_DATA),
      ]);
      formSteps.addAll(<FormStepType>[
        FormStepType.CONFIGURE_ITEMS,
        FormStepType.CONFIGURE_MODIFIERS,
        FormStepType.CONFIGURE_MOD_DATA,
      ]);
      sheetsIssues.addAll(<List<Issue>>[itemIssues, modIssues, modDataIssues]);
    } else if (type == ImportExportType.LIQUOR_PLUS) {
      sheetInfos.add(SheetInfo(type: SheetType.LIQUOR_PLU));
      formSteps.add(FormStepType.CONFIGURE_LIQUOR_PLUS);
      sheetsIssues.add(pluIssues);
    }
    formSteps.addAll(<FormStepType>[
      FormStepType.CONFIRM_CHANGES,
      FormStepType.FINISH,
    ]);

    // This sets importStepInt to observe importStep, and updates it when importStep changes.
    ever(importStep, (SheetType step) {
      importStepInt.value = step.index;
    });
    ever(importStepInt, (int step) {
      importStep.value = SheetType.values[step];
    });
    // Compute importStepInt for the initial value.
    importStep.trigger(importStep.value);

    // This sets annotatingStepInt to observe annotatingStep, and updates it when annotatingStep changes.
    ever(annotatingStep, (AnnotatingStep step) {
      annotatingStepInt.value = step.index;
    });
    ever(annotatingStepInt, (int step) {
      annotatingStep.value = AnnotatingStep.values[step];
    });
    // Compute annotatingStepInt for the initial value.
    annotatingStep.trigger(annotatingStep.value);

    // If either of these change, update the import progress.
    everAll(<RxInterface<dynamic>>[
      _itemsProcessed,
      _modsProcessed,
      _modDataProcessed,
      _liquorPluProcessed,
      importStep,
    ], (_) {
      switch (importStep.value) {
        case SheetType.ITEM:
          final int total = (itemTable?.rows.length ?? 0) - 1;
          importProgress.value = total > 0 ? ((_itemsProcessed.value) / total) : 1.0;
        case SheetType.MOD:
          final int total = (modTable?.rows.length ?? 0) - 1;
          importProgress.value = total > 0 ? ((_modsProcessed.value) / total) : 1.0;
        case SheetType.MOD_DATA:
          final int total = (modDataTable?.rows.length ?? 0) - 1;
          importProgress.value = total > 0 ? ((_modDataProcessed.value) / total) : 1.0;
        case SheetType.LIQUOR_PLU:
          final int total = (liquorPluTable?.rows.length ?? 0) - 1;
          importProgress.value = total > 0 ? ((_liquorPluProcessed.value) / total) : 1.0;
      }
    });
    itemColumnsList.addAll(<ColumnInfo>[
      ColumnInfo("ItemDesc", selectedDescription, false),
      ColumnInfo("ItemUUID", selectedItemUUID, true),
      ColumnInfo("Price", selectedPrice, false),
      ColumnInfo("UPC", selectedUpc, false),
      ColumnInfo("Department", selectedDepartment, false),

      ColumnInfo("Receipt Description", selectedReceiptDesc, true),
      ColumnInfo("Detailed Description", selectedDetailedDesc, true),
      ColumnInfo("isWeighted", selectedIsWeighted, true),
      // TODO: UOM?
      ColumnInfo("allowEbt", selectedAllowEbt, true),
      ColumnInfo("pinToTop", selectedPinToTop, true),
      ColumnInfo("promptForPrice", selectedPromptForPrice, true),
      ColumnInfo("negativeItem", selectedIsNegativePrice, true),
      ColumnInfo("count", selectedItemCount, true),
      ColumnInfo("modDes", selectedItemModDesc, true),
      ColumnInfo("modMaxSel", selectedMaxMod, true),
      ColumnInfo("modMinSel", selectedMinMod, true),
      ColumnInfo("overridePricing", selectedOverridePricing, true),
      ColumnInfo("passDesc", selectedPassDesc, true),
      ColumnInfo("isModifier", selectedIsModifier, true),
      ColumnInfo("multiModLists", selectedMultiModList, true),
      ColumnInfo("defModPricing", selectedDefModPricing, true),
    ]);
    modColumnsList.addAll(<ColumnInfo>[
      ColumnInfo("ModDesc", selectedModDesc, false),
      ColumnInfo("ModItemUUID", selectedModItemUUID, true),
      ColumnInfo("ParentDesc", selectedModParent, false),
      ColumnInfo("ParentUUID", selectedModParentUUID, true),
      ColumnInfo("idx", selectedModIdx, false),
      ColumnInfo("modPricing", selectedModPricing, false),
    ]);
    modDataColumnsList.addAll(<ColumnInfo>[
      ColumnInfo("ModDesc", selectedModDataDesc, false),
      ColumnInfo("ModItemUUID", selectedModDataModUUID, true),
      ColumnInfo("ParentDesc", selectedModDataParent, false),
      ColumnInfo("ParentUUID", selectedModDataParentUUID, true),
      ColumnInfo("Schedule", selectedModDataSchedule, false),
      ColumnInfo("Price", selectedModDataPrice, false),
    ]);
    liquorPluColumnsList.addAll(<ColumnInfo>[
      ColumnInfo("PLU", selectedLiquorPLU, false),
      ColumnInfo("Description", selectedLiquorDesc, false),
    ]);
  }

  // Return a list of (normalized column name, column index) pairs.
  //
  // Normalized here means lowercase, trimmed, and without spaces, removed any parentheses e.g `Column(Type Name)` => `column`.
  // Async because we want the UI to update and it is potentially long-running.
  static Future<Map<String, int>> _discoverColumns({required Sheet sheet, required List<String> issues}) async {
    if (sheet.rows.isEmpty) {
      return <String, int>{};
    }
    final Map<String, int> columns = <String, int>{};
    final List<Data?> columnHeaders = sheet.rows[0];

    final _DelayEvery delayer = _DelayEvery(const Duration(milliseconds: 16));
    for (int colIdx = 0; colIdx < columnHeaders.length; colIdx++) {
      // This is to allow the UI to update.
      await delayer.delay();
      String? header = columnHeaders[colIdx]?.value?.toString();
      if (header != null) {
        // Remove anything between parentheses.
        header = header.replaceAll(RegExp(r"\(.*\)"), "");
        header = header.trim().toLowerCase();
      }
      if (header != null && header.isNotEmpty) {
        final int? existingColIdx = columns[header];
        if (existingColIdx != null) {
          issues.add("Duplicate column name: $header in the sheet at column index $colIdx and $existingColIdx");
          continue;
        }
        columns[header] = colIdx;
      }
    }
    return columns;
  }

  // Try and automatically populate the various column dropdowns for the user.
  void _initializeSheetOptions({
    required Map<String, Sheet> sheetOptions,
  }) {
    // {lowercase sheet name => sheet name}.
    //
    // NOTE: If there are duplicate sheets with the same name, only the last one will be used.
    final Map<String, String> sheetNames = <String, String>{};
    sheetOptions.forEach((String key, Sheet value) {
      sheetNames[key.toLowerCase()] = key;
    });

    Sheet? findSheet(String sheetName) {
      final String? foundSheetName = sheetNames[sheetName.toLowerCase()];
      if (foundSheetName != null) {
        return sheetOptions[foundSheetName];
      }
      return null;
    }

    itemTable = findSheet(SheetType.ITEM.sheetName);
    itemTable ??= findSheet("Sheet1");
    modTable = findSheet(SheetType.MOD.sheetName);
    modDataTable = findSheet(SheetType.MOD_DATA.sheetName);
    liquorPluTable = findSheet(SheetType.LIQUOR_PLU.sheetName);
  }

  void clearAllColumnDropdowns({
    required String sheetName,
    required List<ColumnInfo> columns,
  }) {
    for (final ColumnInfo column in columns) {
      column.columnIndex.value = null;
    }
    autoPopulateIssues[sheetName]?.clear();
  }

  Future<void> autoPopulateAllColumnDropdowns() async {
    try {
      isPopulating.value += 1;
      await autoPopulateColumnDropdowns(
        strict: true,
        sheet: itemTable,
        columnInfos: itemColumnsList,
        sheetName: "Items",
      );
      await autoPopulateColumnDropdowns(
        strict: true,
        sheet: modTable,
        columnInfos: modColumnsList,
        sheetName: "Mod",
      );
      await autoPopulateColumnDropdowns(
        strict: true,
        sheet: modDataTable,
        columnInfos: modDataColumnsList,
        sheetName: "Mod Data",
      );
      await autoPopulateColumnDropdowns(
        strict: true,
        sheet: liquorPluTable,
        columnInfos: liquorPluColumnsList,
        sheetName: "Liquor PLU",
      );
    } finally {
      isPopulating.value -= 1;
    }
  }

  // Async because we want the UI to update and it is potentially long-running.
  Future<void> autoPopulateColumnDropdowns({
    required bool strict,
    required Sheet? sheet,
    required List<ColumnInfo> columnInfos,
    required String sheetName,
  }) async {
    try {
      isPopulating.value += 1;
      final List<String> issues = autoPopulateIssues[sheetName] ?? <String>[];
      issues.clear();

      final List<String> missingRequiredCols = <String>[];
      void findColumn({required Map<String, int> remainingCols, required ColumnInfo columnInfo}) {
        if (remainingCols.containsKey(columnInfo.name.toLowerCase())) {
          columnInfo.columnIndex.value = remainingCols[columnInfo.name.toLowerCase()];

          remainingCols.remove(columnInfo.name.toLowerCase());
        } else {
          columnInfo.columnIndex.value = null;
          if (strict && !columnInfo.optional) {
            missingRequiredCols.add(columnInfo.name);
          }
        }
      }

      // Columns we don't want to warn the user about.
      final List<String> ignoreColumns = <String>["note", "notes", "test note", "test notes", "issue message"];

      if (sheet != null) {
        final Map<String, int> remainingCols = await _discoverColumns(
          sheet: sheet,
          issues: issues,
        );

        final _DelayEvery delayer = _DelayEvery(const Duration(milliseconds: 16));
        for (final ColumnInfo columnInfo in columnInfos) {
          // This is to allow the UI to update.
          await delayer.delay();
          findColumn(remainingCols: remainingCols, columnInfo: columnInfo);
        }

        issues.addAll(missingRequiredCols.map((String col) => "Required column $col not found in the $sheetName sheet"));
        remainingCols.removeWhere((String key, int value) => ignoreColumns.contains(key));
        if (remainingCols.isNotEmpty && strict) {
          final List<String> remainingColsList = remainingCols.values
              .map((int colIdx) => sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIdx, rowIndex: 0)).value.toString().trim())
              .toList();
          issues.add("Extra columns found in ${jsonEncode(sheetName)} sheet: ${remainingColsList.join(",")}");
        }
      }
      autoPopulateIssues[sheetName] = issues;
    } finally {
      isPopulating.value -= 1;
    }
  }

  static Future<Excel> _loadFileDataToExcel(String path) async {
    final DateTime t0 = DateTime.now();
    final Uint8List bytes = await File(path).readAsBytes();
    final DateTime t1 = DateTime.now();
    _logger.info("Time to read excel file: ${t1.difference(t0).inMilliseconds} ms");

    // NOTE: Using asynchronous for now, but it is somewhat slower, probably due to serialization/deserialization.
    // NOTE: Consider using 3rd party thread library.
    final Excel excel_ = await compute(Excel.decodeBytes, bytes);
    // NOTE: Using the synchronous version because the data transfer time is impactful.
    // final Excel excel_ = Excel.decodeBytes(bytes);
    final DateTime t2 = DateTime.now();
    _logger.info("Time to decode excel file: ${t2.difference(t1).inMilliseconds} ms");

    return excel_;
  }

  // Wait for something while allowing the UI to update.
  //
  // Without Future.delayed() the UI was not updating, even though
  // there were await calls in the loops.
  //
  // TODO: Check if this is still necessary.
  static Future<T> _waitFor<T>(Future<T> future, Duration frame) async {
    bool isDone = false;
    final Future<void> wrapped = future.then((T _) {
      isDone = true;
    }).whenComplete(() {
      isDone = true;
    }).catchError((Object error) {
      isDone = true;
      throw error;
    });
    while (!isDone) {
      // This is to allow the UI to update.
      await Future<void>.delayed(frame);
    }
    // Throw any caught errors.
    await wrapped;
    // Return any value.
    return await future;
  }

  Future<void> load(String path) async {
    try {
      isLoading.value += 1;
      excelObject.value = null;
      fileString.value = path;
      sheetOptions.value = <String, Sheet>{};

      final Excel excel_ = await _waitFor(_loadFileDataToExcel(path), const Duration(milliseconds: 16));

      sheetOptions.value = excel_.tables;

      _initializeSheetOptions(sheetOptions: sheetOptions);

      excelObject.value = excel_;
    } finally {
      isLoading.value -= 1;
    }
  }

  // Validate that the sheet selection form step is complete.
  //
  // Async because we want the UI to update and it is potentially long-running.
  Future<bool> validateSheets() async {
    if (fileString.value == null) {
      notificationService.error("No file selected");
      return false;
    }
    if (excelObject.value == null) {
      notificationService.error("No file loaded");
      return false;
    }

    final Map<SheetType, bool> missing = <SheetType, bool>{for (final SheetType s in SheetType.values) s: false};

    for (final SheetInfo sheetInfo in sheetInfos) {
      final Sheet? sheet = sheetInfo.sheet;
      if (sheet == null) {
        missing[sheetInfo.type] = true;
        continue;
      }
      if (sheet.rows.isEmpty) {
        notificationService.error("No columns in ${sheetInfo.type.sheetName}");
        return false;
      }

      final _DelayEvery delayer = _DelayEvery(const Duration(milliseconds: 16));
      int nonBlankLines = 0;
      // Note that we start from line 1, not 0, to skip the header row.
      for (int i = 1; i < sheet.rows.length; i++) {
        // This is to allow the UI to update.
        await delayer.delay();
        if (!isBlankRow(sheet: sheet, row: i)) {
          nonBlankLines++;
        }
        // We only care if there is at least one non-blank line so end early.
        if (nonBlankLines >= 2) {
          break;
        }
      }

      if (nonBlankLines < 1) {
        notificationService.error("No data in ${sheetInfo.type.sheetName}");
        return false;
      }
    }
    SheetType? missingError;
    if (missing[SheetType.MOD]! && !missing[SheetType.MOD_DATA]!) missingError = SheetType.MOD;
    if (type == ImportExportType.ITEMS && missing[SheetType.ITEM]!) missingError = SheetType.ITEM;
    if (type == ImportExportType.LIQUOR_PLUS && missing[SheetType.LIQUOR_PLU]!) missingError = SheetType.LIQUOR_PLU;
    if (missingError != null) {
      notificationService.error("Missing ${missingError.sheetName}");
      return false;
    }
    return true;
  }

  String getAnnotatedFileName() {
    String fileName = "annotated.xlsx";
    final String? loadPath = fileString.value;
    if (loadPath != null) {
      fileName = "${path.basenameWithoutExtension(loadPath)}_annotated.xlsx";
    }
    return fileName;
  }

  String? getAnnotatedFileDirectory() {
    final String? loadPath = fileString.value;
    if (loadPath != null) {
      return path.dirname(loadPath);
    }
    return null;
  }

  static Future<void> _clearExcelAnnotations({
    required Rx<AnnotatingStep> annotatingStep,
    required RxDouble annotatingProgress,
    required List<SheetInfo> sheetInfosClone,
    required Map<Sheet, int> sheet2IssueMsgCell,
    required List<int> sheetLengths,
    required _DelayEvery delayer,
  }) async {
    final DateTime t0 = DateTime.now();
    annotatingStep.value = AnnotatingStep.CLEARING_ISSUE_MESSAGES;
    annotatingProgress.value = 0.0;
    // Clear previous annotations from all sheets.
    for (int sheetIdx = 0; sheetIdx < sheetInfosClone.length; sheetIdx++) {
      // This is to allow the UI to update.
      await delayer.delay();

      final Sheet? sheet = sheetInfosClone[sheetIdx].sheet;
      if (sheet == null) {
        continue;
      }

      final int issueMsgCol = sheet2IssueMsgCell[sheet]!;

      await _clearSheetIssueMessages(
        sheet: sheet,
        annotatingProgress: annotatingProgress,
        sheetLengths: sheetLengths,
        sheetIdx: sheetIdx,
        issueMsgCol: issueMsgCol,
      );
    }
    final DateTime t1 = DateTime.now();
    _logger.info("Time to clear previous annotations: ${t1.difference(t0).inMilliseconds} ms");
  }

  // Computes a map of {sheet index => {row index => ErrorLevel}}.
  static Future<Map<int, Map<int, ErrorLevel>>> _computeSheet2Row2Levels({
    required List<SheetInfo> sheetInfosClone,
    required List<List<Issue>> sheetsIssuesClone,
    required List<int> sheetLengths,
    required List<int> sheetIssuesLengths,
    required Rx<AnnotatingStep> annotatingStep,
    required RxDouble annotatingProgress,
    required _DelayEvery delayer,
  }) async {
    final DateTime t0 = DateTime.now();
    annotatingStep.value = AnnotatingStep.COMPUTING_ROW_LEVELS;
    annotatingProgress.value = 0.0;

    final int allIssuesCount = _sum(sheetIssuesLengths);

    final Map<int, Map<int, ErrorLevel>> sheet2Row2Levels = <int, Map<int, ErrorLevel>>{};

    for (int sheetIdx = 0; sheetIdx < sheetsIssuesClone.length; sheetIdx++) {
      final SheetInfo sheetInfo = sheetInfosClone[sheetIdx];
      final List<Issue> sheetIssues = sheetsIssuesClone[sheetIdx];

      final Sheet? sheet = sheetInfo.sheet;
      if (sheet == null) {
        continue;
      }
      await delayer.delay();

      final double sheetBaseProgress = allIssuesCount > 0 ? _sum(sheetIssuesLengths.sublist(0, sheetIdx)) / allIssuesCount : 1.0;

      final Map<int, ErrorLevel> row2Levels = sheet2Row2Levels[sheetIdx] = <int, ErrorLevel>{};
      for (int issueIdx = 0; issueIdx < sheetIssues.length; issueIdx++) {
        final Issue issue = sheetIssues[issueIdx];
        final double sheetProgress = allIssuesCount > 0 ? issueIdx / allIssuesCount : 1.0;
        annotatingProgress.value = sheetBaseProgress + sheetProgress;

        ErrorLevel level = row2Levels[issue.row] ?? ErrorLevel.NONE;
        level = ErrorLevel.values[max(level.index, issue.level.index)];
        if (level == ErrorLevel.NONE) {
          continue;
        }
        row2Levels[issue.row] = level;
      }
    }
    final DateTime t1 = DateTime.now();
    _logger.info("Time to compute row levels: ${t1.difference(t0).inMilliseconds} ms");
    return sheet2Row2Levels;
  }

  // Annotate colors for all cells in the sheet.
  static Future<void> _annotateColors({
    required Rx<AnnotatingStep> annotatingStep,
    required RxDouble annotatingProgress,
    required List<SheetInfo> sheetInfosClone,
    required List<List<Issue>> sheetsIssuesClone,
    required List<int> sheetLengths,
    required Map<int, Map<int, ErrorLevel>> sheet2Row2Levels,
    required _DelayEvery delayer,
  }) async {
    final DateTime t0 = DateTime.now();
    annotatingStep.value = AnnotatingStep.ANNOTATING_COLORS;
    annotatingProgress.value = 0.0;

    final int allSheetRows = _sum(sheetLengths);

    for (int sheetIdx = 0; sheetIdx < sheetsIssuesClone.length; sheetIdx++) {
      final SheetInfo sheetInfo = sheetInfosClone[sheetIdx];

      final Sheet? sheet = sheetInfo.sheet;
      if (sheet == null) {
        continue;
      }
      final double sheetBaseProgress = allSheetRows > 0 ? _sum(sheetLengths.sublist(0, sheetIdx)) / allSheetRows : 1.0;

      final Map<int, ErrorLevel> row2Levels = sheet2Row2Levels[sheetIdx]!;

      for (int row = 1; row < sheet.rows.length; row++) {
        // This is to allow the UI to update.
        await delayer.delay();

        final double sheetProgress = allSheetRows > 0 ? row / allSheetRows : 1.0;
        annotatingProgress.value = sheetBaseProgress + sheetProgress;

        final ErrorLevel rowLevel = row2Levels[row] ?? ErrorLevel.NONE;
        for (final Data? cell in sheet.rows[row]) {
          if (cell == null) {
            continue;
          }

          CellStyle cellStyle = cell.cellStyle ?? CellStyle();

          cellStyle = cellStyle.copyWith(backgroundColorHexVal: _issueBGColors[rowLevel] ?? ExcelColor.none);
          sheet.updateCell(cell.cellIndex, cell.value, cellStyle: cellStyle);
        }
      }
    }
    final DateTime t1 = DateTime.now();
    _logger.info("Time to annotate colors: ${t1.difference(t0).inMilliseconds} ms");
  }

  static Future<void> _annotateIssues({
    required Rx<AnnotatingStep> annotatingStep,
    required RxDouble annotatingProgress,
    required List<List<Issue>> sheetsIssuesClone,
    required List<int> sheetIssuesLengths,
    required Map<Sheet, int> sheet2IssueMsgCell,
    required _DelayEvery delayer,
  }) async {
    final DateTime t0 = DateTime.now();
    annotatingStep.value = AnnotatingStep.ANNOTATING_ISSUES;
    annotatingProgress.value = 0.0;

    final int allIssuesCount = _sum(sheetIssuesLengths);

    for (int sheetIdx = 0; sheetIdx < sheetsIssuesClone.length; sheetIdx++) {
      final List<Issue> sheetIssues = sheetsIssuesClone[sheetIdx];
      for (int issueIdx = 0; issueIdx < sheetIssues.length; issueIdx++) {
        // This is to allow the UI to update.
        await delayer.delay();

        final Issue issue = sheetIssues[issueIdx];

        final double sheetBaseProgress = allIssuesCount > 0 ? _sum(sheetIssuesLengths.sublist(0, sheetIdx)) / allIssuesCount : 1.0;
        final double sheetProgress = allIssuesCount > 0 ? issueIdx / allIssuesCount : 1.0;
        annotatingProgress.value = sheetBaseProgress + sheetProgress;

        final int issueMsgCol = sheet2IssueMsgCell[issue.sheet]!;
        final Data issueMsgCell = issue.sheet.cell(
          CellIndex.indexByColumnRow(
            columnIndex: issueMsgCol,
            rowIndex: issue.row,
          ),
        );
        await _annotateIssue(
          sheet: issue.sheet,
          row: issue.row,
          col: issue.col,
          reason: issue.msg,
          level: issue.level,
          issueMsgCol: issueMsgCol,
          issueMsgCell: issueMsgCell,
        );
      }
    }

    final DateTime t1 = DateTime.now();
    _logger.info("Annotating issues took ${t1.difference(t0).inMilliseconds} ms");
  }

  // After importing, this method will export the same sheets, annotated with the issues, to a new file.
  //
  // Note: `excel_` must be an independent clone of excelObject, otherwise there can be race conditions.
  //
  // Async because we want the UI to update and it is potentially long-running.
  static Future<void> annotate({
    required Excel excelClone,
    required List<SheetInfo> sheetInfosClone,
    required List<List<Issue>> sheetsIssuesClone,
    required Rx<AnnotatingStep> annotatingStep,
    required RxDouble annotatingProgress,
    required RxInt isAnnotating,
  }) async {
    try {
      final DateTime t0 = DateTime.now();
      isAnnotating.value += 1;
      final _DelayEvery delayer = _DelayEvery(const Duration(milliseconds: 16));

      // Sanity testing:
      for (final SheetInfo sheetInfo in sheetInfosClone) {
        assert(excelClone.sheets.containsValue(sheetInfo.sheet));
      }
      // Sanity testing:
      for (final List<Issue> sheetsIssueClone in sheetsIssuesClone) {
        for (final Issue issue in sheetsIssueClone) {
          assert(excelClone.sheets.containsValue(issue.sheet));
        }
      }

      // Precompute some things.

      // The length of each sheet, useful for progress computation.
      final List<int> sheetLengths = sheetInfosClone.map((SheetInfo sheetInfo) => sheetInfo.sheet?.rows.length ?? 0).toList();

      // The number of issues for each sheet, useful for progress computation.
      final List<int> sheetIssuesLengths = sheetsIssuesClone.map((List<Issue> issues) => issues.length).toList();

      // Map each sheet to its Issue Message column.
      Map<Sheet, int> computeSheetToIssueMsgCell() {
        final Map<Sheet, int> sheetToIssueMsgCell = <Sheet, int>{};
        for (int sheetIdx = 0; sheetIdx < sheetInfosClone.length; sheetIdx++) {
          final SheetInfo sheetInfo = sheetInfosClone[sheetIdx];
          final Sheet? sheet = sheetInfo.sheet;
          if (sheet == null) {
            continue;
          }

          final int issueMsgCol = _findIssueMsgColumn(sheet);
          sheetToIssueMsgCell[sheet] = issueMsgCol;
        }
        return sheetToIssueMsgCell;
      }

      // {Sheet => Issue Message column index}.
      final Map<Sheet, int> sheet2IssueMsgCell = computeSheetToIssueMsgCell();

      // Clear previous annotations.
      await _clearExcelAnnotations(
        annotatingStep: annotatingStep,
        annotatingProgress: annotatingProgress,
        sheetInfosClone: sheetInfosClone,
        sheet2IssueMsgCell: sheet2IssueMsgCell,
        sheetLengths: sheetLengths,
        delayer: delayer,
      );

      // First collect the row levels.
      // {sheet index => {row index => ErrorLevel}}.
      final Map<int, Map<int, ErrorLevel>> sheet2Row2Levels = await _computeSheet2Row2Levels(
        sheetInfosClone: sheetInfosClone,
        sheetsIssuesClone: sheetsIssuesClone,
        sheetLengths: sheetLengths,
        sheetIssuesLengths: sheetIssuesLengths,
        annotatingStep: annotatingStep,
        annotatingProgress: annotatingProgress,
        delayer: delayer,
      );

      await _annotateColors(
        annotatingStep: annotatingStep,
        annotatingProgress: annotatingProgress,
        sheetInfosClone: sheetInfosClone,
        sheetsIssuesClone: sheetsIssuesClone,
        sheetLengths: sheetLengths,
        sheet2Row2Levels: sheet2Row2Levels,
        delayer: delayer,
      );

      await _annotateIssues(
        annotatingStep: annotatingStep,
        annotatingProgress: annotatingProgress,
        sheetsIssuesClone: sheetsIssuesClone,
        sheetIssuesLengths: sheetIssuesLengths,
        sheet2IssueMsgCell: sheet2IssueMsgCell,
        delayer: delayer,
      );

      final DateTime t1 = DateTime.now();
      _logger.info("Annotating took ${t1.difference(t0).inMilliseconds} ms");
    } finally {
      isAnnotating.value -= 1;
    }
  }

  static Uint8List _saveToBytesSync(Excel excelClone) {
    final List<int>? bytesList = excelClone.encode();
    if (bytesList == null) {
      throw Exception("Failed to serialize excel");
    }
    return Uint8List.fromList(bytesList);
  }

  static Future<Uint8List> saveToBytes(Excel excelClone) async {
    final DateTime t0 = DateTime.now();
    // NOTE: Potentially unsafe, as `excel` might be modified by the UI while this is running.
    // NOTE: Using asynchronous for now, but it is somewhat slower, probably due to serialization/deserialization.
    // NOTE: Consider using 3rd party thread library.
    final Uint8List bytes = await compute(_saveToBytesSync, excelClone);

    // NOTE: Using the synchronous version because the data transfer time is impactful.
    // final Uint8List bytes = _saveToBytesSync(excelClone);
    final DateTime t1 = DateTime.now();
    _logger.info("Time to serialize excel: ${t1.difference(t0).inMilliseconds} ms");
    return bytes;
  }

  static Excel _cloneExcelSync(Excel excel) {
    final List<int>? byteList = excel.encode();
    if (byteList == null) {
      throw Exception("Failed to clone excel file");
    }
    return Excel.decodeBytes(Uint8List.fromList(byteList));
  }

  static Future<Excel> cloneExcel(Excel excel) async {
    final DateTime t0 = DateTime.now();
    // NOTE: Using asynchronous for now, but it is somewhat slower, probably due to serialization/deserialization.
    // NOTE: Consider using 3rd party thread library.
    final Excel cloned = await _waitFor(compute(_cloneExcelSync, excel), const Duration(milliseconds: 16));

    // NOTE: Using the synchronous version because the data transfer time is impactful.
    // final Excel cloned = _cloneExcelSync(excel);
    final DateTime t1 = DateTime.now();
    _logger.info("Time to clone excel file: ${t1.difference(t0).inMilliseconds} ms");
    return cloned;
  }

  Future<Excel> getExcelObjectCopy() async {
    final Excel? excel_ = excelObject.value;
    if (excel_ == null) {
      throw Exception("No excel file loaded");
    }

    return await cloneExcel(excel_);
  }

  Future<void> saveFile(Excel excelClone, String savePath) async {
    try {
      isSaving.value += 1;

      final Uint8List bytes = await saveToBytes(excelClone);

      await _waitFor(
        File(savePath).writeAsBytes(Uint8List.fromList(bytes)),
        const Duration(milliseconds: 16),
      );
    } finally {
      isSaving.value -= 1;
    }
  }

  // Find existing, or create, a column to hold issue messages in the excel sheet.
  static int _findIssueMsgColumn(Sheet sheet) {
    const String issueMsgHeader = "Issue Message";
    final List<Data?> columnHeaders = sheet.rows[0];

    for (int col = 0; col < columnHeaders.length; col++) {
      final String? header = columnHeaders[col]?.value?.toString();
      if (header == issueMsgHeader) {
        return col;
      }
    }

    final int issueMsgCol = columnHeaders.length;
    final CellIndex headerCellIdx = CellIndex.indexByColumnRow(
      columnIndex: issueMsgCol,
      rowIndex: 0,
    );

    sheet.updateCell(
      headerCellIdx,
      TextCellValue(issueMsgHeader),
      cellStyle: CellStyle(),
    );
    return issueMsgCol;
  }

  static int _sum(List<int> list) {
    int sum = 0;
    for (final int i in list) {
      sum += i;
    }
    return sum;
  }

  // Remove all issue annotations from the sheet.
  //
  // Async because we want the UI to update and it is potentially long-running.
  static Future<void> _clearSheetIssueMessages({
    required Sheet sheet,
    required RxDouble annotatingProgress,
    required List<int> sheetLengths,
    required int sheetIdx,
    required int issueMsgCol,
  }) async {
    final DateTime t0 = DateTime.now();

    final int allSheetRows = _sum(sheetLengths);

    // Progress so far for all previous sheets.
    final double baseProgress = allSheetRows > 0 ? _sum(sheetLengths.sublist(0, sheetIdx)) / allSheetRows : 1.0;
    final double oneRowProgress = allSheetRows > 0 ? 1 / allSheetRows : 1.0;

    final _DelayEvery delayer = _DelayEvery(const Duration(milliseconds: 16));

    // 1. Clear all the issue messages in the issue message column.
    for (int i = 1; i < sheet.rows.length; i++) {
      // This is to allow the UI to update.
      await delayer.delay();

      annotatingProgress.value = baseProgress + ((i - 1) * oneRowProgress);

      final CellIndex cellIdx = CellIndex.indexByColumnRow(
        columnIndex: issueMsgCol,
        rowIndex: i,
      );
      sheet.updateCell(cellIdx, null);
    }

    final DateTime t1 = DateTime.now();
    _logger.info("Clearing issue messages took ${t1.difference(t0).inMilliseconds} ms");
  }

  // Compute the color to use for an issue message.
  // If there is an existing color, only overwrite it if the new error level is higher.
  static T _computeIssueColor<T>(T? existing, T none, ErrorLevel level, Map<ErrorLevel, T> colors) {
    if (existing == null) {
      return colors[level] ?? none;
    }

    final int existingIdx = colors.values.toList().indexOf(existing);
    if (existingIdx == -1) {
      return existing;
    }

    final ErrorLevel existingLevel = colors.keys.toList()[existingIdx];

    // If there is an existing color, only overwrite it if the new error level is higher.
    // Note: existingIdx might be -1 if the existing color is not in the list of colors.
    if (existingLevel.index > level.index) {
      return existing;
    }
    return colors[level] ?? none;
  }

  // Annotate a cell or row with an issue message.
  static Future<void> _annotateIssue({
    required Sheet sheet,
    required int row,
    required int? col,
    required String reason,
    required ErrorLevel level,
    required int issueMsgCol,
    required Data issueMsgCell,
  }) async {
    // Insert the issue message into the issue message column.
    {
      final TextCellValue? oldValue = issueMsgCell.value as TextCellValue?;
      final String? oldMsg = oldValue?.value.text;

      String newMsg = reason;
      if (oldMsg != null) {
        newMsg = "$oldMsg,\n $reason";
      }
      CellStyle style = issueMsgCell.cellStyle ?? CellStyle();

      final ExcelColor newBGColor = _computeIssueColor(
        style.backgroundColor,
        ExcelColor.none,
        level,
        _issueBGColors,
      );

      style = style.copyWith(
        textWrappingVal: TextWrapping.WrapText,
        backgroundColorHexVal: newBGColor,
      );
      final CellIndex cellIndex = CellIndex.indexByColumnRow(
        columnIndex: issueMsgCol,
        rowIndex: row,
      );
      sheet.updateCell(cellIndex, TextCellValue(newMsg), cellStyle: style);
      sheet.setColumnAutoFit(issueMsgCol);
    }

    // Mark the affected cell with a border.
    if (col != null) {
      final Data cell = sheet.cell(
        CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: row,
        ),
      );
      CellStyle style = cell.cellStyle ?? CellStyle();
      final String borderColorHex = _computeIssueColor(
        style.bottomBorder.borderColorHex,
        ExcelColor.none.colorHex,
        level,
        _issueBorderHexColors,
      );

      // If the color is to be replaced.
      if (borderColorHex != style.bottomBorder.borderColorHex) {
        final excel.Border border = excel.Border(
          borderStyle: excel.BorderStyle.Thick,
          borderColorHex: _issueBorderColors[level],
        );
        style = style.copyWith(
          bottomBorderVal: border,
          topBorderVal: border,
          leftBorderVal: border,
          rightBorderVal: border,
        );
      }

      sheet.updateCell(cell.cellIndex, cell.value, cellStyle: style);
    }
  }

  // Unified method to create an issue with uniform issue messages.
  static Issue _makeCellIssue({
    required Sheet sheet,
    required int row,
    required int? col,
    required String rowDesc,
    required String colInternalName,
    required String? reason,
    ErrorLevel level = ErrorLevel.ERROR,
  }) {
    final String colName = _getColName(sheet: sheet, row: row, col: col);
    String cellContents = "N/A";
    final String rowId = (row + 1).toString();
    String locString = "Row $rowId";
    if (col != null) {
      final String cellId = getCellId(col, row);
      locString = "Cell $cellId";
      final Data cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: row));
      final String? cellValue = cell.value?.toString();
      if (cellValue != null) {
        cellContents = cellValue;
      }
    }

    String issueMsg = "${level.name}: ${jsonEncode(colInternalName)} (${jsonEncode(colName)}) at ($locString))";
    if (reason != null) {
      issueMsg = "$issueMsg: $reason";
    }
    return Issue(
      rowDesc: rowDesc,
      cellContents: cellContents,
      columnName: colName,
      msg: issueMsg,
      rowId: (row + 1).toString(),
      sheet: sheet,
      row: row,
      col: col,
      level: level,
    );
  }

  // Unified method to create an item issue with uniform issue messages.
  static Issue _makeIssue({
    required Sheet sheet,
    required int row,
    required String? rowDesc,
    required String issueMsg,
    ErrorLevel level = ErrorLevel.ERROR,
  }) {
    return Issue(
      rowDesc: rowDesc ?? "N/A",
      cellContents: "N/A",
      columnName: "N/A",
      msg: issueMsg,
      rowId: (row + 1).toString(),
      sheet: sheet,
      row: row,
      col: null,
      level: level,
    );
  }

  static String _getColName({required Sheet sheet, required int row, required int? col}) {
    if (col == null) {
      return "N/A";
    }
    final String? colName = sheet.cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0)).value?.toString();
    if (colName == null) {
      return "N/A";
    }
    return colName.trim();
  }

  static String? _parseOptionalString({
    required Sheet sheet,
    required int r,
    required int? c,
    required String? defVal,
    required String rowDesc,
  }) {
    if (c == null) {
      return defVal;
    }
    final CellValue? value = sheet.cell(CellIndex.indexByColumnRow(columnIndex: c, rowIndex: r)).value;

    if (value == null) {
      return defVal;
    }

    String valueStr;
    if (value is DoubleCellValue) {
      // Don't use toInt() here because that may overflow.
      valueStr = value.value.toStringAsFixed(0);
    } else {
      valueStr = value.toString();
    }

    valueStr = valueStr.trim();
    if (<String>["", "null"].contains(valueStr.toLowerCase())) {
      return defVal;
    }
    return valueStr;
  }

  static bool _parseBool({
    required Sheet sheet,
    required int r,
    required int? c,
    required bool? defVal,
    required String rowDesc,
    required String colIntName,
    required List<Issue> rowIssues,
  }) {
    final String? valueStr = _parseOptionalString(
      sheet: sheet,
      r: r,
      c: c,
      defVal: null,
      rowDesc: rowDesc,
    );

    bool returnDefault() {
      if (defVal == null) {
        rowIssues.add(
          _makeCellIssue(
            sheet: sheet,
            row: r,
            col: c,
            rowDesc: rowDesc,
            colInternalName: colIntName,
            reason: "Expected a boolean value for ${jsonEncode(colIntName)}",
          ),
        );
        return false;
      }
      return defVal;
    }

    if (valueStr == null) {
      return returnDefault();
    }

    final String canonical = valueStr.toLowerCase();

    if (<String>["true", "1", "t"].contains(canonical)) {
      return true;
    } else if (<String>["false", "0", "f"].contains(canonical)) {
      return false;
    } else {
      rowIssues.add(
        _makeCellIssue(
          sheet: sheet,
          row: r,
          col: c,
          rowDesc: rowDesc,
          colInternalName: colIntName,
          reason: "Expected a boolean value for ${jsonEncode(colIntName)}",
        ),
      );
      return false;
    }
  }

  static int? _parseOptionalInt({
    required Sheet sheet,
    required int r,
    required int? c,
    required int? defVal,
    required String rowDesc,
    required String colIntName,
    required List<Issue> rowIssues,
  }) {
    final String? valueStr = _parseOptionalString(sheet: sheet, r: r, c: c, defVal: null, rowDesc: rowDesc);

    if (valueStr == null) {
      return defVal;
    }
    final int? value = int.tryParse(valueStr);
    if (value == null) {
      rowIssues.add(
        _makeCellIssue(
          sheet: sheet,
          row: r,
          col: c,
          rowDesc: rowDesc,
          colInternalName: colIntName,
          reason: "Expected an integer value for ${jsonEncode(colIntName)}",
        ),
      );
      return 0;
    }
    return value;
  }

  static int _parseInt({
    required Sheet sheet,
    required int r,
    required int? c,
    required int? defVal,
    required String rowDesc,
    required String colIntName,
    required List<Issue> rowIssues,
  }) {
    final int? value = _parseOptionalInt(sheet: sheet, r: r, c: c, defVal: defVal, rowDesc: rowDesc, colIntName: colIntName, rowIssues: rowIssues);

    if (value == null) {
      rowIssues.add(
        _makeCellIssue(
          sheet: sheet,
          row: r,
          col: c,
          rowDesc: rowDesc,
          colInternalName: colIntName,
          reason: "Expected an integer value for ${jsonEncode(colIntName)}",
        ),
      );
      return 0;
    }
    return value;
  }

  static String _parseString({
    required Sheet sheet,
    required int r,
    required int? c,
    required String? defVal,
    required String rowDesc,
    required String colInternalName,
    required List<Issue> rowIssues,
  }) {
    final String? value = _parseOptionalString(sheet: sheet, r: r, c: c, defVal: defVal, rowDesc: rowDesc);
    if (value == null) {
      rowIssues.add(
        _makeCellIssue(
          sheet: sheet,
          row: r,
          col: c,
          rowDesc: rowDesc,
          colInternalName: colInternalName,
          reason: "Expected a string value for ${jsonEncode(colInternalName)}",
        ),
      );
      return "";
    }
    return value;
  }

  // Turn an exception into an Issue, and increment the error count.
  //
  // We do this 3 times so it separated out into a method.
  static Issue _exceptionToIssue({
    required dynamic e,
    required String sheetName,
    required Sheet sheet,
    required int rowIdx,
    required String? rowDesc,
    required RxInt errorCount,
  }) {
    errorCount.value += 1;
    String issueMsg = "Unhandled error: ${jsonEncode(e.toString())}";
    if (e is OperationException) {
      // Handle GraphQL uniqueness violations special.
      if (e.graphqlErrors.isNotEmpty) {
        for (final GraphQLError error in e.graphqlErrors) {
          if (error.extensions?['code'] == 'constraint-violation') {
            issueMsg = "Duplicate (itemUUID or itemDesc for example), with error: ${jsonEncode(error.message)}";
          }
        }
      }
    }
    return _makeIssue(
      sheet: sheet,
      row: rowIdx,
      rowDesc: rowDesc,
      issueMsg: issueMsg,
    );
  }

  Future<void> import() async {
    try {
      isImporting.value += 1;
      final DateTime t0 = DateTime.now();

      _itemsProcessed.value = 0;
      _modsProcessed.value = 0;
      _modDataProcessed.value = 0;
      _liquorPluProcessed.value = 0;

      itemIssues.clear();
      insertedItems.value = 0;
      updatedItems.value = 0;
      erroredItemsCount.value = 0;
      warningItemsCount.value = 0;
      ignoredItemsCount.value = 0;

      insertedDepartments.value = 0;

      modIssues.clear();
      updatedModParents.value = 0;
      erroredModsCount.value = 0;
      warningModsCount.value = 0;
      ignoredModsCount.value = 0;

      modDataIssues.clear();
      updatedModData.value = 0;
      erroredModDataCount.value = 0;
      warningModDataCount.value = 0;
      ignoredModDataCount.value = 0;

      pluIssues.clear();
      updatedPlu.value = 0;
      erroredPluCount.value = 0;
      warningPluCount.value = 0;
      ignoredPluCount.value = 0;

      if (itemTable != null) {
        await _importItemTable(sheet: itemTable!);
      }
      if (modTable != null) {
        await _importModTable(sheet: modTable!);
      }
      if (modDataTable != null) {
        await _importModDataTable(sheet: modDataTable!);
      }
      if (liquorPluTable != null) {
        await _importLiquorPluTable(sheet: liquorPluTable!);
      }
      final DateTime t1 = DateTime.now();
      _logger.info("Import took ${t1.difference(t0).inMilliseconds} ms");
    } finally {
      isImporting.value -= 1;
    }
  }

  Future<void> _importItemTable({required Sheet sheet}) async {
    final DateTime t0 = DateTime.now();
    importStep.value = SheetType.ITEM;

    final _DepartmentCache departmentCache = _DepartmentCache(
      departmentService: _departmentService,
      insertedDepartments: insertedDepartments,
    );

    final List<Future<void> Function()> jobs = List<Future<void> Function()>.generate(
      sheet.rows.length,
      (int rowIdx) => () async {
        if (rowIdx == 0) {
          // Skip the header row.
          return;
        }
        await _importItemRow(
          sheet: sheet,
          row: rowIdx,
          itemIssues: itemIssues,
          departmentCache: departmentCache,
          erroredItemsCount: erroredItemsCount,
          warningItemsCount: warningItemsCount,
          ignoredItemsCount: ignoredItemsCount,
          insertedItems: insertedItems,
          updatedItems: updatedItems,
          ignoredItems: ignoredItemsCount,
          insertedDepartments: insertedDepartments,
          itemsProcessed: _itemsProcessed,
        );
      },
    );
    await _runConcurrently(jobs);

    itemIssues.sort((Issue a, Issue b) => a.row.compareTo(b.row));

    final DateTime t1 = DateTime.now();
    _logger.info("Imported ${_itemsProcessed.value} items in ${t1.difference(t0).inMilliseconds} ms");
  }

  Future<Item?> _getExistingItem({
    required String? uuid,
    required String? longDesc,
  }) async {
    Item? item;
    if (uuid != null) {
      item = await _itemService.getItemByUUID(uuid: uuid);
    } else if (longDesc != null) {
      item = await _itemService.getItemByLongDesc(longDesc: longDesc);
    }
    return item;
  }

  Issue _issueForMissingItem({
    required String itemName,
    required String? uuid,
    required String uuidDescInternalName,
    required int? uuidDescCol,
    required String? longDesc,
    required String longDescInternalName,
    required int? longDescCol,
    required Sheet sheet,
    required int row,
    required String rowDesc,
    required String hint,
  }) {
    int? col;
    String colInternalName = uuidDescInternalName;
    if (uuid != null) {
      colInternalName = uuidDescInternalName;
      col = selectedModDataModUUID.value;
    } else if (longDesc != null) {
      colInternalName = longDescInternalName;
      col = selectedModDataDesc.value;
    }
    return _makeCellIssue(
      sheet: sheet,
      row: row,
      col: col,
      rowDesc: rowDesc,
      colInternalName: colInternalName,
      reason: "$itemName does not exist. $hint",
    );
  }

  Future<void> _importItemRow({
    required Sheet sheet,
    required int row,
    required List<Issue> itemIssues,
    required _DepartmentCache departmentCache,
    required RxInt erroredItemsCount,
    required RxInt warningItemsCount,
    required RxInt ignoredItemsCount,
    required RxInt insertedItems,
    required RxInt updatedItems,
    required RxInt ignoredItems,
    required RxInt insertedDepartments,
    required RxInt itemsProcessed,
  }) async {
    String rowDesc = "N/A";
    final List<Issue> rowIssues = <Issue>[];
    try {
      if (isBlankRow(sheet: sheet, row: row)) {
        return;
      }
      bool isModifier = false;
      bool multiModList = false;
      bool passDesc = false;
      String? detailedDesc;
      bool overridePricing = false;
      int modMax = 0;
      int modMin = 0;
      int itemCount = -1;
      bool allowEbt = false;
      bool isWeighted = false;
      bool pinToTop = false;
      bool promptForPrice = false;
      bool isNegativePrice = false;
      int defModPricing = 0;

      final String? longDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedDescription.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      rowDesc = longDesc ?? "N/A";

      if (longDesc == null) {
        rowIssues.add(
          _makeIssue(
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            issueMsg: "A (non empty) item row was found without a description",
          ),
        );
      }

      final String? itemUUID = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedItemUUID.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      if (rowDesc == "N/A") {
        rowDesc = itemUUID ?? "N/A";
      }

      String priceStr = _parseString(
        sheet: sheet,
        r: row,
        c: selectedPrice.value,
        defVal: "0",
        rowDesc: rowDesc,
        colInternalName: "Price",
        rowIssues: rowIssues,
      );
      priceStr = priceStr.replaceAll(".", "").replaceAll("\$", "").replaceAll(",", "");
      if (int.tryParse(priceStr) == null) {
        rowIssues.add(
          _makeCellIssue(
            sheet: sheet,
            row: row,
            col: selectedPrice.value,
            rowDesc: rowDesc,
            colInternalName: "Price",
            reason: "Expected a price value",
          ),
        );
        priceStr = '0';
      }
      final int price = int.parse(priceStr);

      final String upc = _parseString(
        sheet: sheet,
        r: row,
        c: selectedUpc.value,
        defVal: "",
        rowDesc: rowDesc,
        colInternalName: "UPC",
        rowIssues: rowIssues,
      );

      final String departmentTitle = _parseString(
        sheet: sheet,
        r: row,
        c: selectedDepartment.value,
        defVal: null,
        rowDesc: rowDesc,
        colInternalName: "Department",
        rowIssues: rowIssues,
      );

      isWeighted = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedIsWeighted.value,
        defVal: false,
        rowDesc: rowDesc,
        colIntName: "Is Weighted",
        rowIssues: rowIssues,
      );

      final String receiptDesc = _parseString(
        sheet: sheet,
        r: row,
        c: selectedReceiptDesc.value,
        defVal: longDesc,
        rowDesc: rowDesc,
        colInternalName: "Receipt Description",
        rowIssues: rowIssues,
      );
      detailedDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedDetailedDesc.value,
        defVal: detailedDesc,
        rowDesc: rowDesc,
      );
      allowEbt = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedAllowEbt.value,
        defVal: allowEbt,
        rowDesc: rowDesc,
        colIntName: "Allow EBT",
        rowIssues: rowIssues,
      );
      pinToTop = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedPinToTop.value,
        defVal: pinToTop,
        rowDesc: rowDesc,
        colIntName: "Pin to Top",
        rowIssues: rowIssues,
      );
      promptForPrice = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedPromptForPrice.value,
        defVal: promptForPrice,
        rowDesc: rowDesc,
        colIntName: "Prompt for Price",
        rowIssues: rowIssues,
      );
      isNegativePrice = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedIsNegativePrice.value,
        defVal: isNegativePrice,
        rowDesc: rowDesc,
        colIntName: "Negative Price",
        rowIssues: rowIssues,
      );
      isModifier = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedIsModifier.value,
        defVal: isModifier,
        rowDesc: rowDesc,
        colIntName: "Is Modifier",
        rowIssues: rowIssues,
      );
      multiModList = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedMultiModList.value,
        defVal: multiModList,
        rowDesc: rowDesc,
        colIntName: "Multi Mod List",
        rowIssues: rowIssues,
      );
      passDesc = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedPassDesc.value,
        defVal: passDesc,
        rowDesc: rowDesc,
        colIntName: "Pass Description",
        rowIssues: rowIssues,
      );
      overridePricing = _parseBool(
        sheet: sheet,
        r: row,
        c: selectedOverridePricing.value,
        defVal: overridePricing,
        rowDesc: rowDesc,
        colIntName: "Override Pricing",
        rowIssues: rowIssues,
      );

      modMin = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedMinMod.value,
        defVal: modMin,
        rowDesc: rowDesc,
        colIntName: "Min Mod",
        rowIssues: rowIssues,
      );

      modMax = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedMaxMod.value,
        defVal: modMax,
        rowDesc: rowDesc,
        colIntName: "Max Mod",
        rowIssues: rowIssues,
      );
      itemCount = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedItemCount.value,
        defVal: itemCount,
        rowDesc: rowDesc,
        colIntName: "Item Count",
        rowIssues: rowIssues,
      );
      final String? modifierDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedItemModDesc.value,
        defVal: null,
        rowDesc: rowDesc,
      );

      defModPricing = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedDefModPricing.value,
        defVal: defModPricing,
        rowDesc: rowDesc,
        colIntName: "Default Mod Pricing",
        rowIssues: rowIssues,
      );

      final Future<Department> itemRowDepartmentTask = departmentCache.getOrCreateDepartment(departmentTitle);

      final Item? existingItem = await _getExistingItem(
        uuid: itemUUID,
        longDesc: longDesc,
      );
      Item item;
      if (existingItem != null) {
        item = existingItem.copyWith();
      } else {
        item = Item.empty();
        item.created_at = DateTime.now().toUtc();
        item.created_by = CURRENT_EMPLOYEE.value.employee;
      }

      if (itemUUID != null) {
        if (item.item.isNotEmpty && item.item != itemUUID) {
          rowIssues.add(
            _makeCellIssue(
              sheet: sheet,
              row: row,
              col: selectedItemUUID.value,
              rowDesc: rowDesc,
              colInternalName: "ItemUUID",
              reason: "Specified ItemUUID does not match existing item",
            ),
          );
        }
        item.item = itemUUID;
      }
      item.updated_by = CURRENT_EMPLOYEE.value.employee;
      item.long_desc = longDesc ?? item.long_desc;

      // TODO: Add UOM.
      item.document.UOM = 0;
      item.document.isWeighted = isWeighted;
      item.document.receiptDesc = receiptDesc;
      item.document.detailedDesc = detailedDesc;
      item.document.allowEbt = allowEbt;
      item.document.pinToTop = pinToTop;
      item.document.promptForPrice = promptForPrice;
      item.document.negativeItem = isNegativePrice;
      // TODO: In the future, we might want to do a smarter merge?
      // TODO: What about schedules here??
      item.document.pricing["S0L0C0"] = price;
      item.document.isModifier = isModifier;
      item.document.multiModLists = multiModList;
      item.document.passDesc = passDesc;
      item.document.overridePricing = overridePricing;
      item.document.modMinSel = modMin;
      item.document.modMaxSel = modMax;
      item.document.count = itemCount;
      item.document.modifierDesc = modifierDesc;
      // TODO: In the future, we might want to do a smarter merge?
      // TODO: What about schedules here??
      item.document.defModPricing = <String, int>{"S0L0C0": defModPricing};
      item.upc = upc;
      // item.department is a UUID.
      item.department = (await itemRowDepartmentTask).department;

      final bool anyErrors = rowIssues.any((Issue element) => element.level == ErrorLevel.ERROR);
      final bool anyWarnings = rowIssues.any((Issue element) => element.level == ErrorLevel.WARNING);

      // There is an error, skip the row.
      if (anyErrors) {
        erroredItemsCount.value += 1;
        return;
      }
      if (anyWarnings) {
        warningItemsCount.value += 1;
        // Warnings don't stop the import.
      }

      if (item == existingItem) {
        ignoredItemsCount.value += 1;
        return;
      }

      if (existingItem != null) {
        item = await _itemService.updateItemByUUID(
          uuid: existingItem.item,
          updated: item,
          updator: CURRENT_EMPLOYEE.value.employee,
        );
        updatedItems.value += 1;
      } else {
        item = await _itemService.createItem(
          newItem: item,
          creator: CURRENT_EMPLOYEE.value.employee,
        );
        insertedItems.value += 1;
      }
    } catch (e) {
      rowIssues.add(
        _exceptionToIssue(
          e: e,
          sheetName: "Items",
          sheet: sheet,
          rowIdx: row,
          rowDesc: rowDesc,
          errorCount: erroredItemsCount,
        ),
      );
    } finally {
      itemIssues.addAll(rowIssues);
      itemsProcessed.value += 1;
    }
  }

  Future<void> _importModRow({
    required Sheet sheet,
    required int row,
    required List<Issue> modIssues,
    required RxInt updatedModParents,
    required RxInt erroredModsCount,
    required RxInt warningModsCount,
    required RxInt ignoredModsCount,
    required RxInt modsProcessed,
  }) async {
    // Used for errors, meant to point at the most descriptive thing about the row known so far.
    String rowDesc = "N/A";
    final List<Issue> rowIssues = <Issue>[];

    try {
      if (isBlankRow(sheet: sheet, row: row)) {
        return;
      }

      final String? modDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModDesc.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      rowDesc = modDesc ?? "N/A";

      String? parentDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModParent.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      if (parentDesc != null) {
        rowDesc = "${jsonEncode(modDesc)} for ${jsonEncode(parentDesc)}";
      }

      final String? modItemUUID = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModItemUUID.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      final String? parentItemUUID = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModParentUUID.value,
        defVal: null,
        rowDesc: rowDesc,
      );

      final Future<Item?> originalParentItemTask = _getExistingItem(
        uuid: parentItemUUID,
        longDesc: parentDesc,
      );

      final Future<Item?> modAsItemTask = _getExistingItem(
        uuid: modItemUUID,
        longDesc: modDesc,
      );

      final Item? originalParentItem = await originalParentItemTask;
      if (originalParentItem != null) {
        parentDesc ??= originalParentItem.long_desc;
      }
      if (parentDesc != null) {
        rowDesc = "${jsonEncode(modDesc)} for ${jsonEncode(parentDesc)}";
      }

      final int? modIdx = _parseOptionalInt(
        sheet: sheet,
        r: row,
        c: selectedModIdx.value,
        defVal: null,
        rowDesc: rowDesc,
        colIntName: "Mod Index",
        rowIssues: rowIssues,
      );

      final int s0l0c0Pricing = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedModPricing.value,
        // It is legal to leave this empty (in the sheet), and it defaults to zero. Cole said so.
        defVal: 0,
        rowDesc: rowDesc,
        colIntName: "Mod Pricing",
        rowIssues: rowIssues,
      );

      final Item? modAsItem = await modAsItemTask;

      if (modAsItem == null) {
        rowIssues.add(
          _issueForMissingItem(
            itemName: "Mod Item",
            uuid: modItemUUID,
            uuidDescInternalName: "ModItemUUID",
            uuidDescCol: selectedModDesc.value,
            longDesc: modDesc,
            longDescInternalName: "ModDesc",
            longDescCol: selectedModDesc.value,
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            hint: "The UUIDs might be from an old export; consider re-exporting if so",
          ),
        );
      }
      if (originalParentItem == null) {
        rowIssues.add(
          _issueForMissingItem(
            itemName: "Parent Item",
            uuid: parentItemUUID,
            uuidDescInternalName: "ParentItemUUID",
            uuidDescCol: selectedModParentUUID.value,
            longDesc: parentDesc,
            longDescInternalName: "ParentDesc",
            longDescCol: selectedModParent.value,
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            hint: "The UUIDs might be from an old export; consider re-exporting if so",
          ),
        );
      }
      // Some extra error checking.
      if (modAsItem != null && modDesc != null) {
        if (modAsItem.long_desc != modDesc) {
          rowIssues.add(
            _makeCellIssue(
              sheet: sheet,
              row: row,
              col: selectedModParent.value,
              rowDesc: rowDesc,
              colInternalName: "Parent Description",
              reason: "Parent Item description does not match: The parent item's description is not the same as what is in this sheet.",
            ),
          );
        }
      }
      // Some extra error checking.
      if (originalParentItem != null && parentDesc != null) {
        if (originalParentItem.long_desc != parentDesc) {
          rowIssues.add(
            _makeCellIssue(
              sheet: sheet,
              row: row,
              col: selectedModParent.value,
              rowDesc: rowDesc,
              colInternalName: "Parent Description",
              reason: "Parent Item description does not match: The parent item's description is not the same as what is in this sheet.",
            ),
          );
        }
      }

      Item? updatedParentItem;
      if (originalParentItem != null && modAsItem != null) {
        updatedParentItem = originalParentItem.copyWith();
        ModData? modData = updatedParentItem.document.modifiers[modAsItem.item];
        modData ??= ModData(
          idx: updatedParentItem.document.modifiers.keys.length,
          pricing: <String, int>{},
        );

        if (modIdx != null) {
          modData.idx = modIdx;
        }
        modData.pricing['S0L0C0'] = s0l0c0Pricing;

        updatedParentItem.document.modifiers[modAsItem.item] = modData;
      }

      final bool anyErrors = rowIssues.any((Issue element) => element.level == ErrorLevel.ERROR);
      final bool anyWarnings = rowIssues.any((Issue element) => element.level == ErrorLevel.WARNING);

      // There is an error, skip the row.
      if (anyErrors || originalParentItem == null || updatedParentItem == null) {
        erroredModsCount.value += 1;
        return;
      }
      if (anyWarnings) {
        warningModsCount.value += 1;
        // Warnings don't stop the import.
      }

      // Nothing was updated.
      if (updatedParentItem == originalParentItem) {
        ignoredModsCount.value += 1;
        return;
      }

      updatedModParents.value += 1;
      await _itemService.updateItemByUUID(
        uuid: originalParentItem.item,
        updated: updatedParentItem,
        updator: CURRENT_EMPLOYEE.value.employee,
      );
    } catch (e) {
      rowIssues.add(
        _exceptionToIssue(
          e: e,
          sheetName: "Mod",
          sheet: sheet,
          rowIdx: row,
          rowDesc: rowDesc,
          errorCount: erroredModsCount,
        ),
      );
    } finally {
      modIssues.addAll(rowIssues);
      modsProcessed.value += 1;
    }
  }

  static Future<void> _runConcurrently(List<Future<void> Function()> jobs, {int? maxConcurrent}) async {
    maxConcurrent ??= MAX_CONCURRENT;
    final Pool pool = Pool(maxConcurrent);
    try {
      final List<Future<void>> futures = jobs.map((Future<void> Function() job) async {
        return pool.withResource(() async => await job());
      }).toList();

      await Future.wait(futures, eagerError: false);
    } finally {
      await pool.close();
    }
  }

  Future<void> _importModTable({required Sheet sheet}) async {
    final DateTime t0 = DateTime.now();
    importStep.value = SheetType.MOD;
    _modifiedParents.clear();

    final List<Future<void> Function()> jobs = List<Future<void> Function()>.generate(
      sheet.rows.length,
      (int rowIdx) => () async {
        if (rowIdx == 0) {
          // Don't process the column headers as a row.
          return;
        }
        await _importModRow(
          sheet: sheet,
          row: rowIdx,
          modIssues: modIssues,
          updatedModParents: updatedModParents,
          erroredModsCount: erroredModsCount,
          warningModsCount: warningModsCount,
          ignoredModsCount: ignoredModsCount,
          modsProcessed: _modsProcessed,
        );
      },
    );

    // Disable concurrency, because there is a race condition on updating the parent item.
    await _runConcurrently(jobs, maxConcurrent: 1);

    // Sort the modIssues by row.
    modIssues.sort((Issue a, Issue b) => a.row.compareTo(b.row));

    final DateTime t1 = DateTime.now();
    _logger.info("Imported ${_modsProcessed.value} modifiers in ${t1.difference(t0).inMilliseconds} ms");
  }

  Future<void> _importModDataTable({required Sheet sheet}) async {
    final DateTime t0 = DateTime.now();
    importStep.value = SheetType.MOD_DATA;

    final List<Future<void> Function()> jobs = List<Future<void> Function()>.generate(
      sheet.rows.length,
      (int rowIdx) => () async {
        if (rowIdx == 0) {
          // Don't process the column headers as a row.
          return;
        }
        await _importModDataRow(
          sheet: sheet,
          row: rowIdx,
          modDataIssues: modDataIssues,
          erroredModDataCount: erroredModDataCount,
          warningModDataCount: warningModDataCount,
          ignoredModDataCount: ignoredModDataCount,
          updatedModData: updatedModData,
          modDataProcessed: _modDataProcessed,
        );
      },
    );

    // Disable concurrency, because there is a race condition on updating the parent item.
    await _runConcurrently(jobs, maxConcurrent: 1);

    // Sort the modDataIssues by row.
    modDataIssues.sort((Issue a, Issue b) => a.row.compareTo(b.row));

    final DateTime t1 = DateTime.now();
    _logger.info("Imported ${_modDataProcessed.value} mod data in ${t1.difference(t0).inMilliseconds} ms");
  }

  Future<void> _importLiquorPluTable({required Sheet sheet}) async {
    final DateTime t0 = DateTime.now();
    importStep.value = SheetType.LIQUOR_PLU;

    final List<Future<void> Function()> jobs = List<Future<void> Function()>.generate(
      sheet.rows.length,
      (int rowIdx) => () async {
        if (rowIdx == 0) {
          // Don't process the column headers as a row.
          return;
        }
        await _importLiquorPluRow(
          sheet: sheet,
          row: rowIdx,
        );
      },
    );

    await _runConcurrently(jobs);

    // Sort the modDataIssues by row.
    pluIssues.sort((Issue a, Issue b) => a.row.compareTo(b.row));

    final DateTime t1 = DateTime.now();
    _logger.info("Imported ${_liquorPluProcessed.value} liquor PLUs in ${t1.difference(t0).inMilliseconds} ms");
  }

  Future<void> _importModDataRow({
    required Sheet sheet,
    required int row,
    required List<Issue> modDataIssues,
    required RxInt erroredModDataCount,
    required RxInt warningModDataCount,
    required RxInt ignoredModDataCount,
    required RxInt updatedModData,
    required RxInt modDataProcessed,
  }) async {
    // Used for errors, meant to point at the most descriptive thing about the row known so far.
    String rowDesc = "N/A";
    final List<Issue> rowIssues = <Issue>[];

    try {
      final String? modDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModDataDesc.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      rowDesc = modDesc ?? "N/A";

      if (modDesc == null) {
        if (isBlankRow(sheet: sheet, row: row)) {
          return;
        }

        rowIssues.add(
          _makeIssue(
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            issueMsg: "A (non empty) mod data sheet row was found without a description",
          ),
        );
      }

      final String? modUUID = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModDataModUUID.value,
        defVal: null,
        rowDesc: rowDesc,
      );
      rowDesc = modDesc ?? modUUID ?? "N/A";

      final String? modParentDesc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModDataParent.value,
        defVal: null,
        rowDesc: rowDesc,
      );

      final String? modParentUUID = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedModDataParentUUID.value,
        defVal: null,
        rowDesc: rowDesc,
      );

      final String schedule = _parseString(
        sheet: sheet,
        r: row,
        c: selectedModDataSchedule.value,
        defVal: null,
        rowDesc: rowDesc,
        colInternalName: "Schedule",
        rowIssues: rowIssues,
      );

      // regex: S\d+L\d+C\d+
      final RegExp regExp = RegExp(r"^S(\d+)L(\d+)C(0|1)$");

      final Match? match = regExp.firstMatch(schedule);

      if (match == null) {
        rowIssues.add(
          _makeCellIssue(
            sheet: sheet,
            row: row,
            col: selectedModDataSchedule.value,
            rowDesc: rowDesc,
            colInternalName: "Schedule",
            reason: "Invalid schedule format",
          ),
        );
      }

      // final int scheduleS = int.parse(match?.group(1) ?? "0");
      // final int scheduleL = int.parse(match?.group(2) ?? "0");
      // final int scheduleC = int.parse(match?.group(3) ?? "0");

      final int price = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedModDataPrice.value,
        defVal: 0,
        rowDesc: rowDesc,
        colIntName: "Price",
        rowIssues: rowIssues,
      );

      final Future<Item?> modItemTask = _getExistingItem(
        uuid: modUUID,
        longDesc: modDesc,
      );

      final Future<Item?> parentItemTask = _getExistingItem(
        uuid: modParentUUID,
        longDesc: modParentDesc,
      );

      final Item? modItem = await modItemTask;

      final Item? parentItem = await parentItemTask;

      if (modItem == null) {
        rowIssues.add(
          _issueForMissingItem(
            itemName: "Mod Item",
            uuid: modUUID,
            uuidDescInternalName: "ModUUID",
            uuidDescCol: selectedModDataModUUID.value,
            longDesc: modDesc,
            longDescInternalName: "ModDesc",
            longDescCol: selectedModDataDesc.value,
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            hint: "The UUIDs might be from an old export; consider re-exporting if so",
          ),
        );
      }
      if (parentItem == null) {
        rowIssues.add(
          _issueForMissingItem(
            itemName: "Parent Item",
            uuid: modParentUUID,
            uuidDescInternalName: "ParentUUID",
            uuidDescCol: selectedModDataParentUUID.value,
            longDesc: modParentDesc,
            longDescInternalName: "ParentDesc",
            longDescCol: selectedModDataParent.value,
            sheet: sheet,
            row: row,
            rowDesc: rowDesc,
            hint: "The UUIDs might be from an old export; consider re-exporting if so",
          ),
        );
      }

      ModData? modData;
      if (parentItem != null && modItem != null) {
        modData = parentItem.document.modifiers[modItem.item];
      }

      // If mod data exists for an item, warn if `overridePricing` is not set on the parent.
      if (parentItem != null && !parentItem.document.overridePricing) {
        rowIssues.add(
          _makeCellIssue(
            sheet: sheet,
            row: row,
            col: selectedModDataParentUUID.value ?? selectedModDataParent.value,
            rowDesc: rowDesc,
            colInternalName: selectedModDataParentUUID.value != null ? "ParentUUID" : "ParentDesc",
            reason: "Mod data exists for this item, but overridePricing is not set on the parent item",
            level: ErrorLevel.WARNING,
          ),
        );
      }

      final bool anyErrors = rowIssues.any((Issue element) => element.level == ErrorLevel.ERROR);
      final bool anyWarnings = rowIssues.any((Issue element) => element.level == ErrorLevel.WARNING);

      // There is an error, skip the row.
      if (anyErrors || modItem == null || parentItem == null) {
        erroredModDataCount.value += 1;
        return;
      }
      if (anyWarnings) {
        warningModDataCount.value += 1;
        // Warnings don't stop the import.
      }

      final Item updatedParentItem = parentItem.copyWith();

      modData ??= ModData(
        idx: parentItem.document.modifiers.keys.length,
        pricing: <String, int>{},
      );

      modData.pricing[schedule] = price;
      parentItem.document.modifiers[modItem.item] = modData;

      if (updatedParentItem == parentItem) {
        ignoredModDataCount.value += 1;
        return;
      }
      await _itemService.updateItemByUUID(
        uuid: parentItem.item,
        updated: parentItem,
        updator: CURRENT_EMPLOYEE.value.employee,
      );
      updatedModData.value += 1;
    } catch (e) {
      rowIssues.add(
        _exceptionToIssue(
          e: e,
          sheetName: "Mod Data",
          sheet: sheet,
          rowIdx: row,
          rowDesc: rowDesc,
          errorCount: erroredModDataCount,
        ),
      );
    } finally {
      modDataIssues.addAll(rowIssues);
      modDataProcessed.value += 1;
    }
  }

  Future<void> _importLiquorPluRow({
    required Sheet sheet,
    required int row,
  }) async {
    // Used for errors, meant to point at the most descriptive thing about the row known so far.
    final List<Issue> rowIssues = <Issue>[];

    try {
      final int plu = _parseInt(
        sheet: sheet,
        r: row,
        c: selectedLiquorPLU.value,
        defVal: null,
        rowDesc: "PLU",
        colIntName: "PLU",
        rowIssues: rowIssues,
      );

      final String? desc = _parseOptionalString(
        sheet: sheet,
        r: row,
        c: selectedLiquorDesc.value,
        defVal: null,
        rowDesc: "Description",
      );

      if (desc == null || plu < 1) {
        if (isBlankRow(sheet: sheet, row: row)) {
          return;
        }

        final String msgEnd = desc == null && plu < 1
            ? "Description and PLU"
            : plu < 1
                ? "PLU"
                : "description";

        rowIssues.add(
          _makeIssue(
            sheet: sheet,
            row: row,
            rowDesc: plu != 0 ? plu.toString() : desc ?? "N/A",
            issueMsg: "A (non empty) Liquor PLU sheet row was found without a $msgEnd",
          ),
        );
      }

      if (plu != 0 && plu < 1000) {
        rowIssues.add(
          _makeCellIssue(
            sheet: sheet,
            row: row,
            col: selectedLiquorPLU.value,
            rowDesc: plu.toString(),
            colInternalName: "PLU",
            reason: "PLU must contain 4 digits",
          ),
        );
      }

      final LiquorPluDesc? existingPlu = (await _liquorPluDescService.findExisting(plu)).match(
        (ServiceError l) => throw l.message,
        (List<LiquorPluDesc> r) => r.isEmpty ? null : r.first,
      );

      final LiquorPluDesc liquorPLuDesc = LiquorPluDesc(
        liq_ctl_plu_desc: existingPlu != null ? existingPlu.liq_ctl_plu_desc : "",
        liq_ctl_plu: plu,
        desc: desc ?? "N/A",
      );

      final bool anyErrors = rowIssues.any((Issue element) => element.level == ErrorLevel.ERROR);
      final bool anyWarnings = rowIssues.any((Issue element) => element.level == ErrorLevel.WARNING);

      // There is an error, skip the row.
      if (anyErrors) {
        erroredItemsCount.value += 1;
        return;
      }
      if (anyWarnings) {
        warningItemsCount.value += 1;
        // Warnings don't stop the import.
      }

      (await _liquorPluDescService.upsert(liquorPLuDesc)).match(
        (ServiceError l) => throw l.message,
        (LiquorPluDesc r) => null,
      );

      if (existingPlu != null) {
        updatedPlu.value += 1;
      } else {
        insertedPlu.value += 1;
      }
    } catch (e) {
      rowIssues.add(
        _exceptionToIssue(
          e: e,
          sheetName: "Liquor PLU",
          sheet: sheet,
          rowIdx: row,
          rowDesc: "PLU",
          errorCount: erroredPluCount,
        ),
      );
    } finally {
      pluIssues.addAll(rowIssues);
      _liquorPluProcessed.value += 1;
    }
  }
}

enum ErrorLevel {
  NONE,
  WARNING,
  ERROR,
}

class Issue extends Object {
  Issue({
    required this.rowDesc,
    required this.cellContents,
    required this.msg,
    required this.rowId,
    required this.columnName,
    required this.sheet,
    required this.row,
    required this.col,
    required this.level,
  });

  ErrorLevel level;
  // The contents of the "Description" field of the row. "N/A" if not applicable.
  String rowDesc;
  // A message describing the issue.
  String msg;
  // The contents of the cell that caused the issue. "N/A" if not applicable.
  String cellContents;
  // The name of the column of the cell that caused the issue. "N/A" if not applicable.
  String columnName;
  // Human-readable excel row number (1-indexed).
  String rowId;

  // The sheet that this issue came from.
  Sheet sheet;
  // The row index of the issue.
  int row;
  // The column index of the issue. Null if the issue is not cell-specific.
  int? col;

  String get cellId => _cellId();

  String _cellId() {
    final int? col_ = col;
    if (col_ == null) {
      return "N/A";
    }
    return getCellId(col_, row);
  }
}
