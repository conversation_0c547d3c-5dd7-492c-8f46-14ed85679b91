import 'dart:async';

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/enums/import_export_type.dart';
import 'package:backoffice/app/global_widgets/custom_dropdown.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/form_stepper.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/loading_widget/dialog.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/modules/Items/dialogs/import_item/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:lottie/lottie.dart';

final Logger _logger = Logger('ImportItemDialog');

class ImportItemDialog extends GetView<ImportItemDialogController> {
  const ImportItemDialog(this.type);

  final ImportExportType type;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ImportItemDialogController>(
      init: ImportItemDialogController(type),
      builder: (ImportItemDialogController controller) {
        return Scaffold(
          body: Column(
            children: <Widget>[
              if (type == ImportExportType.LIQUOR_PLUS)
                Header(
                  title: "",
                  leftButton: DialogButton(
                    buttonType: EDialogButtonType.BACK,
                    onTapped: () async {
                      await Get.offNamed(
                        AppRoutes.ITEMS_LIQUOR_PLU,
                        id: AppRoutes.id,
                      );
                    },
                  ),
                ),
              Obx(
                () => Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(24),
                          child: FormStepper(
                            currStep: controller.currStep,
                            allowFinishedPrevious: true,
                            children: <FormStep>[
                              FormStep(
                                stepNum: FormStepType.SELECT_FILE.index,
                                label: "Select File",
                                isActive: controller.formSteps[controller.currStep.value] == FormStepType.SELECT_FILE,
                                validateStep: () async {
                                  return await validateSheets();
                                },
                                onStepNext: () async {
                                  if (controller.isPopulating.value > 0 || controller.isLoading.value > 0 || controller.excelObject.value == null) {
                                    return;
                                  }
                                  await autoPopulateAllColumnDropdowns();
                                },
                                child: Obx(() => buildSelectFilePage()),
                              ),
                              if (type == ImportExportType.ITEMS) ...<FormStep>[
                                buildConfigureTableFormStep(
                                  step: FormStepType.CONFIGURE_ITEMS,
                                  validateKey: controller.validateConfigureItemCols,
                                  sheet: controller.itemTable,
                                  sheetName: "Items",
                                  columns: controller.itemColumnsList,
                                  exampeRowWiget: buildExampleRow(sheet: controller.itemTable, columns: controller.itemColumnsList),
                                ),
                                buildConfigureTableFormStep(
                                  step: FormStepType.CONFIGURE_MODIFIERS,
                                  validateKey: controller.validateConfigureModCols,
                                  sheet: controller.modTable,
                                  sheetName: "Mod",
                                  columns: controller.modColumnsList,
                                  exampeRowWiget: buildExampleRow(sheet: controller.modTable, columns: controller.modColumnsList),
                                ),
                                buildConfigureTableFormStep(
                                  step: FormStepType.CONFIGURE_MOD_DATA,
                                  validateKey: controller.validateConfigureModDataCols,
                                  sheet: controller.modDataTable,
                                  sheetName: "Mod Data",
                                  columns: controller.modDataColumnsList,
                                  exampeRowWiget: buildExampleRow(sheet: controller.modDataTable, columns: controller.modDataColumnsList),
                                ),
                              ],
                              if (type == ImportExportType.LIQUOR_PLUS)
                                buildConfigureTableFormStep(
                                  step: FormStepType.CONFIGURE_LIQUOR_PLUS,
                                  validateKey: controller.validateConfigureLiquorPluCols,
                                  sheet: controller.liquorPluTable,
                                  sheetName: "Liquor PLU",
                                  columns: controller.liquorPluColumnsList,
                                  exampeRowWiget: buildExampleRow(sheet: controller.liquorPluTable, columns: controller.liquorPluColumnsList),
                                ),
                              FormStep(
                                stepNum: FormStepType.CONFIRM_CHANGES.index,
                                label: "Confirm Changes",
                                isActive: controller.formSteps[controller.currStep.value] == FormStepType.CONFIRM_CHANGES,
                                validateStep: () async {
                                  if (controller.isImporting.value > 0) {
                                    return false;
                                  }
                                  await importItems();

                                  return true;
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(24),
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(maxHeight: 300, maxWidth: Get.width),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: <Widget>[
                                        if (controller.isImporting.value > 0)
                                          Column(
                                            children: <Widget>[
                                              const Text(
                                                "Saving your changes...",
                                                style: TextStyle(
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.w400,
                                                  color: R2Colors.neutral600,
                                                ),
                                              ),
                                              Text(
                                                "Step: ${controller.importStep.value.name}",
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  color: R2Colors.neutral600,
                                                ),
                                              ),
                                            ],
                                          )
                                        else
                                          const Text(
                                            'Please make sure all of your item information looks correct on the table below',
                                          ),
                                        if (controller.formSteps[controller.currStep.value] == FormStepType.CONFIRM_CHANGES)
                                          Expanded(
                                            flex: 6,
                                            child: SingleChildScrollView(
                                              child: Obx(
                                                () => SizedBox(
                                                  width: 900,
                                                  child: controller.isImporting.value > 0
                                                      ? Center(
                                                          child: Column(
                                                            children: <Widget>[
                                                              LoadingWidget(
                                                                loadingProgress: controller.importProgress,
                                                                loadingStep: controller.importStepInt,
                                                                loadingSteps: SheetType.values.length,
                                                              ),
                                                            ],
                                                          ),
                                                        )
                                                      : Column(
                                                          children: <Widget>[
                                                            buildExampleTable(
                                                              sheet: controller.itemTable,
                                                              columns: controller.itemColumnsList,
                                                              rows: 3,
                                                            ),
                                                            buildExampleTable(
                                                              sheet: controller.modTable,
                                                              columns: controller.modColumnsList,
                                                              rows: 3,
                                                            ),
                                                            buildExampleTable(
                                                              sheet: controller.modDataTable,
                                                              columns: controller.modDataColumnsList,
                                                              rows: 3,
                                                            ),
                                                            buildExampleTable(
                                                              sheet: controller.liquorPluTable,
                                                              columns: controller.liquorPluColumnsList,
                                                              rows: 3,
                                                            ),
                                                          ],
                                                        ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              FormStep(
                                stepNum: FormStepType.FINISH.index,
                                label: "Finish",
                                isActive: controller.formSteps[controller.currStep.value] == FormStepType.FINISH,
                                onStepNext: () async {
                                  type == ImportExportType.ITEMS
                                      ? await Get.offNamed(
                                          AppRoutes.ITEMS,
                                          id: AppRoutes.id,
                                        )
                                      : await Get.offNamed(
                                          AppRoutes.ITEMS_LIQUOR_PLU,
                                          id: AppRoutes.id,
                                        );
                                },
                                child: Obx(
                                  () => Padding(
                                    padding: const EdgeInsets.all(10),
                                    child: SingleChildScrollView(
                                      child: SizedBox(
                                        height: Get.height * 0.8 - 10 * 2,
                                        child: Column(
                                          children: <Widget>[
                                            Padding(
                                              padding: const EdgeInsets.all(10),
                                              child: FaIcon(
                                                controller.issuesCount == 0 ? FontAwesomeIcons.circleCheck : FontAwesomeIcons.triangleExclamation,
                                                color: controller.issuesCount == 0 ? const Color.fromRGBO(60, 214, 127, 1) : Colors.yellow[600],
                                                size: 50,
                                              ),
                                            ),
                                            Flexible(
                                              flex: 2,
                                              child: Padding(
                                                padding: const EdgeInsets.only(top: 10, bottom: 20),
                                                child: DialogButton(
                                                  buttonType: EDialogButtonType.AFFIRMATIVE,
                                                  buttonText: "Save annotated imported XLSX",
                                                  disabled: controller.isSaving.value > 0,
                                                  onTapped: saveAnnotated,
                                                ),
                                              ),
                                            ),
                                            if (controller.isAnnotating.value > 0)
                                              Center(
                                                child: Column(
                                                  children: <Widget>[
                                                    const Text(
                                                      "Building annotated XSLX...",
                                                      style: TextStyle(
                                                        fontSize: 22,
                                                        fontWeight: FontWeight.w400,
                                                        color: R2Colors.neutral600,
                                                      ),
                                                    ),
                                                    Text(
                                                      "Step: ${controller.annotatingStep.value.name}",
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w400,
                                                        color: R2Colors.neutral600,
                                                      ),
                                                    ),
                                                    LoadingWidget(
                                                      loadingProgress: controller.annotatingProgress,
                                                      loadingStep: controller.annotatingStepInt,
                                                      loadingSteps: AnnotatingStep.values.length,
                                                    ),
                                                  ],
                                                ),
                                              )
                                            else if (controller.isSaving.value > 0)
                                              Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: <Widget>[
                                                  const Text(
                                                    "Saving annotated file...",
                                                    style: TextStyle(
                                                      fontSize: 22,
                                                      fontWeight: FontWeight.w400,
                                                      color: R2Colors.neutral600,
                                                    ),
                                                  ),
                                                  Lottie.asset(
                                                    'lib/assets/lottie/squares.json',
                                                    height: 100,
                                                  ),
                                                ],
                                              )
                                            else
                                              SizedBox(
                                                child: Card(
                                                  elevation: 0,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(20), // if you need this
                                                    side: BorderSide(
                                                      color: Colors.grey.withOpacity(0.2),
                                                      width: 2,
                                                    ),
                                                  ),
                                                  color: R2Colors.neutral100,
                                                  child: Padding(
                                                    padding: const EdgeInsets.all(20),
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: <Widget>[
                                                        const Text(
                                                          "Import Result",
                                                          style: TextStyle(fontSize: 24),
                                                        ),
                                                        const Divider(),
                                                        buildStats(),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildSelectFilePage() {
    if (controller.isLoading.value > 0 || controller.isPopulating.value > 0) {
      String actionStr = "N/A";
      if (controller.isLoading.value > 0) {
        actionStr = "Loading file...";
      } else if (controller.isPopulating.value > 0) {
        actionStr = "Populating sheet information...";
      }
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            actionStr,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w400,
              color: R2Colors.neutral600,
            ),
          ),
          Lottie.asset(
            'lib/assets/lottie/squares.json',
            height: 100,
          ),
        ],
      );
    }

    return Flex(
      direction: Axis.vertical,
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        if (controller.fileString.value != null)
          Flexible(
            child: Flex(
              mainAxisAlignment: MainAxisAlignment.center,
              direction: Axis.vertical,
              children: <Text>[
                const Text("You have selected"),
                Text(
                  "'${controller.fileString.value}'",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
                const Text("to load."),
              ],
            ),
          ),
        Flexible(
          flex: 2,
          child: Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 20),
            child: DialogButton(
              buttonType: EDialogButtonType.AFFIRMATIVE,
              buttonText: controller.fileString.value != null ? "Select a new file" : "Select a file",
              onTapped: load,
            ),
          ),
        ),
        if (controller.excelObject.value != null)
          Flexible(
            child: Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.center,
              children: buildSheetDropdowns(controller.sheetInfos, controller.sheetOptions),
            ),
          ),
      ],
    );
  }

  // Helper function to build the dropdowns for the sheet selection.
  //
  // Note: this should be called in a Obx() wrapper, as it depends on
  // dynamic data.
  static List<Widget> buildSheetDropdowns(List<SheetInfo> sheetInfos, Map<String, Sheet> sheetOptions) {
    final List<DropdownMenuItem<Sheet?>> options = <DropdownMenuItem<Sheet?>>[];

    options.add(
      const DropdownMenuItem<Sheet?>(
        child: Text("none"),
      ),
    );
    options.addAll(
      sheetOptions
          .map((String sheetName, Sheet value) {
            return MapEntry<String, DropdownMenuItem<Sheet?>>(
              sheetName,
              DropdownMenuItem<Sheet?>(
                value: value,
                child: Text(sheetName),
              ),
            );
          })
          .values
          .toList(),
    );
    final List<Widget> sheetDropdowns = <Widget>[];
    for (final SheetInfo sheetInfo in sheetInfos) {
      sheetDropdowns.add(
        Flexible(
          child: Flex(
            direction: Axis.vertical,
            children: <Widget>[
              Flexible(child: Text(sheetInfo.type.sheetName)),
              SizedBox(
                width: 200,
                child: CustomDropdown<Sheet?>(
                  items: options,
                  value: sheetInfo.sheet,
                  onChanged: (Sheet? newValue) {
                    sheetInfo.sheet = newValue;
                  },
                ),
              ),
            ],
          ),
        ),
      );
    }
    return sheetDropdowns;
  }

  // Helper function to build the dropdowns for the columns selection.
  //
  // Note: this should be called in a Obx() wrapper, as it depends on
  // dynamic data.
  static Table buildColumnDropdowns({
    required Sheet sheet,
    required List<ColumnInfo> columns,
    required bool optional,
    required int displayColumns,
  }) {
    final List<ColumnInfo> relevantColumns = columns.where((ColumnInfo column) => column.optional == optional).toList();

    final List<Data?> sheetHeaderRow = sheet.rows.isNotEmpty ? sheet.rows.first : <Data>[];
    List<String> sheetColNames = sheetHeaderRow.map<String>((Data? data) => data?.value.toString().trim() ?? "").toList();
    sheetColNames = sheetColNames.where((String name) => name.isNotEmpty).toList();

    final int displayRows = (relevantColumns.length ~/ displayColumns) + (relevantColumns.length % displayColumns == 0 ? 0 : 1);
    final List<List<TableCell>> rows = List<List<TableCell>>.generate(displayRows, (_) => <TableCell>[]);
    for (int displayIdx = 0; displayIdx < relevantColumns.length; ++displayIdx) {
      final ColumnInfo columnInfo = relevantColumns[displayIdx];
      final List<DropdownMenuItem<int?>> options = <DropdownMenuItem<int?>>[];

      options.add(
        const DropdownMenuItem<int?>(
          child: Text("none"),
        ),
      );
      options.addAll(
        List<DropdownMenuItem<int?>>.generate(
          sheetColNames.length,
          (int i) => DropdownMenuItem<int?>(
            value: i,
            child: Text(
              sheetColNames[i],
            ),
          ),
        ),
      );

      final int displayRowIdx = displayIdx ~/ displayColumns;
      final List<TableCell> row = rows[displayRowIdx];
      row.add(
        TableCell(
          child: Column(
            children: <Widget>[
              Text(
                "which column Designates '${columnInfo.name}'?",
                textAlign: TextAlign.center,
              ),
              CustomDropdown<int?>(
                validator: (dynamic selected) {
                  if (columnInfo.columnIndex.value == null && !columnInfo.optional) {
                    return "Please select a column";
                  }
                  return null;
                },
                hint: columnInfo.optional ? 'optional' : 'required',
                value: columnInfo.columnIndex.value,
                onChanged: (int? newValue) {
                  columnInfo.columnIndex.value = newValue;
                },
                items: options,
              ),
            ],
          ),
        ),
      );
    }

    void fillInMissingCells(List<TableCell> cells, int displayColumns) {
      final int missingCells = displayColumns - cells.length;
      for (int i = 0; i < missingCells; ++i) {
        cells.add(
          TableCell(
            child: Container(),
          ),
        );
      }
      assert(cells.length == displayColumns);
    }

    final List<TableCell> lastRow = rows.last;
    fillInMissingCells(lastRow, displayColumns);

    final List<TableRow> tableRows = rows.map((List<TableCell> row) {
      return TableRow(
        children: row,
      );
    }).toList();

    final List<TableColumnWidth> columnWidths = List<TableColumnWidth>.generate(displayColumns, (_) => const FlexColumnWidth());
    return Table(
      border: TableBorder.all(color: const Color.fromARGB(0, 0, 0, 0)),
      columnWidths: columnWidths.asMap(),
      children: tableRows,
    );
  }

  Future<void> _onTap(String name, RxList<Issue>? issues) async {
    if (issues != null && issues.isNotEmpty) {
      final Rx<Widget> table = Rx<Widget>(
        Lottie.asset(
          'lib/assets/lottie/squares.json',
          height: 100,
        ),
      );

      unawaited(
        Get.defaultDialog(
          contentPadding: const EdgeInsets.all(2),
          title: "$name rows",
          titleStyle: const TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
          actions: <DialogButton>[
            DialogButton(
              buttonType: EDialogButtonType.ADD,
              onTapped: () {
                Get.back();
              },
              buttonText: "Close",
            ),
          ],
          content: SizedBox(
            height: Get.height * .5,
            width: Get.width * .9,
            child: Column(
              children: <Widget>[
                const Center(
                  child: Text(
                    "(Horizontally Drag/Scroll overflowing cells)",
                    style: TextStyle(fontSize: 9),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Obx(() {
                      return table.value;
                    }),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
      table.value = await buildIssuesTable(issues, controller.notificationService);
    }
  }

  // Helper function to build the Import Results widget.
  //
  // Note: this should be called in a Obx() wrapper, as it depends on
  // dynamic data.
  List<TableCell> buildStatsEntry({
    required String name,
    required int value,
    required RxList<Issue>? issues,
  }) {
    return <TableCell>[
      TableCell(
        verticalAlignment: TableCellVerticalAlignment.middle,
        child: ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 50),
          child: Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: (issues != null && issues.isNotEmpty) ? () async => _onTap(name, issues) : null,
              child: Text(
                "$name:",
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
        ),
      ),
      TableCell(
        verticalAlignment: TableCellVerticalAlignment.middle,
        child: InkWell(
          onTap: (issues != null && issues.isNotEmpty) ? () async => _onTap(name, issues) : null,
          child: Center(
            child: Text(
              Helpers.formatWholeNumber(value),
              style: const TextStyle(fontSize: 11),
            ),
          ),
        ),
      ),
    ];
  }

  TableCell buildStatsEye({required String name, required RxList<Issue>? issues}) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: InkWell(
        onTap: (issues != null && issues.isNotEmpty) ? () async => _onTap(name, issues) : null,
        child: Center(
          child: FaIcon(
            FontAwesomeIcons.eye,
            size: 14,
            color: (issues != null && issues.isNotEmpty) ? R2Colors.primary500 : Colors.transparent,
          ),
        ),
      ),
    );
  }

  static Future<Widget> buildIssuesTable(
    RxList<Issue> issues,
    NotificationService notificationService,
  ) async {
    Widget buildCell(int flexPoints, Color? color, String contents) {
      return Expanded(
        flex: flexPoints,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: const EdgeInsets.only(left: 6, top: 3, bottom: 3),
            child: Text(
              contents,
              style: TextStyle(fontSize: 10, color: color),
            ),
          ),
        ),
      );
    }

    const int LOCATION_FLEX = 2;
    const int ROW_DESC_FLEX = 2;
    const int CELL_CONTENTS_FLEX = 2;
    const int ISSUE_MSG_FLEX = 6;

    Widget itemBuilder(BuildContext context, int index) {
      final Issue issue = issues[index];
      final String location = issue.cellId != "N/A" ? "Cell ${issue.cellId}|${issue.columnName}" : "${issue.rowId}|${issue.columnName}";
      return Row(
        children: <Widget>[
          buildCell(LOCATION_FLEX, null, location),
          buildCell(ROW_DESC_FLEX, null, issue.rowDesc),
          buildCell(CELL_CONTENTS_FLEX, Colors.red, issue.cellContents),
          buildCell(ISSUE_MSG_FLEX, null, issue.msg),
        ],
      );
    }

    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            buildCell(LOCATION_FLEX, null, "Location|Column Name"),
            buildCell(ROW_DESC_FLEX, null, "rowDesc"),
            buildCell(CELL_CONTENTS_FLEX, null, "Cell Contents"),
            buildCell(ISSUE_MSG_FLEX, null, "Issue"),
          ],
        ),
        const Divider(),
        Expanded(
          child: ListView.builder(
            itemBuilder: itemBuilder,
            itemCount: issues.length,
          ),
        ),
      ],
    );
  }

  Widget buildStats() {
    List<TableCell> buildEmptyEntry() {
      return <TableCell>[
        TableCell(
          child: Container(),
        ),
        TableCell(
          child: Container(),
        ),
      ];
    }

    return Table(
      border: TableBorder.all(
        color: const Color.fromARGB(25, 0, 0, 0),
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: const <int, TableColumnWidth>{
        0: FixedColumnWidth(115),
        1: FlexColumnWidth(),
        2: FixedColumnWidth(115),
        3: FlexColumnWidth(),
        4: FixedColumnWidth(115),
        5: FlexColumnWidth(),
        6: FixedColumnWidth(105),
        7: FlexColumnWidth(),
        8: FixedColumnWidth(25),
      },
      children: <TableRow>[
        if (type == ImportExportType.ITEMS) ...<TableRow>[
          TableRow(
            children: <Widget>[
              ...buildStatsEntry(name: "Items: New", value: controller.insertedItems.value, issues: null),
              ...buildStatsEntry(name: "Items: Updated", value: controller.updatedItems.value, issues: null),
              ...buildStatsEntry(name: "Items: Identical", value: controller.ignoredItemsCount.value, issues: null),
              ...buildStatsEntry(name: "Items: Issues", value: controller.issuesItemCount, issues: controller.itemIssues),
              buildStatsEye(name: "Items", issues: controller.itemIssues),
            ],
          ),
          TableRow(
            children: <Widget>[
              ...buildStatsEntry(name: "Departments: New", value: controller.insertedDepartments.value, issues: null),
              ...buildEmptyEntry(),
              ...buildEmptyEntry(),
              ...buildEmptyEntry(),
              buildStatsEye(name: "Departments", issues: null),
            ],
          ),
          TableRow(
            children: <Widget>[
              ...buildEmptyEntry(),
              ...buildStatsEntry(name: "Mods: Updated (Parents)", value: controller.updatedModParents.value, issues: null),
              ...buildStatsEntry(name: "Mods: Identical", value: controller.ignoredModsCount.value, issues: null),
              ...buildStatsEntry(name: "Mods: Issues", value: controller.issuesModsCount, issues: controller.modIssues),
              buildStatsEye(name: "Mods", issues: controller.modIssues),
            ],
          ),
          TableRow(
            children: <Widget>[
              ...buildEmptyEntry(),
              ...buildStatsEntry(name: "Mod Data: Updated (Parents) ", value: controller.updatedModData.value, issues: null),
              ...buildStatsEntry(name: "Mod Data: Identical", value: controller.ignoredModDataCount.value, issues: null),
              ...buildStatsEntry(name: "Mod Data: Issues", value: controller.issuesModDataCount, issues: controller.modDataIssues),
              buildStatsEye(name: "Mod Data", issues: controller.modDataIssues),
            ],
          ),
        ] else if (type == ImportExportType.LIQUOR_PLUS)
          TableRow(
            children: <Widget>[
              ...buildStatsEntry(name: "Liquor PLUs: New", value: controller.insertedPlu.value, issues: null),
              ...buildStatsEntry(name: "Liquor PLUs: Updated", value: controller.updatedPlu.value, issues: null),
              ...buildStatsEntry(name: "Liquor PLUs: Identical", value: controller.ignoredPluCount.value, issues: null),
              ...buildStatsEntry(name: "Liquor PLUs: Issues", value: controller.issuesPluCount, issues: controller.pluIssues),
              buildStatsEye(name: "Liquor PLUs", issues: controller.pluIssues),
            ],
          ),
      ],
    );
  }

  Widget buildConfigureTableAutoPopulateIssues({required String sheetName}) {
    final List<String> issues = controller.autoPopulateIssues[sheetName] ?? <String>[];
    // Keep the widget approximately the same size so UI doesn't pop around.
    for (int i = issues.length; i < 2; ++i) {
      issues.add("");
    }
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        side: const BorderSide(color: R2Colors.neutral200),
        borderRadius: BorderRadius.circular(10),
      ),
      color: R2Colors.primary100,
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              "Auto Populate Issues",
              style: TextStyle(fontSize: 16, color: R2Colors.primary500),
            ),
            const Divider(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: issues.map((String issue) {
                return Text(
                  issue,
                  style: const TextStyle(fontSize: 14, color: Colors.red),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  FormStep buildConfigureTableFormStep({
    required FormStepType step,
    required GlobalKey<FormState> validateKey,
    required Sheet? sheet,
    required String sheetName,
    required List<ColumnInfo> columns,
    required Widget exampeRowWiget,
  }) {
    return FormStep(
      stepNum: step.index,
      label: "Configure $sheetName Sheet",
      isActive: controller.formSteps[controller.currStep.value] == step,
      validateStep: () async {
        return validateKey.currentState!.validate();
      },
      child: Form(
        key: validateKey,
        child: () {
          if (controller.formSteps[controller.currStep.value] != step) {
            return Container();
          }
          if (sheet == null) {
            return const Text("No sheet to configure");
          }
          return SizedBox(
            height: Get.height,
            width: Get.width,
            child: SingleChildScrollView(
              clipBehavior: Clip.antiAlias,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  buildConfigureTableAutoPopulateIssues(sheetName: sheetName),
                  Padding(
                    padding: const EdgeInsets.only(top: 20, bottom: 15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        MenuGroupLabel("Excel Row Example"),
                        Card(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(color: R2Colors.neutral200),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          color: R2Colors.neutral100,
                          child: SingleChildScrollView(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: exampeRowWiget,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      TextButton(
                        onPressed: () {
                          clearAllColumnDropdowns(
                            sheetName: sheetName,
                            columns: columns,
                          );
                          for (final ColumnInfo column in columns) {
                            column.columnIndex.value = null;
                          }
                          controller.autoPopulateIssues[sheetName]?.clear();
                        },
                        style: ButtonStyle(
                          alignment: Alignment.bottomCenter,
                          backgroundColor: MaterialStateProperty.all(Colors.transparent),
                        ),
                        child: const Text(
                          "(Clear)",
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: R2Colors.primary400),
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          await autoPopulateColumnDropdowns(
                            strict: true,
                            sheet: sheet,
                            columnInfos: columns,
                            sheetName: sheetName,
                          );
                        },
                        style: ButtonStyle(
                          alignment: Alignment.bottomCenter,
                          backgroundColor: MaterialStateProperty.all(Colors.transparent),
                        ),
                        child: const Text(
                          "(Auto Populate)",
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: R2Colors.primary400),
                        ),
                      ),
                    ],
                  ),
                  MenuGroupLabel("Required Column Mappings"),
                  Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      side: const BorderSide(color: R2Colors.neutral200),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    color: R2Colors.neutral100,
                    child: Padding(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          buildColumnDropdowns(
                            sheet: sheet,
                            columns: columns,
                            optional: false,
                            displayColumns: 4,
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (columns.any((ColumnInfo c) => c.optional))
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            MenuGroupLabel("Optional Column Mappings"),
                          ],
                        ),
                        Card(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            side: const BorderSide(color: R2Colors.neutral200),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          color: R2Colors.neutral100,
                          child: Padding(
                            padding: const EdgeInsets.all(15),
                            child: buildColumnDropdowns(
                              sheet: sheet,
                              columns: columns,
                              optional: true,
                              displayColumns: 4,
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            // ),
          );
        }(),
      ),
    );
  }

  // Helper function to show an example row of the data.
  static Widget buildExampleRow({required Sheet? sheet, required List<ColumnInfo> columns}) {
    return buildExampleTable(sheet: sheet, columns: columns, rows: 1);
  }

  static Widget buildExampleTable({
    required Sheet? sheet,
    required List<ColumnInfo> columns,
    required int rows,
  }) {
    if (sheet == null) {
      return Container();
    }
    final List<ColumnInfo> metaRequiredCols = columns.where((ColumnInfo column) => column.optional == false).toList();

    final List<DataColumn> uiCols = <DataColumn>[];
    for (final ColumnInfo column in metaRequiredCols) {
      final DataColumn newColumnEntry = DataColumn(
        label: Padding(
          padding: const EdgeInsets.only(
            left: 40,
            right: 40,
          ),
          child: Center(
            child: Text(
              column.name,
              textAlign: TextAlign.left,
            ),
          ),
        ),
      );
      uiCols.add(newColumnEntry);
    }

    final List<DataRow> uiRows = <DataRow>[];

    for (int row = 1; row < sheet.maxRows && uiRows.length < rows; ++row) {
      if (isBlankRow(sheet: sheet, row: row)) {
        continue;
      }
      final List<Data?> dataRow = sheet.row(row);

      final List<DataCell> uiCells = <DataCell>[];
      for (final ColumnInfo column in metaRequiredCols) {
        String cellValue = "N/A";
        final int? colIdx = column.columnIndex.value;

        if (colIdx != null) {
          final Data? data = dataRow[colIdx];
          if (data != null) {
            cellValue = data.value.toString();
          }
        }
        uiCells.add(
          DataCell(
            Center(
              child: Text(
                cellValue,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      }

      uiRows.add(
        DataRow(
          cells: uiCells,
        ),
      );
    }

    if (uiRows.isEmpty) {
      return Container();
    }

    return DataTable(
      columnSpacing: 0,
      columns: uiCols,
      rows: uiRows,
    );
  }

  Future<bool> validateSheets() async {
    try {
      return await controller.validateSheets();
    } catch (e) {
      controller.notificationService.error("Error validating sheets: $e");
      _logger.shout(e.toString());
      return false;
    }
  }

  Future<void> load() async {
    try {
      // final FilePickerCross myFile = await FilePickerCross.importFromStorage(
      //   fileExtension: 'csv', // Only if FileTypeCross.custom . May be any file extension like `dot`, `ppt,pptx,odp`
      // );

      final FilePickerResult? myFile = await FilePicker.platform.pickFiles();
      if (myFile == null) return;
      final String? path = myFile.files.single.path;
      if (path == null) return;

      await controller.load(path);
    } catch (e) {
      controller.notificationService.error("Error parsing XLSX document: $e");
      _logger.shout(e.toString());
    }
  }

  void clearAllColumnDropdowns({
    required String sheetName,
    required List<ColumnInfo> columns,
  }) {
    try {
      return controller.clearAllColumnDropdowns(sheetName: sheetName, columns: columns);
    } catch (e) {
      controller.notificationService.error("Error clearing columns: $e");
      _logger.shout(e.toString());
    }
  }

  Future<void> autoPopulateAllColumnDropdowns() async {
    try {
      return controller.autoPopulateAllColumnDropdowns();
    } catch (e) {
      controller.notificationService.error("Error auto populating columns: $e");
      _logger.shout(e.toString());
    }
  }

  Future<void> autoPopulateColumnDropdowns({
    required Sheet sheet,
    required List<ColumnInfo> columnInfos,
    required String sheetName,
    required bool strict,
  }) async {
    try {
      return controller.autoPopulateColumnDropdowns(
        sheet: sheet,
        columnInfos: columnInfos,
        sheetName: sheetName,
        strict: strict,
      );
    } catch (e) {
      controller.notificationService.error("Error auto populating columns: $e");
      _logger.shout(e.toString());
    }
  }

  Future<void> importItems() async {
    try {
      return controller.import();
    } catch (e) {
      controller.notificationService.error("Error importing items: $e");
      _logger.shout(e.toString());
    }
  }

  Future<void> saveAnnotated() async {
    try {
      controller.isSaving.value += 1;

      Future<Excel> cloneAndAnnotate() async {
        // Clone excel.
        final Excel excelClone = await controller.getExcelObjectCopy();

        Sheet getClonedSheet(Sheet sheet) {
          final Sheet? clonedSheet = excelClone.tables[sheet.sheetName];
          if (clonedSheet == null) {
            throw Exception("Cloned sheet not found: ${sheet.sheetName}");
          }
          return clonedSheet;
        }

        SheetInfo getClonedSheetInfo(SheetInfo sheetInfo) {
          final Sheet? sheet = sheetInfo.sheet;
          Sheet? clonedSheet;
          if (sheet != null) {
            clonedSheet = getClonedSheet(sheet);
          }
          return SheetInfo(
            type: sheetInfo.type,
            sheet: clonedSheet,
          );
        }

        Issue getClonedIssue(Issue issue) {
          return Issue(
            rowDesc: issue.rowDesc,
            cellContents: issue.cellContents,
            msg: issue.msg,
            rowId: issue.rowId,
            columnName: issue.columnName,
            sheet: getClonedSheet(issue.sheet),
            row: issue.row,
            col: issue.col,
            level: issue.level,
          );
        }

        List<Issue> getClonedSheetIssues(List<Issue> sheetIssues) {
          return sheetIssues.map(getClonedIssue).toList();
        }

        final List<SheetInfo> sheetInfosClone = controller.sheetInfos.map(getClonedSheetInfo).toList();
        final List<List<Issue>> sheetsIssuesClone = controller.sheetsIssues.map(getClonedSheetIssues).toList();

        await ImportItemDialogController.annotate(
          excelClone: excelClone,
          sheetInfosClone: sheetInfosClone,
          sheetsIssuesClone: sheetsIssuesClone,
          annotatingProgress: controller.annotatingProgress,
          annotatingStep: controller.annotatingStep,
          isAnnotating: controller.isAnnotating,
        );
        return excelClone;
      }

      final String fileName = controller.getAnnotatedFileName();

      // Start the export in the background.
      final Future<Excel> excelTask = cloneAndAnnotate();

      final String? savePath = await FilePicker.platform.saveFile(
        dialogTitle: "Save Annotated Excel File",
        allowedExtensions: <String>['xlsx'],
        initialDirectory: controller.getAnnotatedFileDirectory(),
        fileName: fileName,
        // NOTE: This unfortunately only works on windows apparently ¯\_(ツ)_/¯.
        lockParentWindow: true,
      );
      final Excel excelClone = await excelTask;
      if (savePath == null) {
        throw Exception("An error occurred while saving the file.");
      }

      await controller.saveFile(excelClone, savePath);

      controller.notificationService.success("File saved to $savePath");
    } catch (e) {
      controller.notificationService.error("An error occurred while saving the file: $e");
      _logger.shout(e.toString());
    } finally {
      controller.isSaving.value -= 1;
    }
  }
}
