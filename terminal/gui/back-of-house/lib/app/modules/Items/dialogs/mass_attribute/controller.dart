// ignore_for_file: depend_on_referenced_packages, avoid_dynamic_calls

import 'dart:convert';

import 'package:backoffice/app/global_widgets/custom_form_field.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/Items/dialogs/mass_attribute/Widgets/pricing_select/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class MassAttributeDialogController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService notificationService = Get.find();

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  final Logger _logger = Logger('ImportController');

  RxString departmentDropValue = "".obs;
  TextEditingController incrementPriceController = TextEditingController();
  TextEditingController percentIncreasePriceController = TextEditingController();
  TextEditingController percentDecreasePriceController = TextEditingController();
  TextEditingController changePriceController = TextEditingController();

  TextEditingController searchController = TextEditingController();

  RxList<Department> departmentList = <Department>[].obs;
  RxList<Department> selectedDepartments = <Department>[].obs;

  RxList<Item> itemTableList = <Item>[].obs;
  RxList<Item> selectedItems = <Item>[].obs;
  List<DropdownMenuItem<dynamic>> filterDropdownList = <DropdownMenuItem<dynamic>>[];

  RxBool loadingItems = true.obs;
  RxBool roundPrice = false.obs;
  RxBool updateByItem = true.obs;

  List<Item> itemsToUpdate = <Item>[];

  RxString param = "".obs;
  RxString changeDepartmentValue = "".obs;

  RxInt pageSize = 10.obs;
  RxInt selectedRadio = (-1).obs;
  RxInt updateIntent = 0.obs;

  RxInt newPrice = 0.obs;
  RxInt incrementPrice = 0.obs;
  RxInt limit = 10.obs;
  RxInt offset = 0.obs;
  RxInt itemsAggregate = 0.obs;
  RxInt totalDataRows = 0.obs;
  RxInt currentStep = 0.obs;
  RxInt roundingValue = 0.obs;

  RxString changePricing = "S0L0C0".obs;

  MerchantJsonRecord merchantRecord = MerchantJsonRecord.empty();

  List<String> radioTitles = <String>[
    "Change item price",
    "Add to current Price",
    "Increase price by %",
    "Decrease price by %",
    "Change department to",
    "Change to other price amount"
  ];

  List<String> pricingList = <String>["S0L0C0"];

  List<int> roundingList = <int>[
    5,
    10,
    25,
  ];

  @override
  Future<void> onInit() async {
    await getDepartments();
    await getMerchandRecord();

    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await getRows();

    super.onInit();
  }

  Future<void> getDepartments() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
                query GET_DEPARTMENTS {
                  department {
                    created_at
                    created_by
                    department
                    department_order
                    document
                    title
                    updated_at
                    updated_by
                  }
                }

                """,
          ),
        ),
      );
      if (departmentResult.hasException) {
        return notificationService.error(departmentResult.exception.toString());
      }
      departmentList.value = (departmentResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic data) => Department.fromJson(data as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      throw e.toString();
    }
  }

  Future<void> getMerchandRecord() async {
    try {
      final QueryResult<Object> merchantResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
              query GET_MERCHANT_RECORD {
                json_record(where: {record_key: {_eq: "merchant"}}) {
                  document
                  record_key
                  updated_at
                }
              }
            """,
          ),
        ),
      );
      if (merchantResult.hasException) {
        return notificationService.error(merchantResult.exception.toString());
      }
      final List<dynamic> resList = merchantResult.data!['json_record'] as List<dynamic>;
      if (resList.isNotEmpty) {
        merchantRecord = MerchantJsonRecord.fromJson(resList.first as Map<String, dynamic>);
      }
    } catch (e) {
      throw e.toString();
    }
  }

  Future<void> getRows() async {
    if (loadingItems.value) {
      if (updateByItem.value) {
        itemTableList.clear();

        try {
          final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
            QueryOptions<Object>(
              document: g.parseString(
                """
              query GET_SEARCH_ITEM_AGGREGATE(\$param: String) {
                search_item_aggregate(args: {param: \$param}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
              ),
              variables: <String, dynamic>{
                "param": param.value,
              },
            ),
          );

          itemsAggregate.value = aggregateResult.data?["search_item_aggregate"]["aggregate"]["count"] as int;
          if (departmentDropValue.value == "") {
            final QueryResult<Object> result = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
              query GET_ITEMS(\$param: String, \$limit: Int, \$offset: Int) {
                  search_item(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {long_desc: asc}) {
                    item
                    long_desc
                    upc
                    department
                    document
                    updated_at
                    updated_by
                    created_at
                    created_by
                    liq_ctl_plu
                    departmentByDepartment {
                      created_at
                      created_by
                      department
                      department_order
                      document
                      title
                      updated_at
                      updated_by
                    }
                  }
                }
              """,
                ),
                variables: <String, dynamic>{
                  "param": param.value,
                  "limit": limit.value,
                  "offset": offset.value,
                },
              ),
            );
            if (result.hasException) {
              return notificationService.error(result.exception.toString());
            }

            itemTableList.value = (result.data!['search_item'] as List<dynamic>)
                .map(
                  (dynamic data) => Item.fromJson(data as Map<String, dynamic>),
                )
                .toList();

            totalDataRows.value = itemTableList.length;

            loadingItems.value = false;
          } else {
            final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
              query GET_ITEM_AGGREGATE(\$param: String, \$_eq: uuid) {
                search_item_aggregate(args: {param: \$param}, where: {department: {_eq: \$_eq}}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
                ),
                variables: <String, dynamic>{"param": "%${param.value}%", "_eq": departmentDropValue.value},
              ),
            );

            itemsAggregate.value = aggregateResult.data?["search_item_aggregate"]["aggregate"]["count"] as int;

            final QueryResult<Object> result = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
                query GET_ITEMS(\$param: String, \$limit: Int, \$offset: Int, \$_eq: uuid ) {
                  search_item(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {long_desc: asc}, where: {department: {_eq: \$_eq}}) {
                    item
                    long_desc
                    upc
                    department
                    document
                    updated_at
                    updated_by
                    created_at
                    created_by
                    liq_ctl_plu
                    departmentByDepartment {
                      created_at
                      created_by
                      department
                      department_order
                      document
                      title
                      updated_at
                      updated_by
                    }
                  }
                }
                """,
                ),
                variables: <String, dynamic>{
                  "param": param.value,
                  "limit": limit.value,
                  "offset": offset.value,
                  "_eq": departmentDropValue.value,
                },
              ),
            );

            if (result.hasException) {
              return notificationService.error(result.exception.toString());
            }

            itemTableList.value = (result.data!['search_item'] as List<dynamic>)
                .map(
                  (dynamic data) => Item.fromJson(data as Map<String, dynamic>),
                )
                .toList();

            totalDataRows.value = itemTableList.length;

            loadingItems.value = false;
          }
        } catch (e) {
          throw e.toString();
        }
      } else {
        departmentList.clear();

        final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
              query GET_SEARCH_DEPARTMENT_AGGREGATE(\$param: String) {
                search_department_aggregate(args: {param: \$param}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
            ),
            variables: <String, dynamic>{
              "param": param.value,
            },
          ),
        );

        if (aggregateResult.hasException) {
          return notificationService.error(aggregateResult.exception.toString());
        }

        itemsAggregate.value = aggregateResult.data?["search_department_aggregate"]["aggregate"]["count"] as int;

        final QueryResult<Object> departmentListResult = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
              query GET_DEPARTMENT_LIST(\$param: String, \$limit: Int, \$offset: Int) {
                  search_department(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {title: asc}) {
                    department
                    title
                    document
                    department_order
                    created_by
                    created_at
                    updated_at
                    updated_by
                  }
                }
              """,
            ),
            variables: <String, dynamic>{
              "param": param.value,
              "limit": limit.value,
              "offset": offset.value,
            },
          ),
        );

        if (departmentListResult.hasException) {
          return notificationService.error(departmentListResult.exception.toString());
        }

        departmentList.value = (departmentListResult.data!['search_department'] as List<dynamic>)
            .map(
              (dynamic data) => Department.fromJson(data as Map<String, dynamic>),
            )
            .toList();

        loadingItems.value = false;
      }
    }
  }

  Future<void> getItemsByDepartment(List<Department> deptList) async {
    List<Item> itemsByDepartment = <Item>[];
    for (int i = 0; i < deptList.length; i++) {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
            query MyQuery(\$department: uuid_comparison_exp) {
              item(where: {department: \$department}) {
                document
                item
                long_desc
                upc
                updated_at
                updated_by
                created_at
                created_by
                department
              }
            }

         """,
          ),
          variables: <String, dynamic>{
            "department": <String, dynamic>{
              "_eq": selectedDepartments[i].department,
            },
          },
        ),
      );

      itemsByDepartment = (result.data!['item'] as List<dynamic>).map((dynamic data) => Item.fromJson(data as Map<String, dynamic>)).toList();
      for (final Item item in itemsByDepartment) {
        if (!selectedItems.any((Item i) => i.item == item.item)) {
          selectedItems.add(item);
        }
      }
      // selectedItems.addAll(itemsByDepartment);
    }
  }

  Item makeCloneItem(Item? item) {
    Map<String, dynamic> jsonItem;
    Item cloneItem = Item.empty();
    jsonItem = json.decode(json.encode(item)) as Map<String, dynamic>;
    cloneItem = Item.fromJson(jsonItem);
    cloneItem.document.pricing["S0L0C0"] ??= 0;
    return cloneItem;
  }

  Future<bool> changePriceSelected({bool copying = false}) async {
    if (!copying) {
      newPrice.value = int.parse(
        changePriceController.text.replaceAll(".", "").replaceAll(",", ""),
      );
    }
    try {
      if (!updateByItem.value) {
        await getItemsByDepartment(selectedDepartments);
      } else {
        for (int i = 0; i < selectedItems.length; i++) {
          if (!selectedDepartments.any(
            (Department d) => d.department == selectedItems[i].department,
          )) {
            selectedDepartments.add(selectedItems[i].departmentByDepartment!);
          }
        }
      }

      for (final Item item in selectedItems) {
        final Item cloneItem = makeCloneItem(item);

        for (final String key in pricingList) {
          cloneItem.document.pricing[key] = copying ? cloneItem.document.pricing[changePricing.value] ?? 0 : newPrice.value;

          if (roundPrice.value) {
            cloneItem.document.pricing[key] = handleRounding(
              cloneItem.document.pricing[key]!,
              roundingValue.value,
            );
          }
          if (cloneItem.document.pricing[key] == 0 && key != "S0L0C0" && key != "S0L0C1") {
            cloneItem.document.pricing.remove(key);
          }
        }

        cloneItem.departmentByDepartment = null;
        itemsToUpdate.add(cloneItem);
      }

      return true;
    } catch (e, stack) {
      notificationService.error("error mass updating items");
      _logger.shout('error mass updating items', e, stack);
      return false;
    }
  }

  Future<bool> changePriceIncrement() async {
    try {
      if (!updateByItem.value) {
        await getItemsByDepartment(selectedDepartments);
      } else {
        for (int i = 0; i < selectedItems.length; i++) {
          if (!selectedDepartments.any(
            (Department d) => d.department == selectedItems[i].department,
          )) {
            selectedDepartments.add(selectedItems[i].departmentByDepartment!);
          }
        }
      }

      for (final Item item in selectedItems) {
        final Item cloneItem = makeCloneItem(item);

        for (final String key in pricingList) {
          cloneItem.document.pricing[key] = (cloneItem.document.pricing[key] ?? 0) +
              int.parse(
                incrementPriceController.text.replaceAll(",", "").replaceAll(".", ""),
              );

          if (roundPrice.value) {
            cloneItem.document.pricing[key] = handleRounding(
              cloneItem.document.pricing[key]!,
              roundingValue.value,
            );
          }
          if (cloneItem.document.pricing[key] == 0 && key != "S0L0C0" && key != "S0L0C1") {
            cloneItem.document.pricing.remove(key);
          }
        }

        cloneItem.departmentByDepartment = null;
        itemsToUpdate.add(cloneItem);
      }
      return true;
    } catch (e, stack) {
      notificationService.error("error mass updating items");
      _logger.shout('error mass updating items', e, stack);
      return false;
    }
  }

  Future<bool> changeDepartment() async {
    try {
      if (!updateByItem.value) {
        await getItemsByDepartment(selectedDepartments);
      } else {
        for (int i = 0; i < selectedItems.length; i++) {
          if (!selectedDepartments.any(
            (Department d) => d.department == selectedItems[i].department,
          )) {
            selectedDepartments.add(selectedItems[i].departmentByDepartment!);
          }
        }
      }
      for (final Item item in selectedItems) {
        Map<String, dynamic> jsonItem;
        Item cloneItem = Item.empty();

        jsonItem = json.decode(json.encode(item)) as Map<String, dynamic>;
        cloneItem = Item.fromJson(jsonItem);

        cloneItem.department = changeDepartmentValue.value;
        cloneItem.departmentByDepartment = null;

        for (final String key in pricingList) {
          if (roundPrice.value) {
            cloneItem.document.pricing[key] = handleRounding(
              cloneItem.document.pricing[key]!,
              roundingValue.value,
            );
          }
          if (cloneItem.document.pricing[key] == 0 && key != "S0L0C0" && key != "S0L0C1") {
            cloneItem.document.pricing.remove(key);
          }
        }

        itemsToUpdate.add(cloneItem);
      }
      return true;
    } catch (e, stack) {
      notificationService.error("error mass updating items");
      _logger.shout('error mass updating items', e, stack);
      return false;
    }
  }

  Future<bool> changeByPercentage(int value) async {
    double percentage;
    String tempPriceString;

    try {
      if (!updateByItem.value) {
        await getItemsByDepartment(selectedDepartments);
      } else {
        for (int i = 0; i < selectedItems.length; i++) {
          if (!selectedDepartments.any(
            (Department d) => d.department == selectedItems[i].department,
          )) {
            selectedDepartments.add(selectedItems[i].departmentByDepartment!);
          }
        }
      }

      for (final Item item in selectedItems) {
        final Item cloneItem = makeCloneItem(item);

        for (final String key in pricingList) {
          cloneItem.document.pricing[key] ??= 0;

          if (value == 2) {
            percentage = double.parse(percentIncreasePriceController.text) / 100;
            tempPriceString =
                (cloneItem.document.pricing[key]! + (((cloneItem.document.pricing[key]! * (percentage / 100)) * 100).round())).toString();
          } else {
            percentage = double.parse(percentDecreasePriceController.text) / 100;
            tempPriceString =
                (cloneItem.document.pricing[key]! - (((cloneItem.document.pricing[key]! * (percentage / 100)) * 100).round())).toString();
          }

          cloneItem.document.pricing[key] = int.parse(tempPriceString);

          if (roundPrice.value) {
            cloneItem.document.pricing[key] = handleRounding(
              cloneItem.document.pricing[key]!,
              roundingValue.value,
            );
          }
          if (cloneItem.document.pricing[key] == 0 && key != "S0L0C0" && key != "S0L0C1") {
            cloneItem.document.pricing.remove(key);
          }
        }
        cloneItem.departmentByDepartment = null;
        itemsToUpdate.add(cloneItem);
      }

      return true;
    } catch (e, stack) {
      notificationService.error("error mass updating items");
      _logger.shout('error mass updating items', e, stack);
      return false;
    }
  }

  Widget generateInputs(int value) {
    switch (value) {
      case 0:
        return Obx(
          () => CustomFormField(
            controller: changePriceController,
            enabled: selectedRadio.value == value,
            borderColor: R2Colors.neutral200,
            validator: (dynamic value) {
              if (changePriceController.text == "" && selectedRadio.value == 0) {
                return 'Enter a valid Description';
              }
              return null;
            },
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(
                RegExp('[0-9,.]'),
              ),
              CurrencyTextInputFormatter.currency(
                decimalDigits: 2,
                symbol: "",
              ),
            ],
            prefixText: "\$",
          ),
        );
      case 1:
        return Obx(
          () => CustomFormField(
            controller: incrementPriceController,
            enabled: selectedRadio.value == value,
            borderColor: R2Colors.neutral200,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(
                RegExp('[0-9,.]'),
              ),
              CurrencyTextInputFormatter.currency(
                decimalDigits: 2,
                symbol: "",
              ),
            ],
            prefixText: "\$",
          ),
        );
      case 2:
        return Obx(
          () => CustomFormField(
            controller: percentIncreasePriceController,
            enabled: selectedRadio.value == value,
            borderColor: R2Colors.neutral200,
            maxLength: 5,
            suffixText: "%   ",
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(
                RegExp('[0-9,.]'),
              ),
            ],
          ),
        );
      case 3:
        return Obx(
          () => CustomFormField(
            controller: percentDecreasePriceController,
            enabled: selectedRadio.value == value,
            borderColor: R2Colors.neutral200,
            validator: (dynamic value) {
              if (value == null || value.toString().isEmpty) {
                return 'Enter a valid Description';
              }
              return null;
            },
            maxLength: 5,
            suffixText: "%   ",
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(
                RegExp('[0-9,.]'),
              ),
            ],
          ),
        );
      case 4:
        return Obx(
          // ignore: use_decorated_box
          () => Container(
            decoration: BoxDecoration(
              border: Border.all(color: R2Colors.neutral300),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Padding(
              padding: const EdgeInsets.only(left: 5, right: 5),
              child: DropdownButtonFormField<dynamic>(
                isExpanded: true,
                icon: const Icon(Icons.arrow_downward),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                ),
                items: List<DropdownMenuItem<String>>.generate(
                  departmentList.length,
                  (int index) => DropdownMenuItem<String>(
                    value: departmentList[index].department,
                    child: Text(
                      departmentList[index].title,
                      softWrap: false,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                onChanged: selectedRadio.value == value
                    ? (dynamic value) {
                        changeDepartmentValue.value = value.toString();
                      }
                    : null,
              ),
            ),
          ),
        );
      case 5:
        return Center(
          child: GestureDetector(
            onTap: selectedRadio.value == value
                ? () async {
                    await Get.bottomSheet(
                      ThinBottomSheet(
                        child: PricingSelect(
                          singleSelect: true,
                          singleSelectString: changePricing,
                        ),
                      ),
                      isScrollControlled: true,
                    );
                  }
                : null,
            child: Text(
              "Choose Price",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: selectedRadio.value == value ? R2Colors.primary500 : R2Colors.neutral300,
              ),
            ),
          ),
        );
      default:
        return const Text("Too Many Titles");
    }
  }

  RxString getTableInfo() {
    int startNum = offset.value + 1;
    int endNum = offset.value + pageSize.value;

    if (totalDataRows.value < pageSize.value) {
      endNum = offset.value + totalDataRows.value;
    }
    if (itemsAggregate.value == 0) {
      startNum = 0;
    }

    return "${Helpers.formatWholeNumber(startNum)} - ${Helpers.formatWholeNumber(endNum)} of ${Helpers.formatWholeNumber(itemsAggregate.value)}".obs;
  }

  Future<void> insertItemData() async {
    if (selectedRadio.value == 4) {
      try {
        final QueryResult<Object> result = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
            mutation CHANGE_DEPARTMENTS(\$objects: [item_insert_input!] ={}) {
            insert_item(objects: \$objects, on_conflict: {constraint: item_pkey, update_columns: department}) {
              affected_rows
            }
          }

         """,
            ),
            variables: <String, dynamic>{
              "objects": itemsToUpdate,
            },
          ),
        );
        if (result.hasException) {
          return notificationService.error(result.exception!.graphqlErrors.toString());
        }
        final String count = result.data?['insert_item']['affected_rows'].toString() ?? '0';

        _logger.info(
            "${CURRENT_EMPLOYEE.value.employee_full_name} Mass updated ($count) item's department. Change department to $changeDepartmentValue");

        notificationService.success("Items updated succesfully");
        await Get.offNamed(
          AppRoutes.ITEMS,
          id: AppRoutes.id,
        );
      } catch (e, stack) {
        notificationService.error("error mass updating items");
        _logger.shout('error mass updating items', e, stack);
      }
    } else {
      try {
        final QueryResult<Object> result = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
            mutation MyMutation(\$objects: [item_insert_input!] ={}) {
            insert_item(objects: \$objects, on_conflict: {constraint: item_pkey, update_columns: document}) {
              affected_rows
            }
          }
         """,
            ),
            variables: <String, dynamic>{
              "objects": itemsToUpdate,
            },
          ),
        );
        if (result.hasException) {
          return notificationService.error(result.exception!.graphqlErrors.toString());
        }
        final String count = result.data?['insert_item']['affected_rows'].toString() ?? '0';
        _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} Mass updated ($count) items. ${getIntentString()}");

        await Get.offNamed(
          AppRoutes.ITEMS,
          id: AppRoutes.id,
        );
        notificationService.success("Items updated succesfully");
      } catch (e, stack) {
        notificationService.error("error mass updating items");
        _logger.shout('error mass updating items', e, stack);
      }
    }
  }

  int handleRounding(int price, int roundTo) {
    return (price / roundTo).round() * roundTo;
  }

  Future<void> handleUpdateIntent(int value) async {
    selectedDepartments.clear();
    selectedDepartments.refresh();
    selectedItems.clear();
    selectedItems.refresh();
    updateIntent.value = value;
    if (value == 0) {
      updateByItem.value = true;
    } else {
      updateByItem.value = false;
    }
    loadingItems.value = true;
    await getRows();
  }

  void clearControllers() {
    if (changeDepartmentValue.value != "") {
      changeDepartmentValue.value = "";
    }
    changePriceController.clear();
    incrementPriceController.clear();
    percentIncreasePriceController.clear();
    percentDecreasePriceController.clear();
  }

  void updateItemPricing(String key, Map<String, int> pricing) {
    final String altKey = key[key.length - 1] == "0" ? "${key.substring(0, key.length - 1)}1" : "${key.substring(0, key.length - 1)}0";
    if (pricing[key] == 0) {
      if (key != "S0L0C0" && key != "S0L0C1") {
        pricing.remove(key);
        pricing.remove(altKey);
      } else {
        pricing[altKey] = 0;
      }
    } else if ((pricing[altKey] ?? 0) == 0) {
      pricing[altKey] = key[key.length - 1] == "0"
          ? Helpers.getCashPrice(
              cashAmount: pricing[altKey],
              creditAmount: pricing[key],
              dualPercent: merchantRecord.document.dualPricingPercent,
              roundAmount: merchantRecord.document.dualPricingRoundAmount,
            )
          : Helpers.getCreditPrice(
              cashAmount: pricing[key],
              creditAmount: pricing[altKey],
              dualPercent: merchantRecord.document.dualPricingPercent,
              roundAmount: merchantRecord.document.dualPricingRoundAmount,
            );
    }
  }

  String getIntentString() {
    switch (selectedRadio.value) {
      case 0:
        return "Change Price to ${changePriceController.text}";
      case 1:
        return 'Increment price by ${incrementPriceController.text}';
      case 2:
        return "Increased price by ${percentIncreasePriceController.text}%";
      case 3:
        return 'Decrease price by ${percentDecreasePriceController.text}%';
      case 4:
        return "Change department to ${changeDepartmentValue.value}";
      case 5:
        return 'Change Pricing $changePricing';
      default:
        return '';
    }
  }
}
