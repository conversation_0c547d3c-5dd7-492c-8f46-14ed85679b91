// ignore_for_file: depend_on_referenced_packages, avoid_dynamic_calls

import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class MassDeleteDialogController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService notificationService = Get.find();

  RxString departmentDropValue = "".obs;

  final Logger _logger = Logger('MassDeleteController');

  final RxBool isLoading = false.obs;
  final RxBool isSuccess = true.obs;

  TextEditingController searchController = TextEditingController();

  RxList<Department> departmentList = <Department>[].obs;
  RxList<Department> selectedDepartments = <Department>[].obs;

  RxList<Item> itemTableList = <Item>[].obs;
  RxList<Item> selectedItems = <Item>[].obs;
  List<DropdownMenuItem<dynamic>> filterDropdownList = <DropdownMenuItem<dynamic>>[];

  RxBool loadingItems = true.obs;
  RxBool updateByItem = true.obs;

  RxString param = "".obs;

  RxInt pageSize = 10.obs;
  RxInt selectedRadio = (-1).obs;
  RxInt updateIntent = 0.obs;

  RxInt limit = 10.obs;
  RxInt offset = 0.obs;
  RxInt itemsAggregate = 0.obs;
  RxInt totalDataRows = 0.obs;

  RxInt count = 0.obs;
  List<int> roundingList = <int>[
    5,
    10,
    25,
  ];

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  void clear() {
    selectedItems.clear();
    selectedDepartments.clear();
  }

  @override
  Future<void> onReady() async {
    await getRows();
    await getDepartments();

    super.onInit();
  }

  Future<void> getDepartments() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
                query GET_DEPARTMENTS {
                  department {
                    created_at
                    created_by
                    department
                    department_order
                    document
                    title
                    updated_at
                    updated_by
                  }
                }

                """,
          ),
        ),
      );

      if (departmentResult.hasException) {
        return notificationService.error(departmentResult.exception.toString());
      }

      departmentList.value = (departmentResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic data) => Department.fromJson(data as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      throw e.toString();
    }
  }

  Future<void> getRows() async {
    if (loadingItems.value) {
      if (updateByItem.value) {
        itemTableList.clear();

        try {
          final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
            QueryOptions<Object>(
              document: g.parseString(
                """
              query GET_SEARCH_ITEM_AGGREGATE(\$param: String) {
                search_item_aggregate(args: {param: \$param}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
              ),
              variables: <String, dynamic>{
                "param": param.value,
              },
            ),
          );

          itemsAggregate.value = aggregateResult.data?["search_item_aggregate"]["aggregate"]["count"] as int;
          if (departmentDropValue.value == "") {
            final QueryResult<Object> result = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
              query GET_ITEMS(\$param: String, \$limit: Int, \$offset: Int) {
                  search_item(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {long_desc: asc}) {
                    item
                    long_desc
                    upc
                    department
                    document
                    updated_at
                    updated_by
                    created_at
                    created_by
                    liq_ctl_plu
                    departmentByDepartment {
                      created_at
                      created_by
                      department
                      department_order
                      document
                      title
                      updated_at
                      updated_by
                    }
                  }
                }
              """,
                ),
                variables: <String, dynamic>{
                  "param": param.value,
                  "limit": limit.value,
                  "offset": offset.value,
                },
              ),
            );
            if (result.hasException) {
              return notificationService.error(result.exception.toString());
            }

            itemTableList.value = (result.data!['search_item'] as List<dynamic>)
                .map(
                  (dynamic data) => Item.fromJson(data as Map<String, dynamic>),
                )
                .toList();

            totalDataRows.value = itemTableList.length;

            loadingItems.value = false;
          } else {
            final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
              query GET_ITEM_AGGREGATE(\$param: String, \$_eq: uuid) {
                search_item_aggregate(args: {param: \$param}, where: {department: {_eq: \$_eq}}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
                ),
                variables: <String, dynamic>{"param": "%${param.value}%", "_eq": departmentDropValue.value},
              ),
            );

            itemsAggregate.value = aggregateResult.data?["search_item_aggregate"]["aggregate"]["count"] as int;

            final QueryResult<Object> result = await _graphqlService.client.query(
              QueryOptions<Object>(
                document: g.parseString(
                  """
                query GET_ITEMS(\$param: String, \$limit: Int, \$offset: Int, \$_eq: uuid ) {
                  search_item(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {long_desc: asc}, where: {department: {_eq: \$_eq}}) {
                    item
                    long_desc
                    upc
                    department
                    document
                    updated_at
                    updated_by
                    created_at
                    created_by
                    liq_ctl_plu
                    departmentByDepartment {
                      created_at
                      created_by
                      department
                      department_order
                      document
                      title
                      updated_at
                      updated_by
                    }
                  }
                }
                """,
                ),
                variables: <String, dynamic>{
                  "param": param.value,
                  "limit": limit.value,
                  "offset": offset.value,
                  "_eq": departmentDropValue.value,
                },
              ),
            );

            if (result.hasException) {
              return notificationService.error(result.exception.toString());
            }

            itemTableList.value = (result.data!['search_item'] as List<dynamic>)
                .map(
                  (dynamic data) => Item.fromJson(data as Map<String, dynamic>),
                )
                .toList();

            totalDataRows.value = itemTableList.length;

            loadingItems.value = false;
          }
        } catch (e) {
          throw e.toString();
        }
      } else {
        departmentList.clear();

        final QueryResult<Object> aggregateResult = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
              query GET_SEARCH_DEPARTMENT_AGGREGATE(\$param: String) {
                search_department_aggregate(args: {param: \$param}) {
                  aggregate {
                    count
                  }
                }
              }
              """,
            ),
            variables: <String, dynamic>{
              "param": param.value,
            },
          ),
        );

        if (aggregateResult.hasException) {
          return notificationService.error(aggregateResult.exception.toString());
        }

        itemsAggregate.value = aggregateResult.data?["search_department_aggregate"]["aggregate"]["count"] as int;

        final QueryResult<Object> departmentListResult = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              """
              query GET_DEPARTMENT_LIST(\$param: String, \$limit: Int, \$offset: Int) {
                  search_department(args: {param: \$param}, limit: \$limit, offset: \$offset, order_by: {title: asc}) {
                    department
                    title
                    document
                    department_order
                    created_by
                    created_at
                    updated_at
                    updated_by
                  }
                }
              """,
            ),
            variables: <String, dynamic>{
              "param": param.value,
              "limit": limit.value,
              "offset": offset.value,
            },
          ),
        );

        if (departmentListResult.hasException) {
          return notificationService.error(departmentListResult.exception.toString());
        }

        departmentList.value = (departmentListResult.data!['search_department'] as List<dynamic>)
            .map(
              (dynamic data) => Department.fromJson(data as Map<String, dynamic>),
            )
            .toList();

        loadingItems.value = false;
      }
    }
  }

  Future<void> getItemsByDepartment(List<Department> deptList) async {
    List<Item> itemsByDepartment = <Item>[];
    for (final Department object in selectedDepartments) {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
            query MyQuery(\$department: uuid_comparison_exp) {
              item(where: {department: \$department}) {
                document
                item
                long_desc
                upc
                updated_at
                updated_by
                created_at
                created_by
                department
              }
            }

         """,
          ),
          variables: <String, dynamic>{
            "department": <String, dynamic>{
              "_eq": object.department,
            },
          },
        ),
      );

      itemsByDepartment = (result.data!['item'] as List<dynamic>).map((dynamic data) => Item.fromJson(data as Map<String, dynamic>)).toList();
      for (final Item item in itemsByDepartment) {
        if (!selectedItems.any((Item i) => i.item == item.item)) {
          selectedItems.add(item);
        }
      }
      // selectedItems.addAll(itemsByDepartment);
    }
  }

  RxString getTableInfo() {
    int startNum = offset.value + 1;
    int endNum = offset.value + pageSize.value;

    if (totalDataRows.value < pageSize.value) {
      endNum = offset.value + totalDataRows.value;
    }
    if (itemsAggregate.value == 0) {
      startNum = 0;
    }

    return "${Helpers.formatWholeNumber(startNum)} - ${Helpers.formatWholeNumber(endNum)} of ${Helpers.formatWholeNumber(itemsAggregate.value)}".obs;
  }

  Future<void> handleUpdateIntent(int value) async {
    selectedDepartments.clear();
    selectedDepartments.refresh();
    selectedItems.clear();
    selectedItems.refresh();
    updateIntent.value = value;
    if (value == 0) {
      updateByItem.value = true;
    } else {
      updateByItem.value = false;
    }
    loadingItems.value = true;
    await getRows();
  }

  Future<void> deleteItems() async {
    await getItemsByDepartment(selectedDepartments);
    count = 0.obs;
    for (final Item object in selectedItems) {
      try {
        final QueryResult<Object> deleteitemResult = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString(
              '''
          mutation MyMutation(\$item: uuid!) {
              delete_item_by_pk(item: \$item){
                item
              }
            }

          ''',
            ),
            variables: <String, dynamic>{"item": object.item},
          ),
        );

        if (deleteitemResult.hasException) {
          return notificationService.error(deleteitemResult.exception.toString());
        }

        await editUpdateModsOnDelete(object.item);
        count = count + 1;
      } catch (err, stack) {
        _logger.shout('Error deleting items', err, stack);
      }
    }

    isLoading.value = false;
    _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} Mass deleted (${count.value}) items');

    await Get.offNamed(AppRoutes.ITEMS, id: AppRoutes.id);
  }

  Future<void> editUpdateModsOnDelete(String uuid) async {
    final QueryResult<Object> itemRes = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
           query GET_MOD_ITEMS {
              item(where: {document: {_cast: {String: {_like: "%${uuid}_: {%"}}}}) {
                created_at
                department
                created_by
                document
                item
                long_desc
                upc
                updated_at
                updated_by
              }
            }
          ''',
        ),
      ),
    );
    if (itemRes.hasException) {
      return notificationService.error(itemRes.exception.toString());
    }
    // ignore: always_specify_types
    final List<Item> iList = (itemRes.data!["item"] as List).map((dynamic e) => Item.fromJson(e as Map<String, dynamic>)).toList();

    for (final Item i in iList) {
      if (i.document.modifiers[uuid] != null) {
        i.document.modifiers.remove(uuid);

        final QueryResult<Object> updateRes = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString(
              '''
                mutation UPDATE_ITEM_BY_PK(\$item: uuid!, \$document: jsonb) {
                  update_item_by_pk(pk_columns: {item: \$item}, _set: {document: \$document}) {
                    department
                    document
                    item
                    long_desc
                    upc
                  }
                }
              ''',
            ),
            variables: <String, dynamic>{
              "item": i.item,
              "document": i.document.toJson(),
            },
          ),
        );

        if (updateRes.hasException) {
          return notificationService.error(itemRes.exception.toString());
        }
      }
    }
  }
}
