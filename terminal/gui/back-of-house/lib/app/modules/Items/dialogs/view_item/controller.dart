// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/modules/Items/controller.dart';
import 'package:backoffice/app/modules/Items/dialogs/view_item/add_duplicate_item.dart/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_ticket_provider_mixin.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class ViewItemDialogController extends GetxController with GetTickerProviderStateMixin {
  ViewItemDialogController({
    this.item,
    this.addMultiple = false,
  });
  final Logger _logger = Logger("ViewItemLogger");
  final Item? item;
  final bool addMultiple;

  final NotificationService _notificationService = Get.find();
  final ActivityService _activityService = Get.find();
  final IdentityService _identityService = Get.find();
  final ItemsController _itemsController = Get.find();

  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> viewItemFormKey = GlobalKey<FormState>();

  final Rx<Item> currentItem = Item.empty().obs;

  final Rx<String> assignedDepartmentId = "".obs;

  final RxBool isWeighted = false.obs;
  final RxBool allowEbt = true.obs;

  final Rxn<bool> cdValue = Rxn<bool>();

  final RxList<Department> availableDepartments = <Department>[].obs;
  final RxList<String> selectedLiquor = <String>[].obs;

  final Rx<MerchantJsonRecord> merchantDoc = MerchantJsonRecord.empty().obs;

  Map<String, int> itemPricing = <String, int>{"S0L0C0": 0};
  Map<String, int> defModPricing = <String, int>{"S0L0C0": 0};

  final TextEditingController longDescController = TextEditingController();
  final TextEditingController receiptDescController = TextEditingController();
  final TextEditingController detailedDescController = TextEditingController();
  final TextEditingController onlineDescController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController cashPriceController = TextEditingController();
  final TextEditingController upcController = TextEditingController();
  final TextEditingController liqCtlPluController = TextEditingController();
  final TextEditingController modDescController = TextEditingController();
  final TextEditingController minModController = TextEditingController();
  final TextEditingController maxModController = TextEditingController();
  final TextEditingController countController = TextEditingController();

  final RxBool canEdit = false.obs;
  final RxBool canDelete = false.obs;
  final RxBool canCreate = false.obs;
  final RxBool promptForPrice = false.obs;
  final RxBool pinToTop = false.obs;
  final RxBool negativeItem = false.obs;
  final RxBool showOnline = false.obs;
  final RxBool takeOutSurcharge = false.obs;
  final RxBool printInRed = false.obs;
  final RxBool isModifier = false.obs;
  final RxBool multiModLists = false.obs;
  final RxBool passDesc = false.obs;
  final RxBool overridePricing = false.obs;
  final RxBool useLongDesc = true.obs;
  final RxBool printSeparate = false.obs;

  final RxBool longDescLock = true.obs;
  final RxBool receiptDescLock = true.obs;
  final RxBool priceLock = true.obs;
  final RxBool defModPriceLock = true.obs;
  final RxBool upcLock = false.obs;
  final RxBool liqCtlLock = false.obs;
  final RxBool departmentLock = true.obs;
  final RxBool pinToTopLock = true.obs;
  final RxBool negativeItemLock = true.obs;
  final RxBool surchargeLock = true.obs;
  final RxBool showOnlineLock = true.obs;
  final RxBool printInRedLock = true.obs;
  final RxBool promptForPriceLock = true.obs;
  final RxBool isWeightedLock = true.obs;
  final RxBool allowEbtLock = true.obs;
  final RxBool uomLock = false.obs;
  final RxBool countLock = false.obs;
  final RxBool isModLock = false.obs;
  final RxBool modDescLock = false.obs;
  final RxBool multiModLock = false.obs;
  final RxBool passDescLock = false.obs;
  final RxBool overridePricingLock = false.obs;
  final RxBool minModCountLock = false.obs;
  final RxBool maxModCountLock = false.obs;
  final RxBool selectedDevicesLock = false.obs;
  final RxBool printSeparateLock = false.obs;

  bool descError = false;
  bool upcError = false;
  bool liqCtlError = false;

  int originalItemCreditPrice = 0;
  int originalItemCashPrice = 0;

  Map<String, int> originalPricing = <String, int>{};

  Rx<int?> assignedUom = 0.obs;

  final RxInt selectedDevices = 0.obs;

  late AnimationController controller;
  late Animation<Color?> animation;
  late Rx<Color> color = R2Colors.neutral500.obs;

  final ScrollController scrollController = ScrollController();

  @override
  Future<void> onInit() async {
    await editPermission();
    await deletePermission();
    await createPermission();
    await loadDepartments();
    await getMerchantSettings();

    currentItem.value = item ?? Item.empty();
    longDescController.text = currentItem.value.long_desc;
    receiptDescController.text = currentItem.value.document.receiptDesc ?? "";
    onlineDescController.text = currentItem.value.document.onlineDesc ?? "";
    detailedDescController.text = currentItem.value.document.detailedDesc ?? "";
    priceController.text = "\$${Helpers.formatCurrency(currentItem.value.document.pricing["S0L0C0"] ?? 0)}";
    final int cashAmount = Helpers.getCashPrice(
      cashAmount: currentItem.value.document.pricing["S0L0C1"],
      creditAmount: currentItem.value.document.pricing["S0L0C0"],
      dualPercent: merchantDoc.value.document.dualPricingPercent,
      roundAmount: merchantDoc.value.document.dualPricingRoundAmount,
    );

    cashPriceController.text = "\$${Helpers.formatCurrency(cashAmount)}";
    assignedDepartmentId.value = currentItem.value.department;
    upcController.text = currentItem.value.upc;
    liqCtlPluController.text = (currentItem.value.liq_ctl_plu ?? "").toString();
    allowEbt.value = currentItem.value.document.allowEbt;
    promptForPrice.value = currentItem.value.document.promptForPrice;
    pinToTop.value = currentItem.value.document.pinToTop;
    isWeighted.value = currentItem.value.document.isWeighted;
    negativeItem.value = currentItem.value.document.negativeItem;
    showOnline.value = item != null && currentItem.value.document.showOnline;
    takeOutSurcharge.value = currentItem.value.document.takeOutSurcharge;
    printInRed.value = currentItem.value.document.printInRed ?? false;
    isModifier.value = currentItem.value.document.isModifier;
    multiModLists.value = currentItem.value.document.multiModLists;
    passDesc.value = currentItem.value.document.passDesc;
    overridePricing.value = currentItem.value.document.overridePricing;
    modDescController.text = currentItem.value.document.modifierDesc ?? "";
    minModController.text = currentItem.value.document.modMinSel < 1 ? "-" : "${currentItem.value.document.modMinSel}";
    maxModController.text = currentItem.value.document.modMaxSel < 1 ? "-" : "${currentItem.value.document.modMaxSel}";
    countController.text = currentItem.value.document.count < 0 ? "-" : "${currentItem.value.document.count}";
    selectedDevices.value = currentItem.value.document.prep;
    selectedLiquor.value = currentItem.value.document.liquorList;
    printSeparate.value = currentItem.value.document.printSeparate;
    if (isWeighted.value == true) {
      assignedUom.value = currentItem.value.document.UOM;
    }
    if (currentItem.value.document.pricing.isNotEmpty) {
      itemPricing = <String, int>{...currentItem.value.document.pricing};
      originalPricing = <String, int>{...currentItem.value.document.pricing};
    }
    if (currentItem.value.document.defModPricing.isNotEmpty) {
      defModPricing = <String, int>{...currentItem.value.document.defModPricing};
    }
    originalItemCreditPrice = item?.document.pricing["S0L0C0"] ?? 0;
    if (merchantDoc.value.document.dualPricing) {
      originalItemCashPrice = item?.document.pricing["S0L0C1"] ?? 0;
    }

    super.onInit();
  }

  void prepareCurrentItemForUpdate() {
    currentItem.value.long_desc = longDescController.text;
    currentItem.value.document.receiptDesc = item == null && useLongDesc.value ? currentItem.value.long_desc : receiptDescController.text;
    currentItem.value.document.onlineDesc = onlineDescController.text.isEmpty ? null : onlineDescController.text;
    currentItem.value.document.detailedDesc = detailedDescController.text.isEmpty ? null : detailedDescController.text;
    currentItem.value.updated_by = CURRENT_EMPLOYEE.value.employee;
    currentItem.value.updated_at = DateTime.now().toUtc();
    currentItem.value.department = assignedDepartmentId.value;
    currentItem.value.upc = upcController.text;
    currentItem.value.liq_ctl_plu = liqCtlPluController.text == "" ? null : int.parse(liqCtlPluController.text);
    currentItem.value.document.promptForPrice = promptForPrice.value;
    currentItem.value.document.isWeighted = isWeighted.value;
    currentItem.value.document.UOM = assignedUom.value;
    currentItem.value.document.pinToTop = pinToTop.value;
    currentItem.value.document.allowEbt = allowEbt.value;
    currentItem.value.document.negativeItem = negativeItem.value;
    currentItem.value.document.showOnline = showOnline.value;
    currentItem.value.document.takeOutSurcharge = takeOutSurcharge.value;
    currentItem.value.document.printInRed = printInRed.value;
    currentItem.value.document.isModifier = isModifier.value;
    currentItem.value.document.multiModLists = multiModLists.value;
    currentItem.value.document.passDesc = passDesc.value;
    currentItem.value.document.overridePricing = overridePricing.value;
    currentItem.value.document.modifierDesc = modDescController.text;
    currentItem.value.document.pricing = itemPricing;
    currentItem.value.document.defModPricing = defModPricing;
    currentItem.value.document.prep = selectedDevices.value;
    currentItem.value.document.printSeparate = selectedDevices.value > 0 && printSeparate.value;
    currentItem.value.document.liquorList = selectedLiquor;
    currentItem.value.document.modMinSel = minModController.text == "-"
        ? 0
        : int.parse(
            minModController.text,
          );
    currentItem.value.document.modMaxSel = maxModController.text == "-"
        ? 0
        : int.parse(
            maxModController.text,
          );
    currentItem.value.document.count = countController.text == "-"
        ? -1
        : int.parse(
            countController.text,
          );
    if (item == null) {
      currentItem.value.created_by = CURRENT_EMPLOYEE.value.employee;
    }
  }

  Future<void> updateItem() async {
    try {
      if (!viewItemFormKey.currentState!.validate()) throw "Invalid form";

      prepareCurrentItemForUpdate();

      if (await hasDupe()) return;

      final QueryResult<Object> updateItemResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_ITEM_BY_PK(\$item: uuid!, \$department: uuid, \$document: jsonb, \$long_desc: String, \$upc: String, \$updated_by: uuid, \$updated_at: timestamptz, \$liq_ctl_plu: smallint) {
              update_item_by_pk(pk_columns: {item: \$item}, _set: {department: \$department, document: \$document, long_desc: \$long_desc, upc: \$upc, updated_by: \$updated_by, updated_at: \$updated_at, liq_ctl_plu: \$liq_ctl_plu}) {
                department
                document
                item
                long_desc
                upc
                liq_ctl_plu
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "item": currentItem.value.item,
            "long_desc": currentItem.value.long_desc,
            "department": currentItem.value.department,
            "upc": currentItem.value.upc,
            "liq_ctl_plu": currentItem.value.liq_ctl_plu,
            "document": <String, dynamic>{
              "UOM": currentItem.value.document.UOM,
              "isWeighted": currentItem.value.document.isWeighted,
              "receiptDesc": currentItem.value.document.receiptDesc,
              "onlineDesc": currentItem.value.document.onlineDesc,
              "allowEbt": currentItem.value.document.allowEbt,
              "promptForPrice": currentItem.value.document.promptForPrice,
              "pinToTop": currentItem.value.document.pinToTop,
              "negativeItem": currentItem.value.document.negativeItem,
              "takeOutSurcharge": currentItem.value.document.takeOutSurcharge,
              "printInRed": currentItem.value.document.printInRed,
              "pricing": currentItem.value.document.pricing,
              "modifiers": currentItem.value.document.modifiers,
              "isModifier": currentItem.value.document.isModifier,
              "passDesc": currentItem.value.document.passDesc,
              "overridePricing": currentItem.value.document.overridePricing,
              "defModPricing": currentItem.value.document.defModPricing,
              "multiModLists": currentItem.value.document.multiModLists,
              "modifierDesc": currentItem.value.document.modifierDesc,
              "modMinSel": currentItem.value.document.modMinSel,
              "modMaxSel": currentItem.value.document.modMaxSel,
              "count": currentItem.value.document.count,
              "prep": currentItem.value.document.prep,
              "printSeparate": currentItem.value.document.printSeparate,
              "liquorList": currentItem.value.document.liquorList,
              "showOnline": currentItem.value.document.showOnline,
              "detailedDesc": currentItem.value.document.detailedDesc,
            },
            "updated_by": CURRENT_EMPLOYEE.value.employee,
            "updated_at": currentItem.value.updated_at.toString(),
          },
        ),
      );

      if (updateItemResult.hasException) {
        return _notificationService.error(updateItemResult.exception.toString());
      }
      if (originalItemCreditPrice != currentItem.value.document.pricing["S0L0C0"]) {
        await _activityService.insertActivity(
          activity: Activity(
            emp_id: CURRENT_EMPLOYEE.value.id,
            term_num: _identityService.terminalNumber,
            activity: ActivityFlags.UPDATE_RECORD.index,
            str_data: 'Update Item price',
            data1: originalItemCreditPrice,
            data2: currentItem.value.document.pricing["S0L0C0"],
          ),
        );
        _logger.shout(
          merchantDoc.value.document.dualPricing
              ? "${CURRENT_EMPLOYEE.value.employee_full_name} changed item credit price from ${Helpers.formatCurrency(originalItemCreditPrice)} to ${Helpers.formatCurrency(currentItem.value.document.pricing["S0L0C0"] ?? 0)}"
              : "${CURRENT_EMPLOYEE.value.employee_full_name} changed item price from ${Helpers.formatCurrency(originalItemCreditPrice)} to ${Helpers.formatCurrency(currentItem.value.document.pricing["S0L0C0"] ?? 0)}",
        );
      }
      if (merchantDoc.value.document.dualPricing && originalItemCashPrice != currentItem.value.document.pricing["S0L0C1"]) {
        _logger.shout(
          "${CURRENT_EMPLOYEE.value.employee_full_name} changed item cash price from ${Helpers.formatCurrency(originalItemCashPrice)} to ${Helpers.formatCurrency(currentItem.value.document.pricing["S0L0C1"] ?? 0)}",
        );
      }

      final Map<String, Map<String, dynamic>> priceDiffs = findPricingDifferences(itemPricing, originalPricing);
      if (priceDiffs.isNotEmpty) {
        _logger.shout(priceDiffs.toString());
      }

      _logger.shout("${CURRENT_EMPLOYEE.value.employee_full_name} updated Item ${currentItem.value.long_desc} (${currentItem.value.item})");
      _notificationService.success("Update item success");
      Get.back(id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Item Update failed!");
      _logger.severe('Error Updating item - ${currentItem.value.long_desc} (${currentItem.value.item})', err, stack);
    }
  }

  Future<void> deleteItem() async {
    try {
      final QueryResult<Object> deleteitemResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
          mutation MyMutation(\$item: uuid!) {
              delete_item_by_pk(item: \$item){
                item
              }
            }

          ''',
          ),
          variables: <String, dynamic>{"item": currentItem.value.item},
        ),
      );

      if (deleteitemResult.hasException) {
        return _notificationService.error(deleteitemResult.exception.toString());
      }

      await editUpdateItemsOnDelete(currentItem.value.item);
      _logger.shout("${CURRENT_EMPLOYEE.value.employee_full_name} deleted item ${currentItem.value.long_desc}(${currentItem.value.item})");
      _notificationService.success("Item deleted");
      await Get.offNamed(AppRoutes.ITEMS, id: AppRoutes.id);
    } catch (err, stack) {
      _logger.severe("Error deleting item", err, stack);
    }
  }

  Future<void> editUpdateItemsOnDelete(String uuid) async {
    final QueryResult<Object> itemRes = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
           query GET_MOD_ITEMS {
              item(where: {document: {_cast: {String: {_ilike: "%$uuid%"}}}}) {
                created_at
                department
                created_by
                document
                item
                long_desc
                upc
                updated_at
                updated_by
              }
            }
          ''',
        ),
      ),
    );
    if (itemRes.hasException) {
      return _notificationService.error(itemRes.exception.toString());
    }
    // ignore: always_specify_types
    final List<Item> iList = (itemRes.data!["item"] as List).map((dynamic e) => Item.fromJson(e as Map<String, dynamic>)).toList();

    for (final Item i in iList) {
      if (i.document.modifiers[uuid] != null || i.document.liquorList.contains(uuid)) {
        i.document.modifiers.remove(uuid);
        i.document.liquorList.removeWhere((String s) => s == uuid);
        _logger.shout(
            '${CURRENT_EMPLOYEE.value.employee_full_name} removed modifier ${currentItem.value.long_desc}(${currentItem.value.item}) from ${i.long_desc}(${i.item})');
        final QueryResult<Object> updateRes = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString(
              '''
                mutation UPDATE_ITEM_BY_PK(\$item: uuid!, \$document: jsonb) {
                  update_item_by_pk(pk_columns: {item: \$item}, _set: {document: \$document}) {
                    department
                    document
                    item
                    long_desc
                    upc
                  }
                }
              ''',
            ),
            variables: <String, dynamic>{
              "item": i.item,
              "document": i.document.toJson(),
            },
          ),
        );

        if (updateRes.hasException) {
          return _notificationService.error(itemRes.exception.toString());
        }
      }
    }
  }

  Future<void> addItem() async {
    try {
      if (!viewItemFormKey.currentState!.validate()) throw "Invalid form";

      if (item != null) {
        upcError = upcController.text != "" && upcController.text == item!.upc;
        liqCtlError = liqCtlPluController.text != "" && liqCtlPluController.text == item!.liq_ctl_plu.toString();
        descError = longDescController.text == item!.long_desc;
        final int errCnt = <bool>[upcError, liqCtlError, descError].where((bool b) => b).length;
        if (upcError || descError) {
          final StringBuffer buffer = StringBuffer();
          buffer.writeAll(<String>[
            if (upcError) "UPC",
            if (upcError && descError) ", ",
            if (descError) "${upcError ? "d" : "D"}escription",
            if (liqCtlError && (upcError || descError)) ", ",
            if (liqCtlError) "${upcError || descError ? "l" : "L"}iquor Ctrl PLU",
            if (errCnt < 2) " ",
            "must be different to create new.",
          ]);
          _notificationService.error(buffer.toString());
          viewItemFormKey.currentState!.validate();
          upcError = false;
          descError = false;
          return;
        }
      }

      prepareCurrentItemForUpdate();

      if (await hasDupe()) return;

      final Map<String, dynamic> santizeditem = Helpers.sanitizeEntity(
        currentItem.value.toJson(),
        <String>[
          'item',
          'created_at',
          'updated_at',
          'departmentByDepartment',
        ],
      );

      final QueryResult<Object> addItemResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
                  mutation ADD_ITEM(\$item: item_insert_input!) {
                    insert_item_one(object: \$item) {
                      item
                    }
                  }
                ''',
          ),
          variables: <String, dynamic>{
            "item": santizeditem,
          },
        ),
      );
      if (addItemResult.hasException) {
        throw addItemResult.exception!.graphqlErrors.toString();
      }
      final String newItemUUID = (addItemResult.data!['insert_item_one'] as Map<String, dynamic>)['item'].toString();
      _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} created item - ${currentItem.value.long_desc} ($newItemUUID)');
      _notificationService.success("Item added succesfully");

      if (addMultiple) {
        wipeInputs();
      } else {
        Get.back(id: item == null ? null : AppRoutes.id);
      }
    } catch (err, stack) {
      _logger.severe("Error adding item", err, stack);
      _notificationService.error('Error Adding Item');
    }
  }

  Future<bool> hasDupe() async {
    final bool hasUpc = currentItem.value.upc != "";
    final bool hasPlu = currentItem.value.liq_ctl_plu != null;

    final QueryResult<Object> dupeCheckResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(${hasPlu ? "\$liq_ctl_plu: smallint, " : ""}${hasUpc ? "\$upc: String, " : ""}\$long_desc: String = "", \$item: uuid = "") {
              item(where: {_or: [${hasPlu ? "{liq_ctl_plu: {_eq: \$liq_ctl_plu}}, " : ""}${hasUpc ? "{upc: {_eq: \$upc}}, " : ""}{long_desc: {_eq: \$long_desc}}], _and: {item: {_neq: \$item}}}) {
                item
                department
                document
                long_desc
                upc
                created_at
                updated_at
                created_by
                updated_by
                liq_ctl_plu
              }
            }
          ''',
        ),
        variables: <String, dynamic>{
          if (hasUpc) "upc": currentItem.value.upc,
          if (hasPlu) "liq_ctl_plu": currentItem.value.liq_ctl_plu,
          "long_desc": currentItem.value.long_desc,
          "item": item == null ? "00000000-0000-0000-0000-000000000000" : item!.item,
        },
      ),
    );

    if (dupeCheckResult.hasException) {
      _notificationService.error(dupeCheckResult.exception!.graphqlErrors.toString());
      return true;
    }

    if ((dupeCheckResult.data!['item']! as List<dynamic>).isNotEmpty) {
      final Item existingItem = Item.fromJson(
        dupeCheckResult.data!['item']![0] as Map<String, dynamic>,
      );
      final bool sameUpc = hasUpc && currentItem.value.upc == existingItem.upc;
      final bool samePlu = hasPlu && currentItem.value.liq_ctl_plu == existingItem.liq_ctl_plu;
      final bool sameDesc = currentItem.value.long_desc == existingItem.long_desc;
      final dynamic dialogRes = await Get.defaultDialog(
        title:
            "Item found with same ${samePlu ? "Liquor PLU" : ""}${samePlu && (sameUpc || sameDesc) ? " and " : ""}${sameUpc ? "UPC" : ""}${sameUpc && sameDesc ? " and " : ""}${sameDesc ? "description" : ""}!",
        content: AddDuplicateItemDialog(
          item: existingItem,
        ),
        barrierDismissible: false,
      );
      if (dialogRes == true) {
        Get.back(id: item == null ? null : AppRoutes.id);
        await Future<void>.delayed(const Duration(milliseconds: 400));
        await _itemsController.viewItemPermission(existingItem);
      }
      return true;
    }
    return false;
  }

  void wipeInputs() {
    if (!longDescLock.value) longDescController.clear();
    if (!receiptDescLock.value) receiptDescController.clear();
    if (!priceLock.value) {
      itemPricing = <String, int>{"S0L0C0": 0};
      priceController.clear();
      cashPriceController.clear();
    }
    if (!defModPriceLock.value) defModPricing = <String, int>{"S0L0C0": 0};
    if (!upcLock.value) upcController.clear();
    if (!liqCtlLock.value) liqCtlPluController.clear();
    if (!departmentLock.value) assignedDepartmentId.value = "";
    if (!allowEbtLock.value) allowEbt.value = false;
    if (!pinToTopLock.value) pinToTop.value = false;
    if (!negativeItemLock.value) negativeItem.value = false;
    if (!surchargeLock.value) takeOutSurcharge.value = false;
    if (!printInRedLock.value) printInRed.value = false;
    if (!promptForPriceLock.value) promptForPrice.value = false;
    if (!countLock.value) countController.text = "-";
    if (!isModLock.value) isModifier.value = false;
    if (!modDescLock.value) modDescController.text = "";
    if (!multiModLock.value) multiModLists.value = false;
    if (!passDescLock.value) passDesc.value = false;
    if (!overridePricingLock.value) overridePricing.value = false;
    if (!minModCountLock.value) minModController.text = "-";
    if (!maxModCountLock.value) maxModController.text = "-";
    if (!printSeparateLock.value) printSeparate.value = false;
    if (!showOnlineLock.value) showOnline.value = false;

    if (!isWeightedLock.value) {
      isWeighted.value = false;
      assignedUom = null.obs;
    }
    if (!uomLock.value) assignedUom = null.obs;
  }

  void isWeightedItem() {
    isWeighted.value = !isWeighted.value;

    if (!isWeighted.value) {
      assignedUom = null.obs;
    }
  }

  void setUom(int? value) {
    assignedUom = value.obs;
  }

  Future<void> loadDepartments() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query MyQuery {
                department(order_by: {title: asc}) {
                  created_at
                  created_by
                  department
                  document
                  title
                  updated_at
                  updated_by
                }
              }

            ''',
          ),
        ),
      );

      if (departmentResult.hasException) {
        return _notificationService.error(departmentResult.exception!.graphqlErrors.toString());
      }

      availableDepartments.value = (departmentResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic department) => Department.fromJson(department as Map<String, dynamic>),
          )
          .toList();
    } catch (err, stack) {
      _logger.severe("Error loading departments", err, stack);
    }
  }

  Future<void> getMerchantSettings() async {
    try {
      final QueryResult<Object> merchantSettingsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_SYSTEM_SETTINGS(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                record_key
                document
                updated_at
                }
              }
            ''',
          ),
          variables: const <String, dynamic>{
            "_eq": "merchant",
          },
        ),
      );

      merchantDoc.value = MerchantJsonRecord.fromJson(
        (merchantSettingsResult.data!['json_record'] as List<dynamic>)[0] as Map<String, dynamic>,
      );
    } catch (e, stack) {
      _logger.severe("Error Loading Merchant Settings", e, stack);
      return _notificationService.error(e.toString());
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Items",
      _graphqlService,
    );
    canEdit.value = edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Items",
      _graphqlService,
    );
    canDelete.value = delete;
  }

  Future<void> createPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Items",
      _graphqlService,
    );
    canCreate.value = create;
  }

  Future<void> animateDown() async {
    controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    animation = ColorTween(begin: R2Colors.neutral600, end: Colors.red).animate(controller);
    animation.addListener(() {
      color.value = animation.value!;
    });

    await controller.forward();
    await controller.reverse();
  }

  Future<void> animateUp() async {
    controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    animation = ColorTween(
      begin: R2Colors.neutral600,
      end: R2Colors.green700,
    ).animate(controller);

    animation.addListener(() {
      color.value = animation.value!;
    });

    await controller.forward();
    await controller.reverse();
  }

  void scrollToTop() {
    Future<void>.delayed(Duration.zero, () {
      scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.linear,
      );
    });
  }

  void scrollToBottom() {
    Future<void>.delayed(Duration.zero, () {
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.linear,
      );
    });
  }

  Map<String, Map<String, dynamic>> findPricingDifferences(Map<String, dynamic> itemPricing, Map<String, dynamic> originalPricing) {
    final Map<String, Map<String, dynamic>> differences = {};

    // Check for keys in itemPricing that are not in originalPricing or have different values
    for (final String key in itemPricing.keys) {
      if (!originalPricing.containsKey(key)) {
        differences[key] = {'itemPricing': itemPricing[key], 'originaPricing': 'missing'};
      } else if (itemPricing[key] != originalPricing[key]) {
        differences[key] = {'itemPricing': itemPricing[key], 'originalPricing': originalPricing[key]};
      }
    }

    // Check for keys in originalPricing that are not in itemPricing
    for (final String key in originalPricing.keys) {
      if (!itemPricing.containsKey(key)) {
        differences[key] = {'itemPricing': 'missing', 'originalPricing': originalPricing[key]};
      }
    }

    return differences;
  }
}
