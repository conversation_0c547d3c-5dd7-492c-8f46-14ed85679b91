import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/cole_keyboard/widget.dart';
import 'package:backoffice/app/global_widgets/custom_paginated_table/widget.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/content_locker.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_2_line_text_field.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/global_widgets/prep_device_select/widget.dart';
import 'package:backoffice/app/global_widgets/selected_item_list.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/Items/dialogs/edit_modifiers/dialog.dart';
import 'package:backoffice/app/modules/Items/dialogs/pricing_array/dialog.dart';
import 'package:backoffice/app/modules/Items/dialogs/view_item/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/liquor_plu_desc.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class ViewItemDialog extends GetView<ViewItemDialogController> {
  ViewItemDialog({
    this.item,
    this.addMultiple = false,
  });

  Item? item;
  bool addMultiple;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ViewItemDialogController>(
      init: ViewItemDialogController(item: item, addMultiple: addMultiple),
      builder: (ViewItemDialogController controller) {
        return Scaffold(
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(200),
            child: Header(
              title: item != null
                  ? "Items View"
                  : addMultiple
                      ? "Add Multiple Items"
                      : "Add Item",
              transparentBackground: item != null,
              leftButton: DialogButton(
                buttonType: EDialogButtonType.BACK,
                buttonText: item == null ? "Close" : "Items",
                onTapped: () {
                  if (item != null) {
                    Get.back(id: AppRoutes.id);
                  } else {
                    Get.back();
                  }
                },
              ),
              rightButton: item != null
                  ? Obx(
                      () => DialogButton(
                        buttonType: EDialogButtonType.ADD,
                        disabled: !controller.canEdit.value,
                        onTapped: () async {
                          await controller.updateItem();
                        },
                        buttonText: "Update Item",
                      ),
                    )
                  : null,
            ),
          ),
          body: FormWrapper(
            formKey: controller.viewItemFormKey,
            scrollController: controller.scrollController,
            maxWidth: 500,
            children: <Widget>[
              Obx(
                () => MenuGroup(
                  title: "Info",
                  children: <Widget>[
                    if (item != null)
                      MenuTextField(
                        label: "Description",
                        controller: controller.longDescController,
                        enabled: controller.canEdit.value,
                        maxLength: 50,
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            controller.scrollToTop();
                            return 'Enter a valid Description';
                          }
                          if (controller.descError) {
                            controller.scrollToTop();
                            return "";
                          }
                          return null;
                        },
                      )
                    else
                      Menu2LineTextField(
                        checkValue: controller.useLongDesc,
                        checkOnChanged: (bool? value) {
                          controller.useLongDesc.value = !controller.useLongDesc.value;
                          controller.receiptDescController.clear();
                        },
                        checkTitle: 'Use for Receipt Description',
                        label: "Description",
                        controller: controller.longDescController,
                        maxLength: 50,
                        hintText: "",
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                      ),
                    if (!controller.useLongDesc.value || item != null)
                      MenuTextField(
                        leadingIcon: item == null && addMultiple
                            ? ContentLocker(
                                controller.receiptDescLock,
                              )
                            : null,
                        label: "Receipt Description",
                        controller: controller.receiptDescController,
                        enabled: controller.canEdit.value,
                        maxLength: 50,
                        hintText: "",
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            controller.scrollToTop();
                            return 'Enter a valid Description';
                          }
                          return null;
                        },
                      ),
                    MenuTextField(
                      label: "Online Description",
                      controller: controller.onlineDescController,
                      enabled: controller.canEdit.value,
                      maxLength: 50,
                      validator: (String? value) {
                        if (value == null || value.isEmpty) {
                          controller.scrollToTop();
                          return 'Enter a valid Description';
                        }
                        return null;
                      },
                    ),
                    MenuTextField(
                      middleIconButton: controller.merchantDoc.value.document.dualPricing
                          ? IconButton(
                              onPressed: () {
                                controller.itemPricing['S0L0C0'] = Helpers.getCreditPrice(
                                  cashAmount: controller.itemPricing['S0L0C1'],
                                  creditAmount: 0,
                                  dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                                  roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                                );
                                controller.priceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C0'] ?? 0)}";
                              },
                              icon: const Icon(
                                Icons.refresh,
                                color: R2Colors.primary500,
                              ),
                            )
                          : null,
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.priceLock) : null,
                      label: "${controller.merchantDoc.value.document.dualPricing ? "Credit " : ""}Price",
                      hintText: "",
                      controller: controller.priceController,
                      enabled: controller.canEdit.value,
                      onConfirm: (String value) {
                        controller.itemPricing['S0L0C0'] = int.parse(
                          value.replaceAll(RegExp('[^0-9]'), ''),
                        );

                        if ((controller.itemPricing['S0L0C1'] ?? 0) == 0 || (controller.itemPricing['S0L0C0'] ?? 0) == 0) {
                          controller.itemPricing['S0L0C1'] = Helpers.getCashPrice(
                            cashAmount: 0,
                            creditAmount: controller.itemPricing['S0L0C0'],
                            dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                            roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                          );
                          controller.priceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C0'] ?? 0)}";
                          controller.cashPriceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C1'] ?? 0)}";
                        }
                      },
                      validator: (String? value) {
                        if (value == null || value.isEmpty) {
                          controller.priceController.text = '0';
                        }
                        return null;
                      },
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.deny(
                          RegExp("[A-Za-z]"),
                        ),
                        CurrencyTextInputFormatter.currency(
                          decimalDigits: 2,
                          symbol: "",
                        ),
                      ],
                    ),
                    if (controller.merchantDoc.value.document.dualPricing)
                      MenuTextField(
                        leadingIcon: item == null && addMultiple ? ContentLocker(controller.priceLock) : null,
                        middleIconButton: controller.merchantDoc.value.document.dualPricing
                            ? IconButton(
                                onPressed: () {
                                  controller.itemPricing['S0L0C1'] = Helpers.getCashPrice(
                                    cashAmount: 0,
                                    creditAmount: controller.itemPricing['S0L0C0'],
                                    dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                                    roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                                  );
                                  controller.cashPriceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C1'] ?? 0)}";
                                },
                                icon: const Icon(
                                  Icons.refresh,
                                  color: R2Colors.primary500,
                                ),
                              )
                            : null,
                        label: "Cash Price",
                        hintText: "",
                        controller: controller.cashPriceController,
                        enabled: controller.canEdit.value,
                        onConfirm: (String value) {
                          controller.itemPricing['S0L0C1'] = int.parse(
                            value.replaceAll(RegExp('[^0-9]'), ''),
                          );
                          if ((controller.itemPricing['S0L0C1'] ?? 0) == 0 || (controller.itemPricing['S0L0C0'] ?? 0) == 0) {
                            controller.itemPricing['S0L0C0'] = Helpers.getCreditPrice(
                              cashAmount: controller.itemPricing['S0L0C1'],
                              creditAmount: 0,
                              dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                              roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                            );
                          }
                          controller.priceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C0'] ?? 0)}";
                          controller.cashPriceController.text = "\$${Helpers.formatCurrency(controller.itemPricing['S0L0C1'] ?? 0)}";
                        },
                        validator: (String? value) {
                          if (value == null || value.isEmpty) {
                            controller.priceController.text = '0';
                          }
                          return null;
                        },
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.deny(
                            RegExp("[A-Za-z]"),
                          ),
                          CurrencyTextInputFormatter.currency(
                            decimalDigits: 2,
                            symbol: "",
                          ),
                        ],
                      ),
                    MenuTextField(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.upcLock) : null,
                      label: "UPC",
                      controller: controller.upcController,
                      enabled: controller.canEdit.value,
                      hintText: "",
                      maxLength: 16,
                      validator: (Object? value) {
                        if (controller.upcError) {
                          controller.scrollToTop();
                          return "";
                        }
                        return null;
                      },
                    ),
                    MenuTextField(
                      middleIconButton: IconButton(
                        icon: const Icon(Icons.search),
                        onPressed: () async {
                          final RxInt offset = 0.obs;
                          final RxInt modPageSize = 10.obs;
                          final RxString param = "".obs;
                          final double padding = Get.width / 8;
                          await Get.bottomSheet<LiquorPluDesc>(
                            Padding(
                              padding: EdgeInsets.fromLTRB(padding, padding, padding, 0),
                              child: Material(
                                color: R2Colors.white,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                ),
                                child: Column(
                                  children: <Widget>[
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 20),
                                      child: Row(
                                        children: <Widget>[
                                          DialogButton(
                                            buttonType: EDialogButtonType.BACK,
                                            onTapped: () {
                                              Get.back();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.all(20),
                                        child: CustomPaginatedTable(
                                          offset: offset,
                                          pageSize: modPageSize,
                                          param: param,
                                          prefixParamMatchOnly: false,
                                          selectionKey: "liq_ctl_plu_desc",
                                          columnConfigs: <PaginatedColumnConfig>[
                                            PaginatedColumnConfig(
                                              value: "liq_ctl_plu",
                                              title: "PLU",
                                              columnSize: ColumnSize.L,
                                            ),
                                            PaginatedColumnConfig(
                                              value: "desc",
                                              title: "Description",
                                              columnSize: ColumnSize.S,
                                            ),
                                          ],
                                          onRowTap: (
                                            Map<String, dynamic> i,
                                          ) async {
                                            Get.back(result: LiquorPluDesc.fromJson(i));
                                          },
                                          subscriptionName: "liq_ctl_plu_desc",
                                          aggregateSubscription: '''
                                            subscription GET_LIQ_CTL_PLU_DESC_AGGREGATE(\$param: String ) {
                                              liq_ctl_plu_desc_aggregate(where: {desc: {_ilike: \$param}}) {
                                                aggregate {
                                                  count
                                                }
                                              }
                                            }
                                          ''',
                                          dataSubscription: '''
                                            subscription GET_LIQ_CTL_PLU_DESC(\$limit: Int!, \$offset: Int!, \$order_by: [liq_ctl_plu_desc_order_by!], \$param: String = "") {
                                              liq_ctl_plu_desc(limit: \$limit, offset: \$offset, order_by: \$order_by, where: {desc: {_ilike: \$param}}) {
                                                liq_ctl_plu_desc
                                                liq_ctl_plu
                                                desc
                                              }
                                            }
                                          ''',
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            isDismissible: false,
                            isScrollControlled: true,
                            enableDrag: false,
                          ).then((LiquorPluDesc? value) {
                            if (value != null) {
                              controller.liqCtlPluController.text = value.liq_ctl_plu.toString();
                            }
                          });
                        },
                      ),
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.liqCtlLock) : null,
                      label: "Liquor Control PLU",
                      controller: controller.liqCtlPluController,
                      enabled: controller.canEdit.value,
                      hintText: "",
                      maxLength: 4,
                      validator: (Object? value) {
                        if (controller.liqCtlError) {
                          controller.scrollToTop();
                          return "";
                        }
                        return null;
                      },
                    ),
                    MenuDropdown<String>(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.departmentLock) : null,
                      title: "Department",
                      value: controller.assignedDepartmentId.value.isEmpty ? null : controller.assignedDepartmentId.value,
                      hint: const Text("Required"),
                      onChanged: controller.canEdit.value
                          ? (String? newValue) {
                              controller.assignedDepartmentId.value = newValue ?? "";
                            }
                          : null,
                      items: controller.availableDepartments.map((Department department) {
                        return DropdownMenuItem<String>(
                          value: department.department,
                          child: Center(child: Text(department.title)),
                        );
                      }).toList(),
                      validator: (Object? value) {
                        if (value == null) {
                          return '';
                        }
                        return null;
                      },
                    ),
                    MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.isWeightedLock) : null,
                      text: "Weighted Item",
                      value: controller.isWeighted.value,
                      onChanged: (bool? val) {
                        if (controller.canEdit.value) {
                          controller.isWeightedItem();
                        }
                      },
                    ),
                    Obx(
                      () {
                        return MenuTextField(
                          middleIconButton: IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () {
                              controller.countController.text = "-";
                            },
                          ),
                          leadingIcon: item == null && addMultiple ? ContentLocker(controller.countLock) : null,
                          label: "Count",
                          textAlign: TextAlign.center,
                          onConfirm: (String? value) {
                            if (controller.countController.text == "" || controller.countController.text == "-") {
                              controller.countController.text = "-";
                            } else {
                              final int parsedVal = int.parse(
                                controller.countController.text,
                              );
                              if (parsedVal > 1000000) {
                                controller.countController.text = "1000000";
                              } else if (parsedVal < 0) {
                                controller.countController.text = "-";
                              } else {
                                controller.countController.text = parsedVal.toString();
                              }
                            }
                            controller.currentItem.refresh();
                          },
                          initialValue:
                              controller.currentItem.value.document.count < 0 ? "-" : controller.currentItem.value.document.count.toString(),
                          maxLength: 3,
                          controller: controller.countController,
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.allow(
                              RegExp("[0-9]"),
                            ),
                          ],
                        );
                      },
                    ),
                    if (controller.isWeighted.value == true)
                      Column(
                        children: <Widget>[
                          MenuComponent.divider,
                          MenuDropdown<int>(
                            leadingIcon: item == null && addMultiple ? ContentLocker(controller.uomLock) : null,
                            title: "Unit of Measure",
                            hint: const Padding(
                              padding: EdgeInsets.only(left: 10),
                              child: Text('Required'),
                            ),
                            value: controller.assignedUom.value,
                            validator: (Object? value) {
                              if (controller.assignedUom.value == null) {
                                return 'Select a UOM';
                              }
                              return null;
                            },
                            onChanged: controller.canEdit.value == true
                                ? (int? newValue) {
                                    controller.setUom(newValue);
                                  }
                                : null,
                            items: const <DropdownMenuItem<int>>[
                              DropdownMenuItem<int>(
                                value: 0,
                                child: Text('lb'),
                              ),
                              DropdownMenuItem<int>(
                                value: 1,
                                child: Text('oz'),
                              ),
                              DropdownMenuItem<int>(
                                value: 2,
                                child: Text('g'),
                              ),
                              DropdownMenuItem<int>(
                                value: 3,
                                child: Text('kg'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.isModLock) : null,
                      text: "Is a Modifier",
                      textColor: R2Colors.primary500,
                      value: controller.isModifier.value,
                      onChanged: (bool? val) {
                        controller.isModifier.value = val!;
                      },
                    ),
                    MenuTextField(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.modDescLock) : null,
                      label: "Modifier Description",
                      controller: controller.modDescController,
                      maxLength: 50,
                      hintText: "",
                    ),
                  ],
                ),
              ),
              MenuGroup(
                title: "Extended Info",
                children: <Obx>[
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.allowEbtLock) : null,
                      text: "Allow EBT Tender",
                      value: controller.allowEbt.value,
                      onChanged: (bool? val) {
                        controller.allowEbt.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.promptForPriceLock) : null,
                      text: "Prompt For Price",
                      value: controller.promptForPrice.value,
                      onChanged: (bool? val) {
                        controller.promptForPrice.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.pinToTopLock) : null,
                      text: "Pin to Top",
                      value: controller.pinToTop.value,
                      onChanged: (bool? val) {
                        controller.pinToTop.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.negativeItemLock) : null,
                      text: "Negative Item",
                      value: controller.negativeItem.value,
                      onChanged: (bool? val) {
                        controller.negativeItem.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.showOnlineLock) : null,
                      text: "Show Online",
                      value: controller.showOnline.value,
                      onChanged: (bool? val) {
                        controller.showOnline.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.surchargeLock) : null,
                      text: "Takeout fee",
                      value: controller.takeOutSurcharge.value,
                      onChanged: (bool? val) {
                        controller.takeOutSurcharge.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.printInRedLock) : null,
                      text: "Print in Red",
                      value: controller.printInRed.value,
                      onChanged: (bool? val) {
                        controller.printInRed.value = val ?? false;
                      },
                    ),
                  ),
                ],
              ),
              MenuGroup(
                title: "Pricing",
                children: <Widget>[
                  DialogButton(
                    buttonType: EDialogButtonType.ADD,
                    buttonText: "Edit Pricing",
                    onTapped: () async {
                      await Get.bottomSheet(
                        ThinBottomSheet(
                          maxWidth: 900,
                          child: PriceScheduleArray(
                            pricing: controller.itemPricing,
                            editPermission: controller.canEdit.value,
                            dualPricing: controller.merchantDoc.value.document.dualPricing,
                            dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                            roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                          ),
                        ),
                        isScrollControlled: true,
                      ).then((dynamic value) {
                        controller.priceController.text = "\$${Helpers.formatCurrency(controller.itemPricing["S0L0C0"] ?? 0)}";
                        controller.cashPriceController.text = "\$${Helpers.formatCurrency(controller.itemPricing["S0L0C1"] ?? 0)}";
                        controller.canEdit.refresh();
                      });
                    },
                  ),
                ],
              ),
              MenuGroup(
                title: "Modifiers",
                children: <Widget>[
                  DialogButton(
                    buttonType: EDialogButtonType.ADD,
                    buttonText: "Modifiers",
                    onTapped: () async {
                      await Get.bottomSheet(
                        EditModifiersDialog(
                          editPermission: controller.canEdit.value,
                        ),
                        backgroundColor: R2Colors.white,
                        isScrollControlled: true,
                      );
                    },
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: Column(
                        children: <Widget>[
                          if (item == null && addMultiple)
                            ContentLocker(
                              controller.overridePricingLock,
                            ),
                          const SizedBox(
                            width: 300,
                            child: MenuLabel(
                              text: "Override modifiers' pricing (accessible in \"edit modifiers\" menu)",
                              wrapText: true,
                              textColor: R2Colors.primary500,
                            ),
                          ),
                        ],
                      ),
                      value: controller.overridePricing.value,
                      onChanged: (bool? val) {
                        controller.overridePricing.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => Column(
                      children: <Widget>[
                        if (item == null && addMultiple) ContentLocker(controller.defModPriceLock),
                        DialogButton(
                          disabled: !controller.overridePricing.value,
                          buttonType: EDialogButtonType.ADD,
                          buttonText: "Change Default Modifier Pricing",
                          onTapped: () async {
                            await Get.bottomSheet(
                              ThinBottomSheet(
                                maxWidth: 900,
                                child: PriceScheduleArray(
                                  pricing: controller.defModPricing,
                                  editPermission: controller.canEdit.value,
                                  title: "Default Modifier Pricing",
                                  dualPricing: controller.merchantDoc.value.document.dualPricing,
                                  dualPercent: controller.merchantDoc.value.document.dualPricingPercent,
                                  roundAmount: controller.merchantDoc.value.document.dualPricingRoundAmount,
                                ),
                              ),
                              isScrollControlled: true,
                            );
                          },
                        ),
                        const Text(
                          "(Enabled with mod pricing override)",
                          style: TextStyle(color: R2Colors.neutral400),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: Column(
                        children: <Widget>[
                          if (item == null && addMultiple) ContentLocker(controller.multiModLock),
                          const SizedBox(
                            width: 300,
                            child: MenuLabel(
                              text: "Modifiers appear as lists with their modifiers as selections (MultiList Selection)",
                              wrapText: true,
                            ),
                          ),
                        ],
                      ),
                      value: controller.multiModLists.value,
                      onChanged: (bool? val) {
                        controller.multiModLists.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: Column(
                        children: <Widget>[
                          if (item == null && addMultiple) ContentLocker(controller.passDescLock),
                          const SizedBox(
                            width: 300,
                            child: MenuLabel(
                              text: "Add this item's modifier description to it's modifiers (Example: \"Add - Bacon\")",
                              wrapText: true,
                            ),
                          ),
                        ],
                      ),
                      value: controller.passDesc.value,
                      onChanged: (bool? val) {
                        controller.passDesc.value = val!;
                      },
                    ),
                  ),
                  Obx(
                    () {
                      return MenuTextField(
                        leadingIcon: item == null && addMultiple ? ContentLocker(controller.minModCountLock) : null,
                        label: "Min Modifier Count",
                        middleIconButton: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            controller.minModController.text = "-";
                          },
                        ),
                        textAlign: TextAlign.center,
                        onConfirm: (String? value) {
                          if (controller.minModController.text == "" || controller.minModController.text == "-") {
                            controller.minModController.text = "-";
                          } else {
                            final int parsedVal = int.parse(
                              controller.minModController.text,
                            );
                            if (parsedVal > 100) {
                              controller.minModController.text = "100";
                            } else if (parsedVal < 1) {
                              controller.minModController.text = "-";
                            } else {
                              controller.minModController.text = parsedVal.toString();
                            }
                          }
                          controller.currentItem.refresh();
                        },
                        initialValue:
                            controller.currentItem.value.document.modMinSel == 0 ? "-" : controller.currentItem.value.document.modMinSel.toString(),
                        maxLength: 3,
                        controller: controller.minModController,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.allow(
                            RegExp("[0-9]"),
                          ),
                        ],
                        validator: (String? value) {
                          if (value != "-") {
                            if (controller.currentItem.value.document.modifiers.length < int.parse(value ?? "0")) {
                              controller.scrollToBottom();
                              return 'Not enough modifiers for min';
                            }
                          }
                          return null;
                        },
                      );
                    },
                  ),
                  Obx(
                    () {
                      return MenuTextField(
                        label: "Max Modifier Count",
                        leadingIcon: item == null && addMultiple ? ContentLocker(controller.maxModCountLock) : null,
                        middleIconButton: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            controller.maxModController.text = "-";
                          },
                        ),
                        textAlign: TextAlign.center,
                        onConfirm: (String? value) {
                          if (controller.maxModController.text == "" || controller.maxModController.text == "-") {
                            controller.maxModController.text = "-";
                          } else {
                            final int parsedVal = int.parse(
                              controller.maxModController.text,
                            );
                            if (parsedVal > 100) {
                              controller.maxModController.text = "100";
                            } else if (parsedVal < 1) {
                              controller.maxModController.text = "-";
                            } else {
                              controller.maxModController.text = parsedVal.toString();
                            }
                          }
                          controller.currentItem.refresh();
                        },
                        initialValue:
                            controller.currentItem.value.document.modMaxSel == 0 ? "-" : controller.currentItem.value.document.modMaxSel.toString(),
                        maxLength: 3,
                        controller: controller.maxModController,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.allow(
                            RegExp("[0-9]"),
                          ),
                        ],
                        validator: (String? value) {
                          if (value != "-") {
                            if (int.parse(
                                  controller.minModController.text == "-" ? "0" : controller.minModController.text,
                                ) >
                                int.parse(value ?? "0")) {
                              controller.scrollToBottom();
                              return "Max can't be less than min";
                            }
                          }
                          return null;
                        },
                      );
                    },
                  ),
                ],
              ),
              MenuGroup(
                title: "Prep Devices",
                children: <Widget>[
                  Obx(
                    () => MenuCheckbox(
                      leadingIcon: item == null && addMultiple ? ContentLocker(controller.printSeparateLock) : null,
                      text: "Print Separate",
                      value: controller.selectedDevices.value > 0 && controller.printSeparate.value,
                      onChanged: controller.selectedDevices.value > 0
                          ? (bool? val) {
                              controller.printSeparate.value = val!;
                            }
                          : null,
                    ),
                  ),
                  PrepDeviceSelect(
                    selectedDevices: controller.selectedDevices,
                  ),
                ],
              ),
              MenuGroup(
                title: "Detailed Description",
                children: <Widget>[
                  ColeVirtualKeyboardWrapper(
                    textEditingController: controller.detailedDescController,
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(10.0),
                      ),
                      controller: controller.detailedDescController,
                      maxLines: null,
                    ),
                  ),
                ],
              ),
              MenuGroup(
                title: "Liquor to Pour",
                children: <Widget>[
                  SelectedItemList(
                    selectedItems: controller.selectedLiquor,
                    liquorOnly: true,
                  ),
                ],
              ),
              if (item != null)
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Obx(
                    () => Column(
                      children: <Widget>[
                        DialogButton(
                          buttonType: EDialogButtonType.DESTRUCTIVE,
                          disabled: !controller.canDelete.value,
                          buttonText: "Delete Item",
                          onTapped: () async {
                            await controller.deleteItem();
                          },
                        ),
                        DialogButton(
                          buttonType: EDialogButtonType.ADD,
                          disabled: !controller.canCreate.value,
                          buttonText: "Create New From Current",
                          onTapped: () async {
                            await controller.addItem();
                          },
                        ),
                      ],
                    ),
                  ),
                )
              else
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: MaterialButton(
                    height: 70,
                    minWidth: 400,
                    color: R2Colors.primary500,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    onPressed: () async {
                      await controller.addItem();
                    },
                    child: const Text(
                      "Add Item",
                      style: TextStyle(
                        color: R2Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
