import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class DuplicateViewItemDialogController extends GetxController {
  DuplicateViewItemDialogController(this.item);

  Item item;
  final GraphqlService _graphqlService = Get.find();
  final Logger _logger = Logger('DupItemController');

  final NotificationService _notificationService = Get.find();

  RxString deptTitle = "".obs;

  RxList<Department> availableDepartments = <Department>[].obs;

  @override
  Future<void> onInit() async {
    await loadDepartments();
    deptTitle.value = availableDepartments
        .firstWhere(
          (Department department) => item.department == department.department,
        )
        .title;

    super.onInit();
  }

  Future<void> loadDepartments() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query MyQuery {
                department {
                  created_at
                  created_by
                  department
                  document
                  title
                  updated_at
                  updated_by
                }
              }

            ''',
          ),
        ),
      );

      if (departmentResult.hasException) {
        return _notificationService.error(departmentResult.exception!.graphqlErrors.toString());
      }

      availableDepartments.value = (departmentResult.data!['department'] as List<dynamic>)
          .map(
            (dynamic department) => Department.fromJson(department as Map<String, dynamic>),
          )
          .toList();
    } catch (err, stack) {
      _logger.severe('Error Loading Departments', err, stack);
    }
  }

  void close() {
    Get.back();
  }
}
