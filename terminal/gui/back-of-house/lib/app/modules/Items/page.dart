import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/custom_paginated_table/widget.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/modules/Items/controller.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ItemsPage extends GetView<ItemsController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Header(
          title: "Items",
          rightButton: DialogButton(
            buttonType: EDialogButtonType.ADD,
            onTapped: () async => showCupertinoModalPopup(
              context: context,
              builder: (BuildContext context) => CupertinoActionSheet(
                title: const Text("Select Method"),
                actions: <CupertinoActionSheetAction>[
                  CupertinoActionSheetAction(
                    child: const Text(
                      'Add Item',
                      style: TextStyle(
                        color: R2Colors.primary500,
                      ),
                    ),
                    onPressed: () async {
                      Get.back();
                      controller.createItemPermission();
                    },
                  ),
                  CupertinoActionSheetAction(
                    child: const Text(
                      'Add Multiple Items',
                      style: TextStyle(
                        color: R2Colors.primary500,
                      ),
                    ),
                    onPressed: () async {
                      Get.back();
                      controller.createMultipleItemPermission();
                    },
                  ),
                  CupertinoActionSheetAction(
                    child: const Text(
                      'Import Items...',
                      style: TextStyle(
                        color: R2Colors.primary500,
                      ),
                    ),
                    onPressed: () async {
                      controller.importItemPermission();
                    },
                  ),
                  CupertinoActionSheetAction(
                    child: const Text(
                      'Mass Attribute',
                      style: TextStyle(
                        color: R2Colors.primary500,
                      ),
                    ),
                    onPressed: () async {
                      controller.massAttributePermission();
                    },
                  ),
                  CupertinoActionSheetAction(
                    child: const Text(
                      'Mass Delete',
                      style: TextStyle(
                        color: R2Colors.primary500,
                      ),
                    ),
                    onPressed: () async {
                      controller.massDeletePermission();
                    },
                  ),
                  if (CURRENT_EMPLOYEE.value.id == 1000)
                    CupertinoActionSheetAction(
                      child: const Text(
                        'Export Items...',
                        style: TextStyle(
                          color: R2Colors.primary500,
                        ),
                      ),
                      onPressed: () async {
                        controller.exportItemPermission();
                      },
                    ),
                ],
              ),
            ),
            buttonText: "Item Maintenance",
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Obx(
                () => CustomPaginatedTable(
                  prefixParamMatchOnly: false,
                  additionalHeaderWidget: Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: Get.width * 0.08),
                      child: MenuDropdown<String>(
                        items: <DropdownMenuItem<String>>[
                          const DropdownMenuItem<String>(
                            child: Text(
                              "All Departments",
                            ),
                          ),
                          ...controller.departments.map(
                            (Department d) => DropdownMenuItem<String>(
                              value: d.department,
                              child: Text(d.title),
                            ),
                          ),
                        ],
                        onChanged: (String? value) {
                          controller.departmentFilter.value = value ?? "";
                        },
                      ),
                    ),
                  ),
                  columnConfigs: <PaginatedColumnConfig>[
                    PaginatedColumnConfig(
                      value: "long_desc",
                      title: "Item",
                    ),
                    PaginatedColumnConfig(
                      value: "upc",
                      title: "UPC",
                      dataType: CustomPaginatedTableDataType.number,
                      columnSize: ColumnSize.S,
                    ),
                    PaginatedColumnConfig(
                      value: "departmentByDepartment",
                      title: "Department",
                      subfield: "title",
                      sortby: "department",
                      columnSize: ColumnSize.L,
                    ),
                  ],
                  onRowTap: (Map<String, dynamic> i) async {
                    await controller.viewItemPermission(Item.fromJson(i));
                  },
                  subscriptionName: "item",
                  aggregateSubscription: '''
                    subscription GET_ITEM_AGGREGATE(\$param: String ) {
                      item_aggregate(where: {_and: [${controller.departmentFilter.value.isEmpty ? "" : '{department: {_eq: "${controller.departmentFilter.value}" } }'}, {_or: [{long_desc: {_ilike: \$param } },{upc: { _ilike: \$param }}]}]}) {
                        aggregate {
                          count
                        }
                      }
                    }
                  ''',
                  dataSubscription: '''
                    subscription GET_ITEMS(\$limit: Int!, \$offset: Int!, \$order_by: [item_order_by!], \$param: String = "") {
                      item(
                        limit: \$limit,
                        offset: \$offset,
                        order_by: \$order_by,
                        where: {_and: [${controller.departmentFilter.value.isEmpty ? "" : '{department: {_eq: "${controller.departmentFilter.value}" } }'}, {_or: [{long_desc: {_ilike: \$param } },{upc: { _ilike: \$param }}]}]}
                      ) {
                        item
                        long_desc
                        upc
                        department
                        document
                        created_at
                        created_by
                        updated_at
                        updated_by
                        liq_ctl_plu
                        departmentByDepartment {
                          created_at
                          created_by 
                          department
                          document
                          title
                          updated_at
                          updated_by
                        }
                      }
                    }
                  ''',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
