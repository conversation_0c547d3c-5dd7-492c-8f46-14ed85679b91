import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddCustomerController');

class AddCustomerController extends GetxController {
  AddCustomerController();

  // ignore: non_constant_identifier_names
  final String INSERT_CUSTOMER_ONE = '''
     mutation INSERT_CUSTOMER_ONE(\$object: customer_insert_input!) {
          insert_customer_one(object: \$object) {
            created_at
            created_by
            customer
            document
            updated_at
            updated_by
          }
        }
      ''';

  // ignore: non_constant_identifier_names
  final String GET_SYSTEM_SETTINGS = '''
  query GET_JSON_RECORD {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      record_key
      document
      updated_at
    }
  }
''';

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final Rx<Customer> newCustomer = Customer.empty().obs;

  final RxInt priceLevelIdx = 0.obs;

  final RxList<PriceLevel> levelList = <PriceLevel>[].obs;

  ScrollController scrollController = ScrollController();

  final GlobalKey<FormState> addCustomerFormKey = GlobalKey<FormState>();

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController phone1Controller = TextEditingController();
  TextEditingController phone2Controller = TextEditingController();
  TextEditingController badgeController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  TextEditingController addressController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController zipCodeController = TextEditingController();
  TextEditingController companyController = TextEditingController();
  TextEditingController directionsController = TextEditingController();
  TextEditingController deliveryZoneController = TextEditingController();

  TextEditingController midInitController = TextEditingController();
  TextEditingController ytdYearController = TextEditingController();
  TextEditingController ytdTotalController = TextEditingController();
  TextEditingController ytxTotalController = TextEditingController();
  TextEditingController availCreditController = TextEditingController();
  TextEditingController creditLimitController = TextEditingController();
  TextEditingController userDataController = TextEditingController();
  TextEditingController ext1Controller = TextEditingController();
  TextEditingController ext2Controller = TextEditingController();
  TextEditingController numberController = TextEditingController();

  @override
  Future<void> onInit() async {
    await getPriceLevels();
    super.onInit();
  }

  Future<void> addCustomer() async {
    try {
      newCustomer.value.created_by = CURRENT_EMPLOYEE.value.employee;
      newCustomer.value.updated_by = CURRENT_EMPLOYEE.value.employee;

      newCustomer.value.document.firstName = firstNameController.text;
      newCustomer.value.document.lastName = lastNameController.text;
      newCustomer.value.document.phone1 = phone1Controller.text;
      newCustomer.value.document.phone2 = phone2Controller.text;
      newCustomer.value.document.badge = badgeController.text;
      newCustomer.value.document.email = emailController.text;

      newCustomer.value.document.address = addressController.text;
      newCustomer.value.document.city = cityController.text;
      newCustomer.value.document.state = stateController.text;
      newCustomer.value.document.zip = zipCodeController.text;
      newCustomer.value.document.company = companyController.text;
      newCustomer.value.document.directions = directionsController.text;
      newCustomer.value.document.deliveryZone = deliveryZoneController.text != "" ? int.parse(deliveryZoneController.text) : 0;

      newCustomer.value.document.midInit = midInitController.text;
      newCustomer.value.document.ytdYear = ytdYearController.text;
      newCustomer.value.document.ytdTotal = ytdTotalController.text != "" ? int.parse(ytdTotalController.text) : 0;
      newCustomer.value.document.ytxTotal = ytxTotalController.text != "" ? int.parse(ytxTotalController.text) : 0;
      newCustomer.value.document.availCredit = availCreditController.text != "" ? int.parse(availCreditController.text) : 0;
      newCustomer.value.document.creditLimit = creditLimitController.text != "" ? int.parse(creditLimitController.text) : 0;
      newCustomer.value.document.ext1 = ext1Controller.text;
      newCustomer.value.document.ext2 = ext2Controller.text;
      newCustomer.value.document.number = numberController.text != "" ? int.parse(numberController.text) : 0;

      newCustomer.value.document.priceLevel = priceLevelIdx.value;

      final Map<String, dynamic> santizedEmployee = Helpers.sanitizeEntity(
        newCustomer.value.toJson(),
        <String>[
          'customer',
          'created_at',
          'updated_at',
        ],
      );
      if (!addCustomerFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult<Object> addCustomerResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            INSERT_CUSTOMER_ONE,
          ),
          variables: <String, dynamic>{
            "object": santizedEmployee,
          },
        ),
      );

      if (addCustomerResult.hasException) {
        return _notificationService.error(addCustomerResult.exception.toString());
      }

      final String newCustomerUUID = (addCustomerResult.data!['insert_customer_one'] as Map<String, dynamic>)['customer'].toString();
      _logger.info(
        "${CURRENT_EMPLOYEE.value.employee_full_name} created new cusomter - ${newCustomer.value.document.firstName} ($newCustomerUUID)",
      );
      _notificationService.success("Customer added!");

      Get.back();
    } catch (err, stack) {
      _logger.severe(
        "Error adding Customer.",
        err,
        stack,
      );
    }
  }

  Future<void> getPriceLevels() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_SYSTEM_SETTINGS),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SystemSettingJsonRecord settingsRecord = configList
            .map(
              (dynamic config) => SystemSettingJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        levelList.value = settingsRecord.document.priceLevels;
      }
    } catch (err, stack) {
      _notificationService.error("Error getting price levels");
      _logger.severe(
        "Error getting price levels.",
        err,
        stack,
      );
    }
  }
}
