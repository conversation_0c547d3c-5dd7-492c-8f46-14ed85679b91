import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/customer/add_customer/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class CustomerController extends GetxController {
  CustomerController();
  Logger customerLogger = Logger("CustomerLogger");

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  Future<void> createPermissionEmployee() async {
    bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Create",
      "customer",
      _graphqlService,
    );
    if (create) {
      customerLogger.info('${CURRENT_EMPLOYEE.value.employee_full_name} accessed Add Customer');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddCustomerDialog(),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewCustomerPermission(Customer customer) async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Customer",
      _graphqlService,
    );
    if (view) {
      customerLogger.info('Viewing Customer ${customer.customer}');
      await Get.toNamed(
        AppRoutes.CUSTOMER_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "customer": customer,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
