import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

// ignore: non_constant_identifier_names
const String GET_SYSTEM_SETTINGS = '''
  query GET_JSON_RECORD {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      record_key
      document
      updated_at
    }
  }
''';

final Logger _logger = Logger('ConfigService');

class ViewCustomerController extends GetxController {
  ViewCustomerController(
    this.customer,
  );

  // ignore: non_constant_identifier_names
  final String UPDATE_CUSTOMER_DOCUMENT_BY_ID = '''
     mutation UPDATE_CUSTOMER_DOCUMENT_BY_ID(\$_eq: uuid, \$document: jsonb, \$updated_at: timestamptz, \$updated_by: uuid) {
        update_customer(where: {customer: {_eq: \$_eq}}, _set: {document: \$document, updated_at: \$updated_at, updated_by: \$updated_by}) {
          affected_rows
        }
      }
      ''';

  final Customer customer;

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> viewCustomerFormKey = GlobalKey<FormState>();

  final Rx<Customer> currentCustomer = Customer.empty().obs;

  RxBool canEdit = true.obs;
  RxBool canDelete = false.obs;

  final RxInt priceLevelIdx = 0.obs;

  final RxList<PriceLevel> levelList = <PriceLevel>[].obs;

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController phone1Controller = TextEditingController();
  TextEditingController phone2Controller = TextEditingController();
  TextEditingController badgeController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  TextEditingController addressController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController zipCodeController = TextEditingController();
  TextEditingController companyController = TextEditingController();
  TextEditingController directionsController = TextEditingController();
  TextEditingController deliveryZoneController = TextEditingController();

  TextEditingController midInitController = TextEditingController();
  TextEditingController ytdYearController = TextEditingController();
  TextEditingController ytdTotalController = TextEditingController();
  TextEditingController ytxTotalController = TextEditingController();
  TextEditingController availCreditController = TextEditingController();
  TextEditingController creditLimitController = TextEditingController();
  TextEditingController userDataController = TextEditingController();
  TextEditingController ext1Controller = TextEditingController();
  TextEditingController ext2Controller = TextEditingController();
  TextEditingController numberController = TextEditingController();

  @override
  Future<void> onInit() async {
    await getPriceLevels();

    currentCustomer.value = customer;
    firstNameController.text = currentCustomer.value.document.firstName;
    lastNameController.text = currentCustomer.value.document.lastName;
    phone1Controller.text = currentCustomer.value.document.phone1 ?? "";
    phone2Controller.text = currentCustomer.value.document.phone2 ?? "";
    badgeController.text = currentCustomer.value.document.badge ?? "";
    emailController.text = currentCustomer.value.document.email ?? "";

    addressController.text = currentCustomer.value.document.address ?? "";
    cityController.text = currentCustomer.value.document.city ?? "";
    stateController.text = currentCustomer.value.document.state ?? "";
    zipCodeController.text = currentCustomer.value.document.zip ?? "";
    companyController.text = currentCustomer.value.document.company ?? "";
    directionsController.text = currentCustomer.value.document.directions ?? "";
    deliveryZoneController.text = currentCustomer.value.document.deliveryZone.toString();

    midInitController.text = currentCustomer.value.document.midInit ?? "";
    ytdYearController.text = currentCustomer.value.document.ytdYear ?? "";
    ytdTotalController.text = currentCustomer.value.document.ytdTotal.toString();
    ytxTotalController.text = currentCustomer.value.document.ytxTotal.toString();
    availCreditController.text = currentCustomer.value.document.availCredit.toString();
    creditLimitController.text = currentCustomer.value.document.creditLimit.toString();
    userDataController.text = currentCustomer.value.document.userData ?? "";
    ext1Controller.text = currentCustomer.value.document.ext1 ?? "";
    ext2Controller.text = currentCustomer.value.document.ext2 ?? "";
    numberController.text = currentCustomer.value.document.number.toString();
    priceLevelIdx.value = currentCustomer.value.document.priceLevel ?? 0;

    await editPermission();
    await deletePermission();
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await editPermission();
    await deletePermission();

    super.onReady();
  }

  Future<void> updateCustomer() async {
    try {
      if (!viewCustomerFormKey.currentState!.validate()) throw "Invalid form";

      currentCustomer.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentCustomer.value.updated_at = DateTime.now();

      currentCustomer.value.document.firstName = firstNameController.text;
      currentCustomer.value.document.lastName = lastNameController.text;
      currentCustomer.value.document.phone1 = phone1Controller.text;
      currentCustomer.value.document.phone2 = phone2Controller.text;
      currentCustomer.value.document.badge = badgeController.text;
      currentCustomer.value.document.email = emailController.text;

      currentCustomer.value.document.address = addressController.text;
      currentCustomer.value.document.city = cityController.text;
      currentCustomer.value.document.state = stateController.text;
      currentCustomer.value.document.zip = zipCodeController.text;
      currentCustomer.value.document.company = companyController.text;
      currentCustomer.value.document.directions = directionsController.text;
      currentCustomer.value.document.deliveryZone = int.parse(deliveryZoneController.text);

      currentCustomer.value.document.midInit = midInitController.text;
      currentCustomer.value.document.ytdYear = ytdYearController.text;
      currentCustomer.value.document.ytdTotal = int.parse(ytdTotalController.text);
      currentCustomer.value.document.ytxTotal = int.parse(ytxTotalController.text);
      currentCustomer.value.document.availCredit = int.parse(availCreditController.text);
      currentCustomer.value.document.creditLimit = int.parse(creditLimitController.text);
      currentCustomer.value.document.ext1 = ext1Controller.text;
      currentCustomer.value.document.ext2 = ext2Controller.text;
      currentCustomer.value.document.number = int.parse(numberController.text);

      currentCustomer.value.document.priceLevel = priceLevelIdx.value;

      final QueryResult<Object> updateCustomerResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_CUSTOMER_DOCUMENT_BY_ID,
          ),
          variables: <String, dynamic>{
            "_eq": currentCustomer.value.customer,
            "document": currentCustomer.value.document,
            "updated_at": currentCustomer.value.updated_at.toString(),
            "updated_by": currentCustomer.value.updated_by
          },
        ),
      );

      if (updateCustomerResult.hasException) {
        return _notificationService.error(updateCustomerResult.exception.toString());
      }
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} updated customer ${currentCustomer.value.customer}");
      _notificationService.success("Update customer success");
      await Get.offNamed(AppRoutes.CUSTOMER, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Customer update failed!");
      _logger.severe(
        "Error updating Customer.",
        err,
        stack,
      );
    }
  }

  Future<void> deleteCustomer() async {
    try {
      final QueryResult<Object> deleteCustomerResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation DELETE_CUSTOMER_BY_ID(\$customer: uuid) {
              delete_customer(where: {customer: {_eq: \$customer}}) {
                affected_rows
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "customer": currentCustomer.value.customer,
          },
        ),
      );

      if (deleteCustomerResult.hasException) {
        return _notificationService.error(deleteCustomerResult.exception.toString());
      }
      _logger.info(
        "${CURRENT_EMPLOYEE.value.employee_full_name} deleted customer ${currentCustomer.value.document.firstName} ${currentCustomer.value.document.lastName} (${currentCustomer.value.customer})",
      );
      _notificationService.success("Customer deleted!");
      await Get.offNamed(AppRoutes.CUSTOMER, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Delete Customer Failed!");
      _logger.severe(
        "Error deleting Customer.",
        err,
        stack,
      );
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Customer",
      _graphqlService,
    );
    canEdit.value = edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Customer",
      _graphqlService,
    );
    canDelete.value = delete;
  }

  Future<void> getPriceLevels() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_SYSTEM_SETTINGS),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SystemSettingJsonRecord settingsRecord = configList
            .map(
              (dynamic config) => SystemSettingJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        levelList.value = settingsRecord.document.priceLevels;
      }
    } catch (err, stack) {
      _notificationService.error("Error getting price levels");
      _logger.severe(
        "Error getting price levels.",
        err,
        stack,
      );
    }
  }
}
