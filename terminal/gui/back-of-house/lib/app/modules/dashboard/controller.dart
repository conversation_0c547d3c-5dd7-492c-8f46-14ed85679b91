// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/movers.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/models/sale_window.dart';
import 'package:backoffice/app/data/view_models/hourly_report.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('DashboardController');

class DashboardController extends GetxController {
  DashboardController();

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  RxList<Movers> mostPopularItemList = <Movers>[].obs;
  RxList<TimeCard> activeTimeCards = <TimeCard>[].obs;
  List<HourlyReport> hourlyDataReportList = <HourlyReport>[];
  RxList<SalesByDeptSummaryReport> departmentDataReportList = <SalesByDeptSummaryReport>[].obs;
  List<Report> hourlyData = <Report>[];
  RxList<BarChartGroupData> items = <BarChartGroupData>[].obs;
  RxList<FlSpot> linePointsList = <FlSpot>[].obs;

  RxList<Widget> legendWidgets = <Widget>[].obs;

  RxList<PieChartSectionData> pieSections = <PieChartSectionData>[].obs;

  RxInt touchX = 0.obs;
  RxInt showingToolTip = 0.obs;
  RxInt totalDailySales = 0.obs;
  RxInt dailyTransactions = 0.obs;
  RxInt thirtyDayAvg = 0.obs;
  RxInt departmentTotals = 0.obs;
  RxDouble averageHourlyTotal = 0.0.obs;
  RxDouble maxHourTotal = 0.0.obs;
  RxDouble graphCeiling = 0.0.obs;
  RxDouble piePercentage = 0.0.obs;
  List<Activity> testList = <Activity>[];
  //test

  RxBool isTouched = false.obs;
  RxBool showAvg = false.obs;

  RxBool switchBool = false.obs;

  final DateTime selectedDate = DateTime.now();
  String start = "";
  String end = "";

  SaleWindow dayTimes = SaleWindow.empty();

  DateTime popItemsDate = DateTime.now();
  String popItemsStart = "";
  String popItemsEnd = "";
  RxString timeFrame = "Month".obs;
  List<String> filters = <String>["Day", "Week", "Month", "Year"];

  RxList<BarChartGroupData> showingBarGroups = <BarChartGroupData>[].obs;
  RxList<BarChartGroupData> rawBarGroups = <BarChartGroupData>[].obs;
  RxInt touchedGroupIndex = 0.obs;
  RxInt pieTouchedGroupIndex = 0.obs;
  RxDouble touchedLineIndex = 0.0.obs;

  RxBool isLoading = true.obs;

  List<Color> pieColors = <Color>[
    R2Colors.primary500,
    R2Colors.primary400,
    R2Colors.primary300,
  ];

  @override
  Future<void> onInit() async {
    dayTimes = Helpers.getReportWindow(selectedDate, scope: 'Day');
    start = DateTime(selectedDate.year, selectedDate.month, selectedDate.day).toUtc().toString();
    end = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      23,
      59,
      59,
    ).toUtc().toString();
    popItemsStart = DateTime(selectedDate.year, selectedDate.month).toUtc().toString();
    popItemsEnd = DateTime(selectedDate.year, selectedDate.month + 1, 0, 24, 59, 59).toUtc().toString();
    await getTransactionTotal();
    await getDailyVolume();
    await getMostPopularItems();
    // await getActiveEmployees();
    await getHourlyData();
    await getPieData();
    await getThirtyDayAvg();

    touchedGroupIndex.value = -1;
    pieTouchedGroupIndex.value = -1;
    touchedLineIndex.value = -1;
    showingBarGroups = items;

    isLoading.value = false;

    super.onInit();
  }

  Future<void> dateHandler(String value) async {
    popItemsDate = DateTime.now();
    switch (timeFrame.value) {
      case "Day":
        popItemsStart = DateTime(selectedDate.year, selectedDate.month, selectedDate.day).toUtc().toString();
        popItemsEnd = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          23,
          59,
          59,
        ).toUtc().toString();
      case "Week":
        final DateTime weekStart = selectedDate.subtract(Duration(days: selectedDate.weekday));
        popItemsStart = DateTime(weekStart.year, weekStart.month, weekStart.day).toUtc().toString();
        final DateTime weekEnd = selectedDate.add(Duration(days: DateTime.daysPerWeek - selectedDate.weekday));
        popItemsEnd = DateTime(weekEnd.year, weekEnd.month, weekEnd.day - 1, 23, 59, 59).toUtc().toString();
      case "Month":
        popItemsStart = DateTime(selectedDate.year, selectedDate.month).toUtc().toString();
        popItemsEnd = DateTime(selectedDate.year, selectedDate.month + 1, 0, 24, 59, 59).toUtc().toString();
      case "Year":
        popItemsStart = DateTime(selectedDate.year).toUtc().toString();
        popItemsEnd = DateTime(selectedDate.year + 1, 1, 0, 23, 59, 59).toUtc().toString();
    }
    mostPopularItemList.clear();
    await getMostPopularItems();
  }

  Future<void> getMostPopularItems() async {
    try {
      final QueryResult<Object> getMostPopularItemResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_MOVERS(\$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int, \$terminal_index: Int, \$qty: order_by, \$limit: Int = 5) {
            get_movers(args: {start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id, terminal_index: \$terminal_index}, order_by: {qty: \$qty}, limit: \$limit) {
              item
              long_desc
              end_at
              amount
              qty
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": popItemsStart,
            "end_date": popItemsEnd,
          },
        ),
      );

      if (getMostPopularItemResult.hasException) {
        throw "Error in getMostPopularItemResult";
      }

      mostPopularItemList.value = (getMostPopularItemResult.data!['get_movers'] as List<dynamic>)
          .map((dynamic report) => Movers.fromJson(report as Map<String, dynamic>))
          .toList();
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting popular items.",
        err,
        stack,
      );
    }
  }

  Future<void> getDailyVolume() async {
    totalDailySales.value = 0;
    try {
      final QueryResult<Object> getDailyVolumeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation MyMutation(\$end_date: timestamp , \$start_date: timestamp , \$terminal_index: Int ) {
            get_hourly_sales(args: {end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
              bothour
              tophour
              hour
              transactions
              totalhourlysales
              tax_price
              base_price
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "end_date": dayTimes.end,
            "start_date": dayTimes.start,
            "terminal_index": null,
          },
        ),
      );

      if (getDailyVolumeResult.hasException) {
        throw "Error in getDailyVolumeResult";
      }
      if (getDailyVolumeResult.hasException) {
        return _notificationService.error(getDailyVolumeResult.exception.toString());
      }

      final List<Report> reports = (getDailyVolumeResult.data!['get_hourly_sales'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();

      // final String total = getDailyVolumeResult.data!['get_hourly_sales']['aggregate']['sum']['totalhourlysales'].toString();

      if (reports.isNotEmpty) {
        for (final Report element in reports) {
          final HourlyReport hourlyReport = HourlyReport.hourlyReportFromReport(element);

          totalDailySales.value += hourlyReport.hourly_actual_price_total ?? 0;
        }
      } else {
        totalDailySales.value = 0;
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting daily volume.",
        err,
        stack,
      );
    }
  }

  Future<void> getTransactionTotal() async {
    final DateTime formattedDate = DateTime.now();
    final String bottomDate = DateFormat('MM-d-y').format(formattedDate);
    final DateTime topDay = formattedDate.add(const Duration(days: 1));
    final String topDate = DateFormat('MM-d-y').format(topDay);
    try {
      final QueryResult<Object> getDailyTransactionsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_DAILY_TRANSACTIONS(\$_gt: timestamptz, \$_lt: timestamptz, \$_contains: jsonb, \$_contains1: jsonb) {
            sale_aggregate(where: {
              end_at: {_gt: \$_gt}, 
              _and: {end_at: {_lt: \$_lt}, 
                _and: {document: {_contains: \$_contains}, 
                  _and: {_not: {document: {_contains: \$_contains1}}}}}}) {
              aggregate {
                count
              }
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "_gt": bottomDate,
            "_lt": topDate,
            "_contains": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[0],
              },
            },
            "_contains1": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[4],
              },
            },
          },
        ),
      );

      if (getDailyTransactionsResult.hasException) {
        throw "Error in getDailyTransactionsResult";
      }
      final int transactionTotal = getDailyTransactionsResult.data!['sale_aggregate']['aggregate']['count'] as int;
      dailyTransactions.value = transactionTotal;
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting transaction total.",
        err,
        stack,
      );
    }
  }

  Future<void> getHourlyData() async {
    try {
      final QueryResult<Object?> hourlyDataResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(
            '''
           mutation GET_HOURLY_SALES(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_hourly_sales(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                base_price
                bothour
                hour
                tax_price
                tophour
                totalhourlysales
                transactions
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": start,
            "end_date": end,
            "terminal_index": null,
          },
        ),
      );

      if (hourlyDataResult.hasException) throw "Error in HourlyDataResult";
      hourlyData = (hourlyDataResult.data!['get_hourly_sales'] as List<dynamic>)
          .map(
            (dynamic report) => Report.fromJson(report as Map<String, dynamic>),
          )
          .toList();

      for (final Report report in hourlyData) {
        final HourlyReport hourlyReport = HourlyReport.hourlyReportFromReport(report);
        hourlyDataReportList.add(hourlyReport);
      }

      for (int i = 5; i < 23; i++) {
        bool isEmpty = true;

        for (final HourlyReport hourData in hourlyDataReportList) {
          if (hourData.bottom_hour!.toLocal().hour == i) {
            isEmpty = false;
          }
        }
        if (isEmpty) {
          final BarChartGroupData barHours = BarChartGroupData(
            barsSpace: 0,
            x: i,
            barRods: <BarChartRodData>[
              BarChartRodData(
                toY: 0,
                color: const Color.fromRGBO(118, 255, 3, .3),
                width: 15,
              ),
              BarChartRodData(
                toY: 0,
                color: Colors.transparent,
                width: 0,
              ),
            ],
          );
          items.add(barHours);
          rawBarGroups.add(barHours);
          final FlSpot linePoints = FlSpot(
            double.parse(
              i.toString(),
            ),
            0,
          );
          linePointsList.add(linePoints);
        } else {
          for (final HourlyReport hourData in hourlyDataReportList) {
            final num xDot = (hourData.hourly_actual_price_total ?? 0) / 100;
            if (hourData.bottom_hour!.toLocal().hour == i) {
              final BarChartGroupData barHours = BarChartGroupData(
                showingTooltipIndicators: showingToolTip.value == i ? <int>[0] : <int>[],
                barsSpace: 2,
                x: i,
                barRods: <BarChartRodData>[
                  BarChartRodData(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    toY: double.parse(
                      ((hourData.hourly_actual_price_total ?? 0) / 100).toString(),
                    ),
                    color: const Color.fromRGBO(105, 240, 174, 1),
                    width: 15,
                  ),
                  BarChartRodData(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    toY: double.parse((hourData.transactions ?? 0).toString()),
                    color: Colors.transparent,
                    width: 0,
                  ),
                ],
              );
              if ((hourData.hourly_actual_price_total ?? 0) / 100 > maxHourTotal.value) {
                maxHourTotal.value = hourData.hourly_actual_price_total! / 100;
              }
              items.add(barHours);
              rawBarGroups.add(barHours);

              final FlSpot linePoints = FlSpot(
                double.parse(hourData.bottom_hour!.toLocal().hour.toString()),
                double.parse(xDot.toString()),
              );
              linePointsList.add(linePoints);
            }
          }
        }
      }
      graphCeiling.value = (maxHourTotal.value ~/ 1000) * 1000 + 1000;
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting hourly data.",
        err,
        stack,
      );
    }
  }

  Widget leftTitlesBar(double value, TitleMeta meta) {
    const TextStyle style = TextStyle(
      color: R2Colors.neutral500,
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
    String text;
    if (value == 0) {
      text = '${value.round()}';
    } else if (value == (graphCeiling.value * .25).round()) {
      text = "\$${Helpers.formatWholeNumber(value.round())}";
    } else if (value == (graphCeiling.value * .5).round()) {
      text = "\$${Helpers.formatWholeNumber(value.round())}";
    } else if (value == (graphCeiling.value * .75).round()) {
      text = "\$${Helpers.formatWholeNumber(value.round())}";
    } else if (value == graphCeiling.value) {
      text = "\$${Helpers.formatWholeNumber(value.round())}";
    } else {
      return Container();
    }

    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Text(
        text,
        style: style,
        textAlign: TextAlign.end,
      ),
    );
  }

  Future<void> getPieData() async {
    try {
      if (totalDailySales.value <= 0) return;

      final QueryResult<Object> getSalesByDeptResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_SALES_BY_DEPT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_sales_by_department(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}, order_by: {deptactualpricetotals: desc}, limit: 3) {
               department
              deptactualpricetotals
              gross_price_total
              itemsperdepartment
              percentage
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": start,
            "end_date": end,
            "terminal_index": null,
          },
        ),
      );

      if (getSalesByDeptResult.hasException) {
        throw "Error in getSalesByDeptResult";
      }

      final List<Report> reports = (getSalesByDeptResult.data!['get_sales_by_department'] as List<dynamic>)
          .map(
            (dynamic report) => Report.fromJson(report as Map<String, dynamic>),
          )
          .toList();
      for (final Report report in reports) {
        final SalesByDeptSummaryReport salesByDeptSummaryReport = SalesByDeptSummaryReport.salesByDeptSummaryReportFromReport(report);
        departmentTotals.value += salesByDeptSummaryReport.dept_actual_price!;
      }

      for (int i = 0; i < reports.length; i++) {
        final SalesByDeptSummaryReport salesByDeptSummaryReport = SalesByDeptSummaryReport.salesByDeptSummaryReportFromReport(
          reports[i],
        );
        final double percentage = salesByDeptSummaryReport.dept_actual_price! / totalDailySales.value;
        piePercentage.value += double.parse(percentage.toStringAsFixed(2));
        departmentDataReportList.add(salesByDeptSummaryReport);

        final Widget containerWidget = ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 140),
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  salesByDeptSummaryReport.department!,
                  style: const TextStyle(
                    color: R2Colors.neutral500,
                  ),
                ),
                Container(
                  height: 15,
                  width: 15,
                  decoration: BoxDecoration(
                    color: pieColors[i],
                    border: Border.all(width: .5, color: R2Colors.neutral200),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(5),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
        legendWidgets.add(containerWidget);
      }
      piePercentage.value = piePercentage.value * 100;
      if (piePercentage.value.round() < 99.0 && departmentDataReportList.isNotEmpty) {
        final ConstrainedBox containerWidget = ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 140),
          child: Padding(
            padding: const EdgeInsets.all(5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  "Other",
                  style: TextStyle(
                    color: R2Colors.neutral500,
                  ),
                ),
                Container(
                  height: 15,
                  width: 15,
                  decoration: BoxDecoration(
                    color: R2Colors.neutral500,
                    border: Border.all(width: .5, color: R2Colors.neutral200),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(5),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
        legendWidgets.add(containerWidget);
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting pie chart data.",
        err,
        stack,
      );
    }
  }

  List<PieChartSectionData> showingSections() {
    if (totalDailySales.value <= 0) {
      return <PieChartSectionData>[
        PieChartSectionData(color: R2Colors.primary500, title: ''),
      ];
    }

    final List<PieChartSectionData> sections = [];

    // Add sections for each department
    for (int i = 0; i < departmentDataReportList.length; i++) {
      final bool isTouched = i == pieTouchedGroupIndex.value;
      final double fontSize = isTouched ? 20.0 : 14.0;
      final double radius = isTouched ? 60.0 : 50.0;
      final String percentage = ((departmentDataReportList[i].dept_actual_price! / totalDailySales.value) * 100).round().toString();

      sections.add(
        PieChartSectionData(
          color: pieColors[i],
          borderSide: isTouched ? const BorderSide(width: 2, color: R2Colors.neutral200) : null,
          value: double.parse(percentage),
          title: '$percentage%',
          radius: radius,
          titleStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: const Color(0xffffffff),
          ),
        ),
      );
    }

    // Add "Other" section if needed
    if (departmentDataReportList.length == 3 && piePercentage.value.round() < 100) {
      final bool isTouched = 3 == pieTouchedGroupIndex.value;
      final double fontSize = isTouched ? 20.0 : 14.0;
      final double radius = isTouched ? 60.0 : 50.0;

      sections.add(
        PieChartSectionData(
          borderSide: isTouched ? const BorderSide(width: 2, color: R2Colors.neutral200) : null,
          color: R2Colors.neutral500,
          value: 100.0 - piePercentage.value.round(),
          title: '${100 - piePercentage.value.round()}%',
          radius: radius,
          titleStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: const Color(0xffffffff),
          ),
        ),
      );
    }

    return sections;
  }

  Future<void> getThirtyDayAvg() async {
    try {
      final QueryResult<Object> getSaleAvgResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           query get_thirty_day_sales_avg {
              get_thirty_day_sales_avg {
                thirty_day_avg
              }
            }
          ''',
          ),
        ),
      );

      if (getSaleAvgResult.hasException) throw "Error in getSaleAvgResult";

      thirtyDayAvg.value = int.parse(
        getSaleAvgResult.data?['get_thirty_day_sales_avg'][0]['thirty_day_avg'].toString() ?? '0',
      );
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error getting 30 day average.",
        err,
        stack,
      );
    }
  }

  Widget bottomTitles(double value, TitleMeta meta) {
    const TextStyle style = TextStyle(
      color: R2Colors.neutral500,
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    Widget text;
    switch (value.toInt()) {
      case 5:
        text = const Text(
          '5AM',
          style: style,
        );
      case 6:
        text = const Text(
          '',
          style: style,
        );
      case 7:
        text = const Text(
          '7AM',
          style: style,
        );
      case 8:
        text = const Text(
          '',
          style: style,
        );
      case 9:
        text = const Text(
          '9AM',
          style: style,
        );
      case 10:
        text = const Text(
          '',
          style: style,
        );
      case 11:
        text = const Text(
          '11AM',
          style: style,
        );
      case 12:
        text = const Text(
          '',
          style: style,
        );
      case 13:
        text = const Text(
          '1PM',
          style: style,
        );
      case 14:
        text = const Text(
          '',
          style: style,
        );
      case 15:
        text = const Text(
          '3PM',
          style: style,
        );
      case 16:
        text = const Text(
          '',
          style: style,
        );
      case 17:
        text = const Text(
          '5PM',
          style: style,
        );
      case 18:
        text = const Text(
          '',
          style: style,
        );
      case 19:
        text = const Text(
          '7PM',
          style: style,
        );
      case 20:
        text = const Text(
          '',
          style: style,
        );
      case 21:
        text = const Text(
          '9PM',
          style: style,
        );
      case 22:
        text = const Text(
          '',
          style: style,
        );
      case 23:
        text = const Text(
          '11PM',
          style: style,
        );

      default:
        text = const Text(
          '',
          style: style,
        );
        break;
    }
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: text,
    );
  }

  List<Color> gradientColors = <Color>[
    R2Colors.primary200,
    R2Colors.primary500,
    R2Colors.primary200,
  ];
}
