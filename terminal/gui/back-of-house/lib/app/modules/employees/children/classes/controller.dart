import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/employees/children/classes/dialogs/add_employee_class/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/employee_class.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class ClassesController extends GetxController {
  ClassesController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final Logger employeeClassLogger = Logger('EmployeeClassLogger');

  Future<void> createEmployeeClassPermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Employee Classes",
      _graphqlService,
    );
    if (create) {
      employeeClassLogger.info('${CURRENT_EMPLOYEE.value.employee_full_name} accessing create employee class');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddEmployeeClassDialog(),
        ),
        isScrollControlled: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewEmployeeClassPermission(EmployeeClass employeeClass) async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Employee classes",
      _graphqlService,
    );
    if (view) {
      employeeClassLogger.info('${CURRENT_EMPLOYEE.value.employee_full_name} viewing employee class - ${employeeClass.title}');
      await Get.toNamed(
        AppRoutes.EMPLOYEES_CLASSES_VIEW,
        id: AppRoutes.id,
        arguments: <String, EmployeeClass>{
          "employee_class": employeeClass,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
