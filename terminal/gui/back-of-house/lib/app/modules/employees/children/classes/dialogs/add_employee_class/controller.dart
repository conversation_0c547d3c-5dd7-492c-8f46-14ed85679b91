import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/employee_class.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddEmployeeClassDialogController');

class AddEmployeeClassDialogController extends GetxController {
  AddEmployeeClassDialogController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  // final SyncService _syncService = Get.find();

  final GlobalKey<FormState> addEmployeeClassFormKey = GlobalKey<FormState>();

  final Rx<EmployeeClass> newEmployeeClass = EmployeeClass.empty().obs;

  TextEditingController titleController = TextEditingController();
  TextEditingController dayOTLimit = TextEditingController();
  TextEditingController weekOTLimit = TextEditingController();
  TextEditingController mealDedLimit = TextEditingController();
  TextEditingController mealDedMins = TextEditingController();

  @override
  Future<void> onInit() async {
    dayOTLimit.text = '0';
    weekOTLimit.text = '0';
    mealDedLimit.text = '0';
    mealDedMins.text = '0';
    super.onInit();
  }

  Future<void> addEmployeeClass() async {
    try {
      if (!addEmployeeClassFormKey.currentState!.validate()) {
        throw "Invalid form";
      }

      newEmployeeClass.value.title = titleController.text;
      newEmployeeClass.value.document.dailyOTLimit = int.parse(dayOTLimit.text);
      newEmployeeClass.value.document.weeklyOTLimit = int.parse(weekOTLimit.text);
      newEmployeeClass.value.document.mealDedLimit = int.parse(mealDedLimit.text);
      newEmployeeClass.value.document.mealDedMins = int.parse(mealDedMins.text);
      newEmployeeClass.value.created_by = CURRENT_EMPLOYEE.value.employee;
      newEmployeeClass.value.updated_by = CURRENT_EMPLOYEE.value.employee;

      final Map<String, dynamic> santizedJobCode = Helpers.sanitizeEntity(
        newEmployeeClass.value.toJson(),
        <String>[
          'employee_class',
          'created_at',
          'updated_at',
        ],
      );

      final QueryResult<Object> addEmployeeClassResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation ADD_EMPLOYEE_CLASS(\$employeeClass: employee_class_insert_input!) {
                insert_employee_class_one(object: \$employeeClass) {
                  employee_class
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employeeClass": santizedJobCode,
          },
        ),
      );

      if (addEmployeeClassResult.hasException) {
        return _notificationService.error(addEmployeeClassResult.exception.toString());
      }
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} created employee class - ${newEmployeeClass.value.title}');
      _notificationService.success('Add Employee Class Success');
      Get.back();
    } catch (err, stack) {
      _logger.severe(
        "Error adding employee class.",
        err,
        stack,
      );
    }
  }
}
