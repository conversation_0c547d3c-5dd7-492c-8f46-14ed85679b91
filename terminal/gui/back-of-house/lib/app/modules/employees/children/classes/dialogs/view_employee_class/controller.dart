// ignore_for_file: depend_on_referenced_packages, always_specify_types, no_leading_underscores_for_local_identifiers

import 'package:backoffice/app/data/view_models/tree_node.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:collection/collection.dart';
import 'package:desktop/app/data/models/employee_class.dart';
import 'package:desktop/app/data/models/permission.dart';
import 'package:desktop/app/data/models/permission_action.dart';
import 'package:desktop/app/data/models/permission_object.dart';
import 'package:desktop/app/data/models/permission_type.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('ViewEmployeeClassDialogController');

class ViewEmployeeClassDialogController extends GetxController {
  ViewEmployeeClassDialogController(
    this.employeeClass,
  );

  final GraphqlService _graphqlService = Get.find();

  final EmployeeClass employeeClass;

  // final SyncService _syncService = Get.find();

  final NotificationService _notificationService = Get.find();

  final GlobalKey<FormState> viewEmployeeClassFormKey = GlobalKey<FormState>();

  final Rx<EmployeeClass> currentEmployeeClass = EmployeeClass.empty().obs;

  TextEditingController titleController = TextEditingController();
  TextEditingController dayOTLimit = TextEditingController();
  TextEditingController weekOTLimit = TextEditingController();
  TextEditingController mealDedLimit = TextEditingController();
  TextEditingController mealDedMins = TextEditingController();

  List<Permission> permissionList = <Permission>[];
  List<PermissionObject> permissionObjectList = <PermissionObject>[];
  List<PermissionType> permissionTypeList = <PermissionType>[];
  List<PermissionAction> permissionActionList = <PermissionAction>[];
  TreeNode rootNode = TreeNode(
    id: "0",
    parent: null,
    data: null,
    children: <TreeNode>[],
    typeTitle: "",
  );
  List<TreeNode> data = <TreeNode>[];
  RxList<Widget> permissionWidgets = <Widget>[].obs;

  bool isLoading = true;
  RxBool canEdit = true.obs;

  @override
  Future<void> onInit() async {
    currentEmployeeClass.value = employeeClass;
    titleController.text = employeeClass.title;
    dayOTLimit.text = employeeClass.document.dailyOTLimit.toString();
    weekOTLimit.text = employeeClass.document.weeklyOTLimit.toString();
    mealDedLimit.text = employeeClass.document.mealDedLimit.toString();
    mealDedMins.text = employeeClass.document.mealDedMins.toString();
    data.add(rootNode);
    await loadPermissions();
    await loadPermissionActions();
    await loadPermissionObjects();
    await editPermission();
    await loadPermissionTypes();

    await initializePermissionGroupings();
    permissionWidgets.value = _generateTreeView(rootNode);

    super.onInit();
  }

  Future<void> loadPermissions() async {
    try {
      final QueryResult<Object> getPermissionsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
        query GET_PERMISSIONS(\$employee_class: uuid) {
          permission(where: {employee_class: {_eq: \$employee_class}}) {
            created_at
            created_by
            document
            employee_class
            permission
            permission_action
            permission_object
            permission_type
            updated_at
            updated_by
          }
        }


          ''',
          ),
          variables: {"employee_class": currentEmployeeClass.value.employee_class},
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if (getPermissionsResult.hasException) {
        return _notificationService.error(getPermissionsResult.exception.toString());
      }
      if (getPermissionsResult.data!.isNotEmpty) {
        permissionList = (getPermissionsResult.data!['permission'] as List<dynamic>)
            .map(
              (dynamic permission) => Permission.fromJson(
                permission as Map<String, dynamic>,
              ),
            )
            .toList();
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading permissions.",
        err,
        stack,
      );
    }
  }

  // TODO check with joe on query options
  Future<void> loadPermissionActions() async {
    try {
      final QueryResult<Object> getPermissionActionResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
          query GET_PERMISSION_ACTION {
            permission_action {
              updated_by
              updated_at
              title
              permission_type
              permission_action
              document
              created_by
              created_at
            }
          }

          ''',
          ),
        ),
      );
      if (getPermissionActionResult.hasException) {
        return _notificationService.error(getPermissionActionResult.exception.toString());
      }
      if (getPermissionActionResult.data!.isNotEmpty) {
        permissionActionList = (getPermissionActionResult.data!['permission_action'] as List<dynamic>)
            .map(
              (dynamic pAction) => PermissionAction.fromJson(
                pAction as Map<String, dynamic>,
              ),
            )
            .toList();
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading permission actions.",
        err,
        stack,
      );
    }
  }

  Future<void> loadPermissionObjects() async {
    try {
      final QueryResult<Object> getPermissionObjectResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
          query GET_PERMISSION_OBJECT{
            permission_object {
              created_at
              created_by
              document
              parent_object
              permission_object
              permission_type
              title
              updated_at
              updated_by
            }
          }

          ''',
          ),
        ),
      );
      if (getPermissionObjectResult.hasException) {
        return _notificationService.error(getPermissionObjectResult.exception.toString());
      }

      if (getPermissionObjectResult.data!.isNotEmpty) {
        permissionObjectList = (getPermissionObjectResult.data!['permission_object'] as List<dynamic>)
            .map(
              (dynamic pObject) => PermissionObject.fromJson(
                pObject as Map<String, dynamic>,
              ),
            )
            .toList();
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading permission objects.",
        err,
        stack,
      );
    }
  }

  Future<void> loadPermissionTypes() async {
    try {
      final QueryResult<Object> getPermissionTypeResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
          query GET_PERMISSION_TYPE {
            permission_type {
              created_at
              created_by
              document
              permission_type
              title
              updated_at
              updated_by
            }
          }
          ''',
          ),
        ),
      );
      if (getPermissionTypeResult.hasException) {
        return _notificationService.error(getPermissionTypeResult.exception.toString());
      }

      if (getPermissionTypeResult.data!.isNotEmpty) {
        permissionTypeList = (getPermissionTypeResult.data!['permission_type'] as List<dynamic>)
            .map(
              (dynamic pObject) => PermissionType.fromJson(
                pObject as Map<String, dynamic>,
              ),
            )
            .toList();
      }
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading permission types.",
        err,
        stack,
      );
    }
  }

  Future<void> initializePermissionGroupings() async {
    try {
      for (final PermissionObject object in permissionObjectList) {
        final List<PermissionAction> actions = <PermissionAction>[];

        String typeTitle = "";
        for (final PermissionAction action in permissionActionList) {
          if (object.permission_type == action.permission_type) {
            actions.add(action);
          }
        }

        for (final PermissionType type in permissionTypeList) {
          if (type.permission_type == object.permission_type) {
            typeTitle = type.title;
          }
        }

        final TreeNode tempNode = TreeNode(
          id: object.permission_object,
          parent: object.parent_object ?? "0",
          data: object,
          actions: actions,
          typeTitle: typeTitle,
          children: <TreeNode>[],
        );
        data.add(tempNode);
      }

      final Map<dynamic, int> idMapping = <dynamic, int>{};
      data.forEachIndexed((int index, TreeNode element) {
        idMapping[element.id] = index;
      });

      data.forEach((TreeNode element) {
        if (element.parent == null) {
          rootNode = element;
        } else {
          final int thing = idMapping[element.parent] as int;
          final TreeNode parentEl = data[thing];
          parentEl.children = [...parentEl.children, element];
        }
      });
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error initializing permission groupings.",
        err,
        stack,
      );
    }
  }

  List<Widget> _generateTreeView(TreeNode incomingNode) {
    try {
      final List<TreeNode> _nodes = incomingNode.children;
      final List<Widget> _childrenWidgets = <Widget>[];

      for (final TreeNode child in _nodes) {
        if (child.children.isNotEmpty) {
          //This is for Permission Objects with Children
          _childrenWidgets.add(_permissionExpansionTile(child));
        } else {
          //This is for Permission Objects with NO Children
          final List<Widget> containerChildren = <Widget>[];
          for (final PermissionAction permissionAction in permissionActionList) {
            if (permissionAction.permission_type == child.data!.permission_type) {
              containerChildren.add(_permissionEntry(child.data!, permissionAction));
            }
          }
          _childrenWidgets.add(
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.black26),
                  // color: Colors.orange[100],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (child.typeTitle != "Module")
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          child.data!.title,
                          style: const TextStyle(fontSize: 16),
                        ),
                      )
                    else
                      Container(),
                    Column(
                      children: containerChildren,
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      }
      return _childrenWidgets;
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error generating tree view.",
        err,
        stack,
      );
      return <Text>[Text("Error: $err")];
    }
  }

  Widget _permissionEntry(PermissionObject permissionObject, PermissionAction permissionAction) {
    try {
      return ListTile(
        title: Row(
          children: [_mutationCheckBox(permissionObject, permissionAction), Text("${permissionAction.title} ${permissionObject.title}")],
        ),
      );
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error making permission entry.",
        err,
        stack,
      );
      return Text("Error: $err");
    }
  }

  Widget _permissionExpansionTile(TreeNode child) {
    try {
      const bool isIgnoring = false;
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black26),
          ),
          child: Stack(
            children: <Widget>[
              IgnorePointer(
                ignoring: isIgnoring,
                child: ExpansionTile(
                  title: Padding(
                    padding: const EdgeInsets.only(left: 31.0),
                    child: Row(
                      children: <Widget>[
                        Text("${child.actions![0].title} ${child.data!.title}"),
                      ],
                    ),
                  ),
                  children: child.children.isEmpty ? <Widget>[] : _generateTreeView(child),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 10, 0, 0),
                child: _mutationCheckBox(child.data!, child.actions![0]),
              ),
            ],
          ),
        ),
      );
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading permission entry.",
        err,
        stack,
      );
      return Text("Error: $err");
    }
  }

  Widget _mutationCheckBox(PermissionObject permissionObject, PermissionAction permissionAction) {
    try {
      final RxBool _currentValue = false.obs;

      for (final Permission permission in permissionList) {
        if (permission.permission_action == permissionAction.permission_action &&
            permission.permission_object == permissionObject.permission_object) {
          _currentValue.value = true;
        }
      }
      return Obx(() {
        return Checkbox(
          value: _currentValue.value,
          activeColor: R2Colors.primary500,
          fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
            if (!states.contains(MaterialState.selected)) {
              return R2Colors.white;
            }
            return null;
          }),
          onChanged: (bool? val) async {
            if (canEdit.value) {
              if (val!) {
                //add
                await _addPermission(permissionAction, permissionObject);
              } else {
                //delete
                await _deletePermission(permissionAction, permissionObject);
              }
              _currentValue.value = val;
            }
          },
        );
      });
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error loading mutation checkbox.",
        err,
        stack,
      );
      return Text("Error: $err");
    }
  }

  Future<void> _addPermission(
    PermissionAction permissionAction,
    PermissionObject permissionObject,
  ) async {
    try {
      final Map<String, dynamic> santizedPermission = Helpers.sanitizeEntity(
        <String, dynamic>{
          "permission": "",
          "employee_class": employeeClass.employee_class,
          "permission_action": permissionAction.permission_action,
          "permission_object": permissionObject.permission_object,
          "permission_type": permissionObject.permission_type,
          "document": <String, dynamic>{},
          "created_by": CURRENT_EMPLOYEE.value.employee,
          "updated_by": CURRENT_EMPLOYEE.value.employee,
        },
        <String>[
          'permission',
          'created_at',
          'updated_at',
        ],
      );

      final QueryResult addPermissionResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
             mutation ADD_PERMISSION(\$permission: permission_insert_input!) {
              insert_permission_one(object: \$permission) {
               permission
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "permission": santizedPermission,
          },
        ),
      );

      if (addPermissionResult.hasException) {
        return _notificationService.error(addPermissionResult.exception.toString());
      }
      _logger.info('adding permission ${permissionObject.title} ${permissionAction.title}');

      _notificationService.success("Permission added");
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error adding permission.",
        err,
        stack,
      );
    }
  }

  Future<void> _deletePermission(
    PermissionAction permissionAction,
    PermissionObject permissionObject,
  ) async {
    try {
      final QueryResult deletePermissionResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
          mutation MyMutation(\$employeeClass: uuid, \$permissionAction: uuid, \$permissionObject: uuid) {
            delete_permission(where: {employee_class: {_eq: \$employeeClass}, permission_action: {_eq: \$permissionAction}, permission_object: {_eq: \$permissionObject}}) {
              affected_rows
            }
          }


          ''',
          ),
          variables: <String, dynamic>{
            "employeeClass": employeeClass.employee_class,
            "permissionAction": permissionAction.permission_action,
            "permissionObject": permissionObject.permission_object,
          },
        ),
      );

      if (deletePermissionResult.hasException) {
        return _notificationService.error(deletePermissionResult.exception.toString());
      }
      _logger.info('removing permission ${permissionObject.title} ${permissionAction.title}');

      _notificationService.success("Permission deleted");
    } catch (err, stack) {
      _notificationService.error(err.toString());
      _logger.severe(
        "Error deleting permission.",
        err,
        stack,
      );
    }
  }

  Future<void> updateEmployeeClass() async {
    if (!viewEmployeeClassFormKey.currentState!.validate()) {
      throw "Invalid form";
    }
    try {
      currentEmployeeClass.value.title = titleController.text;
      currentEmployeeClass.value.document.mealDedLimit = int.parse(mealDedLimit.text);
      currentEmployeeClass.value.document.mealDedMins = int.parse(mealDedMins.text);
      currentEmployeeClass.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentEmployeeClass.value.employee_class = employeeClass.employee_class;
      currentEmployeeClass.value.document.dailyOTLimit = int.parse(dayOTLimit.text);
      currentEmployeeClass.value.document.weeklyOTLimit = int.parse(weekOTLimit.text);

      final QueryResult updateJobCodeResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
           mutation UPDATE_EMPLOYEE_CLASS(\$employee_class: uuid!, \$title: String, \$document: jsonb, \$updated_by: uuid, \$updated_at: timestamptz,) {
            update_employee_class_by_pk(pk_columns: {employee_class: \$employee_class}, _set: {title: \$title, document: \$document, updated_by: \$updated_by, updated_at: \$updated_at}) {
              employee_class
              title
              document
            }
          }

          ''',
          ),
          variables: <String, dynamic>{
            "employee_class": currentEmployeeClass.value.employee_class,
            "title": currentEmployeeClass.value.title,
            "document": currentEmployeeClass.value.document,
            "updated_by": CURRENT_EMPLOYEE.value.employee,
            "updated_at": DateTime.now().toUtc().toString(),
          },
        ),
      );

      if (updateJobCodeResult.hasException) {
        return _notificationService.error(updateJobCodeResult.exception.toString());
      }
      _logger.info('${CURRENT_EMPLOYEE.value.employee_full_name} updated employee class ${currentEmployeeClass.value.title}');
      _notificationService.success("Update employee success");
      await Get.offNamed(AppRoutes.EMPLOYEES_CLASSES, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Employee update failed!");
      _logger.severe(
        "Error updating employee class.",
        err,
        stack,
      );
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Edit",
      "Employee Classes",
      _graphqlService,
    );
    canEdit.value = edit;
  }
}
