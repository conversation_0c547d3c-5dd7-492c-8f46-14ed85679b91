import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/employees/children/job_codes/dialogs/add_job_code.dart/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class JobCodesController extends GetxController {
  JobCodesController();
  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final RxList<SystemSettingJsonRecordJobCode> jobCodeList = <SystemSettingJsonRecordJobCode>[].obs;
  Logger jobCodeLogger = Logger('JobCodeLogger');
  // ignore: non_constant_identifier_names
  final String GET_JOB_CODES_QUERY = '''
  subscription GET_JSON_RECORD {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      record_key
      document
      updated_at
    }
  }
''';

  Future<void> createJobCodePermission() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Job Codes",
      _graphqlService,
    );
    if (create) {
      jobCodeLogger.info('${CURRENT_EMPLOYEE.value.employee_full_name} access Add Jobcode');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddJobCodeDialog(),
        ),
        isScrollControlled: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewJobCodePermission(
    SystemSettingJsonRecordJobCode jobCode,
  ) async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Job Codes",
      _graphqlService,
    );
    if (create) {
      jobCodeLogger.info('${CURRENT_EMPLOYEE.value.employee_full_name} viewing jobcode - ${jobCode.title}');

      await Get.toNamed(
        AppRoutes.EMPLOYEES_JOB_CODES_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "job_code": jobCode,
        },
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
