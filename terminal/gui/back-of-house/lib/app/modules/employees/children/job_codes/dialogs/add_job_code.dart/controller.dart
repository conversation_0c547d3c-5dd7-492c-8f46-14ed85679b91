import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/job_code.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddJobCodeDialogController');

const String GET_SYSTEM_SETTING_QUERY = '''
 query GET_SYSTEM_SETTING {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String GET_SECTIONS_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "sections"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String UPDATE_JOB_CODES = '''
 mutation UPDATE_Job_CODES(\$_eq: String, \$document: jsonb) {
  update_json_record(where: {record_key: {_eq: \$_eq}}, _append: {document: \$document}) {
    returning {
      document
      record_key
      updated_at
    }
  }
}
''';

class AddJobCodeDialogController extends GetxController {
  AddJobCodeDialogController();

  final GlobalKey<FormState> addJobCodeFormKey = GlobalKey<FormState>();
  final NotificationService _notificationService = Get.find();

  final GraphqlService _graphqlService = Get.find();

  TextEditingController titleController = TextEditingController();

  final Rx<JobCode> newJobCode = JobCode.empty().obs;

  final RxInt sectionIdx = 0.obs;
  final RxInt codeBreaks = 0.obs;

  final RxList<Section> sectionsList = <Section>[].obs;

  List<Break> breakList = <Break>[].obs;

  @override
  Future<void> onInit() async {
    await getSectionsConfig();

    super.onInit();
  }

  Future<void> addJobCode() async {
    try {
      if (!addJobCodeFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult<Object> systemSettingResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            GET_SYSTEM_SETTING_QUERY,
          ),
        ),
      );

      if (systemSettingResult.hasException) {
        throw systemSettingResult.exception.toString();
      }

      final SystemSettingJsonRecord systemSettingRecord = SystemSettingJsonRecord.fromJson(
        (systemSettingResult.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );

      systemSettingRecord.document.jobCodes.add(
        SystemSettingJsonRecordJobCode(
          index: systemSettingRecord.document.jobCodes.length + 1,
          title: titleController.text,
          revCtr: 0,
          isDeliveryDriver: false,
          laborGroup: 0,
          section: sectionIdx.value,
          promptForSeat: true,
          isActive: true,
          breaks: codeBreaks.value,
        ),
      );

      final QueryResult<Object> updateJobCodesResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_JOB_CODES,
          ),
          variables: <String, dynamic>{
            "_eq": "systemSetting",
            "document": systemSettingRecord.document,
          },
        ),
      );

      if (updateJobCodesResult.hasException) {
        throw updateJobCodesResult.exception.toString();
      }
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} created new jobcode - ${titleController.text}");
      _notificationService.success('Add Job Code Success');
      Get.back();
    } catch (err, stack) {
      _notificationService.error('failed to add jobCode');
      _logger.severe(
        "Error adding job code.",
        err,
        stack,
      );
    }
  }

  Future<void> getSectionsConfig() async {
    try {
      final QueryResult<Object> configResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_SECTIONS_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SectionsJsonRecord sectionsRecord = configList
            .map(
              (dynamic config) => SectionsJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        sectionsList.value = sectionsRecord.document.sections;
      }
    } catch (err, stack) {
      _notificationService.error("Error Getting Sections");
      _logger.severe(
        "Error getting sections config.",
        err,
        stack,
      );
    }
  }

  void flipBreak(Break brk) {
    codeBreaks.value = codeBreaks.value ^ (1 << brk.idx);
  }
}
