import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('ViewJobCodeDialogController');

const String GET_SYSTEM_SETTING_QUERY = '''
 query GET_SYSTEM_SETTING {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String GET_SECTIONS_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "sections"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String UPDATE_JOB_CODES = '''
 mutation UPDATE_BANNER_MSG(\$_eq: String, \$document: jsonb) {
  update_json_record(where: {record_key: {_eq: \$_eq}}, _append: {document: \$document}) {
    returning {
      document
      record_key
      updated_at
    }
  }
}
''';

class ViewJobCodeDialogController extends GetxController {
  ViewJobCodeDialogController(
    this.jobCode,
  );

  final SystemSettingJsonRecordJobCode jobCode;

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> viewJobCodeFormKey = GlobalKey<FormState>();

  final Rx<SystemSettingJsonRecordJobCode> currentJobCode = SystemSettingJsonRecordJobCode.empty().obs;

  TextEditingController titleController = TextEditingController();
  TextEditingController payRateController = TextEditingController();
  final RxInt payPeriod = 0.obs;
  final RxInt sectionIdx = 0.obs;
  final RxInt codeBreaks = 0.obs;

  final RxList<Section> sectionsList = <Section>[].obs;

  final RxBool canEdit = false.obs;
  final RxBool canDeactivate = true.obs;

  List<Break> breakList = <Break>[].obs;

  bool deactivating = false;
  bool reactivating = false;

  @override
  Future<void> onInit() async {
    await getSectionsConfig();
    currentJobCode.value = jobCode;
    sectionIdx.value = jobCode.section;
    titleController.text = jobCode.title;
    codeBreaks.value = jobCode.breaks;

    await editPermission();
    await deactivatePermission();
    if (!jobCode.isActive) canEdit.value = false;

    super.onInit();
  }

  Future<void> updateJobCode() async {
    try {
      if (!deactivating && !reactivating && !viewJobCodeFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult<Object> systemSettingResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            GET_SYSTEM_SETTING_QUERY,
          ),
        ),
      );

      if (systemSettingResult.hasException) {
        throw systemSettingResult.exception.toString();
      }

      final SystemSettingJsonRecord systemSettingRecord = SystemSettingJsonRecord.fromJson(
        (systemSettingResult.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );

      final SystemSettingJsonRecordJobCode systemJob = systemSettingRecord.document.jobCodes.firstWhere(
        (SystemSettingJsonRecordJobCode job) => job.index == jobCode.index,
      );
      if (deactivating) {
        systemJob.isActive = false;
        _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} deactivating jobcode - ${systemJob.title}');
      } else if (reactivating) {
        systemJob.isActive = true;
        _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} reactivating jobcode - ${systemJob.title}');
      } else {
        systemJob.title = titleController.text;
        systemJob.section = sectionIdx.value;
        systemJob.breaks = codeBreaks.value;
      }

      deactivating = false;
      reactivating = false;

      final QueryResult<Object> updateJobCodesResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_JOB_CODES,
          ),
          variables: <String, dynamic>{
            "_eq": "systemSetting",
            "document": systemSettingRecord.document,
          },
        ),
      );

      if (updateJobCodesResult.hasException) {
        throw updateJobCodesResult.exception.toString();
      }
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} updated jobcode - ${systemJob.title}");
      _notificationService.success("Update Job Code Success");
      await Get.offNamed(AppRoutes.EMPLOYEES_JOB_CODES, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Job Code Update failed!");
      _logger.severe(
        "Error updating job code.",
        err,
        stack,
      );
    }
  }

  Future<void> getSectionsConfig() async {
    try {
      final QueryResult<Object> configResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_SECTIONS_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SectionsJsonRecord sectionsRecord = configList
            .map(
              (dynamic config) => SectionsJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        sectionsList.value = sectionsRecord.document.sections;
      }
    } catch (err, stack) {
      _notificationService.error("Error Getting Sections");
      _logger.severe(
        "Error getting sections config.",
        err,
        stack,
      );
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Job Codes",
      _graphqlService,
    );
    canEdit.value = edit;
  }

  Future<void> deactivatePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Job Codes",
      _graphqlService,
    );
    canDeactivate.value = !delete;
  }

  void flipBreak(Break brk) {
    codeBreaks.value = codeBreaks.value ^ (1 << brk.idx);
  }
}
