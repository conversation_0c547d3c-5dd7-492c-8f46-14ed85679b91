import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/employees/dialogs/add_employee/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

class EmployeesController extends GetxController {
  EmployeesController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final Logger employeeLogger = Logger('EmployeeLogger');

  final ActivityService _activityService = Get.find();
  final IdentityService _identityService = Get.find();
  RxBool activeEmps = true.obs;
  RxBool allEmps = false.obs;

  Future<void> createPermissionEmployee() async {
    final bool create = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Employees",
      _graphqlService,
    );
    if (create) {
      employeeLogger.info('accessed create employee');
      await Get.bottomSheet(
        ThinBottomSheet(
          child: AddEmployeeDialog(),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
      );
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> viewEmployeePermission(Employee employee) async {
    final Activity insertActivity = Activity.empty();
    insertActivity.emp_id = CURRENT_EMPLOYEE.value.id;
    insertActivity.term_num = _identityService.terminalNumber;

    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Employees",
      _graphqlService,
    );
    if (view) {
      employeeLogger.info('viewing employee ${employee.employee_full_name} (${employee.employee})');
      await Get.toNamed(
        AppRoutes.EMPLOYEES_VIEW,
        id: AppRoutes.id,
        arguments: <String, dynamic>{
          "employee": employee,
        },
      );

      insertActivity.activity = ActivityFlags.BOH_VIEW_EMPLOYEE.index;
      insertActivity.str_data = "View employee granted";
      await _activityService.insertActivity(activity: insertActivity);
    } else {
      _notificationService.error("You do not have permission");
    }
  }
}
