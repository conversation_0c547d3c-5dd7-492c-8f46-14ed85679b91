// ignore_for_file: always_specify_types, avoid_dynamic_calls

import 'package:backoffice/app/data/enums/cash_drawer/types.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/pay_rate.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('AddEmployeeDialogController');

class AddEmployeeDialogController extends GetxController {
  AddEmployeeDialogController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  // final SyncService _syncService = Get.find();
  final GlobalKey<FormState> addEmployeeFormKey = GlobalKey<FormState>();

  Rxn<int> assignedJobCodeId = Rxn<int>();
  final Rx<String> assignedEmployeeClassId = "".obs;
  final Rx<Employee> newEmployee = Employee.empty().obs;
  final RxList<SystemSettingJsonRecordJobCode> availableJobCodes = <SystemSettingJsonRecordJobCode>[].obs;
  final Rx<MerchantJsonRecord> merchantConfig = MerchantJsonRecord.empty().obs;

  final ScrollController scrollController = ScrollController();

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController pinSetController = TextEditingController();
  final TextEditingController pinConfirmController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController zipCodeController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emContactNameController = TextEditingController();
  final TextEditingController emContactNumberController = TextEditingController();
  final TextEditingController birthdateController = TextEditingController();
  final TextEditingController hireDateController = TextEditingController();
  final TextEditingController reviewDateController = TextEditingController();
  final TextEditingController employeeIdController = TextEditingController();

  final RxList<SystemDeviceJsonRecordTerminal> terminals = <SystemDeviceJsonRecordTerminal>[].obs;
  final Map<String, int> drawerSelection = <String, int>{};

  @override
  Future<void> onInit() async {
    await loadJobCodes();
    await loadTerminals();
    await getMerchantConfig();
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> getMerchantConfig() async {
    final QueryResult<Object> configResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
        ),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    if ((configResult.data!['json_record']! as List).isEmpty) throw "No Configs Found";

    merchantConfig.value = MerchantJsonRecord.fromJson(configResult.data!['json_record'][0] as Map<String, dynamic>);
  }

  Future<void> loadJobCodes() async {
    try {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            subscription GET_JSON_RECORD {
              json_record(where: {record_key: {_eq: "systemSetting"}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
        ),
      );

      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }

      final SystemSettingJsonRecord systemSettingRecord = SystemSettingJsonRecord.fromJson(
        (result.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );

      availableJobCodes.value = systemSettingRecord.document.jobCodes.where((SystemSettingJsonRecordJobCode job) => job.isActive != false).toList();
      if (availableJobCodes.isEmpty) {
        _notificationService.error("No assignable job codes found");
        return;
      }
      assignedJobCodeId.value = 1;
      availableJobCodes.sort(
        (SystemSettingJsonRecordJobCode a, SystemSettingJsonRecordJobCode b) => a.index.compareTo(b.index),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error loading job codes.",
        err,
        stack,
      );
    }
  }

  Future<void> loadTerminals() async {
    try {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_SYSTEM_DEVICE {
              json_record(where: {record_key: {_eq: "systemDevice"}}) {
                document
                record_key
                updated_at
              }
            }
          ''',
          ),
        ),
      );
      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }
      final SystemDeviceJsonRecord systemDeviceRecord = SystemDeviceJsonRecord.fromJson(
        (result.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );
      terminals.value = systemDeviceRecord.document.terminal;
    } catch (err, stack) {
      _logger.severe(
        "Error loading terminals.",
        err,
        stack,
      );
    }
  }

  Future<void> addEmployee() async {
    try {
      if (!addEmployeeFormKey.currentState!.validate()) throw "Invalid form";

      final QueryResult passwordResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(
            '''
              query GET_EMPLOYEE(\$password: String!) {
                employee(where: {password: {_eq: \$password}}) {
                  employee
                }
              }
            ''',
          ),
          variables: {
            "password": pinConfirmController.text,
          },
        ),
      );

      if ((passwordResult.data!['employee']! as List).isNotEmpty) {
        return _notificationService.error("Please select a different pin");
      }

      final QueryResult employeeIdResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(
            '''
              query MyQuery(\$_eq: Int) {
                employee(where: {id: {_eq: \$_eq}}) {
                  created_at
                  document
                  created_by
                  employee
                  employee_class
                  employee_full_name
                  id
                  password
                  is_active
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: {
            "_eq": employeeIdController.text,
          },
        ),
      );
      if ((employeeIdResult.data!['employee']! as List).isNotEmpty) {
        return _notificationService.error("Employee ID already exists!");
      }

      // remove hard coated ids
      newEmployee.value.id = int.parse(employeeIdController.text);

      newEmployee.value.document = EmployeeDocument(
        city: cityController.text,
        state: stateController.text,
        address: addressController.text,
        zipCode: zipCodeController.text,
        birthDate: birthdateController.text,
        hireDate: hireDateController.text,
        lastName: lastNameController.text,
        firstName: firstNameController.text,
        reviewDate: reviewDateController.text,
        phoneNumber: phoneNumberController.text,
        emergencyContact: emContactNameController.text,
        emergencyContactPhone: emContactNumberController.text,
        payRates: [PayRate(payRate: 0, jobCode: assignedJobCodeId.value!, index: 1)],
      );
      newEmployee.value.created_by = CURRENT_EMPLOYEE.value.employee;
      newEmployee.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      newEmployee.value.is_active = true;
      newEmployee.value.password = pinConfirmController.text;
      newEmployee.value.employee_class = assignedEmployeeClassId.value;
      newEmployee.value.document.terminalDrawers = drawerSelection;

      final Map<String, dynamic> santizedEmployee = Helpers.sanitizeEntity(
        newEmployee.value.toJson(),
        [
          'employee',
          'created_at',
          'updated_at',
          'employee_full_name',
        ],
      );

      final QueryResult addEmployeeResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
              mutation ADD_EMPLOYEE(\$employee: employee_insert_input!) {
                insert_employee_one(object: \$employee) {
                  employee
                }
              }
            ''',
          ),
          variables: {
            "employee": santizedEmployee,
          },
        ),
      );

      if (addEmployeeResult.hasException) {
        return _notificationService.error(addEmployeeResult.exception.toString());
      }

      final String createdEmpID = (addEmployeeResult.data!['insert_employee_one'] as Map<String, dynamic>)['employee'].toString();
      _logger.info("Added New employee ($createdEmpID)");
      _notificationService.success("Employee added!");
      Get.back();
    } catch (err, stack) {
      _logger.severe(
        "Error adding employee.",
        err,
        stack,
      );
    }
  }

  String getDrawerString(int i, SystemDeviceJsonRecordTerminal terminal) {
    if (i < 0) return "Unassigned";
    final TerminalDrawer? drawer = terminal.cashDrawers.firstWhereOrNull((TerminalDrawer d) => d.idx == i);
    return drawer == null ? "Drawer ${i + 1} - None" : "Drawer ${i + 1} - ${CashDrawerTypes.values[drawer.type].friendlyString}";
  }
}
