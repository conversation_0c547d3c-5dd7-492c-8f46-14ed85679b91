import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('ChangePinDialogController');

class ChangePinDialogController extends GetxController {
  ChangePinDialogController(this.currentEmployee);
  Rx<Employee> currentEmployee;

  final GlobalKey<FormState> changePinKey = GlobalKey<FormState>();
  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();

  TextEditingController pinSetController = TextEditingController();
  TextEditingController pinConfirmController = TextEditingController();

  Future<void> changePin() async {
    try {
      if (!changePinKey.currentState!.validate()) throw "Invalid Pin";

      currentEmployee.value.password = pinConfirmController.text;
      currentEmployee.value.updated_at = DateTime.now();
      currentEmployee.value.updated_by = CURRENT_EMPLOYEE.value.employee;

      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_EMPLOYEE_BY_PASSWORD(\$password: String) {
              employee(where: {password: {_eq: \$password}}) {
                employee
                is_active
                password
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "password": pinConfirmController.text,
          },
        ),
      );

      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }

      if ((result.data!['employee']! as List).isNotEmpty) {
        return _notificationService.error("Please choose a different password");
      }

      final QueryResult<Object> changePasswordResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation UPDATE_EMPLOYEE_PASSWORD(\$employee: uuid!, \$password: String) {
              update_employee_by_pk(pk_columns: {employee: \$employee}, _set: {password: \$password}) {
                employee
                password
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
            "password": pinConfirmController.text,
          },
        ),
      );

      if (changePasswordResult.hasException) {
        return _notificationService.error(changePasswordResult.exception.toString());
      }

      // _syncService.sync(
      //   table: "employee",
      //   pk: changePasswordResult.data?["update_employee_by_pk"]["employee"] as String,
      //   action: SyncAction.UPDATE,
      // );

      _notificationService.success("Password successfully changed");
      Get.back();
    } catch (err, stack) {
      _logger.severe(
        "Error changing pin.",
        err,
        stack,
      );
    }
  }
}
