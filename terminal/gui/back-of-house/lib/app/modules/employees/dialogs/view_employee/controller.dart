import 'package:backoffice/app/data/enums/cash_drawer/types.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/employee_class.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/pay_rate.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:logging/logging.dart';

final Logger _logger = Logger('ViewEmployeeDialogController');

class ViewEmployeeDialogController extends GetxController {
  ViewEmployeeDialogController(
    this.employee,
  );

  final Employee employee;

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> viewEmployeeFormKey = GlobalKey<FormState>();

  final Rx<Employee> currentEmployee = Employee.empty().obs;
  final Rx<SystemSettingJsonRecordJobCode> newEmployeeJobCode = SystemSettingJsonRecordJobCode.empty().obs;
  final Rx<MerchantJsonRecord> merchantConfig = MerchantJsonRecord.empty().obs;
  final Rx<String> assignedJobCodeId = "".obs;
  final Rx<String> assignedEmployeeClassId = "".obs;

  final RxString birthDate = ''.obs;
  final RxList<SystemSettingJsonRecordJobCode> currentJobCodeTitles = <SystemSettingJsonRecordJobCode>[].obs;
  final RxList<SystemSettingJsonRecordJobCode> availableJobCodes = <SystemSettingJsonRecordJobCode>[].obs;
  final RxList<EmployeeClass> availableEmployeeClasses = <EmployeeClass>[].obs;

  final RxBool canEdit = true.obs;
  final RxBool canDelete = false.obs;
  final RxBool readOnly = true.obs;
  final RxBool canChangePin = true.obs;
  final RxBool canDeactivate = false.obs;
  final RxBool canUpdateEmployee = false.obs;
  final RxBool canEditClass = false.obs;
  final RxBool isActive = true.obs;

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController pinSetController = TextEditingController();
  final TextEditingController pinConfirmController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController zipCodeController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emContactNameController = TextEditingController();
  final TextEditingController emContactNumberController = TextEditingController();
  final TextEditingController birthdateController = TextEditingController();
  final TextEditingController hireDateController = TextEditingController();
  final TextEditingController reviewDateController = TextEditingController();
  final TextEditingController employeeIdController = TextEditingController();

  final RxList<SystemDeviceJsonRecordTerminal> terminals = <SystemDeviceJsonRecordTerminal>[].obs;
  final Map<String, int> drawerSelection = <String, int>{};

  @override
  Future<void> onInit() async {
    currentEmployee.value = employee;

    firstNameController.text = currentEmployee.value.document.firstName ?? "";
    lastNameController.text = currentEmployee.value.document.lastName ?? "";
    addressController.text = currentEmployee.value.document.address ?? "";
    cityController.text = currentEmployee.value.document.city ?? "";
    stateController.text = currentEmployee.value.document.state ?? "";
    birthdateController.text = currentEmployee.value.document.birthDate.toString();
    zipCodeController.text = currentEmployee.value.document.zipCode ?? "";
    phoneNumberController.text = currentEmployee.value.document.phoneNumber ?? "";
    emContactNameController.text = currentEmployee.value.document.emergencyContact ?? "";
    emContactNumberController.text = currentEmployee.value.document.emergencyContactPhone ?? "";
    birthdateController.text = currentEmployee.value.document.birthDate ?? "";
    hireDateController.text = currentEmployee.value.document.hireDate ?? "";
    reviewDateController.text = currentEmployee.value.document.reviewDate ?? "";
    employeeIdController.text = currentEmployee.value.id!.toString();
    assignedEmployeeClassId.value = currentEmployee.value.employee_class;
    isActive.value = currentEmployee.value.is_active;

    await loadJobCodes();
    await loadEmployeeJobCodes();
    await loadEmployeeClasses();
    await loadTerminals();
    await getMerchantConfig();
    await editPermission();
    await deletePermission();
    await editClassPermission();
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await editPermission();
    await deletePermission();

    super.onReady();
  }

  Future<void> getMerchantConfig() async {
    final QueryResult<Object> configResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
        ),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    // ignore: always_specify_types
    if ((configResult.data!['json_record']! as List).isEmpty) throw "No Configs Found";

    // ignore: avoid_dynamic_calls
    merchantConfig.value = MerchantJsonRecord.fromJson(configResult.data!['json_record'][0] as Map<String, dynamic>);
  }

  Future<void> loadEmployeeClasses() async {
    try {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_EMPLOYEE_CLASSES {
                employee_class {
                  employee_class
                  created_at
                  created_by
                  clearance
                  document
                  title
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
        ),
      );

      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }

      availableEmployeeClasses.value = (result.data!['employee_class'] as List<dynamic>)
          .map(
            (dynamic employeeClass) => EmployeeClass.fromJson(employeeClass as Map<String, dynamic>),
          )
          .toList();

      availableEmployeeClasses.removeWhere((EmployeeClass element) => element.title == 'sa');
    } catch (err, stack) {
      _logger.severe(
        "Error loading employee classes.",
        err,
        stack,
      );
    }
  }

  Future<void> loadJobCodes() async {
    try {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            subscription GET_JSON_RECORD {
              json_record(where: {record_key: {_eq: "systemSetting"}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
        ),
      );

      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }

      final SystemSettingJsonRecord systemSettingRecord = SystemSettingJsonRecord.fromJson(
        (result.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );

      availableJobCodes.value = systemSettingRecord.document.jobCodes.where((SystemSettingJsonRecordJobCode job) => job.isActive != false).toList();
      availableJobCodes.sort(
        (SystemSettingJsonRecordJobCode a, SystemSettingJsonRecordJobCode b) => a.index.compareTo(b.index),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error loading job codes.",
        err,
        stack,
      );
    }
  }

  Future<void> loadTerminals() async {
    try {
      final QueryResult<Object> result = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_SYSTEM_DEVICE {
              json_record(where: {record_key: {_eq: "systemDevice"}}) {
                document
                record_key
                updated_at
              }
            }
          ''',
          ),
        ),
      );
      if (result.hasException) {
        return _notificationService.error(result.exception!.graphqlErrors.toString());
      }
      final SystemDeviceJsonRecord systemDeviceRecord = SystemDeviceJsonRecord.fromJson(
        (result.data!['json_record'] as List<dynamic>).first as Map<String, dynamic>,
      );
      terminals.value = systemDeviceRecord.document.terminal;
      for (final SystemDeviceJsonRecordTerminal terminal in terminals) {
        if ((currentEmployee.value.document.terminalDrawers[terminal.idx.toString()] ?? -1) >= 0) {
          drawerSelection[terminal.idx.toString()] = currentEmployee.value.document.terminalDrawers[terminal.idx.toString()] ?? -1;
        }
      }
    } catch (err, stack) {
      _logger.severe(
        "Error loading terminals.",
        err,
        stack,
      );
    }
  }

  Future<void> loadEmployeeJobCodes() async {
    try {
      for (final PayRate jobIndex in currentEmployee.value.document.payRates) {
        final SystemSettingJsonRecordJobCode jobCode = availableJobCodes.firstWhere(
          (SystemSettingJsonRecordJobCode jobCode) => jobCode.index == jobIndex.jobCode,
          orElse: () => SystemSettingJsonRecordJobCode.empty(),
        );
        if (jobCode.index != 0) currentJobCodeTitles.add(jobCode);
      }
    } catch (err, stack) {
      _logger.severe(
        "Error loading employee job codes.",
        err,
        stack,
      );
    }
  }

  Future<void> updateEmployee() async {
    try {
      if (!viewEmployeeFormKey.currentState!.validate()) throw "Invalid form";

      currentEmployee.value.document.firstName = firstNameController.text;
      currentEmployee.value.document.lastName = lastNameController.text;
      currentEmployee.value.document.address = addressController.text;
      currentEmployee.value.id = int.parse(employeeIdController.text);
      currentEmployee.value.updated_at = DateTime.now().toUtc();
      currentEmployee.value.document.city = cityController.text;
      currentEmployee.value.document.state = stateController.text;
      currentEmployee.value.document.zipCode = zipCodeController.text;
      currentEmployee.value.document.birthDate = birthdateController.text;
      currentEmployee.value.document.phoneNumber = phoneNumberController.text;
      currentEmployee.value.document.hireDate = hireDateController.text;
      currentEmployee.value.document.reviewDate = reviewDateController.text;
      currentEmployee.value.document.emergencyContact = emContactNameController.text;
      currentEmployee.value.document.emergencyContactPhone = emContactNumberController.text;
      currentEmployee.value.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentEmployee.value.employee_class = assignedEmployeeClassId.value;
      currentEmployee.value.id = int.parse(employeeIdController.text);
      currentEmployee.value.employee = employee.employee;
      currentEmployee.value.document.terminalDrawers = drawerSelection;

      final QueryResult<Object> updateEmployeeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_EMPLOYEE(\$employee: uuid = "", \$document: jsonb, \$employee_class: uuid, \$updated_by: uuid, \$id: Int, \$updated_at: timestamptz) {
            update_employee_by_pk(pk_columns: {employee: \$employee}, _set: {document: \$document, employee_class: \$employee_class, updated_by: \$updated_by, id: \$id, updated_at: \$updated_at}) {
              employee
              document
              employee_class
              password
              is_active
              id
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
            "employee_class": currentEmployee.value.employee_class,
            "updated_by": currentEmployee.value.updated_by,
            "updated_at": currentEmployee.value.updated_at.toString(),
            "id": currentEmployee.value.id,
            "document": <String, dynamic>{
              "firstName": currentEmployee.value.document.firstName,
              "lastName": currentEmployee.value.document.lastName,
              "address": currentEmployee.value.document.address,
              "city": currentEmployee.value.document.city,
              "state": currentEmployee.value.document.state,
              "zipCode": currentEmployee.value.document.zipCode,
              "payRates": currentEmployee.value.document.payRates,
              "birthDate": currentEmployee.value.document.birthDate,
              "phoneNumber": currentEmployee.value.document.phoneNumber,
              "reviewDate": currentEmployee.value.document.reviewDate,
              "hireDate": currentEmployee.value.document.hireDate,
              "emergencyContact": currentEmployee.value.document.emergencyContact,
              "emergencyContactPhone": currentEmployee.value.document.emergencyContactPhone,
              "terminalDrawers": currentEmployee.value.document.terminalDrawers,
            },
          },
        ),
      );

      if (updateEmployeeResult.hasException) {
        return _notificationService.error(updateEmployeeResult.exception.toString());
      }
      _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} updated employee (${currentEmployee.value.employee})");
      _notificationService.success("Update employee success");
      await Get.offNamed(AppRoutes.EMPLOYEES, id: AppRoutes.id);
    } catch (err, stack) {
      _notificationService.error("Employee update failed!");
      _logger.severe(
        "Error updating employee.",
        err,
        stack,
      );
    }
  }

  Future<void> addJobCodes(
    List<SystemSettingJsonRecordJobCode> jobCodesToAdd,
  ) async {
    try {
      currentEmployee.value.document.payRates.sort((PayRate a, PayRate b) => a.index.compareTo(b.index));

      for (int i = 0; i < jobCodesToAdd.length; i++) {
        final PayRate payRateCheck = PayRate(
          payRate: 0,
          jobCode: jobCodesToAdd[i].index,
          index: currentEmployee.value.document.payRates.isEmpty ? 1 : currentEmployee.value.document.payRates.last.index + 1,
        );
        if (!currentEmployee.value.document.payRates.any((PayRate element) => element.jobCode == payRateCheck.jobCode)) {
          currentEmployee.value.document.payRates.add(payRateCheck);
        }
      }

      final QueryResult<Object> addEmployeeJobCodeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_EMPLOYEE(\$employee: uuid!, \$document: jsonb) {
            update_employee_by_pk(pk_columns: {employee: \$employee}, _append: {document: \$document}) {
              employee
              document
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
            "document": currentEmployee.value.document,
          },
        ),
      );

      if (addEmployeeJobCodeResult.hasException) {
        return _notificationService.error(addEmployeeJobCodeResult.exception.toString());
      } else {
        currentJobCodeTitles.addAll(jobCodesToAdd);
      }

      _notificationService.success("Employee Job Codes Updated");
    } catch (err, stack) {
      _notificationService.error("Employee Job Code failed!");
      _logger.severe(
        "Error adding job code.",
        err,
        stack,
      );
    }
  }

  Future<void> deleteEmployeeJobCode(int jobCodeIdx) async {
    try {
      final Employee employeeRes = currentEmployee.value;

      employeeRes.document.payRates.removeWhere((PayRate rate) => rate.jobCode == jobCodeIdx);

      employeeRes.document.payRates.sort((PayRate a, PayRate b) => a.index.compareTo(b.index));

      for (int i = 0; i < employeeRes.document.payRates.length; i++) {
        employeeRes.document.payRates[i].index = i + 1;
      }

      final QueryResult<Object> removeEmployeeJobCodeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_EMPLOYEE(\$employee: uuid!, \$document: jsonb) {
            update_employee_by_pk(pk_columns: {employee: \$employee}, _append: {document: \$document}) {
              employee
              document
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
            "document": employeeRes.document,
          },
        ),
      );

      if (removeEmployeeJobCodeResult.hasException) {
        return _notificationService.error(removeEmployeeJobCodeResult.exception.toString());
      }

      currentJobCodeTitles.remove(availableJobCodes[jobCodeIdx - 1]);
      _logger.shout("${CURRENT_EMPLOYEE.value.employee_full_name} deleted Employee Job Code ${availableJobCodes[jobCodeIdx - 1]}");
      _notificationService.success("Employee Job Code Deleted!");
    } catch (err, stack) {
      _logger.severe(
        "Error deleting job code.",
        err,
        stack,
      );
    }
  }

  Future<void> deactivateEmployee() async {
    try {
      final QueryResult<Object> deactivateEmployeeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation UPDATE_EMPLOYEE(\$employee: uuid!) {
                update_employee_by_pk(pk_columns: {employee: \$employee}, _set:{is_active:false}){
                  employee
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
          },
        ),
      );

      if (deactivateEmployeeResult.hasException) {
        return _notificationService.error(deactivateEmployeeResult.exception.toString());
      }
      _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} Deactivated Employee (${currentEmployee.value.employee})');
      _notificationService.success("Employee Deactivated!");
      Get.back();
    } catch (err, stack) {
      _notificationService.error("Deactive Employee Failed!");
      _logger.severe(
        "Error deactivating emplyee.",
        err,
        stack,
      );
    }
  }

  Future<void> activateEmployee() async {
    try {
      final QueryResult<Object> activateEmployeeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation UPDATE_EMPLOYEE(\$employee: uuid!) {
                update_employee_by_pk(pk_columns: {employee: \$employee}, _set:{is_active:true}){
                  employee
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee": currentEmployee.value.employee,
          },
        ),
      );

      if (activateEmployeeResult.hasException) {
        return _notificationService.error(activateEmployeeResult.exception.toString());
      }
      isActive.value = true;
      _logger.shout('${CURRENT_EMPLOYEE.value.employee_full_name} Reactived Employee (${currentEmployee.value.employee})');

      _notificationService.success("Employee Activated!");
    } catch (err, stack) {
      _notificationService.error("Activate Employee Failed!");
      _logger.severe(
        "Error activating emplyee.",
        err,
        stack,
      );
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Employees",
      _graphqlService,
    );
    canEdit.value = edit;
    readOnly.value = !edit;
    canChangePin.value = !edit;
    canUpdateEmployee.value = !edit;
  }

  Future<void> editClassPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Employee classes",
      _graphqlService,
    );

    canEditClass.value = edit;
  }

  Future<void> deletePermission() async {
    final bool delete = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Employees",
      _graphqlService,
    );
    canDelete.value = delete;
    canDeactivate.value = !delete;
  }

  void getUnassignedJobCodes() {
    for (final SystemSettingJsonRecordJobCode job in availableJobCodes) {
      if (currentJobCodeTitles.contains(job)) {
        continue;
      } else {
        availableJobCodes.add(job);
      }
    }
  }

  String getDrawerString(int i, SystemDeviceJsonRecordTerminal terminal) {
    if (i < 0) return "Unassigned";
    final TerminalDrawer? drawer = terminal.cashDrawers.firstWhereOrNull((TerminalDrawer d) => d.idx == i);
    return drawer == null ? "Drawer ${i + 1} - None" : "Drawer ${i + 1} - ${CashDrawerTypes.values[drawer.type].friendlyString}";
  }
}
