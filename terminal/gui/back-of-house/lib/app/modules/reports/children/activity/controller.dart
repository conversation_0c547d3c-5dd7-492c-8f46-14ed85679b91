// ignore_for_file: avoid_dynamic_calls, always_specify_types

import 'package:backoffice/app/data/models/report.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class ActivityController extends GetxController {
  ActivityController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final Logger _logger = Logger('activityReportController');

  //general variables
  final ScrollController verticalScrollController = ScrollController();
  final ScrollController horizontalScrollController = ScrollController();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();

  // report variables
  final RxList<Sale> reports = <Sale>[].obs;
  List<Activity> activities = <Activity>[].obs;

  // printer variables
  final RxList<Report> printerRows = <Report>[].obs;

  // terminal dropdown variables
  final RxList<dynamic> terminalList = <dynamic>[].obs;
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTerminal = Rxn<int>();

  // employee dropdown variables
  final RxList<Employee> employeeList = <Employee>[].obs;
  final Rxn<int> selectedEmployee = Rxn<int>();

  //date selection variables
  final Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;

  @override
  Future<void> onInit() async {
    await getEmployees();
    await getTerminals();
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

//scope handlers to change the selected scope in reports drawer
  void scopeHandler(int? value) {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = terminalList.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    resetTotalRows();
  }

  void resetTotalRows() {
    printerRows.clear();
  }

//get employees list to referance  for report and report drawer
  Future<void> getEmployees() async {
    try {
      final QueryResult<Object> getEmployeesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_EMPLOYEE {
              employee {
                updated_by
                updated_at
                password
                is_active
                id
                employee_full_name
                employee_class
                employee
                document
                created_by
                created_at
              }
            }
          ''',
          ),
        ),
      );

      if (getEmployeesResult.hasException) {
        return _notificationService.error("error getting employees");
      }

      employeeList.value = (getEmployeesResult.data!['employee'] as List<dynamic>)
          .map(
            (dynamic data) => Employee.fromJson(data as Map<String, dynamic>),
          )
          .toList();
      employeeList.sort(
        (Employee a, Employee b) => a.employee_full_name!.compareTo(b.employee_full_name!),
      );
    } catch (err, stack) {
      _logger.severe("Error Getting Employees", err, stack);
    }
  }

//get terminal list for report drawer variable
  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) {
      return _notificationService.error(getTerminalsResult.exception.toString());
    }

    terminalList.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

//print report to reciept printer
  Future<void> printReport() async {
    final String start = Helpers.getReportStartString(dateRangeController.value);
    final String end = Helpers.getReportEndString(dateRangeController.value);
    PrintJob printJobType;
    printJobType = PrinterInterface.buildActivityReport(
      activityRows: activities,
      scope: selectedScope.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(start).toLocal()),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(end).toLocal()),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult printJobResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe('Error Printing Activity Report', err, stack);
    }
  }
}
