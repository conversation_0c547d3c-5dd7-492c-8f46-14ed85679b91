import 'package:backoffice/app/data/models/all_sale_windows.dart';
import 'package:backoffice/app/data/models/cumulative_discounts.dart';
import 'package:backoffice/app/data/models/cumulative_gift_sales.dart';
import 'package:backoffice/app/data/models/cumulative_media.dart';
import 'package:backoffice/app/data/models/cumulative_sales.dart';
import 'package:backoffice/app/data/models/cumulative_sales_tax.dart';
import 'package:backoffice/app/data/models/cumulative_stats.dart';
import 'package:backoffice/app/data/models/cumulative_tip_grat.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
// import 'package:pdf/widgets.dart' as pw;

class CumulativeSalesController extends GetxController {
  CumulativeSalesController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final ReportService _reportService = Get.find();
  final BohReportService _bohReportService = Get.find();

  final RxList<Sale> reports = <Sale>[].obs;

  final Logger _logger = Logger('CumulativeSalesController');

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();

  final RxList<dynamic> terminalList = <dynamic>[].obs;
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTerminal = Rxn<int>();

  List<CumulativeSalesReport> reportRows = <CumulativeSalesReport>[].obs;
  List<CumulativeMedia> mediaRows = <CumulativeMedia>[].obs;
  List<CumulativeStats> statsRows = <CumulativeStats>[].obs;
  List<CumulativeDiscounts> discountRows = <CumulativeDiscounts>[].obs;

  final TextEditingController dateSelectController = TextEditingController();

  DateTime selectedDate = DateTime.now();

  final RxString startDate = DateTime.now().toString().obs;
  final RxString endDate = DateTime.now().toString().obs;
  final RxString startWeek = DateTime.now().toString().obs;
  final RxString endWeek = DateTime.now().toString().obs;
  final RxString startMonth = DateTime.now().toString().obs;
  final RxString endMonth = DateTime.now().toString().obs;
  final RxString startYear = DateTime.now().toString().obs;
  final RxString endYear = DateTime.now().toString().obs;

  final RxInt dayCount = 0.obs;
  final RxInt dayTotal = 0.obs;
  final RxInt weekCount = 0.obs;
  final RxInt weekTotal = 0.obs;
  final RxInt monthCount = 0.obs;
  final RxInt monthTotal = 0.obs;
  final RxInt yearCount = 0.obs;
  final RxInt yearTotal = 0.obs;

  final RxInt dayMediaCount = 0.obs;
  final RxInt dayMediaTotal = 0.obs;
  final RxInt weekMediaCount = 0.obs;
  final RxInt weekMediaTotal = 0.obs;
  final RxInt monthMediaCount = 0.obs;
  final RxInt monthMediaTotal = 0.obs;
  final RxInt yearMediaCount = 0.obs;
  final RxInt yearMediaTotal = 0.obs;

  final RxInt dayPaidOut = 0.obs;
  final RxInt weekPaidOut = 0.obs;
  final RxInt monthPaidOut = 0.obs;
  final RxInt yearPaidOut = 0.obs;

  final RxInt dayTakeoutFees = 0.obs;
  final RxInt weekTakeoutFees = 0.obs;
  final RxInt monthTakeoutFees = 0.obs;
  final RxInt yearTakeoutFees = 0.obs;

  final RxInt dayHouseCharges = 0.obs;
  final RxInt weekHouseCharges = 0.obs;
  final RxInt monthHouseCharges = 0.obs;
  final RxInt yearHouseCharges = 0.obs;

  final RxInt dayDiscountTotal = 0.obs;
  final RxInt weekDiscountTotal = 0.obs;
  final RxInt monthDiscountTotal = 0.obs;
  final RxInt yearDiscountTotal = 0.obs;

  final RxInt dayGiftTotal = 0.obs;
  final RxInt weekGiftTotal = 0.obs;
  final RxInt monthGiftTotal = 0.obs;
  final RxInt yearGiftTotal = 0.obs;

  final RxInt dayAccountable = 0.obs;
  final RxInt weekAccountable = 0.obs;
  final RxInt monthAccountable = 0.obs;
  final RxInt yearAccountable = 0.obs;
  //tests

  final RxList<CumulativeSalesTax> taxList = <CumulativeSalesTax>[].obs;
  final RxList<CumulativeTipAndGrat> tipList = <CumulativeTipAndGrat>[].obs;

  AllSaleWindows saleWindow = AllSaleWindows.empty();

  final ScrollController verticalScrollController = ScrollController();
  final ScrollController horizontalScrollController = ScrollController();

  @override
  Future<void> onInit() async {
    if (dateSelectController.text == "") {
      dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
      saleWindow = Helpers.getAllReportWindows(selectedDate: selectedDate);
      startDate.value = saleWindow.startDate!;
      endDate.value = saleWindow.endDate!;
      startWeek.value = saleWindow.startWeek!;
      endWeek.value = saleWindow.endWeek!;
      startMonth.value = saleWindow.startMonth!;
      endMonth.value = saleWindow.endMonth!;
      startYear.value = saleWindow.startYear!;
      endYear.value = saleWindow.endYear!;
    }
    await getCumulativeSalesTax();
    await getCumulativeTipGrat();
    await getCumulativePaidOut();
    await getCumulativeHouseCharges();
    await getCumulativeGiftSales();
    await getTakeoutFeeTotal();

    super.onInit();
  }

  @override
  Future<void> onReady() async {
    calculateAccountable();
    super.onReady();
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      // ignore: avoid_dynamic_calls
      final dynamic selectedTerminalObject = terminalList.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      // ignore: avoid_dynamic_calls
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
  }

  Future<void> dateHandler(String value) async {
    dateSelectController.text = value;
    selectedDate = DateTime.parse(dateSelectController.text);

    saleWindow = Helpers.getAllReportWindows(selectedDate: selectedDate);
    startDate.value = saleWindow.startDate!;
    endDate.value = saleWindow.endDate!;
    startWeek.value = saleWindow.startWeek!;
    endWeek.value = saleWindow.endWeek!;
    startMonth.value = saleWindow.startMonth!;
    endMonth.value = saleWindow.endMonth!;
    startYear.value = saleWindow.startYear!;
    endYear.value = saleWindow.endYear!;

    await getCumulativeSalesTax();
    await getCumulativeTipGrat();
    await getCumulativePaidOut();
    await getCumulativeHouseCharges();
    await getCumulativeGiftSales();
    await getTakeoutFeeTotal();
  }

  void resetDepartmentTotals() {
    dayCount.value = 0;
    dayTotal.value = 0;
    weekCount.value = 0;
    weekTotal.value = 0;
    monthCount.value = 0;
    monthTotal.value = 0;
    yearCount.value = 0;
    yearTotal.value = 0;
  }

  void resetMediaTotals() {
    dayMediaCount.value = 0;
    dayMediaTotal.value = 0;
    weekMediaCount.value = 0;
    weekMediaTotal.value = 0;
    monthMediaCount.value = 0;
    monthMediaTotal.value = 0;
    yearMediaCount.value = 0;
    yearMediaTotal.value = 0;
  }

  void resetDiscountTotals() {
    dayDiscountTotal.value = 0;
    weekDiscountTotal.value = 0;
    monthDiscountTotal.value = 0;
    yearDiscountTotal.value = 0;
  }

  void resetGiftTotals() {
    dayGiftTotal.value = 0;
    weekGiftTotal.value = 0;
    monthGiftTotal.value = 0;
    yearGiftTotal.value = 0;
  }

  Future<void> getTakeoutFeeTotal() async {
    try {
      dayTakeoutFees.value = 0;
      weekTakeoutFees.value = 0;
      monthTakeoutFees.value = 0;
      yearTakeoutFees.value = 0;
      final Either<ServiceError, int> dayTakeOutFeeRes =
          await _bohReportService.getTakeOutFee(startDate: startDate.value, endDate: endDate.value, selectedTerminal: selectedTerminal.value);

      dayTakeOutFeeRes.fold(
        (ServiceError l) {
          _notificationService.error(l.message);
          throw l.message;
        },
        (int res) {
          dayTakeoutFees.value = res;
        },
      );

      final Either<ServiceError, int> weekTakeOutFeeRes =
          await _bohReportService.getTakeOutFee(startDate: startWeek.value, endDate: endWeek.value, selectedTerminal: selectedTerminal.value);

      weekTakeOutFeeRes.fold(
        (ServiceError l) {
          _notificationService.error(l.message);
          throw l.message;
        },
        (int res) {
          weekTakeoutFees.value = res;
        },
      );

      final Either<ServiceError, int> monthTakeoutFeesRes =
          await _bohReportService.getTakeOutFee(startDate: startMonth.value, endDate: endMonth.value, selectedTerminal: selectedTerminal.value);

      monthTakeoutFeesRes.fold(
        (ServiceError l) {
          _notificationService.error(l.message);
          throw l.message;
        },
        (int res) {
          monthTakeoutFees.value = res;
        },
      );

      final Either<ServiceError, int> yearTakeoutFeesRes =
          await _bohReportService.getTakeOutFee(startDate: startYear.value, endDate: endYear.value, selectedTerminal: selectedTerminal.value);

      yearTakeoutFeesRes.fold(
        (ServiceError l) {
          _notificationService.error(l.message);
          throw l.message;
        },
        (int res) {
          yearTakeoutFees.value = res;
        },
      );
    } catch (e, stack) {
      _logger.severe('Error getting takeout fees', e, stack);
    }
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) return _notificationService.error(getTerminalsResult.exception.toString());

    // ignore: avoid_dynamic_calls
    terminalList.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> getCumulativeSalesTax() async {
    final Either<ServiceError, List<CumulativeSalesTax>> taxRes = await _bohReportService.getCumulativeSalesTax(
      startDate: startDate.value,
      endDate: endDate.value,
      startWeek: startWeek.value,
      endWeek: endWeek.value,
      startMonth: startMonth.value,
      endMonth: endMonth.value,
      startYear: startYear.value,
      endYear: endYear.value,
      selectedTerminal: selectedTerminal.value,
    );

    taxRes.fold((ServiceError l) {
      _notificationService.error('Error getting sales taxes');
    }, (List<CumulativeSalesTax> r) {
      taxList.value = r;
    });
  }

  Future<void> getCumulativeTipGrat() async {
    final Either<ServiceError, List<CumulativeTipAndGrat>> tipRes = await _bohReportService.getCumulativeTipGrat(
      startDate: startDate.value,
      endDate: endDate.value,
      startWeek: startWeek.value,
      endWeek: endWeek.value,
      startMonth: startMonth.value,
      endMonth: endMonth.value,
      startYear: startYear.value,
      endYear: endYear.value,
      selectedTerminal: selectedTerminal.value,
    );

    tipRes.fold((ServiceError l) {
      _notificationService.error('Error getting tips');
    }, (List<CumulativeTipAndGrat> r) {
      tipList.value = r;
    });
  }

  Future<void> getCumulativeGiftSales() async {
    final Either<ServiceError, List<CumulativeGiftSales>> giftRes = await _bohReportService.getCumulativeGiftSales(
      startDate: startDate.value,
      endDate: endDate.value,
      startWeek: startWeek.value,
      endWeek: endWeek.value,
      startMonth: startMonth.value,
      endMonth: endMonth.value,
      startYear: startYear.value,
      endYear: endYear.value,
      selectedTerminal: selectedTerminal.value,
    );
    giftRes.fold(
      (ServiceError l) {
        _notificationService.error('Error getting Gift Sales');
      },
      (List<CumulativeGiftSales> r) {
        for (final CumulativeGiftSales element in r) {
          dayGiftTotal.value += element.day_amount;
          weekGiftTotal.value += element.week_amount;
          monthGiftTotal.value += element.month_amount;
          yearGiftTotal.value += element.year_amount;
        }
      },
    );
  }

  Future<void> getCumulativePaidOut() async {
    dayPaidOut.value = 0;
    weekPaidOut.value = 0;
    monthPaidOut.value = 0;
    yearPaidOut.value = 0;
    final Either<ServiceError, List<Sale>> dayPaidOutRes = await _reportService.getPaidOut(
      startDate: startDate.value,
      endDate: endDate.value,
      terminalIndex: null,
    );
    dayPaidOutRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin day Paid Out");
      },
      (List<Sale> r) {
        for (final Sale sale in r) {
          dayPaidOut.value += sale.document.saleHeader.total;
        }
      },
    );

    final Either<ServiceError, List<Sale>> weekPaidOutRes = await _reportService.getPaidOut(
      startDate: startWeek.value,
      endDate: endWeek.value,
      terminalIndex: null,
    );
    weekPaidOutRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin week Paid Out");
      },
      (List<Sale> r) {
        for (final Sale sale in r) {
          weekPaidOut.value += sale.document.saleHeader.total;
        }
      },
    );

    final Either<ServiceError, List<Sale>> monthPaidOutRes = await _reportService.getPaidOut(
      startDate: startMonth.value,
      endDate: endMonth.value,
      terminalIndex: null,
    );
    monthPaidOutRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin month Paid Out");
      },
      (List<Sale> r) {
        for (final Sale sale in r) {
          monthPaidOut.value += sale.document.saleHeader.total;
        }
      },
    );

    final Either<ServiceError, List<Sale>> yearPaidOutRes = await _reportService.getPaidOut(
      startDate: startYear.value,
      endDate: endYear.value,
      terminalIndex: null,
    );
    yearPaidOutRes.fold(
      (ServiceError l) {
        _notificationService.error("error getting year Paid Out");
      },
      (List<Sale> r) {
        for (final Sale sale in r) {
          yearPaidOut.value += sale.document.saleHeader.total;
        }
      },
    );
  }

  Future<void> getCumulativeHouseCharges() async {
    dayHouseCharges.value = 0;
    weekHouseCharges.value = 0;
    monthHouseCharges.value = 0;
    yearHouseCharges.value = 0;
    final Either<ServiceError, List<Sale>> dayHouseChargesRes = await _reportService.getHouseCharges(
      startDate: startDate.value,
      endDate: endDate.value,
      terminalIndex: null,
    );
    dayHouseChargesRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin day House Charges");
      },
      (List<Sale> r) {
        for (int i = 0; i < r.length; i++) {
          final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              dayHouseCharges.value += tender.amount!;
            }
          }
        }
      },
    );

    final Either<ServiceError, List<Sale>> weekhouseChargesRes = await _reportService.getHouseCharges(
      startDate: startWeek.value,
      endDate: endWeek.value,
      terminalIndex: null,
    );
    weekhouseChargesRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin week House Charges");
      },
      (List<Sale> r) {
        for (int i = 0; i < r.length; i++) {
          final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              weekHouseCharges.value += tender.amount!;
            }
          }
        }
      },
    );

    final Either<ServiceError, List<Sale>> monthHouseChargesRes = await _reportService.getHouseCharges(
      startDate: startMonth.value,
      endDate: endMonth.value,
      terminalIndex: null,
    );
    monthHouseChargesRes.fold(
      (ServiceError l) {
        _notificationService.error("error gettin month House Charges");
      },
      (List<Sale> r) {
        for (int i = 0; i < r.length; i++) {
          final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              monthHouseCharges.value += tender.amount!;
            }
          }
        }
      },
    );

    final Either<ServiceError, List<Sale>> yearHouseChargesRes = await _reportService.getHouseCharges(
      startDate: startYear.value,
      endDate: endYear.value,
      terminalIndex: null,
    );
    yearHouseChargesRes.fold(
      (ServiceError l) {
        _notificationService.error("error getting year House Charges");
      },
      (List<Sale> r) {
        for (int i = 0; i < r.length; i++) {
          final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              yearHouseCharges.value += tender.amount!;
            }
          }
        }
      },
    );
  }

  void calculateAccountable() {
    dayAccountable.value = 0;
    weekAccountable.value = 0;
    monthAccountable.value = 0;
    yearAccountable.value = 0;

    dayAccountable.value = dayTotal.value - dayPaidOut.value - dayHouseCharges.value - dayGiftTotal.value + dayTakeoutFees.value;
    weekAccountable.value = weekTotal.value - weekPaidOut.value - weekHouseCharges.value - weekGiftTotal.value + weekTakeoutFees.value;
    monthAccountable.value = monthTotal.value - monthPaidOut.value - monthHouseCharges.value - monthGiftTotal.value + monthTakeoutFees.value;
    yearAccountable.value = yearTotal.value - yearPaidOut.value - yearHouseCharges.value - yearGiftTotal.value + yearTakeoutFees.value;
  }
}
