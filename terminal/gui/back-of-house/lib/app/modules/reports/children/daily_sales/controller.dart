import 'package:backoffice/app/data/models/emp_discounts.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/major_report.dart';
import 'package:backoffice/app/data/models/media_report.dart';
import 'package:backoffice/app/data/models/order_type_report.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/data/view_models/items_by_department.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class DailySalesController extends GetxController {
  DailySalesController();

  final Logger _logger = Logger('DailySalesController');

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  final GlobalKey<FormState> formKey = GlobalKey();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();

  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;
  final List<CustomTableRow> salesByDeptRows = <CustomTableRow>[];

  /// maybe delete this later
  final RxList<SalesByDeptSummaryReport> dataRows = <SalesByDeptSummaryReport>[].obs;

  ///
  final RxList<dynamic> list = <dynamic>[].obs;
  final RxList<MediaReport> mediaList = <MediaReport>[].obs;
  final RxList<OrderTypeReport> orderTypeList = <OrderTypeReport>[].obs;
  final List<ItemsByDepartmentReport> itemRows = <ItemsByDepartmentReport>[];
  final RxBool isLoading = true.obs;
  final RxString selectedScope = "Store".obs;
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  ///
  ///
  ///department variables
  final RxInt giftSaleTotal = 0.obs;
  final RxInt giftSaleCount = 0.obs;
  final RxInt subTotal = 0.obs;
  final RxList<TaxReport> taxTotals = <TaxReport>[].obs;
  final RxInt taxTotal = 0.obs;
  final RxInt total = 0.obs;
  final RxInt count = 0.obs;
  final RxInt paidOutCount = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt takeoutFees = 0.obs;
  final RxInt accountablesTotal = 0.obs;

  ///
  ///
  ///House variables
  final RxInt houseChargeCount = 0.obs;
  final RxInt houseChargeTotal = 0.obs;

  ///
  ///
  ///Media variables
  final RxInt mediaTotal = 0.obs;
  final RxList<EmployeeDiscount> discountList = <EmployeeDiscount>[].obs;
  final RxInt cashTotal = 0.obs;
  final RxInt ccTipsTotal = 0.obs;
  final RxInt mediaTotalWithTip = 0.obs;
  final RxInt mediaTipTotal = 0.obs;

  ///
  ///
  ///Media variables
  final RxInt orderTypeTotal = 0.obs;
  final RxInt orderTypeCount = 0.obs;

  ///
  ///
  ///Stats variables
  final RxInt noSaleCount = 0.obs;
  final RxInt cancelSaleCount = 0.obs;
  final RxInt cancelSaleAmount = 0.obs;
  final RxInt refundedAmount = 0.obs;
  final RxInt refundedCount = 0.obs;
  final RxInt reopenedTotal = 0.obs;
  final RxInt reopenedCount = 0.obs;

  ///
  ///
  ///discount variables
  final RxList<String> discountTitles = <String>[].obs;
  final RxInt totalDiscounts = 0.obs;

  ///
  ///
  ///Major Department variables
  RxList<MajorReport> majorDeptList = <MajorReport>[].obs;

  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxList<int> selectedTerminals = <int>[-1].obs;

  final RxInt refundTotal = 0.obs;
  final RxDouble percentageTotal = 0.00.obs;

  @override
  Future<void> onInit() async {
    final DateTime now = DateTime.now();
    dateSelectController.text = DateFormat.yMMMd().format(now);
    // Initialize date range controller with today's date
    dateRangeController.selectedRange = PickerDateRange(now, now);
    await getTerminals();
    await getRows();

    super.onInit();
  }

  Future<void> scopeHandler(dynamic value) async {
    // If value is null, this is a direct terminal selection from the drawer
    if (value == null) {
      // Update scope text based on current selection
      if (selectedTerminals.isEmpty) {
        selectedTerminals.add(-1);
        selectedScope.value = "Store";
      } else if (selectedTerminals.length == 1) {
        if (selectedTerminals.first == -1) {
          selectedScope.value = "Store";
        } else {
          try {
            final dynamic selectedTerminalObject = list.firstWhere(
              (dynamic d) => int.parse(d['idx'].toString()) == selectedTerminals.first,
              orElse: () => {'desc': 'Unknown Terminal'},
            );
            selectedScope.value = selectedTerminalObject["desc"].toString();
          } catch (e) {
            selectedScope.value = 'Unknown Terminal';
          }
        }
      } else {
        selectedScope.value = "Multiple Terminals";
      }
      await getRows();
      return;
    }

    // Handle store selection (-1)
    if (value == -1) {
      selectedTerminals.clear();
      selectedTerminals.add(-1);
      selectedScope.value = "Store";
    } else {
      // Handle individual terminal selection
      final int terminalId = int.tryParse(value.toString()) ?? 0;

      if (selectedTerminals.contains(terminalId)) {
        selectedTerminals.remove(terminalId);
      } else {
        // Remove store selection if adding a specific terminal
        selectedTerminals.remove(-1);
        selectedTerminals.add(terminalId);
      }

      // Update scope text based on selection
      if (selectedTerminals.isEmpty) {
        selectedTerminals.add(-1);
        selectedScope.value = "Store";
      } else if (selectedTerminals.length == 1 && selectedTerminals.first != -1) {
        try {
          final dynamic selectedTerminalObject = list.firstWhere(
            (dynamic d) => int.parse(d['idx'].toString()) == selectedTerminals.first,
            orElse: () => {'desc': 'Unknown Terminal'},
          );
          selectedScope.value = selectedTerminalObject["desc"].toString();
        } catch (e) {
          selectedScope.value = 'Unknown Terminal';
        }
      } else {
        selectedScope.value = "Multiple Terminals";
      }
    }
    await getRows();
  }

  // Helper function to get terminal IDs for filtering
  List<int?> _getTerminalIdsForFilter() {
    if (selectedTerminals.isEmpty) {
      return <int?>[null]; // No selection means all terminals
    }
    if (selectedTerminals.contains(-1)) {
      return <int?>[null]; // Store selection means all terminals
    }
    // Return the actual terminal IDs as a list
    return selectedTerminals.where((int id) => id != -1).toList();
  }

  // Generic method to fetch data for each terminal and accumulate results
  Future<T> _fetchAndAccumulateForTerminals<T, R>({
    required Future<Either<ServiceError, R>> Function(int? terminalId) fetchFunction,
    required T initialValue,
    required T Function(T currentValue, R result) accumulateFunction,
    required String errorMessage,
  }) async {
    try {
      final List<int?> terminalIds = _getTerminalIdsForFilter();
      T accumulatedValue = initialValue;

      for (final int? terminalId in terminalIds) {
        final Either<ServiceError, R> result = await fetchFunction(terminalId);
        result.fold(
          (ServiceError l) {
            throw l.message;
          },
          (R res) {
            accumulatedValue = accumulateFunction(accumulatedValue, res);
          },
        );
      }
      return accumulatedValue;
    } catch (e) {
      _notificationService.error(errorMessage);
      rethrow;
    }
  }

  Future<void> getTakeoutFeeTotal(String start, String end) async {
    final int totalFees = await _fetchAndAccumulateForTerminals<int, int>(
      fetchFunction: (int? terminalId) => _reportService.getTakeOutFee(
        startDate: start,
        endDate: end,
        selectedTerminal: terminalId,
      ),
      initialValue: 0,
      accumulateFunction: (int currentTotal, int feeAmount) => currentTotal + feeAmount,
      errorMessage: "Error getting takeout fees",
    );
    takeoutFees.value = totalFees;
  }

  Future<void> getGiftCardSales(String start, String end) async {
    final Map<String, int> result = await _fetchAndAccumulateForTerminals<Map<String, int>, List<GiftReport>>(
      fetchFunction: (int? terminalId) => _reportService.getGiftCardSales(start, end, terminalId),
      initialValue: {'total': 0, 'count': 0},
      accumulateFunction: (Map<String, int> current, List<GiftReport> giftReports) {
        int totalSales = current['total']!;
        int totalCount = current['count']!;

        for (final GiftReport giftSaleRow in giftReports) {
          totalCount++;
          totalSales += giftSaleRow.original_price;
        }

        return {'total': totalSales, 'count': totalCount};
      },
      errorMessage: "Error getting Gift Card Sales",
    );

    giftSaleTotal.value = result['total']!;
    giftSaleCount.value = result['count']!;
  }

  Future<void> getPaidOut(String start, String end) async {
    final Map<String, int> result = await _fetchAndAccumulateForTerminals<Map<String, int>, List<Sale>>(
      fetchFunction: (int? terminalId) => _reportService.getPaidOut(start, end, terminalId),
      initialValue: {'total': 0, 'count': 0},
      accumulateFunction: (Map<String, int> current, List<Sale> paidOuts) {
        int totalPaidOut = current['total']!;
        int totalCount = current['count']!;

        for (final Sale paidOutRow in paidOuts) {
          totalCount++;
          totalPaidOut += paidOutRow.document.saleHeader.total;
        }

        return {'total': totalPaidOut, 'count': totalCount};
      },
      errorMessage: "Error getting Paid Outs",
    );

    paidOutTotal.value = result['total']!;
    paidOutCount.value = result['count']!;
  }

  Future<void> getTaxTotal(String start, String end) async {
    final List<TaxReport> allTaxReports = await _fetchAndAccumulateForTerminals<List<TaxReport>, List<TaxReport>>(
      fetchFunction: (int? terminalId) => _reportService.getTaxReports(start, end, terminalId, null),
      initialValue: [],
      accumulateFunction: (List<TaxReport> current, List<TaxReport> newReports) => [...current, ...newReports],
      errorMessage: "Error getting tax data",
    );

    // Combine tax reports for same tax types
    final Map<String, TaxReport> combinedTaxes = {};
    for (final TaxReport tax in allTaxReports) {
      final String key = tax.description ?? "Tax ${tax.index}";
      if (combinedTaxes.containsKey(key)) {
        combinedTaxes[key]!.tax_amount += tax.tax_amount;
      } else {
        combinedTaxes[key] = tax;
      }
    }

    taxTotals.value = combinedTaxes.values.toList();
  }

  Future<void> getHouseCharges(String start, String end) async {
    final Map<String, int> result = await _fetchAndAccumulateForTerminals<Map<String, int>, List<Sale>>(
      fetchFunction: (int? terminalId) => _reportService.getHouseCharges(start, end, terminalId),
      initialValue: {'total': 0, 'count': 0},
      accumulateFunction: (Map<String, int> current, List<Sale> sales) {
        int totalCharges = current['total']!;
        int totalCount = current['count']!;

        for (final Sale sale in sales) {
          for (final SaleTender tender in sale.document.saleHeader.tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              totalCharges += tender.amount!;
              totalCount += 1;
            }
          }
        }

        return {'total': totalCharges, 'count': totalCount};
      },
      errorMessage: "Error getting House Charges",
    );

    houseChargeTotal.value = result['total']!;
    houseChargeCount.value = result['count']!;
  }

  Future<void> getRows() async {
    isLoading.value = true;
    resetRows();
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);

    try {
      // Get tax totals
      await getTaxTotal(start, end);

      // Get paid outs
      await getPaidOut(start, end);

      // Get gift card sales
      await getGiftCardSales(start, end);

      // Get takeout fees
      await getTakeoutFeeTotal(start, end);

      // Get house charges
      await getHouseCharges(start, end);

      // Fetch and accumulate all reports
      final Map<String, dynamic> allReports = await _fetchAllReports(start, end);

      // Process the aggregated data
      _processAggregatedData(
        allReports['majorReports'] as List<MajorReport>,
        allReports['mediaReports'] as List<MediaReport>,
        allReports['salesByDept'] as List<SalesByDeptSummaryReport>,
        allReports['orderTypeReports'] as List<OrderTypeReport>,
        allReports['stats'] as List<EmployeeStatistics>,
        allReports['discounts'] as List<EmployeeDiscount>,
      );

      isLoading.value = false;
    } catch (err) {
      _notificationService.error("error getting data rows");
      isLoading.value = false;
    }
  }

  // Fetch all report types in one method
  Future<Map<String, dynamic>> _fetchAllReports(String start, String end) async {
    final List<int?> terminalIds = _getTerminalIdsForFilter();

    final List<MajorReport> allMajorReports = [];
    final List<MediaReport> allMediaReports = [];
    final List<OrderTypeReport> allOrderTypeReports = [];
    final List<SalesByDeptSummaryReport> allSalesByDept = [];
    final List<EmployeeStatistics> allStats = [];
    final List<EmployeeDiscount> allDiscounts = [];

    // Create a function to fetch a specific report type for all terminals
    Future<void> fetchReportForTerminals<T>({
      required Future<Either<ServiceError, List<T>>> Function(int?) fetchFunction,
      required List<T> resultList,
    }) async {
      for (final int? terminalId in terminalIds) {
        final Either<ServiceError, List<T>> result = await fetchFunction(terminalId);
        result.fold(
          (ServiceError l) => throw l.message,
          (List<T> r) => resultList.addAll(r),
        );
      }
    }

    // Fetch all report types in parallel
    await Future.wait([
      fetchReportForTerminals<MajorReport>(
        fetchFunction: (int? terminalId) => _reportService.getMajorReport(
          startDate: start,
          endDate: end,
          selectedTerminal: terminalId,
        ),
        resultList: allMajorReports,
      ),
      fetchReportForTerminals<MediaReport>(
        fetchFunction: (int? terminalId) => _reportService.getMediaBreakdown(start, end, terminalId),
        resultList: allMediaReports,
      ),
      fetchReportForTerminals<SalesByDeptSummaryReport>(
        fetchFunction: (int? terminalId) => _reportService.getSalesByDeptSum(start, end, terminalId),
        resultList: allSalesByDept,
      ),
      fetchReportForTerminals<OrderTypeReport>(
        fetchFunction: (int? terminalId) => _reportService.getOrderTypeReport(
          startDate: start,
          endDate: end,
          selectedTerminal: terminalId,
        ),
        resultList: allOrderTypeReports,
      ),
      fetchReportForTerminals<EmployeeStatistics>(
        fetchFunction: (int? terminalId) => _reportService.getServerStatsReportList(start, end, terminalId, null),
        resultList: allStats,
      ),
      fetchReportForTerminals<EmployeeDiscount>(
        fetchFunction: (int? terminalId) => _reportService.getEmployeeDiscount(
          startDate: start,
          endDate: end,
          selectedTerminal: terminalId,
        ),
        resultList: allDiscounts,
      ),
    ]);

    return {
      'majorReports': allMajorReports,
      'mediaReports': allMediaReports,
      'salesByDept': allSalesByDept,
      'orderTypeReports': allOrderTypeReports,
      'stats': allStats,
      'discounts': allDiscounts,
    };
  }

  // Generic function to combine similar records by a key
  Map<K, T> _combineRecordsByKey<K, T>({
    required List<T> records,
    required K Function(T record) keyExtractor,
    required void Function(T existing, T newRecord) combiner,
  }) {
    final Map<K, T> combined = {};
    for (final T record in records) {
      final K key = keyExtractor(record);
      if (combined.containsKey(key)) {
        combiner(combined[key]!, record);
      } else {
        combined[key] = record;
      }
    }
    return combined;
  }

  void _processAggregatedData(
    List<MajorReport> majorReports,
    List<MediaReport> mediaReports,
    List<SalesByDeptSummaryReport> salesByDept,
    List<OrderTypeReport> orderTypeReports,
    List<EmployeeStatistics> stats,
    List<EmployeeDiscount> discounts,
  ) {
    // Process major reports
    final Map<String, MajorReport> combinedMajors = _combineRecordsByKey<String, MajorReport>(
      records: majorReports,
      keyExtractor: (MajorReport report) => report.major_group,
      combiner: (MajorReport existing, MajorReport newRecord) {
        existing.major_actual_total += newRecord.major_actual_total;
        existing.major_gross_total += newRecord.major_gross_total;
      },
    );
    majorDeptList.value = combinedMajors.values.toList();

    // Process media reports
    final Map<String, MediaReport> combinedMedia = _combineRecordsByKey<String, MediaReport>(
      records: mediaReports,
      keyExtractor: (MediaReport report) => report.media,
      combiner: (MediaReport existing, MediaReport newRecord) {
        existing.amount += newRecord.amount;
        existing.tips += newRecord.tips;
        existing.count += newRecord.count;
      },
    );
    mediaList.value = combinedMedia.values.toList();

    // Process sales by department
    final Map<String, SalesByDeptSummaryReport> combinedSales = _combineRecordsByKey<String, SalesByDeptSummaryReport>(
      records: salesByDept.where((report) => report.department != null).toList(),
      keyExtractor: (SalesByDeptSummaryReport report) => report.department!,
      combiner: (SalesByDeptSummaryReport existing, SalesByDeptSummaryReport newRecord) {
        existing.dept_actual_price = (existing.dept_actual_price ?? 0) + (newRecord.dept_actual_price ?? 0);
        existing.item_count = (existing.item_count ?? 0) + (newRecord.item_count ?? 0);
      },
    );
    dataRows.value = combinedSales.values.toList();

    // Populate salesByDeptRows for display
    salesByDeptRows.clear();
    total.value = 0;
    count.value = 0;

    for (final SalesByDeptSummaryReport report in dataRows) {
      if (report.department != null) {
        final int deptTotal = report.dept_actual_price ?? 0;
        final int deptCount = report.item_count ?? 0;
        total.value += deptTotal;
        count.value += deptCount;
        salesByDeptRows.add(CustomTableRow(
          children: <Widget>[
            Text(report.department!),
            Text(Helpers.formatWholeNumber(deptCount)),
            Text("\$${Helpers.formatCurrency(deptTotal)}"),
          ],
        ));
      }
    }

    // Process order type reports
    final Map<int, OrderTypeReport> combinedOrders = _combineRecordsByKey<int, OrderTypeReport>(
      records: orderTypeReports,
      keyExtractor: (OrderTypeReport report) => report.type,
      combiner: (OrderTypeReport existing, OrderTypeReport newRecord) {
        existing.total += newRecord.total;
        existing.qty += newRecord.qty;
      },
    );
    orderTypeList.value = combinedOrders.values.toList();

    // Process statistics
    _processStatistics(stats);

    // Process discounts
    discountList.value = discounts;
    _processDiscounts(discounts);

    // Update media-related totals
    _updateMediaTotals();

    // Update accountables total
    accountablesTotal.value = giftSaleTotal.value + paidOutTotal.value + houseChargeTotal.value;
  }

  void _processStatistics(List<EmployeeStatistics> stats) {
    for (final EmployeeStatistics stat in stats) {
      noSaleCount.value += stat.no_sale_count;
      cancelSaleCount.value += stat.cancel_sale_count;
      cancelSaleAmount.value += stat.cancel_sale_amount;
      refundedAmount.value += stat.refunded_amount;
      refundedCount.value += stat.refunded_count;
      reopenedTotal.value += stat.reopened_total;
      reopenedCount.value += stat.reopened_count;
    }
  }

  void _processDiscounts(List<EmployeeDiscount> discounts) {
    for (final EmployeeDiscount discount in discounts) {
      if (!discountTitles.contains(discount.title)) {
        discountTitles.add(discount.title);
      }
      totalDiscounts.value += discount.discount_total;
    }
  }

  void _updateMediaTotals() {
    for (final MediaReport media in mediaList) {
      if (media.media == "0") {
        cashTotal.value = media.amount;
      }
      if (media.media == '2') {
        ccTipsTotal.value = media.tips;
      }
    }
  }

  Future<void> printReport() async {
    final PrintJob printJobType = PrinterInterface.buildDailySalesPrintJob(
      deptRecords: dataRows,
      majorRecords: majorDeptList,
      mediaRecords: mediaList,
      taxReport: taxTotals,
      giftSales: giftSaleTotal.value,
      giftSalesCount: giftSaleCount.value,
      paidOutCount: paidOutCount.value,
      takeoutFees: takeoutFees.value,
      paidOut: paidOutTotal.value,
      houseChargeSales: houseChargeTotal.value,
      houseChargeCount: houseChargeCount.value,
      cancelSaleAmount: cancelSaleAmount.value,
      cancelSaleCount: cancelSaleCount.value,
      discRecords: discountList,
      refundedAmount: refundedAmount.value,
      refundedCount: refundedCount.value,
      noSaleCount: noSaleCount.value,
      reopenedCount: reopenedCount.value,
      reopenedTotal: reopenedTotal.value,
      startDate: Helpers.getReportStartString(dateRangeController),
      endDate: Helpers.getReportEndString(dateRangeController),
      cashTotal: cashTotal.value,
      ccTips: ccTipsTotal.value,
      orderTypeCount: orderTypeCount.value,
      orderTypeList: orderTypeList,
      orderTypeTotal: orderTypeTotal.value,
    );
    printJobType.print_job = "";

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) return _notificationService.error(printJobResult.exception.toString());

      _notificationService.success("Print Sales By Department Success");
    } catch (err, stack) {
      _logger.severe("error printing daily sales", err, stack);
    }
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }
          ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) {
      return _notificationService.error(getTerminalsResult.exception.toString());
    }

    // ignore: avoid_dynamic_calls
    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  void resetRows() {
    dataRows.clear();
    reportRows.clear();
    reportTotalsRow.clear();
    salesByDeptRows.clear();
    majorDeptList.clear();
    discountList.clear();
    percentageTotal.value = 0;
    count.value = 0;
    subTotal.value = 0;
    taxTotal.value = 0;
    houseChargeTotal.value = 0;
    houseChargeCount.value = 0;
    total.value = 0;
    giftSaleTotal.value = 0;
    giftSaleCount.value = 0;
    paidOutCount.value = 0;
    paidOutTotal.value = 0;
    noSaleCount.value = 0;
    cancelSaleCount.value = 0;
    cancelSaleAmount.value = 0;
    refundedAmount.value = 0;
    refundedCount.value = 0;
    reopenedTotal.value = 0;
    reopenedCount.value = 0;
    mediaTotal.value = 0;
    totalDiscounts.value = 0;
    //orderType varbles
    orderTypeList.clear();
    orderTypeCount.value = 0;
    orderTypeTotal.value = 0;
    takeoutFees.value = 0;
    mediaTotalWithTip.value = 0;
    mediaTipTotal.value = 0;
  }
}
