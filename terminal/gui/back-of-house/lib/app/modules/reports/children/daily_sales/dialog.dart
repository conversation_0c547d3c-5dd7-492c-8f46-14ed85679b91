import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/models/emp_discounts.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/report_end_drawer.dart';
import 'package:backoffice/app/modules/reports/children/daily_sales/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DailySalesDialog extends GetView<DailySalesController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.scaffoldKey,
      endDrawer: DailySalesReportDrawer(
        formKey: controller.formKey,
        dateRangeController: controller.dateRangeController,
        dateOnChanged: (String value) async {
          await controller.getRows();
        },
        rangeOnChanged: (dynamic value) async {
          await controller.getRows();
        },
        scaffoldKey: controller.scaffoldKey,
        title: "Filter Options",
        terminalList: controller.list,
        terminalOnChanged: (dynamic value) async {
          await controller.scopeHandler(int.tryParse(value.toString()));
        },
        isLoading: controller.isLoading,
        selectedTerminals: controller.selectedTerminals,
        employeeList: const <Employee>[],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.scaffoldKey.currentState!.openEndDrawer(),
        backgroundColor: R2Colors.primary500,
        elevation: 0,
        hoverElevation: 0,
        child: const Icon(
          Icons.menu_open,
        ),
      ),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Header(
          title: "Daily Sales",
          leftButton: DialogButton(
            buttonType: EDialogButtonType.BACK,
            buttonText: "Reports",
            onTapped: () {
              Get.back(id: AppRoutes.id);
            },
          ),
          rightButton: Padding(
            padding: const EdgeInsets.all(4),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: R2Colors.primary500,
                  width: 2,
                ),
              ),
              child: IntrinsicWidth(
                child: MaterialButton(
                  height: 60,
                  minWidth: 120,
                  color: Colors.white,
                  disabledColor: Colors.grey[200],
                  hoverColor: R2Colors.primary200,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  onPressed: () => controller.printReport(),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Icon(
                        Icons.print,
                        color: R2Colors.primary500,
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        clipBehavior: Clip.none,
        child: Center(
          child: Column(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(4),
                child: Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Obx(
                        () => controller.isLoading.value == false
                            ? controller.salesByDeptRows.isNotEmpty
                                ? Column(
                                    children: <Widget>[
                                      CustomTable(
                                        columnConfig: const <ColumnConfig>[
                                          ColumnConfig(
                                            "Department",
                                          ),
                                          ColumnConfig(
                                            "Units",
                                            alignment: Alignment.centerRight,
                                          ),
                                          ColumnConfig(
                                            "Total",
                                            alignment: Alignment.centerRight,
                                          ),
                                        ],
                                        rows: <CustomTableRow>[
                                          ...controller.salesByDeptRows,
                                          CustomTableRow(
                                            divider: true,
                                            children: <Widget>[
                                              const Text('Dept Total:'),
                                              Obx(() => Text(Helpers.formatWholeNumber(controller.count.value))),
                                              Obx(
                                                () => Text(
                                                  (controller.total.value) >= 0
                                                      ? "\$${Helpers.formatCurrency(controller.total.value)}"
                                                      : "-\$${Helpers.formatCurrency(controller.total.value)}",
                                                ),
                                              ),
                                            ],
                                          ),
                                          ...List<CustomTableRow>.generate(
                                            controller.taxTotals.length,
                                            (int index) {
                                              if (index == 0) {
                                                controller.taxTotal.value = 0;
                                              }
                                              controller.taxTotal.value += controller.taxTotals[index].tax_amount;
                                              return CustomTableRow(
                                                children: <Text>[
                                                  Text(controller.taxTotals[index].description ?? "Tax ${controller.taxTotals[index].index}"),
                                                  const Text(""),
                                                  Text("\$${Helpers.formatCurrency(controller.taxTotals[index].tax_amount)}"),
                                                ],
                                              );
                                            },
                                          ),
                                          CustomTableRow(
                                            children: <Widget>[
                                              const Text('Net Total:'),
                                              Obx(() => Text(Helpers.formatWholeNumber(controller.count.value))),
                                              Obx(
                                                () => Text(
                                                  (controller.total.value + controller.taxTotal.value) >= 0
                                                      ? "\$${Helpers.formatCurrency(controller.total.value + controller.taxTotal.value)}"
                                                      : "-\$${Helpers.formatCurrency(controller.total.value + controller.taxTotal.value)}",
                                                ),
                                              ),
                                            ],
                                          ),
                                          CustomTableRow(
                                            children: <Widget>[
                                              const Text('Takeout fees:'),
                                              const Text(""),
                                              Text(
                                                "\$${Helpers.formatCurrency(controller.takeoutFees.value)}",
                                              ),
                                            ],
                                          ),
                                          CustomTableRow(
                                            children: <Widget>[
                                              const Text('Paid Out:'),
                                              Text(
                                                controller.paidOutCount.value.toString(),
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                              Text(
                                                "-\$${Helpers.formatCurrency(controller.paidOutTotal.value)}",
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                            ],
                                          ),
                                          CustomTableRow(
                                            children: <Widget>[
                                              const Text('Gift Card Sales:'),
                                              Text(
                                                controller.giftSaleCount.value.toString(),
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                              Text(
                                                "-\$${Helpers.formatCurrency(controller.giftSaleTotal.value)}",
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                            ],
                                          ),
                                          CustomTableRow(
                                            children: <Widget>[
                                              const Text('House Charges:'),
                                              Text(
                                                controller.houseChargeCount.value.toString(),
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                              Text(
                                                "-\$${Helpers.formatCurrency(controller.houseChargeTotal.value)}",
                                                style: const TextStyle(color: R2Colors.red500),
                                              ),
                                            ],
                                          ),
                                          CustomTableRow(
                                            style: CustomRowStyle.total,
                                            children: <Widget>[
                                              const Text('Total Accountable:'),
                                              Text(Helpers.formatWholeNumber(controller.count.value)),
                                              Text(
                                                ((controller.total.value + controller.taxTotal.value + controller.takeoutFees.value) -
                                                            controller.accountablesTotal.value) >=
                                                        0
                                                    ? "\$${Helpers.formatCurrency((controller.total.value + controller.taxTotal.value + controller.takeoutFees.value) - controller.accountablesTotal.value)}"
                                                    : "-\$${Helpers.formatCurrency((controller.total.value + controller.taxTotal.value + controller.takeoutFees.value) - controller.accountablesTotal.value)}",
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          top: 25,
                                          bottom: 25,
                                        ),
                                        child: Obx(
                                          () => CustomTable(
                                            columnConfig: const <ColumnConfig>[
                                              ColumnConfig('Media'),
                                              ColumnConfig(
                                                'Count',
                                                alignment: Alignment.center,
                                              ),
                                              ColumnConfig(
                                                'Total',
                                                alignment: Alignment.centerRight,
                                              ),
                                              ColumnConfig(
                                                'Tips',
                                                alignment: Alignment.centerRight,
                                              ),
                                              ColumnConfig(
                                                'Total W/ Tips',
                                                alignment: Alignment.centerRight,
                                              ),
                                            ],
                                            rows: List<CustomTableRow>.generate(controller.mediaList.length, (int index) {
                                                  if (index == 0) {
                                                    controller.mediaTotal.value = 0;
                                                    controller.mediaTipTotal.value = 0;
                                                    controller.mediaTotalWithTip.value = 0;
                                                  }
                                                  controller.mediaTotal.value += controller.mediaList[index].amount;
                                                  controller.mediaTipTotal.value += controller.mediaList[index].tips;
                                                  controller.mediaTotalWithTip.value +=
                                                      controller.mediaList[index].amount + controller.mediaList[index].tips;
                                                  return CustomTableRow(
                                                    children: <Widget>[
                                                      Text(
                                                        Helpers.mediaTypeAsString(
                                                          PaymentMediaType.values[int.parse(
                                                            controller.mediaList[index].media,
                                                          )],
                                                        ),
                                                      ),
                                                      Text(
                                                        Helpers.formatWholeNumber(
                                                          controller.mediaList[index].count,
                                                        ),
                                                      ),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.mediaList[index].amount)}",
                                                      ),
                                                      Align(
                                                        alignment: Alignment.centerRight,
                                                        child: Text(
                                                          "\$${Helpers.formatCurrency(controller.mediaList[index].tips)}",
                                                        ),
                                                      ),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.mediaList[index].amount + controller.mediaList[index].tips)}",
                                                      ),
                                                    ],
                                                  );
                                                }) +
                                                <CustomTableRow>[
                                                  CustomTableRow(
                                                    divider: true,
                                                    style: CustomRowStyle.total,
                                                    children: <Widget>[
                                                      const Text(
                                                        "Media Total:",
                                                      ),
                                                      const Text(""),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.mediaTotal.value)}",
                                                        textAlign: TextAlign.right,
                                                      ),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.mediaTipTotal.value)}",
                                                      ),
                                                      Obx(
                                                        () => Text(
                                                          "\$${Helpers.formatCurrency(controller.mediaTotalWithTip.value)}",
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  CustomTableRow(
                                                    children: const <Widget>[
                                                      Text(""),
                                                      Text(""),
                                                      Text(""),
                                                      Text(""),
                                                      Text(""),
                                                    ],
                                                  ),
                                                  CustomTableRow(
                                                    children: <Widget>[
                                                      const Text("Cash Total:"),
                                                      const Text(""),
                                                      const Text(""),
                                                      const Text(""),
                                                      Obx(
                                                        () => Text(
                                                          "\$${Helpers.formatCurrency(controller.cashTotal.value)}",
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  CustomTableRow(
                                                    children: <Widget>[
                                                      const Text(
                                                        "Paid Out Total:",
                                                      ),
                                                      const Text(""),
                                                      const Text(""),
                                                      const Text(""),
                                                      Obx(
                                                        () => Text(
                                                          "-\$${Helpers.formatCurrency(controller.paidOutTotal.value)}",
                                                          style: const TextStyle(
                                                            color: R2Colors.red500,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  CustomTableRow(
                                                    children: <Widget>[
                                                      const Text("C/C tips:"),
                                                      const Text(""),
                                                      const Text(""),
                                                      const Text(""),
                                                      Obx(
                                                        () => Text(
                                                          "-\$${Helpers.formatCurrency(controller.ccTipsTotal.value)}",
                                                          style: const TextStyle(
                                                            color: R2Colors.red500,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  CustomTableRow(
                                                    style: CustomRowStyle.total,
                                                    children: <Widget>[
                                                      const Text(
                                                        "Adjusted Cash:",
                                                      ),
                                                      const Text(""),
                                                      const Text(""),
                                                      const Text(""),
                                                      Obx(
                                                        () => Text(
                                                          (controller.cashTotal.value -
                                                                      controller.paidOutTotal.value -
                                                                      controller.ccTipsTotal.value) >=
                                                                  0
                                                              ? "\$${Helpers.formatCurrency(controller.cashTotal.value - controller.paidOutTotal.value - controller.ccTipsTotal.value)}"
                                                              : "-\$${Helpers.formatCurrency(-1 * (controller.cashTotal.value - controller.paidOutTotal.value - controller.ccTipsTotal.value))}",
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          top: 25,
                                          bottom: 25,
                                        ),
                                        child: Obx(
                                          () => CustomTable(
                                            columnConfig: const <ColumnConfig>[
                                              ColumnConfig('Order Type'),
                                              ColumnConfig(
                                                'Count',
                                                alignment: Alignment.center,
                                              ),
                                              ColumnConfig(
                                                'Total',
                                                alignment: Alignment.centerRight,
                                              ),
                                            ],
                                            rows: List<CustomTableRow>.generate(controller.orderTypeList.length, (int index) {
                                                  if (index == 0) {
                                                    controller.orderTypeCount.value = 0;
                                                    controller.orderTypeTotal.value = 0;
                                                  }
                                                  controller.orderTypeTotal.value += controller.orderTypeList[index].total;
                                                  controller.orderTypeCount.value += controller.orderTypeList[index].qty;

                                                  return CustomTableRow(
                                                    children: <Text>[
                                                      Text(
                                                        OrderType.values[index].friendlyString,
                                                      ),
                                                      Text(
                                                        Helpers.formatWholeNumber(
                                                          controller.orderTypeList[index].qty,
                                                        ),
                                                      ),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.orderTypeList[index].total)}",
                                                      ),
                                                    ],
                                                  );
                                                }) +
                                                <CustomTableRow>[
                                                  CustomTableRow(
                                                    divider: true,
                                                    style: CustomRowStyle.total,
                                                    children: <Widget>[
                                                      const Text("Total:"),
                                                      Text(
                                                        Helpers.formatWholeNumber(
                                                          controller.orderTypeCount.value,
                                                        ),
                                                      ),
                                                      Obx(
                                                        () => Text(
                                                          "\$${Helpers.formatCurrency(controller.orderTypeTotal.value)}",
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          top: 25,
                                          bottom: 25,
                                        ),
                                        child: Obx(
                                          () => CustomTable(
                                            columnConfig: const <ColumnConfig>[
                                              ColumnConfig('Statistics'),
                                              ColumnConfig(
                                                'Count',
                                                alignment: Alignment.center,
                                              ),
                                              ColumnConfig(
                                                'Total',
                                                alignment: Alignment.centerRight,
                                              ),
                                            ],
                                            rows: <CustomTableRow>[
                                              CustomTableRow(
                                                children: <Text>[
                                                  const Text("Cancelled Sales"),
                                                  Text(
                                                    Helpers.formatWholeNumber(
                                                      controller.cancelSaleCount.value,
                                                    ),
                                                  ),
                                                  Text(
                                                    "\$${Helpers.formatCurrency(controller.cancelSaleAmount.value)}",
                                                  ),
                                                ],
                                              ),
                                              CustomTableRow(
                                                children: <Text>[
                                                  const Text("Refunded Sales"),
                                                  Text(
                                                    Helpers.formatWholeNumber(
                                                      controller.refundedCount.value,
                                                    ),
                                                  ),
                                                  Text(
                                                    "\$${Helpers.formatCurrency(controller.refundedAmount.value)}",
                                                  ),
                                                ],
                                              ),
                                              CustomTableRow(
                                                children: <Text>[
                                                  const Text("Reopened Sales"),
                                                  Text(
                                                    Helpers.formatWholeNumber(
                                                      controller.reopenedCount.value,
                                                    ),
                                                  ),
                                                  Text(
                                                    "\$${Helpers.formatCurrency(controller.reopenedTotal.value)}",
                                                  ),
                                                ],
                                              ),
                                              CustomTableRow(
                                                children: <Widget>[
                                                  const Text('Paid Out Total:'),
                                                  Text(
                                                    controller.paidOutCount.value.toString(),
                                                  ),
                                                  Text(
                                                    "\$${Helpers.formatCurrency(controller.paidOutTotal.value)}",
                                                  ),
                                                ],
                                              ),
                                              CustomTableRow(
                                                children: <Text>[
                                                  const Text("No Sales"),
                                                  Text(
                                                    Helpers.formatWholeNumber(
                                                      controller.noSaleCount.value,
                                                    ),
                                                  ),
                                                  const Text("-"),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(bottom: 30),
                                        child: Obx(
                                          () => CustomTable(
                                            columnConfig: const <ColumnConfig>[
                                              ColumnConfig('Discount'),
                                              ColumnConfig(
                                                'Total',
                                                alignment: Alignment.centerRight,
                                              ),
                                            ],
                                            rows: List<CustomTableRow>.generate(controller.discountTitles.length, (int index) {
                                                  final RxInt currentAmount = 0.obs;

                                                  for (final EmployeeDiscount element in controller.discountList) {
                                                    if (element.title == controller.discountTitles[index]) {
                                                      currentAmount.value += element.discount_total;
                                                    }
                                                  }
                                                  controller.totalDiscounts.value += currentAmount.value;
                                                  return CustomTableRow(
                                                    children: <Text>[
                                                      Text(
                                                        controller.discountTitles[index],
                                                      ),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(currentAmount.value)}",
                                                      ),
                                                    ],
                                                  );
                                                }) +
                                                <CustomTableRow>[
                                                  CustomTableRow(
                                                    divider: true,
                                                    style: CustomRowStyle.total,
                                                    children: <Text>[
                                                      const Text("Total:"),
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.totalDiscounts.value)}",
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : const Text(
                                    "No data During this interval to display",
                                    style: TextStyle(fontSize: 20),
                                  )
                            : const CircularProgressIndicator(),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(top: 20),
                      child: Row(
                        children: <Text>[
                          Text(
                            "**Note: Department data reflects refunds, discounts, and allowances.",
                            style: TextStyle(color: R2Colors.neutral500),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
