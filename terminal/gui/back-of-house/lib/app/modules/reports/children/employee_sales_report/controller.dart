import 'package:backoffice/app/data/enums/employee_sales_sort.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class EmployeeSalesReportController extends GetxController {
  EmployeeSalesReportController();

  final Logger _logger = Logger('EmployeeSalesController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _bohReportService = Get.find();
  final ReportService _reportService = Get.find();

  final RxBool isLoading = false.obs;

  ScrollController scrollController = ScrollController();
  ScrollController scrollControllerFlag = ScrollController();

  RxList<int> employeeIDs = <int>[].obs;
  RxList<int> defaultEmps = <int>[].obs;

  final RxList<Sale> saleRows = <Sale>[].obs;

  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxList<int> saleFlag = <int>[].obs;
  final RxList<int> tenderFlag = <int>[].obs;
  final RxList<String> saleFlagWords = RxList<String>();

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxList<Employee> employeeList = <Employee>[].obs;
  final Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;
  final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

  Employee? cashier;
  Sale selectedSale = Sale.empty();
  //comment

  final Rx<EmployeeSalesSort> selectedOrder = EmployeeSalesSort.SALE_NUMBER.obs;

  @override
  Future<void> onInit() async {
    dateRangeController.value.selectedRange = PickerDateRange(
      now,
      null,
    );
    await getActiveEmps();
    await getData();
    super.onInit();
  }

  Future<void> getActiveEmps() async {
    try {
      final Either<ServiceError, List<Employee>> empListRes = await _bohReportService.getActiveEmployees();
      empListRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<Employee> r) {
          employeeList.value = r;
          defaultEmps.addAll(employeeList.map((Employee element) => element.id!));
        },
      );
    } catch (e) {
      _notificationService.error("Error getting Active Employees");
    }
  }

  Future<void> getData() async {
    try {
      isLoading.value = true;
      saleRows.clear();
      // if (employeeIDs.isEmpty) {
      //   employeeIDs.addAll(employeeList.mapp ((Employee element) => element.id!));
      // }
      final Either<ServiceError, List<Sale>> transRes = await _reportService.getEmployeeSalesFiltered(
        startDate: Helpers.getReportStartString(dateRangeController.value),
        endDate: Helpers.getReportEndString(dateRangeController.value),
        selectedTerminal: selectedTerminal.value,
        employee_id: employeeIDs.isEmpty ? defaultEmps : employeeIDs,
        sale_flags: saleFlag.isEmpty ? <int>[0] : saleFlag,
        mediaFlags: tenderFlag.isEmpty ? <int>[] : tenderFlag,
      );

      transRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        saleRows.value = r;
      });

      sortTimecards(selectedOrder.value);
      isLoading.value = false;
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  String getReportStartString(DateRangePickerController rangeController) {
    return (rangeController.selectedRange?.startDate ?? now).toUtc().toString();
  }

  String getReportEndString(DateRangePickerController rangeController) {
    return (rangeController.selectedRange?.endDate ?? (rangeController.selectedRange?.startDate ?? now).add(const Duration(days: 1)))
        .toUtc()
        .toString();
  }

  Future<void> getCashier(Sale sale) async {
    try {
      String employeeUUID = employeeList.firstWhere((Employee element) => element.id! == sale.document.saleHeader.currentEmployeeNumber).employee;
      final QueryResult<Object> getCashierResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_CASHIER(\$employee: uuid!) {
              employee_by_pk(employee: \$employee) {
                 created_at
                  created_by
                  document
                  employee
                  employee_class
                  id
                  is_active
                  password
                  updated_at
                  updated_by
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "employee": employeeUUID,
          },
        ),
      );

      if (getCashierResult.hasException) {
        return _notificationService.error(getCashierResult.exception!.graphqlErrors.toString());
      }

      cashier = Employee.fromJson(
        getCashierResult.data!['employee_by_pk'] as Map<String, dynamic>,
      );
    } catch (e) {
      cashier = Employee.empty();
    }
  }

  void getRecipetSales({required Sale sales, required Employee employee}) {}

  Future<void> printReport() async {
    final PrintJob printJobType = PrinterInterface.buildEmployeeSalesReport(
        saleList: saleRows,
        startDate: Helpers.getReportStartString(dateRangeController.value),
        endDate: Helpers.getReportEndString(dateRangeController.value));
    printJobType.print_job = "";

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) return _notificationService.error(printJobResult.exception.toString());

      _notificationService.success("Print Sales By Department Success");
    } catch (err, stack) {
      _logger.severe('error printing employee sales', err, stack);
    }
  }

  void sortTimecards(EmployeeSalesSort sort) {
    switch (sort) {
      case EmployeeSalesSort.SALE_NUMBER:
        saleRows.sort((Sale a, Sale b) => a.sale_number.compareTo(b.sale_number));
        break;
      case EmployeeSalesSort.SERVER_NAME:
        saleRows.sort(
          (Sale a, Sale b) =>
              (employeeList.firstWhere((Employee element) => element.id! == a.document.saleHeader.currentEmployeeNumber).document.lastName ?? "")
                  .compareTo(
            (employeeList.firstWhere((Employee element) => element.id! == b.document.saleHeader.currentEmployeeNumber).document.lastName) ?? "",
          ),
        );
        break;
      case EmployeeSalesSort.SERVER_ID:
        saleRows.sort((Sale a, Sale b) => a.document.saleHeader.currentEmployeeNumber.compareTo(b.document.saleHeader.currentEmployeeNumber));
        break;
      case EmployeeSalesSort.END_AT:
        saleRows.sort((Sale a, Sale b) => (a.end_at ?? DateTime.now()).compareTo(b.end_at ?? DateTime.now()));
        break;
      case EmployeeSalesSort.TOTAL:
        saleRows.sort((Sale a, Sale b) => a.document.saleHeader.total.compareTo(b.document.saleHeader.total));
        break;
      default:
        saleRows.sort((Sale a, Sale b) => a.sale_number.compareTo(b.sale_number));
        break;
    }
  }
}
