// ignore_for_file: avoid_dynamic_calls, always_specify_types

import 'package:backoffice/app/data/enums/gift_type.dart';
import 'package:backoffice/app/data/models/gift_summary_report.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
// import 'package:pdf/widgets.dart' as pw;

class GiftSummaryController extends GetxController {
  GiftSummaryController();
  final Logger _logger = Logger('GiftSummaryController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  //general variables
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxBool isLoading = true.obs;

  // printer variables
  final RxList<Report> printerRows = <Report>[].obs;

  // report variables
  final RxInt issueTotal = 0.obs;
  final RxInt redeemTotal = 0.obs;
  final RxList<GiftSummaryReport> giftList = <GiftSummaryReport>[].obs;
  final RxList<dynamic> list = <dynamic>[].obs;
  final List<String> saleFlags = <String>[];

  // terminal dropdown variables
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxString selectedScope = "Store".obs;

  //date selection variables
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());

    await getTerminals();
    await getRows();

    super.onInit();
  }

  Future<void> getRows() async {
    try {
      resetTotalRows();
      final String start = Helpers.getReportStartString(dateRangeController);
      final String end = Helpers.getReportEndString(dateRangeController);
      final Either<ServiceError, List<Sale>> giftRes = await _reportService.getGiftReport(
        start,
        end,
        selectedTerminal.value,
      );
      giftRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        r.sort(
          (Sale a, Sale b) => a.sale_number.compareTo(b.sale_number),
        );
        for (final Sale element in r) {
          if (element.document.saleHeader.saleFlags.contains(SaleFlags.GIFT_ISSUE.index)) {
            for (final SaleRow saleRow in element.document.saleRows) {
              if (saleRow.flags.contains(SaleRowFlags.GIFT.index)) {
                issueTotal.value += saleRow.actualPrice;
                giftList.add(
                  GiftSummaryReport(
                    saleNumber: element.sale_number,
                    amount: saleRow.actualPrice,
                    description: Helpers.removeGiftPrice(saleRow.receiptDescription),
                    giftType: GiftType.ISSUE.index,
                  ),
                );
              }
            }
          }

          if (element.document.saleHeader.saleFlags.contains(SaleFlags.GIFT_REDEMPTION.index)) {
            for (final SaleTender tender in element.document.saleHeader.tenders) {
              if (tender.media == PaymentMediaType.Gift.index) {
                final String giftCardNumber = tender.giftTransactionData!.identification.cardNumber.substring(
                  tender.giftTransactionData!.identification.cardNumber.length - 4,
                );
                redeemTotal.value += tender.amount ?? 0;
                giftList.add(
                  GiftSummaryReport(
                    amount: tender.amount ?? 0,
                    saleNumber: element.sale_number,
                    description: "Gift Card ****$giftCardNumber",
                    giftType: GiftType.REDEEM.index,
                  ),
                );
              }
            }
          }
        }
      });
      isLoading.value = false;
    } catch (err) {
      _notificationService.error('error getting gift data');
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    await getRows();
  }

  void resetTotalRows() {
    issueTotal.value = 0;
    redeemTotal.value = 0;
    giftList.clear();
    printerRows.clear();
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) {
      return _notificationService.error(getTerminalsResult.exception.toString());
    }

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> printReport() async {
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    PrintJob printJobType;
    printJobType = PrinterInterface.buildGiftReport(
      giftIssued: issueTotal.value,
      giftRedeemed: redeemTotal.value,
      giftRows: giftList,
      scope: selectedScope.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(start).toLocal()),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(end).toLocal()),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult printJobResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe('error printing', err, stack);
    }
  }
}
