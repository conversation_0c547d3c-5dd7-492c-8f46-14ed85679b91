// ignore_for_file: non_constant_identifier_names, always_specify_types

import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class JobCodeBreakdownController extends GetxController {
  JobCodeBreakdownController();

  final Logger _logger = Logger('JobecodeBreakdownController');

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;
  final RxList<Report> dataRows = <Report>[].obs;
  final RxInt wage_total = 0.obs;
  final RxDouble hour_total = 0.00.obs;
  final DateRangePickerController dateRangeController = DateRangePickerController();

  @override
  Future<void> onInit() async {
    await getRows();
    super.onInit();
  }

  Future<void> getRows() async {
    try {
      clearDataRows();
      final String start = Helpers.getReportStartString(dateRangeController);
      final String end = Helpers.getReportEndString(dateRangeController);

      final QueryResult getJobCodeBreakdownResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
           mutation GET_JOB_CODE_BREAKDOWN(\$start_date: timestamp, \$end_date: timestamp) {
              get_job_code_breakdown(args: {start_date: \$start_date, end_date: \$end_date}) {
                hours
                pay_rate
                rounded_hours
                rounded_wages
                title
                wages
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": start,
            "end_date": end,
          },
        ),
      );

      if (getJobCodeBreakdownResult.hasException) throw getJobCodeBreakdownResult.exception.toString();

      final List<Report> jobCodeReports = (getJobCodeBreakdownResult.data!['get_job_code_breakdown'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();
      for (final Report report in jobCodeReports) {
        dataRows.add(report);
        wage_total.value += report.wages!;
        hour_total.value += report.hours!;
      }
    } catch (err, stack) {
      _logger.severe("error getting jobcode breakdown", err, stack);
      _notificationService.error('error getting jobcode breakdown');
    }
    for (final Report report in dataRows) {
      final CustomTableRow newEntry = CustomTableRow(
        padding: const EdgeInsets.only(top: 2, bottom: 2),
        children: <Widget>[
          Text(report.title!),
          Text("\$${Helpers.formatCurrency(report.wages!)}"),
          Text(report.hours!.toString()),
        ],
      );
      reportRows.add(newEntry);
    }

    final CustomTableRow totals = CustomTableRow(
      style: CustomRowStyle.total,
      children: <Widget>[
        const Text('Totals:'),
        Text(
          "\$${Helpers.formatCurrency(wage_total.value)}",
        ),
        Text(hour_total.value.toString()),
      ],
    );
    reportTotalsRow.add(totals);
  }

  Future<void> printReport() async {
    final PrintJob printJobType = PrinterInterface.buildJobCodeBreakdownReport(
      reports: dataRows,
      hourTotal: hour_total.value,
      wageTotal: wage_total.value,
    );
    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult printJobResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe('error printing jobcode breakdown', err, stack);
      _notificationService.error("error printing jobcode breakdown");
    }
  }

  void clearDataRows() {
    wage_total.value = 0;
    hour_total.value = 0.00;
    reportRows.clear();
    reportTotalsRow.clear();
    dataRows.clear();
  }
}
