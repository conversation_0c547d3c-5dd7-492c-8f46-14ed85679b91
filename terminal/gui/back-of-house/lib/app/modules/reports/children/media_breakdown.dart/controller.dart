// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/media_report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class MediaBreakdownController extends GetxController {
  MediaBreakdownController();

  final Logger _logger = Logger('MediaBreakdownController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  //general variables
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();

  // printer variables
  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;
  final RxList<MediaReport> mediaRows = <MediaReport>[].obs;

  final RxList<dynamic> list = <dynamic>[].obs;
  final List<String> saleFlags = <String>[];

  final RxInt count = 0.obs;
  final RxInt netTotal = 0.obs;
  final RxInt amountTotal = 0.obs;
  final RxInt tipsTotal = 0.obs;
  final RxInt amountWithTipsTotal = 0.obs;
  final RxInt paidOutCount = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt ccTipTotal = 0.obs;
  final RxInt cashTotal = 0.obs;

  // terminal dropdown variables
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTerminal = Rxn<int>();

  //date selection variables
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
    await getTerminals();
    await getRows();
    super.onInit();
  }

  Future<void> getRows() async {
    try {
      clearAllDataRows();

      final String start = Helpers.getReportStartString(dateRangeController);
      final String end = Helpers.getReportEndString(dateRangeController);

      final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(
        start,
        end,
        selectedTerminal.value,
      );

      paidOutRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<Sale> r) {
          for (final Sale paidOutRow in r) {
            paidOutCount.value++;
            paidOutTotal.value += paidOutRow.document.saleHeader.total;
          }
        },
      );

      final Either<ServiceError, List<MediaReport>> mediaRes = await _reportService.getMediaBreakdown(
        start,
        end,
        selectedTerminal.value,
      );

      mediaRes.fold((ServiceError l) {
        throw l.message;
      }, (List<MediaReport> r) {
        for (final MediaReport report in r) {
          mediaRows.add(report);
          netTotal.value += report.net_total;
          amountTotal.value += report.amount;
          count.value += report.count;
          tipsTotal.value += report.tips;
          amountWithTipsTotal.value += report.amount + report.tips;
        }
      });
    } catch (e) {
      _notificationService.error(e.toString());
    }

    for (final MediaReport report in mediaRows) {
      if (report.media == '2') {
        ccTipTotal.value = report.tips;
      }
      if (report.media == '0') {
        cashTotal.value = report.amount;
      }
      final CustomTableRow newEntry = CustomTableRow(
        padding: const EdgeInsets.only(top: 2, bottom: 2),
        children: <Widget>[
          Text(
            Helpers.mediaTypeAsString(
              PaymentMediaType.values[int.parse(report.media)],
            ),
          ),
          Text(report.count.toString()),
          Text("\$${Helpers.formatCurrency(report.amount)}"),
          Text("\$${Helpers.formatCurrency(report.tips)}"),
          Text(
            report.amount >= 0
                ? "\$ ${Helpers.formatCurrency(report.amount + report.tips)}"
                : "-\$${Helpers.formatCurrency((report.amount + report.tips).abs())}",
          ),
        ],
      );
      reportRows.add(newEntry);
    }
    final List<CustomTableRow> totals = <CustomTableRow>[
      CustomTableRow(
        divider: true,
        children: <Widget>[
          const Text('Media Totals:'),
          Text(count.value.toString()),
          Text("\$${Helpers.formatCurrency(amountTotal.value)}"),
          Text("\$${Helpers.formatCurrency(tipsTotal.value)}"),
          Text(
            amountTotal.value >= 0
                ? "\$${Helpers.formatCurrency(amountWithTipsTotal.value)}"
                : "-\$${Helpers.formatCurrency(amountWithTipsTotal.value.abs())}",
          ),
        ],
      ),
      CustomTableRow(
        children: const <Widget>[
          Text(''),
          Text(''),
          Text(''),
          Text(''),
          Text(''),
        ],
      ),
      CustomTableRow(
        children: <Widget>[
          const Text(
            'Cash Total:',
          ),
          const Text(
            "",
          ),
          const Text(''),
          const Text(''),
          Text(
            "\$${Helpers.formatCurrency(cashTotal.value)}",
          ),
        ],
      ),
      CustomTableRow(
        children: <Widget>[
          const Text(
            'Credit Card Tips:',
          ),
          const Text(
            "",
          ),
          const Text(
            "",
          ),
          const Text(
            "",
          ),
          Text(
            "-\$${Helpers.formatCurrency(ccTipTotal.value)}",
            style: const TextStyle(color: Colors.red),
          ),
        ],
      ),
      CustomTableRow(
        children: <Widget>[
          const Text(
            'Paid Out:',
          ),
          const Text(
            "",
          ),
          const Text(
            "",
          ),
          const Text(
            "",
          ),
          Text(
            "-\$${Helpers.formatCurrency(paidOutTotal.value)}",
            style: const TextStyle(color: Colors.red),
          ),
        ],
      ),
      CustomTableRow(
        style: CustomRowStyle.total,
        children: <Widget>[
          const Text('Adjusted Cash:'),
          const Text(""),
          const Text(
            "",
          ),
          const Text(
            "",
          ),
          Obx(
            () => Text(
              "\$${Helpers.formatCurrency(cashTotal.value - paidOutTotal.value - ccTipTotal.value)}",
            ),
          ),
        ],
      ),
    ];
    print(cashTotal.value);
    reportTotalsRow.addAll(totals);
  }

  Future<void> printReport() async {
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    final PrintJob printJobType = PrinterInterface.buildMediaBreakdownReport(
      reports: mediaRows,
      paidOutCount: paidOutCount.value,
      cashTotal: cashTotal.value,
      paidOutTotal: paidOutTotal.value,
      tipTotal: ccTipTotal.value,
      scope: selectedScope.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(start).toLocal()),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(end).toLocal()),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<dynamic> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<dynamic>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err) {
      _notificationService.success("Error Printing Sales Breakdown");
    }
  }

  Future<void> getTerminals() async {
    try {
      final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
          ),
          variables: const <String, dynamic>{"_eq": "systemDevice"},
        ),
      );

      if (getTerminalsResult.hasException) {
        throw getTerminalsResult.exception.toString();
      }

      list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
    } catch (e, stack) {
      _notificationService.error('error getting terminals');
      _logger.severe("Error getting terminals", e, stack);
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    await getRows();
    // await getGiftCardSales();
  }

  void clearAllDataRows() {
    reportRows.clear();
    reportTotalsRow.clear();
    mediaRows.clear();
    tipsTotal.value = 0;
    amountWithTipsTotal.value = 0;
    count.value = 0;
    netTotal.value = 0;
    amountTotal.value = 0;
    paidOutTotal.value = 0;
    paidOutCount.value = 0;
    ccTipTotal.value = 0;
    cashTotal.value = 0;
  }
}
