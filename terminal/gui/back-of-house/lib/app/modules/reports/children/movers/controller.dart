// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/movers.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class MoversController extends GetxController {
  MoversController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final Logger _logger = Logger('MoversController');

  //general variables
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();

  // report variables
  final RxList<Movers> reports = <Movers>[].obs;
  final List<String> saleFlags = <String>[];

  // employee dropdown variables
  final RxList<Employee> employeeList = <Employee>[].obs;

  // terminal dropdown variables
  final RxList<dynamic> terminalList = <dynamic>[].obs;
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxString selectedScope = "Store".obs;
  final RxString orderBy = "desc".obs;

  //date selection variables
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
    await getTerminals();

    super.onInit();
  }

  Future<void> getActiveEmps() async {
    try {
      final QueryResult<Object> getEmployee = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_ACTIVE_EMPS(\$_eq: Boolean) {
                employee(where: {is_active: {_eq: \$_eq}}, order_by: {employee_full_name: asc}) {
                  created_at
                  created_by
                  document
                  employee
                  employee_class
                  employee_full_name
                  id
                  is_active
                  password
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: const <String, dynamic>{
            "_eq": true,
          },
        ),
      );
      if (getEmployee.hasException) {
        throw getEmployee.exception.toString();
      }

      employeeList.value = (getEmployee.data!['employee'] as List<dynamic>)
          .map(
            (dynamic employee) => Employee.fromJson(employee as Map<String, dynamic>),
          )
          .toList();
    } catch (e, stack) {
      _logger.severe("error getting active employees", e, stack);
      _notificationService.error("Error getting Active Employees");
    }
  }

  void resetTotalRows() {
    reports.clear();
    orderBy.refresh();
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) {
      return _notificationService.error(getTerminalsResult.exception.toString());
    }

    terminalList.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = terminalList.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    resetTotalRows();
  }
}
