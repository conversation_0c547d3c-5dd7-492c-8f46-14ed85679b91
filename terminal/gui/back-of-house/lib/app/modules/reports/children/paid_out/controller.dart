// ignore_for_file: avoid_dynamic_calls

import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class PaidOutController extends GetxController {
  PaidOutController();

  final Logger _logger = Logger('PaidOutController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  //general variables
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxBool isLoading = true.obs;

  // report variables
  final RxList<PaidOutGroup> reports = <PaidOutGroup>[].obs;
  final RxInt paidOutTotal = 0.obs;
  final RxList<dynamic> list = <dynamic>[].obs;
  final List<String> saleFlags = <String>[];

  // terminal dropdown variables
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTemerinal = Rxn<int>();

  //date selection variables
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
    await getRows();
    await getTerminals();

    super.onInit();
  }

  Future<void> getRows() async {
    try {
      isLoading.value = true;
      resetTotalRows();
      final QueryResult<Object> getPaidOutResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation	GET_PAID_OUT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
              get_paid_out(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                created_at
                created_by
                document
                end_at
                sale
                sale_number
                suspended
                updated_at
                updated_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": Helpers.getReportStartString(dateRangeController),
            "end_date": Helpers.getReportEndString(dateRangeController),
            "terminal_index": selectedTemerinal.value,
          },
        ),
      );

      if (getPaidOutResult.hasException) {
        throw getPaidOutResult.exception.toString();
      }
      final List<Sale> reportsRes =
          (getPaidOutResult.data!['get_paid_out'] as List<dynamic>).map((dynamic report) => Sale.fromJson(report as Map<String, dynamic>)).toList();

// Group sales into PaidOutGroup objects
      final Map<String, PaidOutGroup> paidOutGroups = {};

      for (int i = 0; i < reportsRes.length; i++) {
        final Sale sale = reportsRes[i];
        final String reasonCode = sale.document.saleHeader.saleDescription ?? "N/A";
        final int saleTotal = sale.document.saleHeader.total;

        // Create or update the group
        paidOutGroups.putIfAbsent(
          reasonCode,
          () => PaidOutGroup(
            reasonCode: reasonCode,
            sales: <Sale>[],
          ),
        );

        paidOutGroups[reasonCode]!.sales.add(sale);
        paidOutGroups[reasonCode]!.total += saleTotal;
      }

      // If you need to convert to a list of PaidOutGroup
      reports.value = paidOutGroups.values.toList();
      // Debug output or further processing

      isLoading.value = false;
    } catch (err, stack) {
      _logger.severe("Error getting paid out", err, stack);
      _notificationService.error("Error getting paid out");
    }
  }

  void resetTotalRows() {
    paidOutTotal.value = 0;
    reports.clear();
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) return _notificationService.error(getTerminalsResult.exception.toString());

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> printReport() async {
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    PrintJob printJobType;
    printJobType = PrinterInterface.buildPaidOutReport(
      paidOutReports: reports,
      scope: selectedScope.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(start).toLocal()),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(end).toLocal()),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe("Error printing sales breakdown", err, stack);
      _notificationService.error("Error printing sales breakdown");
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTemerinal.value = int.tryParse(value.toString());
    if (selectedTemerinal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    await getRows();
  }
}

class PaidOutGroup {
  PaidOutGroup({
    this.sales = const <Sale>[],
    this.reasonCode = "N/A",
    this.total = 0,
  });
  List<Sale> sales;
  String reasonCode;
  int total;
}
