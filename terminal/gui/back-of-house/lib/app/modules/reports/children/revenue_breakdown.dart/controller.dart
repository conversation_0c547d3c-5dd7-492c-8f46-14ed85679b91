// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class RevenueBreakdownController extends GetxController {
  RevenueBreakdownController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  final Logger _logger = Logger('RevenueController');

  final RxList<Sale> sales = <Sale>[].obs;
  final RxList<Report> printerRows = <Report>[].obs;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxList<dynamic> list = <dynamic>[].obs;
  final List<String> saleFlags = <String>[];
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxInt totalAccountable = 0.obs;
  final RxBool isLoading = true.obs;
  final RxInt subTotal = 0.obs;
  final RxInt taxTotal = 0.obs;
  final RxInt grossTotal = 0.obs;
  final RxInt netTotal = 0.obs;
  final RxInt discountTotal = 0.obs;
  final RxInt refundedTotal = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt giftIssuedTotal = 0.obs;
  final RxInt houseChargeTotal = 0.obs;
  final RxInt houseChargeCount = 0.obs;
  final RxInt dualPriceTotal = 0.obs;
  final RxInt tipTotal = 0.obs;
  final RxInt gratTotal = 0.obs;
  final RxList<TaxReport> taxReportList = <TaxReport>[].obs;
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
    await getRows();
    await getTerminals();
    isLoading.value = false;
    super.onInit();
  }

  Future<void> getHouseCharges(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(
        start,
        end,
        selectedTerminal.value,
      );
      houseRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        for (int i = 0; i < r.length; i++) {
          final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == 6) {
              houseChargeTotal.value += tender.amount!;
              houseChargeCount.value += 1;
            }
          }
        }
      });
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getTipTotal(String start, String end) async {
    try {
      final Either<ServiceError, List<EmployeeTipBreakdown>> tipRes = await _reportService.getTipTotal(
        start,
        end,
        selectedTerminal.value,
      );
      tipRes.fold((ServiceError l) {
        throw l.message;
      }, (List<EmployeeTipBreakdown> r) {
        for (final EmployeeTipBreakdown tipRow in r) {
          tipTotal.value += tipRow.tip_amount;
          gratTotal.value += tipRow.grat_amount;
        }
      });
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getTaxTotal(String start, String end) async {
    try {
      final Either<ServiceError, List<TaxReport>> taxRes = await _reportService.getTaxReports(
        start,
        end,
        selectedTerminal.value,
        null,
      );
      taxRes.fold((ServiceError l) {
        throw l.message;
      }, (List<TaxReport> r) {
        taxReportList.value = r;
      });

      for (final TaxReport element in taxReportList) {
        taxTotal.value += element.tax_amount;
      }
    } catch (err) {
      _notificationService.error("error getting tax data");
    }
  }

  Future<void> getDualPriceTotal(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> getCashDiscRes = await _reportService.getDualPriceTotal(
        start,
        end,
        selectedTerminal.value,
        null,
      );
      getCashDiscRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        for (final Sale sale in r) {
          dualPriceTotal.value += sale.document.saleHeader.dualPricingAmount;
        }
      });
    } catch (err) {
      _notificationService.error(err.toString());
    }
  }

  Future<void> getRows() async {
    resetTotals();
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    await getTaxTotal(start, end);
    await getHouseCharges(start, end);
    await getDualPriceTotal(start, end);
    await getTipTotal(start, end);
    try {
      final Either<ServiceError, List<Sale>> saleWindowRes = await _reportService.getSaleWindow(
        start,
        end,
      );
      saleWindowRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        sales.value = r;
      });

      for (int i = 0; i < sales.length; i++) {
        final Sale sale = sales[i];
        if (selectedTerminal.value != null) {
          if (selectedTerminal.value == sale.document.saleHeader.settleTerminalNumber) {
            handleData(sale);
          }
        } else {
          handleData(sale);
        }
      }
      netTotal.value = (grossTotal.value - discountTotal.value) + refundedTotal.value;
      totalAccountable.value = (((netTotal.value - paidOutTotal.value) - giftIssuedTotal.value) - houseChargeTotal.value) + taxTotal.value;
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  void handleData(Sale sale) {
    if (sale.document.saleHeader.saleFlags.contains(0) && !sale.document.saleHeader.saleFlags.contains(5)) {
      discountTotal.value += sale.document.saleHeader.discountTotal;

      for (final SaleRow saleRow in sale.document.saleRows) {
        if (!saleRow.flags.contains(7)) {
          grossTotal.value += saleRow.grossPrice;
          if (saleRow.flags.contains(0)) giftIssuedTotal.value += saleRow.originalPrice;
        } else {
          refundedTotal.value += saleRow.grossPrice;
          if (saleRow.flags.contains(0)) giftIssuedTotal.value += saleRow.originalPrice;
        }
      }
    }
    if (sale.document.saleHeader.saleFlags.contains(0) && sale.document.saleHeader.saleFlags.contains(5)) {
      paidOutTotal.value += sale.document.saleHeader.total;
    }
  }

  Future<void> printReport() async {
    PrintJob printJobType;
    printJobType = PrinterInterface.builRevenueReport(
      totalAccountable: totalAccountable.value,
      discountTotal: discountTotal.value,
      giftCardsIssued: giftIssuedTotal.value,
      houseChargeTotal: houseChargeTotal.value,
      grossTotal: grossTotal.value,
      netTotal: netTotal.value,
      paidOutTotal: paidOutTotal.value,
      refundsTotal: refundedTotal.value,
      dualPricingTotal: dualPriceTotal.value,
      taxTotal: taxTotal.value,
      taxList: taxReportList,
      tipTotal: tipTotal.value,
      gratTotal: gratTotal.value,
      scope: selectedScope.value,
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print Revenue Breakdown Success");
    } catch (err) {
      _logger.shout('error printing reveune report');
      _notificationService.error('error printing reveune report');
    }
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) return _notificationService.error(getTerminalsResult.exception.toString());

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    await getRows();
  }

  void resetTotals() {
    subTotal.value = 0;
    taxTotal.value = 0;
    grossTotal.value = 0;
    netTotal.value = 0;
    discountTotal.value = 0;
    refundedTotal.value = 0;
    paidOutTotal.value = 0;
    giftIssuedTotal.value = 0;
    dualPriceTotal.value = 0;
    tipTotal.value = 0;
    gratTotal.value = 0;
  }
}
