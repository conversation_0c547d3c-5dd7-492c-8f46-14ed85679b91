// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/sale_window.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/data/view_models/hourly_report.dart';
import 'package:backoffice/app/data/view_models/monthly_report.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/reports/children/sales_breakdown/extended_sales_breakdown.dart/dialog.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

class SalesBreakdownController extends GetxController {
  SalesBreakdownController();

  final Logger _logger = Logger('SalesBreakdownController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  // printer variables
  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;

  //general variables
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<ScaffoldState> key = GlobalKey();
  final RxList<dynamic> list = <dynamic>[].obs;

  // terminal dropdown variables
  final RxInt reportType = 0.obs;
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxString selectedScope = "Store".obs;

  // report variables
  final RxList<HourlyReport> hourlyReportRows = <HourlyReport>[].obs;
  final RxList<MonthlyReport> monthlyReportRows = <MonthlyReport>[].obs;
  final RxList<EmployeeTipBreakdown> tipList = <EmployeeTipBreakdown>[].obs;

  // Calculation Variables
  final RxInt total = 0.obs;
  final RxInt transactions = 0.obs;
  final RxInt taxTotal = 0.obs;
  final RxInt subTotal = 0.obs;
  final RxInt giftSaleTotal = 0.obs;
  final RxInt giftSaleCount = 0.obs;
  final RxInt paidOutCount = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt houseChargeTotal = 0.obs;
  final RxInt houseChargeCount = 0.obs;
  final RxInt tipTotal = 0.obs;

  //date selection variables
  DateTime selectedDate = DateTime.now();
  String? start;
  String? end;
  final TextEditingController dateController = TextEditingController();
  final RxString selectedValue = "Day".obs;
  final List<String> rangeList = <String>['Day', 'Month'];
  SaleWindow saleWindow = SaleWindow.empty();

  String today = "";
  final RxBool isLoading = true.obs;

  @override
  Future<void> onInit() async {
    saleWindow = Helpers.getReportWindow(selectedDate, scope: selectedValue.value);

    start = saleWindow.start;
    end = saleWindow.end;
    dateController.text = DateFormat("yyyy-MM-dd hh:mm:ss").format(
      DateTime.now(),
    );
    await getHourlyRows();
    await getTerminals();
    isLoading.value = false;

    super.onInit();
  }

  Future<void> getMonthlyRows() async {
    isLoading.value = true;
    await getGiftCardSales();
    await getPaidOut();
    await getHouseCharges();
    await getTipTotal();
    try {
      final Either<ServiceError, List<MonthlyReport>> monthlyRes =
          await _reportService.getMonthlySalesBreakdown(start!, end!, selectedTerminal.value);

      monthlyRes.fold(
        (ServiceError l) => throw l.message,
        (List<MonthlyReport> reports) {
          monthlyReportRows.value = reports;
          for (final MonthlyReport report in reports) {
            //need to fix printing
            // printerRows.add(report);
            transactions.value += report.transactions!;
            taxTotal.value += report.daily_tax_total!;
            subTotal.value += report.daily_base_price!;
            total.value += report.daily_actual_price_total!;
          }
        },
      );
    } catch (err) {
      _notificationService.error("Error Getting monthly rows");
    }

    for (final MonthlyReport monthlyReport in monthlyReportRows) {
      final String type = selectedValue.value;
      final CustomTableRow newEntry = CustomTableRow(
        onTapped: monthlyReport.transactions != 0
            ? () {
                getExtendedSales(
                  type: type,
                  selectedDate: monthlyReport.bottom_day!.toString(),
                );
              }
            : null,
        children: <Widget>[
          Text(DateFormat('yMMMd').format(monthlyReport.bottom_day!)),
          Text(
            monthlyReport.transactions != null ? monthlyReport.transactions.toString() : 0.toString(),
          ),
          Text(
            monthlyReport.daily_actual_price_total != null
                ? "\$${Helpers.formatCurrency(monthlyReport.daily_base_price!)}"
                : "\$  ${Helpers.formatCurrency(0)}",
          ),
          Text(
            monthlyReport.daily_actual_price_total != null
                ? "\$${Helpers.formatCurrency(monthlyReport.daily_tax_total!)}"
                : "\$  ${Helpers.formatCurrency(0)}",
          ),
          Text(
            monthlyReport.daily_actual_price_total != null
                ? "\$${Helpers.formatCurrency(monthlyReport.daily_base_price! + monthlyReport.daily_tax_total!)}"
                : "\$  ${Helpers.formatCurrency(0)}",
          ),
          // ignore: prefer_if_elements_to_conditional_expressions
          monthlyReport.transactions != null
              ? const FaIcon(
                  FontAwesomeIcons.eye,
                  color: R2Colors.primary500,
                )
              : const FaIcon(
                  FontAwesomeIcons.eyeSlash,
                  color: Colors.grey,
                ),
        ],
      );
      reportRows.add(newEntry);
    }

    final CustomTableRow deptTotals = CustomTableRow(
      divider: true,
      children: <Widget>[
        const Text('Dept Total:'),
        Text(transactions.value.toString()),
        Text("\$${Helpers.formatCurrency(subTotal.value)}"),
        Text("\$${Helpers.formatCurrency(taxTotal.value)}"),
        Text("\$${Helpers.formatCurrency(subTotal.value + taxTotal.value)}"),
        const Text(""),
      ],
    );
    reportTotalsRow.add(deptTotals);
    final CustomTableRow takeOutFee = CustomTableRow(
      children: <Widget>[
        const Text('Takeout Fee:'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text("\$${Helpers.formatCurrency(total.value - (subTotal.value + taxTotal.value))}"),
        const Text(""),
      ],
    );
    reportTotalsRow.add(takeOutFee);
    final CustomTableRow netTotals = CustomTableRow(
      children: <Widget>[
        const Text('Net Total:'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text("\$${Helpers.formatCurrency(total.value)}"),
        const Text(""),
      ],
    );
    reportTotalsRow.add(netTotals);
    final CustomTableRow paidOut = CustomTableRow(
      children: <Widget>[
        const Text('Paid Out:'),
        Text("${paidOutCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(paidOutTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(paidOut);
    final CustomTableRow houseChargeRow = CustomTableRow(
      children: <Widget>[
        const Text('House Charges:'),
        Text("${houseChargeCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(houseChargeTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(houseChargeRow);
    final CustomTableRow giftCardsSales = CustomTableRow(
      children: <Widget>[
        const Text('Gift Card Sales:'),
        Text("${giftSaleCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(giftSaleTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(giftCardsSales);
    final CustomTableRow finalTotals = CustomTableRow(
      style: CustomRowStyle.total,
      children: <Widget>[
        const Text('Total Accountable:'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text(
          "\$${Helpers.formatCurrency((total.value) - (giftSaleTotal.value + paidOutTotal.value + houseChargeTotal.value))}",
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(finalTotals);
    isLoading.value = false;
  }

  Future<void> getHourlyRows() async {
    isLoading.value = true;

    await getGiftCardSales();
    await getPaidOut();
    await getHouseCharges();
    await getTipTotal();
    try {
      final Either<ServiceError, List<HourlyReport>> hourlyRes = await _reportService.getHourlySalesBreakdown(start!, end!, selectedTerminal.value);

      hourlyRes.fold((ServiceError l) {
        throw l.message;
      }, (List<HourlyReport> reports) {
        hourlyReportRows.value = reports;
        for (final HourlyReport report in reports) {
          //need to fix printing
          transactions.value += report.transactions ?? 0;
          taxTotal.value += report.hourly_tax_total ?? 0;
          subTotal.value += report.hourly_base_price_total ?? 0;
          total.value += report.hourly_actual_price_total ?? 0;
        }
      });
    } catch (err) {
      _notificationService.error(err.toString());
    }

    for (final HourlyReport hourlyReport in hourlyReportRows) {
      final CustomTableRow newEntry = CustomTableRow(
        onTapped: hourlyReport.transactions != null
            ? () {
                getExtendedSales(
                  type: selectedValue.value,
                  bottomHour: hourlyReport.bottom_hour!.toUtc().toString(),
                  topHour: hourlyReport.top_hour!.toUtc().toString(),
                );
              }
            : null,
        children: <Widget>[
          Text(
            "${DateFormat('h:mma').format(
              hourlyReport.bottom_hour!.toLocal(),
            )}-${DateFormat('h:mma').format(
              hourlyReport.top_hour!.toLocal(),
            )}",
            style: const TextStyle(fontSize: 13),
          ),
          Text(hourlyReport.transactions != null ? hourlyReport.transactions.toString() : 0.toString()),
          Text(
            hourlyReport.hourly_base_price_total != null
                ? "\$${Helpers.formatCurrency(hourlyReport.hourly_base_price_total!)}"
                : "\$${Helpers.formatCurrency(0)}",
          ),
          Text(
            hourlyReport.hourly_tax_total != null ? "\$${Helpers.formatCurrency(hourlyReport.hourly_tax_total!)}" : "\$${Helpers.formatCurrency(0)}",
          ),
          Text(
            hourlyReport.hourly_actual_price_total != null
                ? "\$${Helpers.formatCurrency(hourlyReport.hourly_base_price_total! + hourlyReport.hourly_tax_total!)}"
                : "\$${Helpers.formatCurrency(0)}",
          ),
          if (hourlyReport.transactions != null)
            const FaIcon(
              FontAwesomeIcons.eye,
              color: R2Colors.primary500,
            )
          else
            const FaIcon(
              FontAwesomeIcons.eyeSlash,
              color: Colors.grey,
            ),
        ],
      );
      reportRows.add(newEntry);
    }

    final CustomTableRow totals = CustomTableRow(
      divider: true,
      children: <Widget>[
        const Text('Dept Total:'),
        Text(transactions.value.toString()),
        Text("\$${Helpers.formatCurrency(subTotal.value)}"),
        Text("\$${Helpers.formatCurrency(taxTotal.value)}"),
        Text("\$${Helpers.formatCurrency(subTotal.value + taxTotal.value)}"),
        const Text(""),
      ],
    );
    final CustomTableRow takeOutFee = CustomTableRow(
      children: <Widget>[
        const Text('TakeOutFee'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text("\$${Helpers.formatCurrency(total.value - (subTotal.value + taxTotal.value))}"),
        const Text(""),
      ],
    );
    final CustomTableRow totalsNet = CustomTableRow(
      children: <Widget>[
        const Text('Net Total:'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text("\$${Helpers.formatCurrency(total.value)}"),
        const Text(""),
      ],
    );
    reportTotalsRow.add(totals);
    reportTotalsRow.add(takeOutFee);
    reportTotalsRow.add(totalsNet);
    final CustomTableRow paidOut = CustomTableRow(
      children: <Widget>[
        const Text('Paid Out:'),
        Text("${paidOutCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(paidOutTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(paidOut);
    final CustomTableRow houseChargeRow = CustomTableRow(
      children: <Widget>[
        const Text('House Charges:'),
        Text("${houseChargeCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(houseChargeTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(houseChargeRow);
    final CustomTableRow giftCardsRedeemed = CustomTableRow(
      children: <Widget>[
        const Text('Gift Card Sales:'),
        Text("${giftSaleCount.value}"),
        const Text(""),
        const Text(""),
        Text(
          "-\$${Helpers.formatCurrency(giftSaleTotal.value)}",
          style: const TextStyle(
            color: Color.fromRGBO(255, 53, 53, 1),
          ),
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(giftCardsRedeemed);

    final CustomTableRow finalTotals = CustomTableRow(
      style: CustomRowStyle.total,
      children: <Widget>[
        const Text('Total Accountable:'),
        const Text(""),
        const Text(""),
        const Text(""),
        Text(
          "\$${Helpers.formatCurrency((total.value) - (giftSaleTotal.value + paidOutTotal.value + houseChargeTotal.value))}",
        ),
        const Text(""),
      ],
    );
    reportTotalsRow.add(finalTotals);
    isLoading.value = false;
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{
          "_eq": "systemDevice",
        },
      ),
    );

    if (getTerminalsResult.hasException) return _notificationService.error(getTerminalsResult.exception.toString());

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> getHouseCharges() async {
    try {
      final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(start!, end!, selectedTerminal.value);

      houseRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> res) {
        for (int i = 0; i < res.length; i++) {
          final List<SaleTender> tenders = res[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              houseChargeTotal.value += tender.amount!;
              houseChargeCount.value += 1;
            }
          }
        }
      });
    } catch (e) {
      _notificationService.error('error getting house charges');
    }
  }

  Future<void> getTipTotal() async {
    try {
      final Either<ServiceError, List<EmployeeTipBreakdown>> tipRes = await _reportService.getTipTotal(start!, end!, selectedTerminal.value);
      tipRes.fold((ServiceError l) {
        throw l.message;
      }, (List<EmployeeTipBreakdown> res) {
        for (final EmployeeTipBreakdown tipRow in res) {
          tipTotal.value += tipRow.tip_amount;
        }
      });
    } catch (e) {
      _notificationService.error("error getting tip Total");
    }
  }

  Future<void> getPaidOut() async {
    try {
      final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(start!, end!, selectedTerminal.value);
      paidOutRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> res) {
        for (final Sale paidOutRow in res) {
          paidOutCount.value++;
          paidOutTotal.value += paidOutRow.document.saleHeader.total;
        }
      });
    } catch (e) {
      _notificationService.error('error getting paid out');
    }
  }

  Future<void> getGiftCardSales() async {
    try {
      final Either<ServiceError, List<GiftReport>> getGiftSalesResult = await _reportService.getGiftCardSales(start!, end!, selectedTerminal.value);

      getGiftSalesResult.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<GiftReport> res) {
          for (final GiftReport giftSaleRow in res) {
            giftSaleCount.value++;
            giftSaleTotal.value += giftSaleRow.original_price;
          }
        },
      );
    } catch (e) {
      _notificationService.error('error getting gift card sales');
    }
  }

  Future<void> rangeHandler() async {
    switch (reportType.value) {
      case 0:
        selectedValue.value = "Day";
        selectedDate = DateTime.parse(dateController.text);
        saleWindow = Helpers.getReportWindow(selectedDate, scope: selectedValue.value);
        start = saleWindow.start;
        end = saleWindow.end;
        resetTotalRows();
        await getHourlyRows();
        break;
      case 1:
        selectedValue.value = "Month";
        selectedDate = DateTime.parse(dateController.text);
        saleWindow = Helpers.getReportWindow(selectedDate, scope: selectedValue.value);
        start = saleWindow.start;
        end = saleWindow.end;
        resetTotalRows();
        await getMonthlyRows();
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    resetTotalRows();

    if (reportType.value == 0) {
      await getHourlyRows();
    } else {
      await getMonthlyRows();
    }
  }

  Future<void> printReport() async {
    PrintJob printJobType;
    if (selectedValue.value == "Month") {
      printJobType = PrinterInterface.buildMonthlyBreakdownReport(
        reports: monthlyReportRows,
        giftRedeemedTotal: giftSaleTotal.value,
        paidOutTotal: paidOutTotal.value,
        scope: selectedScope.value,
        houseChargeTotal: houseChargeTotal.value,
        reportPeriodStart: DateFormat.yMd().add_jm().format(DateTime.parse(start!).toLocal()),
        reportPeriodEnd: DateFormat.yMd().add_jm().format(DateTime.parse(end!).toLocal()),
      );
    } else {
      printJobType = PrinterInterface.buildReport(
        reports: hourlyReportRows,
        tipTotal: tipTotal.value,
        giftRedeemedTotal: giftSaleTotal.value,
        houseChargeTotal: houseChargeTotal.value,
        paidOutTotal: paidOutTotal.value,
        scope: selectedScope.value,
        reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(start!).toLocal()),
        reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(end!).toLocal()),
      );
    }

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _notificationService.error('error printing sales breakdown');
      _logger.severe("error printing sales breakdown", err, stack);
    }
  }

  Future<void> dateHandler(String value) async {
    selectedDate = DateTime.parse(dateController.text);
    if (selectedValue.value == "Month" && reportType.value == 1) {
      saleWindow = saleWindow = Helpers.getReportWindow(selectedDate, scope: selectedValue.value);
      start = saleWindow.start;
      end = saleWindow.end;
      resetTotalRows();
      await getMonthlyRows();
    } else {
      saleWindow = Helpers.getReportWindow(selectedDate, scope: selectedValue.value);
      start = saleWindow.start;
      end = saleWindow.end;
      resetTotalRows();
      await getHourlyRows();
    }
  }

  void getExtendedSales({required String type, String? bottomHour, String? topHour, String? selectedDate}) {
    Get.bottomSheet(
      ThinBottomSheet(
        child: ExtendedSalesBreakdownDialog(
          type: type,
          bottomHour: bottomHour,
          topHour: topHour,
          selectedDate: selectedDate,
        ),
      ),
      isScrollControlled: true,
    );
  }

  void resetTotalRows() {
    reportRows.clear();
    reportTotalsRow.clear();
    hourlyReportRows.clear();
    monthlyReportRows.clear();
    transactions.value = 0;
    taxTotal.value = 0;
    subTotal.value = 0;
    total.value = 0;
    paidOutTotal.value = 0;
    paidOutCount.value = 0;
    giftSaleTotal.value = 0;
    giftSaleCount.value = 0;
    houseChargeTotal.value = 0;
    houseChargeCount.value = 0;
    tipTotal.value = 0;
  }
}
