import 'package:backoffice/app/data/enums/extended_sales_breakdown_status.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

class ExtendedSalesBreakdownController extends GetxController {
  ExtendedSalesBreakdownController(
    this.type,
    this.bottomHour,
    this.topHour,
    this.selectedDate,
  );
  final String? type;
  final String? bottomHour;
  final String? topHour;
  final String? selectedDate;

  final Logger _logger = Logger('SalesBreakdownEXTController');

  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();

  Rx<ExtendedBreakdownDialogStatus> currentStatus = ExtendedBreakdownDialogStatus.TRANSACTIONS.obs;

  ScrollController transactionListScrollController = ScrollController();

  bool hasMultipleTenders = false;
  List<dynamic> currentTenders = <dynamic>[];
  String? flagTypeString;
  bool multipleTenders = false;
  String? mediaTypeString;
  String? mediaTypeStringList;
  final List<String> mediaStrings = <String>[];
  List<Card> transactionRows = <Card>[];
  RxList<Sale> sales = <Sale>[].obs;
  String legacyGiftName = "Legacy Gift";
  bool showCommentRows = false;
  bool showSaleDesc = false;

  Employee? cashier;
  Sale selectedSale = Sale.empty();

  @override
  Future<void> onInit() async {
    if (type == "Day") {
      await getHourlyData(bottomHour!, topHour!);
      print(bottomHour);
      print(topHour);
    } else {
      await getDailyData(selectedDate!);
    }
    await getLegacyGiftName();

    super.onInit();
  }

  Future<void> getHourlyData(String bottomHour, String topHour) async {
    try {
      final QueryResult<Object> getHourlyDataResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_HOURLY_SALE_DATA(\$bottomHour: timestamptz, \$topHour: timestamptz, \$jsonFilter: jsonb, \$_contains: jsonb) {
                sale(where: {end_at: {_gt: \$bottomHour}, _and: {end_at: {_lt: \$topHour}, _and: {document: {_contains: \$jsonFilter}, _and: {_not: {document: {_contains: \$_contains}}}}}}) {
                  created_at
                  created_by
                  document
                  end_at
                  sale
                  sale_number
                  suspended
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "bottomHour": bottomHour,
            "topHour": topHour,
            "jsonFilter": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[0],
              },
            },
            "_contains": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[5],
              },
            },
          },
        ),
      );

      if (getHourlyDataResult.hasException) {
        throw getHourlyDataResult.exception!.graphqlErrors.toString();
      }
      final List<Sale> hourlySales =
          (getHourlyDataResult.data!['sale'] as List<dynamic>).map((dynamic sale) => Sale.fromJson(sale as Map<String, dynamic>)).toList();
      for (final Sale sale in hourlySales) {
        sales.add(sale);
      }
    } catch (e, stack) {
      _logger.severe("error getting hourly data", e, stack);
      _notificationService.error("error getting hourly data");
    }
  }

  Future<void> getDailyData(String selectedDate) async {
    final DateTime formattedDate = DateTime.parse(selectedDate);
    final String bottomDate = DateFormat('MM-d-y').format(formattedDate);
    final DateTime topDay = formattedDate.add(const Duration(days: 1));
    final String topDate = DateFormat('MM-d-y').format(topDay);

    try {
      final QueryResult<Object> getDailyDataResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_MONTHLY_SALE_DATA(\$bottomDay: timestamptz, \$topDay: timestamptz, \$jsonFilter: jsonb, \$_contains: jsonb) {
                sale(where: {end_at: {_gt: \$bottomDay}, _and: {end_at: {_lt: \$topDay}, _and: {document: {_contains: \$jsonFilter}, _and: {_not: {document: {_contains: \$_contains}}}}}}) {
                  created_at
                  created_by
                  document
                  end_at
                  sale
                  sale_number
                  suspended
                  updated_at
                  updated_by
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "bottomDay": bottomDate,
            "topDay": topDate,
            "jsonFilter": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[0],
              },
            },
            "_contains": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[5],
              },
            },
          },
        ),
      );
      if (getDailyDataResult.hasException) {
        throw getDailyDataResult.exception!.graphqlErrors.toString();
      }

      final List<Sale> dailySales =
          (getDailyDataResult.data!['sale'] as List<dynamic>).map((dynamic sale) => Sale.fromJson(sale as Map<String, dynamic>)).toList();

      for (final Sale sale in dailySales) {
        sales.add(sale);
      }
    } catch (e, stack) {
      _logger.severe("error getting monthly sale data", e, stack);
      _notificationService.error("error getting monthly sale data");
    }
  }

  Future<void> getCashier(Sale sale) async {
    try {
      final QueryResult<Object> getCashierResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             query GET_CASHIER(\$employee: uuid!) {
              employee_by_pk(employee: \$employee) {
                 created_at
                  created_by
                  document
                  employee
                  employee_class
                  id
                  is_active
                  password
                  updated_at
                  updated_by
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "employee": sale.created_by,
          },
        ),
      );

      if (getCashierResult.hasException) {
        throw getCashierResult.exception!.graphqlErrors.toString();
      }

      cashier = Employee.fromJson(
        getCashierResult.data!['employee_by_pk'] as Map<String, dynamic>,
      );
    } catch (e, stack) {
      _logger.severe("error getting cashier", e, stack);
      _notificationService.error('error getting cashier');
    }
  }

  Future<void> getHeaderData(
    Sale sale,
  ) async {
    if (sale.document.saleHeader.saleFlags.isEmpty) {
      flagTypeString = "No Sales Flags";

      flagTypeString!;
    } else {
      for (final int tenderType in sale.document.saleHeader.saleFlags) {
        final SaleFlags tender = SaleFlags.values[tenderType];
        switch (tender) {
          case SaleFlags.COMPLETED:
            flagTypeString = "Completed";
            break;
          case SaleFlags.NO_SALE:
            flagTypeString = "No Sale";
            break;
          case SaleFlags.SUSPENDED:
            flagTypeString = "Suspended";
            break;
          case SaleFlags.CANCEL:
            flagTypeString = "Cancelled";
            break;
          case SaleFlags.REFUNDED:
            flagTypeString = "Refunded";
            break;
          case SaleFlags.PAID_OUT:
            flagTypeString = "Paid Out";
            break;
          case SaleFlags.GIFT_ISSUE:
            flagTypeString = "Gift Issue";
            break;
          case SaleFlags.GIFT_REDEMPTION:
            flagTypeString = "Gift Redeemed";
            break;
          case SaleFlags.DUAL_PRICING:
            flagTypeString = "Dual Pricing";
            break;
          case SaleFlags.NONREFUNDABLE:
            flagTypeString = "Non-Refundable";
            break;
          case SaleFlags.TAX_FORGIVEN:
            flagTypeString = "Tax forgiven";
            break;
          case SaleFlags.TIP_ADJUSTED:
            flagTypeString = "Tip Adjusted";
            break;
          case SaleFlags.RECEIPT_PRINTED:
            flagTypeString = "Receipt Printed";
            break;
          case SaleFlags.PREP_PRINTED:
            flagTypeString = "Prep Ticket Printed";
            break;
          case SaleFlags.REOPENED:
            flagTypeString = "Reopened";
            break;
          case SaleFlags.VOIDED:
            flagTypeString = "Voided";
            break;
          case SaleFlags.PICK_UP:
            flagTypeString = "Voided";
            break;
          case SaleFlags.LOAN_OUT:
            flagTypeString = "Voided";
            break;
          case SaleFlags.COMBINED:
            flagTypeString = "Combined";
            break;
          case SaleFlags.QR_PAY:
            flagTypeString = "QR-Pay";
            break;
          case SaleFlags.ONLINE_ORDER:
            flagTypeString = "Online Order";
            break;
        }
        flagTypeString!;
      }
    }

    if (sale.document.saleHeader.tenders.length <= 1) {
      hasMultipleTenders = false;
    } else {
      final List<SaleTender> saleTenders = sale.document.saleHeader.tenders;
      for (int i = 0; i < saleTenders.length; i++) {
        if (!currentTenders.contains(saleTenders[i].media)) {
          currentTenders.add(saleTenders[i].media);
        }
      }
      if (currentTenders.length > 1) {
        multipleTenders = true;
      } else {
        multipleTenders = false;
      }
    }

    mediaStrings.clear();

    for (final SaleTender mediaType in sale.document.saleHeader.tenders) {
      final PaymentMediaType media = PaymentMediaType.values[mediaType.media!];
      mediaTypeString = Helpers.mediaTypeAsString(media);
      if (!mediaStrings.contains(mediaTypeString)) {
        mediaStrings.add(mediaTypeString!);
      }
    }
    if (mediaStrings.isNotEmpty) {
      mediaTypeStringList = mediaStrings.reduce((String value, String element) {
        return "$value,  $element";
      });
    }
  }

  Future<void> getLegacyGiftName() async {
    try {
      final QueryResult<Object> getMerchResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
          ),
        ),
      );
      if (getMerchResult.hasException) {
        throw getMerchResult.exception.toString();
      }
      final MerchantJsonRecord merchRecord = MerchantJsonRecord.fromJson(
        (getMerchResult.data!['json_record'] as List<dynamic>)[0] as Map<String, dynamic>,
      );
      legacyGiftName = merchRecord.document.legacyGiftName;
      showCommentRows = merchRecord.document.commentRowsOnReceipt;
      showSaleDesc = merchRecord.document.saleDescOnReceipt;
    } catch (e, stack) {
      _logger.severe('error getting legacy gift title', e, stack);
      _notificationService.error(e.toString());
    }
  }

  Future<void> printReceipt() async {
    try {
      final PrintJob printJobType = PrinterInterface.buildReceipt(
        currentEmployee: cashier!,
        sale: selectedSale,
        currentUser: cashier!,
        legacyGiftName: legacyGiftName,
        showCommentRows: showCommentRows,
        showSaleDesc: showSaleDesc,
      );

      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }

      final PrintJob generatedPrintJob = PrintJob.fromJson(
        printJobResult.data!['insert_print_job_one'] as Map<String, dynamic>,
      );

      final String uuid = generatedPrintJob.print_job;
      final String date = generatedPrintJob.created_at.toString();

      final QueryResult<Object> printTriggerResultResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PJ_TRIGGER(\$created_at: timestamp, \$pj_trigger: uuid) {
               insert_pj_trigger(objects: {created_at: \$created_at, pj_trigger: \$pj_trigger}) {
                    affected_rows
                    returning {
                      pj_trigger
                      created_at
                    }
                  }
                }

            ''',
          ),
          variables: <String, dynamic>{
            "created_at": date,
            "pj_trigger": uuid,
          },
        ),
      );

      if (printTriggerResultResult.hasException) {
        throw printTriggerResultResult.exception.toString();
      }
      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe("error printing", err, stack);
      _notificationService.error('error printing');
    }
  }
}
