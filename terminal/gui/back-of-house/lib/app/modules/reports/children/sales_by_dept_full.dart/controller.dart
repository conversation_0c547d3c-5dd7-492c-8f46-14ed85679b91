// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/sales_by_dept.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class SalesByDeptController extends GetxController {
  SalesByDeptController();

  final Logger _logger = Logger('SalesByDeptController');

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  final GlobalKey<FormState> formKey = GlobalKey();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final RxBool expanded = false.obs;
  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTaxesRow = <CustomTableRow>[].obs;
  final RxList<SalesByDeptReport> itemData = <SalesByDeptReport>[].obs;
  final RxList<SalesByDeptReport> deptData = <SalesByDeptReport>[].obs;
  final RxList<dynamic> list = <dynamic>[].obs;
  final RxString selectedScope = "Store".obs;
  final RxInt total = 0.obs;
  final RxInt count = 0.obs;
  final RxInt taxTotal = 0.obs;
  final RxInt subTotal = 0.obs;
  final RxDouble percentageTotal = 0.00.obs;
  final RxList<TaxReport> taxRows = <TaxReport>[].obs;
  final RxInt refundTotal = 0.obs;
  final RxInt refundCount = 0.obs;
  final RxInt giftSaleTotal = 0.obs;
  final RxInt giftSaleCount = 0.obs;
  final RxInt paidOutCount = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt houseChargeTotal = 0.obs;
  final RxInt houseChargeCount = 0.obs;

  final RxInt takeoutFeeTotal = 0.obs;

  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxBool isLoading = true.obs;

  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());
    await getTerminals();
    await getRows();

    super.onInit();
  }

  Future<void> getTakeoutFeeTotal(String start, String end) async {
    try {
      final Either<ServiceError, int> takeOutFeeRes = await _reportService.getTakeOutFee(
        startDate: start,
        endDate: end,
        selectedTerminal: selectedTerminal.value,
      );

      takeOutFeeRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (int res) {
          takeoutFeeTotal.value = res;
        },
      );
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getHouseCharges(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> houseChargeRes = await _reportService.getHouseCharges(
        start,
        end,
        selectedTerminal.value,
      );
      houseChargeRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<Sale> r) {
          for (int i = 0; i < r.length; i++) {
            final List<SaleTender> tenders = r[i].document.saleHeader.tenders;
            for (final SaleTender tender in tenders) {
              if (tender.media == 6) {
                houseChargeTotal.value += tender.amount!;
                houseChargeCount.value += 1;
              }
            }
          }
        },
      );
    } catch (err) {
      _notificationService.error(err.toString());
    }
  }

  Future<void> getgiftCardSales(String start, String end) async {
    try {
      final Either<ServiceError, List<GiftReport>> giftCardRes = await _reportService.getGiftCardSales(
        start,
        end,
        selectedTerminal.value,
      );
      giftCardRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<GiftReport> r) {
          for (final GiftReport giftSaleRow in r) {
            giftSaleCount.value++;
            giftSaleTotal.value += giftSaleRow.original_price;
          }
        },
      );
    } catch (err) {
      _notificationService.error(err.toString());
    }
  }

  Future<void> getPaidOut(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(
        start,
        end,
        selectedTerminal.value,
      );
      paidOutRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<Sale> r) {
          for (final Sale paidOutRow in r) {
            paidOutCount.value++;
            paidOutTotal.value += paidOutRow.document.saleHeader.total;
          }
        },
      );
    } catch (err) {
      _notificationService.error('error getting paid out');
    }
  }

  Future<void> getTaxTotal(String start, String end) async {
    try {
      final List<String> taxStrings = <String>[];
      final Either<ServiceError, List<String>> taxListRes = await _reportService.getTaxList();
      taxListRes.fold((ServiceError l) {
        throw l.message;
      }, (List<String> r) {
        taxStrings.addAll(r);
      });
      final Either<ServiceError, List<TaxReport>> taxRes = await _reportService.getTaxReports(
        start,
        end,
        selectedTerminal.value,
        null,
      );

      taxRes.fold((ServiceError l) {
        throw l.message;
      }, (List<TaxReport> r) {
        taxRows.value = r;
      });

      for (int i = 0; i < taxStrings.length; i++) {
        taxTotal.value += taxRows.firstWhereOrNull((TaxReport element) => element.description == taxStrings[i])?.tax_amount ?? 0;
        reportTaxesRow.add(
          CustomTableRow(
            children: <Widget>[
              Text("${taxStrings[i]}:"),
              const Text(""),
              Text(
                "\$${Helpers.formatCurrency(taxRows.firstWhereOrNull((TaxReport element) => element.description == taxStrings[i])?.tax_amount ?? 0)}",
              ),
              const Text(""),
            ],
          ),
        );
      }
    } catch (err) {
      _notificationService.error("error getting cash tax data");
    }
  }

  Future<void> getRows() async {
    isLoading.value = true;
    resetData();
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    await getTaxTotal(start, end);
    await getHouseCharges(start, end);
    await getgiftCardSales(start, end);
    await getPaidOut(start, end);
    await getTakeoutFeeTotal(start, end);

    try {
      final Either<ServiceError, List<SalesByDeptReport>> salesByRangeRes = await _reportService.getSalesByRange(
        start,
        end,
        <int>[if (selectedTerminal.value != null) selectedTerminal.value!],
      );

      salesByRangeRes.fold((ServiceError l) {
        throw l.message;
      }, (List<SalesByDeptReport> r) {
        for (final SalesByDeptReport report in r) {
          if (report.item_actual_price! >= 0) subTotal.value += report.item_actual_price!;
          itemData.add(report);
        }
      });
      final Either<ServiceError, List<SalesByDeptReport>> salesByDeptRes = await _reportService.getSalesByDeptFull(
        start,
        end,
        selectedTerminal.value,
      );

      salesByDeptRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<SalesByDeptReport> r) {
          for (final SalesByDeptReport report in r) {
            final RxDouble deptPercentage = 0.0.obs;

            for (final SalesByDeptReport item in itemData) {
              if (item.department_title == report.department_title) {
                deptPercentage.value = item.percentage! + deptPercentage.value;
              }
            }
            report.percentage = deptPercentage.value;

            deptData.add(report);
            count.value += report.dept_count!;
            total.value += report.dept_actual_price!;

            percentageTotal.value += report.percentage!;
          }
        },
      );

      itemData.sort(
        (SalesByDeptReport a, SalesByDeptReport b) => (a.department_title ?? "").compareTo(b.department_title ?? ""),
      );

      for (int i = 0; i < deptData.length; i++) {
        for (int j = 0; j < itemData.length; j++) {
          if (itemData[j] == itemData.last) {
            itemData.add(deptData[i]);
            break;
          }
          if (itemData[j].department_title != itemData[j + 1].department_title && itemData[j].department_title == deptData[i].department_title) {
            itemData.insert(j + 1, deptData[i]);
            break;
          } else {
            continue;
          }
        }
      }

      for (int i = 0; i < itemData.length; i++) {
        if (itemData[i] == itemData[0]) {
          itemData.insert(
            0,
            SalesByDeptReport(
              dept_header: itemData[i].department_title.toString(),
            ),
          );
        }
        if (itemData[i] != itemData[0]) {
          if (itemData[i - 1].dept_actual_price != null && itemData[i - 1].dept_header == null) {
            itemData.insert(
              i,
              SalesByDeptReport(
                dept_header: itemData[i].department_title.toString(),
              ),
            );
          }
        }
      }
      for (int i = 0; i < itemData.length; i++) {
        if (itemData[i].dept_header != null) {
          reportRows.add(
            CustomTableRow(
              padding: const EdgeInsets.only(top: 12),
              children: <Widget>[
                CustomTextCell(
                  itemData[i].dept_header.toString(),
                  style: TextCellStyle.subheader,
                ),
                const Text(""),
                const Text(""),
                const Text(""),
              ],
            ),
          );
        } else {
          reportRows.add(
            CustomTableRow(
              padding: const EdgeInsets.only(top: 2, bottom: 2),
              style: itemData[i].item_description != null ? CustomRowStyle.basic : CustomRowStyle.subtotal,
              children: <Widget>[
                if (itemData[i].item_description != null)
                  CustomTextCell(
                    itemData[i].item_description.toString(),
                    style: TextCellStyle.indent,
                    textStyle: TextStyle(color: itemData[i].item_actual_price! < 0 ? R2Colors.red500 : Colors.black),
                  )
                else
                  const Text("Dept Totals:"),
                if (itemData[i].item_count == null)
                  CustomTextCell(
                    itemData[i].dept_count.toString(),
                    style: TextCellStyle.indent,
                  )
                else
                  Text(
                    itemData[i].item_count.toString(),
                    style: TextStyle(color: itemData[i].item_actual_price! < 0 ? R2Colors.red500 : Colors.black),
                  ),
                if (itemData[i].item_actual_price == null)
                  CustomTextCell(
                    " \$ ${Helpers.formatCurrency(int.parse((itemData[i].dept_actual_price!).toString()))}",
                    style: TextCellStyle.indent,
                  )
                else if ((itemData[i].item_actual_price!) >= 0)
                  Text(
                    " \$ ${Helpers.formatCurrency(int.parse((itemData[i].item_actual_price!).toString()))}",
                    style: const TextStyle(color: Colors.black),
                  )
                else
                  Text(
                    "-\$ ${Helpers.formatCurrency(int.parse((itemData[i].item_actual_price!).abs().toString()))}",
                    style: const TextStyle(color: R2Colors.red500),
                  ),
                Text(
                  itemData[i].percentage!.toStringAsFixed(2),
                ),
              ],
            ),
          );
        }
      }

      final RxInt accountablesTotal = 0.obs;
      accountablesTotal.value = giftSaleTotal.value + paidOutTotal.value + houseChargeTotal.value;

      final List<CustomTableRow> totalSales = <CustomTableRow>[
        CustomTableRow(
          divider: true,
          children: <Widget>[
            const Text("Dept Total:"),
            Text(count.value.toString()),
            Text(
              (total.value) >= 0 ? "\$${Helpers.formatCurrency(total.value)}" : "-\$${Helpers.formatCurrency(total.value.abs())}",
            ),
            const Text(""),
          ],
        ),
        ...reportTaxesRow,
        CustomTableRow(
          children: <Widget>[
            const Text("Net Total:"),
            Text(count.value.toString()),
            Text(
              (total.value + taxTotal.value) >= 0
                  ? "\$${Helpers.formatCurrency(total.value + taxTotal.value)}"
                  : "-\$${Helpers.formatCurrency((total.value + taxTotal.value).abs())}",
            ),
            const Text(""),
          ],
        ),
        CustomTableRow(
          children: <Widget>[
            const Text('Takeout fees:'),
            const Text(""),
            Text(
              "\$${Helpers.formatCurrency(takeoutFeeTotal.value)}",
            ),
            const Text(""),
          ],
        ),
        CustomTableRow(
          children: <Widget>[
            const Text('Paid Out:'),
            Text(
              paidOutCount.value.toString(),
              style: const TextStyle(color: R2Colors.red500),
            ),
            Text(
              "-\$${Helpers.formatCurrency(paidOutTotal.value)}",
              style: const TextStyle(color: R2Colors.red500),
            ),
            const Text(""),
          ],
        ),
        CustomTableRow(
          children: <Widget>[
            const Text('House Charges:'),
            Text(
              houseChargeCount.value.toString(),
              style: const TextStyle(color: R2Colors.red500),
            ),
            Text(
              "-\$${Helpers.formatCurrency(houseChargeTotal.value)}",
              style: const TextStyle(color: R2Colors.red500),
            ),
            const Text(""),
          ],
        ),
        CustomTableRow(
          children: <Widget>[
            const Text('Gift Card Sales:'),
            Text(
              giftSaleCount.value.toString(),
              style: const TextStyle(color: R2Colors.red500),
            ),
            Text(
              "-\$${Helpers.formatCurrency(giftSaleTotal.value)}",
              style: const TextStyle(color: R2Colors.red500),
            ),
            const Text(""),
          ],
        ),
        CustomTableRow(
          style: CustomRowStyle.total,
          children: <Widget>[
            const Text("Total Accountable:"),
            Text((count.value - refundCount.value).toString()),
            Text(
              ((total.value + taxTotal.value + takeoutFeeTotal.value) - accountablesTotal.value) >= 0
                  ? "\$${Helpers.formatCurrency((total.value + taxTotal.value + takeoutFeeTotal.value) - accountablesTotal.value)}"
                  : "-\$${Helpers.formatCurrency(((total.value + taxTotal.value + takeoutFeeTotal.value) - accountablesTotal.value).abs())}",
            ),
            const Text(""),
          ],
        ),
      ];
      reportTotalsRow.addAll(totalSales);
      isLoading.value = false;
    } catch (err) {
      isLoading.value = false;
      _notificationService.error(err.toString());
      throw err.toString();
    }
  }

  Future<void> printReport() async {
    final PrintJob printJobType = PrinterInterface.buildSalesByRangeReport(
      reports: itemData,
      countTotal: count.value,
      total: total.value,
      takeoutFees: takeoutFeeTotal.value,
      giftCardSales: giftSaleTotal.value,
      giftCardSalesCount: giftSaleCount.value,
      paidOutTotal: paidOutTotal.value,
      paidOutCount: paidOutCount.value,
      houseChargeCount: houseChargeCount.value,
      houseChargeTotal: houseChargeTotal.value,
      taxList: taxRows,
      taxTotal: taxTotal.value,
      tipTotal: 0,
      scope: selectedScope.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(
        DateTime.parse(
          Helpers.getReportStartString(dateRangeController),
        ).toLocal(),
      ),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(
        DateTime.parse(
          Helpers.getReportEndString(dateRangeController),
        ).toLocal(),
      ),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print sales by department success");
    } catch (err, stack) {
      _logger.severe("error printing", err, stack);
      _notificationService.error("error printing sales by dept");
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    resetData();

    await getRows();
  }

  Future<void> getTerminals() async {
    try {
      final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
          ),
          variables: const <String, dynamic>{"_eq": "systemDevice"},
        ),
      );

      if (getTerminalsResult.hasException) throw getTerminalsResult.exception.toString();

      list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
    } catch (e, stack) {
      _logger.severe("error getting terminals", e, stack);
      _notificationService.error('error loading terminals');
    }
  }

  void resetData() {
    count.value = 0;
    total.value = 0;
    percentageTotal.value = 0.00;
    taxTotal.value = 0;
    itemData.clear();
    reportTaxesRow.clear();

    deptData.clear();
    reportRows.clear();
    giftSaleTotal.value = 0;
    giftSaleCount.value = 0;
    paidOutCount.value = 0;
    paidOutTotal.value = 0;
    houseChargeCount.value = 0;
    houseChargeTotal.value = 0;
    reportTotalsRow.clear();
    takeoutFeeTotal.value = 0;
  }
}
