// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/major_report.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/data/view_models/items_by_department.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/reports/children/sales_by_dept_summary.dart/extended_sales_by_dept/dialog.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class SalesByDeptSummaryController extends GetxController {
  SalesByDeptSummaryController();

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();
  final Logger _logger = Logger('SalesByDeptSumController');

  final GlobalKey<FormState> formKey = GlobalKey();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final RxList<CustomTableRow> reportRows = <CustomTableRow>[].obs;
  final RxList<CustomTableRow> reportTotalsRow = <CustomTableRow>[].obs;
  final RxList<SalesByDeptSummaryReport> dataRows = <SalesByDeptSummaryReport>[].obs;
  final RxList<SalesByDeptSummaryReport> printerRows = <SalesByDeptSummaryReport>[].obs;
  final RxList<dynamic> list = <dynamic>[].obs;
  final List<ItemsByDepartmentReport> itemRows = <ItemsByDepartmentReport>[];
  final RxString selectedDepartment = ''.obs;
  final RxBool isLoading = false.obs;
  final RxString selectedScope = "Store".obs;
  final RxInt giftSaleTotal = 0.obs;
  final RxInt giftSaleCount = 0.obs;
  final RxInt subTotal = 0.obs;
  final RxList<TaxReport> taxTotals = <TaxReport>[].obs;
  final RxList<MajorReport> majorRows = <MajorReport>[].obs;
  final RxInt taxTotal = 0.obs;
  final RxInt taxCount = 0.obs;
  final RxInt total = 0.obs;
  final RxInt count = 0.obs;
  final RxInt refundCount = 0.obs;
  final RxInt paidOutCount = 0.obs;
  final RxInt paidOutTotal = 0.obs;
  final RxInt houseChargeTotal = 0.obs;
  final RxInt houseChargeCount = 0.obs;
  final RxInt accountablesTotal = 0.obs;
  final RxInt departmentTotals = 0.obs;
  final RxInt takeoutFeeTotal = 0.obs;
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxInt refundTotal = 0.obs;
  final RxDouble percentageTotal = 0.00.obs;
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();

  @override
  Future<void> onInit() async {
    isLoading.value = true;
    dateSelectController.text = DateFormat.yMMMd().format(DateTime.now());

    await getTerminals();
    await getRows();
    isLoading.value = false;

    super.onInit();
  }

  Future<void> getGiftCardSales(String start, String end) async {
    try {
      final Either<ServiceError, List<GiftReport>> getGiftSalesResult = await _reportService.getGiftCardSales(
        start,
        end,
        selectedTerminal.value,
      );

      getGiftSalesResult.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<GiftReport> res) {
          for (final GiftReport giftSaleRow in res) {
            giftSaleCount.value++;
            giftSaleTotal.value += giftSaleRow.original_price;
          }
        },
      );
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getTakeoutFeeTotal(String start, String end) async {
    try {
      final Either<ServiceError, int> takeOutFeeRes = await _reportService.getTakeOutFee(
        startDate: start,
        endDate: end,
        selectedTerminal: selectedTerminal.value,
      );

      takeOutFeeRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (int res) {
          takeoutFeeTotal.value = res;
        },
      );
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getHouseCharges(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(
        start,
        end,
        selectedTerminal.value,
      );

      houseRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> res) {
        for (int i = 0; i < res.length; i++) {
          final List<SaleTender> tenders = res[i].document.saleHeader.tenders;
          for (final SaleTender tender in tenders) {
            if (tender.media == PaymentMediaType.House.index) {
              houseChargeTotal.value += tender.amount!;
              houseChargeCount.value += 1;
            }
          }
        }
      });
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getPaidOut(String start, String end) async {
    try {
      final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(
        start,
        end,
        selectedTerminal.value,
      );
      paidOutRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> res) {
        for (final Sale paidOutRow in res) {
          paidOutCount.value++;
          paidOutTotal.value += paidOutRow.document.saleHeader.total;
        }
      });
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> getTaxTotal(String start, String end) async {
    try {
      final Either<ServiceError, List<TaxReport>> taxRes = await _reportService.getTaxReports(
        start,
        end,
        selectedTerminal.value,
        null,
      );
      taxRes.fold((ServiceError l) {
        throw l.message;
      }, (List<TaxReport> r) {
        taxTotals.value = r;
      });
    } catch (err) {
      _notificationService.error(err.toString());
    }
  }

  Future<void> getMajorData(String start, String end) async {
    try {
      final Either<ServiceError, List<MajorReport>> taxRes = await _reportService.getMajorReport(
        startDate: start,
        endDate: end,
        selectedTerminal: selectedTerminal.value,
      );
      taxRes.fold((ServiceError l) {
        throw l.message;
      }, (List<MajorReport> r) {
        majorRows.value = r;
      });
    } catch (err) {
      _notificationService.error("error getting Major data");
    }
  }

  Future<void> getRows() async {
    isLoading.value = true;
    final String start = Helpers.getReportStartString(dateRangeController);
    final String end = Helpers.getReportEndString(dateRangeController);
    resetRows();
    await getTaxTotal(start, end);
    await getPaidOut(start, end);
    await getHouseCharges(start, end);
    await getGiftCardSales(start, end);
    await getTakeoutFeeTotal(start, end);
    await getMajorData(start, end);
    try {
      final Either<ServiceError, List<SalesByDeptSummaryReport>> getSalesByDeptRes = await _reportService.getSalesByDeptSum(
        start,
        end,
        selectedTerminal.value,
      );
      getSalesByDeptRes.fold((ServiceError l) {
        throw l.message;
      }, (List<SalesByDeptSummaryReport> r) {
        for (final SalesByDeptSummaryReport report in r) {
          count.value += report.item_count!;
          total.value += report.dept_actual_price!;
          percentageTotal.value += report.dept_actual_price!;
          dataRows.add(report);
          printerRows.add(report);
        }
      });
      for (int majorIndex = 0; majorIndex < majorRows.length; majorIndex++) {
        int majorTotal = 0;
        int majorCount = 0;
        reportRows.add(
          CustomTableRow(
            children: <Text>[
              Text(
                majorRows[majorIndex].major_group,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const Text(""),
              const Text(""),
              const Text(""),
            ],
          ),
        );
        for (int dataIndex = 0; dataIndex < dataRows.length; dataIndex++) {
          final bool childOfMajor = await getMajorChildren(
            dataRows[dataIndex].department!,
            majorRows[majorIndex].major_group,
          );
          if (childOfMajor) {
            majorTotal += dataRows[dataIndex].dept_actual_price ?? 0;
            majorCount += dataRows[dataIndex].item_count ?? 0;
            dataRows[dataIndex].major = majorRows[majorIndex].major_group;
            reportRows.add(
              CustomTableRow(
                boxDecoration: const BoxDecoration(color: R2Colors.neutral100),
                onTapped: () async {
                  selectedDepartment.value = dataRows[dataIndex].department!;
                  await getExtendedSalesByDept(
                    start,
                    end,
                    itemRows,
                    selectedDepartment.value,
                    dataRows[dataIndex].dept_actual_price!,
                    dataRows[dataIndex].item_count!,
                  );
                },
                children: <Text>[
                  Text(dataRows[dataIndex].department!),
                  Text(Helpers.formatWholeNumber(dataRows[dataIndex].item_count ?? 0)),
                  Text("\$${Helpers.formatCurrency(dataRows[dataIndex].dept_actual_price ?? 0)}"),
                  const Text(""),
                ],
              ),
            );
          }
        }
        reportRows.add(
          CustomTableRow(
            padding: const EdgeInsetsDirectional.only(bottom: 10),
            children: <Text>[
              Text(
                "${majorRows[majorIndex].major_group} total:",
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              Text(
                Helpers.formatWholeNumber(
                  majorCount,
                ),
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              Text(
                "\$${Helpers.formatCurrency(majorTotal)}",
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const Text(""),
            ],
          ),
        );
        departmentTotals.value += majorTotal;
      }

      accountablesTotal.value = giftSaleTotal.value + paidOutTotal.value + houseChargeTotal.value;
      isLoading.value = false;
    } catch (err) {
      _notificationService.error("error getting data rows");
    }
  }

  Future<bool> getMajorChildren(String department, String major) async {
    try {
      ///case to catch default non existant departments
      if (major == 'No Major Assigned' && (department == 'Gift Cards' || department == 'COMMENT')) {
        return true;
      }
      final QueryResult<Object> getMajorResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           query getDepartment(\$_eq: String) {
              department(where: {title: {_eq: \$_eq}}) {
                title
                updated_at
                updated_by
                document
                department_order
                department
                created_at
                created_by
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "_eq": department,
          },
        ),
      );

      if (getMajorResult.hasException) throw getMajorResult.exception.toString();

      final List<Department> departmentRes =
          (getMajorResult.data!['department'] as List<dynamic>).map((dynamic report) => Department.fromJson(report as Map<String, dynamic>)).toList();

      /// return true if the current major matches the deparment report major
      if (departmentRes.first.document.majorGroup == major) {
        return true;
      } else {
        /// case to handle null and unassigned majors
        /// can probably be cleaned up
        if (departmentRes.first.document.majorGroup == null && major == 'No Major Assigned') {
          return true;
        }
      }
      if (departmentRes.first.document.majorGroup == null && major == 'No Major Assigned') {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> getExtendedSalesByDept(
    String start,
    String end,
    List<ItemsByDepartmentReport> itemRows,
    String selectedDepartment,
    int deptTotal,
    int deptCount,
  ) async {
    itemRows.clear();

    try {
      final QueryResult<Object> getItemsByDeptResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_ITEMS_BY_DEPARTMENT(\$start_date: timestamp, \$selecteddepartment: String, \$end_date: timestamp, \$terminal_index: Int) {
              get_items_by_department(args: {start_date: \$start_date, selecteddepartment: \$selecteddepartment, end_date: \$end_date, terminal_index: \$terminal_index}) {
                    actualpricetotal
                    department
                    gross_price_total
                    items
                    itemscount
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": start,
            "selecteddepartment": selectedDepartment,
            "end_date": end,
            "terminal_index": selectedTerminal.value,
          },
        ),
      );

      if (getItemsByDeptResult.hasException) throw getItemsByDeptResult.exception.toString();

      final List<Report> reports = (getItemsByDeptResult.data!['get_items_by_department'] as List<dynamic>)
          .map((dynamic report) => Report.fromJson(report as Map<String, dynamic>))
          .toList();

      for (final Report report in reports) {
        final ItemsByDepartmentReport itemsByDeptSummaryReport = ItemsByDepartmentReport.itemsByDepartmentReport(report);
        // if (itemsByDeptSummaryReport.dept_actual_price! > 0) {
        itemRows.add(itemsByDeptSummaryReport);
        // }
      }
      await Get.bottomSheet(
        ThinBottomSheet(
          child: ExtendedSalesByDeptDialog(
            itemRows: itemRows,
            total: deptTotal,
            count: deptCount,
          ),
        ),
        isScrollControlled: true,
      );
    } catch (err, stack) {
      _logger.severe("error getting extended sales by department", err, stack);
      _notificationService.error('error getting extended sales by department');
    }
  }

  Future<void> printReport() async {
    final PrintJob printJobType = PrinterInterface.buildSalesByDeptSumReport(
      reports: printerRows,
      majorRows: majorRows,
      count: count.value,
      total: total.value,
      giftCardCount: giftSaleCount.value,
      giftCardSales: giftSaleTotal.value,
      paidOutTotal: paidOutTotal.value,
      paidOutCount: paidOutCount.value,
      taxTotal: taxTotal.value,
      taxList: taxTotals,
      houseChargeCount: houseChargeCount.value,
      houseChargeTotal: houseChargeTotal.value,
      scope: selectedScope.value,
      takeoutFees: takeoutFeeTotal.value,
      reportPeriodStart: DateFormat('M/d/yy hh:mma').format(DateTime.parse(Helpers.getReportStartString(dateRangeController)).toLocal()),
      reportPeriodEnd: DateFormat('M/d/yy hh:mma').format(DateTime.parse(Helpers.getReportEndString(dateRangeController)).toLocal()),
    );
    printJobType.print_job = "";

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();

      _notificationService.success("Print Sales By Department Success");
    } catch (err) {}
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }
          ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) return _notificationService.error(getTerminalsResult.exception.toString());

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    resetRows();

    await getRows();
  }

  void resetRows() {
    dataRows.clear();
    reportRows.clear();
    reportTotalsRow.clear();
    majorRows.clear();
    printerRows.clear();
    percentageTotal.value = 0;
    count.value = 0;
    subTotal.value = 0;
    taxTotal.value = 0;
    total.value = 0;
    giftSaleTotal.value = 0;
    giftSaleCount.value = 0;
    paidOutCount.value = 0;
    paidOutTotal.value = 0;
    houseChargeCount.value = 0;
    houseChargeTotal.value = 0;
    departmentTotals.value = 0;
    takeoutFeeTotal.value = 0;
  }
}
