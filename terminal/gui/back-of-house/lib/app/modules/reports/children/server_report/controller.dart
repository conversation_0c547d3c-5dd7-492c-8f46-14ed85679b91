import 'package:backoffice/app/data/models/emp_cash_discount.dart';
import 'package:backoffice/app/data/models/emp_media_breakdown.dart';
import 'package:backoffice/app/data/models/emp_record.dart';
import 'package:backoffice/app/data/models/emp_sales_by_dept.dart';
import 'package:backoffice/app/data/models/emp_sales_tax.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class ServerReportController extends GetxController {
  ServerReportController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  final Logger _logger = Logger('ServerReportController');

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxList<Employee> employeeList = <Employee>[].obs;
  final RxList<EmployeeRecord> employeeRecordList = <EmployeeRecord>[].obs;
  final RxList<int> empIDList = <int>[].obs;
  final RxList<EmployeeSalesByDepartment> empSalesByDeptList = <EmployeeSalesByDepartment>[].obs;
  final RxList<EmployeeMediaBreakdown> employeeBreakdownList = <EmployeeMediaBreakdown>[].obs;
  final RxList<EmployeeStatistics> employeeStatsList = <EmployeeStatistics>[].obs;
  final RxList<EmployeeTipBreakdown> employeeTipList = <EmployeeTipBreakdown>[].obs;
  final RxList<EmployeeSalesTax> taxRecordList = <EmployeeSalesTax>[].obs;
  final RxList<EmployeeCashDiscount> empDualPriceList = <EmployeeCashDiscount>[].obs;
  final RxString selectedScope = 'All Employees'.obs;
  final Rxn<int> empID = Rxn<int>();
  final DateRangePickerController dateRangeController = DateRangePickerController();
  final TextEditingController dateSelectController = TextEditingController();
  final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

  @override
  Future<void> onInit() async {
    dateRangeController.selectedRange = PickerDateRange(
      now,
      null,
    );
    dateSelectController.text = now.toString();
    await getActiveEmps();
    super.onInit();
  }

  Future<void> getActiveEmps() async {
    try {
      final Either<ServiceError, List<Employee>> empListRes = await _reportService.getActiveEmployees();
      empListRes.fold(
        (ServiceError l) {
          throw l.message;
        },
        (List<Employee> r) {
          employeeList.value = r;
        },
      );
    } catch (e) {
      _notificationService.error("Error getting Active Employees");
    }
  }

  Future<void> printReport() async {
    PrintJob printJobType;
    printJobType = PrinterInterface.buildServerPrintJob(
      scope: selectedScope.value,
      empRecords: employeeRecordList,
      startDate: Helpers.getReportStartString(dateRangeController),
      endDate: Helpers.getReportEndString(dateRangeController),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
               mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
                insert_print_job_one(object: \$printJob) {
                  print_job
                  document
                  created_at
                }
              }
              ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();
    } catch (err) {
      _logger.severe("error printing server report");
      _notificationService.error("error printing server report");
    }
  }

  void clearVariables() {
    empIDList.clear();
    employeeRecordList.clear();
    empDualPriceList.clear();
    taxRecordList.clear();
  }

  String getReportStartString(DateRangePickerController rangeController) {
    return (rangeController.selectedRange?.startDate ?? now).toUtc().toString();
  }

  String getReportEndString(DateRangePickerController rangeController) {
    return (rangeController.selectedRange?.endDate ?? (rangeController.selectedRange?.startDate ?? now).add(const Duration(days: 1)))
        .toUtc()
        .toString();
  }
}
