// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/enums/timecard_sort.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/enums/pay_period.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report_engine.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
// import 'package:pdf/widgets.dart' as pw;

class TimeCardReportController extends GetxController {
  TimeCardReportController();
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final ReportEngineService _reportEngineService = Get.find();
  final BohReportService _reportService = Get.find();
  final Logger _logger = Logger('TimecardReportController');

  final RxMap<String, dynamic> reports = <String, dynamic>{}.obs;
  final RxInt paidOutTotal = 0.obs;
  final Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final List<String> saleFlags = <String>[];
  final RxString selectedScope = "Store".obs;
  final Rxn<int> selectedTemerinal = Rxn<int>();
  final RxBool isLoading = true.obs;
  final RxBool usePayPeriod = true.obs;
  final RxString periodStart = DateTime.now().toString().obs;
  final RxString periodEnd = DateTime.now().toString().obs;
  final RxInt selectedPayPeriod = 0.obs;
  final RxInt selectedEmployee = 0.obs;
  final Rx<TimecardSort> selectedOrder = TimecardSort.LAST_NAME.obs;

  final RxList<Employee> employeeList = <Employee>[].obs;
  List<SystemSettingJsonRecordJobCode> jobCodeList = <SystemSettingJsonRecordJobCode>[];
  int payPeriodType = 0;
  DateTime? currentPayPeriodEndDate;

  final RxList<Timecard> timecards = <Timecard>[].obs;

  RxList<String> timecardErrors = <String>[].obs;

  @override
  Future<void> onInit() async {
    await getPpdType();
    await getEmps();
    await getRows();
    await getPayPeriodDates();
    dateRangeController.value.selectedRange = PickerDateRange(
      DateTime.parse(
        periodStart.value,
      ),
      DateTime.parse(
        periodEnd.value,
      ),
    );

    super.onInit();
  }

  Future<void> getRows() async {
    try {
      isLoading.value = true;
      timecards.clear();
      timecardErrors.clear();
      reports.value = await _reportEngineService.request(<String, dynamic>{
        "reportID": usePayPeriod.value ? 2 : 1,
        "empID": selectedEmployee.value,
        if (usePayPeriod.value) "payPeriod": selectedPayPeriod.value,
        if (!usePayPeriod.value) "startDate": DateTime.parse(Helpers.getReportStartString(dateRangeController.value)).toLocal().toString(),
        if (!usePayPeriod.value) "endDate": DateTime.parse(Helpers.getReportEndString(dateRangeController.value)).toLocal().toString(),
      });
      if (usePayPeriod.value) {
        periodStart.value = reports['startDate'].toString();
        periodEnd.value = reports['endDate'].toString();

        dateRangeController.value.selectedRange = PickerDateRange(
          DateTime.parse(periodStart.value),
          DateTime.parse(periodEnd.value),
        );

        currentPayPeriodEndDate ??= dateRangeController.value.selectedRange?.endDate ?? DateTime.now();
      }
      reports.removeWhere(
        (String key, dynamic value) => int.tryParse(key) == null,
      );

      for (int i = 0; i < reports.keys.length; i++) {
        final Timecard currentTimeCard = Timecard.empty();
        //Set the id of the timecard to the employee id
        currentTimeCard.id = int.parse(reports.keys.elementAt(i));
        //set the employee Record that matches the employee id
        currentTimeCard.employee = employeeList.firstWhere(
          (Employee element) => element.id == int.parse(reports.keys.elementAt(i)),
        );
        //set the punches of the timecard to the punches that match the employee id
        currentTimeCard.timecardPunches = (reports['${currentTimeCard.id}']['punches'] as List<dynamic>)
            .map((dynamic bd) => TimecardPunch.fromJson(bd as Map<String, dynamic>))
            .toList();
        if (usePayPeriod.value) {
          //set the wages of the timecard to the wages that match the employee id
          currentTimeCard.timecardWages = (reports['${currentTimeCard.id}']['wages'] as List<dynamic>)
              .map((dynamic bd) => TimecardWage.fromJson(bd as Map<String, dynamic>))
              .toList();
          currentTimeCard.timecardTotals = TimecardTotals.fromJson(reports['${currentTimeCard.id}']['totals'] as Map<String, dynamic>);
        }
        //add the timecard to the list of timecards
        timecards.add(currentTimeCard);
      }
      //sort timecards based on the selected order
      sortTimecards(selectedOrder.value);
      getErrors();
      final Either<ServiceError, SystemSettingJsonRecord> sysRes = await _reportService.getSystemSettings();

      sysRes.fold((ServiceError l) {
        throw l.message;
      }, (SystemSettingJsonRecord r) {
        jobCodeList = r.document.jobCodes;
      });

      isLoading.value = false;
    } catch (err) {
      _notificationService.error(err.toString());
      isLoading.value = false;
    }
  }

  Future<void> getPpdType() async {
    try {
      final QueryResult<Object> getSystemSettingResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_JSON_RECORD (\$record_key: String) {
              json_record(where: {record_key: {_eq: \$record_key}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
          variables: const <String, dynamic>{"record_key": "systemSetting"},
        ),
      );

      if (getSystemSettingResult.hasException) {
        throw getSystemSettingResult.exception.toString();
      }

      payPeriodType = SystemSettingJsonRecord.fromJson(
        getSystemSettingResult.data!['json_record'][0] as Map<String, dynamic>,
      ).document.ppdType;
    } catch (err, stack) {
      _notificationService.error("Failed to load system settings!");
      _logger.severe("error getting pay period", err, stack);
    }
  }

  Future<void> getEmps() async {
    try {
      final Either<ServiceError, List<Employee>> empList = await _reportService.getEmployees();

      empList.fold((ServiceError l) {
        throw l.message;
      }, (List<Employee> r) {
        employeeList.value = r;
      });
    } catch (e) {
      _notificationService.error("Error getting Employees");
    }
  }

  Future<void> changeSelectedRange(PickerDateRange dateRange) async {
    periodStart.value = dateRange.startDate.toString();
    periodEnd.value = (dateRange.endDate ?? dateRange.startDate!).add(const Duration(days: 1)).toString();
    await getRows();
    usePayPeriod.refresh();
  }

  Future<void> getPayPeriodOnDateSelect(
    PickerDateRange dateRange,
  ) async {
    if (dateRange.startDate!.isAfter(currentPayPeriodEndDate ?? DateTime.now())) {
      dateRangeController.value.selectedRange = PickerDateRange(
        DateTime.parse(
          periodStart.value,
        ),
        DateTime.parse(
          periodEnd.value,
        ),
      );
      return;
    }
    if (dateRange.endDate == null) {
      if (payPeriodType == PayPeriod.MONTHLY.index) {
        final int yearDiff = currentPayPeriodEndDate!.year - dateRange.startDate!.year;
        final int monthDiff = currentPayPeriodEndDate!.month - dateRange.startDate!.month;
        selectedPayPeriod.value = 0 - (monthDiff + (yearDiff * 12));
      } else {
        final Duration difference = currentPayPeriodEndDate!.difference(dateRange.startDate ?? DateTime.now()).abs();
        if (payPeriodType == PayPeriod.WEEKLY.index) {
          selectedPayPeriod.value = 0 - (difference.inDays / 7).floor();
        } else {
          selectedPayPeriod.value = 0 - (difference.inDays / 14).floor();
        }
      }
      await getRows();
      // await getPayPeriodDates();
      return;
    }
    usePayPeriod.refresh();
  }

  Future<void> getPayPeriodDates() async {
    try {
      isLoading.value = true;
      reports.value = await _reportEngineService.request(<String, dynamic>{
        "reportID": 2,
        "empID": selectedEmployee.value,
        "payPeriod": selectedPayPeriod.value,
      });

      periodStart.value = reports['startDate'].toString();
      periodEnd.value = reports['endDate'].toString();

      dateRangeController.value.selectedRange = PickerDateRange(
        DateTime.parse(periodStart.value),
        DateTime.parse(periodEnd.value),
      );

      currentPayPeriodEndDate ??= dateRangeController.value.selectedRange?.endDate ?? DateTime.now();

      reports.removeWhere(
        (String key, dynamic value) => int.tryParse(key) == null,
      );

      isLoading.value = false;
    } catch (err) {
      _notificationService.error("Error Getting Pay Period");
    }
  }

  String getCurrentRangeString() {
    if (dateRangeController.value.selectedRange?.startDate == null) return "";
    final String start = DateFormat('MM/dd/yyyy').format(
      dateRangeController.value.selectedRange!.startDate!,
    );
    if (dateRangeController.value.selectedRange?.endDate != null) {
      return "$start - ${DateFormat('MM/dd/yyyy').format(dateRangeController.value.selectedRange!.endDate!)}";
    } else {
      return start;
    }
  }

  Future<void> printReport() async {
    PrintJob printJobType;
    printJobType = PrinterInterface.printTimeCardAllEmps(
      currentEmployeeList: employeeList,
      endDate: DateFormat('M/d/yy hh:mma').format(
        DateTime.parse(
          Helpers.getReportEndString(dateRangeController.value),
        ).toLocal(),
      ),
      startDate: DateFormat('M/d/yy hh:mma').format(
        DateTime.parse(
          Helpers.getReportStartString(dateRangeController.value),
        ).toLocal(),
      ),
      jobCodesList: jobCodeList,
      reportData: timecards,
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
               mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
                insert_print_job_one(object: \$printJob) {
                  print_job
                  document
                  created_at
                }
              }
              ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe("error printing", err, stack);
      _notificationService.error('error printing');
    }
  }

  void sortTimecards(TimecardSort sort) {
    switch (sort) {
      case TimecardSort.LAST_NAME:
        timecards.sort((Timecard a, Timecard b) => a.employee.document.lastName!.compareTo(b.employee.document.lastName!));
      case TimecardSort.FIRST_NAME:
        timecards.sort((Timecard a, Timecard b) => a.employee.document.firstName!.compareTo(b.employee.document.firstName!));
      case TimecardSort.EMPLOYEE_ID:
        timecards.sort((Timecard a, Timecard b) => a.employee.id!.compareTo(b.employee.id!));
      default:
        break;
    }
  }

  void getErrors() {
    timecardErrors.clear();
    for (final Timecard timecard in timecards) {
      for (final TimecardPunch element in timecard.timecardPunches) {
        final List<String> splitPunchTime = element.total.split(":");
        final int hourValue = int.tryParse(splitPunchTime[0]) ?? 0;
        if (hourValue >= 24) {
          timecardErrors
              .add("${timecard.employee.document.firstName} ${timecard.employee.document.lastName} has a long clocked in time: ${element.total}.");
        }
      }
    }
  }

  // Map<DateTime, List<TimecardPunch>> groupPunchesByCalendarWeek(List<TimecardPunch> punches) {
  //   final Map<DateTime, List<TimecardPunch>> groupedPunches = <DateTime, List<TimecardPunch>>{};

  //   for (final TimecardPunch punch in punches) {
  //     if (punch.punchIn.isEmpty) continue;

  //     // Parse punch-in date
  //     final DateTime punchDate = DateTime.parse(punch.punchIn);

  //     // Find the start of the week (Sunday)
  //     final DateTime weekStart = punchDate.subtract(Duration(days: punchDate.weekday % 7));

  //     // Align to midnight for consistent grouping
  //     final DateTime alignedWeekStart = DateTime(weekStart.year, weekStart.month, weekStart.day);

  //     // Add punch to the corresponding week
  //     if (!groupedPunches.containsKey(alignedWeekStart)) {
  //       groupedPunches[alignedWeekStart] = <TimecardPunch>[];
  //     }
  //     groupedPunches[alignedWeekStart]!.add(punch);
  //   }

  //   return groupedPunches;
  // }

  /// code to group hours based on 5:00am start of days
//   Map<DateTime, List<TimecardPunch>> groupPunchesByCustomWeek(List<TimecardPunch> punches) {
//   final Map<DateTime, List<TimecardPunch>> groupedPunches = {};

//   for (final punch in punches) {
//     if (punch.punchIn.isEmpty) continue;

//     // Parse punch-in date and adjust for 5:00 AM cutoff
//     final punchDateTime = DateTime.parse(punch.punchIn);
//     final adjustedPunchDateTime = punchDateTime.hour < 5
//         ? punchDateTime.subtract(const Duration(days: 1))
//         : punchDateTime;

//     // Find the start of the week (Sunday, adjusted to 5:00 AM cutoff)
//     final weekStart = adjustedPunchDateTime.subtract(Duration(days: adjustedPunchDateTime.weekday % 7));
//     final alignedWeekStart = DateTime(weekStart.year, weekStart.month, weekStart.day, 5);

//     // Add punch to the corresponding week
//     if (!groupedPunches.containsKey(alignedWeekStart)) {
//       groupedPunches[alignedWeekStart] = [];
//     }
//     groupedPunches[alignedWeekStart]!.add(punch);
//   }

//   return groupedPunches;
// }

  /// Calculates the total minutes for a list of punches
  int calculateTotalMinutes(List<TimecardPunch> punches) {
    return punches.fold(0, (int total, TimecardPunch punch) {
      final List<String> split = punch.total.split(":");
      if (split.length < 2) return total;

      final int hours = int.tryParse(split[0]) ?? 0;
      final int minutes = int.tryParse(split[1]) ?? 0;

      return total + (hours * 60) + minutes;
    });
  }

  /// Formats total minutes into "hours:minutes" string
  String formatTotalMinutes(int totalMinutes) {
    final int hours = totalMinutes ~/ 60;
    final int minutes = totalMinutes % 60;
    return "$hours:${minutes.toString().padLeft(2, '0')}";
  }
}
