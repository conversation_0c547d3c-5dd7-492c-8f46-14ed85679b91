// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/reports/children/transaction_history.dart/receipt_view/dialog.dart';
import 'package:backoffice/app/modules/reports/children/transaction_history.dart/receipt_view/dual_price_dialog.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class TransactionHistoryController extends GetxController {
  TransactionHistoryController();

  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();

  final ScrollController transactionListScrollController = ScrollController();
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  final GlobalKey<FormState> formKey = GlobalKey();
  final RxList<dynamic> list = <dynamic>[].obs;
  final List<String> saleFlags = <String>[];
  final Rxn<int> selectedTerminal = Rxn<int>();
  final RxList<Sale> saleRows = <Sale>[].obs;
  final RxBool isLoading = true.obs;
  final RxString selectedScope = "Store".obs;
  Employee? cashier;
  final TextEditingController dateController = TextEditingController();
  final DateRangePickerController dateRangeController = DateRangePickerController();

  @override
  Future<void> onInit() async {
    dateController.text = DateFormat.yMMMd().format(DateTime.now());
    await getData();
    await getTerminals();
    isLoading.value = false;

    super.onInit();
  }

  Future<void> getData() async {
    try {
      saleRows.clear();
      final Either<ServiceError, List<Sale>> transRes = await _reportService.getReportTransHisrtoy(
        Helpers.getReportStartString(dateRangeController),
        Helpers.getReportEndString(dateRangeController),
        selectedTerminal.value,
      );

      transRes.fold((ServiceError l) {
        throw l.message;
      }, (List<Sale> r) {
        for (final Sale sale in r) {
          if (selectedTerminal.value != null) {
            if (sale.document.saleHeader.settleTerminalNumber == selectedTerminal.value!) {
              saleRows.add(sale);
            }
          } else {
            saleRows.add(sale);
          }
        }
      });
      saleRows.sort(
        (Sale a, Sale b) => a.sale_number.compareTo(b.sale_number),
      );
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }

  Future<void> scopeHandler(int? value) async {
    selectedTerminal.value = int.tryParse(value.toString());
    if (selectedTerminal.value == null) {
      selectedScope.value = "Store";
    } else {
      final dynamic selectedTerminalObject = list.firstWhere((dynamic d) => int.parse(d['idx'].toString()) == value);
      final String selectedTerminalDesc = selectedTerminalObject["desc"].toString();
      selectedScope.value = selectedTerminalDesc;
    }
    await getData();
  }

  Future<void> getReceiptView(Sale sale) async {
    final QueryResult<Object> getEmployeePKResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
             query GET_CASHIER(\$employee: uuid!) {
              employee_by_pk(employee: \$employee) {
                 created_at
                  created_by
                  document
                  employee
                  employee_class
                  id
                  is_active
                  password
                  updated_at
                  updated_by
              }
            }
            ''',
        ),
        variables: <String, dynamic>{
          "employee": sale.created_by,
        },
      ),
    );

    if (getEmployeePKResult.hasException) {
      return _notificationService.error(getEmployeePKResult.exception.toString());
    }

    cashier = Employee.fromJson(
      getEmployeePKResult.data!['employee_by_pk'] as Map<String, dynamic>,
    );

    Get.bottomSheet(
      ThinBottomSheet(
        child: !sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)
            ? ReceiptViewDialog(sale, cashier)
            : DualPriceReceiptViewDialog(sale, cashier),
      ),
      isScrollControlled: true,
    );
  }

  Future<void> getTerminals() async {
    final QueryResult<Object> getTerminalsResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query MyQuery(\$_eq: String) {
              json_record(where: {record_key: {_eq: \$_eq}}) {
                document
                record_key
                updated_at
              }
            }

            ''',
        ),
        variables: const <String, dynamic>{"_eq": "systemDevice"},
      ),
    );

    if (getTerminalsResult.hasException) {
      return _notificationService.error(getTerminalsResult.exception.toString());
    }

    list.value = getTerminalsResult.data!["json_record"][0]["document"]["terminal"] as List<dynamic>;
  }

  void getFlags(Sale sale) {
    saleFlags.clear();
    for (final int tenderType in sale.document.saleHeader.saleFlags) {
      final SaleFlags tender = SaleFlags.values[tenderType];
      switch (tender) {
        case SaleFlags.COMPLETED:
          saleFlags.add("Completed");
          break;
        case SaleFlags.NO_SALE:
          saleFlags.add("No Sale");
          break;
        case SaleFlags.SUSPENDED:
          saleFlags.add("Suspended");
          break;
        case SaleFlags.CANCEL:
          saleFlags.add("Cancelled");
          break;
        case SaleFlags.REFUNDED:
          saleFlags.add("Refunded");
          break;
        case SaleFlags.PAID_OUT:
          saleFlags.add("Paid Out");
          break;
        case SaleFlags.GIFT_ISSUE:
          saleFlags.add("Gift Issue");
          break;
        case SaleFlags.GIFT_REDEMPTION:
          saleFlags.add("Gift Redeemed");
          break;
        case SaleFlags.DUAL_PRICING:
          saleFlags.add("Dual Pricing");
          break;
        case SaleFlags.NONREFUNDABLE:
          saleFlags.add("Non-Refundable");
          break;
        case SaleFlags.TAX_FORGIVEN:
          saleFlags.add("Tax forgiven");
          break;
        case SaleFlags.TIP_ADJUSTED:
          saleFlags.add("Tip Adjusted");
          break;
        case SaleFlags.RECEIPT_PRINTED:
          saleFlags.add("Receipt Printed");
          break;
        case SaleFlags.PREP_PRINTED:
          saleFlags.add("Prep Ticket Printed");
          break;
        case SaleFlags.REOPENED:
          saleFlags.add("Reopened");
          break;
        case SaleFlags.VOIDED:
          saleFlags.add("Voided");
          break;
        case SaleFlags.PICK_UP:
          saleFlags.add("Pick Up");
          break;
        case SaleFlags.LOAN_OUT:
          saleFlags.add("Loan Out");
          break;
        case SaleFlags.COMBINED:
          saleFlags.add("Combined");
          break;
        case SaleFlags.QR_PAY:
          saleFlags.add("QR-Pay");
          break;
        case SaleFlags.ONLINE_ORDER:
          saleFlags.add("Online Order");
          break;
      }
    }
  }
}
