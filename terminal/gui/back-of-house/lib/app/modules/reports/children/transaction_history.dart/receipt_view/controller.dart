// ignore_for_file: avoid_dynamic_calls

import 'package:desktop/app/data/models/discount.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class ReceiptViewDialogController extends GetxController {
  ReceiptViewDialogController(
    this.sale,
    this.cashier,
  );

  final GraphqlService _graphqlService = Get.find();
  final Logger _logger = Logger('ReceiptViewController');
  final NotificationService _notificationService = Get.find();

  final Sale sale;
  final Employee? cashier;

  RxBool hasMultipleTenders = false.obs;
  bool multipleTenders = false;
  List<String> receiptHeader = <String>[];
  List<Discount> discountList = <Discount>[];
  List<String> statuses = <String>[];
  final StringBuffer flagsBuffer = StringBuffer();
  List<Tax> taxList = <Tax>[].obs;
  RxBool isLoading = true.obs;
  RxBool dualPriceEnabled = false.obs;
  RxString legacyGiftName = "Legacy Gift".obs;
  bool showCommentRows = false;
  bool showSaleDesc = false;

  @override
  Future<void> onInit() async {
    await getReceiptHeader();
    await getSalesTaxes();
    isLoading.value = false;
    super.onInit();
  }

  int tipTotal() {
    int tipTotal = 0;
    for (final SaleTender element in sale.document.saleHeader.tenders) {
      tipTotal += element.tipAmount ?? 0;
    }
    return tipTotal;
  }

  Future<void> getSalesTaxes() async {
    try {
      final QueryResult<Object> getTaxResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "salesTax"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
          ),
        ),
      );

      if (getTaxResult.hasException) {
        throw getTaxResult.exception.toString();
      }
      final SalesTaxJsonRecord salesTaxRecord = SalesTaxJsonRecord.fromJson(
        (getTaxResult.data!['json_record'] as List<dynamic>)[0] as Map<String, dynamic>,
      );
      taxList = salesTaxRecord.document.taxes;
    } catch (e, stack) {
      _logger.severe('error getting sales tax', e, stack);
      _notificationService.error('error getting sales tax');
    }
  }

  Future<void> printReceipt() async {
    final PrintJob printJobType = PrinterInterface.buildReceipt(
      currentEmployee: cashier!,
      sale: sale,
      taxList: taxList,
      receiptHeader: receiptHeader,
      isMerchantDualPricing: dualPriceEnabled.value,
      currentUser: cashier!,
      legacyGiftName: legacyGiftName.value,
      showCommentRows: showCommentRows,
      showSaleDesc: showSaleDesc,
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        throw printJobResult.exception.toString();
      }

      _notificationService.success("Print Sales Breakdown Success");
    } catch (err, stack) {
      _logger.severe("error printing", err, stack);
      _notificationService.error('error printing');
    }
  }

  Future<void> getReceiptHeader() async {
    try {
      final QueryResult<Object> receiptHeaderResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
          ),
        ),
      );

      if (receiptHeaderResult.hasException) {
        throw receiptHeaderResult.exception.toString();
      }
      final MerchantJsonRecord merchRecord = MerchantJsonRecord.fromJson(
        receiptHeaderResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      receiptHeader = merchRecord.document.receiptHeader;
      dualPriceEnabled.value = merchRecord.document.dualPricing;
      legacyGiftName.value = merchRecord.document.legacyGiftName;
      showCommentRows = merchRecord.document.commentRowsOnReceipt;
      showSaleDesc = merchRecord.document.saleDescOnReceipt;
    } catch (e, stack) {
      _logger.severe('error getting receipt header', e, stack);
      _notificationService.error('error getting receipt header');
    }
  }
}
