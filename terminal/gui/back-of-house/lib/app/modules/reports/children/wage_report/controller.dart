// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:desktop/app/data/enums/pay_period.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/data/services/report_engine.service.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class WageReportController extends GetxController {
  WageReportController();
  final GraphqlService _graphqlService = Get.find();
  final Logger _logger = Logger('WageController');
  final NotificationService _notificationService = Get.find();
  final ReportEngineService _reportEngineService = Get.find();
  final BohReportService _bohReportService = Get.find();
  final ReportService _fohReportService = Get.find();

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();
  GlobalKey<FormState> formKey = GlobalKey();

  RxMap<String, dynamic> reports = <String, dynamic>{}.obs;
  Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;

  RxString periodStart = DateTime.now().toString().obs;
  RxString periodEnd = DateTime.now().toString().obs;

  RxList<Employee> employeeList = <Employee>[].obs;
  List<SystemSettingJsonRecordJobCode> jobCodeList = <SystemSettingJsonRecordJobCode>[];

  bool arrowClicked = false;
  RxBool isLoading = true.obs;

  RxInt selectedPayPeriod = 0.obs;
  RxInt selectedEmployee = 0.obs;

  int payPeriodType = 0;

  late RxMap<String, dynamic> currentDateRange;

  DateTime currentPayPeriodEndDate = DateTime.now().subtract(Duration(days: DateTime.now().weekday));

  final RxList<Timecard> timecards = <Timecard>[].obs;
  final RxList<String> timecardErrors = <String>[].obs;
  final RxList<int> employeeTimecardErrors = <int>[].obs;

  Rx<JsonRecordReports> reportRecord = JsonRecordReports.empty().obs;

  // Used to organized employee tips breakdown by employee id.
  final RxMap<int, EmployeeTipBreakdown> empTips = <int, EmployeeTipBreakdown>{}.obs;

  @override
  Future<void> onInit() async {
    await getPpdType();
    currentPayPeriodEndDate = payPeriodType == PayPeriod.MONTHLY.index
        ? DateTime(
            currentPayPeriodEndDate.month == 12 ? currentPayPeriodEndDate.year + 1 : currentPayPeriodEndDate.year,
            currentPayPeriodEndDate.month == 12 ? 1 : currentPayPeriodEndDate.month + 1,
            0,
          )
        : currentPayPeriodEndDate.add(
            Duration(days: payPeriodType == PayPeriod.WEEKLY.index ? 7 : 14),
          );
    await getEmps();
    await getRows();
    dateRangeController.value.selectedRange = PickerDateRange(
      DateTime.parse(
        periodStart.value,
      ),
      DateTime.parse(
        periodEnd.value,
      ),
    );
    final Either<ServiceError, JsonRecordReports> reportRecordRes = await _bohReportService.getJsonRecordReport();
    reportRecordRes.fold(
      (ServiceError l) {
        throw l.message;
      },
      (JsonRecordReports r) {
        reportRecord.value = r;
      },
    );
    super.onInit();
  }

  Future<void> getRows() async {
    try {
      isLoading.value = true;
      timecards.clear();
      reports.value = await _reportEngineService.request(
        <String, dynamic>{
          "reportID": 2,
          "empID": selectedEmployee.value,
          "payPeriod": selectedPayPeriod.value,
        },
      );

      periodStart.value = reports['startDate'].toString();
      periodEnd.value = reports['endDate'].toString();

      dateRangeController.value.selectedRange = PickerDateRange(
        DateTime.parse(periodStart.value),
        DateTime.parse(periodEnd.value),
      );

      // timecardErrors.value = (reports['errors'] as List<dynamic>).map((dynamic bd) => bd.toString()).toList();

      reports.removeWhere((String key, dynamic value) => int.tryParse(key) == null);

      for (int i = 0; i < reports.keys.length; i++) {
        final Timecard currentTimeCard = Timecard.empty();
        //Set the id of the timecard to the employee id
        currentTimeCard.id = int.parse(reports.keys.elementAt(i));
        //set the employee Record that matches the employee id
        currentTimeCard.employee = employeeList.firstWhere(
          (Employee element) => element.id == int.parse(reports.keys.elementAt(i)),
        );
        //set the punches of the timecard to the punches that match the employee id
        currentTimeCard.timecardPunches = (reports['${currentTimeCard.id}']['punches'] as List<dynamic>)
            .map((dynamic bd) => TimecardPunch.fromJson(bd as Map<String, dynamic>))
            .toList();
        //set the wages of the timecard to the wages that match the employee id
        currentTimeCard.timecardWages = (reports['${currentTimeCard.id}']['wages'] as List<dynamic>)
            .map((dynamic bd) => TimecardWage.fromJson(bd as Map<String, dynamic>))
            .toList();
        currentTimeCard.timecardTotals = TimecardTotals.fromJson(reports['${currentTimeCard.id}']['totals'] as Map<String, dynamic>);
        //add the timecard to the list of timecards
        timecards.add(currentTimeCard);
      }
      getEmployeeErrors();

      timecards.sort((Timecard a, Timecard b) {
        return (a.employee.document.lastName ?? "").compareTo(b.employee.document.lastName ?? "");
      });

      final Either<ServiceError, SystemSettingJsonRecord> docResp = await _bohReportService.getSystemSettings();
      docResp.fold((ServiceError l) {
        throw l.message;
      }, (SystemSettingJsonRecord r) {
        jobCodeList = r.document.jobCodes;
      });

      final ServiceError? err = await _getTipsBreakdown();

      if (err != null) throw err.message;

      isLoading.value = false;
    } catch (err, stack) {
      _logger.severe(err, stack);
      _notificationService.error(err.toString());
      isLoading.value = false;
    }
  }

  Future<void> getEmps() async {
    try {
      final Either<ServiceError, List<Employee>> empList = await _bohReportService.getEmployees();

      empList.fold((ServiceError l) {
        throw l.message;
      }, (List<Employee> r) {
        employeeList.value = r;
      });
    } catch (e) {
      _notificationService.error("Error getting Employees");
    }
  }

  void resetTotalRows() {
    reports.clear();
  }

  Future<void> printReport() async {
    PrintJob printJobType;
    printJobType = PrinterInterface.printWagesAllEmps(
      currentEmployeeList: employeeList,
      endDate: periodEnd.value,
      startDate: periodStart.value,
      jobCodesList: jobCodeList,
      reportData: timecards,
      empTips: empTips,
      reportRecord: reportRecord.value,
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );

      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
               mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
                insert_print_job_one(object: \$printJob) {
                  print_job
                  document
                  created_at
                }
              }
              ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) throw printJobResult.exception.toString();
    } catch (err) {
      _notificationService.error('error printing');
    }
  }

  Future<void> getPayPeriodOnDateSelect(
    PickerDateRange dateRange,
  ) async {
    if (dateRange.startDate!.isAfter(currentPayPeriodEndDate) || arrowClicked) {
      dateRangeController.value.selectedRange = PickerDateRange(
        DateTime.parse(
          periodStart.value,
        ),
        DateTime.parse(
          periodEnd.value,
        ),
      );
      arrowClicked = false;
      return;
    }
    if (dateRange.endDate == null) {
      if (payPeriodType == PayPeriod.MONTHLY.index) {
        final int yearDiff = currentPayPeriodEndDate.year - dateRange.startDate!.year;
        final int monthDiff = currentPayPeriodEndDate.month - dateRange.startDate!.month;
        selectedPayPeriod.value = 0 - (monthDiff + (yearDiff * 12));
      } else {
        final Duration difference = currentPayPeriodEndDate.difference(dateRange.startDate ?? DateTime.now());
        if (payPeriodType == PayPeriod.WEEKLY.index) {
          selectedPayPeriod.value = 0 - (difference.inDays / 7).floor();
        } else {
          selectedPayPeriod.value = 0 - (difference.inDays / 14).floor();
        }
      }

      // await getPayPeriodDates();
      await getRows();
      return;
    }
    selectedPayPeriod.refresh();
  }

  Future<void> getPpdType() async {
    try {
      final QueryResult<Object> getSystemSettingResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_JSON_RECORD (\$record_key: String) {
              json_record(where: {record_key: {_eq: \$record_key}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
          variables: const <String, dynamic>{"record_key": "systemSetting"},
        ),
      );

      if (getSystemSettingResult.hasException) {
        throw getSystemSettingResult.exception.toString();
      }

      payPeriodType = SystemSettingJsonRecord.fromJson(
        getSystemSettingResult.data!['json_record'][0] as Map<String, dynamic>,
      ).document.ppdType;
    } catch (err) {
      _notificationService.error("Failed to load system settings!");
      _logger.severe("error getting Pay Period Type");
    }
  }

  Widget getCurrentRangeString() {
    String res = DateFormat('MM/dd/yyyy').format(
      dateRangeController.value.selectedRange?.startDate ?? DateTime.now(),
    );
    if (dateRangeController.value.selectedRange?.endDate != null) {
      res += " - ${DateFormat('MM/dd/yyyy').format(dateRangeController.value.selectedRange?.endDate ?? DateTime.now())}";

      return Text(res);
    } else {
      return const Text("");
    }
  }

  void getEmployeeErrors() {
    employeeTimecardErrors.clear();
    timecardErrors.clear();
    for (final Timecard timecard in timecards) {
      for (final TimecardPunch element in timecard.timecardPunches) {
        final List<String> splitPunchTime = element.total.split(":");
        final int hourValue = int.tryParse(splitPunchTime[0]) ?? 0;
        if (hourValue >= 24) {
          employeeTimecardErrors.add(timecard.employee.id ?? 0);
          timecardErrors.add(
            "${timecard.employee.document.firstName} ${timecard.employee.document.lastName} has a punch over 24 hours. This will not be included in their wages.",
          );
        }
      }
    }
  }

  Future<ServiceError?> _getTipsBreakdown() async {
    final Either<ServiceError, List<EmployeeTipBreakdown>> empTipsRes =
        await _fohReportService.getServerTipReport(periodStart.value, periodEnd.value, null, null);

    empTipsRes.fold(
      (ServiceError l) {
        return l;
      },
      (List<EmployeeTipBreakdown> records) {
        for (final EmployeeTipBreakdown record in records) {
          if (record.tender_media == PaymentMediaType.Credit.index) {
            empTips[record.emp_id] = record;
          }
        }
      },
    );
    return null;
  }

  String hoursString(String hours) {
    if (reportRecord.value.document.wageDecimalHours) {
      return Helpers.hoursAndMinToDecimal(hours);
    }
    return hours;
  }
}
