// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/global_widgets/timecard_error_button.dart';
import 'package:backoffice/app/modules/reports/children/wage_report/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

BohReportService _reportService = Get.find();

class WageReportDialog extends GetView<WageReportController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: controller.scaffoldKey,
      endDrawer: Drawer(
        width: 400,
        backgroundColor: R2Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: <Widget>[
              const Text(
                "Fliter Options",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, bottom: 10),
                  child: FormWrapper(
                    formKey: controller.formKey,
                    children: <Widget>[
                      MenuGroup(
                        title: "Filters",
                        children: <Widget>[
                          const SizedBox(width: 4),
                          Obx(
                            () => MenuCheckbox(
                              text: "Decimal Hours",
                              value: controller.reportRecord.value.document.wageDecimalHours,
                              onChanged: (bool? val) async {
                                controller.reportRecord.value.document.wageDecimalHours = val ?? true;
                                controller.reportRecord.refresh();
                                await _reportService.updateReportRecord(controller.reportRecord.value);
                                await controller.getRows();
                              },
                            ),
                          ),
                          Obx(
                            () => MenuDropdown<int>(
                              title: "Scope",
                              value: controller.selectedEmployee.value,
                              onChanged: (int? value) async {
                                controller.selectedEmployee.value = value!;
                                await controller.getRows();
                              },
                              items: <DropdownMenuItem<int>>[
                                const DropdownMenuItem<int>(
                                  value: 0,
                                  child: Text("All Employees"),
                                ),
                                ...controller.employeeList.map(
                                  (Employee e) => DropdownMenuItem<int>(
                                    value: e.id,
                                    child: Text(e.employee_full_name!),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Column(
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(15),
                                child: Row(
                                  children: <Widget>[
                                    const Expanded(
                                      child: Text(
                                        "Pay Period",
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    Obx(
                                      () => controller.selectedPayPeriod.value == 0 &&
                                              controller.dateRangeController.value.selectedRange?.endDate != null
                                          ? const Text("Current Pay Period")
                                          : controller.getCurrentRangeString(),
                                    ),
                                  ],
                                ),
                              ),
                              Obx(
                                () => SfDateRangePicker(
                                  backgroundColor: R2Colors.white,
                                  headerStyle: const DateRangePickerHeaderStyle(
                                    backgroundColor: R2Colors.white,
                                  ),
                                  selectionColor: R2Colors.primary500,
                                  rangeSelectionColor: R2Colors.primary300,
                                  startRangeSelectionColor: R2Colors.primary500,
                                  endRangeSelectionColor: R2Colors.primary500,
                                  todayHighlightColor: R2Colors.primary500,
                                  controller: controller.dateRangeController.value,
                                  monthViewSettings: const DateRangePickerMonthViewSettings(
                                    enableSwipeSelection: false,
                                  ),
                                  selectionMode: DateRangePickerSelectionMode.range,
                                  onSelectionChanged: (DateRangePickerSelectionChangedArgs newDate) async {
                                    await controller.getPayPeriodOnDateSelect(newDate.value as PickerDateRange);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              DialogButton(
                buttonType: EDialogButtonType.DESTRUCTIVE,
                buttonText: "Close",
                onTapped: () => controller.scaffoldKey.currentState!.closeEndDrawer(),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.scaffoldKey.currentState!.openEndDrawer(),
        backgroundColor: R2Colors.primary500,
        elevation: 0,
        hoverElevation: 0,
        child: const Icon(
          Icons.menu_open,
        ),
      ),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Header(
          title: "Wage Report",
          leftButton: DialogButton(
            buttonType: EDialogButtonType.BACK,
            buttonText: "Reports",
            onTapped: () {
              Get.back(id: AppRoutes.id);
            },
          ),
          rightButton: Padding(
            padding: const EdgeInsets.only(right: 15.0, top: 15),
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: R2Colors.primary500,
                  width: 2,
                ),
              ),
              child: IntrinsicWidth(
                child: MaterialButton(
                  height: 60,
                  minWidth: 120,
                  color: Colors.white,
                  disabledColor: Colors.grey[200],
                  hoverColor: R2Colors.primary200,
                  elevation: 0,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
                  onPressed: () async {
                    await controller.printReport();
                  },
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Icon(
                        Icons.print,
                        color: R2Colors.primary500,
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      body: Flex(
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Flexible(
            child: SingleChildScrollView(
              clipBehavior: Clip.none,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Flex(
                  direction: Axis.vertical,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Obx(
                        () => Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Text(
                              "Pay Period ${DateFormat.yMd().add_jm().format(DateTime.parse(controller.periodStart.value))} to ${DateFormat.yMd().add_jm().format(DateTime.parse(controller.periodEnd.value))}",
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            if (controller.timecardErrors.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(left: 10),
                                child: TimecardErrorButton(
                                  timecardErrors: controller.timecardErrors,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    Obx(
                      () => Padding(
                        padding: const EdgeInsets.only(
                          top: 10,
                        ),
                        child: controller.isLoading.value
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Lottie.asset(
                                      'lib/assets/lottie/loading-animation.json',
                                      height: 100,
                                    ),
                                  ],
                                ),
                              )
                            : controller.reports.isNotEmpty
                                ? Obx(
                                    () => Column(
                                      children: List<Padding>.generate(controller.timecards.length, (int tcIndex) {
                                        bool showErrorIcon = false;
                                        final int currentEmpId = controller.timecards[tcIndex].id;
                                        if (controller.employeeTimecardErrors.any((int element) => currentEmpId == element)) {
                                          showErrorIcon = true;
                                        }

                                        final TimecardTotals totals = controller.timecards[tcIndex].timecardTotals;
                                        final RxInt totalGrossPay = 0.obs;

                                        final EmployeeTipBreakdown? tips = controller.empTips[currentEmpId];

                                        return Padding(
                                          padding: EdgeInsets.only(top: tcIndex == 0 ? 0 : 20, bottom: 20),
                                          child: Flex(
                                            direction: Axis.vertical,
                                            crossAxisAlignment: CrossAxisAlignment.stretch,
                                            children: <Widget>[
                                              Padding(
                                                padding: const EdgeInsets.only(top: 10, bottom: 10),
                                                child: Flex(
                                                  direction: Axis.horizontal,
                                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                  children: <Flex>[
                                                    Flex(
                                                      direction: Axis.horizontal,
                                                      children: <Widget>[
                                                        Text(
                                                          controller.employeeList
                                                              .firstWhere((Employee element) => element.id == currentEmpId)
                                                              .employee_full_name!,
                                                          style: const TextStyle(
                                                            fontSize: 24,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                        if (showErrorIcon)
                                                          const Padding(
                                                            padding: EdgeInsets.only(left: 10),
                                                            child: FaIcon(
                                                              FontAwesomeIcons.circleExclamation,
                                                              color: R2Colors.red500,
                                                              size: 20,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                    Flex(
                                                      direction: Axis.horizontal,
                                                      children: <Text>[
                                                        Text(
                                                          "Emp ID: $currentEmpId",
                                                          style: const TextStyle(
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              CustomTable(
                                                columnConfig: const <ColumnConfig>[
                                                  ColumnConfig("JobCode"),
                                                  ColumnConfig("Tot Hrs"),
                                                  ColumnConfig("Meal"),
                                                  ColumnConfig("Reg Hrs"),
                                                  ColumnConfig("Reg Rate"),
                                                  ColumnConfig("OT Hrs"),
                                                  ColumnConfig("OT Rate"),
                                                  ColumnConfig("Gross Pay"),
                                                ],
                                                rows: List<CustomTableRow>.generate(controller.timecards[tcIndex].timecardWages.length,
                                                        (int wageIndex) {
                                                      final TimecardWage wage = controller.timecards[tcIndex].timecardWages[wageIndex];
                                                      final String grossPay = Helpers.calculateGrossPay(
                                                        wage.regWages.toString(),
                                                        wage.otWages.toString(),
                                                      );
                                                      totalGrossPay.value += int.parse(grossPay);

                                                      return CustomTableRow(
                                                        padding: const EdgeInsets.only(top: 5, bottom: 5),
                                                        children: <Text>[
                                                          Text(
                                                            controller.jobCodeList
                                                                .firstWhere(
                                                                  (SystemSettingJsonRecordJobCode element) =>
                                                                      element.index == int.parse(wage.jobCode.toString()),
                                                                )
                                                                .title,
                                                          ),
                                                          Text(
                                                            controller.hoursString(Helpers.addTime(wage.regHrs, wage.otHrs)),
                                                          ),
                                                          Text(
                                                            controller.hoursString(wage.mealHrs),
                                                          ),
                                                          Text(
                                                            controller.hoursString(wage.regHrs),
                                                          ),
                                                          Text(
                                                            Helpers.formatCurrency(wage.regRate),
                                                          ),
                                                          Text(
                                                            controller.hoursString(wage.otHrs),
                                                          ),
                                                          Text(
                                                            Helpers.formatCurrency(wage.otRate),
                                                          ),
                                                          Text(
                                                            "\$${Helpers.formatCurrency(int.tryParse(grossPay) ?? 0)}",
                                                          ),
                                                        ],
                                                      );
                                                    }) +
                                                    <CustomTableRow>[
                                                      CustomTableRow(
                                                        divider: true,
                                                        style: CustomRowStyle.total,
                                                        children: <Text>[
                                                          const Text(
                                                            "Totals",
                                                          ),
                                                          Text(
                                                            controller.hoursString(totals.totalHrs),
                                                          ),
                                                          Text(
                                                            controller.hoursString(totals.mealHrs),
                                                          ),
                                                          Text(
                                                            controller.hoursString(totals.regHrs),
                                                          ),
                                                          const Text(
                                                            "-",
                                                          ),
                                                          Text(
                                                            controller.hoursString(totals.otHrs),
                                                          ),
                                                          const Text(
                                                            "-",
                                                          ),
                                                          Text(
                                                            "\$${Helpers.formatCurrency(totalGrossPay.value)}",
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                              ),
                                              if (tips != null) wageReportTips(tips, totalGrossPay.value),
                                            ],
                                          ),
                                        );
                                      }),

                                      // List<Padding>.generate(controller.reports.keys.length, (int index) {
                                      //   final String currentEmpId = controller.reports.keys.elementAt(index);

                                      //   final Map<String, dynamic> currentEmpServiceData = controller.reports[currentEmpId] as Map<String, dynamic>;
                                      //   final dynamic totals = currentEmpServiceData['totals'] as dynamic;
                                      //   final RxInt totalGrossPay = 0.obs;

                                      //   return Padding(
                                      //     padding: EdgeInsets.only(top: index == 0 ? 0 : 20, bottom: 20),
                                      //     child: Flex(
                                      //       direction: Axis.vertical,
                                      //       crossAxisAlignment: CrossAxisAlignment.stretch,
                                      //       children: <Widget>[
                                      //         Padding(
                                      //           padding: const EdgeInsets.only(top: 10, bottom: 10),
                                      //           child: Flex(
                                      //             direction: Axis.horizontal,
                                      //             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      //             children: <Flex>[
                                      //               Flex(
                                      //                 direction: Axis.horizontal,
                                      //                 children: <Text>[
                                      //                   Text(
                                      //                     controller.employeeList
                                      //                         .firstWhere((Employee element) => element.id == int.parse(currentEmpId))
                                      //                         .employee_full_name!,
                                      //                     style: const TextStyle(
                                      //                       fontSize: 24,
                                      //                       fontWeight: FontWeight.w500,
                                      //                     ),
                                      //                   ),
                                      //                 ],
                                      //               ),
                                      //               Flex(
                                      //                 direction: Axis.horizontal,
                                      //                 children: <Text>[
                                      //                   Text(
                                      //                     "Emp ID: $currentEmpId",
                                      //                     style: const TextStyle(
                                      //                       fontWeight: FontWeight.w500,
                                      //                     ),
                                      //                   ),
                                      //                 ],
                                      //               ),
                                      //             ],
                                      //           ),
                                      //         ),
                                      //         CustomTable(
                                      //           columnConfig: const <ColumnConfig>[
                                      //             ColumnConfig("JobCode"),
                                      //             ColumnConfig("Tot Hrs"),
                                      //             ColumnConfig("Meal"),
                                      //             ColumnConfig("Reg Hrs"),
                                      //             ColumnConfig("Reg Rate"),
                                      //             ColumnConfig("OT Hrs"),
                                      //             ColumnConfig("OT Rate"),
                                      //             ColumnConfig("Gross Pay"),
                                      //           ],
                                      //           rows: List<CustomTableRow>.generate((currentEmpServiceData['wages'] as List<dynamic>).length,
                                      //                   (int index) {
                                      //                 final dynamic wage = (currentEmpServiceData['wages'] as List<dynamic>)[index];
                                      //                 final String grossPay = Helpers.calculateGrossPay(
                                      //                   wage['regWages'].toString(),
                                      //                   wage['OTWages'].toString(),
                                      //                 );
                                      //                 totalGrossPay.value += int.parse(grossPay);

                                      //                 return CustomTableRow(
                                      //                   padding: const EdgeInsets.only(top: 5, bottom: 5),
                                      //                   children: <Text>[
                                      //                     Text(
                                      //                       controller.jobCodeList
                                      //                           .firstWhere(
                                      //                             (SystemSettingJsonRecordJobCode element) =>
                                      //                                 element.index == int.parse(wage['jobCode'].toString()),
                                      //                           )
                                      //                           .title,
                                      //                     ),
                                      //                     Text(
                                      //                       Helpers.addTime(wage['regHrs'].toString(), wage['OTHrs'].toString()),
                                      //                     ),
                                      //                     Text(
                                      //                       wage['mealHrs'].toString(),
                                      //                     ),
                                      //                     Text(
                                      //                       wage['regHrs'].toString(),
                                      //                     ),
                                      //                     Text(
                                      //                       Helpers.formatCurrency(int.parse(wage['regRate'].toString())),
                                      //                     ),
                                      //                     Text(
                                      //                       wage['OTHrs'].toString(),
                                      //                     ),
                                      //                     Text(
                                      //                       Helpers.formatCurrency(int.parse(wage['OTRate'].toString())),
                                      //                     ),
                                      //                     Text(
                                      //                       "\$${Helpers.formatCurrency(int.tryParse(grossPay) ?? 0)}",
                                      //                     ),
                                      //                   ],
                                      //                 );
                                      //               }) +
                                      //               <CustomTableRow>[
                                      //                 CustomTableRow(
                                      //                   divider: true,
                                      //                   style: CustomRowStyle.total,
                                      //                   children: <Text>[
                                      //                     const Text(
                                      //                       "Totals",
                                      //                     ),
                                      //                     Text(
                                      //                       (totals as Map<String, dynamic>)['totalHrs'].toString(),
                                      //                     ),
                                      //                     Text(
                                      //                       totals['mealHrs'].toString(),
                                      //                     ),
                                      //                     Text(
                                      //                       totals['regHrs'].toString(),
                                      //                     ),
                                      //                     const Text(
                                      //                       "-",
                                      //                     ),
                                      //                     Text(
                                      //                       totals['OTHrs'].toString(),
                                      //                     ),
                                      //                     const Text(
                                      //                       "-",
                                      //                     ),
                                      //                     Text(
                                      //                       "\$${Helpers.formatCurrency(totalGrossPay.value)}",
                                      //                     ),
                                      //                   ],
                                      //                 ),
                                      //               ],
                                      //         ),
                                      //       ],
                                      //     ),
                                      //   );
                                      // }),
                                    ),
                                  )
                                : const Text(
                                    "No wages to report during this period",
                                    style: TextStyle(fontSize: 20),
                                  ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Widget wageReportTips(EmployeeTipBreakdown tips, int totalGrossPay) {
  return Padding(
    padding: const EdgeInsets.only(top: 10),
    child: ColoredBox(
      color: R2Colors.primary100,
      child: Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Row(
                children: <Widget>[
                  const Text(
                    "Tips:   ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral700,
                    ),
                  ),
                  Text('\$${Helpers.formatCurrency(tips.tip_amount)}'),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: <Widget>[
                  const Text(
                    "Gatuity:   ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral700,
                    ),
                  ),
                  Text('\$${Helpers.formatCurrency(tips.grat_amount)}'),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: <Widget>[
                  const Text(
                    "Grat+Tip:   ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral700,
                    ),
                  ),
                  Text('\$${Helpers.formatCurrency(tips.grat_amount + tips.tip_amount)}'),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: <Widget>[
                  const Text(
                    "w/ Gross:   ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral700,
                    ),
                  ),
                  Text(
                    "\$${Helpers.formatCurrency(tips.grat_amount + tips.tip_amount + totalGrossPay)}",
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
