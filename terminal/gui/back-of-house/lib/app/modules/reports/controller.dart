// ignore_for_file: avoid_dynamic_calls

import 'dart:io';

import 'package:backoffice/app/data/enums/gift_type.dart';
import 'package:backoffice/app/data/models/all_sale_windows.dart';
import 'package:backoffice/app/data/models/cumulative_gift_sales.dart';
import 'package:backoffice/app/data/models/cumulative_media.dart';
import 'package:backoffice/app/data/models/cumulative_sales.dart';
import 'package:backoffice/app/data/models/cumulative_sales_tax.dart';
import 'package:backoffice/app/data/models/cumulative_stats.dart';
import 'package:backoffice/app/data/models/cumulative_tip_grat.dart';
import 'package:backoffice/app/data/models/emp_discounts.dart';
import 'package:backoffice/app/data/models/emp_media_breakdown.dart';
import 'package:backoffice/app/data/models/emp_record.dart';
import 'package:backoffice/app/data/models/emp_sales_by_dept.dart';
import 'package:backoffice/app/data/models/emp_sales_tax.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/gift_report.dart';
import 'package:backoffice/app/data/models/gift_summary_report.dart';
import 'package:backoffice/app/data/models/major_report.dart';
import 'package:backoffice/app/data/models/media_report.dart';
import 'package:backoffice/app/data/models/movers.dart';
import 'package:backoffice/app/data/models/order_type_report.dart';
import 'package:backoffice/app/data/models/sales_by_dept.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/data/services/email.service.dart';
import 'package:backoffice/app/data/services/pdf.service.dart';
import 'package:backoffice/app/data/view_models/hourly_report.dart';
import 'package:backoffice/app/data/view_models/monthly_report.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:backoffice/app/modules/reports/children/paid_out/controller.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/enums/pay_period.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/data/services/report_engine.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:mailer/mailer.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/widgets.dart' hide Text, Widget;
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class ReportsController extends GetxController {
  ReportsController();

  final Logger _logger = Logger('ReportsController');
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final BohReportService _reportService = Get.find();
  final PDFService _pdfService = Get.find();
  final ReportEngineService _reportEngineService = Get.find();
  final ReportService _fohReportService = Get.find();
  final EmailService _emailService = Get.find();

  final RxBool isLoading = false.obs;
  int countTotal = 0;
  int countFailed = 0;

  ScrollController scrollController = ScrollController();

  RxBool expanded = false.obs;
  int? thisTile;
  RxBool hasPermission = false.obs;
  TextEditingController addEmailController = TextEditingController();
  Rx<SystemSettingJsonRecord> sysRecord = SystemSettingJsonRecord.empty().obs;
  Rx<JsonRecordReports> reportRecord = JsonRecordReports.empty().obs;

  RxList<String> taxList = <String>[].obs;
  RxList<Employee> empList = <Employee>[].obs;
  List<SystemSettingJsonRecordJobCode> jobCodeList = <SystemSettingJsonRecordJobCode>[];
  pw.TextStyle smallFont = const pw.TextStyle(fontSize: 8);
  pw.TextStyle smallFontNegative = const pw.TextStyle(fontSize: 8, color: PdfColors.red500);

  ///
  ///
  ///payperiod calander variables
  final Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;
  RxInt selectedPayPeriod = 0.obs;
  int payPeriodType = 0;
  final RxString periodStart = DateTime.now().toString().obs;
  final RxString periodEnd = DateTime.now().toString().obs;
  DateTime? currentPayPeriodEndDate;
  final RxBool usePayPeriod = true.obs;

  @override
  Future<void> onInit() async {
    await getPpdType();
    await getPayPeriodDates();
    dateRangeController.value.selectedRange = PickerDateRange(
      DateTime.parse(
        periodStart.value,
      ),
      DateTime.parse(
        periodEnd.value,
      ),
    );
    await viewReportPermission();

    final Either<ServiceError, List<String>> taxRes = await _reportService.getTaxList();
    taxRes.fold((ServiceError l) {
      _notificationService.error("failed to get Tax Titles");
    }, (List<String> r) {
      taxList.value = r;
    });

    final Either<ServiceError, List<Employee>> empListRes = await _reportService.getEmployees();
    empListRes.fold(
      (ServiceError l) {
        _notificationService.error("Failed to get Employee List");
      },
      (List<Employee> r) {
        r.sort(
          (Employee a, Employee b) => (a.document.lastName ?? "").compareTo(b.document.lastName ?? ""),
        );
        empList.value = r;
      },
    );

    final Either<ServiceError, SystemSettingJsonRecord> sysRes = await _reportService.getSystemSettings();
    sysRes.fold(
      (ServiceError l) {
        throw l.message;
      },
      (SystemSettingJsonRecord r) {
        sysRecord.value = r;
        jobCodeList = r.document.jobCodes;
      },
    );

    final Either<ServiceError, JsonRecordReports> reportRecordRes = await _reportService.getJsonRecordReport();
    reportRecordRes.fold(
      (ServiceError l) {
        throw l.message;
      },
      (JsonRecordReports r) {
        reportRecord.value = r;
      },
    );

    super.onInit();
  }

  String hoursString(String hours) {
    if (reportRecord.value.document.wageDecimalHours) {
      return Helpers.hoursAndMinToDecimal(hours);
    }
    return hours;
  }

  Future<void> getPpdType() async {
    try {
      final QueryResult<Object> getSystemSettingResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_JSON_RECORD (\$record_key: String) {
              json_record(where: {record_key: {_eq: \$record_key}}) {
                record_key
                document
                updated_at
              }
            }
          ''',
          ),
          variables: const <String, dynamic>{"record_key": "systemSetting"},
        ),
      );

      if (getSystemSettingResult.hasException) {
        throw getSystemSettingResult.exception.toString();
      }

      payPeriodType = SystemSettingJsonRecord.fromJson(
        getSystemSettingResult.data!['json_record'][0] as Map<String, dynamic>,
      ).document.ppdType;
    } catch (err, stack) {
      _notificationService.error("Failed to load system settings!");
      _logger.severe("Error getting pay period type", err, stack);
    }
  }

  String getCurrentRangeString() {
    if (dateRangeController.value.selectedRange?.startDate == null) return "";
    final String start = DateFormat('MM/dd/yyyy').format(
      dateRangeController.value.selectedRange!.startDate!,
    );
    if (dateRangeController.value.selectedRange?.endDate != null) {
      return "$start - ${DateFormat('MM/dd/yyyy').format(dateRangeController.value.selectedRange!.endDate!)}";
    } else {
      return start;
    }
  }

  String formatTotalMinutes(int totalMinutes) {
    final int hours = totalMinutes ~/ 60;
    final int minutes = totalMinutes % 60;
    return "$hours:${minutes.toString().padLeft(2, '0')}";
  }

  Future<void> getPayPeriodOnDateSelect(PickerDateRange dateRange) async {
    if (dateRange.startDate!.isAfter(currentPayPeriodEndDate ?? DateTime.now())) {
      dateRangeController.value.selectedRange = PickerDateRange(
        DateTime.parse(
          periodStart.value,
        ),
        DateTime.parse(
          periodEnd.value,
        ),
      );
      return;
    }
    if (dateRange.endDate == null) {
      if (payPeriodType == PayPeriod.MONTHLY.index) {
        final int yearDiff = currentPayPeriodEndDate!.year - dateRange.startDate!.year;
        final int monthDiff = currentPayPeriodEndDate!.month - dateRange.startDate!.month;
        selectedPayPeriod.value = 0 - (monthDiff + (yearDiff * 12));
      } else {
        final Duration difference = currentPayPeriodEndDate!.difference(dateRange.startDate ?? DateTime.now()).abs();
        if (payPeriodType == PayPeriod.WEEKLY.index) {
          selectedPayPeriod.value = 0 - (difference.inDays / 7).floor();
        } else {
          selectedPayPeriod.value = 0 - (difference.inDays / 14).floor();
        }
      }
      await getPayPeriodDates();
      return;
    }
    usePayPeriod.refresh();
  }

  Future<void> getPayPeriodDates() async {
    try {
      isLoading.value = true;
      final Map<String, dynamic> reports = await _reportEngineService.request(<String, dynamic>{
        "reportID": 2,
        "empID": 0,
        "payPeriod": selectedPayPeriod.value,
      });

      periodStart.value = reports['startDate'].toString();
      periodEnd.value = reports['endDate'].toString();

      if (usePayPeriod.value) {
        dateRangeController.value.selectedRange = PickerDateRange(
          DateTime.parse(periodStart.value),
          DateTime.parse(periodEnd.value),
        );
      }

      currentPayPeriodEndDate ??= dateRangeController.value.selectedRange?.endDate ?? DateTime.now();

      isLoading.value = false;
    } catch (err) {
      _notificationService.error("Error Getting Pay Period");
    }
  }

  Future<void> viewReportPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "access",
      "Reports",
      _graphqlService,
    );
    hasPermission.value = view;
  }

  Future<void> permissionDenied() async {
    _notificationService.error("Access denied");
  }

  Future<bool> sendDailyReportsEmail() async {
    final String start = Helpers.getReportStartString(dateRangeController.value);
    final String end = Helpers.getReportEndString(dateRangeController.value);

    final Rxn<int> selectedTerminal = Rxn<int>();

    final List<FileAttachment> pdfFiles = <FileAttachment>[];
    const pw.BoxDecoration divider = pw.BoxDecoration(border: pw.TableBorder(top: pw.BorderSide()));

    final RxInt tipTotal = 0.obs;
    final RxInt giftTotal = 0.obs;
    final RxInt giftCount = 0.obs;
    final RxInt houseTotal = 0.obs;
    final RxInt houseCount = 0.obs;
    final RxInt paidOutTotal = 0.obs;
    final RxInt paidOutCount = 0.obs;
    final RxInt takeoutFees = 0.obs;

    final RxInt taxTotal = 0.obs;
    final RxInt gratTotal = 0.obs;
    final RxList<pw.TableRow> taxRows = <pw.TableRow>[].obs;
    final RxList<Sale> paidOutReports = <Sale>[].obs;

    final Either<ServiceError, List<TaxReport>> taxRes = await _reportService.getTaxReports(start, end, selectedTerminal.value, null);

    taxRes.fold(
      (ServiceError l) {
        throw l.message;
      },
      (List<TaxReport> r) {
        for (final String element in taxList) {
          taxTotal.value += r
                  .firstWhereOrNull(
                    (TaxReport taxOb) => taxOb.description == element,
                  )
                  ?.tax_amount ??
              0;
          taxRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "$element tax:",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(
                    r
                            .firstWhereOrNull(
                              (TaxReport taxOb) => taxOb.description == element,
                            )
                            ?.tax_amount ??
                        0,
                  )}",
                ),
              ],
            ),
          );
        }
      },
    );
    final Either<ServiceError, List<EmployeeTipBreakdown>> tipRes = await _reportService.getTipTotal(start, end, selectedTerminal.value);
    await tipRes.fold((ServiceError l) {
      throw l.message;
    }, (List<EmployeeTipBreakdown> r) async {
      for (final EmployeeTipBreakdown element in r) {
        tipTotal.value += element.tip_amount;
        gratTotal.value += element.grat_amount;
      }
    });

    final Either<ServiceError, int> takeoutFeesRes = await _reportService.getTakeOutFee(
      startDate: start,
      endDate: end,
      selectedTerminal: selectedTerminal.value,
    );
    takeoutFeesRes.fold(
      (ServiceError l) {
        _notificationService.error("Failed to get takeout fees");
      },
      (int r) {
        takeoutFees.value = r;
      },
    );

    final Either<ServiceError, List<GiftReport>> giftRes = await _reportService.getGiftCardSales(start, end, selectedTerminal.value);
    await giftRes.fold((ServiceError l) {
      throw l.message;
    }, (List<GiftReport> r) async {
      for (final GiftReport element in r) {
        giftCount.value++;
        giftTotal.value += element.original_price;
      }
    });
    final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(start, end, selectedTerminal.value);
    await houseRes.fold((ServiceError l) {
      throw l.message;
    }, (List<Sale> r) async {
      for (final Sale element in r) {
        for (final SaleTender tender in element.document.saleHeader.tenders) {
          if (tender.media == PaymentMediaType.House.index) {
            houseTotal.value += tender.amount!;
            houseCount.value++;
          }
        }
      }
    });
    final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(start, end, selectedTerminal.value);
    await paidOutRes.fold((ServiceError l) {
      throw l.message;
    }, (List<Sale> r) async {
      paidOutReports.value = r;

      for (final Sale element in r) {
        paidOutCount.value++;
        paidOutTotal.value += element.document.saleHeader.total;
      }
    });
    countFailed = 0;
    countTotal = 0;
    for (final String reportTitle in reportRecord.value.document.reportList) {
      switch (reportTitle) {
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        case 'Cumulative Sales':
          final RxList<CumulativeSalesTax> cumTaxList = <CumulativeSalesTax>[].obs;
          countTotal = countTotal + 1;

          final RxString windowStartDate = "".obs;
          final RxString windowEndDate = "".obs;
          final RxString windowStartWeek = "".obs;
          final RxString windowEndWeek = "".obs;
          final RxString windowStartMonth = "".obs;
          final RxString windowEndMonth = "".obs;
          final RxString windowStartYear = "".obs;
          final RxString windowEndYear = "".obs;

          final AllSaleWindows allSaleWindow = Helpers.getAllReportWindows(
            selectedDate: dateRangeController.value.selectedRange?.startDate ?? DateTime.now(),
          );

          final List<pw.TableRow> cumDeptRows = <pw.TableRow>[];
          windowStartDate.value = allSaleWindow.startDate!;
          windowEndDate.value = allSaleWindow.endDate!;
          windowStartWeek.value = allSaleWindow.startWeek!;
          windowEndWeek.value = allSaleWindow.endWeek!;
          windowStartMonth.value = allSaleWindow.startMonth!;
          windowEndMonth.value = allSaleWindow.endMonth!;
          windowStartYear.value = allSaleWindow.startYear!;
          windowEndYear.value = allSaleWindow.endYear!;

          final Either<ServiceError, List<CumulativeSalesReport>> deptRes = await _reportService.getCumulativeDept(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
          );

          final Either<ServiceError, List<CumulativeSalesTax>> taxRes = await _reportService.getCumulativeSalesTax(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
          );

          taxRes.fold(
            (ServiceError l) {
              throw l.message;
            },
            (List<CumulativeSalesTax> r) {
              cumTaxList.value = r;
            },
          );
          final RxInt dayCount = 0.obs;
          final RxInt dayTotal = 0.obs;
          final RxInt weekCount = 0.obs;
          final RxInt weekTotal = 0.obs;
          final RxInt monthCount = 0.obs;
          final RxInt monthTotal = 0.obs;
          final RxInt yearCount = 0.obs;
          final RxInt yearTotal = 0.obs;
          deptRes.fold((ServiceError l) {
            throw l.message;
          }, (List<CumulativeSalesReport> r) {
            cumDeptRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Department", style: smallFont),
                  pw.Text("Day Item Count", style: smallFont),
                  pw.Text("Day Total", style: smallFont),
                  pw.Text("Week Count", style: smallFont),
                  pw.Text("Week Total", style: smallFont),
                  pw.Text("Month Count", style: smallFont),
                  pw.Text("Month Total", style: smallFont),
                  pw.Text("Year Count", style: smallFont),
                  pw.Text("Year Total", style: smallFont),
                ],
              ),
            );
            for (int i = 0; i < r.length; i++) {
              dayTotal.value += r[i].day_actual_total;
              dayCount.value += r[i].day_item_count;
              weekTotal.value += r[i].week_actual_total;
              weekCount.value += r[i].week_item_count;
              monthTotal.value += r[i].month_actual_total;
              monthCount.value += r[i].month_item_count;
              yearTotal.value += r[i].year_actual_total;
              yearCount.value += r[i].year_item_count;
              cumDeptRows.add(
                pw.TableRow(
                  decoration: i == 0 ? divider : null,
                  children: <pw.Text>[
                    pw.Text(r[i].department.toUpperCase(), style: smallFont),
                    pw.Text(
                      Helpers.formatWholeNumber(r[i].day_item_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[i].day_actual_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[i].week_item_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[i].week_actual_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[i].month_item_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[i].month_actual_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[i].year_item_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[i].year_actual_total)}",
                      style: smallFont,
                    ),
                  ],
                ),
              );
            }
          });
          final RxInt dayAccountable = 0.obs;
          final RxInt weekAccountable = 0.obs;
          final RxInt monthAccountable = 0.obs;
          final RxInt yearAccountable = 0.obs;

          dayAccountable.value += dayTotal.value;
          weekAccountable.value += weekTotal.value;
          monthAccountable.value += monthTotal.value;
          yearAccountable.value += yearTotal.value;

          cumDeptRows.add(
            pw.TableRow(
              decoration: divider,
              children: <pw.Text>[
                pw.Text("Totals:", style: smallFont),
                pw.Text(
                  Helpers.formatWholeNumber(dayCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(weekCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(monthCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(yearCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearTotal.value)}",
                  style: smallFont,
                ),
              ],
            ),
          );

          for (int t = 0; t < taxList.length; t++) {
            final CumulativeSalesTax currentTax = cumTaxList.firstWhere(
              (CumulativeSalesTax element) => element.description == taxList[t],
              orElse: CumulativeSalesTax.empty,
            );

            dayAccountable.value += currentTax.day_amount;
            weekAccountable.value += currentTax.week_amount;
            monthAccountable.value += currentTax.month_amount;
            yearAccountable.value += currentTax.year_amount;

            cumDeptRows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text(
                    taxList[t],
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(currentTax.day_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(currentTax.week_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(currentTax.month_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(currentTax.year_amount)}",
                    style: smallFont,
                  ),
                ],
              ),
            );
          }

          final Either<ServiceError, List<CumulativeTipAndGrat>> tipRes = await _reportService.getCumulativeTipGrat(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
          );

          tipRes.fold((ServiceError l) {
            throw l.message;
          }, (List<CumulativeTipAndGrat> r) {
            dayAccountable.value += r.first.day_grat_amount;
            weekAccountable.value += r.first.week_grat_amount;
            monthAccountable.value += r.first.month_grat_amount;
            yearAccountable.value += r.first.year_grat_amount;
            dayAccountable.value += r.first.day_tip_amount;
            weekAccountable.value += r.first.week_tip_amount;
            monthAccountable.value += r.first.month_tip_amount;
            yearAccountable.value += r.first.year_tip_amount;
            cumDeptRows.addAll(<pw.TableRow>[
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Gratuity",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.day_grat_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.week_grat_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.month_grat_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.year_grat_amount)}",
                    style: smallFont,
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Tips",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.day_tip_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.week_tip_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.month_tip_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.year_tip_amount)}",
                    style: smallFont,
                  ),
                ],
              ),
            ]);
          });

          final Either<ServiceError, List<CumulativeGiftSales>> giftSaleRes = await _reportService.getCumulativeGiftSales(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
          );

          giftSaleRes.fold((ServiceError l) {
            _notificationService.error('error Getting gift Sales');
          }, (List<CumulativeGiftSales> r) {
            if (r.isEmpty) {
              r = <CumulativeGiftSales>[CumulativeGiftSales.empty()];
            }
            dayAccountable.value = dayAccountable.value - r.first.day_amount;
            weekAccountable.value = weekAccountable.value - r.first.week_amount;
            monthAccountable.value = monthAccountable.value - r.first.month_amount;
            yearAccountable.value = yearAccountable.value - r.first.year_amount;
            cumDeptRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-Gift Card Sales",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.day_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.week_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.month_amount)}",
                    style: smallFont,
                  ),
                  pw.Text(
                    "",
                    style: smallFont,
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(r.first.year_amount)}",
                    style: smallFont,
                  ),
                ],
              ),
            );
          });

          ///
          ///
          ///
          ///

          final RxInt dayTakeoutFees = 0.obs;
          final RxInt weekTakeoutFees = 0.obs;
          final RxInt monthTakeoutFees = 0.obs;
          final RxInt yearTakeoutFees = 0.obs;

          final Either<ServiceError, int> dayTakeOutFeeRes = await _reportService.getTakeOutFee(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            selectedTerminal: selectedTerminal.value,
          );

          dayTakeOutFeeRes.fold(
            (ServiceError l) {
              _notificationService.error(l.message);
              throw l.message;
            },
            (int res) {
              dayTakeoutFees.value = res;
            },
          );

          final Either<ServiceError, int> weekTakeOutFeeRes = await _reportService.getTakeOutFee(
            startDate: windowStartWeek.value,
            endDate: windowEndWeek.value,
            selectedTerminal: selectedTerminal.value,
          );

          weekTakeOutFeeRes.fold(
            (ServiceError l) {
              _notificationService.error(l.message);
              throw l.message;
            },
            (int res) {
              weekTakeoutFees.value = res;
            },
          );

          final Either<ServiceError, int> monthTakeoutFeesRes = await _reportService.getTakeOutFee(
            startDate: windowStartMonth.value,
            endDate: windowEndMonth.value,
            selectedTerminal: selectedTerminal.value,
          );

          monthTakeoutFeesRes.fold(
            (ServiceError l) {
              _notificationService.error(l.message);
              throw l.message;
            },
            (int res) {
              monthTakeoutFees.value = res;
            },
          );

          final Either<ServiceError, int> yearTakeoutFeesRes = await _reportService.getTakeOutFee(
            startDate: windowStartYear.value,
            endDate: windowEndYear.value,
            selectedTerminal: selectedTerminal.value,
          );

          yearTakeoutFeesRes.fold(
            (ServiceError l) {
              _notificationService.error(l.message);
              throw l.message;
            },
            (int res) {
              yearTakeoutFees.value = res;
            },
          );

          dayAccountable.value += dayTakeoutFees.value;
          weekAccountable.value += weekTakeoutFees.value;
          monthAccountable.value += monthTakeoutFees.value;
          yearAccountable.value += yearTakeoutFees.value;

          cumDeptRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Takeout fees",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayTakeoutFees.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekTakeoutFees.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthTakeoutFees.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearTakeoutFees.value)}",
                  style: smallFont,
                ),
              ],
            ),
          );

          ///
          ///
          ///
          ///
          final RxInt dayPaidOut = 0.obs;
          final RxInt weekPaidOut = 0.obs;
          final RxInt monthPaidOut = 0.obs;
          final RxInt yearPaidOut = 0.obs;
          final Either<ServiceError, List<Sale>> dayPaidOutRes = await _fohReportService.getPaidOut(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            terminalIndex: null,
          );
          dayPaidOutRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin day Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                dayPaidOut.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> weekPaidOutRes = await _fohReportService.getPaidOut(
            startDate: windowStartWeek.value,
            endDate: windowEndWeek.value,
            terminalIndex: null,
          );
          weekPaidOutRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin week Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                weekPaidOut.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> monthPaidOutRes = await _fohReportService.getPaidOut(
            startDate: windowStartMonth.value,
            endDate: windowEndMonth.value,
            terminalIndex: null,
          );
          monthPaidOutRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin month Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                monthPaidOut.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> yearPaidOutRes = await _fohReportService.getPaidOut(
            startDate: windowStartYear.value,
            endDate: windowStartYear.value,
            terminalIndex: null,
          );
          yearPaidOutRes.fold(
            (ServiceError l) {
              _notificationService.error("error getting year Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                yearPaidOut.value += sale.document.saleHeader.total;
              }
            },
          );

          cumDeptRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "-Paid Out",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayPaidOut.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekPaidOut.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthPaidOut.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearPaidOut.value)}",
                  style: smallFont,
                ),
              ],
            ),
          );
          dayAccountable.value = dayAccountable.value - dayPaidOut.value;
          weekAccountable.value = weekAccountable.value - weekPaidOut.value;
          monthAccountable.value = monthAccountable.value - monthPaidOut.value;
          yearAccountable.value = yearAccountable.value - yearPaidOut.value;

          final RxInt dayHouseCharges = 0.obs;
          final RxInt weekHouseCharges = 0.obs;
          final RxInt monthHouseCharges = 0.obs;
          final RxInt yearHouseCharges = 0.obs;
          final Either<ServiceError, List<Sale>> dayHouseChargesRes = await _fohReportService.getHouseCharges(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            terminalIndex: null,
          );
          dayHouseChargesRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin day Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                dayHouseCharges.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> weekhouseChargesRes = await _fohReportService.getHouseCharges(
            startDate: windowStartWeek.value,
            endDate: windowEndWeek.value,
            terminalIndex: null,
          );
          weekhouseChargesRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin week Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                weekHouseCharges.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> monthHouseChargesRes = await _fohReportService.getHouseCharges(
            startDate: windowStartMonth.value,
            endDate: windowEndMonth.value,
            terminalIndex: null,
          );
          monthHouseChargesRes.fold(
            (ServiceError l) {
              _notificationService.error("error gettin month Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                monthHouseCharges.value += sale.document.saleHeader.total;
              }
            },
          );

          final Either<ServiceError, List<Sale>> yearHouseChargesRes = await _fohReportService.getHouseCharges(
            startDate: windowStartYear.value,
            endDate: windowEndYear.value,
            terminalIndex: null,
          );
          yearHouseChargesRes.fold(
            (ServiceError l) {
              _notificationService.error("error getting year Paid Out");
            },
            (List<Sale> r) {
              for (final Sale sale in r) {
                yearHouseCharges.value += sale.document.saleHeader.total;
              }
            },
          );
          dayAccountable.value = dayAccountable.value - dayHouseCharges.value;
          weekAccountable.value = weekAccountable.value - weekHouseCharges.value;
          monthAccountable.value = monthAccountable.value - monthHouseCharges.value;
          yearAccountable.value = yearAccountable.value - yearHouseCharges.value;

          cumDeptRows.addAll(<pw.TableRow>[
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "-House Charges",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayHouseCharges.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekHouseCharges.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthHouseCharges.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearHouseCharges.value)}",
                  style: smallFont,
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Total Accountable",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayAccountable.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekAccountable.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthAccountable.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  "",
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearAccountable.value)}",
                  style: smallFont,
                ),
              ],
            ),
          ]);

          final Either<ServiceError, File> pdfDeptRes = await _pdfService.generatePDF(cumDeptRows, "$reportTitle(Department)", start, end);

          pdfDeptRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

          final List<pw.TableRow> cumMediaRows = <pw.TableRow>[];

          final RxInt dayMediaCount = 0.obs;
          final RxInt dayMediaTotal = 0.obs;
          final RxInt weekMediaCount = 0.obs;
          final RxInt weekMediaTotal = 0.obs;
          final RxInt monthMediaCount = 0.obs;
          final RxInt monthMediaTotal = 0.obs;
          final RxInt yearMediaCount = 0.obs;
          final RxInt yearMediaTotal = 0.obs;

          final Either<ServiceError, List<CumulativeMedia>> mediaRes = await _reportService.getCumulativeMedia(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
            selectedTerminal: selectedTerminal.value,
          );

          cumMediaRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Media",
                  style: smallFont,
                ),
                pw.Text(
                  "Day Count",
                  style: smallFont,
                ),
                pw.Text(
                  "Day Total",
                  style: smallFont,
                ),
                pw.Text(
                  "Week Count",
                  style: smallFont,
                ),
                pw.Text(
                  "Week Total",
                  style: smallFont,
                ),
                pw.Text(
                  "Month Count",
                  style: smallFont,
                ),
                pw.Text(
                  "Month Total",
                  style: smallFont,
                ),
                pw.Text(
                  "Year Count",
                  style: smallFont,
                ),
                pw.Text(
                  "Year Total",
                  style: smallFont,
                ),
              ],
            ),
          );

          mediaRes.fold((ServiceError l) {
            _notificationService.error(l.message);
          }, (List<CumulativeMedia> r) {
            for (int mb = 0; mb < r.length; mb++) {
              dayMediaCount.value += r[mb].day_count;
              dayMediaTotal.value += r[mb].day_amount;
              weekMediaCount.value += r[mb].week_count;
              weekMediaTotal.value += r[mb].week_amount;
              monthMediaCount.value += r[mb].month_count;
              monthMediaTotal.value += r[mb].month_amount;
              yearMediaCount.value += r[mb].year_count;
              yearMediaTotal.value += r[mb].year_amount;
              cumMediaRows.add(
                pw.TableRow(
                  decoration: mb == 0 ? divider : null,
                  children: <pw.Text>[
                    pw.Text(
                      r[mb].media,
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[mb].day_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[mb].day_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[mb].week_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[mb].week_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[mb].month_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[mb].month_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r[mb].year_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r[mb].year_amount)}",
                      style: smallFont,
                    ),
                  ],
                ),
              );
            }
          });

          cumMediaRows.add(
            pw.TableRow(
              decoration: divider,
              children: <pw.Text>[
                pw.Text(
                  "Total:",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(dayMediaCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(dayMediaTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(weekMediaCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(weekMediaTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(monthMediaCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(monthMediaTotal.value)}",
                  style: smallFont,
                ),
                pw.Text(
                  Helpers.formatWholeNumber(yearMediaCount.value),
                  style: smallFont,
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(yearMediaTotal.value)}",
                  style: smallFont,
                ),
              ],
            ),
          );

          final Either<ServiceError, File> pdfMediaRes = await _pdfService.generatePDF(
            cumMediaRows,
            "$reportTitle(Media)",
            windowStartDate.value,
            windowEndDate.value,
          );

          pdfMediaRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

          final List<pw.TableRow> cumStatsRows = <pw.TableRow>[];

          final Either<ServiceError, CumulativeStats> statsRes = await _reportService.getCumulativeStats(
            startDate: windowStartDate.value,
            endDate: windowEndDate.value,
            startWeek: windowStartWeek.value,
            endWeek: windowEndWeek.value,
            startMonth: windowStartMonth.value,
            endMonth: windowEndMonth.value,
            startYear: windowStartYear.value,
            endYear: windowEndYear.value,
          );

          statsRes.fold((ServiceError l) {
            throw l.message;
          }, (CumulativeStats r) {
            cumStatsRows.addAll(
              <pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Stat",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Day Count",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Day Total",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Week Count",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Week Total",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Month Count",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Month Total",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Year Count",
                      style: smallFont,
                    ),
                    pw.Text(
                      "Year Total",
                      style: smallFont,
                    ),
                  ],
                ),
                pw.TableRow(
                  decoration: divider,
                  children: <pw.Text>[
                    pw.Text(
                      "Cancelled",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.day_cancel_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.day_cancel_sale_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.week_cancel_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.week_cancel_sale_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.month_cancel_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.month_cancel_sale_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.year_cancel_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.year_cancel_sale_amount)}",
                      style: smallFont,
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "ReFunded",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.day_refunded_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.day_refunded_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.week_refunded_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.week_refunded_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.month_refunded_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.month_refunded_amount)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.year_refunded_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.year_refunded_amount)}",
                      style: smallFont,
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Re-opened",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.day_reopened_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.day_reopened_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.week_reopened_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.week_reopened_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.month_reopened_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.month_reopened_total)}",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.year_reopened_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(r.year_reopened_total)}",
                      style: smallFont,
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Re-opened",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.day_no_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "-",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.week_no_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "-",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.month_no_sale_count),
                      style: smallFont,
                    ),
                    pw.Text(
                      "-",
                      style: smallFont,
                    ),
                    pw.Text(
                      Helpers.formatWholeNumber(r.year_no_sale_count),
                      style: smallFont,
                    ),
                    pw.Text("-"),
                  ],
                ),
              ],
            );
          });

          final Either<ServiceError, File> pdfStatsRes = await _pdfService.generatePDF(
            cumStatsRows,
            "$reportTitle(Stats)",
            windowStartDate.value,
            windowEndDate.value,
          );

          pdfStatsRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        case 'Employee Sales Report':
          final List<pw.TableRow> empSaleReport = <pw.TableRow>[];
          final RxList<Employee> employeeList = <Employee>[].obs;
          final RxList<int> empIDList = <int>[].obs;
          final List<int> saleFlag = SaleFlags.values.map((SaleFlags e) => e.index).toList();
          final List<int> mediaFlags = PaymentMediaType.values.map((PaymentMediaType e) => e.index).toList();

          final RxList<Sale> saleRows = <Sale>[].obs;
          try {
            final Either<ServiceError, List<Employee>> empListRes = await _reportService.getActiveEmployees();
            empListRes.fold(
              (ServiceError l) {
                throw l.message;
              },
              (List<Employee> r) {
                employeeList.value = r;
              },
            );
          } catch (e, stack) {
            _logger.severe("Email Electronic Journal Error", e, stack);
            _notificationService.error("Error getting Active Employees");
          }

          for (final Employee employee in employeeList) {
            empIDList.add(employee.id!);
          }

          try {
            isLoading.value = true;
            saleRows.clear();
            final Either<ServiceError, List<Sale>> transRes = await _fohReportService.getEmployeeSalesFiltered(
              startDate: start,
              endDate: end,
              selectedTerminal: selectedTerminal.value,
              employee_id: empIDList,
              sale_flags: saleFlag,
              mediaFlags: mediaFlags,
            );

            transRes.fold((ServiceError l) {
              throw l.message;
            }, (List<Sale> r) {
              saleRows.value = r;
            });

            isLoading.value = false;
          } catch (e, stack) {
            _logger.severe("Email Electronic Journal Error", e, stack);

            _notificationService.error(e.toString());
          }
          for (final Employee employee in employeeList) {
            bool contain = false;
            for (final Sale sale in saleRows) {
              if (employee.id == sale.document.saleHeader.currentEmployeeNumber) {
                contain = true;
                break;
              }
            }
            if (contain) {
              empSaleReport.addAll(<pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(""),
                    pw.Text(
                      employee.employee_full_name ?? "no name",
                      textScaleFactor: 1.5,
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "Emp ID: ${employee.id}",
                      textScaleFactor: 1.2,
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
              ]);

              empSaleReport.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Sale No.",
                    ),
                    pw.Text(
                      "Competion Time",
                    ),
                    pw.Text(
                      "Total",
                    ),
                    pw.Text(
                      "Tip",
                    ),
                    pw.Text(
                      "Total W/ Tip",
                    ),
                  ],
                ),
              );
              empSaleReport.add(
                pw.TableRow(
                  decoration: divider,
                  children: <pw.Text>[
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                  ],
                ),
              );
              saleRows.sort((Sale a, Sale b) => a.sale_number.compareTo(b.sale_number));
              for (final Sale sale in saleRows) {
                if (sale.document.saleHeader.currentEmployeeNumber == employee.id) {
                  final RxInt tipTotal = 0.obs;
                  for (final SaleTender element in sale.document.saleHeader.tenders) {
                    tipTotal.value += element.tipAmount ?? 0;
                  }
                  empSaleReport.add(
                    pw.TableRow(
                      children: <pw.Text>[
                        pw.Text(
                          "${sale.document.saleHeader.saleNumber}",
                        ),
                        pw.Text(
                          DateFormat('M/d/yy hh:mma').format(sale.end_at!.toLocal()),
                        ),
                        pw.Text(
                          "\$${Helpers.formatCurrency(sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index) ? sale.document.saleHeader.cashTotal : sale.document.saleHeader.total)}",
                        ),
                        pw.Text(
                          "\$${Helpers.formatCurrency(tipTotal.value)}",
                        ),
                        pw.Text(
                          "\$${Helpers.formatCurrency((sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index) ? sale.document.saleHeader.cashTotal : sale.document.saleHeader.total) + tipTotal.value)}",
                        ),
                      ],
                    ),
                  );
                }
              }
              empSaleReport.addAll(<pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
              ]);
            }
          }
          final Either<ServiceError, File> pdfStatsRes = await _pdfService.generatePDF(
            empSaleReport,
            reportTitle,
            start,
            end,
          );

          pdfStatsRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        case 'Sales Breakdown(Hourly)':
          final List<pw.TableRow> hourlyPwRows = <pw.TableRow>[];
          final RxInt transactionTotal = 0.obs;
          final RxInt subTotal = 0.obs;
          final RxInt total = 0.obs;
          final RxInt taxTotal = 0.obs;

          final Either<ServiceError, List<HourlyReport>> hourlyDataRes = await _reportService.getHourlySalesBreakdown(
            start,
            end,
            selectedTerminal.value,
          );
          await hourlyDataRes.fold(
            (ServiceError l) {
              throw "Hourly Sales Report error";
            },
            (List<HourlyReport> r) async {
              hourlyPwRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Time Period",
                      style: const pw.TextStyle(fontSize: 13),
                    ),
                    pw.Text("Transactions"),
                    pw.Text(
                      "Subtotal",
                    ),
                    pw.Text(
                      "Tax Total",
                    ),
                    pw.Text(
                      "Total",
                    ),
                  ],
                ),
              );
              for (final HourlyReport element in r) {
                transactionTotal.value += element.transactions ?? 0;

                total.value += element.hourly_actual_price_total ?? 0;
                taxTotal.value += element.hourly_tax_total ?? 0;
                hourlyPwRows.add(
                  pw.TableRow(
                    decoration: element == r.first ? divider : null,
                    children: <pw.Text>[
                      pw.Text(
                        "${DateFormat('h:mma').format(
                          element.bottom_hour!.toLocal(),
                        )}-${DateFormat('h:mma').format(
                          element.top_hour!.toLocal(),
                        )}",
                        style: const pw.TextStyle(fontSize: 13),
                      ),
                      pw.Text(
                        element.transactions != null ? element.transactions.toString() : 0.toString(),
                      ),
                      pw.Text(
                        element.hourly_base_price_total != null
                            ? "\$${Helpers.formatCurrency(element.hourly_actual_price_total! - element.hourly_tax_total!)}"
                            : "\$${Helpers.formatCurrency(0)}",
                      ),
                      pw.Text(
                        element.hourly_tax_total != null
                            ? "\$${Helpers.formatCurrency(element.hourly_tax_total!)}"
                            : "\$${Helpers.formatCurrency(0)}",
                      ),
                      pw.Text(
                        element.hourly_actual_price_total != null
                            ? "\$${Helpers.formatCurrency(element.hourly_actual_price_total!)}"
                            : "\$${Helpers.formatCurrency(0)}",
                      ),
                    ],
                  ),
                );
              }
            },
          );

          subTotal.value = total.value - taxTotal.value;
          final RxInt tipTotal = 0.obs;
          final RxInt giftTotal = 0.obs;
          final RxInt giftCount = 0.obs;
          final RxInt houseTotal = 0.obs;
          final RxInt houseCount = 0.obs;
          final RxInt paidOutTotal = 0.obs;
          final RxInt paidOutCount = 0.obs;
          final Either<ServiceError, List<EmployeeTipBreakdown>> tipRes = await _reportService.getTipTotal(
            start,
            end,
            selectedTerminal.value,
          );
          await tipRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeTipBreakdown> r) async {
            for (final EmployeeTipBreakdown element in r) {
              tipTotal.value += element.tip_amount;
            }
          });

          final Either<ServiceError, List<GiftReport>> giftRes = await _reportService.getGiftCardSales(
            start,
            end,
            selectedTerminal.value,
          );
          await giftRes.fold((ServiceError l) {
            throw l.message;
          }, (List<GiftReport> r) async {
            for (final GiftReport element in r) {
              giftCount.value++;
              giftTotal.value += element.original_price;
            }
          });
          final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(start, end, selectedTerminal.value);
          await houseRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) async {
            for (final Sale element in r) {
              for (final SaleTender tender in element.document.saleHeader.tenders) {
                if (tender.media == PaymentMediaType.House.index) {
                  houseTotal.value += tender.amount!;
                  houseCount.value++;
                }
              }
            }
          });
          final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(
            start,
            end,
            selectedTerminal.value,
          );
          await paidOutRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) async {
            for (final Sale element in r) {
              paidOutCount.value++;
              paidOutTotal.value += element.document.saleHeader.total;
            }
          });
          hourlyPwRows.addAll(
            <pw.TableRow>[
              pw.TableRow(
                decoration: divider,
                children: <pw.Text>[
                  pw.Text(
                    "Totals",
                    style: const pw.TextStyle(fontSize: 13),
                  ),
                  pw.Text(
                    transactionTotal.value.toString(),
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(subTotal.value)}",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(taxTotal.value)}",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(total.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-Paid Out",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(paidOutCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(paidOutTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-House Charges",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(houseCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(houseTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-Gift Card Sales",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(giftCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(giftTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Total Accountable ",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency((((total.value) - paidOutTotal.value) - houseTotal.value) - giftTotal.value)}",
                  ),
                ],
              ),
            ],
          );
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(hourlyPwRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Sales Breakdown(Monthly)':
          final List<pw.TableRow> monthlyPwRows = <pw.TableRow>[];
          final RxInt transactionTotal = 0.obs;
          final RxInt subTotal = 0.obs;
          final RxInt total = 0.obs;
          final RxInt taxTotal = 0.obs;

          final Either<ServiceError, List<MonthlyReport>> monthlyDataRes = await _reportService.getMonthlySalesBreakdown(
            start,
            end,
            selectedTerminal.value,
          );
          monthlyDataRes.fold((ServiceError l) {
            throw "Error Fetching Monthly Reports";
          }, (List<MonthlyReport> r) {
            monthlyPwRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Time Period",
                    style: const pw.TextStyle(fontSize: 13),
                  ),
                  pw.Text("Transactions"),
                  pw.Text(
                    "Subtotal",
                  ),
                  pw.Text(
                    "Tax Total",
                  ),
                  pw.Text(
                    "Total",
                  ),
                ],
              ),
            );

            for (final MonthlyReport element in r) {
              transactionTotal.value += element.transactions ?? 0;
              total.value += element.daily_actual_price_total ?? 0;
              taxTotal.value += element.daily_tax_total ?? 0;
              monthlyPwRows.add(
                pw.TableRow(
                  decoration: element == r.first ? divider : null,
                  children: <pw.Text>[
                    pw.Text(DateFormat('yMMMd').format(element.bottom_day!)),
                    pw.Text(
                      element.transactions != null ? element.transactions.toString() : 0.toString(),
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.daily_actual_price_total! - element.daily_tax_total!)}",
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.daily_tax_total ?? 0)}",
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.daily_actual_price_total ?? 0)}",
                    ),
                  ],
                ),
              );
            }
          });

          subTotal.value = total.value - taxTotal.value;
          final RxInt tipTotal = 0.obs;
          final RxInt giftTotal = 0.obs;
          final RxInt giftCount = 0.obs;
          final RxInt houseTotal = 0.obs;
          final RxInt houseCount = 0.obs;
          final RxInt paidOutTotal = 0.obs;
          final RxInt paidOutCount = 0.obs;
          final Either<ServiceError, List<EmployeeTipBreakdown>> tipRes = await _reportService.getTipTotal(
            start,
            end,
            selectedTerminal.value,
          );
          await tipRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeTipBreakdown> r) async {
            for (final EmployeeTipBreakdown element in r) {
              tipTotal.value += element.tip_amount;
            }
          });

          final Either<ServiceError, List<GiftReport>> giftRes = await _reportService.getGiftCardSales(
            start,
            end,
            selectedTerminal.value,
          );
          await giftRes.fold((ServiceError l) {
            throw l.message;
          }, (List<GiftReport> r) async {
            for (final GiftReport element in r) {
              giftCount.value++;
              giftTotal.value += element.original_price;
            }
          });
          final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(start, end, selectedTerminal.value);
          await houseRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) async {
            for (final Sale element in r) {
              for (final SaleTender tender in element.document.saleHeader.tenders) {
                if (tender.media == PaymentMediaType.House.index) {
                  houseTotal.value += tender.amount!;
                  houseCount.value++;
                }
              }
            }
          });
          final Either<ServiceError, List<Sale>> paidOutRes = await _reportService.getPaidOut(
            start,
            end,
            selectedTerminal.value,
          );
          await paidOutRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) async {
            for (final Sale element in r) {
              paidOutCount.value++;
              paidOutTotal.value += element.document.saleHeader.total;
            }
          });

          monthlyPwRows.addAll(
            <pw.TableRow>[
              pw.TableRow(
                decoration: divider,
                children: <pw.Text>[
                  pw.Text(
                    "Totals",
                    style: const pw.TextStyle(fontSize: 13),
                  ),
                  pw.Text(
                    transactionTotal.value.toString(),
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(subTotal.value)}",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(taxTotal.value)}",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(total.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-Paid Out",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(paidOutCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(paidOutTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-House Charges",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(houseCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(houseTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "-Gift Card Sales",
                  ),
                  pw.Text(
                    Helpers.formatWholeNumber(giftCount.value),
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(giftTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Total Accountable ",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency((((total.value) - paidOutTotal.value) - houseTotal.value) - giftTotal.value)}",
                  ),
                ],
              ),
            ],
          );

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(monthlyPwRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Sales by Department(Summary)':
          final List<pw.TableRow> salesByDeptSumRows = <pw.TableRow>[];
          final RxInt units = 0.obs;

          final RxInt total = 0.obs;

          final Either<ServiceError, List<SalesByDeptSummaryReport>> summRes = await _reportService.getSalesByDeptSum(
            start,
            end,
            selectedTerminal.value,
          );
          await summRes.fold(
            (ServiceError l) {
              throw "Sales by Dept Summary Error";
            },
            (List<SalesByDeptSummaryReport> r) async {
              salesByDeptSumRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Department",
                      style: const pw.TextStyle(fontSize: 13),
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text(
                      "",
                    ),
                    pw.Text("Units"),
                    pw.Text(
                      "Total",
                    ),
                  ],
                ),
              );
              for (final SalesByDeptSummaryReport element in r) {
                units.value += element.item_count ?? 0;
                total.value += element.dept_actual_price ?? 0;
                salesByDeptSumRows.add(
                  pw.TableRow(
                    decoration: element == r.first ? divider : null,
                    children: <pw.Text>[
                      pw.Text(
                        element.department ?? "ERR Dept",
                        style: const pw.TextStyle(fontSize: 13),
                      ),
                      pw.Text(
                        "",
                      ),
                      pw.Text(
                        "",
                      ),
                      pw.Text((element.item_count ?? 0).toString()),
                      pw.Text(
                        "\$${Helpers.formatCurrency(element.dept_actual_price ?? 0)}",
                      ),
                    ],
                  ),
                );
              }
            },
          );

          final RxInt totalAccountable = 0.obs;
          totalAccountable.value = ((((total.value + takeoutFees.value) - paidOutTotal.value) - houseTotal.value) - giftTotal.value) + taxTotal.value;

          salesByDeptSumRows.addAll(<pw.TableRow>[
            pw.TableRow(
              decoration: divider,
              children: <pw.Text>[
                pw.Text(
                  "Dept Total",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  units.value.toString(),
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(total.value)}",
                ),
              ],
            ),
            ...taxRows,
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Net Total",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  units.value.toString(),
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(total.value + taxTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Takeout fees",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(takeoutFees.value)}",
                ),
              ],
            ),

            // pw.TableRow(
            //   children: <pw.Text>[
            //     pw.Text(
            //       "Tips",
            //       style: const pw.TextStyle(fontSize: 13),
            //     ),
            //     pw.Text(
            //       "",
            //     ),
            //     pw.Text(
            //       "",
            //     ),
            //     pw.Text(""),
            //     pw.Text(
            //       "\$${Helpers.formatCurrency(tipTotal.value)}",
            //     ),
            //   ],
            // ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "-Paid Out",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(paidOutCount.value.toString()),
                pw.Text(
                  "\$${Helpers.formatCurrency(paidOutTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "-House Charges",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(houseCount.value.toString()),
                pw.Text(
                  "\$${Helpers.formatCurrency(houseTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "-GiftCard Sales",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(giftCount.value.toString()),
                pw.Text(
                  "\$${Helpers.formatCurrency(giftTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text(
                  "Total Accountable",
                  style: const pw.TextStyle(fontSize: 13),
                ),
                pw.Text(
                  "",
                ),
                pw.Text(
                  "",
                ),
                pw.Text(""),
                pw.Text(
                  "\$${Helpers.formatCurrency(totalAccountable.value)}",
                ),
              ],
            ),
          ]);
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(salesByDeptSumRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        ///
        ///
        ///
        ///

        case 'Sales by Department(Full)':

          ///
          ///
          ///
          ///
          ///
          ///
          ///
          final RxList<SalesByDeptReport> itemData = <SalesByDeptReport>[].obs;
          final RxList<SalesByDeptReport> deptData = <SalesByDeptReport>[].obs;
          final RxList<pw.TableRow> reportRows = <pw.TableRow>[].obs;

          final RxInt subTotal = 0.obs;
          final RxInt total = 0.obs;
          final RxInt count = 0.obs;
          final RxDouble percentageTotal = 0.00.obs;

          final Either<ServiceError, List<SalesByDeptReport>> salesByRangeRes = await _reportService.getSalesByRange(
            start,
            end,
            <int>[if (selectedTerminal.value != null) selectedTerminal.value!],
          );

          salesByRangeRes.fold((ServiceError l) {
            throw l.message;
          }, (List<SalesByDeptReport> r) {
            for (final SalesByDeptReport report in r) {
              if (report.item_actual_price! >= 0) subTotal.value += report.item_actual_price!;
              itemData.add(report);
            }
          });
          final Either<ServiceError, List<SalesByDeptReport>> salesByDeptRes = await _reportService.getSalesByDeptFull(
            start,
            end,
            selectedTerminal.value,
          );

          salesByDeptRes.fold(
            (ServiceError l) {
              throw l.message;
            },
            (List<SalesByDeptReport> r) {
              for (final SalesByDeptReport report in r) {
                final RxDouble deptPercentage = 0.0.obs;

                for (final SalesByDeptReport item in itemData) {
                  if (item.department_title == report.department_title) {
                    deptPercentage.value = item.percentage! + deptPercentage.value;
                  }
                }
                report.percentage = deptPercentage.value;

                deptData.add(report);
                count.value += report.dept_count!;
                total.value += report.dept_actual_price!;

                percentageTotal.value += report.percentage!;
              }
            },
          );

          itemData.sort(
            (SalesByDeptReport a, SalesByDeptReport b) => (a.department_title ?? "").compareTo(b.department_title ?? ""),
          );

          for (int i = 0; i < deptData.length; i++) {
            for (int j = 0; j < itemData.length; j++) {
              if (itemData[j] == itemData.last) {
                itemData.add(deptData[i]);
                break;
              }
              if (itemData[j].department_title != itemData[j + 1].department_title && itemData[j].department_title == deptData[i].department_title) {
                itemData.insert(j + 1, deptData[i]);
                break;
              } else {
                continue;
              }
            }
          }

          for (int i = 0; i < itemData.length; i++) {
            if (itemData[i] == itemData[0]) {
              itemData.insert(
                0,
                SalesByDeptReport(
                  dept_header: itemData[i].department_title.toString(),
                ),
              );
            }
            if (itemData[i] != itemData[0]) {
              if (itemData[i - 1].dept_actual_price != null && itemData[i - 1].dept_header == null) {
                itemData.insert(
                  i,
                  SalesByDeptReport(
                    dept_header: itemData[i].department_title.toString(),
                  ),
                );
              }
            }
          }
          reportRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Description"),
                pw.Text(""),
                pw.Text(""),
                pw.Text(""),
                pw.Text("Amount"),
                pw.Text("% Sls"),
              ],
            ),
          );

          for (int i = 0; i < itemData.length; i++) {
            if (itemData[i].dept_header != null) {
              reportRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
              );
              reportRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      itemData[i].dept_header.toString(),
                      style: pw.TextStyle(fontWeight: FontWeight.bold),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
              );
            } else {
              reportRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    if (itemData[i].item_description != null)
                      pw.Text(
                        "   ${itemData[i].item_description}",

                        // style: pw.TextCellStyle.indent,
                        // pw.textStyle: pw.TextStyle(color: itemData[i].item_actual_price! < 0 ? R2Colors.red500 : Colors.black),
                      )
                    else
                      pw.Text(
                        "Dept Total:",
                        style: pw.TextStyle(fontWeight: FontWeight.bold),
                      ),
                    pw.Text(""),
                    pw.Text(""),
                    if (itemData[i].item_count == null)
                      pw.Text(
                        itemData[i].dept_count.toString(),
                        // style: pw.TextCellStyle.indent,
                      )
                    else
                      pw.Text(
                        itemData[i].item_count.toString(),
                      ),
                    if (itemData[i].item_actual_price == null)
                      pw.Text(
                        "\$ ${Helpers.formatCurrency(int.parse((itemData[i].dept_actual_price!).toString()))}",
                        // style: pw.TextCellStyle.indent,
                      )
                    else if (itemData[i].item_actual_price! >= 0)
                      pw.Text(
                        "\$ ${Helpers.formatCurrency(int.parse((itemData[i].item_actual_price!).toString()))}",
                      )
                    else
                      pw.Text(
                        "-\$ ${Helpers.formatCurrency(int.parse(itemData[i].item_actual_price!.abs().toString()))}",
                      ),
                    pw.Text(
                      Helpers.formatCurrency(
                        (itemData[i].percentage! * 100).toInt(),
                      ),
                    ),
                  ],
                ),
              );
            }
          }

          final RxInt accountablesTotal = 0.obs;
          accountablesTotal.value = giftTotal.value + paidOutTotal.value + houseTotal.value;

          final List<pw.TableRow> totalSales = <pw.TableRow>[
            pw.TableRow(
              decoration: divider,
              children: <pw.Text>[
                pw.Text("Dept. Totals:"),
                pw.Text(""),
                pw.Text(""),
                pw.Text(count.value.toString()),
                pw.Text(
                  (total.value) >= 0 ? "\$${Helpers.formatCurrency(total.value)}" : "-\$${Helpers.formatCurrency(total.value.abs())}",
                ),
                pw.Text(""),
              ],
            ),
            ...taxRows,
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Net Total:"),
                pw.Text(""),
                pw.Text(""),
                pw.Text(count.value.toString()),
                pw.Text(
                  total.value + taxTotal.value >= 0
                      ? "\$${Helpers.formatCurrency(total.value + taxTotal.value)}"
                      : "-\$${Helpers.formatCurrency((total.value + taxTotal.value).abs())}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Takeout fees:"),
                pw.Text(""),
                pw.Text(""),
                pw.Text(""),
                pw.Text(
                  "\$${Helpers.formatCurrency(takeoutFees.value)}",
                ),
              ],
            ),
            // pw.TableRow(
            //   children: <pw.Text>[
            //     pw.Text('Tips:'),
            //     pw.Text(""),
            //     pw.Text(""),
            //     pw.Text(""),
            //     pw.Text(
            //       "\$${Helpers.formatCurrency(tipTotal.value)}",
            //     ),
            //   ],
            // ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text('-Paid Out:'),
                pw.Text(""),
                pw.Text(""),
                pw.Text(
                  paidOutCount.value.toString(),
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(paidOutTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text('-House Charges:'),
                pw.Text(""),
                pw.Text(""),
                pw.Text(
                  houseCount.value.toString(),
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(houseTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              children: <pw.Text>[
                pw.Text('-Gift Card Sales:'),
                pw.Text(""),
                pw.Text(""),
                pw.Text(
                  giftCount.value.toString(),
                ),
                pw.Text(
                  "\$${Helpers.formatCurrency(giftTotal.value)}",
                ),
              ],
            ),
            pw.TableRow(
              // style: CustomRowStyle.total,
              children: <pw.Text>[
                pw.Text("Total Accountable:"),
                pw.Text(""),
                pw.Text(""),
                pw.Text(count.value.toString()),
                pw.Text(
                  (((total.value + taxTotal.value) + takeoutFees.value) - accountablesTotal.value) >= 0
                      ? "\$${Helpers.formatCurrency((total.value + taxTotal.value + takeoutFees.value) - accountablesTotal.value)}"
                      : "-\$${Helpers.formatCurrency(((total.value + taxTotal.value + takeoutFees.value) - accountablesTotal.value).abs())}",
                ),
              ],
            ),
          ];
          reportRows.addAll(totalSales);

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(reportRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });
        case 'Media Breakdown':
          final RxInt netTotal = 0.obs;
          final RxInt count = 0.obs;
          final RxInt tips = 0.obs;
          final RxInt totalWithoutTips = 0.obs;
          final RxInt refundTotal = 0.obs;
          final RxInt amountTotal = 0.obs;
          final RxInt ccTips = 0.obs;
          final RxInt cashTotal = 0.obs;

          final List<pw.TableRow> tableRows = <pw.TableRow>[];

          final Either<ServiceError, List<MediaReport>> mediaRes = await _reportService.getMediaBreakdown(
            start,
            end,
            selectedTerminal.value,
          );
          //
          tableRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Media"),
                pw.Text("Count"),
                pw.Text("Total"),
                pw.Text("Tips"),
                pw.Text("Total with Tips"),
              ],
            ),
          );
          mediaRes.fold((ServiceError l) {
            throw l.message;
          }, (List<MediaReport> r) {
            for (final MediaReport report in r) {
              netTotal.value += report.net_total;
              count.value += report.count;
              tips.value += report.tips;
              totalWithoutTips.value += report.amount;
              refundTotal.value += report.refunded_sum;
              amountTotal.value += report.amount + report.tips;

              if (report.media == '0') {
                cashTotal.value = report.amount;
              }
              if (report.media == '2') {
                ccTips.value = report.tips;
              }

              final pw.TableRow newEntry = pw.TableRow(
                decoration: report == r.first ? divider : null,
                children: <pw.Text>[
                  pw.Text(
                    Helpers.mediaTypeAsString(
                      PaymentMediaType.values[int.parse(report.media)],
                    ),
                  ),
                  pw.Text(report.count.toString()),
                  pw.Text("\$ ${Helpers.formatCurrency(report.amount)}"),
                  pw.Text("\$ ${Helpers.formatCurrency(report.tips)}"),
                  pw.Text(
                    report.amount >= 0
                        ? "\$ ${Helpers.formatCurrency(report.amount + report.tips)}"
                        : "-\$${Helpers.formatCurrency((report.amount + report.tips).abs())}",
                  ),
                ],
              );
              tableRows.add(newEntry);
            }

            tableRows.addAll(<pw.TableRow>[
              pw.TableRow(
                decoration: divider,
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Text('Total:'),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Text(Helpers.formatWholeNumber(count.value)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Text(
                      "\$ ${Helpers.formatCurrency(totalWithoutTips.value)}",
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Text("\$ ${Helpers.formatCurrency(tips.value)}"),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 10),
                    child: pw.Text(
                      amountTotal.value >= 0
                          ? "\$${Helpers.formatCurrency(amountTotal.value)}"
                          : "-\$${Helpers.formatCurrency(amountTotal.value.abs())}",
                    ),
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    'Cash total:',
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(cashTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    '-Tips:',
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(ccTips.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    '-Paid Out:',
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "",
                  ),
                  pw.Text(
                    "\$${Helpers.formatCurrency(paidOutTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                // style: CustomRowStyle.total,
                children: <pw.Text>[
                  pw.Text('Adjusted Cash:'),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    (cashTotal.value - tipTotal.value) >= 0
                        ? "\$${Helpers.formatCurrency((cashTotal.value - tipTotal.value) - paidOutTotal.value)}"
                        : "-\$${Helpers.formatCurrency((cashTotal.value.abs() - tipTotal.value) - paidOutTotal.value)}",
                  ),
                ],
              ),
            ]);
          });
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Paid Out':
          final List<pw.TableRow> tableRows = <pw.TableRow>[];
          final Map<String, PaidOutGroup> paidOutGroups = <String, PaidOutGroup>{};

          tableRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Reason Code"),
                pw.Text("Amount"),
              ],
            ),
          );

          ///
          ///
          ///do logic here for paid out group
          for (int i = 0; i < paidOutReports.length; i++) {
            final Sale sale = paidOutReports[i];
            final String reasonCode = sale.document.saleHeader.saleDescription ?? "N/A";
            final int saleTotal = sale.document.saleHeader.total;
            // Create or update the group
            paidOutGroups.putIfAbsent(
              reasonCode,
              () => PaidOutGroup(
                reasonCode: reasonCode,
                sales: <Sale>[],
              ),
            );

            paidOutGroups[reasonCode]!.sales.add(sale);
            paidOutGroups[reasonCode]!.total += saleTotal;
          }

          // If you need to convert to a list of PaidOutGroup
          final List<PaidOutGroup> localPaidOutGroups = paidOutGroups.values.toList();

          ///
          ///
          for (final PaidOutGroup element in localPaidOutGroups) {
            tableRows.add(
              pw.TableRow(
                decoration: element == localPaidOutGroups.first ? divider : null,
                children: <pw.Text>[
                  pw.Text(element.reasonCode),
                  pw.Text(
                    "\$${Helpers.formatCurrency(element.total)}",
                  ),
                ],
              ),
            );
          }

          tableRows.add(
            pw.TableRow(
              decoration: divider,
              children: <pw.Text>[
                pw.Text("Total"),
                pw.Text("\$${Helpers.formatCurrency(paidOutTotal.value)}"),
              ],
            ),
          );
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });
        case 'Gift Summary':
          final List<pw.TableRow> tableRows = <pw.TableRow>[];

          final RxInt totalIssued = 0.obs;
          final RxInt totalRedeemed = 0.obs;
          final RxList<GiftSummaryReport> giftList = <GiftSummaryReport>[].obs;

          final Either<ServiceError, List<Sale>> giftRes = await _reportService.getGiftReport(start, end, selectedTerminal.value);
          giftRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) {
            r.sort(
              (Sale a, Sale b) => a.sale_number.compareTo(b.sale_number),
            );
            for (final Sale element in r) {
              // set gift issue saleRows to list
              if (element.document.saleHeader.saleFlags.contains(SaleFlags.GIFT_ISSUE.index)) {
                for (final SaleRow saleRow in element.document.saleRows) {
                  if (saleRow.flags.contains(SaleRowFlags.GIFT.index)) {
                    totalIssued.value += saleRow.actualPrice;
                    giftList.add(
                      GiftSummaryReport(
                        saleNumber: element.sale_number,
                        amount: saleRow.actualPrice,
                        description: Helpers.removeGiftPrice(saleRow.receiptDescription),
                        giftType: GiftType.ISSUE.index,
                      ),
                    );
                  }
                }
              }

              if (element.document.saleHeader.saleFlags.contains(SaleFlags.GIFT_REDEMPTION.index)) {
                for (final SaleTender tender in element.document.saleHeader.tenders) {
                  if (tender.media == PaymentMediaType.Gift.index) {
                    final String giftCardNumber = tender.giftTransactionData!.identification.cardNumber.substring(
                      tender.giftTransactionData!.identification.cardNumber.length - 4,
                    );
                    totalRedeemed.value += tender.amount ?? 0;
                    giftList.add(
                      GiftSummaryReport(
                        amount: tender.amount ?? 0,
                        saleNumber: element.sale_number,
                        description: "Gift Card ****$giftCardNumber",
                        giftType: GiftType.REDEEM.index,
                      ),
                    );
                  }
                }
              }
            }
          });
          tableRows.add(
            pw.TableRow(
              children: <pw.Widget>[
                pw.Text("Sale #"),
                pw.Text("Type"),
                pw.Text("Card"),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Text("Issue Amount"),
                ),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Text("Redeem Amount"),
                ),
              ],
            ),
          );

          for (final GiftSummaryReport row in giftList) {
            tableRows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text(row.saleNumber.toString()),
                  pw.Text(GiftType.values[row.giftType].friendlyString),
                  pw.Text(row.description),
                  pw.Align(
                    alignment: pw.Alignment.centerRight,
                    child: pw.Text(
                      row.giftType == GiftType.ISSUE.index ? "\$${Helpers.formatCurrency(row.amount)}" : "",
                    ),
                  ),
                  pw.Align(
                    alignment: pw.Alignment.centerRight,
                    child: pw.Text(
                      row.giftType == GiftType.REDEEM.index ? "\$${Helpers.formatCurrency(row.amount)}" : "",
                    ),
                  ),
                ],
              ),
            );
          }

          tableRows.addAll(<pw.TableRow>[
            pw.TableRow(
              decoration: divider,
              children: <pw.Widget>[
                pw.Text("Total Issued:"),
                pw.Text(""),
                pw.Text(""),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Text("\$${Helpers.formatCurrency(totalIssued.value)}"),
                ),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Text(
                    "\$${Helpers.formatCurrency(totalRedeemed.value)}",
                  ),
                ),
              ],
            ),
          ]);

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Movers':
          final List<pw.TableRow> tableRows = <pw.TableRow>[
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Item"),
                pw.Text("Qty"),
                pw.Text("Amount"),
                pw.Text("Last Sold"),
              ],
            ),
          ];

          final Either<ServiceError, List<Movers>> giftRes = await _reportService.getMovers(
            start,
            end,
            selectedTerminal.value,
            'desc',
          );
          giftRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Movers> r) {
            for (final Movers element in r) {
              tableRows.add(
                pw.TableRow(
                  decoration: element == r.first ? divider : null,
                  children: <pw.Text>[
                    pw.Text(element.long_desc),
                    pw.Text(Helpers.formatWholeNumber(element.qty)),
                    pw.Text("\$${Helpers.formatCurrency(element.amount)}"),
                    pw.Text(
                      DateFormat.yMMMd().add_jm().format(element.end_at!),
                    ),
                  ],
                ),
              );
            }
          });

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });
        case 'Time Card':
          try {
            final List<pw.TableRow> pdfContent = <pw.TableRow>[];
            List<Employee> empList = <Employee>[];
            final List<Timecard> timecardList = <Timecard>[];

            // Fetch timecard data
            final Map<String, dynamic> timcardRes = await _reportEngineService.request(<String, dynamic>{
              "reportID": usePayPeriod.value ? 2 : 1,
              "empID": 0,
              if (usePayPeriod.value) "payPeriod": selectedPayPeriod.value,
              if (!usePayPeriod.value) "startDate": DateTime.parse(start).toLocal().toString(),
              if (!usePayPeriod.value) "endDate": DateTime.parse(end).toLocal().toString(),
            });
            timcardRes.removeWhere((String key, dynamic value) => int.tryParse(key) == null);

            final Either<ServiceError, List<Employee>> empRes = await _reportService.getAllEmployees();
            empRes.fold(
              (ServiceError l) => throw l.message,
              (List<Employee> r) => empList = r,
            );

            // Build timecard data
            for (final String empId in timcardRes.keys) {
              final Timecard currentTimeCard = Timecard.empty();
              currentTimeCard.id = int.parse(empId);
              currentTimeCard.employee = empList.firstWhere((Employee element) => element.id == int.parse(empId));
              currentTimeCard.timecardPunches =
                  (timcardRes[empId]['punches'] as List<dynamic>).map((dynamic bd) => TimecardPunch.fromJson(bd as Map<String, dynamic>)).toList();
              if (usePayPeriod.value) {
                currentTimeCard.timecardWages =
                    (timcardRes[empId]['wages'] as List<dynamic>).map((dynamic bd) => TimecardWage.fromJson(bd as Map<String, dynamic>)).toList();
                currentTimeCard.timecardTotals = TimecardTotals.fromJson(timcardRes[empId]['totals'] as Map<String, dynamic>);
              }
              timecardList.add(currentTimeCard);
            }

            // Build PDF content
            for (final Timecard timecard in timecardList) {
              final String empName = timecard.employee.employee_full_name ?? "Unknown";
              final int empId = timecard.id;
              final Map<DateTime, List<TimecardPunch>> weeklyGroupedPunches = Helpers.groupPunchesByCalendarWeek(timecard.timecardPunches);

              int overallTotalMinutes = 0;

              // Employee Header
              pdfContent.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Padding(
                      padding: const pw.EdgeInsets.symmetric(vertical: 10),
                      child: pw.Text(
                        empName,
                        style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.symmetric(vertical: 10),
                      child: pw.Text(
                        "Emp ID: $empId",
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ),
                    pw.Text(""), // Empty cells for alignment
                    pw.Text(""),
                  ],
                ),
              );

              // Weekly Data
              int weekIndex = 1;
              for (final DateTime weekStart in weeklyGroupedPunches.keys) {
                final List<TimecardPunch> punches = weeklyGroupedPunches[weekStart]!;
                final int weeklyTotalMinutes = Helpers.calculateTotalMinutes(punches);

                // Weekly Header
                pdfContent.add(
                  pw.TableRow(
                    children: <pw.Widget>[
                      pw.Padding(
                        padding: const pw.EdgeInsets.only(top: 5),
                        child: pw.Text(
                          "Week $weekIndex",
                          style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
                        ),
                      ),
                      pw.Text(""),
                      pw.Text(""),
                      pw.Text(""),
                    ],
                  ),
                );

                // Table Header
                pdfContent.add(
                  pw.TableRow(
                    children: <pw.Widget>[
                      pw.Text(
                        "In Date/Time",
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        "Job Code",
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        "Out Date/Time",
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(
                        "Hours",
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ],
                  ),
                );

                // Punch Rows
                for (final TimecardPunch punch in punches) {
                  final String jobCodeTitle =
                      jobCodeList.firstWhereOrNull((SystemSettingJsonRecordJobCode jc) => jc.index == punch.jobCode)?.title ?? "N/A";
                  final String punchIn = punch.punchIn.isNotEmpty ? DateFormat.yMd().add_jm().format(DateTime.parse(punch.punchIn)) : "Missing";
                  final String punchOut = punch.punchOut.isNotEmpty ? DateFormat.yMd().add_jm().format(DateTime.parse(punch.punchOut)) : "Missing";

                  pdfContent.add(
                    pw.TableRow(
                      children: <pw.Widget>[
                        pw.Text(punchIn),
                        pw.Text(jobCodeTitle),
                        pw.Text(punchOut),
                        pw.Text(punch.total.isEmpty ? "N/A" : punch.total),
                      ],
                    ),
                  );
                }

                // Weekly Total
                pdfContent.add(
                  pw.TableRow(
                    children: <pw.Widget>[
                      pw.Text(
                        "Weekly Total:",
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                      pw.Text(""),
                      pw.Text(""),
                      pw.Text(
                        formatTotalMinutes(weeklyTotalMinutes),
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                      ),
                    ],
                  ),
                );

                overallTotalMinutes += weeklyTotalMinutes;
                weekIndex++;
              }

              // Overall Total
              pdfContent.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text(
                      "Overall Total:",
                      style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      formatTotalMinutes(overallTotalMinutes),
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                  ],
                ),
              );

              // Divider for separation between employees
              pdfContent.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
              );
            }

            // Generate PDF
            final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(
              pdfContent,
              reportTitle,
              start,
              end,
            );

            pdfRes.fold(
              (ServiceError l) => throw l.message,
              (File r) => pdfFiles.add(FileAttachment(r)),
            );
          } catch (e) {
            countFailed++;
            _notificationService.error(e.toString());
          }

        ///
        case 'Wage Report':
          try {
            final List<List<pw.TableRow>> table = <List<pw.TableRow>>[];
            // Key is Employee id as String because that's what's returned from the report enigine
            final RxMap<String, dynamic> reports = <String, dynamic>{}.obs;
            final RxMap<int, EmployeeTipBreakdown> empTips = <int, EmployeeTipBreakdown>{}.obs;
            final List<pw.TableRow> allRows = <pw.TableRow>[];
            // Employee ids sorted by employee last name
            final List<int> sortedReportKeys = <int>[];

            reports.value = await _reportEngineService.request(
              <String, dynamic>{
                "reportID": 2,
                "empID": 0,
                "payPeriod": selectedPayPeriod.value,
              },
            );

            final String wageStart = reports['startDate'].toString();
            final String wageEnd = reports['endDate'].toString();

            reports.removeWhere(
              (String key, dynamic value) => int.tryParse(key) == null,
            );

            final Either<ServiceError, List<EmployeeTipBreakdown>> empTipsRes =
                await _fohReportService.getServerTipReport(periodStart.value, periodEnd.value, null, null);

            empTipsRes.fold(
              (ServiceError l) {
                throw l;
              },
              (List<EmployeeTipBreakdown> records) {
                for (final EmployeeTipBreakdown record in records) {
                  if (record.tender_media == PaymentMediaType.Credit.index) {
                    empTips[record.emp_id] = record;
                  }
                }
              },
            );

            // empList is sorted by employee last name
            for (final Employee emp in empList) {
              if (reports[emp.id.toString()] != null && emp.id != null) {
                sortedReportKeys.add(emp.id!);
              }
            }

            for (final int id in sortedReportKeys) {
              final List<pw.TableRow> tableRows = <pw.TableRow>[];
              final RxInt totalGrossPay = 0.obs;
              final String currentEmpId = id.toString();
              final Map<String, dynamic> currentEmpServiceData = reports[currentEmpId] as Map<String, dynamic>;
              final dynamic totals = currentEmpServiceData['totals'] as dynamic;
              // organized employee tips breakdown by employee id
              final EmployeeTipBreakdown? tips = empTips[id];

              tableRows.add(
                pw.TableRow(
                  children: <pw.Padding>[
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(
                        top: 6,
                      ),
                      child: pw.Text(
                        empList
                                .firstWhere(
                                  (Employee element) => element.id == int.parse(currentEmpId),
                                )
                                .employee_full_name ??
                            "",
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(top: 6, bottom: 2),
                      child: pw.Text(currentEmpId),
                    ),
                  ],
                ),
              );
              tableRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("JobCode"),
                    pw.Text("Tot Hours"),
                    pw.Text("Meal"),
                    pw.Text("Reg Hours"),
                    pw.Text("Reg Rate"),
                    pw.Text("OT Hours"),
                    pw.Text("OT Rate"),
                    pw.Text("Gross Pay"),
                  ],
                ),
              );
              for (int j = 0; j < (currentEmpServiceData['wages'] as List<dynamic>).length; j++) {
                final dynamic wage = (currentEmpServiceData['wages'] as List<dynamic>)[j];
                final String grossPay = Helpers.calculateGrossPay(
                  wage['regWages'].toString(),
                  wage['OTWages'].toString(),
                );

                totalGrossPay.value += int.parse(grossPay);

                tableRows.add(
                  pw.TableRow(
                    children: <pw.Text>[
                      pw.Text(
                        jobCodeList
                            .firstWhere(
                              (SystemSettingJsonRecordJobCode element) => element.index == int.parse(wage['jobCode'].toString()),
                            )
                            .title,
                      ),
                      pw.Text(
                        hoursString(
                          Helpers.addTime(
                            wage['regHrs'].toString(),
                            wage['OTHrs'].toString(),
                          ),
                        ),
                      ),
                      pw.Text(
                        hoursString(wage['mealHrs'].toString()),
                      ),
                      pw.Text(
                        hoursString(wage['regHrs'].toString()),
                      ),
                      pw.Text(
                        Helpers.formatCurrency(
                          int.parse(wage['regRate'].toString()),
                        ),
                      ),
                      pw.Text(
                        hoursString(wage['OTHrs'].toString()),
                      ),
                      pw.Text(
                        Helpers.formatCurrency(
                          int.parse(wage['OTRate'].toString()),
                        ),
                      ),
                      pw.Text(
                        "\$${Helpers.formatCurrency(int.parse(grossPay))}",
                      ),
                    ],
                  ),
                );
              }
              tableRows.add(
                pw.TableRow(
                  decoration: divider,
                  children: <pw.Text>[
                    pw.Text(
                      "Totals",
                    ),
                    pw.Text(
                      hoursString((totals as Map<String, dynamic>)['totalHrs'].toString()),
                    ),
                    pw.Text(
                      hoursString(totals['mealHrs'].toString()),
                    ),
                    pw.Text(
                      hoursString(totals['regHrs'].toString()),
                    ),
                    pw.Text(
                      "-",
                    ),
                    pw.Text(
                      hoursString(totals['OTHrs'].toString()),
                    ),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(totalGrossPay.value)}",
                    ),
                  ],
                ),
              );
              if (tips != null) {
                tableRows.addAll(
                  <pw.TableRow>[
                    pw.TableRow(
                      children: <pw.Widget>[pw.Container(height: 12)],
                    ),
                    pw.TableRow(
                      children: <pw.Text>[
                        pw.Text("Tips/Gratuity               Tips:"),
                        pw.Text("\$${Helpers.formatCurrency(tips.tip_amount)}"),
                        pw.Text("Gratuity:"),
                        pw.Text("\$${Helpers.formatCurrency(tips.grat_amount)}"),
                        pw.Text("Grat+Tip:"),
                        pw.Text("\$${Helpers.formatCurrency(tips.grat_amount + tips.tip_amount)}"),
                        pw.Text("w/ Gross:"),
                        pw.Text("\$${Helpers.formatCurrency(tips.grat_amount + tips.tip_amount + totalGrossPay.value)}"),
                      ],
                    ),
                  ],
                );
              }
              if (!reportRecord.value.document.wageSignatureLines) {
                tableRows.add(
                  pw.TableRow(
                    children: <pw.Widget>[pw.Container(height: 35)],
                  ),
                );
              }
              allRows.addAll(tableRows);
              table.add(tableRows);
            }

            // If Signature lines are turned off use standard function with all rows
            // else use specialized paginated wage report pdf function with nested row lists
            final Either<ServiceError, File> pdfRes = reportRecord.value.document.wageSignatureLines
                ? await _pdfService.generatePDFWageReport(
                    table,
                    reportTitle,
                    wageStart,
                    wageEnd,
                  )
                // Has to use multipage function to avoid formatting errors
                : await _pdfService.generateMuliPagePDF(
                    allRows,
                    reportTitle,
                    wageStart,
                    wageEnd,
                  );

            pdfRes.fold((ServiceError l) {
              throw l.message;
            }, (File r) {
              pdfFiles.add(FileAttachment(r));
            });
          } catch (e) {
            countFailed = countFailed + 1;
            countTotal = countTotal - 1;
            _notificationService.error(e.toString());
          }

        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        ///
        case 'Server Report':
          final List<pw.TableRow> tableRows = <pw.TableRow>[];

          final List<EmployeeRecord> empRecordList = <EmployeeRecord>[];
          final List<int> empIDList = <int>[];

          final RxList<EmployeeMediaBreakdown> employeeBreakdownList = <EmployeeMediaBreakdown>[].obs;
          final RxList<EmployeeStatistics> employeeStatsList = <EmployeeStatistics>[].obs;
          final RxList<EmployeeTipBreakdown> employeeTipList = <EmployeeTipBreakdown>[].obs;
          final RxList<EmployeeSalesTax> taxRecordList = <EmployeeSalesTax>[].obs;
          final RxList<EmployeeSalesByDepartment> empSalesByDeptList = <EmployeeSalesByDepartment>[].obs;
          final RxList<Employee> employeeList = <Employee>[].obs;
          final RxInt departTotal = 0.obs;
          RxInt departCountTotal = 0.obs;
          final RxInt taxTotal = 0.obs;

          final Either<ServiceError, List<EmployeeSalesByDepartment>> empSalesByDeptRes = await _reportService.getServerDeptReport(
            start,
            end,
            null,
            null,
          );
          empSalesByDeptRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeSalesByDepartment> r) {
            empSalesByDeptList.value = r;
          });
          final Either<ServiceError, List<EmployeeMediaBreakdown>> empMediaRes = await _fohReportService.getServerMediaReport(
            start,
            end,
            null,
            empSalesByDeptList.map((EmployeeSalesByDepartment e) => e.id ?? 0).toSet().toList(),
          );
          empMediaRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeMediaBreakdown> r) {
            employeeBreakdownList.value = r;
          });
          final Either<ServiceError, List<EmployeeStatistics>> employeeStatsRes = await _reportService.getServerStatsReportList(
            start,
            end,
            null,
            null,
          );

          employeeStatsRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeStatistics> r) {
            employeeStatsList.value = r;
          });

          final Either<ServiceError, List<EmployeeTipBreakdown>> employeeTipRes = await _fohReportService.getServerTipReport(
            start,
            end,
            null,
            null,
          );

          employeeTipRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeTipBreakdown> r) {
            employeeTipList.value = r;
          });

          final Either<ServiceError, List<EmployeeSalesTax>> getEmpTaxRes = await _reportService.getEmployeeSalesTax(start, end, null, null);

          getEmpTaxRes.fold((ServiceError l) {
            throw l.message;
          }, (List<EmployeeSalesTax> r) {
            taxRecordList.value = r;
          });
          final Either<ServiceError, List<Employee>> empListRes = await _reportService.getActiveEmployees();
          empListRes.fold(
            (ServiceError l) {
              throw l.message;
            },
            (List<Employee> r) {
              employeeList.value = r;
            },
          );

          for (int i = 0; i < empSalesByDeptList.length; i++) {
            if (!empIDList.contains(empSalesByDeptList[i].id)) {
              empIDList.add(empSalesByDeptList[i].id!);
              empRecordList.add(
                EmployeeRecord(
                  employeeID: empSalesByDeptList[i].id!,
                  taxRows: <EmployeeSalesTax>[],
                  employee: employeeList.firstWhere(
                    (Employee element) => element.id == empSalesByDeptList[i].id!,
                  ),
                  salesByDeptList: <EmployeeSalesByDepartment>[],
                  mediaBreakdownList: <EmployeeMediaBreakdown>[],
                  tipBreakdownList: <EmployeeTipBreakdown>[],
                  empStats: employeeStatsList.firstWhereOrNull(
                        (EmployeeStatistics s) => s.id == empSalesByDeptList[i].id,
                      ) ??
                      EmployeeStatistics.empty(),
                ),
              );
            }
          }

          for (int i = 0; i < taxRecordList.length; i++) {
            for (int j = 0; j < empRecordList.length; j++) {
              if (taxRecordList[i].emp_id == empRecordList[j].employeeID) {
                empRecordList[j].taxRows.add(taxRecordList[i]);
              }
            }
          }

          for (int i = 0; i < empSalesByDeptList.length; i++) {
            for (int j = 0; j < empRecordList.length; j++) {
              if (empSalesByDeptList[i].id == empRecordList[j].employeeID) {
                empRecordList[j].salesByDeptList.add(empSalesByDeptList[i]);
              }
            }
          }

          for (int i = 0; i < employeeBreakdownList.length; i++) {
            for (int j = 0; j < empRecordList.length; j++) {
              if (employeeBreakdownList[i].id == empRecordList[j].employeeID) {
                empRecordList[j].mediaBreakdownList.add(employeeBreakdownList[i]);
              }
            }
          }

          for (int i = 0; i < employeeTipList.length; i++) {
            for (int j = 0; j < empRecordList.length; j++) {
              if (employeeTipList[i].emp_id == empRecordList[j].employeeID) {
                empRecordList[j].tipBreakdownList.add(employeeTipList[i]);
              }
            }
          }

          for (final EmployeeRecord element in empRecordList) {
            departTotal.value = 0;
            departCountTotal.value = 0;
            tableRows.addAll(<pw.TableRow>[
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    element.employee.employee_full_name ?? "no name",
                    textScaleFactor: 1.5,
                  ),
                  pw.Text(""),
                  pw.Text(
                    "Emp ID: ${element.employeeID}",
                    textScaleFactor: 1.2,
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                ],
              ),
            ]);
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Department"),
                  pw.Text("Count"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("Amount"),
                ],
              ),
            );
            for (final EmployeeSalesTax a in element.taxRows) {
              taxTotal.value = taxTotal.value + a.tax_amount;
            }

            for (final EmployeeSalesByDepartment empDeptRecord in element.salesByDeptList) {
              departTotal.value += empDeptRecord.actual_price;
              departCountTotal += empDeptRecord.count;
              tableRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(empDeptRecord.department),
                    pw.Text(Helpers.formatWholeNumber(empDeptRecord.count)),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(empDeptRecord.actual_price)}",
                    ),
                  ],
                ),
              );
            }

            tableRows.addAll(<pw.TableRow>[
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  border: pw.TableBorder(top: pw.BorderSide()),
                ),
                children: <pw.Text>[
                  pw.Text("Department Total:"),
                  pw.Text(departCountTotal.value.toString()),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(departTotal.value)}"),
                ],
              ),
            ]);
            for (final EmployeeSalesTax tax in element.taxRows) {
              tableRows.addAll(<pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(tax.description),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text("\$${Helpers.formatCurrency(tax.tax_amount)}"),
                  ],
                ),
              ]);
            }

            tableRows.addAll(<pw.TableRow>[
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Takeout Fee:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(takeoutFees.value)}"),
                ],
              ),
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Total:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    "\$${Helpers.formatCurrency(departTotal.value + taxTotal.value + takeoutFees.value)}",
                  ),
                ],
              ),
            ]);
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                ],
              ),
            );
            // ignore: unnecessary_statements
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Media Type"),
                  pw.Text("Total"),
                  pw.Text("Tips"),
                  pw.Text(""),
                  pw.Text("Total with Tips"),
                ],
              ),
            );
            RxInt mediaAmountTotal = 0.obs;
            RxInt mdiaCountTotal = 0.obs;
            for (final EmployeeMediaBreakdown empMediaRow in element.mediaBreakdownList) {
              final RxInt tipAmount = 0.obs;
              mdiaCountTotal += empMediaRow.count;
              mediaAmountTotal += empMediaRow.amount;
              for (final EmployeeTipBreakdown empTipBreakdown in element.tipBreakdownList) {
                if (empTipBreakdown.tender_media.toString() == empMediaRow.media) {
                  tipAmount.value = empTipBreakdown.tip_amount;
                }
              }
              tableRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "${Helpers.mediaTypeAsString(
                        PaymentMediaType.values.firstWhere(
                          (PaymentMediaType element) => element.index == int.parse(empMediaRow.media!),
                        ),
                      )}:(${Helpers.formatWholeNumber(empMediaRow.count)})",
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(empMediaRow.amount)}",
                    ),
                    pw.Text("\$${Helpers.formatCurrency(tipAmount.value)}"),
                    pw.Text(""),
                    pw.Text("\$${Helpers.formatCurrency(empMediaRow.amount + tipAmount.value)}"),
                  ],
                ),
              );
            }
            tableRows.add(
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  border: pw.TableBorder(top: pw.BorderSide()),
                ),
                children: <pw.Text>[
                  pw.Text("Total:($mdiaCountTotal)"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    "\$${Helpers.formatCurrency(mediaAmountTotal.value)}",
                  ),
                ],
              ),
            );
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                ],
              ),
            );
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("Media"),
                  pw.Text("Gratuity"),
                  pw.Text("Tips"),
                  pw.Text(""),
                  pw.Text("Total"),
                ],
              ),
            );
            RxInt gratMediaTotal = 0.obs;
            RxInt tipMediaTotal = 0.obs;
            for (final EmployeeTipBreakdown empTipBreakdown in element.tipBreakdownList) {
              gratMediaTotal += empTipBreakdown.grat_amount;
              tipMediaTotal += empTipBreakdown.tip_amount;

              tableRows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "${Helpers.mediaTypeAsString(
                        PaymentMediaType.values.firstWhere(
                          (PaymentMediaType element) => element.index == empTipBreakdown.tender_media,
                        ),
                      )}:",
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(empTipBreakdown.grat_amount)}",
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(empTipBreakdown.tip_amount)}",
                    ),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(empTipBreakdown.grat_amount + empTipBreakdown.tip_amount)}",
                    ),
                  ],
                ),
              );
            }
            tableRows.add(
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  border: pw.TableBorder(top: pw.BorderSide()),
                ),
                children: <pw.Text>[
                  pw.Text("Total:"),
                  pw.Text("\$${Helpers.formatCurrency(gratMediaTotal.value)}"),
                  pw.Text("\$${Helpers.formatCurrency(tipMediaTotal.value)}"),
                  pw.Text(""),
                  pw.Text(
                    "\$${Helpers.formatCurrency(gratMediaTotal.value + tipMediaTotal.value)}",
                  ),
                ],
              ),
            );
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                  pw.Text("\n"),
                ],
              ),
            );
            tableRows.addAll(
              <pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("Stat"),
                    pw.Text("Count"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text("Amount"),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("#Guest AVG:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.guest_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.empStats.guest_average)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("#Check AVG:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.check_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.empStats.check_average)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("AVG turn time:"),
                    pw.Text(element.empStats.duration),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text("-"),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("Refunded Sales:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.refunded_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.empStats.refunded_amount)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("Cancelled Sales:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.cancel_sale_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      "\$${Helpers.formatCurrency(element.empStats.cancel_sale_amount)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("No Sales:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.no_sale_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("No Sales:"),
                    pw.Text(
                      Helpers.formatWholeNumber(
                        element.empStats.no_sale_count,
                      ),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("Sale Breakdown:"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
              ],
            );

            final Either<ServiceError, List<Sale>> empSaleRes = await _fohReportService.getEmployeeCompletedSalesAll(
              startDate: start,
              endDate: end,
              employeeID: element.employeeID,
            );

            final List<Sale> empSaleList = empSaleRes.fold(
              (ServiceError l) => throw l.message,
              (List<Sale> r) => r,
            );

            for (final Sale s in empSaleList) {
              final int? displayIdx = Helpers.getDisplayFlag(s);
              final SaleFlags? displayFlag = displayIdx == null ? null : SaleFlags.values[displayIdx];
              final String title = Helpers.getSaleTitle(s, null);
              final bool dualPriced = s.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index);
              tableRows.addAll(
                <pw.TableRow>[
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      border: pw.TableBorder(top: pw.BorderSide(color: PdfColors.grey)),
                    ),
                    children: <pw.Text>[
                      pw.Text(
                        title,
                      ),
                      pw.Text(""),
                      pw.Text(displayFlag?.friendlyString ?? 'N/A'),
                      pw.Text(s.end_at == null ? "-" : DateFormat("yyyy-MM-dd h:mm a").format(s.end_at!.toLocal())),
                      pw.Text("total: \$${Helpers.formatCurrency(dualPriced ? s.document.saleHeader.cashTotal : s.document.saleHeader.total)}"),
                    ],
                  ),
                  for (final SaleTender t in s.document.saleHeader.tenders)
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey200,
                      ),
                      children: <pw.Text>[
                        pw.Text(""),
                        pw.Text(""),
                        pw.Text("${PaymentMediaType.values[t.media ?? 0].string} Tender"),
                        pw.Text("Amount: ${Helpers.formatCurrency(t.amount ?? 0)}"),
                        pw.Text("Tip: ${Helpers.formatCurrency(t.tipAmount ?? 0)}"),
                      ],
                    ),
                ],
              );
            }

            if (element.employeeID != empRecordList.last.employeeID) {
              tableRows.addAll(<pw.TableRow>[
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
                pw.TableRow(
                  decoration: const pw.BoxDecoration(
                    border: pw.TableBorder(top: pw.BorderSide()),
                  ),
                  children: <pw.Widget>[
                    pw.Text(""),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
              ]);
            }
          }

          if (empRecordList.isEmpty) {
            tableRows.add(
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text("No server reports for this time period."),
                ],
              ),
            );
          }

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Transaction History':
          final List<pw.TableRow> tableRows = <pw.TableRow>[];
          final RxList<Sale> saleList = <Sale>[].obs;

          final Either<ServiceError, List<Sale>> transRes = await _reportService.getReportTransHisrtoy(start, end, selectedTerminal.value);
          transRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Sale> r) {
            saleList.value = r;
          });
          tableRows.add(
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Sale Number"),
                pw.Text("Settle Employee"),
                pw.Text("Settle Terminal"),
                pw.Text("Status"),
                pw.Text("Total"),
              ],
            ),
          );
          for (final Sale element in saleList) {
            final RxString status = "".obs;
            if (element.document.saleHeader.saleFlags.contains(0) &&
                element.document.saleHeader.saleFlags.contains(4) &&
                element.document.saleHeader.total <= 0) {
              status.value = "Refunded";
            } else if (element.document.saleHeader.saleFlags.contains(0) && element.document.saleHeader.saleFlags.contains(5)) {
              status.value = "Paid Out";
            } else if (element.document.saleHeader.saleFlags.contains(1)) {
              status.value = "No Sale";
            } else if (element.document.saleHeader.saleFlags.contains(3)) {
              status.value = "Cancelled";
            } else if (element.document.saleHeader.saleFlags.contains(2)) {
              status.value = "Suspended";
            } else {
              status.value = "Completed";
            }
            tableRows.add(
              pw.TableRow(
                decoration: element == saleList.first ? divider : null,
                children: <pw.Text>[
                  pw.Text(element.sale_number.toString()),
                  pw.Text(
                    element.document.saleHeader.settleEmployeeNumber.toString(),
                  ),
                  pw.Text(
                    element.document.saleHeader.settleTerminalNumber.toString(),
                  ),
                  pw.Text(status.value),
                  pw.Text(
                    "\$${Helpers.formatCurrency(element.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index) ? element.document.saleHeader.cashTotal : element.document.saleHeader.total)}",
                  ),
                ],
              ),
            );
          }
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });
        case 'Activity':
          final List<pw.TableRow> tableRows = <pw.TableRow>[
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("Time"),
                pw.Text("Employee"),
                pw.Text("Terminal"),
                pw.Text("Data 1"),
                pw.Text("Data 2"),
                pw.Text("Activity"),
              ],
            ),
          ];

          final Either<ServiceError, List<Activity>> activityRes = await _reportService.getReportActivity(
            start,
            end,
            selectedTerminal.value,
            null,
          );

          activityRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Activity> r) {
            for (final Activity activity in r) {
              tableRows.add(
                pw.TableRow(
                  decoration: activity == r.first ? divider : null,
                  children: <pw.Text>[
                    pw.Text(
                      DateFormat.yMd().add_jm().format(
                            DateTime.parse(activity.created_at.toString()).toLocal(),
                          ),
                    ),
                    pw.Text(
                      "${activity.emp_id} - ${empList.firstWhere((Employee element) => element.id == activity.emp_id).document.lastName},${empList.firstWhere((Employee element) => element.id == activity.emp_id).document.firstName} ",
                    ),
                    pw.Text(activity.term_num.toString()),
                    pw.Text((activity.data1 ?? 0).toString()),
                    pw.Text((activity.data2 ?? 0).toString()),
                    pw.Text(activity.str_data ?? "No Data"),
                  ],
                ),
              );
            }
          });
          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'EmployeeList':
          final List<pw.TableRow> tableRows = <pw.TableRow>[
            pw.TableRow(
              children: <pw.Text>[
                pw.Text("ID"),
                pw.Text("Active"),
                pw.Text("Birth Date"),
                pw.Text("Phone #"),
                pw.Text("Employee"),
                pw.Text("Class"),
              ],
            ),
          ];

          final Either<ServiceError, List<Employee>> empRes = await _reportService.getAllEmployees();
          empRes.fold((ServiceError l) {
            throw l.message;
          }, (List<Employee> r) {
            for (final Employee element in r) {
              tableRows.add(
                pw.TableRow(
                  decoration: element == r.first ? divider : null,
                  children: <pw.Widget>[
                    pw.Text(element.id.toString()),
                    pw.Text(element.is_active.toString()),
                    pw.Text(
                      element.document.birthDate == "" ? "00/00/00" : element.document.birthDate.toString(),
                    ),
                    pw.Text(
                      element.document.phoneNumber == "" ? "( )  -     " : element.document.phoneNumber ?? "( ) -      ",
                    ),
                    pw.Text(element.employee_full_name ?? ""),
                    pw.Text(element.employeeClassByEmployeeClass?.title ?? ""),
                  ],
                ),
              );
            }
          });

          final Either<ServiceError, File> pdfRes = await _pdfService.generatePDF(tableRows, reportTitle, start, end);

          pdfRes.fold((ServiceError l) {
            throw l.message;
          }, (File r) {
            pdfFiles.add(FileAttachment(r));
          });

        case 'Daily Sales':
          try {
            ///
            ///
            ///Major Department variables
            final RxList<MajorReport> majorDeptList = <MajorReport>[].obs;

            final RxInt total = 0.obs;
            final RxInt count = 0.obs;
            final RxInt mediaTotal = 0.obs;
            final RxInt mediaCount = 0.obs;
            final RxInt mediaTipTotal = 0.obs;
            final RxInt mediaTotalWithTip = 0.obs;
            final RxInt orderTypeTotal = 0.obs;
            final RxInt orderTypeCount = 0.obs;

            final RxInt houseChargeCount = 0.obs;
            final RxInt houseChargeTotal = 0.obs;

            final RxInt cashTotal = 0.obs;
            final RxInt ccTips = 0.obs;

            final RxInt noSaleCount = 0.obs;
            final RxInt cancelSaleCount = 0.obs;
            final RxInt cancelSaleAmount = 0.obs;
            final RxInt refundedAmount = 0.obs;
            final RxInt refundedCount = 0.obs;
            final RxInt reopenedTotal = 0.obs;
            final RxInt reopenedCount = 0.obs;
            final RxInt totalDiscounts = 0.obs;

            final RxList<SalesByDeptSummaryReport> dataRows = <SalesByDeptSummaryReport>[].obs;
            final RxList<SalesByDeptSummaryReport> printerRows = <SalesByDeptSummaryReport>[].obs;
            final RxList<EmployeeDiscount> discountList = <EmployeeDiscount>[].obs;
            final RxList<MediaReport> mediaList = <MediaReport>[].obs;
            final RxList<OrderTypeReport> orderTypeList = <OrderTypeReport>[].obs;
            final RxList<String> discountTitles = <String>[].obs;

            final List<pw.TableRow> rows = <pw.TableRow>[
              pw.TableRow(
                children: <pw.Text>[
                  pw.Text(
                    "Department",
                    style: pw.TextStyle(fontWeight: FontWeight.bold),
                  ),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    "Count",
                    style: pw.TextStyle(fontWeight: FontWeight.bold),
                  ),
                  pw.Text(
                    "Amount",
                    style: pw.TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ];

            final Either<ServiceError, List<SalesByDeptSummaryReport>> getSalesByDeptRes = await _reportService.getSalesByDeptSum(
              start,
              end,
              selectedTerminal.value,
            );

            getSalesByDeptRes.fold((ServiceError l) {
              throw l.message;
            }, (List<SalesByDeptSummaryReport> r) {
              for (final SalesByDeptSummaryReport report in r) {
                dataRows.add(report);
                printerRows.add(report);
              }
            });

            final Either<ServiceError, List<MediaReport>> mediaRes = await _reportService.getMediaBreakdown(
              start,
              end,
              selectedTerminal.value,
            );

            mediaRes.fold(
              (ServiceError l) {
                throw l.message;
              },
              (List<MediaReport> r) {
                mediaList.value = r;
              },
            );

            final Either<ServiceError, List<Sale>> houseRes = await _reportService.getHouseCharges(
              start,
              end,
              selectedTerminal.value,
            );

            final Either<ServiceError, List<MajorReport>> majorRes = await _reportService.getMajorReport(
              startDate: start,
              endDate: end,
              selectedTerminal: selectedTerminal.value,
            );

            majorRes.fold(
              (ServiceError l) {
                throw l.message;
              },
              (List<MajorReport> r) {
                majorDeptList.value = r;
              },
            );

            houseRes.fold((ServiceError l) {
              _notificationService.error(l.message);
              throw l.message;
            }, (List<Sale> res) {
              for (int i = 0; i < res.length; i++) {
                final List<SaleTender> tenders = res[i].document.saleHeader.tenders;
                for (final SaleTender tender in tenders) {
                  if (tender.media == PaymentMediaType.House.index) {
                    houseChargeTotal.value += tender.amount!;
                    houseChargeCount.value += 1;
                  }
                }
              }
            });

            final Either<ServiceError, List<OrderTypeReport>> orderRes = await _reportService.getOrderTypeReport(
              startDate: start,
              endDate: end,
              selectedTerminal: selectedTerminal.value,
            );
            orderRes.fold((ServiceError l) {
              throw l.message;
            }, (List<OrderTypeReport> r) {
              orderTypeList.value = r;
            });

            final Either<ServiceError, List<EmployeeStatistics>> statsRes = await _reportService.getServerStatsReportList(
              start,
              end,
              selectedTerminal.value,
              null,
            );

            statsRes.fold(
              (ServiceError l) {
                throw l.message;
              },
              (List<EmployeeStatistics> r) {
                for (final EmployeeStatistics element in r) {
                  noSaleCount.value += element.no_sale_count;
                  cancelSaleCount.value += element.cancel_sale_count;
                  cancelSaleAmount.value += element.cancel_sale_amount;
                  refundedAmount.value += element.refunded_amount;
                  refundedCount.value += element.refunded_count;
                  reopenedTotal.value += element.reopened_total;
                  reopenedCount.value += element.reopened_count;
                }
              },
            );

            final Either<ServiceError, List<EmployeeDiscount>> empDiscountRes = await _reportService.getEmployeeDiscount(
              startDate: start,
              endDate: end,
              selectedTerminal: selectedTerminal.value,
            );

            empDiscountRes.fold(
              (ServiceError l) {
                throw l.message;
              },
              (List<EmployeeDiscount> r) {
                discountList.value = r;
                for (final EmployeeDiscount element in r) {
                  if (!discountTitles.contains(element.title)) {
                    discountTitles.add(element.title);
                  }
                }
              },
            );
            for (final MajorReport m in majorDeptList) {
              rows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                    pw.Text("\n"),
                  ],
                ),
              );
              int majorTotal = 0;
              int majorCount = 0;
              rows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      m.major_group,
                      style: pw.TextStyle(fontWeight: FontWeight.bold),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(""),
                  ],
                ),
              );
              for (final SalesByDeptSummaryReport deptRow in dataRows) {
                final bool childOfMajor = await _reportService.getMajorChildren(
                  deptRow.department!,
                  m.major_group,
                );
                if (childOfMajor) {
                  majorTotal += deptRow.dept_actual_price ?? 0;
                  majorCount += deptRow.item_count ?? 0;
                  rows.add(
                    pw.TableRow(
                      children: <pw.Text>[
                        pw.Text(deptRow.department!),
                        pw.Text(""),
                        pw.Text(""),
                        pw.Text(Helpers.formatWholeNumber(deptRow.item_count!)),
                        pw.Text(
                          "\$${Helpers.formatCurrency(deptRow.dept_actual_price ?? 0)}",
                        ),
                      ],
                    ),
                  );

                  total.value += deptRow.dept_actual_price ?? 0;
                  count.value += deptRow.item_count ?? 0;
                }
              }
              rows.add(
                pw.TableRow(
                  children: <pw.Text>[
                    pw.Text(
                      "Major Total:",
                      style: pw.TextStyle(fontWeight: FontWeight.bold),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(
                      Helpers.formatWholeNumber(majorCount),
                      style: pw.TextStyle(fontWeight: FontWeight.bold),
                    ),
                    pw.Text(
                      "\$${Helpers.formatCurrency(majorTotal)}",
                      style: pw.TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            }

            rows.addAll(<pw.TableRow>[
              pw.TableRow(
                decoration: const pw.BoxDecoration(
                  border: pw.TableBorder(top: pw.BorderSide()),
                ),
                children: <pw.Widget>[
                  pw.Text("Department totals:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    Helpers.formatWholeNumber(count.value),
                  ),
                  pw.Text("\$${Helpers.formatCurrency(total.value)}"),
                ],
              ),
              ...taxRows,
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Net Total:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(
                    "\$${Helpers.formatCurrency(total.value + taxTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Takeout Fees:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(takeoutFees.value)}"),
                ],
              ),
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("-Paid Out:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(paidOutCount.value.toString()),
                  pw.Text("\$${Helpers.formatCurrency(paidOutTotal.value)}"),
                ],
              ),
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("-Gift Card Sales:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(giftCount.value.toString()),
                  pw.Text("\$${Helpers.formatCurrency(giftTotal.value)}"),
                ],
              ),
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("-House Charges:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(houseChargeCount.value.toString()),
                  pw.Text(
                    "\$${Helpers.formatCurrency(houseChargeTotal.value)}",
                  ),
                ],
              ),
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text("Total Accountable:"),
                  ),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(""),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency((total.value + taxTotal.value + takeoutFees.value) - paidOutTotal.value - giftTotal.value - houseChargeTotal.value)}",
                    ),
                  ),
                ],
              ),
            ]);

            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Media"),
                  pw.Text("Count"),
                  pw.Text("Total"),
                  pw.Text("Tips"),
                  pw.Text("Total with Tips"),
                ],
              ),
            );
            for (final MediaReport media in mediaList) {
              mediaTotal.value += media.amount;
              mediaCount.value += media.count;
              mediaTipTotal.value += media.tips;
              mediaTotalWithTip.value += media.amount;
              mediaTotalWithTip.value += media.tips;

              if (media.media == '0') {
                cashTotal.value = media.amount;
              }
              if (media.media == '2') {
                ccTips.value = media.tips;
              }
              rows.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text(
                      Helpers.mediaTypeAsString(
                        PaymentMediaType.values[int.parse(media.media)],
                      ),
                    ),
                    pw.Text(Helpers.formatWholeNumber(media.count)),
                    pw.Text("\$${Helpers.formatCurrency(media.amount)}"),
                    pw.Text("\$${Helpers.formatCurrency(media.tips)}"),
                    pw.Text(
                      "\$${Helpers.formatCurrency(media.amount + media.tips)}",
                    ),
                  ],
                ),
              );
            }

            rows.add(
              pw.TableRow(
                decoration: divider,
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text("Total"),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      Helpers.formatWholeNumber(mediaCount.value),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(mediaTotal.value)}",
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(mediaTipTotal.value)}",
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(mediaTotalWithTip.value)}",
                    ),
                  ),
                ],
              ),
            );

            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Cash Total:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(cashTotal.value)}"),
                ],
              ),
            );
            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("-C/C Tips:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(ccTips.value)}"),
                ],
              ),
            );
            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("-Paid Out:"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("\$${Helpers.formatCurrency(paidOutTotal.value)}"),
                ],
              ),
            );
            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text("Adjusted Cash:"),
                  ),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "",
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(cashTotal.value - paidOutTotal.value - ccTips.value)}",
                    ),
                  ),
                ],
              ),
            );

            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Order Type"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("Count"),
                  pw.Text("Total"),
                ],
              ),
            );

            for (final OrderTypeReport element in orderTypeList) {
              orderTypeCount.value += element.qty;
              orderTypeTotal.value += element.total;
              rows.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text(OrderType.values[element.type].friendlyString),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(Helpers.formatWholeNumber(element.qty)),
                    pw.Text("\$${Helpers.formatCurrency(element.total)}"),
                  ],
                ),
              );
            }

            rows.add(
              pw.TableRow(
                decoration: divider,
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text("Total"),
                  ),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      Helpers.formatWholeNumber(orderTypeCount.value),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(orderTypeTotal.value)}",
                    ),
                  ),
                ],
              ),
            );

            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Statistic"),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("Count"),
                  pw.Text("Total"),
                ],
              ),
            );

            rows.addAll(
              <pw.TableRow>[
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text("Cancelled Sales"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(Helpers.formatWholeNumber(cancelSaleCount.value)),
                    pw.Text(
                      "\$${Helpers.formatCurrency(cancelSaleAmount.value)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text("Refunded Sales"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(Helpers.formatWholeNumber(refundedCount.value)),
                    pw.Text(
                      "\$${Helpers.formatCurrency(refundedAmount.value)}",
                    ),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text("Reopened Sales"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(Helpers.formatWholeNumber(reopenedCount.value)),
                    pw.Text("\$${Helpers.formatCurrency(reopenedTotal.value)}"),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text("Paid Out Total"),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(Helpers.formatWholeNumber(paidOutCount.value)),
                    pw.Text("\$${Helpers.formatCurrency(paidOutTotal.value)}"),
                  ],
                ),
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(bottom: 20),
                      child: pw.Text("No Sales"),
                    ),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(bottom: 20),
                      child: pw.Text(
                        Helpers.formatWholeNumber(noSaleCount.value),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.only(bottom: 20),
                      child: pw.Text(" "),
                    ),
                  ],
                ),
              ],
            );

            rows.add(
              pw.TableRow(
                children: <pw.Widget>[
                  pw.Text("Discount"),
                  pw.Text(" "),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Text("Total"),
                ],
              ),
            );
            for (final String element in discountTitles) {
              final RxInt currentAmount = 0.obs;

              for (final EmployeeDiscount x in discountList) {
                if (x.title == element) {
                  currentAmount.value += x.discount_total;
                  totalDiscounts.value += x.discount_total;
                }
              }

              rows.add(
                pw.TableRow(
                  children: <pw.Widget>[
                    pw.Text(element),
                    pw.Text(""),
                    pw.Text(""),
                    pw.Text(" "),
                    pw.Text("\$${Helpers.formatCurrency(currentAmount.value)}"),
                  ],
                ),
              );
            }

            rows.add(
              pw.TableRow(
                decoration: divider,
                children: <pw.Widget>[
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text("Total"),
                  ),
                  pw.Text(""),
                  pw.Text(""),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      " ",
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 20),
                    child: pw.Text(
                      "\$${Helpers.formatCurrency(totalDiscounts.value)}",
                    ),
                  ),
                ],
              ),
            );

            final Either<ServiceError, File> pdfDeptRes = await _pdfService.generatePDF(rows, reportTitle, start, end);

            pdfDeptRes.fold((ServiceError l) {
              throw l.message;
            }, (File r) {
              pdfFiles.add(FileAttachment(r));
            });
          } catch (e) {
            return false;
          }

        default:
      }
    }

    if (pdfFiles.isNotEmpty) {
      countTotal = pdfFiles.length;
      final bool resp = await _emailService.sendReport(
        pdfFiles,
        reportRecord.value.document.reportEmailAddresses,
      );

      _notificationService.success("Reports sent");
      for (final FileAttachment element in pdfFiles) {
        if (element.fileName != null) {
          await File(element.fileName!).delete();
        }
      }
      return resp;
    } else {
      _notificationService.error("No reports set to send");
    }
    return false;
  }
}
