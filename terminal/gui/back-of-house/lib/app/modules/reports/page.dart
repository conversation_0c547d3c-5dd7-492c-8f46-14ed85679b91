import 'dart:async';

import 'package:backoffice/app/data/boh_constants.dart';
import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/services/boh_report.service.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/navigable.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/modules/reports/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

BohReportService _reportService = Get.find();

@immutable
class ReportsPage extends GetView<ReportsController> {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> formKey = GlobalKey();
  final GlobalKey<FormState> reportKey = GlobalKey();
  final GlobalKey<FormState> emailKey = GlobalKey();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      endDrawer: Container(
        clipBehavior: Clip.hardEdge,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            bottomLeft: Radius.circular(10),
          ),
        ),
        child: Drawer(
          backgroundColor: R2Colors.white,
          surfaceTintColor: Colors.transparent,
          width: 600,
          child: Flex(
            direction: Axis.vertical,
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Flexible(
                flex: 3,
                child: Header(
                  title: "Send Reports",
                  transparentBackground: false,
                  leftButton: DialogButton(
                    buttonType: EDialogButtonType.BACK,
                    onTapped: () {
                      scaffoldKey.currentState!.closeEndDrawer();
                    },
                  ),
                  rightButton: DialogButton(
                    buttonType: EDialogButtonType.ADD,
                    buttonText: "Save",
                    onTapped: () async {
                      await _reportService.updateReportRecord(controller.reportRecord.value);
                    },
                  ),
                ),
              ),
              Flexible(
                flex: 15,
                child: SingleChildScrollView(
                  child: Flex(
                    direction: Axis.vertical,
                    children: <Widget>[
                      FormWrapper(
                        formKey: formKey,
                        children: <MenuGroup>[
                          MenuGroup(
                            title: 'Reporting Dates',
                            children: <Widget>[
                              Obx(
                                () => Column(
                                  children: <Widget>[
                                    const SizedBox(height: 10),
                                    ToggleButtons(
                                      isSelected: <bool>[controller.usePayPeriod.value, !controller.usePayPeriod.value],
                                      children: const <Widget>[
                                        Padding(
                                          padding: EdgeInsets.only(left: 10, right: 10),
                                          child: Text("By Pay Period"),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(left: 10, right: 10),
                                          child: Text("Custom  Range"),
                                        ),
                                      ],
                                      onPressed: (int i) async {
                                        controller.usePayPeriod.value = !controller.usePayPeriod.value;
                                        if (controller.usePayPeriod.value) {
                                          await controller.getPayPeriodOnDateSelect(
                                            PickerDateRange(
                                              controller.dateRangeController.value.selectedRange?.startDate ?? DateTime.now(),
                                              null,
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                    const SizedBox(height: 10),
                                    SfDateRangePicker(
                                      backgroundColor: R2Colors.white,
                                      headerStyle: const DateRangePickerHeaderStyle(
                                        backgroundColor: R2Colors.white,
                                      ),
                                      selectionColor: R2Colors.primary500,
                                      rangeSelectionColor: R2Colors.primary300,
                                      startRangeSelectionColor: R2Colors.primary500,
                                      endRangeSelectionColor: R2Colors.primary500,
                                      todayHighlightColor: R2Colors.primary500,
                                      controller: controller.dateRangeController.value,
                                      selectionMode: DateRangePickerSelectionMode.range,
                                      monthViewSettings: const DateRangePickerMonthViewSettings(
                                        enableSwipeSelection: false,
                                      ),
                                      onSelectionChanged: (DateRangePickerSelectionChangedArgs newDate) async {
                                        await controller.getPayPeriodOnDateSelect(newDate.value as PickerDateRange);
                                      },
                                    ),
                                    if (!controller.usePayPeriod.value && controller.reportRecord.value.document.reportList.contains("Wage Report"))
                                      Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Column(
                                          children: <Widget>[
                                            const Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: <Widget>[
                                                Icon(
                                                  Icons.info,
                                                  size: 20,
                                                  color: R2Colors.blue500,
                                                ),
                                                Text(
                                                  " Wage report will only show pay period range",
                                                  style: TextStyle(color: R2Colors.neutral700),
                                                ),
                                              ],
                                            ),
                                            Text(
                                              "Current range: ${DateFormat('MM/dd/yyyy hh:mma').format(DateTime.parse(controller.periodStart.value))} - ${DateFormat('MM/dd/yyyy hh:mma').format(DateTime.parse(controller.periodEnd.value))}",
                                              style: const TextStyle(
                                                fontSize: 12,
                                                fontStyle: FontStyle.italic,
                                                color: R2Colors.neutral500,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          MenuGroup(
                            title: 'Reports to send',
                            children: <Widget>[
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: TextButton(
                                      onPressed: () async {
                                        await controller.scrollController.animateTo(
                                          controller.scrollController.offset - 150,
                                          duration: const Duration(milliseconds: 200),
                                          curve: Curves.linear,
                                        );
                                      },
                                      child: const Icon(Icons.arrow_upward),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 244,
                                child: ListView.separated(
                                  separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                                  shrinkWrap: true,
                                  controller: controller.scrollController,
                                  itemCount: BohConstants.reports.length,
                                  itemBuilder: (BuildContext context, int index) => Obx(
                                    () => Material(
                                      child: ListTile(
                                        tileColor: R2Colors.neutral100,
                                        selectedColor: R2Colors.primary500,
                                        selectedTileColor: R2Colors.primary100,
                                        selected: controller.reportRecord.value.document.reportList.contains(BohConstants.reports[index]),
                                        trailing: BohConstants.reports[index].toLowerCase() == "wage report"
                                            ? Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: <Widget>[
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      borderRadius: const BorderRadius.all(
                                                        Radius.circular(8),
                                                      ),
                                                      color: R2Colors.primary300.withOpacity(0.3),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: <Widget>[
                                                        const SizedBox(width: 4),
                                                        const Text("Signature Lines"),
                                                        Checkbox(
                                                          value: controller.reportRecord.value.document.wageSignatureLines,
                                                          onChanged: (bool? val) {
                                                            controller.reportRecord.value.document.wageSignatureLines = val ?? true;
                                                            controller.reportRecord.refresh();
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      borderRadius: const BorderRadius.all(
                                                        Radius.circular(8),
                                                      ),
                                                      color: R2Colors.primary300.withOpacity(0.3),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: <Widget>[
                                                        const SizedBox(width: 4),
                                                        const Text("Decimal Hours"),
                                                        Checkbox(
                                                          value: controller.reportRecord.value.document.wageDecimalHours,
                                                          onChanged: (bool? val) {
                                                            controller.reportRecord.value.document.wageDecimalHours = val ?? true;
                                                            controller.reportRecord.refresh();
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              )
                                            : null,
                                        title: Text(
                                          BohConstants.reports[index],
                                        ),
                                        onTap: () async {
                                          controller.reportRecord.value.document.reportList.contains(BohConstants.reports[index])
                                              ? controller.reportRecord.value.document.reportList
                                                  .removeWhere((String element) => element == BohConstants.reports[index])
                                              : controller.reportRecord.value.document.reportList.add(BohConstants.reports[index]);
                                          controller.reportRecord.refresh();
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: TextButton(
                                      onPressed: () async {
                                        await controller.scrollController.animateTo(
                                          controller.scrollController.offset + 150,
                                          duration: const Duration(milliseconds: 200),
                                          curve: Curves.linear,
                                        );
                                      },
                                      child: const Icon(Icons.arrow_downward),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          MenuGroup(
                            title: 'Email addresses',
                            children: <Widget>[
                              Obx(
                                () => ListView.separated(
                                  separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                                  shrinkWrap: true,
                                  itemCount: controller.reportRecord.value.document.reportEmailAddresses.length,
                                  itemBuilder: (BuildContext context, int index) => ListTile(
                                    title: Text(
                                      controller.reportRecord.value.document.reportEmailAddresses[index],
                                    ),
                                    trailing: MaterialButton(
                                      onPressed: () {
                                        controller.reportRecord.value.document.reportEmailAddresses.removeAt(index);
                                        controller.reportRecord.refresh();
                                      },
                                      child: const FaIcon(
                                        FontAwesomeIcons.circleMinus,
                                        color: R2Colors.red500,
                                        size: 18,
                                      ),
                                    ),
                                    onTap: () async {},
                                  ),
                                ),
                              ),
                              MenuButton(
                                title: "Add Email",
                                onPressed: () async {
                                  await Get.defaultDialog(
                                    content: SizedBox(
                                      height: Get.height * .1,
                                      width: 500,
                                      child: FormWrapper(
                                        formKey: emailKey,
                                        children: <MenuGroup>[
                                          MenuGroup(
                                            title: 'Add Email',
                                            children: <MenuTextField>[
                                              MenuTextField(
                                                controller: controller.addEmailController,
                                                hintText: "<EMAIL>",
                                                validator: (String? value) {
                                                  if (!EmailValidator.validate(value!)) {
                                                    return 'not a valid Email address';
                                                  }
                                                  return null;
                                                },
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    actions: <DialogButton>[
                                      DialogButton(
                                        buttonType: EDialogButtonType.DESTRUCTIVE,
                                        buttonText: 'Back',
                                        onTapped: () {
                                          controller.addEmailController.clear();
                                        },
                                      ),
                                      DialogButton(
                                        buttonType: EDialogButtonType.AFFIRMATIVE,
                                        buttonText: 'Confirm',
                                        onTapped: () {
                                          if (!emailKey.currentState!.validate()) {
                                            return 'Invalid Email';
                                          } else {
                                            controller.reportRecord.value.document.reportEmailAddresses.add(controller.addEmailController.text);
                                            controller.reportRecord.refresh();
                                            controller.addEmailController.clear();
                                            Get.back();
                                          }
                                        },
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Flexible(
                flex: 2,
                child: Flex(
                  mainAxisAlignment: MainAxisAlignment.center,
                  direction: Axis.vertical,
                  children: <Widget>[
                    Flexible(
                      fit: FlexFit.tight,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 5, bottom: 5),
                        child: DialogButton(
                          mainAxisAlignment: MainAxisAlignment.center,
                          buttonType: EDialogButtonType.AFFIRMATIVE,
                          padding: 20,
                          buttonText: "Send Reports",
                          onTapped: () async {
                            final RxBool sendComplete = false.obs;
                            await _reportService.updateReportRecord(controller.reportRecord.value);
                            unawaited(
                              Get.dialog<bool>(
                                Obx(
                                  () => AlertDialog(
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                    elevation: 16,
                                    actionsAlignment: MainAxisAlignment.center,
                                    actions: <DialogButton>[
                                      if (sendComplete.value)
                                        DialogButton(
                                          buttonType: EDialogButtonType.CANCEL,
                                          buttonText: 'Close',
                                          onTapped: () {
                                            scaffoldKey.currentState!.closeEndDrawer();
                                            Get.back();
                                          },
                                        ),
                                    ],
                                    content: SizedBox(
                                      height: Get.height / 3.5,
                                      width: Get.width / 4,
                                      child: Obx(
                                        () => !sendComplete.value
                                            ? Flex(
                                                direction: Axis.vertical,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: <Widget>[
                                                  const Flexible(
                                                    child: Text(
                                                      "Sending Reports...",
                                                      style: TextStyle(
                                                        fontSize: 22,
                                                        fontWeight: FontWeight.w400,
                                                        color: Color.fromARGB(255, 0, 0, 0),
                                                      ),
                                                    ),
                                                  ),
                                                  Flexible(
                                                    child: Lottie.asset(
                                                      'lib/assets/lottie/loading-animation.json',
                                                      height: 100,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            : Flex(
                                                direction: Axis.vertical,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: <Widget>[
                                                  const Text(
                                                    "Results:",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      fontSize: 22,
                                                      fontWeight: FontWeight.w400,
                                                      color: Color.fromARGB(255, 0, 0, 0),
                                                    ),
                                                  ),
                                                  const Divider(),
                                                  ListTile(
                                                    leading: const Text(
                                                      "Reports Sent:",
                                                      style: TextStyle(fontSize: 18),
                                                    ),
                                                    trailing: Text(
                                                      controller.countTotal.toString(),
                                                      style: const TextStyle(fontSize: 18),
                                                    ),
                                                  ),
                                                  if (controller.countFailed > 0)
                                                    ListTile(
                                                      leading: const Text(
                                                        "Reports Failed:",
                                                        style: TextStyle(fontSize: 18),
                                                      ),
                                                      trailing: Text(
                                                        controller.countFailed.toString(),
                                                        style: const TextStyle(fontSize: 18),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );

                            sendComplete.value = await controller.sendDailyReportsEmail();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => scaffoldKey.currentState!.openEndDrawer(),
        backgroundColor: R2Colors.primary500,
        elevation: 0,
        hoverElevation: 0,
        child: const Icon(
          Icons.menu_open,
        ),
      ),
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(200),
        child: Header(
          title: "Reports",
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 450),
                  child: Flex(
                    direction: Axis.vertical,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: MenuGroup(
                          title: "Sales",
                          children: <Widget>[
                            Obx(
                              () => MenuNavigable(
                                title: "Cumulative Sales",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_CUMULATIVE_SALES,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Daily Sales",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORT_DAILY_SALES,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Sales Breakdown",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_MONTHLY_SALES,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Sales by Department (Summary)",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_SALE_BY_DEPT_SUMMARY,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Sales by Department (Full)",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_SALE_BY_DEPT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Media Breakdown",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_MEDIA_BREAKDOWN,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Paid Out",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_PAID_OUT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Gift Summary",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_GIFT_SUMMARY,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Movers",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_MOVERS,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: MenuGroup(
                          title: "Labor",
                          children: <Obx>[
                            Obx(
                              () => MenuNavigable(
                                title: "Time Card",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_TIME_CARD_REPORT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Wage Report",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_WAGE_REPORT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Server Report",
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_SERVER_REPORT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                            Obx(
                              () => MenuNavigable(
                                title: "Electronic Journal",
                                beta: true,
                                onTap: controller.hasPermission.value
                                    ? () {
                                        Get.toNamed(
                                          AppRoutes.REPORTS_EMPLOYEE_SALES_REPORT,
                                          id: AppRoutes.id,
                                        );
                                      }
                                    : () {
                                        controller.permissionDenied();
                                      },
                              ),
                            ),
                          ],
                        ),
                      ),
                      MenuGroup(
                        title: "Miscellaneous",
                        children: <Obx>[
                          Obx(
                            () => MenuNavigable(
                              title: "Transaction History",
                              onTap: controller.hasPermission.value
                                  ? () {
                                      Get.toNamed(
                                        AppRoutes.REPORTS_TRANSACTION_HISTORY,
                                        id: AppRoutes.id,
                                      );
                                    }
                                  : () {
                                      controller.permissionDenied();
                                    },
                            ),
                          ),
                          Obx(
                            () => MenuNavigable(
                              title: "Activity",
                              onTap: controller.hasPermission.value
                                  ? () {
                                      Get.toNamed(
                                        AppRoutes.REPORTS_ACTIVITY,
                                        id: AppRoutes.id,
                                      );
                                    }
                                  : () {
                                      controller.permissionDenied();
                                    },
                            ),
                          ),
                          Obx(
                            () => MenuNavigable(
                              title: "Employee List",
                              onTap: controller.hasPermission.value
                                  ? () {
                                      Get.toNamed(
                                        AppRoutes.REPORTS_EMPLOYEE_LIST,
                                        id: AppRoutes.id,
                                      );
                                    }
                                  : () {
                                      controller.permissionDenied();
                                    },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
