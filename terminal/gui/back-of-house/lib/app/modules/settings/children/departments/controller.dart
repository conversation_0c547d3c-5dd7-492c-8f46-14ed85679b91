import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class DepartmentsController extends GetxController {
  // final HttpRepository<Department> _departmentRepo;
  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();
  // final DepartmentService _departmentService = Get.find();
  final Logger _logger = Logger('DepartmentController');

  RxString currentDepartmentId = "".obs;
  RxList<Department> departmentList = <Department>[].obs;
  RxList<DepartmentGroupItem> majorMinorList = <DepartmentGroupItem>[].obs;

  ScrollController departmentListController = ScrollController();

  RxBool canEdit = false.obs;
  RxBool canUpdateDepartmentSettings = false.obs;
  //Set some default color to please picker on null
  RxInt currentColorHash = 4278234305.obs;
  RxList<String> majorList = <String>[].obs;

  @override
  Future<void> onInit() async {
    await loadDepartments();
    await initialDepartmentCheck(departmentList);

    currentDepartmentId.value = departmentList[0].department;
    await editPermission();

    super.onInit();
  }

  Rx<Department> get currentDepartment {
    if (departmentList.isNotEmpty && currentDepartmentId.value.isNotEmpty) {
      return departmentList
          .firstWhere(
            (Department department) => department.department == currentDepartmentId.value,
          )
          .obs;
    } else {
      return Department.empty().obs;
    }
  }

  Future<void> initialDepartmentCheck(List<Department> checkList) async {
    //Check all departments for order
    bool dumpOrders = false;
    if (checkList.isEmpty) return;
    for (final Department dept in checkList) {
      if (dept.document.order == null) {
        dumpOrders = true;
        break;
      }
    }
    //if no order detected on one, reset all and assign orders in loop
    if (dumpOrders) {
      for (int i = 0; i < checkList.length; i++) {
        checkList[i].document.order = i + 1;
      }
      //run through and save all the new ordered departments
      for (final Department dept in checkList) {
        await updateDepartment(dept);
      }
      await loadDepartments();
    }
  }

  Future<void> loadDepartments({bool sort = true}) async {
    try {
      departmentList.clear();
      majorMinorList.clear();
      majorList.clear();
      final QueryResult<Object> departmentsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
            query GET_DEPARTMENTS {
              department {
                created_at
                created_by
                department
                document
                title
                updated_at
                updated_by
              }
            }
            ''',
          ),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if ((departmentsResult.data!['department']! as List<dynamic>).isEmpty) {
        throw "No Departments Found";
      }

      List<Department> allDepartments = <Department>[];

      allDepartments = (departmentsResult.data!['department'] as List<dynamic>)
          .map((dynamic department) => Department.fromJson(department as Map<String, dynamic>))
          .toList();
      if (sort) {
        allDepartments.sort(
          (Department a, Department b) => a.document.order!.compareTo(b.document.order!),
        );
      }

      for (final Department department in allDepartments) {
        if (department.document.majorGroup != null) {
          if (!majorList.contains(department.document.majorGroup)) {
            majorMinorList.add(
              DepartmentGroupItem(major: department.document.majorGroup),
            );
          }
          majorList.addIf(
            !majorList.contains(department.document.majorGroup),
            department.document.majorGroup!,
          );
        } else {
          majorMinorList.add(
            DepartmentGroupItem(
              department: department.obs,
            ),
          );
        }
      }

      for (final DepartmentGroupItem majorItem in majorMinorList) {
        if (majorItem.major != null) {
          for (final Department minorDepartment in allDepartments) {
            majorItem.departments!.addIf(
              majorItem.major == minorDepartment.document.majorGroup,
              minorDepartment,
            );
          }
        }
      }

      departmentList.value = allDepartments;
    } catch (err, stack) {
      _logger.severe("error loading departments", err, stack);
      _notificationService.error(err.toString());
    }
  }

  Future<void> updateDepartment(Department currentDepartment) async {
    try {
      currentDepartment.updated_by = CURRENT_EMPLOYEE.value.employee;
      currentDepartment.updated_at = DateTime.now().toUtc();
      final QueryResult<Object> updateDepartmentResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_DEPARTMENT(\$department: uuid!, \$document: jsonb, \$updated_by: uuid, \$updated_at: timestamptz) {
            update_department_by_pk(pk_columns: {department: \$department}, _set: {document: \$document, updated_by: \$updated_by, updated_at: \$updated_at}) {
              department
              document
              updated_by
            }
          }

          ''',
          ),
          variables: <String, dynamic>{
            "department": currentDepartment.department,
            "document": currentDepartment.document,
            "updated_by": CURRENT_EMPLOYEE.value.employee,
            "updated_at": currentDepartment.updated_at.toString(),
          },
        ),
      );

      if (updateDepartmentResult.hasException) {
        throw updateDepartmentResult.exception.toString();
      }
      // _notificationService.success("Update department success");
    } catch (err) {
      _notificationService.error("Department Update failed!");
    }
  }

  Future<void> reorderDepartments() async {
    departmentList.clear();
    for (final DepartmentGroupItem groupItem in majorMinorList) {
      if (groupItem.department != null) {
        departmentList.add(groupItem.department!.value);
      } else {
        for (final Department department in groupItem.departments!) {
          departmentList.add(department);
        }
      }
    }
    for (int i = 0; i < departmentList.length; i++) {
      departmentList[i].document.order = i + 1;
    }

    for (final Department dept in departmentList) {
      await updateDepartment(dept);
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Back Office Settings",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateDepartmentSettings.value = !edit;
  }
}

class DepartmentGroupItem {
  DepartmentGroupItem({
    this.major,
    this.department,
    bool? selected,
    RxList<Department>? departments,
  })  : departments = departments ?? <Department>[].obs,
        selected = selected ?? false;
  DepartmentGroupItem.empty();
  String? major;
  Rx<Department>? department;
  bool? selected;
  RxList<Department>? departments;
}
