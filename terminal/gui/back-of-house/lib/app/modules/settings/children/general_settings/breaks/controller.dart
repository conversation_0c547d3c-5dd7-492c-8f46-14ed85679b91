import 'package:backoffice/app/modules/settings/children/general_settings/controller.dart';
import 'package:backoffice/app/modules/settings/children/hardware/controller.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';

class BreaksController extends GetxController {
  BreaksController({
    this.editBreak,
  });

  Break? editBreak;

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  final GeneralSettingsController _generalSettingsController = Get.find();

  final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

  final TextEditingController descController = TextEditingController();
  final RxBool isPaidController = false.obs;
  final TextEditingController breakMinsController = TextEditingController(text: "0");
  final TextEditingController minsToQualifyController = TextEditingController(text: "0");

  final RxBool loading = false.obs;

  @override
  void onInit() {
    if (editBreak != null) {
      descController.text = editBreak!.desc;
      isPaidController.value = editBreak!.isPaid > 0;
      breakMinsController.text = editBreak!.breakMins.toString();
      minsToQualifyController.text = editBreak!.minsToQualify.toString();
    }
    super.onInit();
  }

  Break buildBreakFromFields() {
    final int index = editBreak == null ? _generalSettingsController.breakList.length : editBreak!.idx;
    return Break(
      idx: index,
      desc: descController.text,
      isPaid: isPaidController.value ? 1 : 0,
      breakMins: int.parse(breakMinsController.text),
      minsToQualify: int.parse(minsToQualifyController.text),
    );
  }

  Future<void> updateBreak() async {
    loading.value = true;

    bool add = false;

    final QueryResult<Object> queryResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          _generalSettingsController.GET_JSON_RECORD_BY_RECORD_KEY_QUERY,
        ),
        variables: const <String, dynamic>{"record_key": "breaks"},
      ),
    );

    final BreaksJsonRecord breaksJsonRecord = (queryResult.data!['json_record'] as List<dynamic>)
        .map(
          (dynamic breakRecord) => BreaksJsonRecord.fromJson(
            breakRecord as Map<String, dynamic>,
          ),
        )
        .toList()
        .first;

    final List<Break> brkList = breaksJsonRecord.document.breaks;

    final Break editBrk = buildBreakFromFields();
    if (editBreak == null) {
      brkList.add(editBrk);
      add = true;
    } else {
      brkList[editBreak!.idx] = editBrk;
    }

    final QueryResult<Object> updateConfigResult = await _graphqlService.client.mutate(
      MutationOptions<Object>(
        document: g.parseString(
          UPDATE_JSON_RECORD,
        ),
        variables: <String, dynamic>{
          "record_key": breaksJsonRecord.record_key,
          "document": breaksJsonRecord.document,
        },
      ),
    );

    if (updateConfigResult.hasException) {
      _notificationService.error("Unable to update breaks");
    } else {
      _notificationService.success(
        add ? "Break Added" : "Break Updated",
      );
    }

    loading.value = false;
    Get.back();
  }
}
