import 'package:backoffice/app/modules/settings/children/general_settings/controller.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';

const String GET_JSON_RECORD_BY_RECORD_KEY_SUBSCRIPTION = '''
    subscription GET_JSON_RECORD (\$record_key: String) {
      json_record(where: {record_key: {_eq: \$record_key}}) {
        record_key
        document
        updated_at
      }
    }
  ''';

class BreaksWidget extends GetView<GeneralSettingsController> {
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Flex(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          ClipRRect(
            clipBehavior: Clip.hardEdge,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: R2Colors.neutral100,
                border: Border.all(
                  color: R2Colors.neutral100,
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(2),
                child: Subscription<Obx>(
                  options: SubscriptionOptions<Obx>(
                    document: g.parseString(
                      GET_JSON_RECORD_BY_RECORD_KEY_SUBSCRIPTION,
                    ),
                    variables: const <String, dynamic>{"record_key": "breaks"},
                  ),
                  builder: (
                    QueryResult<dynamic> result, {
                    dynamic refetch,
                    dynamic fetchMore,
                  }) {
                    if (result.isLoading) {
                      return const Flex(
                        direction: Axis.vertical,
                        children: <Widget>[
                          Center(
                            child: CircularProgressIndicator(),
                          ),
                        ],
                      );
                    }
                    if (result.hasException) {
                      return const Flex(
                        direction: Axis.vertical,
                        children: <Widget>[
                          Center(
                            child: Text("Failed to load Breaks"),
                          ),
                        ],
                      );
                    }

                    final List<BreaksJsonRecord> breaksJsonRecord = (result.data!['json_record'] as List<dynamic>)
                        .map(
                          (dynamic systemDeviceRecord) => BreaksJsonRecord.fromJson(
                            systemDeviceRecord as Map<String, dynamic>,
                          ),
                        )
                        .toList();

                    controller.breakList = breaksJsonRecord.first.document.breaks;

                    return controller.breakList.isEmpty
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 20, 0, 10),
                                child: Text(
                                  "No Breaks",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: R2Colors.neutral500,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : DataTable(
                            dataRowColor: MaterialStateProperty.all(R2Colors.white),
                            headingRowColor: MaterialStateProperty.all(R2Colors.neutral100),
                            showCheckboxColumn: false,
                            columns: const <DataColumn>[
                              DataColumn(
                                label: Text("Break Description"),
                              ),
                              DataColumn(
                                label: Expanded(
                                  child: Center(child: Text("Break Length")),
                                ),
                              ),
                            ],
                            rows: List<DataRow>.generate(controller.breakList.length, (int index) {
                              final Break editBreak = controller.breakList[index];
                              return DataRow(
                                onSelectChanged: controller.canEdit.value ? (bool? value) async => await controller.viewBreak(editBreak) : null,
                                cells: <DataCell>[
                                  DataCell(
                                    Text(
                                      editBreak.desc,
                                      style: TextStyle(
                                        color: controller.canEdit.value ? null : R2Colors.neutral400,
                                      ),
                                    ),
                                  ),
                                  DataCell(
                                    Center(
                                      child: Text(
                                        "${editBreak.breakMins} Minutes",
                                        style: TextStyle(
                                          color: controller.canEdit.value ? null : R2Colors.neutral400,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }),
                          );
                  },
                ),
              ),
            ),
          ),
          TextButton(
            onPressed: controller.canEdit.value && controller.breakList.length < 50
                ? () async {
                    await controller.addBreak();
                  }
                : null,
            child: const Padding(
              padding: EdgeInsets.all(15),
              child: Text(
                "Add Break",
                style: TextStyle(color: R2Colors.primary500),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
