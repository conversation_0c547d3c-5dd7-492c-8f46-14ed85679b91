// ignore_for_file: avoid_dynamic_calls, non_constant_identifier_names

import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/settings/children/general_settings/breaks/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  document
  record_key
  updated_at
''';

const String UPDATE_SYSTEM_SETTINGS = '''
 mutation UPDATE_BANNER_MSG(\$document: jsonb) {
  update_json_record(where: {record_key: {_eq: "systemSetting"}}, _append: {document: \$document}) {
    returning {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
}
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        $JSON_RECORD_FIELDS_FRAGMENT
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String UPDATE_CASHIER = '''
 mutation UPDATE_CASHIER_RECORD(\$document: jsonb) {
  update_json_record(where: {record_key: {_eq: "cashier"}}, _append: {document: \$document}) {
    returning {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
}
''';

class RxHours {
  RxHours({
    required this.open,
    required this.close,
  });

  RxInt open;
  RxInt close;
}

class GeneralSettingsController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final ConfigService _configService = Get.find();
  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();

  final Logger _logger = Logger('GeneralSettingsController');

  final GlobalKey<FormState> generalSettingsKey = GlobalKey<FormState>();
  final GlobalKey<FormState> cashierDialogKey = GlobalKey<FormState>();
  final GlobalKey<FormState> reasonCodeDialogKey = GlobalKey<FormState>();

  TextEditingController dateController = TextEditingController();
  TextEditingController addEmailController = TextEditingController();
  TextEditingController deliveryFeeController = TextEditingController();

  ScrollController scrollController = ScrollController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> emailKey = GlobalKey<FormState>();

  final RxBool canEdit = false.obs;
  final RxBool isLoading = true.obs;
  // Turn online ordering on/off
  final RxBool ecomActive = false.obs;
  // Prints expedite slip when online order is placed
  final RxBool ecomExpedite = false.obs;
  // Prints customer receipt when online order is placed
  final RxBool ecomCustomerReceipt = false.obs;
  // Prints merchant receipt when online order is placed
  final RxBool ecomMerchantReceipt = false.obs;
  // Enable delivery for online orders
  final RxBool deliveryEnabled = false.obs;
  // Delivery fee for online orders
  final RxInt deliveryFee = 0.obs;
  // Show holidays in the open hours section
  final RxBool showHolidays = false.obs;
  // Terminal that handles the online orders when they come in
  final RxInt ecomTerminal = 0.obs;
  // Price level for online orders
  final RxInt ecomPriceLevel = 0.obs;
  final RxList<String> cashierList = <String>[].obs;
  final RxList<String> selectedReportsList = <String>[].obs;
  final RxList<String> emailList = <String>[].obs;
  final RxMap<String, RxList<RxHours>> openHours = <String, RxList<RxHours>>{}.obs;
  final Rx<SystemSettingJsonRecordDocument> systemSettingDocument = SystemSettingJsonRecordDocument.empty().obs;
  final Rx<JsonRecordCashierDocument> cashierDocument = JsonRecordCashierDocument.empty().obs;
  final Rx<ReasonCodeDocument> reasonCodeDocument = ReasonCodeDocument.empty().obs;
  final Rx<EcomSettingJsonRecord> ecomSettingRecord = EcomSettingJsonRecord.empty().obs;
  final RxList<SystemDeviceJsonRecordTerminal> deviceTerminals = <SystemDeviceJsonRecordTerminal>[].obs;
  List<Break> breakList = <Break>[];
  List<PriceLevel> priceLevels = <PriceLevel>[];

  final String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
    query GET_JSON_RECORD (\$record_key: String) {
      json_record(where: {record_key: {_eq: \$record_key}}) {
        record_key
        document
        updated_at
      }
    }
  ''';

  @override
  Future<void> onInit() async {
    await editPermission();
    final SystemSettingJsonRecord? settingsRecord = await getSystemSettings();
    if (settingsRecord != null) {
      systemSettingDocument.value = settingsRecord.document;
      dateController.text = settingsRecord.document.ppdRefDate;
      priceLevels = settingsRecord.document.priceLevels;
    }
    reasonCodeDocument.value = (await getReasonCode()).document;

    final JsonRecordCashier? doc = await getCashierJsonRecord();
    if (doc != null) {
      cashierDocument.value = doc.document;
      cashierList.value = doc.document.cashiers;
    }
    ecomSettingRecord.value = await getEcomSettingJsonRecord();
    ecomActive.value = ecomSettingRecord.value.document.ecomEnabled;
    ecomExpedite.value = ecomSettingRecord.value.document.expediteSlip;
    ecomTerminal.value = ecomSettingRecord.value.document.printTerminal;
    ecomPriceLevel.value = ecomSettingRecord.value.document.ecomPriceLevel;
    ecomCustomerReceipt.value = ecomSettingRecord.value.document.customerReceipt;
    ecomMerchantReceipt.value = ecomSettingRecord.value.document.merchantReceipt;
    deliveryEnabled.value = ecomSettingRecord.value.document.delivery?.enabled ?? false;
    deliveryFee.value = ecomSettingRecord.value.document.delivery?.deliveryFee ?? 0;
    deliveryFeeController.text = (deliveryFee.value / 100.0).toStringAsFixed(2);
    _getHours();

    deviceTerminals.value = (await _identityService.getSystemDeviceDocument()).fold(
      (ServiceError e) {
        _notificationService.error("Failed to get system devices");
        return <SystemDeviceJsonRecordTerminal>[];
      },
      (SystemDeviceJsonRecordDocument d) => d.terminal,
    );

    isLoading.value = false;
    super.onInit();
  }

  void _getHours() {
    final Map<String, List<ActiveWindow>> days = _makeDays(ecomSettingRecord.value.document);
    for (final String key in days.keys) {
      if (days[key] != null) {
        openHours[key] = days[key]!.map((ActiveWindow w) => RxHours(open: w.open.obs, close: w.close.obs)).toList().obs;
      }
    }
  }

  void _makeNewOpenHours() {
    final Map<String, List<ActiveWindow>> days = _makeDays(EcomSettingJsonRecordDocument.empty());
    for (final String key in days.keys) {
      days[key] = openHours[key]!.map((RxHours h) => ActiveWindow(open: h.open.value, close: h.close.value)).toList();
    }
    ecomSettingRecord.value.document.activeHours = ActiveHours(
      mon: days["mon"]!,
      tue: days["tue"]!,
      wed: days["wed"]!,
      thu: days["thu"]!,
      fri: days["fri"]!,
      sat: days["sat"]!,
      sun: days["sun"]!,
      xmas: days["xmas"]!,
      xmasEve: days["xmasEve"]!,
      nwYrs: days["nwYrs"]!,
      nwYrsEve: days["nwYrsEve"]!,
      thanks: days["thanks"]!,
      ind: days["ind"]!,
      memor: days["memor"]!,
      labor: days["labor"]!,
      vets: days["vets"]!,
    );
  }

  Map<String, List<ActiveWindow>> _makeDays(EcomSettingJsonRecordDocument document) {
    document.activeHours ??= ActiveHours.empty();
    return <String, List<ActiveWindow>>{
      "mon": document.activeHours!.mon,
      "tue": document.activeHours!.tue,
      "wed": document.activeHours!.wed,
      "thu": document.activeHours!.thu,
      "fri": document.activeHours!.fri,
      "sat": document.activeHours!.sat,
      "sun": document.activeHours!.sun,
      "xmas": document.activeHours!.xmas,
      "xmasEve": document.activeHours!.xmasEve,
      "nwYrs": document.activeHours!.nwYrs,
      "nwYrsEve": document.activeHours!.nwYrsEve,
      "thanks": document.activeHours!.thanks,
      "ind": document.activeHours!.ind,
      "labor": document.activeHours!.labor,
      "memor": document.activeHours!.memor,
      "vets": document.activeHours!.vets,
      "pres": document.activeHours!.pres,
      "mlk": document.activeHours!.mlk,
    };
  }

  Future<void> updateSettings() async {
    await updateSystemSettings();
    await Get.offAllNamed(
      AppRoutes.SETTINGS,
      id: AppRoutes.id,
    );
  }

  Future<SystemSettingJsonRecord?> getSystemSettings() async {
    return (await _configService.getSystemSettings()).fold(
      (ServiceError e) {
        _notificationService.error("Failed to get system settings!");
        return null;
      },
      (SystemSettingJsonRecord r) => r,
    );
  }

  Future<JsonRecordReasonCode> getReasonCode() async {
    try {
      final QueryResult<Object> getReasonCodeResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_JSON_RECORD_BY_RECORD_KEY_QUERY,
          ),
          variables: const <String, dynamic>{"record_key": "reasonCode"},
        ),
      );

      if (getReasonCodeResult.hasException) {
        throw getReasonCodeResult.exception.toString();
      }

      final List<dynamic> recordList = getReasonCodeResult.data!['json_record'] as List<dynamic>;

      if (recordList.isEmpty) return JsonRecordReasonCode.empty();

      return JsonRecordReasonCode.fromJson(recordList[0] as Map<String, dynamic>);
    } catch (err, stack) {
      _notificationService.error("Failed to Reason Codes");
      _logger.severe("Error getting Reason Codes", err, stack);
      return JsonRecordReasonCode.empty();
    }
  }

  Future<JsonRecordCashier?> getCashierJsonRecord() async {
    try {
      final QueryResult<Object> getCashierResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_JSON_RECORD_BY_RECORD_KEY_QUERY,
          ),
          variables: const <String, dynamic>{"record_key": "cashier"},
        ),
      );

      if (getCashierResult.hasException) {
        throw getCashierResult.exception.toString();
      }

      return JsonRecordCashier.fromJson(
        getCashierResult.data!['json_record'][0] as Map<String, dynamic>,
      );
    } catch (err, stack) {
      _notificationService.error("Failed to load Cashiers!");
      _logger.severe("Error getting Cashiers", err, stack);

      return null;
    }
  }

  Future<EcomSettingJsonRecord> getEcomSettingJsonRecord() async {
    return (await _configService.getEcomConfig()).fold(
      (ServiceError e) {
        _notificationService.error("Failed to get ecom settings!");
        return EcomSettingJsonRecord.empty();
      },
      (EcomSettingJsonRecord r) => r,
    );
  }

  Future<void> updateSystemSettings() async {
    try {
      _makeNewOpenHours();
      ecomSettingRecord.value.document.ecomEnabled = ecomActive.value;
      ecomSettingRecord.value.document.printTerminal = ecomTerminal.value;
      ecomSettingRecord.value.document.ecomPriceLevel = ecomPriceLevel.value;
      ecomSettingRecord.value.document.expediteSlip = ecomExpedite.value;
      ecomSettingRecord.value.document.customerReceipt = ecomCustomerReceipt.value;
      ecomSettingRecord.value.document.merchantReceipt = ecomMerchantReceipt.value;

      // Ensure delivery object exists before setting properties
      ecomSettingRecord.value.document.delivery ??= Delivery.empty();
      ecomSettingRecord.value.document.delivery!.enabled = deliveryEnabled.value;
      ecomSettingRecord.value.document.delivery!.deliveryFee = deliveryFee.value;

      final Map<String, dynamic> santizedConfig = Helpers.sanitizeEntity(
        ecomSettingRecord.value.toJson(),
        <String>['updated_at'],
      );

      final QueryResult<Object> updateEcomResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPSERT_JSON_RECORD_MUTATION,
          ),
          variables: <String, dynamic>{
            "json_record": santizedConfig,
          },
        ),
      );

      if (updateEcomResult.hasException) {
        throw updateEcomResult.exception.toString();
      }

      final QueryResult<Object> updateSettingsResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_SYSTEM_SETTINGS,
          ),
          variables: <String, dynamic>{
            "document": systemSettingDocument,
          },
        ),
      );

      if (updateSettingsResult.hasException) {
        throw updateSettingsResult.exception.toString();
      }

      _notificationService.success("Updates Saved!");
    } catch (err, stack) {
      _notificationService.error("System Settings update failed!");
      _logger.severe("Error updating systemSettings", err, stack);
    }
  }

  Future<void> updateCashierSettings() async {
    cashierDocument.value.cashiers = cashierList;
    try {
      final QueryResult<Object> updateCashierResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_CASHIER,
          ),
          variables: <String, dynamic>{
            "document": cashierDocument.value,
          },
        ),
      );

      if (updateCashierResult.hasException) {
        throw updateCashierResult.exception.toString();
      }

      _notificationService.success("Updates Saved!");
    } catch (err, stack) {
      _notificationService.error("Cashier update failed!");
      _logger.severe("Error updating cashiers", err, stack);
    }
  }

  Future<void> upsertPaidOutReasonCode() async {
    try {
      final JsonRecordReasonCode reasonCodeRecord = JsonRecordReasonCode.empty();
      reasonCodeRecord.document = reasonCodeDocument.value;
      final Map<String, dynamic> santizedConfig = Helpers.sanitizeEntity(
        reasonCodeRecord.toJson(),
        <String>['updated_at'],
      );

      // ignore: always_specify_types
      final QueryResult upsertRecordResult = await _graphqlService.client.mutate(
        // ignore: always_specify_types
        MutationOptions(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": santizedConfig,
          },
        ),
      );

      if (upsertRecordResult.hasException) {
        throw upsertRecordResult.exception.toString();
      }

      _notificationService.success("Updates Saved!");
    } catch (err, stack) {
      _notificationService.error("Reason Code update failed!");
      _logger.severe("Error updating reason codes", err, stack);
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Back Office Settings",
      _graphqlService,
    );
    canEdit.value = edit;
  }

  Future<void> viewBreak(Break brk) async {
    await Get.bottomSheet(
      ThinBottomSheet(child: BreaksDialog(editBreak: brk)),
      isScrollControlled: true,
    );
  }

  Future<void> addBreak() async {
    await Get.bottomSheet(
      ThinBottomSheet(child: BreaksDialog()),
      isScrollControlled: true,
    );
  }

  void existingReasonCode(String code) {
    _notificationService.error("reason code $code already exists");
  }
}
