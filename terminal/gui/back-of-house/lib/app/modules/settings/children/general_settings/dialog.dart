// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/custom_form_field.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_datepicker.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_timepicker.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/modules/settings/children/general_settings/breaks/widget.dart';
import 'package:backoffice/app/modules/settings/children/general_settings/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/enums/pay_period.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class GeneralSettingsPage extends GetView<GeneralSettingsController> {
  final Map<String, String> weekdays = <String, String>{
    "mon": 'Monday',
    "tue": 'Tuesday',
    "wed": 'Wednesday',
    "thu": 'Thursday',
    "fri": 'Friday',
    "sat": 'Saturday',
    "sun": 'Sunday',
    "xmas": 'Christmas',
    "xmasEve": 'Christmas Eve',
    "nwYrs": 'New Years',
    "nwYrsEve": 'New Years Eve',
    "thanks": 'Thanksgiving',
    "ind": 'Independence Day',
    "labor": 'Labor Day',
    "memor": 'Memorial Day',
    "vets": 'Veterans Day',
    "pres": 'Presidents Day',
    "mlk": 'MLK Jr. Day',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Header(
          transparentBackground: false,
          title: "General Settings",
          rightButton: Obx(
            () => DialogButton(
              buttonType: EDialogButtonType.ADD,
              disabled: !controller.canEdit.value,
              onTapped: () async => controller.updateSettings(),
              buttonText: "Update Settings",
            ),
          ),
          leftButton: DialogButton(
            buttonType: EDialogButtonType.BACK,
            buttonText: "Settings",
            onTapped: () async {
              await Get.offAllNamed(
                AppRoutes.SETTINGS,
                id: AppRoutes.id,
              );
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Obx(
          () => controller.isLoading.value
              ? SizedBox(
                  height: Get.height,
                  width: Get.width,
                  child: Center(
                    child: Lottie.asset(
                      'lib/assets/lottie/loading-animation.json',
                      height: 100,
                    ),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.only(bottom: 50),
                  child: Column(
                    children: <Widget>[
                      Obx(
                        () => FormWrapper(
                          maxWidth: 450,
                          formKey: controller.generalSettingsKey,
                          children: <Widget>[
                            MenuGroup(
                              title: "Pay Period",
                              children: <Widget>[
                                MenuDropdown<int>(
                                  title: 'Pay Period Type',
                                  value: controller.systemSettingDocument.value.ppdType,
                                  onChanged: (int? value) {
                                    controller.systemSettingDocument.value.ppdType = value ?? 0;
                                  },
                                  items: PayPeriod.values
                                      .map(
                                        (PayPeriod e) => DropdownMenuItem<int>(
                                          value: e.index,
                                          child: Text(
                                            e.name.toLowerCase().capitalizeFirst!,
                                          ),
                                        ),
                                      )
                                      .toList(),
                                ),
                                MenuDatePicker(
                                  title: 'Pay Period Refference Date',
                                  controller: controller.dateController,
                                  onChanged: (String value) {
                                    controller.systemSettingDocument.value.ppdRefDate = value;
                                  },
                                  dateMask: "yyyy-MM-dd",
                                ),
                              ],
                            ),
                            MenuGroup(
                              title: "Online Ordering",
                              children: <Widget>[
                                MenuCheckbox(
                                  text: 'Online Sales Active',
                                  value: controller.ecomActive.value,
                                  onChanged: (bool? v) {
                                    controller.ecomActive.value = v ?? false;
                                  },
                                ),
                                MenuCheckbox(
                                  text: 'Enable Delivery',
                                  value: controller.deliveryEnabled.value,
                                  onChanged: (bool? v) {
                                    controller.deliveryEnabled.value = v ?? false;
                                  },
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    const MenuLabel(text: 'Delivery Fee'),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: <Widget>[
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: R2Colors.neutral300),
                                            borderRadius: const BorderRadius.only(
                                              topLeft: Radius.circular(4),
                                              bottomLeft: Radius.circular(4),
                                            ),
                                          ),
                                          child: const Text(
                                            '\$',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: R2Colors.neutral600,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: CustomFormField(
                                            controller: controller.deliveryFeeController,
                                            hint: '0.00',
                                            suffixText: 'USD',
                                            inputPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                            onChanged: (String value) {
                                              final double? parsedValue = double.tryParse(value);
                                              if (parsedValue != null && parsedValue >= 0) {
                                                controller.deliveryFee.value = (parsedValue * 100).round();
                                              }
                                            },
                                            validator: (dynamic value) {
                                              final String? stringValue = value?.toString();
                                              if (stringValue != null && stringValue.isNotEmpty) {
                                                final double? parsedValue = double.tryParse(stringValue);
                                                if (parsedValue == null) {
                                                  return 'Please enter a valid amount';
                                                }
                                                if (parsedValue < 0) {
                                                  return 'Delivery fee cannot be negative';
                                                }
                                                if (parsedValue > 999.99) {
                                                  return 'Delivery fee cannot exceed \$999.99';
                                                }
                                              }
                                              return null;
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                MenuDropdown<int>(
                                  title: "Online Price Level",
                                  value: controller.ecomPriceLevel.value,
                                  items: <DropdownMenuItem<int>>[
                                    const DropdownMenuItem<int>(
                                      value: 0,
                                      child: Text("Default"),
                                    ),
                                    ...controller.priceLevels.map(
                                      (PriceLevel pl) => DropdownMenuItem<int>(
                                        value: pl.idx,
                                        child: Text(pl.desc),
                                      ),
                                    ),
                                  ],
                                  onChanged: (int? value) {
                                    controller.ecomPriceLevel.value = value ?? controller.ecomPriceLevel.value;
                                  },
                                ),
                                MenuDropdown<int>(
                                  title: "Printing Terminal",
                                  value: controller.ecomTerminal.value,
                                  items: controller.deviceTerminals
                                      .map(
                                        (SystemDeviceJsonRecordTerminal t) => DropdownMenuItem<int>(
                                          value: t.idx,
                                          child: Text(t.desc),
                                        ),
                                      )
                                      .toList(),
                                  onChanged: (int? value) {
                                    controller.ecomTerminal.value = value ?? controller.ecomTerminal.value;
                                  },
                                ),
                                MenuCheckbox(
                                  text: 'Print Expedite Slip',
                                  value: controller.ecomExpedite.value,
                                  onChanged: (bool? v) {
                                    controller.ecomExpedite.value = v ?? false;
                                  },
                                ),
                                MenuCheckbox(
                                  text: 'Print Customer Receipt',
                                  value: controller.ecomCustomerReceipt.value,
                                  onChanged: (bool? v) {
                                    controller.ecomCustomerReceipt.value = v ?? false;
                                  },
                                ),
                                MenuCheckbox(
                                  text: 'Print Merchant Receipt',
                                  value: controller.ecomMerchantReceipt.value,
                                  onChanged: (bool? v) {
                                    controller.ecomMerchantReceipt.value = v ?? false;
                                  },
                                ),
                                ...getDayRows(controller.openHours, weekdays, false),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    const MenuLabel(
                                      text: "Show Holidays",
                                      textColor: R2Colors.neutral500,
                                    ),
                                    IconButton(
                                      onPressed: () => controller.showHolidays.toggle(),
                                      icon: Icon(
                                        controller.showHolidays.value ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                                        color: R2Colors.neutral500,
                                      ),
                                    ),
                                  ],
                                ),
                                if (controller.showHolidays.value) ...getDayRows(controller.openHours, weekdays, true),
                              ],
                            ),
                            MenuGroup(
                              title: "Cashier Maintenance",
                              children: <Widget>[
                                ListView.separated(
                                  separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                                  shrinkWrap: true,
                                  itemCount: controller.cashierList.length,
                                  itemBuilder: (BuildContext context, int index) {
                                    return ListTile(
                                      title: Text(
                                        controller.cashierList[index],
                                      ),
                                      trailing: IconButton(
                                        onPressed: () async {
                                          controller.cashierList.removeAt(index);
                                          await controller.updateCashierSettings();
                                        },
                                        icon: const FaIcon(
                                          FontAwesomeIcons.solidCircleMinus,
                                          color: R2Colors.red500,
                                          size: 18,
                                        ),
                                      ),
                                      onTap: () async {
                                        final TextEditingController dialogController = TextEditingController();
                                        dialogController.text = controller.cashierList[index];
                                        await Get.dialog(
                                          AlertDialog(
                                            backgroundColor: R2Colors.neutral200,
                                            title: const Center(
                                              child: Text("Cashier Title"),
                                            ),
                                            content: Material(
                                              color: R2Colors.neutral200,
                                              child: FormWrapper(
                                                formKey: controller.cashierDialogKey,
                                                children: <Widget>[
                                                  Padding(
                                                    padding: const EdgeInsets.only(
                                                      top: 20,
                                                    ),
                                                    child: CustomFormField(
                                                      inputPadding: const EdgeInsets.only(
                                                        right: 10,
                                                      ),
                                                      controller: dialogController,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            actionsAlignment: MainAxisAlignment.spaceAround,
                                            actions: <Widget>[
                                              MaterialButton(
                                                minWidth: 50,
                                                height: 70,
                                                onPressed: () => Get.back(
                                                  result: dialogController.text,
                                                ),
                                                child: const Text(
                                                  "back",
                                                  style: TextStyle(
                                                    color: R2Colors.red500,
                                                  ),
                                                ),
                                              ),
                                              MaterialButton(
                                                minWidth: 50,
                                                height: 70,
                                                onPressed: () => Get.back(
                                                  result: dialogController.text,
                                                ),
                                                child: const Text(
                                                  "confirm",
                                                  style: TextStyle(
                                                    color: R2Colors.primary500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ).then((dynamic value) async {
                                          if (value != null) {
                                            controller.cashierList[index] = value.toString();
                                            await controller.updateCashierSettings();
                                          }
                                        });
                                      },
                                    );
                                  },
                                ),
                                MenuButton(
                                  title: 'Add',
                                  onPressed: () async {
                                    final TextEditingController dialogController = TextEditingController();
                                    await Get.dialog(
                                      AlertDialog(
                                        backgroundColor: R2Colors.neutral200,
                                        title: const Center(
                                          child: Text("Cashier Title"),
                                        ),
                                        content: Material(
                                          color: R2Colors.neutral200,
                                          child: FormWrapper(
                                            formKey: controller.cashierDialogKey,
                                            children: <Widget>[
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  top: 20,
                                                ),
                                                child: CustomFormField(
                                                  controller: dialogController,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        actionsAlignment: MainAxisAlignment.spaceAround,
                                        actions: <Widget>[
                                          MaterialButton(
                                            minWidth: 50,
                                            height: 70,
                                            onPressed: () => Get.back(),
                                            child: const Text(
                                              "back",
                                              style: TextStyle(
                                                color: R2Colors.red500,
                                              ),
                                            ),
                                          ),
                                          MaterialButton(
                                            minWidth: 50,
                                            height: 70,
                                            onPressed: () => Get.back(
                                              result: dialogController.text,
                                            ),
                                            child: const Text(
                                              "confirm",
                                              style: TextStyle(
                                                color: R2Colors.primary500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ).then((dynamic value) async {
                                      if (value != null) {
                                        controller.cashierList.add(value.toString());
                                        await controller.updateCashierSettings();
                                      }
                                    });
                                  },
                                ),
                              ],
                            ),
                            MenuGroup(
                              title: "Paid Out Reason Codes",
                              children: <Widget>[
                                Obx(
                                  () => ListView.separated(
                                    separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                                    shrinkWrap: true,
                                    itemCount: controller.reasonCodeDocument.value.paidOut.length,
                                    itemBuilder: (BuildContext context, int index) {
                                      return ListTile(
                                        title: Text(
                                          controller.reasonCodeDocument.value.paidOut[index],
                                        ),
                                        trailing: IconButton(
                                          onPressed: () async {
                                            controller.reasonCodeDocument.value.paidOut.removeAt(index);
                                            await controller.upsertPaidOutReasonCode();
                                            controller.reasonCodeDocument.refresh();
                                          },
                                          icon: const FaIcon(
                                            FontAwesomeIcons.solidCircleMinus,
                                            color: R2Colors.red500,
                                            size: 18,
                                          ),
                                        ),
                                        onTap: () async {
                                          final TextEditingController dialogController = TextEditingController();
                                          dialogController.text = controller.reasonCodeDocument.value.paidOut[index];
                                          await Get.dialog(
                                            AlertDialog(
                                              backgroundColor: R2Colors.neutral200,
                                              title: const Center(
                                                child: Text("Reason Code"),
                                              ),
                                              content: Material(
                                                color: R2Colors.neutral200,
                                                child: FormWrapper(
                                                  formKey: controller.reasonCodeDialogKey,
                                                  children: <Widget>[
                                                    Padding(
                                                      padding: const EdgeInsets.only(
                                                        top: 20,
                                                      ),
                                                      child: CustomFormField(
                                                        inputPadding: const EdgeInsets.only(
                                                          right: 10,
                                                        ),
                                                        controller: dialogController,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              actionsAlignment: MainAxisAlignment.spaceAround,
                                              actions: <Widget>[
                                                MaterialButton(
                                                  minWidth: 50,
                                                  height: 70,
                                                  onPressed: () => Get.back(
                                                    result: dialogController.text,
                                                  ),
                                                  child: const Text(
                                                    "back",
                                                    style: TextStyle(
                                                      color: R2Colors.red500,
                                                    ),
                                                  ),
                                                ),
                                                MaterialButton(
                                                  minWidth: 50,
                                                  height: 70,
                                                  onPressed: () => Get.back(
                                                    result: dialogController.text,
                                                  ),
                                                  child: const Text(
                                                    "confirm",
                                                    style: TextStyle(
                                                      color: R2Colors.primary500,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ).then((dynamic value) async {
                                            if (value != null) {
                                              controller.reasonCodeDocument.value.paidOut[index] = value.toString();
                                              await controller.upsertPaidOutReasonCode();
                                              controller.reasonCodeDocument.refresh();
                                            }
                                          });
                                        },
                                      );
                                    },
                                  ),
                                ),
                                MenuButton(
                                  title: 'Add',
                                  onPressed: () async {
                                    final TextEditingController dialogController = TextEditingController();
                                    await Get.dialog(
                                      AlertDialog(
                                        backgroundColor: R2Colors.neutral200,
                                        title: const Center(
                                          child: Text("Reason Code"),
                                        ),
                                        content: Material(
                                          color: R2Colors.neutral200,
                                          child: FormWrapper(
                                            formKey: controller.reasonCodeDialogKey,
                                            children: <Widget>[
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                  top: 20,
                                                ),
                                                child: CustomFormField(
                                                  controller: dialogController,
                                                  inputPadding: const EdgeInsets.only(right: 10),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        actionsAlignment: MainAxisAlignment.spaceAround,
                                        actions: <Widget>[
                                          MaterialButton(
                                            minWidth: 50,
                                            height: 70,
                                            onPressed: () => Get.back(),
                                            child: const Text(
                                              "back",
                                              style: TextStyle(
                                                color: R2Colors.red500,
                                              ),
                                            ),
                                          ),
                                          MaterialButton(
                                            minWidth: 50,
                                            height: 70,
                                            onPressed: () => Get.back(
                                              result: dialogController.text,
                                            ),
                                            child: const Text(
                                              "confirm",
                                              style: TextStyle(
                                                color: R2Colors.primary500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ).then((dynamic value) async {
                                      if (value != null) {
                                        // Convert the value to lowercase for case-insensitive comparison
                                        final String lowerCaseValue = value.toString().toLowerCase();

                                        // Check if the value already exists in the list
                                        final bool alreadyExists =
                                            controller.reasonCodeDocument.value.paidOut.any((String code) => code.toLowerCase() == lowerCaseValue);

                                        if (!alreadyExists) {
                                          controller.reasonCodeDocument.value.paidOut.add(value.toString());
                                          await controller.upsertPaidOutReasonCode();
                                          controller.reasonCodeDocument.refresh();
                                        } else {
                                          controller.existingReasonCode(value.toString());
                                          // Handle the case where the value already exists (optional)
                                        }
                                      }
                                    });
                                  },
                                ),
                              ],
                            ),
                            MenuGroup(
                              title: "TimeClock Breaks",
                              children: <Widget>[
                                BreaksWidget(),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}

Iterable<Widget> getDayRows(Map<String, RxList<RxHours>> openHours, Map<String, String> weekdays, bool holidays) {
  return (holidays ? openHours.entries.toList().sublist(7) : openHours.entries.take(7)).map(
    (MapEntry<String, List<RxHours>> listMap) => Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(child: MenuLabel(text: weekdays[listMap.key]!)),
            IconButton(
              onPressed: () {
                openHours[listMap.key]!.add(RxHours(open: 0.obs, close: 0.obs));
              },
              icon: const Icon(
                Icons.add,
                color: R2Colors.primary500,
              ),
            ),
          ],
        ),
        ...listMap.value.asMap().entries.map(
              (MapEntry<int, RxHours> hoursMap) => Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  Expanded(
                    child: MenuTimePicker(
                      milliseconds: hoursMap.value.open,
                    ),
                  ),
                  const Text("to"),
                  Expanded(
                    child: MenuTimePicker(
                      milliseconds: hoursMap.value.close,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      openHours[listMap.key]!.removeAt(hoursMap.key);
                    },
                    icon: const Icon(
                      Icons.close,
                      color: R2Colors.red500,
                    ),
                  ),
                ],
              ),
            ),
      ],
    ),
  );
}
