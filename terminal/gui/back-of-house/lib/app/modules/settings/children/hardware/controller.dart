// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/settings/children/hardware/prep_devices/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_SYSTEM_SETTING_QUERY = '''
 query GET_SYSTEM_SETTING {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String GET_HARDWARE_SETTING_QUERY = '''
    query GET_HARDWARE_JSON_RECORD {
      json_record(where: {record_key: {_eq: "hardware"}}) {
        record_key
        updated_at
        document
      }
    }
  ''';

const String UPDATE_BANNER_MSG = '''
 mutation UPDATE_BANNER_MSG(\$_eq: String, \$document: jsonb) {
  update_json_record(where: {record_key: {_eq: \$_eq}}, _append: {document: \$document}) {
    returning {
      document
      record_key
      updated_at
    }
  }
}
''';

const String UPDATE_JSON_RECORD = '''
      mutation UPDATE_JSON_RECORD(\$record_key: String!, \$document: jsonb) {
        update_json_record_by_pk(pk_columns: {record_key: \$record_key}, _set: {document: \$document}) {
          record_key
          updated_at
          document
        }
      }
  ''';

class HardwareController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  // ignore: non_constant_identifier_names
  final String GET_SYSTEM_DEVICES_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

  final Logger _logger = Logger('HardwareController');

  final GlobalKey<FormState> hardwareSettingsKey = GlobalKey<FormState>();

  Rx<HardwareJsonRecord> hardwareJsonRecord = HardwareJsonRecord.empty().obs;

  final TextEditingController bannerMessageController = TextEditingController();
  final TextEditingController scalePortController = TextEditingController();

  final TextEditingController newTareDescController = TextEditingController();
  final TextEditingController newTareWeightController = TextEditingController();
  final TextEditingController newTareUOMController = TextEditingController();

  final TextEditingController tareDescController = TextEditingController();
  final TextEditingController tareWeightController = TextEditingController();
  final TextEditingController tareUOMController = TextEditingController();

  RxBool canEdit = false.obs;
  RxBool canUpdateHardwareSettings = false.obs;
  RxBool onScreenKeyboard = true.obs;

  Rx<SystemSettingJsonRecordDocument> systemSettingDocument = SystemSettingJsonRecordDocument.empty().obs;

  RxBool enableTare = false.obs;
  RxBool isUsbSerial = true.obs;

  List<PrepDevice> deviceList = <PrepDevice>[];

  List<SystemDeviceJsonRecordTerminal> terminalsList = <SystemDeviceJsonRecordTerminal>[];

  @override
  Future<void> onInit() async {
    await editPermission();
    await getHardwareJsonRecord();
    await getSystemSettings();
    super.onInit();
  }

  Future<void> updateSettings() async {
    await updateSystemSettings();
    await updateHardwareSettings();
    await Get.offAllNamed(
      AppRoutes.SETTINGS,
      id: AppRoutes.id,
    );
  }

  Future<void> getHardwareJsonRecord() async {
    try {
      final QueryResult<Object> configResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_HARDWARE_SETTING_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if (configResult.hasException) throw "No Configs Found";

      hardwareJsonRecord.value = HardwareJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );

      onScreenKeyboard.value = hardwareJsonRecord.value.document.onScreenKeyboard;
    } catch (err, stack) {
      _notificationService.error("Couldn't load hardware settings!");
      _logger.severe("Error getting hardware config", err, stack);
    }
  }

  Future<void> getSystemSettings() async {
    try {
      final QueryResult<Object> getSystemSettingResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_SYSTEM_SETTING_QUERY,
          ),
        ),
      );

      if (getSystemSettingResult.hasException) {
        throw getSystemSettingResult.exception.toString();
      }

      systemSettingDocument.value = SystemSettingJsonRecordDocument.fromJson(
        getSystemSettingResult.data!['json_record'][0]['document'] as Map<String, dynamic>,
      );

      bannerMessageController.text = systemSettingDocument.value.bannerMsg;
      enableTare.value = systemSettingDocument.value.tareUOM;
    } catch (err, stack) {
      _notificationService.error("Failed to load system settings!");
      _logger.severe("Error Loading system settings", err, stack);
    }
  }

  ///
  ///update functions
  ///

  Future<void> updateHardwareSettings() async {
    try {
      if (!hardwareSettingsKey.currentState!.validate()) throw "Invalid form";

      hardwareJsonRecord.value.document.onScreenKeyboard = onScreenKeyboard.value;
      hardwareJsonRecord.value.updated_at = DateTime.now().toUtc();

      final QueryResult<Object> updateConfigResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_JSON_RECORD,
          ),
          variables: <String, dynamic>{
            "record_key": hardwareJsonRecord.value.record_key,
            "document": hardwareJsonRecord.value.document,
          },
        ),
      );

      if (updateConfigResult.hasException) {
        throw updateConfigResult.exception.toString();
      }

      _notificationService.success("Hardware Settings updated!");
      Get.back();
    } catch (err, stack) {
      _notificationService.error("Hardware Settings update failed!");
      _logger.severe("Error updating hardware settings", err, stack);
    }
  }

  Future<void> updateSystemSettings() async {
    try {
      systemSettingDocument.value.bannerMsg = bannerMessageController.text;

      final QueryResult<Object> updateBannerResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_BANNER_MSG,
          ),
          variables: <String, dynamic>{
            "_eq": "systemSetting",
            "document": systemSettingDocument,
          },
        ),
      );

      if (updateBannerResult.hasException) {
        throw updateBannerResult.exception.toString();
      }
    } catch (err, stack) {
      _notificationService.error("System Settings update failed!");
      _logger.severe("Error updating system settings", err, stack);
    }
  }

  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Back Office Settings",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateHardwareSettings.value = !edit;
  }

  Future<void> viewDevice(PrepDevice device) async {
    await Get.bottomSheet(
      ThinBottomSheet(child: PrepDevicesDialog(device: device)),
      isScrollControlled: true,
    );
  }

  Future<void> addDevice() async {
    await Get.bottomSheet(
      ThinBottomSheet(child: PrepDevicesDialog()),
      isScrollControlled: true,
    );
  }
}
