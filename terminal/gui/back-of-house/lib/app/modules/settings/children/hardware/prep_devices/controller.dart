import 'package:backoffice/app/data/services/department.service.dart';
import 'package:backoffice/app/data/services/item_service.dart';
import 'package:backoffice/app/modules/settings/children/hardware/controller.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_SECTIONS_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "sections"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  record_key
  updated_at
  document
''';

class PrepDeviceController extends GetxController {
  PrepDeviceController({
    this.device,
  });

  PrepDevice? device;

  final Logger _logger = Logger("PrepDeviceController");

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final DepartmentService _departmentService = Get.find();
  final ItemService _itemService = Get.find();
  final HardwareController hardwareController = Get.find();

  final GlobalKey<FormState> deviceFormKey = GlobalKey<FormState>();

  final TextEditingController descController = TextEditingController();
  final TextEditingController ipController1 = TextEditingController();
  final TextEditingController ipController2 = TextEditingController();
  final TextEditingController ipController3 = TextEditingController();
  final TextEditingController ipController4 = TextEditingController();

  final RxInt typeController = 0.obs;
  final RxInt terminalController = 0.obs;
  final RxInt reRouteController = 0.obs;

  final RxBool loading = false.obs;

  // ignore: non_constant_identifier_names
  late String GET_SYSTEM_DEVICES_QUERY;

  @override
  void onInit() {
    GET_SYSTEM_DEVICES_QUERY = hardwareController.GET_SYSTEM_DEVICES_QUERY;
    if (device != null) {
      descController.text = device!.desc;
      typeController.value = device!.type;
      terminalController.value = device!.rcptTerm;
      reRouteController.value = device!.reRoute;
      if (".".allMatches(device!.IP).length == 3) {
        final List<String> strList = device!.IP.split(".");
        if (strList.length > 3) {
          ipController1.text = strList[0];
          ipController2.text = strList[1];
          ipController3.text = strList[2];
          ipController4.text = strList[3];
        }
      }
    } else {
      reRouteController.value = hardwareController.deviceList.length;
    }
    super.onInit();
  }

  PrepDevice buildDeviceFromFields() {
    final int index = device == null ? hardwareController.deviceList.length : device!.idx;
    return PrepDevice(
      idx: index,
      desc: descController.text,
      type: typeController.value,
      IP: "${ipController1.text}.${ipController2.text}.${ipController3.text}.${ipController4.text}",
      rcptTerm: terminalController.value,
      reRoute: reRouteController.value,
    );
  }

  Future<void> updateDevice({
    bool delete = false,
  }) async {
    try {
      loading.value = true;

      bool add = false;

      final QueryResult<Object> queryResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_SYSTEM_DEVICES_QUERY,
          ),
        ),
      );

      final SystemDeviceJsonRecord systemDeviceJsonRecord = (queryResult.data!['json_record'] as List<dynamic>)
          .map(
            (dynamic systemDeviceRecord) => SystemDeviceJsonRecord.fromJson(
              systemDeviceRecord as Map<String, dynamic>,
            ),
          )
          .toList()
          .first;

      final List<PrepDevice> prepList = systemDeviceJsonRecord.document.prep;

      String updateMsg = "";
      if (delete && device != null) {
        /// Remove deleted printers from the department they are apart of.
        final List<Department> departments = await _departmentService.getDepartments();
        for (final Department dept in departments) {
          bool updated = false;
          for (int i = device!.idx; i < prepList.length; i++) {
            if (dept.document.prep & (1 << i) == (1 << i)) {
              updated = true;
              dept.document.prep = dept.document.prep ^ (1 << i);
              if (i > device!.idx) {
                if (dept.document.prep & (1 << (i - 1)) != (1 << (i - 1))) {
                  dept.document.prep = dept.document.prep ^ (1 << (i - 1));
                }
              }
            }
          }
          if (updated) await _departmentService.updateDepartment(dept);
        }

        /// Remove deleted printers from the department they are apart of.
        final List<Item> items = await _itemService.getAllItems();
        for (final Item item in items) {
          bool updated = false;
          for (int i = device!.idx; i < prepList.length; i++) {
            if (item.document.prep & (1 << i) == (1 << i)) {
              updated = true;
              item.document.prep = item.document.prep ^ (1 << i);
              if (i > device!.idx) {
                if (item.document.prep & (1 << (i - 1)) != (1 << (i - 1))) {
                  item.document.prep = item.document.prep ^ (1 << (i - 1));
                }
              }
            }
          }

          if (updated) await _itemService.updateItem(item);
        }

        /// Remove deleted printers from the sections they are apart of.
        /// Updates printers indexes that are above the deleted printers.
        final List<Section> sections = await getSectionsConfig();
        bool updated = false;
        for (final Section section in sections) {
          for (final PrinterSchedule ps in section.printerSchedule) {
            final int toPrinterId = ps.toPrinter;
            final int fromPrinterId = ps.fromPrinter;
            if (toPrinterId == device!.idx) {
              updated = true;
              ps.toPrinter = -1;
            } else if (toPrinterId > device!.idx) {
              updated = true;
              ps.toPrinter = toPrinterId - 1;
            }
            if (fromPrinterId == device!.idx) {
              updated = true;
              ps.fromPrinter = -1;
            } else if (fromPrinterId > device!.idx) {
              updated = true;
              ps.fromPrinter = fromPrinterId - 1;
            }
          }
        }
        if (updated) {
          await updateSections(sections);
          updateMsg = "\n One or more kitchen printer schedules have been modified.";
        }

        prepList.removeAt(device!.idx);
        for (int i = device!.idx; i < prepList.length; i++) {
          prepList[i].idx -= 1;
        }
        for (final PrepDevice p in prepList) {
          if (p.reRoute == device!.idx + 1) {
            p.reRoute = 0;
          } else if (p.reRoute > device!.idx + 1) {
            p.reRoute -= 1;
          }
        }
      } else {
        final PrepDevice editDevice = buildDeviceFromFields();

        if (device == null) {
          prepList.add(editDevice);
          add = true;
        } else {
          if (editDevice.type != 1) {
            for (final PrepDevice pd in prepList) {
              if (pd.reRoute == device!.idx + 1) pd.reRoute = 0;
            }
          }

          prepList[device!.idx] = editDevice;
        }
      }

      final QueryResult<Object> updateConfigResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_JSON_RECORD,
          ),
          variables: <String, dynamic>{
            "record_key": systemDeviceJsonRecord.record_key,
            "document": systemDeviceJsonRecord.document,
          },
        ),
      );

      if (updateConfigResult.hasException) {
        _notificationService.error("Unable to update devices");
      } else {
        _notificationService.success(
          delete
              ? "Device Deleted$updateMsg"
              : add
                  ? "Device Added"
                  : "Device Updated",
        );
      }

      loading.value = false;
      Get.back();
    } catch (e, stack) {
      loading.value = false;
      _notificationService.error("Error to updating devices");
      _logger.severe(
        "Error updating device",
        e,
        stack,
      );
    }
  }

  Future<List<Section>> getSectionsConfig() async {
    try {
      final QueryResult<Object> configResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_SECTIONS_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SectionsJsonRecord sectionsRecord = configList
            .map(
              (dynamic config) => SectionsJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        return sectionsRecord.document.sections;
      }
    } catch (err, stack) {
      _notificationService.error("Error Getting Sections");
      _logger.severe(
        "Error getting sections config.",
        err,
        stack,
      );
    }
    return <Section>[];
  }

  ///
  ///
  Future<void> updateSections(List<Section> sections) async {
    final Map<String, dynamic> sectionsObject = SectionsJsonRecord(
      record_key: "sections",
      updated_at: DateTime.now(),
      document: SectionsJsonRecordDocument(
        sections: sections,
      ),
    ).toJson();

    sectionsObject.removeWhere((String key, dynamic value) => key == "updated_at");

    final QueryResult<Object?> objectResult = await _graphqlService.client.mutate(
      MutationOptions<Object?>(
        document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
        variables: <String, dynamic>{
          "json_record": sectionsObject,
        },
      ),
    );

    if (objectResult.hasException) {
      throw objectResult.exception.toString();
    }
  }
}
