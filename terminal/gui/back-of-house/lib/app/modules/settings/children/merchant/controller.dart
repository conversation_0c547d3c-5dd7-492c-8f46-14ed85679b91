// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/enums/payment.dart';
import 'package:desktop/app/data/enums/prep_print_sizing.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class MerchantController extends GetxController {
  final GraphqlService _graphqlService = Get.find();

  final NotificationService _notificationService = Get.find();

  final Logger _logger = Logger('MerchantController');

  // final SyncService _syncService = Get.find();

  final GlobalKey<FormState> merchantSettingsKey = GlobalKey<FormState>();

  SalesTaxJsonRecord salesTaxRecords = SalesTaxJsonRecord.empty();

  final Rx<MerchantJsonRecord> merchantConfig = MerchantJsonRecord.empty().obs;
  final RxList<Tax> taxList = <Tax>[].obs;

  final TextEditingController idxController = TextEditingController();
  final TextEditingController descController = TextEditingController();
  final TextEditingController isVATController = TextEditingController();
  final TextEditingController taxPercentController = TextEditingController();
  final TextEditingController dualPriceController = TextEditingController();
  final TextEditingController preAuthController = TextEditingController();
  final TextEditingController clientIdController = TextEditingController();
  final TextEditingController locationIdController = TextEditingController();
  final TextEditingController terminalIdController = TextEditingController();
  final TextEditingController initiatorIdController = TextEditingController();
  final TextEditingController initatorPassController = TextEditingController();
  final TextEditingController integrationAuthController = TextEditingController();
  final TextEditingController integrationPassController = TextEditingController();
  final TextEditingController surchargeController = TextEditingController();
  final TextEditingController legacyGiftNameController = TextEditingController();
  final TextEditingController tipLimitController = TextEditingController();

  final RxBool ebtEnabled = false.obs;
  final RxBool tippingEnabled = false.obs;
  final RxBool giftcardsEnabled = false.obs;
  final RxBool dualPricingEnabled = false.obs;
  final RxBool canEdit = false.obs;
  final RxBool canDelete = false.obs;
  final RxBool canUpdateMerchantSettings = false.obs;
  final RxBool refundReceiptSignatureLine = false.obs;
  final RxBool houseAccounts = false.obs;
  final RxBool legacyGift = false.obs;
  final RxBool cashConfirmation = false.obs;
  final RxBool printMerchantOnCash = true.obs;
  final RxBool printMerchantOnNonCash = true.obs;
  final RxBool printMerchantOnHouse = true.obs;
  final RxBool prepOnSaleChange = true.obs;
  final RxBool prepOnSignOut = true.obs;
  final RxBool prepOnCancel = true.obs;
  final RxBool tipLinesOnCustCopy = true.obs;
  final RxBool multiCashDrawers = false.obs;
  final RxBool commentRowsOnReceipt = false.obs;
  final RxBool saleDescOnReceipt = false.obs;
  final RxBool paidOutPrintCustomer = false.obs;
  final RxBool condensedAuthSlip = false.obs;
  final RxBool defaultRefundMedia = false.obs;
  final RxBool viewTablesOnSignOn = false.obs;

  final RxInt takeoutSurcharge = 0.obs;
  final RxInt roundAmount = 1.obs;
  final RxInt orderTypePrintOpt = 0.obs;
  final RxInt customerCopyPrintOpt = 0.obs;
  final RxInt overlappingScheduleOpt = 0.obs;
  final RxInt paymentDeviceType = PaymentProvider.Unknown.index.obs;
  final RxInt prepPrintSizing = PrepPrintSizing.Default.index.obs;
  final RxBool printPrepFooter = false.obs;
  final RxBool markModifiersRed = false.obs;
  final RxBool markCommentsRed = false.obs;
  final RxBool markToGoRed = false.obs;
  final RxBool markOrderTypeRed = false.obs;
  final RxBool markPromisedTimeRed = false.obs;

  final GlobalKey<FormState> taxFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> dpFormKey = GlobalKey<FormState>();

  final RxList<String> receiptHeader = <String>[].obs;

  final ScrollController scrollController = ScrollController();

  MerchantJsonRecord preserveMerchConfig = MerchantJsonRecord.empty();

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    _logger.info("${CURRENT_EMPLOYEE.value.employee_full_name} viewing merchant config");
    await editPermission();
    await getMerchantConfig();
    await getSalesTaxConfig();

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getMerchantConfig() async {
    final QueryResult<Object> configResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
        ),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    // ignore: always_specify_types
    if ((configResult.data!['json_record']! as List).isEmpty) throw "No Configs Found";

    merchantConfig.value = MerchantJsonRecord.fromJson(configResult.data!['json_record'][0] as Map<String, dynamic>);
    //check for equality on update
    preserveMerchConfig = MerchantJsonRecord.fromJson(configResult.data!['json_record'][0] as Map<String, dynamic>);

    dualPriceController.text = merchantConfig.value.document.dualPricingPercent.toString();
    ebtEnabled.value = merchantConfig.value.document.ebt;
    tippingEnabled.value = merchantConfig.value.document.tipping;
    giftcardsEnabled.value = merchantConfig.value.document.giftcards;
    dualPricingEnabled.value = merchantConfig.value.document.dualPricing;
    receiptHeader.value = merchantConfig.value.document.receiptHeader;
    refundReceiptSignatureLine.value = merchantConfig.value.document.refundReceiptSignatureLine;
    houseAccounts.value = merchantConfig.value.document.house;
    legacyGift.value = merchantConfig.value.document.legacyGiftTender;
    takeoutSurcharge.value = merchantConfig.value.document.takeOutSurchargeAmt;
    cashConfirmation.value = merchantConfig.value.document.cashConfirmation;
    printMerchantOnCash.value = merchantConfig.value.document.printMerchantOnCash;
    printMerchantOnNonCash.value = merchantConfig.value.document.printMerchantOnNonCash;
    printMerchantOnHouse.value = merchantConfig.value.document.printMerchantOnHouse;
    prepOnSaleChange.value = merchantConfig.value.document.prepOnSaleChange;
    prepOnSignOut.value = merchantConfig.value.document.prepOnSignOut;
    prepOnCancel.value = merchantConfig.value.document.prepOnCancel;
    preAuthController.text = "\$${Helpers.formatCurrency(merchantConfig.value.document.preAuthAmount)}";
    clientIdController.text = merchantConfig.value.document.giftCardProgram?.clientId ?? "";
    locationIdController.text = merchantConfig.value.document.giftCardProgram?.locationId ?? "";
    terminalIdController.text = merchantConfig.value.document.giftCardProgram?.terminalId ?? "";
    initiatorIdController.text = merchantConfig.value.document.giftCardProgram?.initiatorId ?? "";
    initatorPassController.text = merchantConfig.value.document.giftCardProgram?.initiatorPass ?? "";
    integrationAuthController.text = merchantConfig.value.document.giftCardProgram?.integrationAuth ?? "";
    integrationPassController.text = merchantConfig.value.document.giftCardProgram?.integrationPass ?? "";
    roundAmount.value = merchantConfig.value.document.dualPricingRoundAmount;
    surchargeController.text = "\$${Helpers.formatCurrency(merchantConfig.value.document.takeOutSurchargeAmt)}";
    legacyGiftNameController.text = merchantConfig.value.document.legacyGiftName;
    tipLinesOnCustCopy.value = merchantConfig.value.document.tipLinesOnCustCopy;
    orderTypePrintOpt.value = merchantConfig.value.document.orderTypePrintOpt;
    customerCopyPrintOpt.value = merchantConfig.value.document.customerCopyPrintOpt;
    overlappingScheduleOpt.value = merchantConfig.value.document.overlappingScheduleOpt;
    paymentDeviceType.value = merchantConfig.value.document.paymentDeviceType;
    multiCashDrawers.value = merchantConfig.value.document.multiCashDrawer;
    commentRowsOnReceipt.value = merchantConfig.value.document.commentRowsOnReceipt;
    saleDescOnReceipt.value = merchantConfig.value.document.saleDescOnReceipt;
    paidOutPrintCustomer.value = merchantConfig.value.document.paidOutPrintCustomer;
    condensedAuthSlip.value = merchantConfig.value.document.condensedAuthSlip;
    defaultRefundMedia.value = merchantConfig.value.document.defaultRefundMedia;
    viewTablesOnSignOn.value = merchantConfig.value.document.viewTablesOnSignOn;
    prepPrintSizing.value = merchantConfig.value.document.prepPrintSizing;
    printPrepFooter.value = merchantConfig.value.document.printPrepFooter;
    markModifiersRed.value = merchantConfig.value.document.markModifiersRed;
    markCommentsRed.value = merchantConfig.value.document.markCommentsRed;
    markToGoRed.value = merchantConfig.value.document.markToGoRed;
    markOrderTypeRed.value = merchantConfig.value.document.markOrderTypeRed;
    markPromisedTimeRed.value = merchantConfig.value.document.markPromisedTimeRed;
    tipLimitController.text = merchantConfig.value.document.tipConfirmLimit >= 0
        ? "\$${Helpers.formatCurrency(merchantConfig.value.document.tipConfirmLimit).split(".")[0]}"
        : "";
  }

  //
  //
  //
  //
  //
  //
  Future<void> getSalesTaxConfig() async {
    final QueryResult<Object> salesTaxResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_SALES_TAX_JSON_RECORD {
              json_record(where: {record_key: {_eq: "salesTax"}}) {
                record_key
                document
                updated_at
              }
            }
            ''',
        ),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    if ((salesTaxResult.data!['json_record']! as List<dynamic>).isEmpty) throw "No Taxes Found";

    salesTaxRecords = SalesTaxJsonRecord.fromJson((salesTaxResult.data!['json_record'] as List<dynamic>)[0] as Map<String, dynamic>);

    taxList.value = salesTaxRecords.document.taxes;
  }

  //
  //
  //
  //
  //
  Future<void> updateSalesTax({bool stayOpen = false}) async {
    try {
      salesTaxRecords.document.taxes = taxList;

      final QueryResult<Object> updateSalesTaxResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation UPDATE_SALES_TAX_JSON_RECORD(\$record_key: String!, \$document: jsonb) {
                update_json_record_by_pk(pk_columns: {record_key: \$record_key}, _set: {document: \$document}) {
                  record_key
                  updated_at
                  document
                }
              }

          ''',
          ),
          variables: <String, dynamic>{
            "record_key": salesTaxRecords.record_key,
            "document": salesTaxRecords.document,
          },
        ),
      );

      if (updateSalesTaxResult.hasException) throw updateSalesTaxResult.exception.toString();
      _notificationService.success("Updated sales taxes");
    } catch (err, stack) {
      _notificationService.error("Failed to update sales tax");
      _logger.severe('Error updating sales tax', err, stack);
    }
  }

  Future<void> clearGiftCards() async {
    clientIdController.clear();
    locationIdController.clear();
    terminalIdController.clear();
    initiatorIdController.clear();
    initatorPassController.clear();
    integrationAuthController.clear();
    integrationPassController.clear();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updateMerchantSettings({bool toggleDualPrice = false}) async {
    try {
      if (!toggleDualPrice) {
        if (!merchantSettingsKey.currentState!.validate()) throw "Invalid form";

        merchantConfig.value.document.ebt = ebtEnabled.value;
        merchantConfig.value.document.tipping = tippingEnabled.value;
        merchantConfig.value.document.giftcards = giftcardsEnabled.value;
        merchantConfig.value.document.receiptHeader = receiptHeader;
        merchantConfig.value.document.refundReceiptSignatureLine = refundReceiptSignatureLine.value;
        merchantConfig.value.document.house = houseAccounts.value;
        merchantConfig.value.document.legacyGiftTender = legacyGift.value;
        merchantConfig.value.document.cashConfirmation = cashConfirmation.value;
        merchantConfig.value.document.printMerchantOnCash = printMerchantOnCash.value;
        merchantConfig.value.document.printMerchantOnNonCash = printMerchantOnNonCash.value;
        merchantConfig.value.document.printMerchantOnHouse = printMerchantOnHouse.value;
        merchantConfig.value.document.prepOnSaleChange = prepOnSaleChange.value;
        merchantConfig.value.document.prepOnSignOut = prepOnSignOut.value;
        merchantConfig.value.document.prepOnCancel = prepOnCancel.value;
        merchantConfig.value.document.preAuthAmount = int.parse(preAuthController.text.replaceAll(",", "").replaceAll("\$", "").replaceAll(".", ""));
        merchantConfig.value.document.takeOutSurchargeAmt = takeoutSurcharge.value;
        merchantConfig.value.document.tipLinesOnCustCopy = tipLinesOnCustCopy.value;
        merchantConfig.value.document.orderTypePrintOpt = orderTypePrintOpt.value;
        merchantConfig.value.document.customerCopyPrintOpt = customerCopyPrintOpt.value;
        merchantConfig.value.document.overlappingScheduleOpt = overlappingScheduleOpt.value;
        merchantConfig.value.document.paymentDeviceType = paymentDeviceType.value;
        merchantConfig.value.document.multiCashDrawer = multiCashDrawers.value;
        merchantConfig.value.document.commentRowsOnReceipt = commentRowsOnReceipt.value;
        merchantConfig.value.document.saleDescOnReceipt = saleDescOnReceipt.value;
        merchantConfig.value.document.paidOutPrintCustomer = paidOutPrintCustomer.value;
        merchantConfig.value.document.condensedAuthSlip = condensedAuthSlip.value;
        merchantConfig.value.document.defaultRefundMedia = defaultRefundMedia.value;
        merchantConfig.value.document.viewTablesOnSignOn = viewTablesOnSignOn.value;
        merchantConfig.value.document.prepPrintSizing = prepPrintSizing.value;
        merchantConfig.value.document.printPrepFooter = printPrepFooter.value;
        merchantConfig.value.document.markModifiersRed = markModifiersRed.value;
        merchantConfig.value.document.markCommentsRed = markCommentsRed.value;
        merchantConfig.value.document.markToGoRed = markToGoRed.value;
        merchantConfig.value.document.markOrderTypeRed = markOrderTypeRed.value;
        merchantConfig.value.document.markPromisedTimeRed = markPromisedTimeRed.value;
        merchantConfig.value.document.tipConfirmLimit = tipLimitController.text.isEmpty
            ? -1
            : int.parse(tipLimitController.text.replaceAll(",", "").replaceAll("\$", "").replaceAll(".", "")) * 100;
        merchantConfig.value.document.giftCardProgram = MerchantJsonRecordGiftCardProgram(
          clientId: clientIdController.text.isEmpty ? null : clientIdController.text,
          locationId: locationIdController.text.isEmpty ? null : locationIdController.text,
          terminalId: terminalIdController.text.isEmpty ? null : terminalIdController.text,
          initiatorId: initiatorIdController.text.isEmpty ? null : initiatorIdController.text,
          initiatorPass: initatorPassController.text.isEmpty ? null : initatorPassController.text,
          integrationAuth: integrationAuthController.text.isEmpty ? null : integrationAuthController.text,
          integrationPass: integrationPassController.text.isEmpty ? null : integrationPassController.text,
        );
        merchantConfig.value.document.takeOutSurchargeAmt =
            int.parse(surchargeController.text.replaceAll(",", "").replaceAll("\$", "").replaceAll(".", ""));
        merchantConfig.value.document.legacyGiftName = legacyGiftNameController.text;
      }

      merchantConfig.value.document.dualPricing = dualPricingEnabled.value;
      merchantConfig.value.document.dualPricingPercent = double.parse(dualPriceController.text);
      merchantConfig.value.document.dualPricingRoundAmount = roundAmount.value;
      merchantConfig.value.updated_at = DateTime.now().toUtc();

      final QueryResult<Object> updateConfigResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation UPDATE_MERCHANT_JSON_RECORD(\$record_key: String!, \$document: jsonb) {
                update_json_record_by_pk(pk_columns: {record_key: \$record_key}, _set: {document: \$document}) {
                  record_key
                  updated_at
                  document
                }
              }

          ''',
          ),
          variables: <String, dynamic>{
            "record_key": merchantConfig.value.record_key,
            "document": merchantConfig.value.document,
          },
        ),
      );

      if (updateConfigResult.hasException) throw updateConfigResult.exception.toString();

      _logger.info(compareMerchantJsonRecords(merchantConfig.value, preserveMerchConfig));
      _notificationService.success(toggleDualPrice ? "Dual pricing settings updated!" : "Merchant Settings updated!");
      if (!toggleDualPrice) {
        await Get.offAllNamed(
          AppRoutes.SETTINGS,
          id: AppRoutes.id,
        );
      }
    } catch (err, stack) {
      _notificationService.error("Merchant Settings update failed!");
      _logger.severe(err.toString(), err, stack);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> editPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Back Office Settings",
      _graphqlService,
    );
    canEdit.value = edit;
    canUpdateMerchantSettings.value = !edit;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> toggleDualPrice({required bool dpEnabled}) async {
    if (dpEnabled) {
      final int originalRoundAmt = roundAmount.value;
      final double? percentage = await dualPriceChangeConfirmation(roundAmount);
      if (percentage == null) {
        roundAmount.value = originalRoundAmt;
        return;
      }
      final QueryResult<Object> dualPriceChangeResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            mutation CASH_DISCOUNT_ITEM_PRICE_INCREASE(\$dualpercent: numeric, \$roundamount: numeric) {
              dual_pricing_item_price_increase(args: {dualpercent: \$dualpercent, roundamount: \$roundamount}) {
                affected_price_count
                affected_item_count
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "dualpercent": percentage,
            "roundamount": roundAmount.value,
          },
        ),
      );
      if (dualPriceChangeResult.hasException) throw dualPriceChangeResult.exception.toString();

      dualPriceController.text = percentage.toString();
    } else {
      final String? changeKey = await dualPriceResetConfirmation();
      if (changeKey == null) {
        return;
      }
      final QueryResult<Object> dualPriceResetResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
            mutation CASH_DISCOUNT_ITEM_PRICE_RESET(\$resetkey: String) {
              dual_pricing_item_price_reset(args: {resetkey: \$resetkey}) {
                affected_price_count
                affected_item_count
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "resetkey": changeKey,
          },
        ),
      );
      if (dualPriceResetResult.hasException) throw dualPriceResetResult.exception.toString();
      dualPriceController.text = '0';
    }
    _notificationService.success("Items updated!");
    dualPricingEnabled.value = dpEnabled;

    await updateMerchantSettings(toggleDualPrice: true);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  // ignore: avoid_positional_boolean_parameters
  Future<double?> dualPriceChangeConfirmation(RxInt roundAmount) async {
    final TextEditingController cdPercentController = TextEditingController();
    return await Get.defaultDialog<double?>(
      actions: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            DialogButton(
              buttonType: EDialogButtonType.DESTRUCTIVE,
              buttonText: 'Cancel',
              onTapped: Get.back,
            ),
            DialogButton(
              buttonType: EDialogButtonType.AFFIRMATIVE,
              onTapped: () {
                if (cdPercentController.text.isEmpty || double.parse(cdPercentController.text) > 4) {
                  _notificationService.error("Please enter a value between %0 and %4");
                } else {
                  Get.back(result: double.parse(cdPercentController.text));
                }
              },
            ),
          ],
        ),
      ],
      title: "Activate dual Pricing",
      content: SizedBox(
        width: Get.width / 2.6,
        height: Get.height / 2.8,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            const Expanded(
              child: Text(
                "WARNING: This will generate card prices that are a percentage increase from your current prices!",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 15,
                  color: R2Colors.red500,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Column(
                children: <Widget>[
                  FormWrapper(
                    formKey: dpFormKey,
                    children: <MenuGroup>[
                      MenuGroup(
                        title: 'Dual Price Info',
                        children: <Widget>[
                          MenuTextField(
                            controller: cdPercentController,
                            label: "Dual Price Percent",
                            textAlign: TextAlign.right,
                            maxLength: 4,
                            suffixIcon: const Icon(Icons.percent),
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.deny(
                                RegExp("[A-Za-z]"),
                              ),
                            ],
                            hintText: 'DP Percent',
                          ),
                          MenuDropdown<int>(
                            title: 'Cent Rounding',
                            items: const <DropdownMenuItem<int>>[
                              DropdownMenuItem<int>(
                                value: 1,
                                child: Text("None"),
                              ),
                              DropdownMenuItem<int>(
                                value: 5,
                                child: Text("5"),
                              ),
                              DropdownMenuItem<int>(
                                value: 10,
                                child: Text("10"),
                              ),
                              DropdownMenuItem<int>(
                                value: 25,
                                child: Text("25"),
                              ),
                              DropdownMenuItem<int>(
                                value: 50,
                                child: Text("50"),
                              ),
                            ],
                            onChanged: (int? val) {
                              roundAmount.value = val ?? 1;
                            },
                            value: roundAmount.value,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<String?> dualPriceResetConfirmation() async {
    return await Get.defaultDialog<String?>(
      title: "Deactivate dual Pricing",
      content: SizedBox(
        width: 500,
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            const Padding(
              padding: EdgeInsets.only(bottom: 20.0),
              child: Text(
                "Select single price to use for items:",
                style: TextStyle(
                  fontSize: 18,
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                DialogButton(
                  buttonType: EDialogButtonType.AFFIRMATIVE,
                  buttonText: "Card",
                  onTapped: () {
                    Get.back(result: "C0");
                  },
                ),
                DialogButton(
                  buttonType: EDialogButtonType.AFFIRMATIVE,
                  color: R2Colors.green700,
                  buttonText: "Cash",
                  onTapped: () {
                    Get.back(result: "C1");
                  },
                ),
                DialogButton(
                  buttonType: EDialogButtonType.CANCEL,
                  onTapped: Get.back,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> compareMerchantJsonRecords(MerchantJsonRecord original, MerchantJsonRecord updated) {
    final Map<String, dynamic> changes = <String, dynamic>{};

    if (original.record_key != updated.record_key) {
      changes['record_key'] = <String, dynamic>{'original': original.record_key, 'updated': updated.record_key};
    }

    if (original.updated_at != updated.updated_at) {
      changes['updated_at'] = <String, dynamic>{'original': original.updated_at, 'updated': updated.updated_at};
    }

    // Compare nested document field
    final Map<String, dynamic> documentChanges = compareDocuments(original.document, updated.document);
    if (documentChanges.isNotEmpty) {
      changes['document'] = documentChanges;
    }

    return changes;
  }

  Map<String, Map<String, dynamic>> compareDocuments(MerchantJsonRecordDocument original, MerchantJsonRecordDocument updated) {
    final Map<String, Map<String, dynamic>> changes = <String, Map<String, dynamic>>{};

    if (original.ebt != updated.ebt) {
      changes['ebt'] = <String, dynamic>{'original': original.ebt, 'updated': updated.ebt};
    }

    if (original.tipping != updated.tipping) {
      changes['tipping'] = <String, dynamic>{'original': original.tipping, 'updated': updated.tipping};
    }

    if (original.giftcards != updated.giftcards) {
      changes['giftcards'] = <String, dynamic>{'original': original.giftcards, 'updated': updated.giftcards};
    }

    if (original.dualPricing != updated.dualPricing) {
      changes['dualPricing'] = <String, dynamic>{'original': original.dualPricing, 'updated': updated.dualPricing};
    }

    if (original.dualPricingPercent != updated.dualPricingPercent) {
      changes['dualPricingPercent'] = <String, dynamic>{'original': original.dualPricingPercent, 'updated': updated.dualPricingPercent};
    }

    if (original.dualPricingRoundAmount != updated.dualPricingRoundAmount) {
      changes['dualPricingRoundAmount'] = <String, dynamic>{'original': original.dualPricingRoundAmount, 'updated': updated.dualPricingRoundAmount};
    }

    if (original.refundReceiptSignatureLine != updated.refundReceiptSignatureLine) {
      changes['refundReceiptSignatureLine'] = <String, dynamic>{
        'original': original.refundReceiptSignatureLine,
        'updated': updated.refundReceiptSignatureLine,
      };
    }

    if (original.house != updated.house) {
      changes['house'] = <String, dynamic>{'original': original.house, 'updated': updated.house};
    }

    if (original.preAuthAmount != updated.preAuthAmount) {
      changes['preAuthAmount'] = <String, dynamic>{'original': original.preAuthAmount, 'updated': updated.preAuthAmount};
    }

    if (original.cashConfirmation != updated.cashConfirmation) {
      changes['cashConfirmation'] = <String, dynamic>{'original': original.cashConfirmation, 'updated': updated.cashConfirmation};
    }

    if (original.showModPrice != updated.showModPrice) {
      changes['showModPrice'] = <String, dynamic>{'original': original.showModPrice, 'updated': updated.showModPrice};
    }

    if (original.takeOutSurchargeAmt != updated.takeOutSurchargeAmt) {
      changes['takeOutSurchargeAmt'] = <String, dynamic>{'original': original.takeOutSurchargeAmt, 'updated': updated.takeOutSurchargeAmt};
    }

    if (original.printMerchantOnCash != updated.printMerchantOnCash) {
      changes['printMerchantOnCash'] = <String, dynamic>{'original': original.printMerchantOnCash, 'updated': updated.printMerchantOnCash};
    }

    if (original.printMerchantOnNonCash != updated.printMerchantOnNonCash) {
      changes['printMerchantOnNonCash'] = <String, dynamic>{'original': original.printMerchantOnNonCash, 'updated': updated.printMerchantOnNonCash};
    }

    if (original.printMerchantOnHouse != updated.printMerchantOnHouse) {
      changes['printMerchantOnHouse'] = <String, dynamic>{'original': original.printMerchantOnHouse, 'updated': updated.printMerchantOnHouse};
    }

    if (original.prepOnSaleChange != updated.prepOnSaleChange) {
      changes['prepOnSaleChange'] = <String, dynamic>{'original': original.prepOnSaleChange, 'updated': updated.prepOnSaleChange};
    }

    if (original.prepOnSignOut != updated.prepOnSignOut) {
      changes['prepOnSignOut'] = <String, dynamic>{'original': original.prepOnSignOut, 'updated': updated.prepOnSignOut};
    }

    if (original.prepOnCancel != updated.prepOnCancel) {
      changes['prepOnCancel'] = <String, dynamic>{'original': original.prepOnCancel, 'updated': updated.prepOnCancel};
    }

    if (original.legacyGiftTender != updated.legacyGiftTender) {
      changes['legacyGiftTender'] = <String, dynamic>{'original': original.legacyGiftTender, 'updated': updated.legacyGiftTender};
    }

    if (original.legacyGiftName != updated.legacyGiftName) {
      changes['legacyGiftName'] = <String, dynamic>{'original': original.legacyGiftName, 'updated': updated.legacyGiftName};
    }

    if (original.tipLinesOnCustCopy != updated.tipLinesOnCustCopy) {
      changes['tipLinesOnCustCopy'] = <String, dynamic>{'original': original.tipLinesOnCustCopy, 'updated': updated.tipLinesOnCustCopy};
    }

    if (original.orderTypePrintOpt != updated.orderTypePrintOpt) {
      changes['orderTypePrintOpt'] = <String, dynamic>{'original': original.orderTypePrintOpt, 'updated': updated.orderTypePrintOpt};
    }

    if (original.customerCopyPrintOpt != updated.customerCopyPrintOpt) {
      changes['customerCopyPrintOpt'] = <String, dynamic>{'original': original.customerCopyPrintOpt, 'updated': updated.customerCopyPrintOpt};
    }

    if (original.paymentDeviceType != updated.paymentDeviceType) {
      changes['paymentDeviceType'] = <String, dynamic>{'original': original.paymentDeviceType, 'updated': updated.paymentDeviceType};
    }

    if (original.multiCashDrawer != updated.multiCashDrawer) {
      changes['multiCashDrawer'] = <String, dynamic>{'original': original.multiCashDrawer, 'updated': updated.multiCashDrawer};
    }

    if (original.commentRowsOnReceipt != updated.commentRowsOnReceipt) {
      changes['commentRowsOnReceipt'] = <String, dynamic>{'original': original.commentRowsOnReceipt, 'updated': updated.commentRowsOnReceipt};
    }

    if (original.saleDescOnReceipt != updated.saleDescOnReceipt) {
      changes['saleDescOnReceipt'] = <String, dynamic>{'original': original.saleDescOnReceipt, 'updated': updated.saleDescOnReceipt};
    }

    if (original.paidOutPrintCustomer != updated.paidOutPrintCustomer) {
      changes['paidOutPrintCustomer'] = <String, dynamic>{'original': original.paidOutPrintCustomer, 'updated': updated.paidOutPrintCustomer};
    }

    if (original.tipConfirmLimit != updated.tipConfirmLimit) {
      changes['tipConfirmLimit'] = <String, dynamic>{'original': original.tipConfirmLimit, 'updated': updated.tipConfirmLimit};
    }

    if (original.prepPrintSizing != updated.prepPrintSizing) {
      changes['prepPrintSizing'] = <String, dynamic>{'original': original.prepPrintSizing, 'updated': updated.prepPrintSizing};
    }

    if (original.printPrepFooter != updated.printPrepFooter) {
      changes['printPrepFooter'] = <String, dynamic>{'original': original.printPrepFooter, 'updated': updated.printPrepFooter};
    }

    if (original.markModifiersRed != updated.markModifiersRed) {
      changes['markModifiersRed'] = <String, dynamic>{'original': original.markModifiersRed, 'updated': updated.markModifiersRed};
    }

    if (original.markCommentsRed != updated.markCommentsRed) {
      changes['markCommentsRed'] = <String, dynamic>{'original': original.markCommentsRed, 'updated': updated.markCommentsRed};
    }

    if (original.markToGoRed != updated.markToGoRed) {
      changes['markToGoRed'] = <String, dynamic>{'original': original.markToGoRed, 'updated': updated.markToGoRed};
    }

    if (original.markOrderTypeRed != updated.markOrderTypeRed) {
      changes['markOrderTypeRed'] = <String, dynamic>{'original': original.markOrderTypeRed, 'updated': updated.markOrderTypeRed};
    }

    if (original.markPromisedTimeRed != updated.markPromisedTimeRed) {
      changes['markPromisedTimeRed'] = <String, dynamic>{'original': original.markPromisedTimeRed, 'updated': updated.markPromisedTimeRed};
    }

    if (original.condensedAuthSlip != updated.condensedAuthSlip) {
      changes['condensedAuthSlip'] = <String, dynamic>{'original': original.condensedAuthSlip, 'updated': updated.condensedAuthSlip};
    }

    if (original.defaultRefundMedia != updated.defaultRefundMedia) {
      changes['defaultRefundMedia'] = <String, dynamic>{'original': original.defaultRefundMedia, 'updated': updated.defaultRefundMedia};
    }
    // Compare nested giftCardProgram
    final Map<String, dynamic> giftCardProgramChanges = compareGiftCardPrograms(original.giftCardProgram!, updated.giftCardProgram!);
    if (giftCardProgramChanges.isNotEmpty) {
      changes['giftCardProgram'] = giftCardProgramChanges;
    }

    // Compare nested modules
    final Map<String, dynamic> moduleChanges = compareModules(original.modules!, updated.modules!);
    if (moduleChanges.isNotEmpty) {
      changes['modules'] = moduleChanges;
    }

    return changes;
  }

  Map<String, dynamic> compareGiftCardPrograms(MerchantJsonRecordGiftCardProgram original, MerchantJsonRecordGiftCardProgram updated) {
    final Map<String, dynamic> changes = <String, dynamic>{};

    if (original.clientId != updated.clientId) {
      changes['clientId'] = <String, dynamic>{'original': original.clientId, 'updated': updated.clientId};
    }

    if (original.locationId != updated.locationId) {
      changes['locationId'] = <String, dynamic>{'original': original.locationId, 'updated': updated.locationId};
    }

    if (original.terminalId != updated.terminalId) {
      changes['terminalId'] = <String, dynamic>{'original': original.terminalId, 'updated': updated.terminalId};
    }

    if (original.initiatorId != updated.initiatorId) {
      changes['initiatorId'] = <String, dynamic>{'original': original.initiatorId, 'updated': updated.initiatorId};
    }

    if (original.initiatorPass != updated.initiatorPass) {
      changes['initiatorPass'] = <String, dynamic>{'original': original.initiatorPass, 'updated': updated.initiatorPass};
    }

    if (original.integrationAuth != updated.integrationAuth) {
      changes['integrationAuth'] = <String, dynamic>{'original': original.integrationAuth, 'updated': updated.integrationAuth};
    }

    if (original.integrationPass != updated.integrationPass) {
      changes['integrationPass'] = <String, dynamic>{'original': original.integrationPass, 'updated': updated.integrationPass};
    }

    return changes;
  }

  Map<String, dynamic> compareModules(MerchantJsonRecordModules original, MerchantJsonRecordModules updated) {
    final Map<String, dynamic> changes = <String, dynamic>{};

    // Compare timeClock
    if (original.timeClock.isActive != updated.timeClock.isActive) {
      changes['timeClock.isActive'] = <String, dynamic>{'original': original.timeClock.isActive, 'updated': updated.timeClock.isActive};
    }

    // Compare buttonManagement
    if (original.buttonManagement.isActive != updated.buttonManagement.isActive) {
      changes['buttonManagement.isActive'] = <String, dynamic>{
        'original': original.buttonManagement.isActive,
        'updated': updated.buttonManagement.isActive
      };
    }

    return changes;
  }
}
