import 'package:backoffice/app/data/enums/customer_copy_print_options.dart';
import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/enums/order_type_print_options.dart';
import 'package:backoffice/app/data/enums/overlapping_schedule_options.dart';
import 'package:backoffice/app/global_widgets/custom_form_field.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/hint.dart';
import 'package:backoffice/app/global_widgets/menu/components/label.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_button.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_component.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/modules/settings/children/merchant/controller.dart';
import 'package:backoffice/app/modules/settings/children/merchant/widgets/dialog.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:desktop/app/data/enums/payment.dart';
import 'package:desktop/app/data/enums/prep_print_sizing.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/global_widgets/custom_reorderable_list.widget.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class MerchantPage extends GetView<MerchantController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Header(
          title: "Merchant Settings",
          rightButton: Obx(
            () => DialogButton(
              buttonType: EDialogButtonType.ADD,
              disabled: controller.canUpdateMerchantSettings.value,
              onTapped: () async {
                await controller.updateMerchantSettings();
              },
              buttonText: "Update Settings",
            ),
          ),
          leftButton: DialogButton(
            buttonType: EDialogButtonType.BACK,
            buttonText: "Settings",
            onTapped: () {
              Get.offAllNamed(
                AppRoutes.SETTINGS,
                id: AppRoutes.id,
              );
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(top: 30, bottom: 30),
          child: Column(
            children: <Widget>[
              Obx(
                () => FormWrapper(
                  maxWidth: 550,
                  formKey: controller.merchantSettingsKey,
                  children: <MenuGroup>[
                    MenuGroup(
                      title: "General Settings",
                      children: <Widget>[
                        if (CURRENT_EMPLOYEE.value.id == 1000)
                          Row(
                            children: <Widget>[
                              const Expanded(
                                child: MenuLabel(text: "Payment Device Type"),
                              ),
                              Expanded(
                                child: Center(
                                  child: ToggleButtons(
                                    onPressed: (int index) {
                                      controller.paymentDeviceType.value = index;
                                    },
                                    isSelected: <bool>[
                                      controller.paymentDeviceType.value == PaymentProvider.Unknown.index,
                                      controller.paymentDeviceType.value == PaymentProvider.Pax.index,
                                      controller.paymentDeviceType.value == PaymentProvider.ValorPay.index,
                                    ],
                                    children: const <Padding>[
                                      Padding(
                                        padding: EdgeInsets.only(left: 20, right: 20),
                                        child: Text(" N/A "),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(left: 20, right: 20),
                                        child: Text(" PAX "),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(left: 20, right: 20),
                                        child: Text("Valor"),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        MenuTextField(
                          enabled: controller.canEdit.value,
                          textAlign: TextAlign.right,
                          label: "Pre-Authorization amount",
                          controller: controller.preAuthController,
                          maxLength: 7,
                          validator: (String? value) {
                            if (value == null || value.isEmpty) {
                              return 'Enter a valid amount';
                            }

                            return null;
                          },
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.deny(
                              RegExp("[A-Za-z]"),
                            ),
                            CurrencyTextInputFormatter.currency(
                              decimalDigits: 2,
                              symbol: "\$",
                            ),
                          ],
                        ),
                        Obx(
                          () => CURRENT_EMPLOYEE.value.id == 1000
                              ? MenuCheckbox(
                                  text: "Dual Pricing",
                                  value: controller.dualPricingEnabled.value,
                                  onChanged: (bool? val) {
                                    if (CURRENT_EMPLOYEE.value.id == 1000) {
                                      if (val != null) {
                                        controller.toggleDualPrice(
                                          dpEnabled: val,
                                        );
                                      }
                                    }
                                    // controller.canEdit.value ? controller.dualPricingEnabled.value = val! : null;
                                  },
                                )
                              : Row(
                                  children: <Widget>[
                                    const Expanded(
                                      flex: 20,
                                      child: MenuLabel(
                                        text: "Dual Pricing",
                                      ),
                                    ),
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: controller.dualPricingEnabled.value ? R2Colors.green200 : R2Colors.red200,
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(20),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: controller.dualPricingEnabled.value
                                            ? const Text(
                                                "Enabled",
                                                style: TextStyle(
                                                  color: R2Colors.green700,
                                                ),
                                              )
                                            : const Text(
                                                "Disabled",
                                                style: TextStyle(
                                                  color: R2Colors.red500,
                                                ),
                                              ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                        ),
                        if (controller.dualPricingEnabled.value)
                          Obx(
                            () => MenuTextField(
                              label: "Default Dual Percent Difference",
                              controller: controller.dualPriceController,
                              enabled: false,
                              maxLength: 25,
                              validator: (String? value) {
                                if (value == null || value.isEmpty) {
                                  return 'Enter a valid percent';
                                }
                                if (double.parse(value) > 4) {
                                  return 'Must be between 0-4';
                                }
                                return null;
                              },
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.deny(
                                  RegExp("[A-Za-z]"),
                                ),
                              ],
                            ),
                          ),
                        if (controller.dualPricingEnabled.value)
                          Obx(
                            () => MenuDropdown<int>(
                              title: "Dual Price Rounding",
                              enabled: controller.canEdit.value,
                              items: const <DropdownMenuItem<int>>[
                                DropdownMenuItem<int>(
                                  value: 1,
                                  child: Text("None"),
                                ),
                                DropdownMenuItem<int>(
                                  value: 5,
                                  child: Text("5"),
                                ),
                                DropdownMenuItem<int>(
                                  value: 10,
                                  child: Text("10"),
                                ),
                                DropdownMenuItem<int>(
                                  value: 25,
                                  child: Text("25"),
                                ),
                                DropdownMenuItem<int>(
                                  value: 50,
                                  child: Text("50"),
                                ),
                              ],
                              value: controller.roundAmount.value,
                              onChanged: (int? value) {
                                controller.roundAmount.value = value ?? 1;
                              },
                            ),
                          ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Cash Confirmation",
                            value: controller.cashConfirmation.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.cashConfirmation.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => CURRENT_EMPLOYEE.value.id == 1000
                              ? MenuCheckbox(
                                  text: "Tipping",
                                  value: controller.tippingEnabled.value,
                                  onChanged: CURRENT_EMPLOYEE.value.id == 1000
                                      ? (bool? val) {
                                          controller.tippingEnabled.value = val!;
                                        }
                                      : null,
                                )
                              : Row(
                                  children: <Widget>[
                                    const Expanded(
                                      flex: 20,
                                      child: MenuLabel(text: "Tipping"),
                                    ),
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: controller.tippingEnabled.value ? R2Colors.green200 : R2Colors.red200,
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(20),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: controller.tippingEnabled.value
                                            ? const Text(
                                                "Enabled",
                                                style: TextStyle(
                                                  color: R2Colors.green700,
                                                ),
                                              )
                                            : const Text(
                                                "Disabled",
                                                style: TextStyle(
                                                  color: R2Colors.red500,
                                                ),
                                              ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                        ),
                        MenuTextField(
                          textAlign: TextAlign.end,
                          label: "Takeout Surcharge",
                          controller: controller.surchargeController,
                          enabled: controller.canEdit.value,
                          maxLength: 10,
                          validator: (String? value) {
                            if (value == null || value.isEmpty) {
                              return 'Enter a valid number';
                            }

                            return null;
                          },
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.deny(
                              RegExp("[A-Za-z]"),
                            ),
                            CurrencyTextInputFormatter.currency(
                              decimalDigits: 2,
                              symbol: "\$",
                            ),
                          ],
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Assigned Cash Drawers",
                            value: controller.multiCashDrawers.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.multiCashDrawers.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        MenuTextField(
                          enabled: controller.canEdit.value && controller.tippingEnabled.value,
                          textAlign: TextAlign.right,
                          label: "Dollar limit for tip confirm",
                          hintText: "",
                          controller: controller.tipLimitController,
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.allow(
                              RegExp("[0-9]"),
                            ),
                            CurrencyTextInputFormatter.currency(
                              decimalDigits: 0,
                              symbol: "\$",
                            ),
                          ],
                          onConfirm: (String val) {
                            if (RegExp("[0-9]").allMatches(val).length < 4) {
                              controller.tipLimitController.text = val.replaceAll(",", "");
                            }
                          },
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "View Tables on Sign On",
                            value: controller.viewTablesOnSignOn.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.viewTablesOnSignOn.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuDropdown<int>(
                            title: "Overlapping Price Schedules",
                            items: OverlappingScheduleOptions.values
                                .map(
                                  (OverlappingScheduleOptions o) => DropdownMenuItem<int>(
                                    value: o.index,
                                    child: Text(o.friendlyString),
                                  ),
                                )
                                .toList(),
                            value: controller.overlappingScheduleOpt.value,
                            onChanged: controller.canEdit.value
                                ? (int? value) {
                                    controller.overlappingScheduleOpt.value = value ?? 0;
                                  }
                                : null,
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Tender Options",
                      children: <Widget>[
                        Obx(
                          () => CURRENT_EMPLOYEE.value.id == 1000
                              ? MenuCheckbox(
                                  text: "EBT",
                                  value: controller.ebtEnabled.value,
                                  onChanged: CURRENT_EMPLOYEE.value.id == 1000
                                      ? (bool? val) {
                                          controller.ebtEnabled.value = val!;
                                        }
                                      : null,
                                )
                              : Row(
                                  children: <Widget>[
                                    const Expanded(
                                      flex: 20,
                                      child: MenuLabel(text: "EBT"),
                                    ),
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: controller.ebtEnabled.value ? R2Colors.green200 : R2Colors.red200,
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(20),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: controller.ebtEnabled.value
                                            ? const Text(
                                                "Enabled",
                                                style: TextStyle(
                                                  color: R2Colors.green700,
                                                ),
                                              )
                                            : const Text(
                                                "Disabled",
                                                style: TextStyle(
                                                  color: R2Colors.red500,
                                                ),
                                              ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "House Accounts",
                            value: controller.houseAccounts.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.houseAccounts.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Default Refund Media",
                            value: controller.defaultRefundMedia.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.defaultRefundMedia.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Legacy Gift Card Tender",
                            value: controller.legacyGift.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.legacyGift.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        MenuTextField(
                          enabled: controller.canEdit.value,
                          textAlign: TextAlign.left,
                          label: "Legacy Gift Name",
                          middleIconButton: IconButton(
                            icon: Icon(
                              Icons.refresh,
                              color: controller.canEdit.value ? R2Colors.primary500 : R2Colors.neutral300,
                            ),
                            onPressed: controller.canEdit.value
                                ? () {
                                    controller.legacyGiftNameController.text = "Legacy Gift";
                                  }
                                : null,
                          ),
                          controller: controller.legacyGiftNameController,
                          maxLength: 20,
                          onConfirm: (String value) {
                            if (value.isEmpty) {
                              controller.legacyGiftNameController.text = "Legacy Gift";
                            }
                          },
                        ),
                        Obx(
                          () => CURRENT_EMPLOYEE.value.id == 1000
                              ? MenuCheckbox(
                                  text: "Gift Cards",
                                  value: controller.giftcardsEnabled.value,
                                  onChanged: (bool? val) async {
                                    controller.giftcardsEnabled.value = val!;
                                    if (!controller.giftcardsEnabled.value) {
                                      await controller.clearGiftCards();
                                    }
                                  },
                                )
                              : Row(
                                  children: <Widget>[
                                    const Expanded(
                                      flex: 20,
                                      child: MenuLabel(text: "Gift Cards"),
                                    ),
                                    DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: controller.giftcardsEnabled.value ? R2Colors.green200 : R2Colors.red200,
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(20),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: controller.giftcardsEnabled.value
                                            ? const Text(
                                                "Enabled",
                                                style: TextStyle(
                                                  color: R2Colors.green700,
                                                ),
                                              )
                                            : const Text(
                                                "Disabled",
                                                style: TextStyle(
                                                  color: R2Colors.red500,
                                                ),
                                              ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                        ),
                        Obx(
                          () {
                            final String? hintText = controller.giftcardsEnabled.value ? null : "";
                            String? validate(String? val) =>
                                controller.giftcardsEnabled.value && (val == null || val.isEmpty) ? 'Enter a valid value' : null;
                            return Column(
                              children: <Widget>[
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Client Id",
                                  controller: controller.clientIdController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Location Id",
                                  controller: controller.locationIdController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Terminal Id",
                                  controller: controller.terminalIdController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Initiator Id",
                                  controller: controller.initiatorIdController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Initiator Pass",
                                  controller: controller.initatorPassController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Integration Auth",
                                  controller: controller.integrationAuthController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                                MenuTextField(
                                  enabled: controller.giftcardsEnabled.value,
                                  textAlign: TextAlign.right,
                                  label: "Integration Pass",
                                  controller: controller.integrationPassController,
                                  hintText: hintText,
                                  validator: (String? value) => validate(value),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Print Settings",
                      children: <Widget>[
                        Obx(
                          () => MenuCheckbox(
                            text: "Refund Signature",
                            value: controller.refundReceiptSignatureLine.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.refundReceiptSignatureLine.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Tip Lines On Customer Receipt",
                            value: controller.tipLinesOnCustCopy.value && controller.tippingEnabled.value,
                            onChanged: controller.canEdit.value && controller.tippingEnabled.value
                                ? (bool? val) {
                                    controller.tipLinesOnCustCopy.value = val == true;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Comment Lines on Receipt",
                            value: controller.commentRowsOnReceipt.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.commentRowsOnReceipt.value = val == true;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Sale Description on Receipt",
                            value: controller.saleDescOnReceipt.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.saleDescOnReceipt.value = val == true;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Paid Out Customer Receipt",
                            value: controller.paidOutPrintCustomer.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.paidOutPrintCustomer.value = val == true;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Merchant Copy on Cash Sale",
                            value: controller.printMerchantOnCash.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.printMerchantOnCash.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Merch. Copy on Non-Cash Sale",
                            value: controller.printMerchantOnNonCash.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.printMerchantOnNonCash.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Merchant Copy on House Sale",
                            value: controller.printMerchantOnHouse.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.printMerchantOnHouse.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuDropdown<int>(
                            title: "Customer Copy Options",
                            items: CustomerCopyPrintOptions.values
                                .map(
                                  (CustomerCopyPrintOptions o) => DropdownMenuItem<int>(
                                    value: o.index,
                                    child: Text(o.friendlyString),
                                  ),
                                )
                                .toList(),
                            value: controller.customerCopyPrintOpt.value,
                            onChanged: controller.canEdit.value
                                ? (int? value) {
                                    controller.customerCopyPrintOpt.value = value ?? 0;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Condensed Auth Slip",
                            value: controller.condensedAuthSlip.value && controller.tippingEnabled.value,
                            onChanged: controller.canEdit.value && controller.tippingEnabled.value
                                ? (bool? val) {
                                    controller.condensedAuthSlip.value = val == true;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Prep Print on Change Sale",
                            value: controller.prepOnSaleChange.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.prepOnSaleChange.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Prep Print on Sign Out",
                            value: controller.prepOnSignOut.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.prepOnSignOut.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Prep Print on Cancel",
                            value: controller.prepOnCancel.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.prepOnCancel.value = val!;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuDropdown<int>(
                            title: "Order Type on Prep Print",
                            items: OrderTypePrintOptions.values
                                .map(
                                  (OrderTypePrintOptions o) => DropdownMenuItem<int>(
                                    value: o.index,
                                    child: Text(o.friendlyString),
                                  ),
                                )
                                .toList(),
                            value: controller.orderTypePrintOpt.value,
                            onChanged: controller.canEdit.value
                                ? (int? value) {
                                    controller.orderTypePrintOpt.value = value ?? 0;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Wide Prep Print",
                            value: controller.prepPrintSizing.value == PrepPrintSizing.Wide.index,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.prepPrintSizing.value = val == true ? PrepPrintSizing.Wide.index : PrepPrintSizing.Default.index;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Print Prep Footer",
                            value: controller.printPrepFooter.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.printPrepFooter.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Mark Modifiers Red (Prep)",
                            leadingIcon: makeHintIcon(
                              hintTitle: "Mark Modifiers Red",
                              hintContent: const Text("If this is enabled, all modifiers will be printed as red text by the prep printer."),
                            ),
                            value: controller.markModifiersRed.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.markModifiersRed.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Mark Comments Red (Prep)",
                            leadingIcon: makeHintIcon(
                              hintTitle: "Mark Comments Red",
                              hintContent: const Text("If this is enabled, all comments will be printed as red text by the prep printer."),
                            ),
                            value: controller.markCommentsRed.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.markCommentsRed.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Mark To-Go Red (Prep)",
                            leadingIcon: makeHintIcon(
                              hintTitle: "Mark To-Go Red",
                              hintContent: const Text(
                                  "If this is enabled, the line `<<< ToGo (SeatNumber) >>>` will be printed as red text by the prep printer."),
                            ),
                            value: controller.markToGoRed.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.markToGoRed.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Mark Order Type Red (Prep)",
                            leadingIcon: makeHintIcon(
                              hintTitle: "Mark Order Type Red",
                              hintContent: const Text(
                                  "If this is enabled, the line `Order Type: Takeout|Eat in` will be printed as red text by the prep printer."),
                            ),
                            value: controller.markOrderTypeRed.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.markOrderTypeRed.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                        Obx(
                          () => MenuCheckbox(
                            text: "Mark Promised Time Red (Prep)",
                            leadingIcon: makeHintIcon(
                              hintTitle: "Mark Promised Time Red",
                              hintContent: const Text(
                                  "If this is enabled, the line `Promised Time: 10:00 AM` will be printed as red text by the prep printer."),
                            ),
                            value: controller.markPromisedTimeRed.value,
                            onChanged: controller.canEdit.value
                                ? (bool? val) {
                                    controller.markPromisedTimeRed.value = val ?? false;
                                  }
                                : null,
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      isList: true,
                      hintButton: true,
                      hintContent: const Column(
                        children: <Padding>[
                          Padding(
                            padding: EdgeInsets.only(top: 8, bottom: 8),
                            child: Text(
                              "- Add, edit, or remove lines to your receipt header.",
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 8, bottom: 8),
                            child: Text(
                              "- Drag tiles to the order you would like them to print on the receipt header.",
                            ),
                          ),
                        ],
                      ),
                      hintTitle: "Reorderable Receipt Header",
                      hint: "",
                      title: "Receipt Header",
                      buttonTapSize: 20,
                      children: <Widget>[
                        CustomReorderableListWidget.builder(
                          buildDefaultDragHandles: false,
                          shrinkWrap: true,
                          itemBuilder: (BuildContext context, int index) {
                            return ListTile(
                              key: Key(controller.receiptHeader[index]),
                              leading: ConstrainedBox(
                                constraints: const BoxConstraints(
                                  maxWidth: 260,
                                  minWidth: 50,
                                ),
                                child: Text(
                                  controller.receiptHeader[index],
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              trailing: SizedBox(
                                width: 100,
                                child: Row(
                                  children: <Expanded>[
                                    Expanded(
                                      child: IconButton(
                                        icon: const FaIcon(
                                          FontAwesomeIcons.penToSquare,
                                          color: R2Colors.primary500,
                                        ),
                                        onPressed: () async {
                                          final TextEditingController textController = TextEditingController();
                                          textController.text = controller.receiptHeader[index];
                                          await showDialog(
                                            barrierDismissible: true,
                                            context: context,
                                            builder: (BuildContext context) {
                                              return CupertinoAlertDialog(
                                                title: const Text(
                                                  "Edit Receipt Header Line",
                                                ),
                                                content: Padding(
                                                  padding: const EdgeInsets.only(
                                                    top: 8,
                                                    bottom: 8,
                                                  ),
                                                  child: Column(
                                                    children: <Widget>[
                                                      Material(
                                                        color: Colors.transparent,
                                                        child: VirtualKeyboardWrapper(
                                                          textEditingController: textController,
                                                          child: CustomFormField(
                                                            controller: textController,
                                                            maxLength: 45,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                actions: <CupertinoDialogAction>[
                                                  CupertinoDialogAction(
                                                    child: const Text(
                                                      "Close",
                                                      style: TextStyle(
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                    onPressed: () {
                                                      Get.back();
                                                    },
                                                  ),
                                                  CupertinoDialogAction(
                                                    child: const Text(
                                                      "Done",
                                                      style: TextStyle(
                                                        color: R2Colors.primary500,
                                                      ),
                                                    ),
                                                    onPressed: () {
                                                      controller.receiptHeader[index] = textController.text;
                                                      Get.back();
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: IconButton(
                                        icon: const FaIcon(
                                          FontAwesomeIcons.circleMinus,
                                          color: R2Colors.red500,
                                        ),
                                        onPressed: () {
                                          controller.receiptHeader.removeAt(index);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          itemCount: controller.receiptHeader.length,
                          onReorder: (int oldIndex, int newIndex) {
                            if (newIndex > oldIndex) {
                              newIndex -= 1;
                            }

                            final String item = controller.receiptHeader.removeAt(oldIndex);
                            controller.receiptHeader.insert(newIndex, item);
                          },
                        ),
                        MaterialButton(
                          height: 55,
                          minWidth: double.maxFinite,
                          onPressed: controller.canEdit.value
                              ? () async {
                                  final TextEditingController textController = TextEditingController();
                                  await showCupertinoDialog(
                                    barrierDismissible: true,
                                    context: context,
                                    builder: (BuildContext context) {
                                      String test;
                                      return CupertinoAlertDialog(
                                        title: const Text("Add Receipt Header Line"),
                                        content: Padding(
                                          padding: const EdgeInsets.only(
                                            top: 8,
                                            bottom: 8,
                                          ),
                                          child: Column(
                                            children: <Widget>[
                                              VirtualKeyboardWrapper(
                                                textEditingController: textController,
                                                child: CupertinoTextField(
                                                  controller: textController,
                                                  maxLength: 45,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        actions: <CupertinoDialogAction>[
                                          CupertinoDialogAction(
                                            child: const Text(
                                              "Close",
                                              style: TextStyle(color: Colors.red),
                                            ),
                                            onPressed: () {
                                              Get.back();
                                            },
                                          ),
                                          CupertinoDialogAction(
                                            child: const Text(
                                              "Add",
                                              style: TextStyle(
                                                color: R2Colors.primary500,
                                              ),
                                            ),
                                            onPressed: () {
                                              test = textController.text;
                                              controller.receiptHeader.add(test);
                                              Get.back();
                                            },
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                }
                              : null,
                          child: Text(
                            "Add Receipt Header Line",
                            style: TextStyle(
                              color: controller.canEdit.value ? R2Colors.primary500 : R2Colors.neutral400,
                            ),
                          ),
                        ),
                      ],
                    ),
                    MenuGroup(
                      title: "Tax Options",
                      children: <Widget>[
                        ListView.separated(
                          separatorBuilder: (BuildContext context, int index) => MenuComponent.divider,
                          shrinkWrap: true,
                          controller: controller.scrollController,
                          itemCount: controller.taxList.length,
                          itemBuilder: (BuildContext context, int index) => ListTile(
                            title: Text(
                              controller.taxList[index].desc ?? 'Empty Slot',
                            ),
                            trailing: Text(
                              "${controller.taxList[index].taxPercent! / 10000}%",
                            ),
                            onTap: () async {
                              final Tax? updateTax = await Get.bottomSheet<Tax>(
                                TaxBottomSheet(
                                  controller.taxList[index],
                                  idx: controller.taxList[index].idx!,
                                ),
                                isScrollControlled: true,
                              );
                              if (updateTax != null) {
                                controller.taxList.removeAt(index);
                                controller.taxList.insert(index, updateTax);
                                await controller.updateSalesTax();
                              }
                            },
                          ),
                        ),
                        if (controller.taxList.length <= 7)
                          MenuButton(
                            title: "Add Tax",
                            onPressed: controller.canEdit.value
                                ? () async {
                                    final Tax? addTax = await Get.bottomSheet<Tax>(
                                      TaxBottomSheet(
                                        Tax.empty(),
                                        addTax: true,
                                        idx: controller.taxList.length,
                                      ),
                                      isScrollControlled: true,
                                    );
                                    if (addTax != null) {
                                      controller.taxList.add(addTax);
                                      await controller.updateSalesTax();
                                    }
                                  }
                                : null,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
