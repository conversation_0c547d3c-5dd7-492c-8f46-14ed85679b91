import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class TaxController extends GetxController {
  TaxController(
    this.taxObject,
    this.idx,
  );

  final GraphqlService _graphqlService = Get.find();
  final Logger _logger = Logger('TaxController');
  final NotificationService _notificationService = Get.find();

  Tax taxObject = Tax.empty();
  int idx;

  TextEditingController descController = TextEditingController();
  TextEditingController isVATController = TextEditingController();
  TextEditingController taxPercentController = TextEditingController();
  TextEditingController dualPriceController = TextEditingController();

  RxList<Department> deptList = <Department>[].obs;

  RxBool forgiveTakeout = false.obs;
  RxBool isVat = false.obs;

  final GlobalKey<FormState> taxFormKey = GlobalKey<FormState>();

  RxList<String> receiptHeader = <String>[].obs;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    descController.text = taxObject.desc ?? '';
    taxPercentController.text = taxObject.taxPercent == 0 ? "" : (taxObject.taxPercent! / 10000).toString();
    forgiveTakeout.value = taxObject.forgiveTakeout == true;
    isVat.value = taxObject.isVAT;
    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> setTax() async {
    taxObject.desc = descController.text;
    taxObject.idx = idx;
    taxObject.taxPercent = int.parse((double.parse(taxPercentController.text) * 10000).floor().toString());
    taxObject.isVAT = isVat.value;
    taxObject.forgiveTakeout = forgiveTakeout.value;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> setDelete() async {
    taxObject.desc = null;
    taxObject.idx = idx;
    taxObject.taxPercent = 0;
    taxObject.forgiveTakeout = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> deleteCheck() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           query GET_DEPARTMENTS {
              department {
                department
                document
                title
                updated_at
                updated_by
                created_by
                created_at
              }
            }
          ''',
          ),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if ((departmentResult.data!['department']! as List<dynamic>).isEmpty) return _notificationService.error("Department list empty");

      deptList.value = (departmentResult.data!['department'] as List<dynamic>)
          .map((dynamic department) => Department.fromJson(department as Map<String, dynamic>))
          .toList();

      for (int i = 0; i < deptList.length; i++) {
        final Rx<Department> currentDepartment = deptList[i].obs;
        if (currentDepartment.value.document.taxFlags != 0) {
          final String deptTaxBinary = currentDepartment.value.document.taxFlags.toRadixString(2);

          ///turn off tax in departments that include this tax

          final List<String> deptTaxBinaryList = deptTaxBinary.split('').reversed.toList();
          deptTaxBinaryList[idx] = '0';

          ///convert binary back to decimal
          final RxInt binaryAsDecimal = 0.obs;
          binaryAsDecimal.value = int.parse(deptTaxBinaryList.reversed.join(), radix: 2);

          currentDepartment.value.document.taxFlags = binaryAsDecimal.value;

          final QueryResult<Object> departmentResult = await _graphqlService.client.query(
            QueryOptions<Object>(
              document: g.parseString(
                '''
                mutation UPDATE_DEPARTMENTS(\$department: uuid!, \$document: jsonb!) {
                    update_department_by_pk(pk_columns: {department: \$department}, _set: {document: \$document}) {
                      created_at
                      created_by
                      department
                      document
                      title
                      updated_at
                      updated_by
                    }
                  }
                ''',
              ),
              variables: <String, dynamic>{
                "department": currentDepartment.value.department,
                "document": currentDepartment.value.document,
              },
              fetchPolicy: FetchPolicy.noCache,
            ),
          );

          if (departmentResult.hasException) throw departmentResult.exception.toString();
        }
      }
    } catch (e, stack) {
      _logger.severe('error updating departments', e, stack);
      _notificationService.error(e.toString());
    }
  }

  void validateForm() {
    if (!taxFormKey.currentState!.validate()) {
      _notificationService.error("Invalid form");
      throw "Invalid form";
    }
  }
}
