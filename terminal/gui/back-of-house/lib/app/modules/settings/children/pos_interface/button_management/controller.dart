// ignore_for_file: always_specify_types, avoid_dynamic_calls, non_constant_identifier_names, depend_on_referenced_packages
import 'dart:async';
import 'dart:convert';

import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/board_view/custom_board_item.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/board_view/custom_board_list.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/board_view/custom_board_view_controller.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/edit_menus.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:backoffice/main.dart';
import 'package:dartz/dartz.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  record_key
  updated_at
  document
''';

const String GET_TOOLBAR_JSON_RECORD_QUERY = '''
  query GET_TOOLBAR_JSON_RECORD{
    json_record(where:{record_key:{_eq:"registerMenus"}}){
			record_key
      updated_at
    	document
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

class ButtonManagementController extends GetxController with GetSingleTickerProviderStateMixin {
  ButtonManagementController();

  RegisterMenusJsonRecord registerMenusConfig = RegisterMenusJsonRecord.empty();
  final CustomBoardViewController boardViewController = CustomBoardViewController();
  List<CustomBoardList> boardLists = List<CustomBoardList>.empty(growable: true);
  final RxBool editToolbarLoading = true.obs;
  final RxBool toolbarChanged = false.obs;
  final RxBool isActiveCheck = true.obs;
  final RxBool customColorsCheck = true.obs;
  Rx<RegisterMenusJsonRecordButton> selectedItem = RegisterMenusJsonRecordButton.empty().obs;
  RxBool editingBackgroundColor = false.obs;
  Rx<Color> newFgColor = R2Colors.white.obs;
  Rx<Color> newBgColor = R2Colors.neutral700.obs;
  Rx<RegisterMenusJsonRecordButton> newButton = RegisterMenusJsonRecordButton(
    index: 0,
    text: "New Button",
    action: "UNKNOWN",
    section: "SALE",
  ).obs;
  late TabController tabController;

  final Logger _logger = Logger('ConfigService');

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  bool editButtonsPermission = false;
  bool deleteButtonsPermission = false;
  bool viewButtonsPermission = false;
  bool createButtonsPermission = false;

  @override
  Future<void> onInit() async {
    await getButtonPermissions();
    tabController = TabController(length: 2, vsync: this);
    initData();
    super.onInit();
  }

  @override
  Future<void> onClose() async {
    tabController.dispose();
    super.onClose();
  }

  Future<void> getButtonPermissions() async {
    editButtonsPermission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Edit",
      "POS Interface",
      _graphqlService,
    );
    deleteButtonsPermission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Delete",
      "POS Interface",
      _graphqlService,
    );
    viewButtonsPermission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "POS Interface",
      _graphqlService,
    );
    createButtonsPermission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Create",
      "POS Interface",
      _graphqlService,
    );
  }

  Future<void> getMenuConfig() async {
    try {
      final RegisterMenusJsonRecord emptyRecord = RegisterMenusJsonRecord.empty();
      emptyRecord.document = RegisterMenusJsonRecordDocument.fromJson(
        json.decode(json.encode(Constants.defaultRegisterMenus)) as Map<String, dynamic>,
      );
      RegisterMenusJsonRecord registerMenuRes = emptyRecord;

      final QueryResult configResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(GET_TOOLBAR_JSON_RECORD_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<dynamic> configList = configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        if (configList.first["document"] == null) {
          configList.first["document"] = registerMenuRes.document!.toJson();
        }
        registerMenuRes = configList
            .map(
              (config) => RegisterMenusJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;
      }

      registerMenusConfig = registerMenuRes;
    } catch (err, stack) {
      _logger.severe(
        "error fetching toolbar config",
        err,
        stack,
      );
    }
  }

  Future<void> updateMenuSettings() async {
    if (!toolbarChanged.value) return;
    final List<RegisterMenusJsonRecordButton> userMenu = registerMenusConfig.document!.user.menu;
    final List<RegisterMenusJsonRecordButton> adminMenu = registerMenusConfig.document!.admin.menu;
    final List<RegisterMenusJsonRecordButton> toolbarMenu = registerMenusConfig.document!.toolbar.menu;
    for (int i = 0; i < userMenu.length; i++) {
      userMenu[i].index = i;
    }
    for (int i = 0; i < adminMenu.length; i++) {
      adminMenu[i].index = i;
    }
    for (int i = 0; i < toolbarMenu.length; i++) {
      toolbarMenu[i].index = i;
    }
    final Either<ServiceError, RegisterMenusJsonRecord> updateResult = await upsertRegisterMenuConfig(
      registerMenusJsonRecord: registerMenusConfig,
    );

    updateResult.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (RegisterMenusJsonRecord updatedConfig) => null,
    );

    boardLists = List<CustomBoardList>.empty(growable: true);
    initData();
  }

  Future<Either<ServiceError, RegisterMenusJsonRecord>> upsertRegisterMenuConfig({
    required RegisterMenusJsonRecord registerMenusJsonRecord,
  }) async {
    try {
      final List<String> fieldsToSanitize = ['updated_at'];
      if (registerMenusJsonRecord.record_key.isEmpty) {
        fieldsToSanitize.add('record_key');
      }

      final Map<String, dynamic> santizedConfig = Helpers.sanitizeEntity(
        registerMenusJsonRecord.toJson(),
        fieldsToSanitize,
      );

      final QueryResult upsertConfigResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": santizedConfig,
          },
        ),
      );

      if (upsertConfigResult.hasException) {
        throw upsertConfigResult.exception.toString();
      }

      final RegisterMenusJsonRecord updatedConfig = RegisterMenusJsonRecord.fromJson(
        upsertConfigResult.data!['insert_json_record_one'] as Map<String, dynamic>,
      );

      return Right(updatedConfig);
    } catch (err, stack) {
      _logger.severe(
        "error upserting register toolbar config",
        err,
        stack,
      );
      return Left(ServiceError("Failed to update register toolbar config!"));
    }
  }

  Future<void> initData() async {
    if (editToolbarLoading.value) {
      await getMenuConfig();
    }

    boardLists.clear();

    final RegisterMenusJsonRecordDocument doc = registerMenusConfig.document!;

    final List<RegisterMenusJsonRecordButton> toolbarItems = registerMenusConfig.document!.toolbar.menu;

    final List<RegisterMenusJsonRecordButton> userItems = registerMenusConfig.document!.user.menu;

    final List<RegisterMenusJsonRecordButton> adminItems = registerMenusConfig.document!.admin.menu;

    final List<Map<String, dynamic>> listData = [
      {
        "title": "Toolbar",
        "items": toolbarItems,
        "key": "toolbar",
        "doc": doc.toolbar,
      },
      {
        "title": "User Menu",
        "items": userItems,
        "key": "user",
        "doc": doc.user,
      },
      {
        "title": "Supervisor Menu",
        "items": adminItems,
        "key": "admin",
        "doc": doc.admin,
      },
    ];

    for (int i = 0; i < listData.length; i++) {
      boardLists.add(
        _createBoardList(listData[i], listData, i) as CustomBoardList,
      );
    }

    if (editToolbarLoading.value) {
      editToolbarLoading.value = false;
    } else {
      editToolbarLoading.refresh();
    }
  }

  Widget _createBoardList(
    Map<String, dynamic> list,
    List<Map<String, dynamic>> listData,
    int initListIndex,
  ) {
    final List<CustomBoardItem> items = [];
    final List<RegisterMenusJsonRecordButton> listItems = list["items"] as List<RegisterMenusJsonRecordButton>;
    for (int i = 0; i < listItems.length; i++) {
      items.insert(
        i,
        buildBoardItem(listItems[i], listData, initListIndex) as CustomBoardItem,
      );
    }

    return CustomBoardList(
      draggable: false,
      onStartDragList: (int? listIndex) {},
      onTapList: (int? listIndex) async {},
      onDropList: (int? listIndex, int? oldListIndex) {},
      headerBackgroundColor: R2Colors.neutral200,
      backgroundColor: R2Colors.neutral200,
      borderColor: R2Colors.neutral300,
      header: [
        Obx(
          () => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: editToolbarLoading.value
                ? Container()
                : Row(
                    children: [
                      Text(
                        list["title"]! as String,
                        style: TextStyle(
                          fontSize: 18,
                          color: list["key"] == "toolbar"
                              ? Colors.black
                              : list["doc"].is_active as bool
                                  ? Colors.black
                                  : R2Colors.neutral400,
                        ),
                      ),
                      IconButton(
                        iconSize: 26,
                        onPressed: () async {
                          if (!editButtonsPermission) {
                            _notificationService.error("Edit Permission Denied");
                            return;
                          }
                          toolbarChanged.value = await Get.defaultDialog(
                                title: "Edit Menu",
                                content: MenuSettingsDialog(
                                  title: list["key"] as String,
                                ),
                                barrierDismissible: true,
                              ) ??
                              false;
                          editToolbarLoading.value = false;
                        },
                        icon: const Icon(
                          Icons.settings,
                          color: R2Colors.primary500,
                        ),
                      )
                    ],
                  ),
          ),
        ),
      ],
      items: items,
    );
  }

  Widget buildBoardItem(
    RegisterMenusJsonRecordButton itemObject,
    List<Map<String, dynamic>> listData,
    int initListIndex,
  ) {
    return CustomBoardItem(
      onStartDragItem: (int? listIndex, int? itemIndex, CustomBoardItemState? state) {},
      onDropItem: (
        int? listIndex,
        int? itemIndex,
        int? oldListIndex,
        int? oldItemIndex,
        CustomBoardItemState? state,
      ) async {
        if (oldListIndex == null || oldItemIndex == null || (oldListIndex == listIndex && oldItemIndex == itemIndex)) {
          return;
        }
        toolbarChanged.value = true;
        final List oldListItems = listData[oldListIndex]["items"] as List;
        final List newListItems = listData[listIndex!]["items"] as List;
        final RegisterMenusJsonRecordButton item = oldListItems[oldItemIndex] as RegisterMenusJsonRecordButton;
        oldListItems.removeAt(oldItemIndex);
        newListItems.insert(itemIndex!, item);
        await updateMenuSettings();
      },
      item: GestureDetector(
        onTapDown: (val) {
          selectedItem.value = itemObject;
        },
        child: Card(
          child: Obx(
            () => ColoredBox(
              color: selectedItem.value == itemObject ? R2Colors.primary200 : R2Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text(
                  itemObject.text,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
