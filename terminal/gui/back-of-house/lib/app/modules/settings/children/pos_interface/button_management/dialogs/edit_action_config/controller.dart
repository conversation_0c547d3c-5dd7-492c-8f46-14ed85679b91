// ignore_for_file: avoid_dynamic_calls

import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/controller.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/action_config_fields.dart';
import 'package:desktop/app/data/enums/action_config_items_variables.dart';
import 'package:desktop/app/data/models/action.dart' as a;
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:numberpicker/numberpicker.dart';

class EditConfigController extends GetxController {
  EditConfigController({required this.isDialog});
  final bool isDialog;

  final GraphqlService _graphqlService = Get.find();
  final NotificationService notificationService = Get.find();

  final ButtonManagementController _posInterfaceSettingsController = Get.find();

  final List<DropdownMenuItem<int>> actionDropdownList = <DropdownMenuItem<int>>[];

  final RxList<Widget> fieldList = <Widget>[].obs;

  final RxInt actionIndex = 0.obs;

  List<a.ActionDocumentItem> terminalList = <a.ActionDocumentItem>[];
  List<a.ActionDocumentItem> departmentList = <a.ActionDocumentItem>[];

  List<a.Action> actionList = <a.Action>[];

  late RxString categoryController;

  late final RegisterMenusJsonRecordButton item;

  RxMap<String, dynamic> configFormFields = <String, dynamic>{}.obs;

  RxBool loading = true.obs;

  @override
  Future<void> onInit() async {
    await getDepartments();
    await initValues(isDialog: isDialog);
    loading.value = false;
    super.onInit();
  }

  void updateItem() {
    final Map<String, dynamic> configMap = <String, dynamic>{};

    configFormFields.forEach((String key, dynamic value) {
      configMap[key] = value is TextEditingController ? value.text : value.value;
    });

    item.config = configMap.isEmpty ? null : configMap;

    item.section = categoryController.value;

    if (item.action != actionList[actionIndex.value].name) {
      item.action = actionList[actionIndex.value].name;
    }
  }

  Future<void> getDepartments() async {
    try {
      final QueryResult<Object> departmentResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            """
                query GET_DEPARTMENTS {
                  department {
                    created_at
                    created_by
                    department
                    department_order
                    document
                    title
                    updated_at
                    updated_by
                  }
                }
                """,
          ),
        ),
      );

      if (departmentResult.hasException) {
        return notificationService.error(departmentResult.exception.toString());
      }

      departmentList = <a.ActionDocumentItem>[
        a.ActionDocumentItem(text: "", value: ""),
        ...(departmentResult.data!['department'] as List<dynamic>)
            .map(
              (dynamic data) => Department.fromJson(data as Map<String, dynamic>),
            )
            .map(
              (Department dept) => a.ActionDocumentItem(value: dept.title, text: dept.title),
            ),
      ];
    } catch (e) {
      throw e.toString();
    }
  }

  void keepChanges() {
    updateItem();
    _posInterfaceSettingsController.editToolbarLoading.value = false;
    Get.back(result: true);
  }

  Future<void> initValues({required bool isDialog}) async {
    item = isDialog ? _posInterfaceSettingsController.selectedItem.value : _posInterfaceSettingsController.newButton.value;
    categoryController = item.section.obs;

    actionList = <a.Action>[...Constants.actions];

    actionList.sort((a.Action a, a.Action b) => a.name.compareTo(b.name));

    actionDropdownList.clear();

    for (int i = 0; i < actionList.length; i++) {
      if (actionList[i].name == item.action) actionIndex.value = i;
      actionDropdownList.add(
        DropdownMenuItem<int>(
          value: i,
          child: Text(actionList[i].name.replaceAll(RegExp('_'), ' ')),
        ),
      );
    }
    initForm();
  }

  void initForm() {
    if (actionList.isEmpty) return;

    final a.Action currAction = actionList[actionIndex.value];

    fieldList.value = List<Widget>.filled(
      currAction.document != null ? currAction.document!.length : 0,
      Container(),
    );

    configFormFields.clear();

    currAction.document?.forEach((String key, a.ActionDocumentField val) {
      final List<a.ActionDocumentItem> selectedList = val.items_variable == ActionConfigItemsVariables.TERMINALS.index
          ? terminalList
          : val.items_variable == ActionConfigItemsVariables.DEPARTMENTS.index
              ? departmentList
              : val.items ?? <a.ActionDocumentItem>[];

      if (val.field == ActionConfigFields.BOOL.index) {
        final RxBool newBool = item.config != null && item.config![key] != null ? (item.config![key] as bool).obs : (val.default_value as bool).obs;
        configFormFields[key] = newBool;

        fieldList[val.idx - 1] = Obx(
          () {
            final bool isEnabled = val.active_if_equals.isEmpty || val.active_if_equals.contains(configFormFields[val.active_if_field].value);
            return GestureDetector(
              onTap: () {
                final bool oldVal = configFormFields[key].value as bool;
                configFormFields[key].value = !oldVal;
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: <Widget>[
                    Checkbox(
                      value: configFormFields[key].value as bool,
                      onChanged: isEnabled
                          ? (bool? newVal) {
                              if (newVal == null) return;
                              configFormFields[key].value = newVal;
                            }
                          : null,
                    ),
                    Text(val.label),
                  ],
                ),
              ),
            );
          },
        );
      } else if (val.field == ActionConfigFields.INT.index) {
        final RxInt newInt = item.config != null && item.config![key] != null ? (item.config![key] as int).obs : (val.default_value as int).obs;
        configFormFields[key] = newInt;
        fieldList[val.idx - 1] = Obx(
          () => Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: <Widget>[
                Text(val.label),
                NumberPicker(
                  value: configFormFields[key].value as int,
                  minValue: val.lower_limit != null ? val.lower_limit! : 0,
                  maxValue: val.upper_limit != null ? val.upper_limit! : 100,
                  axis: Axis.horizontal,
                  onChanged: (int value) => configFormFields[key].value = value,
                ),
              ],
            ),
          ),
        );
      } else if (val.field == ActionConfigFields.RADIO.index) {
        final RxInt newInt = item.config != null && item.config![key] != null ? (item.config![key] as int).obs : (val.default_value as int).obs;
        configFormFields[key] = newInt;
        fieldList[val.idx - 1] = Obx(() {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: <Widget>[
                Text(val.label),
                ...selectedList.map((a.ActionDocumentItem item) {
                  // ignore: use_decorated_box
                  return Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: R2Colors.primary200),
                    ),
                    child: Column(
                      children: <Widget>[
                        RadioListTile<int>(
                          activeColor: R2Colors.primary500,
                          dense: true,
                          title: Text(item.text),
                          value: item.value as int,
                          groupValue: configFormFields[key].value as int,
                          onChanged: (int? value) {
                            if (value == null) return;
                            configFormFields[key].value = value;
                          },
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          );
        });
      } else if (val.field == ActionConfigFields.DROPDOWN.index) {
        final RxString newStr =
            item.config != null && item.config![key] != null ? (item.config![key] as String).obs : (val.default_value as String).obs;
        configFormFields[key] = newStr;
        fieldList[val.idx - 1] = Obx(() {
          final bool isEnabled = val.active_if_equals.isEmpty || val.active_if_equals.contains(configFormFields[val.active_if_field].value);
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: <Widget>[
                Text(
                  val.label,
                  style: TextStyle(color: isEnabled ? null : R2Colors.neutral300),
                ),
                SizedBox(
                  height: 50,
                  width: 250,
                  child: MenuDropdown<Object>(
                    value: configFormFields[key].value as dynamic,
                    items: isEnabled
                        ? selectedList.map((a.ActionDocumentItem item) {
                            return DropdownMenuItem<Object>(
                              value: item.value,
                              child: Text(item.text),
                            );
                          }).toList()
                        : null,
                    onChanged: (Object? value) {
                      if (value == null) return;
                      configFormFields[key].value = value;
                    },
                  ),
                ),
              ],
            ),
          );
        });
      } else {
        final String text = item.config != null && item.config![key] != null ? item.config![key] as String : val.default_value as String;
        configFormFields[key] = TextEditingController(text: text);
        fieldList[val.idx - 1] = Obx(
          () => Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: <Widget>[
                Text(val.label),
                VirtualKeyboardWrapper(
                  textEditingController: configFormFields[key] as TextEditingController,
                  child: SizedBox(
                    width: 300,
                    height: 50,
                    child: TextField(
                      controller: configFormFields[key] as TextEditingController,
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                      decoration: const InputDecoration(
                        border: UnderlineInputBorder(),
                        contentPadding: EdgeInsets.all(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    });
  }
}
