// ignore_for_file: prefer_const_literals_to_create_immutables, avoid_dynamic_calls, always_specify_types

import 'dart:convert';

import 'package:backoffice/app/global_widgets/thin_bottom_sheet.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/board_view/custom_board_view.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/controller.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/add_button.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/delete_button.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/edit_action_config/widget.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/edit_appearance.dart';
import 'package:backoffice/app/modules/settings/children/pos_interface/button_management/dialogs/reset_menus.dart';

import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

// ignore: must_be_immutable
class ButtonManagementPage extends GetView<ButtonManagementController> {
  final NotificationService _notificationService = Get.find();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ButtonManagementController>(
      init: ButtonManagementController(),
      builder: (ButtonManagementController controller) {
        controller.selectedItem.value = RegisterMenusJsonRecordButton.empty();
        return Obx(
          () => controller.editToolbarLoading.value
              ? SizedBox(
                  height: Get.height,
                  width: Get.width,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Lottie.asset(
                          'lib/assets/lottie/loading-animation.json',
                          height: 100,
                        )
                      ],
                    ),
                  ),
                )
              : Column(
                  children: <Widget>[
                    Expanded(
                      flex: 9,
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5.0),
                          child: SizedBox(
                            width: 765,
                            child: CustomBoardView(
                              lists: controller.boardLists,
                              boardViewController: controller.boardViewController,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      height: 60,
                      width: double.infinity,
                      alignment: Alignment.topCenter,
                      child: Stack(
                        children: [
                          Align(
                            alignment: Alignment.bottomRight,
                            child: TextButton(
                              style: ButtonStyle(
                                backgroundColor: MaterialStateProperty.all<Color?>(
                                  Colors.transparent,
                                ),
                              ),
                              onPressed: () async {
                                if (!controller.editButtonsPermission) {
                                  _notificationService.error("Edit Permission Denied");
                                  return;
                                }
                                if (!controller.createButtonsPermission) {
                                  _notificationService.error("Create Permission Denied");
                                  return;
                                }
                                if (!controller.deleteButtonsPermission) {
                                  _notificationService.error("Delete Permission Denied");
                                  return;
                                }
                                final bool resetBool = await Get.defaultDialog(
                                      title: "",
                                      content: ResetMenusDialog(),
                                      barrierDismissible: true,
                                    ) ??
                                    false;
                                if (controller.toolbarChanged.value == false && resetBool) {
                                  controller.toolbarChanged.value = true;
                                }
                                if (resetBool) {
                                  controller.registerMenusConfig.document = RegisterMenusJsonRecordDocument.fromJson(
                                    json.decode(
                                      json.encode(
                                        Constants.defaultRegisterMenus,
                                      ),
                                    ) as Map<String, dynamic>,
                                  );

                                  controller.selectedItem.value = RegisterMenusJsonRecordButton.empty();

                                  await controller.updateMenuSettings();

                                  _notificationService.success("Menus & Buttons Reset!");
                                }
                              },
                              child: const Text(
                                "Reset Menus to Default",
                                style: TextStyle(color: R2Colors.neutral500),
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                ),
                                child: IconButton(
                                  iconSize: 50,
                                  color: controller.selectedItem.value == RegisterMenusJsonRecordButton.empty()
                                      ? R2Colors.neutral400
                                      : R2Colors.primary500,
                                  onPressed: () async {
                                    if (controller.selectedItem.value.action == "") {
                                      _notificationService.error("No Button Selected");
                                      return;
                                    }
                                    if (!controller.editButtonsPermission) {
                                      _notificationService.error("Edit Permission Denied");
                                      return;
                                    }
                                    final bool appearanceBool = await Get.bottomSheet(
                                          const ThinBottomSheet(
                                            topFlex: 3,
                                            child: ActionAppearanceDialog(),
                                          ),
                                          isScrollControlled: true,
                                          ignoreSafeArea: true,
                                        ) ??
                                        false;
                                    if (controller.toolbarChanged.value == false && appearanceBool) {
                                      controller.toolbarChanged.value = true;
                                    }
                                    final val = controller.selectedItem.value;
                                    // triggers rerender of selected item
                                    controller.selectedItem.value = RegisterMenusJsonRecordButton.empty();
                                    controller.selectedItem.value = val;
                                    await controller.updateMenuSettings();
                                  },
                                  icon: const Icon(
                                    Icons.color_lens,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                ),
                                child: IconButton(
                                  iconSize: 50,
                                  // ignore: unrelated_type_equality_checks
                                  color: controller.selectedItem == RegisterMenusJsonRecordButton.empty() ? R2Colors.neutral400 : R2Colors.primary500,
                                  onPressed: () async {
                                    if (controller.selectedItem.value.action == "") {
                                      _notificationService.error("No Button Selected");
                                      return;
                                    }
                                    if (!controller.editButtonsPermission) {
                                      _notificationService.error("Edit Permission Denied");
                                      return;
                                    }
                                    final bool configBool = await Get.bottomSheet(
                                          const ThinBottomSheet(
                                            topFlex: 2,
                                            child: ActionConfigDialog(),
                                          ),
                                          isScrollControlled: true,
                                          ignoreSafeArea: true,
                                        ) ??
                                        false;
                                    if (controller.toolbarChanged.value == false && configBool) {
                                      controller.toolbarChanged.value = true;
                                    }
                                    final val = controller.selectedItem.value;
                                    // triggers rerender of selected item
                                    controller.selectedItem.value = RegisterMenusJsonRecordButton.empty();
                                    controller.selectedItem.value = val;
                                    await controller.updateMenuSettings();
                                  },
                                  icon: const Icon(
                                    Icons.settings,
                                    size: 50,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                ),
                                child: IconButton(
                                  iconSize: 50,
                                  color: R2Colors.primary500,
                                  onPressed: () async {
                                    if (!controller.createButtonsPermission) {
                                      _notificationService.error(
                                        "Create Permission Denied",
                                      );
                                      return;
                                    }
                                    final bool addNewBool = await Get.bottomSheet(
                                          ThinBottomSheet(
                                            topFlex: 2,
                                            child: AddButtonDialog(),
                                          ),
                                          isScrollControlled: true,
                                          ignoreSafeArea: true,
                                        ) ??
                                        false;
                                    if (controller.toolbarChanged.value == false && addNewBool) {
                                      controller.toolbarChanged.value = true;
                                    }
                                    if (addNewBool) {
                                      final List<RegisterMenusJsonRecordButton> userMenu = controller.registerMenusConfig.document!.user.menu;
                                      userMenu.add(
                                        controller.newButton.value,
                                      );
                                      await controller.updateMenuSettings();

                                      _notificationService.success("Button Added!");
                                    }
                                    controller.newButton.value = RegisterMenusJsonRecordButton(
                                      index: 0,
                                      text: "New Button",
                                      action: "UNKNOWN",
                                      section: "SALE",
                                    );
                                  },
                                  icon: const Icon(
                                    Icons.add_box,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                ),
                                child: IconButton(
                                  iconSize: 50,
                                  // ignore: unrelated_type_equality_checks
                                  color: controller.selectedItem == RegisterMenusJsonRecordButton.empty() ? R2Colors.neutral400 : R2Colors.red500,
                                  onPressed: () async {
                                    if (controller.selectedItem.value.action == "") {
                                      _notificationService.error("No Button Selected");
                                      return;
                                    }
                                    if (!controller.deleteButtonsPermission) {
                                      _notificationService.error(
                                        "Delete Permission Denied",
                                      );
                                      return;
                                    }
                                    final bool deleteBool = await Get.defaultDialog(
                                          title: "",
                                          content: DeleteButtonDialog(),
                                          barrierDismissible: true,
                                        ) ??
                                        false;
                                    if (controller.toolbarChanged.value == false && deleteBool) {
                                      controller.toolbarChanged.value = true;
                                    }
                                    if (deleteBool) {
                                      controller.registerMenusConfig.document!.user.menu.remove(
                                        controller.selectedItem.value,
                                      );
                                      controller.registerMenusConfig.document!.admin.menu.remove(
                                        controller.selectedItem.value,
                                      );
                                      controller.registerMenusConfig.document!.toolbar.menu.remove(
                                        controller.selectedItem.value,
                                      );
                                      controller.selectedItem.value = RegisterMenusJsonRecordButton.empty();

                                      await controller.updateMenuSettings();

                                      _notificationService.success("Button Removed!");
                                    }
                                  },
                                  icon: const Icon(
                                    Icons.remove_circle,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }
}
