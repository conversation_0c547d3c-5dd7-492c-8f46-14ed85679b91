// ignore_for_file: depend_on_referenced_packages, avoid_dynamic_calls

import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_MERCHANT_JSON_RECORD = '''
  query GET_JSON_RECORD {
    json_record(where: {record_key: {_eq: "merchant"}}) {
      record_key
      document
      updated_at
    }
  }
''';

class POSInterfaceSettingsController extends GetxController with GetSingleTickerProviderStateMixin {
  POSInterfaceSettingsController();

  final GlobalKey posInterfaceKey = GlobalKey();

  final Logger _logger = Logger('ConfigService');
  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final Rx<MerchantJsonRecordModules> modules = MerchantJsonRecord.empty().document.modules!.obs;

  final RxBool showModPrice = MerchantJsonRecord.empty().document.showModPrice.obs;
  final RxBool fetchError = false.obs;

  @override
  Future<void> onInit() async {
    final MerchantJsonRecordDocument? merchantDoc = await getModuleSettings();
    if (merchantDoc!.modules != null) {
      modules.value = merchantDoc.modules!;
    }
    showModPrice.value = merchantDoc.showModPrice;
    super.onInit();
  }

  Future<MerchantJsonRecordDocument?> getModuleSettings() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_MERCHANT_JSON_RECORD),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );
      // ignore: always_specify_types
      if ((configResult.data!['json_record']! as List).isEmpty) {
        throw "No Json Records Found";
      }
      final MerchantJsonRecord merchantConfig = MerchantJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      return merchantConfig.document;
    } catch (err, stack) {
      _logger.severe(
        "error fetching modules config",
        err,
        stack,
      );
      _notificationService.error(err.toString());
    }
    fetchError.value = true;
    return null;
  }

  Future<void> updateMerchantSettings() async {
    try {
      final MerchantJsonRecordDocument? merchantDoc = await getModuleSettings();

      if (merchantDoc == null) return;

      merchantDoc.modules = modules.value;
      merchantDoc.showModPrice = showModPrice.value;

      final QueryResult<Object> updateConfigResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
              mutation UPDATE_MERCHANT_JSON_RECORD(\$document: jsonb) {
                update_json_record_by_pk(pk_columns: {record_key: "merchant"}, _append: {document: \$document}) {
                  record_key
                  updated_at
                  document
                }
              }

          ''',
          ),
          variables: <String, dynamic>{
            "document": merchantDoc,
          },
        ),
      );

      if (updateConfigResult.hasException) {
        throw updateConfigResult.exception.toString();
      }
    } catch (err, stack) {
      _notificationService.error("Interface update failed!");
      _logger.severe('error updating merchant document', err, stack);
    }
  }
}
