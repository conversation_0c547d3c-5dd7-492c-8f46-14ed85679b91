// ignore_for_file: depend_on_referenced_packages

import 'package:backoffice/app/modules/_root/controller.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      record_key
      updated_at
      document
    }
  }
''';

final Logger _logger = Logger('ConfigService');

class PriceLevelsController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final ConfigService _configService = Get.find();

  final RootController _rootController = Get.find();

  RxString param = "".obs;

  RxInt offset = 0.obs;
  RxInt pageSize = 10.obs;

  RxBool sortAsc = false.obs;
  RxBool loading = true.obs;
  RxBool queryError = false.obs;
  RxBool editing = false.obs;

  RxInt selectedLevel = 0.obs;

  RxList<PriceLevel> levelList = <PriceLevel>[].obs;
  TextEditingController titleController = TextEditingController();

  @override
  Future<void> onInit() async {
    final SystemSettingJsonRecord levelRecord = (await _configService.getSystemSettings()).fold(
      (ServiceError e) {
        _notificationService.error("Failed to get system settings!");
        return SystemSettingJsonRecord.empty();
      },
      (SystemSettingJsonRecord r) => r,
    );
    levelList.value = levelRecord.document.priceLevels;
    sortLevels();
    editing = _rootController.disabled;
    loading.value = false;
    super.onInit();
  }

  Future<SystemDeviceJsonRecord> getSystemDevice() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'systemDevice',
          },
        ),
      );

      if (configResult.hasException) {
        throw configResult.exception.toString();
      }

      // ignore: always_specify_types
      if ((configResult.data!['json_record']! as List).isEmpty) {
        throw "No system devices found!";
      }

      return SystemDeviceJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
    } catch (err, stack) {
      _logger.severe(
        "Error getting system Devices.",
        err,
        stack,
      );
      queryError.value = true;
      return SystemDeviceJsonRecord.empty();
    }
  }

  Future<SectionsJsonRecord> getSectionsRecord() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'sections',
          },
        ),
      );

      if (configResult.hasException) {
        throw configResult.exception.toString();
      }

      // ignore: always_specify_types
      if ((configResult.data!['json_record']! as List).isEmpty) {
        throw "No sections found!";
      }

      return SectionsJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
    } catch (err, stack) {
      _logger.severe(
        "Error getting sections.",
        err,
        stack,
      );
      queryError.value = true;
      return SectionsJsonRecord.empty();
    }
  }

  Future<void> createLevelPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "create",
      "Price Levels",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate();
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> updateLevelPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "edit",
      "Price Levels",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate();
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  Future<void> deleteLevelPermission() async {
    final bool permission = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "delete",
      "Price Levels",
      _graphqlService,
    );
    if (permission) {
      editing.value = true;
      await confirmUpdate(
        delete: true,
      );
      editing.value = false;
    } else {
      _notificationService.error("You do not have permission");
    }
  }

  void sortLevels() {
    if (sortAsc.value) {
      levelList.sort((PriceLevel a, PriceLevel b) => b.desc.compareTo(a.desc));
    } else {
      levelList.sort((PriceLevel a, PriceLevel b) => a.desc.compareTo(b.desc));
    }
    loading.refresh();
  }

  List<DataRow> generateDataRows() {
    final List<DataRow> rows = <DataRow>[];
    for (int i = 0; i < levelList.length; i++) {
      rows.add(
        DataRow.byIndex(
          selected: selectedLevel.value == levelList[i].idx,
          onSelectChanged: (_) {
            if (editing.value) return;

            if (selectedLevel.value == levelList[i].idx) {
              selectedLevel.value = 0;
              titleController.text = "";
              return;
            }
            selectedLevel.value = levelList[i].idx;
            titleController.text = levelList[i].desc;
          },
          index: i,
          cells: <DataCell>[
            DataCell(
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      levelList[i].desc,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return rows;
  }

  Future<void> confirmUpdate({bool delete = false}) async {
    try {
      final SystemSettingJsonRecord settingsConfig = (await _configService.getSystemSettings()).fold(
        (ServiceError e) => throw e,
        (SystemSettingJsonRecord r) => r,
      );

      final List<PriceLevel> levelConfigList = settingsConfig.document.priceLevels;

      levelConfigList.sort((PriceLevel a, PriceLevel b) => a.idx.compareTo(b.idx));

      final PriceLevel createdLevel = PriceLevel(
        idx: selectedLevel.value < 1 ? levelConfigList.length + 1 : selectedLevel.value,
        desc: titleController.text,
      );

      if (selectedLevel.value < 1) {
        if (levelConfigList.length > 5) {
          _notificationService.error("Levels limit reached");
          return;
        }

        final PriceLevel? existing = levelConfigList.firstWhereOrNull(
          (PriceLevel ps) => ps.desc == titleController.text,
        );

        if (existing != null) {
          _notificationService.error("Price Schedule title must be unique");
          return;
        }

        levelConfigList.add(createdLevel);

        titleController.text = "";
      } else if (delete) {
        await updateItemsForDelete(levelConfigList.length);
        await updateCustomersForDelete(levelConfigList.length);
        await updateSectionsAndTerminalsForDelete();
        await updateOnlineOrderPriceLevelForDelete();

        levelConfigList.removeWhere(
          (PriceLevel level) => level.idx == selectedLevel.value,
        );

        for (final PriceLevel level in levelConfigList) {
          if (level.idx > selectedLevel.value) {
            level.idx = level.idx - 1;
          }
        }

        titleController.text = "";
        selectedLevel.value = 0;
      } else {
        final int idx = levelConfigList.indexWhere((PriceLevel ps) => ps.idx == createdLevel.idx);

        levelConfigList[idx] = createdLevel;
      }

      final Map<String, dynamic> jsonObject = settingsConfig.toJson();

      jsonObject.removeWhere((String key, dynamic value) => key == "updated_at");

      final QueryResult<Object?> objectResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": jsonObject,
          },
        ),
      );

      if (objectResult.hasException) {
        throw objectResult.exception.toString();
      }

      levelList.value = levelConfigList;
      sortLevels();

      _notificationService.success("Price levels updated!");
    } catch (err, stack) {
      _logger.severe(
        "Error updating price levels.",
        err,
        stack,
      );
      _notificationService.error("Error updating price levels.");
    }
  }

  Future<void> updateItemsForDelete(int levelsLength) async {
    try {
      final StringBuffer buffer = StringBuffer();

      for (int i = selectedLevel.value; i < levelsLength + 1; i++) {
        buffer.write('{document: {_cast: {String: {_like: "%S_L$i%"}}}}, ');
      }

      // ignore: non_constant_identifier_names
      final String GET_ITEMS = '''
      query GET_ITEMS {
        item(where: {_or: [$buffer]}) {
          item
          long_desc
          upc
          department
          created_at
          created_by
          updated_at
          updated_by
          document
          liq_ctl_plu
        }
      }
    ''';

      final QueryResult<Object?> itemsResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_ITEMS),
        ),
      );

      if (itemsResult.hasException) {
        throw itemsResult.exception.toString();
      }

      if ((itemsResult.data!['item'] as List<dynamic>).isEmpty) return;

      final List<Item> itemList = (itemsResult.data!['item'] as List<dynamic>)
          .map(
            (dynamic config) => Item.fromJson(
              config as Map<String, dynamic>,
            ),
          )
          .toList();

      for (final Item item in itemList) {
        final Map<String, int> pricingObject = item.document.pricing;
        final List<String> pricingKeys = pricingObject.keys.toList();
        final Map<String, int> newObject = <String, int>{};

        for (final String key in pricingKeys) {
          String newKey = key;
          final String subStr = key.substring(1);
          final List<String> splitStr = subStr.split("L");
          final int parsedInt = int.parse(splitStr[1]);

          if (splitStr[1] != selectedLevel.value.toString()) {
            if (parsedInt > selectedLevel.value) {
              newKey = "S${splitStr[0]}L${parsedInt - 1}";
            }

            newObject[newKey] = pricingObject[key] ?? 0;
          }
        }
        item.document.pricing = newObject;

        final QueryResult<Object> updateItemResult = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString(
              '''
           mutation UPDATE_ITEM_BY_PK(\$item: uuid!, \$document: jsonb) {
              update_item_by_pk(pk_columns: {item: \$item}, _set: {document: \$document}) {
                department
                document
                item
                long_desc
                upc
              }
            }
          ''',
            ),
            variables: <String, dynamic>{
              "item": item.item,
              "document": item.document.toJson(),
            },
          ),
        );

        if (updateItemResult.hasException) {
          throw updateItemResult.exception.toString();
        }
      }
    } catch (err, stack) {
      _logger.severe(
        "Error updating items.",
        err,
        stack,
      );
      _notificationService.error("Error updating items.");
    }
  }

  Future<void> updateSectionsAndTerminalsForDelete() async {
    final SystemDeviceJsonRecord systemDevices = await getSystemDevice();
    final SectionsJsonRecord sectionRecord = await getSectionsRecord();

    for (final SystemDeviceJsonRecordTerminal terminal in systemDevices.document.terminal) {
      if (terminal.priceLevel == selectedLevel.value) {
        terminal.priceLevel = 0;
      } else if (terminal.priceLevel > selectedLevel.value) {
        terminal.priceLevel = terminal.priceLevel - 1;
      }
    }

    for (final Section section in sectionRecord.document.sections) {
      section.priceLevel ??= 0;
      if (section.priceLevel == selectedLevel.value) {
        section.priceLevel = 0;
      } else if (section.priceLevel! > selectedLevel.value) {
        section.priceLevel = section.priceLevel! - 1;
      }
    }

    final Map<String, dynamic> terminalObject = systemDevices.toJson();
    final Map<String, dynamic> sectionObject = sectionRecord.toJson();

    terminalObject.removeWhere((String key, dynamic value) => key == "updated_at");
    sectionObject.removeWhere((String key, dynamic value) => key == "updated_at");

    final QueryResult<Object?> terminalResult = await _graphqlService.client.mutate(
      MutationOptions<Object?>(
        document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
        variables: <String, dynamic>{
          "json_record": terminalObject,
        },
      ),
    );
    if (terminalResult.hasException) {
      throw terminalResult.exception.toString();
    }

    final QueryResult<Object?> sectionResult = await _graphqlService.client.mutate(
      MutationOptions<Object?>(
        document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
        variables: <String, dynamic>{
          "json_record": sectionObject,
        },
      ),
    );
    if (sectionResult.hasException) {
      throw sectionResult.exception.toString();
    }
  }

  Future<void> updateCustomersForDelete(int levelsLength) async {
    final StringBuffer buffer = StringBuffer();

    for (int i = selectedLevel.value; i < levelsLength + 1; i++) {
      buffer.write(
        // ignore: unnecessary_string_escapes
        '{document: {_cast: {String: {_like: "%priceLevel_: $i%"}}}}, ',
      );
    }

    // ignore: non_constant_identifier_names
    final String GET_CUSTOMERS = '''
      query GET_CUSTOMERS {
        customer(where: {_or: [$buffer]}) {
          document
          customer
          created_by
          created_at
          search_customer_full_name
          updated_by
          updated_at
        }
      }
    ''';

    final QueryResult<Object?> customerResult = await _graphqlService.client.query(
      QueryOptions<Object?>(
        document: g.parseString(GET_CUSTOMERS),
      ),
    );

    if (customerResult.hasException) {
      throw customerResult.exception.toString();
    }

    if ((customerResult.data!['customer'] as List<dynamic>).isEmpty) return;

    final List<Customer> customerList = (customerResult.data!['customer'] as List<dynamic>)
        .map(
          (dynamic config) => Customer.fromJson(
            config as Map<String, dynamic>,
          ),
        )
        .toList();

    for (final Customer customer in customerList) {
      customer.document.priceLevel ??= 0;
      if (customer.document.priceLevel == selectedLevel.value) {
        customer.document.priceLevel = 0;
      } else if (customer.document.priceLevel! > selectedLevel.value) {
        customer.document.priceLevel = customer.document.priceLevel! - 1;
      }

      final QueryResult<Object> updateCustomerResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
           mutation UPDATE_CUSTOMER_BY_PK(\$customer: uuid!, \$document: jsonb) {
              update_customer_by_pk(pk_columns: {customer: \$customer}, _set: {document: \$document}) {
                document
                customer
                created_by
                created_at
                search_customer_full_name
                updated_by
                updated_at
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "customer": customer.customer,
            "document": customer.document.toJson(),
          },
        ),
      );

      if (updateCustomerResult.hasException) {
        return _notificationService.error(updateCustomerResult.exception.toString());
      }
    }
  }

  Future<void> updateOnlineOrderPriceLevelForDelete() async {
    final EcomSettingJsonRecord record = (await _configService.getEcomConfig()).fold(
      (ServiceError e) => throw e,
      (EcomSettingJsonRecord r) => r,
    );
    if (record.document.ecomPriceLevel == selectedLevel.value) {
      record.document.ecomPriceLevel = 0;
    } else if (record.document.ecomPriceLevel > selectedLevel.value) {
      record.document.ecomPriceLevel = record.document.ecomPriceLevel - 1;
    }
    (await _configService.upsertEcomConfig(record)).fold(
      (ServiceError e) => throw e,
      (EcomSettingJsonRecord r) => null,
    );
  }
}
