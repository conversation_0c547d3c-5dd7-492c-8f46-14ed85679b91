// ignore_for_file: depend_on_referenced_packages

import 'package:backoffice/app/modules/settings/children/rooms/chart_view/dashboard.dart';
import 'package:backoffice/app/modules/settings/children/rooms/chart_view/elements/flow_element.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  record_key
  updated_at
  document
''';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String DELETE_TABLESSCROLL = '''
  mutation DELETE_TABLESSCROLL {
    update_employee(where: {document: {_has_key: "tablesScroll"}}, _delete_key: {document: "tablesScroll"}) {
      returning {
        id
      }
    }
  }
''';

const String DELETE_TABLESROOM = '''
  mutation DELETE_TABLESROOM {
    update_employee(where: {document: {_has_key: "tablesRoom"}}, _delete_key: {document: "tablesRoom"}) {
      returning {
        id
      }
    }
  }
''';

class TablesController extends GetxController {
  TablesController();

  final Logger _logger = Logger('ConfigService');

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final RxInt currentRoomIdx = 1.obs;
  final RxBool isLoading = true.obs;
  final RxString newRoomDesc = "".obs;
  final Rx<FlowElement> selectedElement = FlowElement().obs;
  List<RoomsJsonRecordRoom> roomList = <RoomsJsonRecordRoom>[];
  List<Section> sectionList = <Section>[];
  List<FlowElement> textList = <FlowElement>[];
  List<FlowElement> tableList = <FlowElement>[];

  final ScrollController verticalScrollController = ScrollController();
  final ScrollController horizontalScrollController = ScrollController();
  final TextEditingController changeTextController = TextEditingController();
  final TextEditingController newRoomTextController = TextEditingController();
  final GlobalKey chartKey = GlobalKey();
  Dashboard dashboard = Dashboard();
  Offset oldPosition = Offset.zero;

  bool roomDeleted = false;

  @override
  void onReady() {
    getTableConfig();
    super.onReady();
  }

  Future<void> selectElement(FlowElement element) async {
    if (selectedElement.value.desc != "") {
      deselectElement();
      // deactivateEditElement();
      await Future<void>.delayed(
        const Duration(milliseconds: 200),
      );
    }
    element.setBorderColor(R2Colors.primary400);
    selectedElement.value = element;
    if (element.kind == ElementKind.text) {
      changeTextController.text = element.desc;
    }
  }

  void deselectElement() {
    if (selectedElement.value.id != "") {
      // deactivateEditElement();
      selectedElement.value.setBorderColor(R2Colors.neutral400);
      selectedElement.value = FlowElement();
      changeTextController.text = "";
    }
  }

  void rotateElementRight() {
    if (selectedElement.value.id == "") {
      _notificationService.error("No ElementSelected");
      return;
    }
    selectedElement.value.changeRotationRight();
  }

  void rotateElementLeft() {
    if (selectedElement.value.id == "") {
      _notificationService.error("No Element Selected");
      return;
    }
    selectedElement.value.changeRotationLeft();
  }

  void increaseElementSize() {
    if (selectedElement.value.kind == ElementKind.text) return;
    if (selectedElement.value.id == "") {
      _notificationService.error("No Table Selected");
      return;
    }
    selectedElement.value.changeSize(
      Size(
        selectedElement.value.size.width + 24,
        selectedElement.value.size.height + 24,
      ),
    );
  }

  void decreaseElementSize() {
    if (selectedElement.value.kind == ElementKind.text) return;
    if (selectedElement.value.id == "") {
      _notificationService.error("No Table Selected");
      return;
    }
    selectedElement.value.changeSize(
      Size(
        selectedElement.value.size.width - 24,
        selectedElement.value.size.height - 24,
      ),
    );
  }

  void deleteElement() {
    if (selectedElement.value.id == "") {
      _notificationService.error("No Element Selected");
      return;
    }
    dashboard.removeElement(selectedElement.value);
    if (selectedElement.value.kind == ElementKind.text) {
      textList.remove(selectedElement.value);
    } else {
      tableList.remove(selectedElement.value);
    }
    selectedElement.value = FlowElement();
    _notificationService.success("Element Deleted");
  }

  void deleteAllElements() {
    dashboard.removeAllElements();
    selectedElement.value = FlowElement();
    tableList.removeWhere(
      (FlowElement element) => element.roomIdx == currentRoomIdx.value,
    );
    textList.removeWhere(
      (FlowElement element) => element.roomIdx == currentRoomIdx.value,
    );
    selectedElement.value = FlowElement();
  }

  void changeRooms(int idx) {
    deselectElement();
    dashboard.removeAllElements();
    if (currentRoomIdx.value == 1 && roomList.length > 1) {
      currentRoomIdx.value = 2;
    }
    currentRoomIdx.value = idx;
    addDashboardElements();
    isLoading.refresh();
  }

  void addRoom() {
    if (newRoomDesc.value == "") {
      _notificationService.error("Room description must not be blank.");
      return;
    }
    roomList.add(
      RoomsJsonRecordRoom(
        desc: newRoomDesc.value,
        prcLvl: 0,
        idx: roomList.length + 1,
      ),
    );
    changeRooms(roomList.length);
  }

  void editRoomDescription() {
    if (newRoomDesc.value == "") {
      _notificationService.error("Room description must not be blank.");
      return;
    }
    roomList[currentRoomIdx.value - 1].desc = newRoomDesc.value;
    currentRoomIdx.refresh();
  }

  void deleteRoom() {
    deleteAllElements();
    roomList.removeAt(currentRoomIdx.value - 1);
    for (int i = 0; i < roomList.length; i++) {
      for (final FlowElement table in tableList) {
        if (table.roomIdx == roomList[i].idx) {
          table.roomIdx = i + 1;
        }
      }
      for (final FlowElement text in textList) {
        if (text.roomIdx == roomList[i].idx) {
          text.roomIdx = i + 1;
        }
      }
      roomList[i].idx = i + 1;
    }
    roomDeleted = true;
    changeRooms(1);
  }

  void addElement({
    ElementKind kind = ElementKind.rectangle,
    Size size = const Size(96, 96),
  }) {
    if (kind == ElementKind.text) {
      final FlowElement newElement = FlowElement(
        position: Offset(
          250 + horizontalScrollController.offset,
          250 + verticalScrollController.offset,
        ),
        size: const Size(156, 48),
        desc: "Default Text",
        kind: ElementKind.text,
        seatCnt: 0,
        idx: dashboard.textElements.length + 1,
        roomIdx: currentRoomIdx.value,
      );
      textList.add(newElement);
      dashboard.addElement(textList[textList.length - 1]);
      return;
    }
    final FlowElement newElement = FlowElement(
      position: Offset(
        250 + horizontalScrollController.offset,
        250 + verticalScrollController.offset,
      ),
      size: size,
      desc: newRoomDesc.value,
      kind: kind,
      seatCnt: size.width > size.height ? 6 : 4,
      idx: dashboard.tableElements.length + 1,
      roomIdx: currentRoomIdx.value,
    );
    tableList.add(newElement);
    dashboard.addElement(tableList[tableList.length - 1]);
  }

  bool checkForDuplicateTableDesc() {
    final FlowElement? existing = tableList.firstWhereOrNull(
      (FlowElement t) => t.desc.toLowerCase() == newRoomDesc.value.toLowerCase(),
    );
    if (existing != null) {
      _notificationService.error("Table description already exists.");
      return true;
    }
    return false;
  }

  bool checkForDuplicateRoomDesc() {
    final RoomsJsonRecordRoom? existing = roomList.firstWhereOrNull(
      (RoomsJsonRecordRoom r) => r.desc.toLowerCase() == newRoomDesc.value.toLowerCase(),
    );
    if (existing != null) {
      _notificationService.error("Room description already exists.");
      return true;
    }
    return false;
  }

  Future<void> getTableConfig() async {
    try {
      final QueryResult<Object?> roomResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{"record_key": "rooms"},
        ),
      );
      final QueryResult<Object?> sectionResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{"record_key": "sections"},
        ),
      );

      RoomsJsonRecord tableConfig = RoomsJsonRecord.empty();
      SectionsJsonRecord sectionConfig = SectionsJsonRecord.empty();

      final List<dynamic> roomResList = roomResult.data!['json_record']! as List<dynamic>;
      final List<dynamic> sectionResList = sectionResult.data!['json_record']! as List<dynamic>;

      if (roomResList.isNotEmpty) {
        // ignore: avoid_dynamic_calls
        if (roomResList.first["document"] == null) {
          // ignore: avoid_dynamic_calls
          roomResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
        }
        tableConfig = RoomsJsonRecord.fromJson(
          // ignore: avoid_dynamic_calls
          roomResList.first as Map<String, dynamic>,
        );
      }

      if (sectionResList.isNotEmpty) {
        // ignore: avoid_dynamic_calls
        if (sectionResList.first["document"] == null) {
          // ignore: avoid_dynamic_calls
          sectionResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
        }
        sectionConfig = SectionsJsonRecord.fromJson(
          // ignore: avoid_dynamic_calls
          sectionResList.first as Map<String, dynamic>,
        );
      }

      roomList = tableConfig.document.rooms;
      sectionList = sectionConfig.document.sections;

      roomList.sort(
        (RoomsJsonRecordRoom a, RoomsJsonRecordRoom b) => a.idx.compareTo(b.idx),
      );
      sectionList.sort(
        (Section a, Section b) => a.idx.compareTo(b.idx),
      );

      textList = tableConfig.document.text
          .map(
            (RoomsJsonRecordText element) => FlowElement(
              position: Offset(
                (element.X + (24 ~/ 2)) ~/ 24 * 24,
                (element.Y + (24 ~/ 2)) ~/ 24 * 24,
              ),
              size: Size(
                (element.desc.length * 12) + 12,
                48,
              ),
              desc: element.desc,
              kind: ElementKind.text,
              rotation: element.rotation ?? 0,
              seatCnt: 0,
              idx: element.idx,
              fgnd: element.fgnd,
              bgnd: element.bgnd,
              roomIdx: element.roomIdx,
            ),
          )
          .toList();
      tableList = tableConfig.document.tables
          .map(
            (RoomsJsonRecordTable element) => FlowElement(
              position: Offset(
                (element.X + (24 ~/ 2)) ~/ 24 * 24,
                (element.Y + (24 ~/ 2)) ~/ 24 * 24,
              ),
              size: Size(element.width, element.height),
              desc: element.desc,
              kind: element.shape < 3 ? ElementKind.oval : ElementKind.rectangle,
              rotation: element.rotation,
              seatCnt: element.seatCnt,
              idx: element.idx,
              sectIdx: element.sectIdx,
              roomIdx: element.roomIdx,
            ),
          )
          .toList();

      Future<void>.delayed(
        const Duration(milliseconds: 500),
        () => addDashboardElements(),
      );
    } catch (err, stack) {
      _logger.severe(
        "error fetching merchant config",
        err,
        stack,
      );
    }
  }

  void resetDialogText() {
    newRoomDesc.value = "";
    newRoomTextController.text = "";
  }

  Size getRoomSize(int idx) {
    const double buffer = 168;
    double maxWidth = 600;
    double maxHeight = 600;
    bool widthChange = false;
    bool heightChange = false;

    for (final FlowElement text in textList) {
      if (text.roomIdx == idx) {
        if (((text.position.dx + (24 ~/ 2)) ~/ 24 * 24) > maxWidth) {
          maxWidth = (text.position.dx + (24 ~/ 2)) ~/ 24 * 24;
          widthChange = true;
        }
        if (((text.position.dy + (24 ~/ 2)) ~/ 24 * 24) > maxHeight) {
          maxHeight = (text.position.dy + (24 ~/ 2)) ~/ 24 * 24;
          heightChange = true;
        }
      }
    }
    for (final FlowElement table in tableList) {
      if (table.roomIdx == idx) {
        if (((table.position.dx + (24 ~/ 2)) ~/ 24 * 24) > maxWidth) {
          maxWidth = (table.position.dx + (24 ~/ 2)) ~/ 24 * 24;
          widthChange = true;
        }
        if (((table.position.dy + (24 ~/ 2)) ~/ 24 * 24) > maxHeight) {
          maxHeight = (table.position.dy + (24 ~/ 2)) ~/ 24 * 24;
          heightChange = true;
        }
      }
    }
    return Size(
      widthChange ? maxWidth + buffer : maxWidth,
      heightChange ? maxHeight + buffer : maxHeight,
    );
  }

  void addDashboardElements() {
    if (roomList.isEmpty) {
      dashboard.setDashboardSize(Size.zero);
      isLoading.value = false;
      return;
    }
    final Size dashSize = getRoomSize(currentRoomIdx.value);
    dashboard.setDashboardSize(dashSize);
    for (final FlowElement element in textList) {
      if (element.roomIdx == currentRoomIdx.value) {
        dashboard.addElement(element);
      }
    }
    for (final FlowElement element in tableList) {
      if (element.roomIdx == currentRoomIdx.value) {
        dashboard.addElement(element);
      }
    }
    isLoading.value = false;
  }

  Future<void> saveTables() async {
    try {
      final List<RoomsJsonRecordTable> tableConfig = <RoomsJsonRecordTable>[];
      final List<RoomsJsonRecordText> textConfig = <RoomsJsonRecordText>[];

      for (int i = 0; i < tableList.length; i++) {
        tableConfig.add(
          RoomsJsonRecordTable(
            desc: tableList[i].desc,
            X: tableList[i].position.dx,
            Y: tableList[i].position.dy,
            width: tableList[i].size.width,
            height: tableList[i].size.height,
            idx: i + 1,
            roomIdx: tableList[i].roomIdx,
            rotation: tableList[i].rotation,
            shape: tableList[i].kind == ElementKind.oval ? 1 : 10,
            seatCnt: tableList[i].seatCnt,
            sectIdx: tableList[i].sectIdx,
          ),
        );
      }
      for (int i = 0; i < textList.length; i++) {
        textConfig.add(
          RoomsJsonRecordText(
            desc: textList[i].desc,
            X: textList[i].position.dx,
            Y: textList[i].position.dy,
            width: textList[i].size.width,
            height: textList[i].size.height,
            idx: i + 1,
            roomIdx: textList[i].roomIdx,
            rotation: textList[i].rotation,
            fgnd: textList[i].fgnd,
            bgnd: textList[i].bgnd,
            font: "System",
            orient: 0,
          ),
        );
      }

      tableConfig.sort(
        (RoomsJsonRecordTable a, RoomsJsonRecordTable b) => a.idx.compareTo(b.idx),
      );
      textConfig.sort(
        (RoomsJsonRecordText a, RoomsJsonRecordText b) => a.idx.compareTo(b.idx),
      );

      final Map<String, dynamic> roomsObject = RoomsJsonRecord(
        record_key: "rooms",
        updated_at: DateTime.now(),
        document: RoomsJsonRecordDocument(
          text: textConfig,
          rooms: roomList,
          tables: tableConfig,
        ),
      ).toJson();

      roomsObject.removeWhere((String key, dynamic value) => key == "updated_at");

      final QueryResult<Object?> objectResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": roomsObject,
          },
        ),
      );

      if (objectResult.hasException) {
        throw objectResult.exception.toString();
      }

      if (roomDeleted) {
        final QueryResult<Object?> tsResult = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(DELETE_TABLESSCROLL),
          ),
        );

        if (tsResult.hasException) {
          throw tsResult.exception.toString();
        }

        final QueryResult<Object?> trResult = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(DELETE_TABLESROOM),
          ),
        );

        if (trResult.hasException) {
          throw trResult.exception.toString();
        }
      }

      _notificationService.success("Rooms Saved!");
    } catch (err, stack) {
      _logger.severe(
        "error upserting rooms or sections config",
        err,
        stack,
      );
    }
  }
}
