// ignore_for_file: depend_on_referenced_packages

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/view_models/start_end_clock.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/modules/settings/children/sections/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/global_widgets/custom_time_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  record_key
  updated_at
  document
''';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String GET_SYSTEM_DEVICES_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

class SectionsDialogController extends GetxController {
  SectionsDialogController({this.idx});
  int? idx;

  final Logger _logger = Logger('ConfigService');

  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  final SectionsController _sectionsController = Get.find();

  final TextEditingController descController = TextEditingController();
  final TextEditingController saleNameController = TextEditingController();
  final TextEditingController textBlinkController = TextEditingController();
  final TextEditingController textRedController = TextEditingController();
  final TextEditingController gratAmtController = TextEditingController();
  final TextEditingController minGratCustCntController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  final GlobalKey<FormState> sectionFormKey = GlobalKey<FormState>();

  final RxList<Section> sectionList = <Section>[].obs;
  final RxList<PriceLevel> levelList = <PriceLevel>[].obs;
  final RxList<String> printerFieldError = <String>[].obs;

  RxBool loading = true.obs;
  RxBool editing = false.obs;

  Section selectedSection = Section(desc: "", idx: 0);

  RxList<StartEndClock> timerDisplay = <StartEndClock>[].obs;
  RxList<PrepDevice> availablePrinter = <PrepDevice>[].obs;

  bool isErrored = false;
  List<bool> printerScheduleError = <bool>[false, false, false, false, false, false, false];
  late Worker loadingEver;

  @override
  Future<void> onInit() async {
    await getSectionsConfig();
    if (idx != null) {
      selectedSection = sectionList[idx! - 1];
    }
    descController.text = selectedSection.desc;
    millisecondsToTimeInts(selectedSection.printerSchedule);
    await getPrinterList();

    loading.value = false;

    loadingEver = ever<bool>(loading, (bool val) {
      if (isErrored && !val) {
        if (sectionFormKey.currentState!.validate()) {
          isErrored = false;
        }
      }
    });

    super.onInit();
  }

  @override
  void onClose() {
    loadingEver.dispose();
    super.onClose();
  }

  void addSection() {
    selectedSection.idx = sectionList.length + 1;
    sectionList.add(
      selectedSection,
    );
    _notificationService.success("Section Added!");
  }

  Future<void> getSectionsConfig() async {
    try {
      final QueryResult<Object?> sectionResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{"record_key": "sections"},
        ),
      );
      final SystemSettingJsonRecord settingsConfig = await getSystemSettings();

      SectionsJsonRecord sectionConfig = SectionsJsonRecord.empty();

      final List<dynamic> sectionResList = sectionResult.data!['json_record']! as List<dynamic>;

      if (sectionResList.isNotEmpty) {
        // ignore: avoid_dynamic_calls
        if (sectionResList.first["document"] == null) {
          // ignore: avoid_dynamic_calls
          sectionResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
        }
        sectionConfig = SectionsJsonRecord.fromJson(
          // ignore: avoid_dynamic_calls
          sectionResList.first as Map<String, dynamic>,
        );
      }

      sectionList.value = sectionConfig.document.sections;
      levelList.value = settingsConfig.document.priceLevels;

      sectionList.sort(
        (Section a, Section b) => a.idx.compareTo(b.idx),
      );
    } catch (err, stack) {
      _logger.severe(
        "error fetching json records.",
        err,
        stack,
      );
    }
  }

  Future<void> saveSections({bool delete = false}) async {
    try {
      editing.value = true;

      if (!delete && !sectionFormKey.currentState!.validate()) {
        if (printerScheduleError.contains(true)) {
          _notificationService.error("Reroute printer required!");
        } else {
          _notificationService.error("Failed to store Section!");
        }
        isErrored = true;
        throw "Invalid form";
      }

      sectionList.sort(
        (Section a, Section b) => a.idx.compareTo(b.idx),
      );

      final SystemSettingJsonRecord jobCodesConfig = await getSystemSettings();

      if (jobCodesConfig.document.priceLevels.join() != levelList.join()) {
        final Map<int, String> levelsMap1 = <int, String>{};
        for (final PriceLevel level in jobCodesConfig.document.priceLevels) {
          levelsMap1[level.idx] = level.desc;
        }
        final Map<int, String> levelsMap2 = <int, String>{};
        for (final PriceLevel level in levelList) {
          levelsMap2[level.idx] = level.desc;
        }

        for (final Section sect in sectionList) {
          if ((sect.priceLevel ?? 0) != 0) {
            if (levelsMap1[sect.priceLevel] != levelsMap2[sect.priceLevel]) {
              final List<int> levelsKeys = levelsMap1.keys.toList();

              for (final int key in levelsKeys) {
                if (levelsMap1[key] == levelsMap2[sect.priceLevel]) {
                  sect.priceLevel = key;
                }
              }
            }
          }
        }
      }

      if (idx == null) {
        addSection();
      } else if (delete) {
        final SystemDeviceJsonRecord terminalsConfig = await getTerminalRecord();
        final RoomsJsonRecord tablesConfig = await getRoomRecord();

        final List<RoomsJsonRecordTable> tableList = tablesConfig.document.tables;
        final List<SystemSettingJsonRecordJobCode> jobCodeList = jobCodesConfig.document.jobCodes;
        final List<SystemDeviceJsonRecordTerminal> terminalList = terminalsConfig.document.terminal;

        tableList.sort(
          (RoomsJsonRecordTable a, RoomsJsonRecordTable b) => a.idx.compareTo(b.idx),
        );
        jobCodeList.sort(
          (
            SystemSettingJsonRecordJobCode a,
            SystemSettingJsonRecordJobCode b,
          ) =>
              a.index.compareTo(b.index),
        );
        terminalList.sort(
          (
            SystemDeviceJsonRecordTerminal a,
            SystemDeviceJsonRecordTerminal b,
          ) =>
              a.idx.compareTo(b.idx),
        );

        final int deletedIdx = selectedSection.idx;

        sectionList.removeAt(deletedIdx - 1);

        for (final RoomsJsonRecordTable table in tableList) {
          if (table.sectIdx == deletedIdx) {
            table.sectIdx = 0;
          } else if (table.sectIdx > deletedIdx) {
            table.sectIdx -= 1;
          }
        }
        for (final SystemSettingJsonRecordJobCode code in jobCodeList) {
          if (code.section == deletedIdx) {
            code.section = 0;
          } else if (code.section > deletedIdx) {
            code.section -= 1;
          }
        }
        for (final SystemDeviceJsonRecordTerminal terminal in terminalList) {
          if (terminal.section == deletedIdx) {
            terminal.section = 0;
          } else if (terminal.section > deletedIdx) {
            terminal.section = terminal.section - 1;
          }
        }
        for (int i = 0; i < sectionList.length; i++) {
          sectionList[i].idx = i + 1;
        }

        jobCodesConfig.document.jobCodes = jobCodeList;
        terminalsConfig.document.terminal = terminalList;
        tablesConfig.document.tables = tableList;

        final Map<String, dynamic> jobCodesObject = jobCodesConfig.toJson();
        final Map<String, dynamic> terminalsObject = terminalsConfig.toJson();
        final Map<String, dynamic> roomsObject = tablesConfig.toJson();

        await saveJsonObject(roomsObject);
        await saveJsonObject(jobCodesObject);
        await saveJsonObject(terminalsObject);
      }

      final Map<String, dynamic> sectionsObject = SectionsJsonRecord(
        record_key: "sections",
        updated_at: DateTime.now(),
        document: SectionsJsonRecordDocument(
          sections: sectionList,
        ),
      ).toJson();

      await saveJsonObject(sectionsObject);

      await _sectionsController.onInit();

      if (idx == null) {
        _notificationService.success("Section Added!");
        Get.back();
      } else {
        if (delete) {
          _notificationService.success("Section Deleted!");
        } else {
          _notificationService.success("Section Updated!");
        }
        await Get.offNamed(
          AppRoutes.SETTINGS_SECTIONS,
          id: AppRoutes.id,
        );
      }
    } catch (err, stack) {
      _logger.severe(
        "error updating sections.",
        err,
        stack,
      );
    }
  }

  Future<void> saveJsonObject(Map<String, dynamic> object) async {
    object.removeWhere((String key, dynamic value) => key == "updated_at");

    final QueryResult<Object?> objectResult = await _graphqlService.client.mutate(
      MutationOptions<Object?>(
        document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
        variables: <String, dynamic>{
          "json_record": object,
        },
      ),
    );

    if (objectResult.hasException) {
      throw objectResult.exception.toString();
    }
  }

  Future<RoomsJsonRecord> getRoomRecord() async {
    final QueryResult<Object?> roomResult = await _graphqlService.client.query(
      QueryOptions<Object?>(
        document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
        fetchPolicy: FetchPolicy.noCache,
        variables: const <String, dynamic>{"record_key": "rooms"},
      ),
    );

    final List<dynamic> roomResList = roomResult.data!['json_record']! as List<dynamic>;

    if (roomResList.isNotEmpty) {
      // ignore: avoid_dynamic_calls
      if (roomResList.first["document"] == null) {
        // ignore: avoid_dynamic_calls
        roomResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
      }
      return RoomsJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        roomResList.first as Map<String, dynamic>,
      );
    }

    return RoomsJsonRecord.empty();
  }

  Future<void> getPrinterList() async {
    final QueryResult<Object> queryResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          GET_SYSTEM_DEVICES_QUERY,
        ),
      ),
    );

    final SystemDeviceJsonRecord systemDeviceJsonRecord = (queryResult.data!['json_record'] as List<dynamic>)
        .map(
          (dynamic systemDeviceRecord) => SystemDeviceJsonRecord.fromJson(
            systemDeviceRecord as Map<String, dynamic>,
          ),
        )
        .toList()
        .first;

    availablePrinter.value = systemDeviceJsonRecord.document.prep;
  }

  Future<SystemSettingJsonRecord> getSystemSettings() async {
    final QueryResult<Object?> settingsResult = await _graphqlService.client.query(
      QueryOptions<Object?>(
        document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
        fetchPolicy: FetchPolicy.noCache,
        variables: const <String, dynamic>{"record_key": "systemSetting"},
      ),
    );
    SystemSettingJsonRecord settingsConfig = SystemSettingJsonRecord.empty();

    final List<dynamic> settingsResList = settingsResult.data!['json_record']! as List<dynamic>;

    if (settingsResList.isNotEmpty) {
      // ignore: avoid_dynamic_calls
      if (settingsResList.first["document"] == null) {
        // ignore: avoid_dynamic_calls
        settingsResList.first["document"] = SystemSettingJsonRecord.empty().document.toJson();
      }
      settingsConfig = SystemSettingJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        settingsResList.first as Map<String, dynamic>,
      );
    }

    return settingsConfig;
  }

  Future<SystemDeviceJsonRecord> getTerminalRecord() async {
    final QueryResult<Object?> terminalsResult = await _graphqlService.client.query(
      QueryOptions<Object?>(
        document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
        fetchPolicy: FetchPolicy.noCache,
        variables: const <String, dynamic>{"record_key": "systemDevice"},
      ),
    );

    SystemDeviceJsonRecord terminalsConfig = SystemDeviceJsonRecord.empty();

    final List<dynamic> terminalsResList = terminalsResult.data!['json_record']! as List<dynamic>;

    if (terminalsResList.isNotEmpty) {
      // ignore: avoid_dynamic_calls
      if (terminalsResList.first["document"] == null) {
        // ignore: avoid_dynamic_calls
        terminalsResList.first["document"] = SystemDeviceJsonRecord.empty().document.toJson();
      }
      terminalsConfig = SystemDeviceJsonRecord.fromJson(
        // ignore: avoid_dynamic_calls
        terminalsResList.first as Map<String, dynamic>,
      );
    }

    return terminalsConfig;
  }

  /// Description:
  /// -Converts ints in to milliseconds
  ///
  /// Parameters
  /// - int hour - positive integer value for hours.
  /// - int minutes - positive integer value for minutes.
  /// - bool pm - True if it is an afternoon time, false otherwise.
  int intsToMilliseconds({
    required int hour,
    required int minute,
    required bool pm,
  }) {
    final bool addHours = pm && hour != 12;
    if (hour == 12 && !pm) hour = 0;
    final int hours = addHours ? hour + 12 : hour;
    return Duration(minutes: (hours * 60) + minute).inMilliseconds;
  }

  /// Description:
  /// -Converts milliseconds into ints and adds them to the [timerDisplay] list
  ///
  /// Parameters
  /// - List<PrinterSchedule> listSchedule - list of printer schedules
  void millisecondsToTimeInts(List<PrinterSchedule> listSchedule) {
    for (final PrinterSchedule schedule in listSchedule) {
      final int startMinutesFromMS = Duration(milliseconds: schedule.startTime).inMinutes;
      final int endMinutesFromMS = Duration(milliseconds: schedule.endTime).inMinutes;

      int startHour = startMinutesFromMS ~/ 60;
      int endHour = endMinutesFromMS ~/ 60;

      final bool isStartTimePM = startHour > 11;
      final bool isEndTimePM = endHour > 11;

      if (startHour > 12) startHour -= 12;
      if (endHour > 12) endHour -= 12;

      final StartEndClock tmr = StartEndClock(
        index: schedule.idx,
        startHour: startHour == 0 ? 12 : startHour,
        startMinute: startMinutesFromMS % 60,
        sPmTime: isStartTimePM,
        endHour: endHour == 0 ? 12 : endHour,
        endMinute: endMinutesFromMS % 60,
        ePmTime: isEndTimePM,
      );

      timerDisplay.add(tmr);
    }
  }

  /// - Displays the widget to set the time for the start time.
  /// - Updates the start time for a schedule.
  ///
  /// Parameters
  /// - int index - index of the schedule to use.
  Future<void> onTapStartTime(PrinterSchedule schedule) async {
    final int index = schedule.idx;
    final int hour = timerDisplay[index].startHourController.value;
    final int min = timerDisplay[index].startMinuteController.value;
    final bool pm = timerDisplay[index].startPmTime.value;

    await Get.defaultDialog(
      title: "Start Time",
      content: Column(
        children: <Widget>[
          CustomTimePicker(
            hourController: timerDisplay[index].startHourController,
            minuteController: timerDisplay[index].startMinuteController,
            pmTime: timerDisplay[index].startPmTime,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () {
                  Get.back(result: false);
                },
              ),
              DialogButton(
                buttonType: EDialogButtonType.AFFIRMATIVE,
                onTapped: () async {
                  if (checkIfPrevDayOverlaps(schedule)) {
                    await Get.dialog(
                      AlertDialog(
                        title: const Text("Notice"),
                        actions: <Widget>[
                          Center(
                            child: DialogButton(
                              buttonType: EDialogButtonType.AFFIRMATIVE,
                              buttonText: 'ok',
                              onTapped: () {
                                Get.back();
                              },
                            ),
                          ),
                        ],
                        content: const Text(
                          "There is an overlap with this schedule and the previous day's schedule.",
                        ),
                      ),
                    );
                  }
                  Get.back(result: true);
                },
              ),
            ],
          ),
        ],
      ),
    ).then((dynamic value) {
      if (value != true) {
        timerDisplay[index].startHourController = hour.obs;
        timerDisplay[index].startMinuteController = min.obs;
        timerDisplay[index].startPmTime = pm.obs;
      }
      schedule.startTime = intsToMilliseconds(
        hour: timerDisplay[index].startHourController.value,
        minute: timerDisplay[index].startMinuteController.value,
        pm: timerDisplay[index].startPmTime.value,
      );
    });
  }

  /// Description
  /// - Checks to see if the current day's schedule conflicts with the previous day's schedule.
  ///
  /// Parameters
  /// - PrinterSchedule schedule - Current day's schedule
  ///
  /// Returns
  /// - True if there is an overlap with the previous day's end time and the current day's start time.
  /// - False otherwise.
  bool checkIfPrevDayOverlaps(PrinterSchedule schedule) {
    final int currentDayId = schedule.idx;
    final int prevDayId = currentDayId == 0 ? 6 : currentDayId - 1;

    final PrinterSchedule prevDaySchedule = selectedSection.printerSchedule.firstWhere((PrinterSchedule ps) => ps.idx == prevDayId);

    return compareSchedule(prevDaySchedule, schedule);
  }

  /// Description
  /// - Checks to see if the current day's schedule conflicts with the next day's schedule.
  ///
  /// Parameters
  /// - PrinterSchedule schedule - Current day's schedule
  ///
  /// Returns
  /// - True if there is an overlap with the next day's start time and the current day's end time.
  /// - False otherwise.
  bool checkIfNextDayOverlaps(PrinterSchedule schedule) {
    final int currentDayId = schedule.idx;

    final int nextDayId = currentDayId == 6 ? 0 : currentDayId + 1;

    final PrinterSchedule nextDaySchedule = selectedSection.printerSchedule.firstWhere((PrinterSchedule ps) => ps.idx == nextDayId);

    return compareSchedule(schedule, nextDaySchedule);
  }

  /// Description
  /// - Checks to see if the previous schedule and the next schedule
  ///
  /// Parameters
  /// - PrinterSchedule prevSchedule - The previous day schedule Use the current day if comparing the next day.
  /// - PrinterSchedule nextSchedule - The next day schedule. Use the current day if comparing the previous day.
  ///
  /// Returns
  /// - True if there is an overlap with the nextSchedule start time and the prevSchedule day's end time.
  /// - False if:
  ///   - Either prevSchedule or nextSchedule are not enabled
  ///   - prevSchedule does not got over to the next day.
  ///   - there is no overlap with the nextSchedule start time and the prevSchedule day's end time.
  bool compareSchedule(PrinterSchedule prevSchedule, PrinterSchedule nextSchedule) {
    final int prevScheduleId = prevSchedule.idx;

    final int nextscheduleId = nextSchedule.idx;

    final int prevEndTime = intsToMilliseconds(
      hour: timerDisplay[prevScheduleId].endHourController.value,
      minute: timerDisplay[prevScheduleId].endMinuteController.value,
      pm: timerDisplay[prevScheduleId].endPmTime.value,
    );

    final int nextStartTime = intsToMilliseconds(
      hour: timerDisplay[nextscheduleId].startHourController.value,
      minute: timerDisplay[nextscheduleId].startMinuteController.value,
      pm: timerDisplay[nextscheduleId].startPmTime.value,
    );

    if (prevEndTime > prevSchedule.startTime) {
      return false;
    }

    if (!prevSchedule.enabled || !nextSchedule.enabled) {
      return false;
    }

    return prevEndTime > nextStartTime;
  }

  /// Description:
  /// - Displays the widget to set the time for the end time.
  /// - Updates the end time for a schedule.
  ///
  /// Parameters
  /// - int index - index of the schedule to use.
  Future<void> onTapEndTime(PrinterSchedule schedule) async {
    final int index = schedule.idx;
    final int hour = timerDisplay[index].endHourController.value;
    final int min = timerDisplay[index].endMinuteController.value;
    final bool pm = timerDisplay[index].endPmTime.value;
    await Get.defaultDialog(
      title: "End Time",
      content: Column(
        children: <Widget>[
          CustomTimePicker(
            hourController: timerDisplay[index].endHourController,
            minuteController: timerDisplay[index].endMinuteController,
            pmTime: timerDisplay[index].endPmTime,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () {
                  Get.back(result: false);
                },
              ),
              DialogButton(
                buttonType: EDialogButtonType.AFFIRMATIVE,
                onTapped: () async {
                  if (checkIfNextDayOverlaps(schedule)) {
                    await Get.dialog(
                      AlertDialog(
                        title: const Text("Notice"),
                        actions: <Widget>[
                          Center(
                            child: DialogButton(
                              buttonType: EDialogButtonType.AFFIRMATIVE,
                              buttonText: 'ok',
                              onTapped: () {
                                Get.back();
                              },
                            ),
                          ),
                        ],
                        content: const Text(
                          "There is an overlap with this schedule and the next day's schedule.",
                        ),
                      ),
                    );
                  }
                  Get.back(result: true);
                },
              ),
            ],
          ),
        ],
      ),
    ).then((dynamic value) {
      if (value != true) {
        timerDisplay[index].endHourController.value = hour;
        timerDisplay[index].endMinuteController.value = min;
        timerDisplay[index].endPmTime.value = pm;
      }
      schedule.endTime = intsToMilliseconds(
        hour: timerDisplay[index].endHourController.value,
        minute: timerDisplay[index].endMinuteController.value,
        pm: timerDisplay[index].endPmTime.value,
      );
    });
  }

  /// Description:
  /// - Displays the start time as a human readable string.
  ///
  /// Parameters
  /// - int index - index of the schedule to use.
  ///
  /// Returns
  /// - The start time as a human readable string.
  String getStartTimeString(int index) {
    final int hour = timerDisplay[index].startHourController.value;
    final int minute = timerDisplay[index].startMinuteController.value;
    return "$hour:${minute < 10 ? "0" : ""}$minute ${timerDisplay[index].startPmTime.value ? "PM" : "AM"}";
  }

  /// Description:
  /// - Displays the end time as a human readable string.
  ///
  /// Parameters
  /// - int index - index of the schedule to use.
  ///
  /// Returns
  /// - The end time as a human readable string.
  String getEndTimeString(int index) {
    final int hour = timerDisplay[index].endHourController.value;
    final int minute = timerDisplay[index].endMinuteController.value;
    return "$hour:${minute < 10 ? "0" : ""}$minute ${timerDisplay[index].endPmTime.value ? "PM" : "AM"}";
  }

  /// Description:
  /// - Checks to see if at least 1 kitchen printer schedule is enabled.
  ///
  /// Parameters:
  /// - List<PrinterSchedule> listSchedule - List of printer schedules.
  ///
  /// Returns:
  /// - True is at least one schedule is enabled
  /// - False otherwise.
  bool checkIfAnyScheduleEnabled(List<PrinterSchedule> listSchedule) {
    for (final PrinterSchedule schedule in listSchedule) {
      if (schedule.enabled) {
        return true;
      }
    }
    return false;
  }

  /// Description:
  /// - Disables all enabled schedules.
  ///
  /// Parameters:
  /// - List<PrinterSchedule> listSchedule - List of printer schedules.
  void disableAllEnabledSchedules(List<PrinterSchedule> listSchedule) {
    for (final PrinterSchedule schedule in listSchedule) {
      schedule.enabled = false;
    }
  }
}
