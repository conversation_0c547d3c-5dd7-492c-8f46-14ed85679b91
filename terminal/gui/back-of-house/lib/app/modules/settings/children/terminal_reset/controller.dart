import 'dart:convert';
import 'dart:io';

import 'package:backoffice/app/data/enums/terminal_import.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

class TerminalResetController extends GetxController {
  final GlobalKey<FormState> exportFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> importFormKey = GlobalKey<FormState>();

  final String _baseUrl = "http://localhost:8880/api/v1";

  final RxBool exportItemDeptOnly = true.obs;

  final Logger _logger = Logger('TerminalResetController');

  final NotificationService _notificationService = Get.find();
  final TextEditingController fileNameController = TextEditingController();

  final Rx<TerminalImport> selectedIndex = Rx<TerminalImport>(TerminalImport.ITEM_DEPARTMENT);
  final RxBool isExporting = false.obs;
  final RxBool isImporting = false.obs;

  // List of tables to clear for item and department
  final List<String> clearItemDeptTables = <String>[
    'item',
    'department',
    'activity',
    'pre_auth',
    'sale',
  ];

  // Export variables
  final Rx<String?> filePath = ''.obs;
  final RxString fileName = ''.obs;

  String preserveOldDataDump = "";

  final RxBool importDefault = true.obs;
  final RxBool importProd = false.obs;
  final RxBool importCustomSeed = false.obs;

  RxDouble loaderProgress = 0.0.obs;

  // @override
  // void onInit() {
  //   super.onInit();
  // }

  /// Exports data based on user selection
  Future<void> export(String saveResult) async {
    try {
      isExporting.value = true;
      const int exportTotalSteps = 4;
//test
      // Open file picker to save export

      //step 1 construct endpoit
      loaderProgress.value = 1 / exportTotalSteps;
      // Construct the dump URL based on user selection
      final String dumpUrl = _constructDumpUrl();

      //step 2 call API endpoint
      loaderProgress.value = 2 / exportTotalSteps;

      // Make the API call to get the dump
      final http.Response resp = await http.get(Uri.parse(dumpUrl));

      if (resp.statusCode != 200) {
        throw Exception("Export failed: ${resp.statusCode} ${resp.body}");
      }

      //step 3 write to file
      loaderProgress.value = 3 / exportTotalSteps;

      /// for testing
      // const String realFilePath = "/home/<USER>/Downloads/cole.sql";

      final String filePathWithExt = "$saveResult.sql";
      final File newFile = File(filePathWithExt);
      await newFile.writeAsString(resp.body);

      //step 4 complete
      loaderProgress.value = exportTotalSteps / exportTotalSteps;
      _notificationService.success("File was successfully exported to $filePathWithExt.");

      Get.back();
      // allow dialog to close before  changing values
      await Future<void>.delayed(const Duration(milliseconds: 450));
      //reset loader
      loaderProgress.value = 0;
      isExporting.value = false;
    } catch (e, stack) {
      _notificationService.error("Failed to export file. Error: $e");
      _logger.severe("Export failed", e, stack);
      Get.back();
      isExporting.value = false;
      //reset loader
      loaderProgress.value = 0;
    }
  }

  /// Constructs the dump URL based on user preferences.
  String _constructDumpUrl() {
    String dumpUrl = "$_baseUrl/database/demo/dump";

    if (exportItemDeptOnly.value) {
      dumpUrl += "?table=item&table=department";
    } else {
      dumpUrl += "?table=*";
    }

    return dumpUrl;
  }

  /// Handles the import functionality based on the user's intention.
  Future<void> import(TerminalImport intention) async {
    isImporting.value = true;
    String? oldDumpFile = '';

    try {
      // Save the state of the Database to restore if import intention fails
      oldDumpFile = await _preserveOldDatadump();

      switch (intention) {
        case TerminalImport.ITEM_DEPARTMENT:
          // Import new items and departments from file
          final String? itemDeptPath = await _getFilePathFromPicker();
          if (itemDeptPath == null) throw Exception("No file selected for import.");
          await _importItemAndDepartment(itemDeptPath);

        case TerminalImport.PROD_SEED:
          // Factory reset: clear current DB and insert prod seed
          await _importProdSeed();

        case TerminalImport.EXISTING_DB:
          // Restore entire existing Database... ALL TABLES
          final String? existingDBPath = await _getFilePathFromPicker();
          if (existingDBPath == null) throw Exception("No file selected for import.");
          await _importDB(existingDBPath);

        default:
          throw UnimplementedError('Unsupported import intention: $intention');
      }

      Get.back();
      await Future<void>.delayed(const Duration(milliseconds: 450));
      isImporting.value = false;
      loaderProgress.value = 0;
    } catch (e, stack) {
      await _handleImportError(e, stack, oldDumpFile);
      isImporting.value = false;
      loaderProgress.value = 0;
    }
  }

  /// Opens a file picker and returns the selected file path.
  Future<String?> _getFilePathFromPicker() async {
    final FilePickerResult? myFile = await FilePicker.platform.pickFiles();
    if (myFile == null) {
      _logger.warning("File selection was canceled.");
      return null;
    }
    final String? path = myFile.files.single.path;
    if (path == null) {
      _logger.warning('No file path found.');
      return null;
    }
    return path;
  }

  /// Imports items and departments from the given file path.
  Future<void> _importItemAndDepartment(String filePath) async {
    try {
      //step 1
      loaderProgress.value = 1 / 3;
      // Reset database tables
      await _resetTables(clearItemDeptTables);
      // step 2
      loaderProgress.value = 2 / 3;
      // Read SQL file content
      final String content = await File(filePath).readAsString();
      //step 3
      loaderProgress.value = 3 / 4;
      // Restore database
      await _restoreDatabase(content);
      loaderProgress.value = 4 / 4;
      _notificationService.success('Database restored successfully.');
    } catch (e) {
      _logger.severe('Error during ITEM_DEPARTMENT import: $e');
      _notificationService.error('Failed to import ITEM_DEPARTMENT.');
      rethrow;
    }
  }

  /// Imports the entire database from the given file path.
  Future<void> _importDB(String filePath) async {
    try {
      //step 1
      loaderProgress.value = 1 / 4;

      // Reset all database tables
      await _resetTables(<String>['*']);

      //step 2 read file
      loaderProgress.value = 2 / 4;

      // Read SQL file content
      final String content = await File(filePath).readAsString();
      //step 3 restore via API
      loaderProgress.value = 3 / 4;
      // Restore database
      await _restoreDatabase(content);

      //step 4 complete
      loaderProgress.value = 4 / 4;
      _notificationService.success('Database restored successfully.');
    } catch (e) {
      _logger.severe('Error during database import: $e');
      _notificationService.error('Failed to import database.');
      rethrow;
    }
  }

  /// Performs a factory reset and imports the production seed.
  Future<void> _importProdSeed() async {
    final String fetchProdEndPoint = "$_baseUrl/database/seed";
    const int totalSteps = 6;

    try {
      //step 1
      loaderProgress.value = 1 / totalSteps;

      // Reset all database tables
      await _resetTables(<String>['*']);
      //step2
      loaderProgress.value = 2 / totalSteps;

      // Fetch production seed
      final http.Response prodSeedResp = await http.get(Uri.parse(fetchProdEndPoint));

      if (prodSeedResp.statusCode != 200) {
        throw Exception("Failed to fetch prod seed file: ${prodSeedResp.statusCode}");
      }
      //step 3
      loaderProgress.value = 3 / totalSteps;

      // Restore database with the prod seed
      await _restoreDatabase(prodSeedResp.body);

      // Fetch self IP Address
      final http.Response getSelfIP = await http.get(Uri.parse("$_baseUrl/identify"));

      if (getSelfIP.statusCode != 200) {
        throw Exception("Failed to fetch self IP address: ${getSelfIP.statusCode}");
      }
      //step 4
      loaderProgress.value = 4 / totalSteps;
      // Decode the JSON response
      final Map<String, dynamic> responseData = jsonDecode(getSelfIP.body) as Map<String, dynamic>;

      // Extract the IP address
      final String ipAddress = responseData['ip'].toString();

      // step 5
      loaderProgress.value = 5 / totalSteps;

      // Add terminal with the IP address
      final http.Response addTerminalResp = await http.post(
        Uri.parse("$_baseUrl/database/terminals"),
        headers: <String, String>{"Content-Type": "application/json"},
        body: jsonEncode(<String, String>{"IP": ipAddress}),
      );

      if (addTerminalResp.statusCode != 201) {
        throw Exception("Failed to add terminal: ${addTerminalResp.statusCode}");
      }

      //step 6
      loaderProgress.value = 6 / totalSteps;
      await Future<void>.delayed(const Duration(milliseconds: 200));

      _notificationService.success('Database restored successfully.');
    } catch (e) {
      _logger.severe('Error during PROD_SEED import: $e');
      _notificationService.error('Failed to import PROD_SEED.');
      rethrow;
    }
  }

  /// Resets the specified tables in the database.
  Future<void> _resetTables(List<String> tables) async {
    final String resetEndPoint = "$_baseUrl/database/demo/reset";

    final http.Response response = await http.post(
      Uri.parse(resetEndPoint),
      headers: <String, String>{"Content-Type": "application/json"},
      body: jsonEncode(<String, List<String>>{"tables": tables}),
    );

    if (response.statusCode != 200) {
      throw Exception("Failed to clear tables: ${response.statusCode}");
    }
  }

  /// Restores the database with the provided SQL content.
  Future<void> _restoreDatabase(String content) async {
    final http.Response response = await http.post(
      Uri.parse("$_baseUrl/database/demo/restore"),
      headers: <String, String>{"Content-Type": "text/plain"},
      body: content,
    );

    if (response.statusCode != 200) {
      throw Exception("Restore failed: ${response.statusCode}");
    }
  }

  /// Preserves the old data dump before making changes.
  Future<String?> _preserveOldDatadump() async {
    try {
      _setPreserveOldDataDumpUrl();
      final http.Response resp = await http.get(Uri.parse(preserveOldDataDump));

      if (resp.statusCode != 200) {
        throw Exception("Failed to preserve old dump file: ${resp.statusCode}");
      }

      _logger.info('Successfully preserved dump file.');
      return resp.body;
    } catch (e, stack) {
      _logger.severe("Error preserving old dump file: $e", e, stack);
      return null;
    }
  }

  /// Sets the URL for preserving the old data dump based on the selected index.
  void _setPreserveOldDataDumpUrl() {
    final String baseDumpUrl = "$_baseUrl/database/demo/dump";
    preserveOldDataDump =
        (selectedIndex.value == TerminalImport.ITEM_DEPARTMENT) ? "$baseDumpUrl?table=item&table=department" : "$baseDumpUrl?table=[*]";
  }

  /// Handles errors during import by attempting to restore previous data.
  Future<void> _handleImportError(dynamic error, StackTrace stack, String? oldDumpFile) async {
    _notificationService.error("An error occurred during import.");
    _logger.severe("Failed to upload file: $error", error, stack);
    _logger.info("Attempting to restore previous data...");

    if (oldDumpFile == null) {
      _logger.warning("No backup available. Data restoration is not possible.");
    } else {
      try {
        await _restoreDatabase(oldDumpFile);
        _notificationService.success("Previous data restored successfully.");
      } catch (restoreError) {
        _logger.severe("Failed to restore previous data: $restoreError");
        _notificationService.error("Failed to restore previous data.");
      }
    }
  }

  /// Returns a warning message string based on the import intention.
  String getImportWarningString(TerminalImport intention) {
    switch (intention) {
      case TerminalImport.ITEM_DEPARTMENT:
        return "\u2022 Are you sure you selected an 'items and departments' file?\n"
            "\u2022 Proceeding will delete all items, departments, sales, activities, pre_auths, and sales.\n"
            "\u2022 If file import fails, your existing/previous data will be restored.";
      case TerminalImport.PROD_SEED:
        return "\u2022 Proceeding will factory reset your device.\n"
            "\u2022 If factory reset fails, your existing/previous data will be restored.";
      case TerminalImport.EXISTING_DB:
        return "\u2022 Are you sure you selected a 'Full Database Backup' file?\n"
            "\u2022 Proceeding will delete all current and existing data.\n"
            "\u2022 If file import fails, your existing/previous data will be restored.";
      default:
        return "";
    }
  }
}
