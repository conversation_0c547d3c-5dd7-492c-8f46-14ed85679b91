import 'dart:async';
import 'dart:convert';

import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

class TerminalSettingsController extends GetxController {
  TerminalSettingsController();

  // ignore: non_constant_identifier_names
  final String GET_SYSTEM_DEVICES_QUERY = '''
  subscription GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

  final String GET_SYSTEM_DEVICES_ = '''
  mutat GET_SYSTEM_DEVICE {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

  final String _baseUrl = "http://localhost:8880";

  final GlobalKey<FormState> terminalKey = GlobalKey<FormState>();
  TextEditingController ipController = TextEditingController();
  TextEditingController macController = TextEditingController();

  final GraphqlService _graphqlService = Get.find();
  final NotificationService notificationService = Get.find();
  final Logger logger = Logger('terminalController');

  Rx<SystemDeviceJsonRecordDocument> systemDeviceDocument = SystemDeviceJsonRecordDocument.empty().obs;
  final Rx<SystemDeviceJsonRecord> systemDeviceRecord = SystemDeviceJsonRecord.empty().obs;

  RxBool viewPermission = false.obs;
  RxBool isLoading = true.obs;
  RxBool awaitingAddTerminal = false.obs;
  RxBool addTerminalErrors = false.obs;

  RxString errorString = "".obs;

  @override
  Future<void> onInit() async {
    await viewSettingsPermission();
    super.onInit();
  }

  Future<void> viewSettingsPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "view",
      "Back Office Settings",
      _graphqlService,
    );
    viewPermission.value = view;
  }

  Future<bool> addTerminal(String ip, String mac) async {
    logger.info("adding terminal");
    if (!terminalKey.currentState!.validate()) notificationService.error("Invalid form");
    try {
      awaitingAddTerminal.value = true;
      final http.Response resp = await http.post(
        Uri.parse("$_baseUrl/api/v1/database/terminals"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(<String, String>{
          "IP": ip,
        }),
      );
      if (resp.statusCode != 201) {
        final Map<String, dynamic> responseObject = jsonDecode(resp.body) as Map<String, dynamic>;
        throw "Error Message: ${responseObject["message"]}";
      }

      awaitingAddTerminal.value = false;
      addTerminalErrors.value = false;
      Get.back();
      ipController.clear();
      logger.info("Successfully added terminal", resp.body);
      notificationService.success("Successfully added terminal");

      return true;
    } catch (e, stack) {
      logger.severe("Failed to add terminal", e, stack);
      errorString.value = e.toString();
      awaitingAddTerminal.value = false;
      return false;
    }
  }
}
