// ignore_for_file: noop_primitive_operations

import 'package:backoffice/app/data/enums/pole_display/type.dart';
import 'package:backoffice/app/data/enums/receipt_printer/type.dart';
import 'package:backoffice/app/data/enums/scale/type.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

class ViewTerminalDialogController extends GetxController {
  ViewTerminalDialogController(
    this.terminal,
    this.terminalIndex,
    this.sysDevDoc,
  );

  // ignore: non_constant_identifier_names
  final String UPDATE_TERMINALS = '''
      mutation UPDATE_TERMINALS(\$_eq: String, \$document: jsonb) {
        update_json_record(where: {record_key: {_eq: \$_eq}}, _append: {document: \$document}) {
          returning {
            record_key
            document
            updated_at
          }
        }
      }
      ''';

// ignore: non_constant_identifier_names
  final String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

  final Logger _logger = Logger('ViewTerminalController');

  final SystemDeviceJsonRecordTerminal terminal;
  final SystemDeviceJsonRecordDocument sysDevDoc;
  final int terminalIndex;

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  final GlobalKey<FormState> viewTerminalFormKey = GlobalKey<FormState>();

  final Rx<SystemDeviceJsonRecordTerminal> currentTerminal = SystemDeviceJsonRecordTerminal.empty().obs;
  final Rx<MerchantJsonRecord> merchantConfig = MerchantJsonRecord.empty().obs;
  final RxBool editPermission = false.obs;
  final RxBool isLoading = true.obs;
  final RxBool quickSignIn = false.obs;
  final RxBool autoSignOutEnabled = false.obs;

  final RxString title = "".obs;

  final RxInt scaleType = 0.obs;
  final RxInt scalePortType = 0.obs;
  final RxInt receiptPrinterType = 0.obs;
  final RxInt receiptPrinterPortType = 0.obs;
  final RxInt sectionIdx = 0.obs;
  final RxInt priceLevelIdx = 0.obs;
  final RxInt poleDisplayType = 0.obs;
  final RxInt poleDisplayPortType = 0.obs;
  final RxInt bergPortType = 0.obs;
  final RxInt bergDisplayType = 0.obs;
  final RxInt bergItemMaintenance = 0.obs;

  final RxInt skipDevices = 0.obs;
  final RxInt quickSection = 0.obs;
  final RxInt remotePrintIndex = 0.obs;

  final RxList<Section> sectionsList = <Section>[].obs;
  final RxList<PriceLevel> levelList = <PriceLevel>[].obs;

  final TextEditingController descController = TextEditingController();
  final TextEditingController macController = TextEditingController();
  final TextEditingController ipController = TextEditingController();
  final TextEditingController terminalIndexController = TextEditingController();

  final TextEditingController paymentIPController = TextEditingController();
  final TextEditingController paymentPortPortController = TextEditingController();
  final TextEditingController epiController = TextEditingController();
  final TextEditingController appIDController = TextEditingController();
  final TextEditingController appKeyController = TextEditingController();

  final RxInt scalePort = 0.obs;
  final RxInt rcptPort = 0.obs;
  final RxInt poleDispPort = 0.obs;
  final RxInt bergPort = 0.obs;

  final TextEditingController rcptBaudController = TextEditingController();

  final TextEditingController quickMinutes = TextEditingController();
  final TextEditingController autoSignOutSecondsController = TextEditingController();

  List<RxInt> cashDrawerTypes = <RxInt>[
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
  ];
  List<RxInt> cashDrawerPorts = <RxInt>[
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
    0.obs,
  ];

  @override
  Future<void> onInit() async {
    await editSettingsPermission();
    await getSectionsConfig();
    await getPriceLevels();
    await getMerchantConfig();

    currentTerminal.value = terminal;
    //
    sectionIdx.value = terminal.section;
    priceLevelIdx.value = terminal.priceLevel;
    //
    title.value = currentTerminal.value.desc.toString();
    //
    descController.text = currentTerminal.value.desc.toString();
    macController.text = currentTerminal.value.MAC.toString();
    ipController.text = currentTerminal.value.IP.toString();
    terminalIndexController.text = currentTerminal.value.idx.toString();
    //
    paymentIPController.text = currentTerminal.value.paymentDevice.IP.toString();
    paymentPortPortController.text = currentTerminal.value.paymentDevice.port.toString();
    epiController.text = currentTerminal.value.paymentDevice.epi;
    appIDController.text = currentTerminal.value.paymentDevice.appID;
    appKeyController.text = currentTerminal.value.paymentDevice.appKey;
    //
    scaleType.value = int.parse(currentTerminal.value.scale.type.toString());

    scalePort.value = currentTerminal.value.scale.port;
    scalePortType.value = int.parse(currentTerminal.value.scale.portType.toString());
    //
    receiptPrinterType.value = int.parse(currentTerminal.value.rcptPrn.type.toString());
    rcptBaudController.text = currentTerminal.value.rcptPrn.baud.toString();
    rcptPort.value = currentTerminal.value.rcptPrn.port;
    receiptPrinterPortType.value = int.parse(currentTerminal.value.rcptPrn.portType.toString());
    remotePrintIndex.value = currentTerminal.value.remotePrintIdx;
    //
    poleDisplayType.value = int.parse(currentTerminal.value.poleDisp.type.toString());
    poleDispPort.value = currentTerminal.value.poleDisp.port;
    poleDisplayPortType.value = int.parse(currentTerminal.value.poleDisp.portType.toString());
    //
    bergPort.value = int.parse(currentTerminal.value.liqCtl.port.toString());
    bergDisplayType.value = int.parse(currentTerminal.value.liqCtl.type.toString());
    bergItemMaintenance.value = int.parse(currentTerminal.value.liqCtl.dispenseMethod.toString());
    bergPortType.value = int.parse(currentTerminal.value.liqCtl.portType.toString());
    //
    skipDevices.value = int.parse(currentTerminal.value.skipDevices.toString());
    //
    quickSignIn.value = currentTerminal.value.quickSignIn == true;
    quickSection.value = int.parse(currentTerminal.value.quickSignSection.toString());
    quickMinutes.text = currentTerminal.value.quickSignMinutes == 0 ? "-" : currentTerminal.value.quickSignMinutes.toString();
    //
    final List<TerminalDrawer> drawers = <TerminalDrawer>[
      ...currentTerminal.value.cashDrawers,
    ];
    for (final TerminalDrawer drawer in drawers) {
      cashDrawerTypes[drawer.idx].value = drawer.type;
      cashDrawerPorts[drawer.idx].value = drawer.port;
    }

    // Set auto sign-out value from the record
    autoSignOutSecondsController.text = currentTerminal.value.autoSignOutSeconds <= 0 ? "-" : currentTerminal.value.autoSignOutSeconds.toString();
    autoSignOutEnabled.value = autoSignOutSecondsController.text != "-";

    /// FUNCTION WILL DEFAULT PORTS FOR ANY DEVICES THAT PRE-DATE PORT CHANGES
    defaultPorts();

    isLoading.value = false;
    super.onInit();
  }

  Future<void> getMerchantConfig() async {
    final QueryResult<Object> configResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_MERCHANT_JSON_RECORD {
              json_record(where: {record_key: {_eq: "merchant"}}) {
                record_key
                document
                updated_at
                
              }
            }
            ''',
        ),
        fetchPolicy: FetchPolicy.noCache,
      ),
    );

    // ignore: always_specify_types
    if ((configResult.data!['json_record']! as List).isEmpty) throw "No Configs Found";

    // ignore: avoid_dynamic_calls
    merchantConfig.value = MerchantJsonRecord.fromJson(configResult.data!['json_record'][0] as Map<String, dynamic>);
  }

  Future<void> updateTerminals() async {
    try {
      if (!viewTerminalFormKey.currentState!.validate()) throw "Invalid form";

      currentTerminal.value.desc = descController.text;
      currentTerminal.value.MAC = macController.text;
      currentTerminal.value.IP = ipController.text;
      currentTerminal.value.idx = int.parse(terminalIndexController.text);
      currentTerminal.value.section = sectionIdx.value;
      currentTerminal.value.priceLevel = priceLevelIdx.value;

      //
      currentTerminal.value.paymentDevice.IP = paymentIPController.text;
      currentTerminal.value.paymentDevice.port = paymentPortPortController.text;
      currentTerminal.value.paymentDevice.epi = epiController.text;
      currentTerminal.value.paymentDevice.appID = appIDController.text;
      currentTerminal.value.paymentDevice.appKey = appKeyController.text;

      //
      currentTerminal.value.scale.port = scalePort.value;
      currentTerminal.value.scale.type = scaleType.value;
      currentTerminal.value.scale.portType = scalePortType.value;

      //
      currentTerminal.value.rcptPrn.baud = int.parse(rcptBaudController.text);
      currentTerminal.value.rcptPrn.port = rcptPort.value;
      currentTerminal.value.rcptPrn.type = receiptPrinterType.value;
      currentTerminal.value.rcptPrn.portType = receiptPrinterPortType.value;
      currentTerminal.value.remotePrintIdx = receiptPrinterType.value == ReceiptPrinterType.REMOTE_PRINT.index ? remotePrintIndex.value : 0;

      //
      currentTerminal.value.poleDisp.port = poleDispPort.value;
      currentTerminal.value.poleDisp.type = poleDisplayType.value;
      currentTerminal.value.poleDisp.portType = poleDisplayPortType.value;

      //
      currentTerminal.value.skipDevices = skipDevices.value;

      //
      currentTerminal.value.liqCtl.port = bergPort.value;
      currentTerminal.value.liqCtl.type = bergDisplayType.value;
      currentTerminal.value.liqCtl.dispenseMethod = bergItemMaintenance.value;
      currentTerminal.value.liqCtl.portType = bergPortType.value;

      //
      currentTerminal.value.quickSignIn = quickSignIn.value;
      currentTerminal.value.quickSignSection = quickSection.value;
      currentTerminal.value.quickSignMinutes = quickMinutes.text == "-" ? 0 : int.parse(quickMinutes.text);

      // Set auto sign-out seconds value
      currentTerminal.value.autoSignOutSeconds = autoSignOutSecondsController.text == "-" ? 0 : int.parse(autoSignOutSecondsController.text);

      //
      final List<TerminalDrawer> updateDrawers = <TerminalDrawer>[];
      for (int i = 0; i < cashDrawerTypes.length; i++) {
        final int portNum = cashDrawerPorts[i].value;
        if (cashDrawerTypes[i].value > 0 || portNum > 0) {
          updateDrawers.add(
            TerminalDrawer(
              idx: i,
              type: cashDrawerTypes[i].value,
              port: portNum,
            ),
          );
        }
      }
      currentTerminal.value.cashDrawers = updateDrawers;

      sysDevDoc.terminal[terminalIndex] = currentTerminal.value;

      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'systemSetting',
          },
        ),
      );

      SystemSettingJsonRecord settingsRecord = SystemSettingJsonRecord.empty();

      final List<dynamic> settingsList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (settingsList.isNotEmpty) {
        settingsRecord = settingsList
            .map(
              (dynamic config) => SystemSettingJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;
      }

      if (settingsRecord.document.priceLevels.join() != levelList.join()) {
        final Map<int, String> levelsMap1 = <int, String>{};
        for (final PriceLevel level in settingsRecord.document.priceLevels) {
          levelsMap1[level.idx] = level.desc;
        }
        final Map<int, String> levelsMap2 = <int, String>{};
        for (final PriceLevel level in levelList) {
          levelsMap2[level.idx] = level.desc;
        }

        final SystemDeviceJsonRecordTerminal term = sysDevDoc.terminal[terminalIndex];

        if (term.priceLevel != 0) {
          if (levelsMap1[term.priceLevel] != levelsMap2[term.priceLevel]) {
            final List<int> levelsKeys = levelsMap1.keys.toList();

            for (final int key in levelsKeys) {
              if (levelsMap1[key] == levelsMap2[term.priceLevel]) {
                term.priceLevel = key;
              }
            }
          }
        }
      }

      final QueryResult<Object> updateTerminalResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            UPDATE_TERMINALS,
          ),
          variables: <String, dynamic>{
            "_eq": "systemDevice",
            "document": sysDevDoc,
          },
        ),
      );

      if (updateTerminalResult.hasException) {
        throw updateTerminalResult.exception.toString();
      }

      _notificationService.success("Update terminal success!");
      await Get.offAndToNamed(
        AppRoutes.SETTINGS_TERMINALS,
        id: AppRoutes.id,
      );
    } catch (err, stack) {
      _notificationService.error("Error updating terminal");
      _logger.severe('Error updating termainl', err, stack);
    }
  }

  Future<void> getSectionsConfig() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'sections',
          },
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SectionsJsonRecord sectionsRecord = configList
            .map(
              (dynamic config) => SectionsJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        sectionsList.value = sectionsRecord.document.sections;
      }
    } catch (err, stack) {
      _notificationService.error("Error getting sections");
      _logger.severe('Error updating sections', err, stack);
    }
  }

  Future<void> getPriceLevels() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'systemSetting',
          },
        ),
      );

      final List<dynamic> configList =
          // ignore: always_specify_types
          configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final SystemSettingJsonRecord settingsRecord = configList
            .map(
              (dynamic config) => SystemSettingJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;

        levelList.value = settingsRecord.document.priceLevels;
      }
    } catch (err, stack) {
      _notificationService.error("Error getting price levels");
      _logger.severe('Error getting Price Levels', err, stack);
    }
  }

  Future<void> deleteTerminal() async {
    try {
      sysDevDoc.terminal.remove(currentTerminal.value);
      final QueryResult<Object> deleteTerminalResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
                mutation UPDATE_TERMINALS(\$document: jsonb, \$_eq: String) {
                  update_json_record(where: {record_key: {_eq: \$_eq}}, _set: {document: \$document}) {
                    returning {
                      document
                      record_key
                      updated_at
                    }
                  }
                }
                ''',
          ),
          variables: <String, dynamic>{
            "_eq": 'systemDevice',
            "document": sysDevDoc,
          },
        ),
      );

      if (deleteTerminalResult.hasException) throw deleteTerminalResult.exception!.graphqlErrors.toString();

      _notificationService.success("Terminal Deleted");

      await Get.offAndToNamed(
        AppRoutes.SETTINGS_TERMINALS,
        id: AppRoutes.id,
      );
    } catch (err, stack) {
      _notificationService.error("Error removing Terminal");
      _logger.severe('Error removing terminal', err, stack);
    }
  }

  Future<void> editSettingsPermission() async {
    final bool edit = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Edit",
      "Back Office Settings",
      _graphqlService,
    );
    editPermission.value = edit;
  }

  void defaultPorts() {
    /// HARDWARE SERVICE PORTS ARE 1 BASED.
    /// SETTING PORT TO 0 = NONE OR NO PORT AND WILL BE DEFAULT FOR NON SERIAL DEVICES
    if (scaleType.value == ScaleType.NONE.index || scaleType.value == ScaleType.BRECKNELL_PS_USB.index) {
      scalePort.value = 0;
    }

    if (receiptPrinterType.value != ReceiptPrinterType.EPSON_T88_ACM_SERIAL.index) {
      rcptPort.value = 0;
    }

    if (poleDisplayType.value == PoleDisplayType.PARTNERTECH_USB.index ||
        poleDisplayType.value == PoleDisplayType.NONE.index ||
        poleDisplayType.value == PoleDisplayType.PANASONIC_960WS.index) {
      poleDispPort.value = 0;
    }
  }
}
