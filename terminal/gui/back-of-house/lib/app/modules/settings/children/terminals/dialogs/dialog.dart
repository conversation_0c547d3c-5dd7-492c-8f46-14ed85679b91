import 'package:backoffice/app/data/enums/berg_item_maintenance.dart';
import 'package:backoffice/app/data/enums/berg_type.dart';
import 'package:backoffice/app/data/enums/cash_drawer/types.dart';
import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/enums/pole_display/type.dart';
import 'package:backoffice/app/data/enums/port_type.dart';
import 'package:backoffice/app/data/enums/receipt_printer/type.dart';
import 'package:backoffice/app/data/enums/scale/type.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/global_widgets/header.dart';
import 'package:backoffice/app/global_widgets/menu/components/checkbox_row.dart';
import 'package:backoffice/app/global_widgets/menu/components/menu_dropdown.dart';
import 'package:backoffice/app/global_widgets/menu/components/text_field.dart';
import 'package:backoffice/app/global_widgets/menu/form_wrapper.dart';
import 'package:backoffice/app/global_widgets/menu/menu_group.dart';
import 'package:backoffice/app/global_widgets/prep_device_select/widget.dart';
import 'package:backoffice/app/modules/settings/children/terminals/dialogs/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class ViewTerminalDialog extends GetView<ViewTerminalDialogController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(200),
        child: Obx(
          () => Header(
            title: controller.title.value,
            leftButton: DialogButton(
              buttonType: EDialogButtonType.BACK,
              buttonText: "Terminals",
              onTapped: () {
                Get.back(id: AppRoutes.id);
              },
            ),
            rightButton: Obx(
              () => DialogButton(
                buttonType: EDialogButtonType.ADD,
                disabled: !controller.editPermission.value,
                onTapped: () async => controller.updateTerminals(),
                buttonText: "Update Terminal Settings",
              ),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Obx(
          () => Flex(
            direction: Axis.vertical,
            children: <Widget>[
              if (controller.isLoading.value)
                const Center(child: CircularProgressIndicator())
              else
                Padding(
                  padding: const EdgeInsets.only(top: 20, bottom: 20),
                  child: FormWrapper(
                    formKey: controller.viewTerminalFormKey,
                    children: <Widget>[
                      MenuGroup(
                        title: "Terminal Info",
                        children: <Widget>[
                          MenuTextField(
                            label: "Description",
                            enabled: controller.editPermission.value,
                            controller: controller.descController,
                            maxLength: 50,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuTextField(
                            label: "MAC",
                            enabled: controller.editPermission.value,
                            controller: controller.macController,
                            maxLength: 50,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuTextField(
                            label: "IP Address",
                            controller: controller.ipController,
                            enabled: controller.editPermission.value,
                            maxLength: 50,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuTextField(
                            label: "Index",
                            controller: controller.terminalIndexController,
                            enabled: controller.currentTerminal.value.idx != 98 && controller.editPermission.value,
                            maxLength: 50,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuDropdown<int>(
                            title: "Section",
                            value: controller.sectionIdx.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.sectionIdx.value = value!;
                                  }
                                : null,
                            items: <DropdownMenuItem<int>>[
                              const DropdownMenuItem<int>(
                                value: 0,
                                child: Text("None"),
                              ),
                              ...controller.sectionsList.map(
                                (Section section) => DropdownMenuItem<int>(
                                  value: section.idx,
                                  child: Text(section.desc),
                                ),
                              ),
                            ],
                          ),
                          MenuDropdown<int>(
                            title: "PriceLevel",
                            value: controller.priceLevelIdx.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.priceLevelIdx.value = value!;
                                  }
                                : null,
                            items: <DropdownMenuItem<int>>[
                              const DropdownMenuItem<int>(
                                value: 0,
                                child: Text("None"),
                              ),
                              ...controller.levelList.map(
                                (PriceLevel level) => DropdownMenuItem<int>(
                                  value: level.idx,
                                  child: Text(level.desc),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      MenuGroup(
                        title: "Payment Info",
                        children: <Widget>[
                          MenuTextField(
                            label: "Payments IP",
                            enabled: controller.editPermission.value,
                            controller: controller.paymentIPController,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuTextField(
                            label: "Payments Port",
                            enabled: controller.editPermission.value,
                            controller: controller.paymentPortPortController,
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          MenuTextField(
                            label: "EPI",
                            enabled: false,
                            hintText: "",
                            controller: controller.epiController,
                          ),
                          MenuTextField(
                            label: "App ID",
                            enabled: false,
                            hintText: "",
                            controller: controller.appIDController,
                          ),
                          MenuTextField(
                            label: "App Key",
                            enabled: false,
                            hintText: "",
                            controller: controller.appKeyController,
                          ),
                        ],
                      ),
                      MenuGroup(
                        title: "Scale",
                        children: <Widget>[
                          MenuDropdown<int>(
                            title: "Type",
                            value: controller.scaleType.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.scaleType.value = value!;
                                    if (controller.scaleType.value == ScaleType.BRECKNELL_PS_USB.index ||
                                        controller.scaleType.value == ScaleType.NONE.index) {
                                      controller.scalePort.value = 0;
                                    }
                                  }
                                : null,
                            items: ScaleType.values
                                .map(
                                  (ScaleType type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),
                          if (controller.scaleType.value == ScaleType.DATALOGIC_MAGELLAN_9300I.index ||
                              controller.scaleType.value == ScaleType.CAS_PDN_30BD_Serial.index)
                            MenuDropdown<int>(
                              title: "Port Type",
                              value: controller.scalePortType.value,
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.scalePortType.value = value!;
                                    }
                                  : null,
                              items: PortType.values
                                  .map(
                                    (PortType type) => DropdownMenuItem<int>(
                                      value: type.index,
                                      child: Text(type.friendlyString),
                                    ),
                                  )
                                  .toList(),
                            ),
                          if (controller.scaleType.value == ScaleType.DATALOGIC_MAGELLAN_9300I.index ||
                              controller.scaleType.value == ScaleType.CAS_PDN_30BD_Serial.index)
                            MenuDropdown<int>(
                              title: "Port",
                              value: controller.scalePort.value,
                              validator: (int? value) {
                                if (value != 0 && (value == controller.poleDispPort.value || value == controller.rcptPort.value)) {
                                  return 'Ports cannot have the same value';
                                } else {
                                  return null;
                                }
                              },
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.scalePort.value = value!;
                                    }
                                  : null,
                              items: List<DropdownMenuItem<int>>.generate(
                                9,
                                (int i) => DropdownMenuItem<int>(
                                  value: i,
                                  child: Text(
                                    i == 0 ? "None" : "Comm  $i",
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                      MenuGroup(
                        title: "Receipt Printer",
                        children: <Widget>[
                          MenuDropdown<int>(
                            title: "Type",
                            value: controller.receiptPrinterType.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.receiptPrinterType.value = value!;
                                    if (controller.receiptPrinterType.value != ReceiptPrinterType.EPSON_T88_ACM_SERIAL.index) {
                                      controller.rcptPort.value = 0;
                                    }
                                    if (controller.receiptPrinterType.value != ReceiptPrinterType.REMOTE_PRINT.index) {
                                      controller.remotePrintIndex.value = 0;
                                    }
                                  }
                                : null,
                            items: ReceiptPrinterType.values
                                .map(
                                  (ReceiptPrinterType type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),

                          ///
                          ///
                          ///
                          /// remote print index
                          if (controller.receiptPrinterType.value == ReceiptPrinterType.REMOTE_PRINT.index)
                            MenuDropdown<int>(
                              title: "Remote Index",
                              value: controller.remotePrintIndex.value,
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.remotePrintIndex.value = value!;
                                    }
                                  : null,
                              items: <DropdownMenuItem<int>>[
                                const DropdownMenuItem<int>(
                                  value: 0,
                                  child: Text('None'),
                                ),
                                ...controller.sysDevDoc.terminal
                                    .where((SystemDeviceJsonRecordTerminal element) => element.idx != controller.currentTerminal.value.idx)
                                    .map(
                                      (SystemDeviceJsonRecordTerminal terminal) => DropdownMenuItem<int>(
                                        value: terminal.idx,
                                        child: Text(terminal.desc),
                                      ),
                                    ),
                              ],
                            ),

                          ///
                          ///
                          ///
                          ///
                          MenuTextField(
                            label: "BAUD",
                            controller: controller.rcptBaudController,
                            enabled: controller.editPermission.value,
                            maxLength: 50,
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.deny(
                                RegExp("[A-Za-z]"),
                              ),
                            ],
                            validator: (String? value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter a valid Description';
                              }
                              return null;
                            },
                          ),
                          if (controller.receiptPrinterType.value == ReceiptPrinterType.EPSON_T88_ACM_SERIAL.index)
                            MenuDropdown<int>(
                              title: "Port Type",
                              value: controller.receiptPrinterPortType.value,
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.receiptPrinterPortType.value = value!;
                                    }
                                  : null,
                              items: PortType.values
                                  .map(
                                    (PortType type) => DropdownMenuItem<int>(
                                      value: type.index,
                                      child: Text(type.friendlyString),
                                    ),
                                  )
                                  .toList(),
                            ),
                          if (controller.receiptPrinterType.value == ReceiptPrinterType.EPSON_T88_ACM_SERIAL.index)
                            MenuDropdown<int>(
                              title: "Port",
                              value: controller.rcptPort.value,
                              validator: (int? value) {
                                if (value != 0 && (value == controller.scalePort.value || value == controller.poleDispPort.value)) {
                                  return 'Ports cannot have the same value';
                                } else {
                                  return null;
                                }
                              },
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.rcptPort.value = value!;
                                    }
                                  : null,
                              items: List<DropdownMenuItem<int>>.generate(
                                9,
                                (int i) => DropdownMenuItem<int>(
                                  value: i,
                                  child: Text(
                                    i == 0 ? "None" : "Comm  $i",
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                      MenuGroup(
                        title: "Pole Display",
                        children: <Widget>[
                          MenuDropdown<int>(
                            title: "Type",
                            value: controller.poleDisplayType.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.poleDisplayType.value = value!;
                                    if (controller.poleDisplayType.value == PoleDisplayType.PARTNERTECH_USB.index ||
                                        controller.poleDisplayType.value == PoleDisplayType.NONE.index ||
                                        controller.poleDisplayType.value == PoleDisplayType.PANASONIC_960WS.index) {
                                      controller.poleDispPort.value = 0;
                                    }
                                  }
                                : null,
                            items: PoleDisplayType.values
                                .map(
                                  (PoleDisplayType type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),
                          if (controller.poleDisplayType.value == PoleDisplayType.AEDEX_SERIAL.index ||
                              controller.poleDisplayType.value == PoleDisplayType.EPSON_SERIAL.index ||
                              controller.poleDisplayType.value == PoleDisplayType.Partnertech_Serial.index)
                            MenuDropdown<int>(
                              title: "Port Type",
                              value: controller.poleDisplayPortType.value,
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.poleDisplayPortType.value = value!;
                                    }
                                  : null,
                              items: PortType.values
                                  .map(
                                    (PortType type) => DropdownMenuItem<int>(
                                      value: type.index,
                                      child: Text(type.friendlyString),
                                    ),
                                  )
                                  .toList(),
                            ),
                          if (controller.poleDisplayType.value == PoleDisplayType.AEDEX_SERIAL.index ||
                              controller.poleDisplayType.value == PoleDisplayType.EPSON_SERIAL.index ||
                              controller.poleDisplayType.value == PoleDisplayType.Partnertech_Serial.index)
                            MenuDropdown<int>(
                              title: "Port",
                              value: controller.poleDispPort.value,
                              validator: (int? value) {
                                if (value != 0 && (value == controller.scalePort.value || value == controller.rcptPort.value)) {
                                  return 'Ports cannot have the same value';
                                } else {
                                  return null;
                                }
                              },
                              onChanged: controller.editPermission.value
                                  ? (int? value) {
                                      controller.poleDispPort.value = value!;
                                    }
                                  : null,
                              items: List<DropdownMenuItem<int>>.generate(
                                9,
                                (int i) => DropdownMenuItem<int>(
                                  value: i,
                                  child: Text(
                                    i == 0 ? "None" : "Comm  $i",
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),

                      MenuGroup(
                        title: "Liquor Control",
                        children: <Widget>[
                          MenuDropdown<int>(
                            title: "Type",
                            value: controller.bergDisplayType.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.bergDisplayType.value = value!;
                                  }
                                : null,
                            items: BergType.values
                                .map(
                                  (BergType type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),
                          MenuDropdown<int>(
                            title: "Dispensing Method",
                            value: controller.bergItemMaintenance.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.bergItemMaintenance.value = value!;
                                  }
                                : null,
                            items: BergItemMaintenance.values
                                .map(
                                  (BergItemMaintenance type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),
                          MenuDropdown<int>(
                            title: "Port",
                            value: controller.bergPort.value,
                            validator: (int? value) {
                              if (value == controller.scalePort.value || value == controller.poleDispPort.value) {
                                return 'Ports cannot have the same value';
                              } else {
                                return null;
                              }
                            },
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.bergPort.value = value!;
                                  }
                                : null,
                            items: List<DropdownMenuItem<int>>.generate(
                              8,
                              (int i) => DropdownMenuItem<int>(
                                value: i + 1,
                                child: Text(
                                  "Comm  ${i + 1}",
                                ),
                              ),
                            ),
                          ),
                          MenuDropdown<int>(
                            title: "Port Type",
                            value: controller.bergPortType.value,
                            onChanged: controller.editPermission.value
                                ? (int? value) {
                                    controller.bergPortType.value = value!;
                                  }
                                : null,
                            items: PortType.values
                                .map(
                                  (PortType type) => DropdownMenuItem<int>(
                                    value: type.index,
                                    child: Text(type.friendlyString),
                                  ),
                                )
                                .toList(),
                          ),
                        ],
                      ),
                      MenuGroup(
                        title: "Quick Sign-In",
                        children: <Widget>[
                          MenuCheckbox(
                            text: "Quick Sign-In",
                            value: controller.quickSignIn.value,
                            onChanged: controller.editPermission.value
                                ? (bool? val) {
                                    controller.quickSignIn.value = val == true;
                                  }
                                : null,
                          ),
                          MenuDropdown<int>(
                            title: "Limit QSI to Section",
                            value: controller.quickSection.value,
                            onChanged: controller.editPermission.value && controller.quickSignIn.value
                                ? (int? value) {
                                    controller.quickSection.value = value!;
                                  }
                                : null,
                            items: <DropdownMenuItem<int>>[
                              const DropdownMenuItem<int>(
                                value: 0,
                                child: Text("None"),
                              ),
                              ...controller.sectionsList.map(
                                (Section section) => DropdownMenuItem<int>(
                                  value: section.idx,
                                  child: Text(section.desc),
                                ),
                              ),
                            ],
                          ),
                          MenuTextField(
                            label: "QSI Minute Limit",
                            controller: controller.quickMinutes,
                            enabled: controller.editPermission.value && controller.quickSignIn.value,
                            maxLength: 3,
                            onConfirm: (String? value) {
                              if (controller.quickMinutes.text == "") {
                                controller.quickMinutes.text = "-";
                              } else {
                                final int parsedVal = int.parse(controller.quickMinutes.text);
                                if (parsedVal == 0) controller.quickMinutes.text = "-";
                              }
                            },
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(
                                RegExp("[0-9]"),
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (controller.merchantConfig.value.document.multiCashDrawer)
                        MenuGroup(
                          title: "Cash Drawers",
                          children: <Widget>[
                            for (int i = 0; i < controller.cashDrawerTypes.length; i++)
                              Column(
                                children: <Widget>[
                                  MenuDropdown<int>(
                                    title: "Drawer ${i + 1}",
                                    value: controller.cashDrawerTypes[i].value,
                                    onChanged: controller.editPermission.value
                                        ? (int? value) {
                                            if (value != CashDrawerTypes.SERIAL.index) {
                                              controller.cashDrawerPorts[i].value = 0;
                                            } else {
                                              controller.cashDrawerPorts[i].value = 1;
                                            }
                                            controller.cashDrawerTypes[i].value = value ?? 0;
                                          }
                                        : null,
                                    items: CashDrawerTypes.values
                                        .map(
                                          (CashDrawerTypes type) => DropdownMenuItem<int>(
                                            value: type.index,
                                            child: Text(
                                              type.name.replaceAll("_", " "),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                  if (controller.cashDrawerTypes[i].value == CashDrawerTypes.SERIAL.index)
                                    MenuDropdown<int>(
                                      title: "Port",
                                      value: controller.cashDrawerPorts[i].value,
                                      validator: (int? value) {
                                        if (value != 0 && controller.cashDrawerPorts.where((RxInt d) => d.value == value).length > 1) {
                                          return 'Ports cannot have the same value';
                                        } else {
                                          return null;
                                        }
                                      },
                                      onChanged: controller.editPermission.value
                                          ? (int? value) {
                                              controller.cashDrawerPorts[i].value = value!;
                                            }
                                          : null,
                                      items: List<DropdownMenuItem<int>>.generate(
                                        8,
                                        (int i) => DropdownMenuItem<int>(
                                          value: i + 1,
                                          child: Text(
                                            "Comm  ${i + 1}",
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                          ],
                        ),

                      MenuGroup(
                        title: "Auto Sign-Out",
                        children: <Widget>[
                          Obx(
                            () => MenuCheckbox(
                              text: "Active",
                              value: controller.autoSignOutEnabled.value,
                              onChanged: (bool? v) {
                                controller.autoSignOutEnabled.value = v == true;
                                controller.autoSignOutSecondsController.text = v == true ? "30" : "-";
                              },
                            ),
                          ),
                          MenuTextField(
                            label: "Auto Sign-Out Seconds",
                            controller: controller.autoSignOutSecondsController,
                            enabled: controller.editPermission.value && controller.autoSignOutEnabled.value,
                            maxLength: 4,
                            onConfirm: (String? value) {
                              if (controller.autoSignOutSecondsController.text == "") {
                                controller.autoSignOutSecondsController.text = "-";
                              } else {
                                final int parsedVal = int.parse(controller.autoSignOutSecondsController.text);
                                if (parsedVal == 0) controller.autoSignOutSecondsController.text = "-";
                              }
                            },
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(
                                RegExp("[0-9]"),
                              ),
                            ],
                          ),
                        ],
                      ),

                      MenuGroup(
                        title: "Skip Prep Devices",
                        children: <Widget>[
                          PrepDeviceSelect(
                            selectedDevices: controller.skipDevices,
                          ),
                        ],
                      ),
                      // MenuGroup(
                      //   title: "Liqour Control",
                      //   children: <Widget>[
                      //     Obx(
                      //       () => MenuDropdown<int>(
                      //         title: "Type",
                      //         value: controller.poleDisplayType.value,
                      //         onChanged: controller.editPermission.value
                      //             ? (int? value) {
                      //                 controller.poleDisplayType.value = value!;
                      //               }
                      //             : null,
                      //         items: PoleDisplayType.values
                      //             .map(
                      //               (PoleDisplayType type) => DropdownMenuItem<int>(
                      //                 value: type.index,
                      //                 child: Text(type.name.replaceAll("_", " ")),
                      //               ),
                      //             )
                      //             .toList(),
                      //       ),
                      //     ),
                      //     Obx(
                      //       () => MenuTextField(
                      //         label: "Port",
                      //         controller: controller.poleDispPortController,
                      //         enabled: controller.editPermission.value,
                      //         maxLength: 50,
                      //         inputFormatters: <TextInputFormatter>[
                      //           FilteringTextInputFormatter.deny(
                      //             RegExp("[A-Za-z]"),
                      //           ),
                      //         ],
                      //         validator: (String? value) {
                      //           if (value == null || value.isEmpty) {
                      //             return 'Enter a valid Description';
                      //           }
                      //           return null;
                      //         },
                      //       ),
                      //     ),
                      //   ],
                      // )
                      Center(
                        child: DialogButton(
                          buttonType: EDialogButtonType.DESTRUCTIVE,
                          buttonText: "Delete Terminal",
                          disabled: controller.currentTerminal.value.idx == 98,
                          onTapped: () async => await controller.deleteTerminal(),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
