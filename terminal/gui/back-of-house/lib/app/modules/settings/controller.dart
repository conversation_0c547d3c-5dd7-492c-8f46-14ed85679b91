import 'package:backoffice/app/global_widgets/menu/components/navigable.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/main.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingsController extends GetxController {
  SettingsController();

  final NotificationService _notificationService = Get.find();
  final GraphqlService _graphqlService = Get.find();

  RxBool hasPermission = false.obs;
  RxBool hasResetPermission = false.obs;
  bool hasButtonPermission = false;
  bool hasLevelsPermission = false;
  List<Widget> settingsPages = <Widget>[];

  @override
  Future<void> onInit() async {
    getTiles();
    await viewSettingsPermission();
    await viewButtonsPermission();
    await viewLevelsPermission();
    await resetTerminalPermission();
    //test
    super.onInit();
  }

  void getTiles() {
    settingsPages = <Widget>[
      MenuNavigable(
        title: "Department",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_DEPARTMENT,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "General",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_GENERAL,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Hardware",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_HARDWARE,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Merchant",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_MERCHANT,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Price Levels",
        onTap: () async {
          hasPermission.value && hasLevelsPermission
              ? Get.toNamed(
                  AppRoutes.SETTINGS_PRICE_LEVELS,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "POS Interface Settings",
        onTap: () async {
          hasPermission.value && hasButtonPermission
              ? Get.toNamed(
                  AppRoutes.SETTINGS_POSINTERFACE,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Rooms",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_TABLE_LAYOUT,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Sections",
        onTap: () async {
          hasPermission.value && hasButtonPermission
              ? Get.toNamed(
                  AppRoutes.SETTINGS_SECTIONS,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Terminals",
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_TERMINALS,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
      MenuNavigable(
        title: "Terminal Reset",
        beta: true,
        onTap: () async {
          hasPermission.value
              ? Get.toNamed(
                  AppRoutes.SETTINGS_TERMINAL_RESET,
                  id: AppRoutes.id,
                )
              : _notificationService.error("Access Denied");
        },
      ),
    ];
  }

  Future<void> viewSettingsPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "view",
      "Back Office Settings",
      _graphqlService,
    );
    hasPermission.value = view;
  }

  Future<void> viewButtonsPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "POS Interface",
      _graphqlService,
    );
    hasButtonPermission = view;
  }

  Future<void> resetTerminalPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "Access",
      "Reset Terminal",
      _graphqlService,
    );
    hasResetPermission.value = view;
  }

  Future<void> viewLevelsPermission() async {
    final bool view = await PermissionService.enforce(
      CURRENT_EMPLOYEE.value.employee_class,
      "View",
      "Price Levels",
      _graphqlService,
    );
    hasLevelsPermission = view;
  }
}
