// ignore_for_file: avoid_dynamic_calls

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:args/args.dart';
import 'package:backoffice/app/data/services/department.service.dart';
import 'package:backoffice/app/data/services/item_service.dart';
import 'package:backoffice/app/data/services/liquor_plu_desc_service.dart';
import 'package:backoffice/app/modules/settings/controller.dart';
import 'package:backoffice/app/routes/app_pages.dart';
import 'package:backoffice/app/theme/theme.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/data/services/report_engine.service.dart';
import 'package:desktop/app/data/services/system.service.dart';
// ignore: depend_on_referenced_packages
import 'package:desktop_window/desktop_window.dart' as dw;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:logging_appenders/logging_appenders.dart';

final Logger mainLogger = Logger('BOHMain');

late String? logFilePath;
late File? logFile;

// ignore: non_constant_identifier_names
Rx<Employee> CURRENT_EMPLOYEE = Employee.empty().obs;

class Round2ScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => <PointerDeviceKind>{
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        // etc.
      };
}

Future<void> main(List<String> args) async {
  PrintAppender.setupLogging();

  mainLogger.finest("begin BOH run");

  // Collect and parse arguments passed into main
  final ArgParser parser = ArgParser()..addOption("employee", abbr: "e");
  final ArgResults results = parser.parse(args);

  WidgetsFlutterBinding.ensureInitialized();

  if (kReleaseMode) {
    await dw.DesktopWindow.setFullScreen(true);
  } else {
    await dw.DesktopWindow.setWindowSize(const Size(1024, 768));
  }

  await initServices();

  final GraphqlService graphqlService = Get.find<GraphqlService>();

  if (results["employee"] == null) {
    // Set to demo user when opening app through debug
    final QueryResult<Object> employeeResult = await graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
           query GET_EMPLOYEE(\$id: Int!) {
            employee(where: {id: {_eq: \$id}}) {
              employee
              document
              created_at
              created_by
              employee_class
              updated_at
              updated_by
              is_active
              password
              id
              employee_full_name
              employeeClassByEmployeeClass {
                clearance
                created_at
                created_by
                document
                employee_class
                title
                updated_at
                updated_by
              }
            }
          }
          ''',
        ),
        variables: const <String, dynamic>{
          "id": "1000",
        },
      ),
    );
    if (employeeResult.hasException) {
      mainLogger.severe(employeeResult.exception.toString());
      exit(1);
    }
    CURRENT_EMPLOYEE.value = Employee.fromJson(
      employeeResult.data!['employee'][0] as Map<String, dynamic>,
    );
  } else {
    // Set the real user if the incoming employee isn't null
    final QueryResult<Object> employeeResult = await graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          '''
            query GET_EMPLOYEE(\$employee: uuid!) {
              employee_by_pk(employee: \$employee) {
                employee
                document
                created_at
                created_by
                employee_class
                updated_at
                updated_by
                is_active
                password
                id
                employee_full_name
                employeeClassByEmployeeClass {
                  clearance
                  created_at
                  created_by
                  document
                  employee_class
                  title
                  updated_at
                  updated_by
                }
              }
            }
          ''',
        ),
        variables: <String, dynamic>{
          "employee": results["employee"].toString(),
        },
      ),
    );
    if (employeeResult.hasException) {
      mainLogger.severe(employeeResult.exception.toString());

      exit(1);
    }
    CURRENT_EMPLOYEE.value = Employee.fromJson(
      employeeResult.data!['employee_by_pk'] as Map<String, dynamic>,
    );
  }

  mainLogger.info("Current User: ${CURRENT_EMPLOYEE.value.employee_full_name}");

  runApp(
    GraphQLProvider(
      client: graphqlService.valueNotifierClient,
      child: GetMaterialApp(
        title: "Application",
        initialRoute: AppRoutes.SPLASH,
        builder: BotToastInit(),
        getPages: AppPages.pages,
        debugShowCheckedModeBanner: false,
        navigatorKey: GlobalKey(),
        logWriterCallback: (String text, {bool isError = false}) {
          if (isError) {
            // ignore: avoid_print
            print("BACK OF HOUSE ERROR: $text");
          }
        },
        theme: appThemeData,
        defaultTransition: Transition.noTransition,
        scrollBehavior: Round2ScrollBehavior(),
        navigatorObservers: <NavigatorObserver>[
          BotToastNavigatorObserver(),
        ],
      ),
    ),
  );
}

Future<void> initServices() async {
  try {
    Get.lazyPut(() => NotificationService());

    Get.lazyPut(() => SystemService());

    final GraphqlService graphqlService = await GraphqlService().init("boh");
    Get.lazyPut(() => graphqlService);

    final IdentityService identityService = await IdentityService().init();
    Get.lazyPut(() => identityService);

    Get.lazyPut(() => ReportService());

    Get.lazyPut(() => ReportEngineService());

    Get.lazyPut(() => DepartmentService());

    Get.lazyPut(() => ItemService());

    Get.lazyPut(() => LiquorPluDescService());

    Get.lazyPut(() => SettingsController());

    Get.lazyPut(() => ConfigService());
  } catch (err, stack) {
    mainLogger.severe("Main initServices error", err, stack);
  }
}
