# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "0b2f2bd91ba804e53a61d757b986f89f1f9eaed5b11e4b2f5a2468d86d6c9fc7"
      url: "https://pub.dev"
    source: hosted
    version: "67.0.0"
  advanced_datatable:
    dependency: "direct main"
    description:
      name: advanced_datatable
      sha256: "516d8e7b54398db798009f906d01df1f65285b762beb3631bb1d0c3de6bd6592"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.8"
  analog_clock:
    dependency: transitive
    description:
      name: analog_clock
      sha256: "6550dc371c5834d76295ec7b50c44b96c52f60e9bb2dd49568a3cb100e4b289e"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "37577842a27e4338429a1cbc32679d508836510b056f1eedf0c8d20e39c1383d"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.1"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: "direct main"
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  barcode:
    dependency: transitive
    description:
      name: barcode
      sha256: ab180ce22c6555d77d45f0178a523669db67f95856e3378259ef2ffeb43e6003
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8"
  bidi:
    dependency: transitive
    description:
      name: bidi
      sha256: "9a712c7ddf708f7c41b1923aa83648a3ed44cfd75b04f72d598c45e5be287f9d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.12"
  boardview:
    dependency: "direct main"
    description:
      name: boardview
      sha256: a3319f868baf036b6c217dc4f35c152c4432207552433d94841aa8d3059c6170
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  bot_toast:
    dependency: "direct main"
    description:
      name: bot_toast
      sha256: "6b93030a99a98335b8827ecd83021e92e885ffc61d261d3825ffdecdd17f3bdf"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.3"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "0343061a33da9c5810b2d6cee51945127d8f4c060b7fbdd9d54917f0a3feaaa1"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "339086358431fa15d7eca8b6a36e5d783728cf025e559b834f4609a1fcfb7b0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "581bacf68f89ec8792f5e5a0b2c4decd1c948e97ce659dc783688c8a88fbec21"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.8"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "4ae8ffe5ac758da294ecf1802f2aff01558d8b1b00616aa7538ea9a8a5d50799"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: c7913a9737ee4007efedaffc968c049fd0f3d0e49109e778edc10de9426005cb
      url: "https://pub.dev"
    source: hosted
    version: "8.9.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: f692079e25e7869c14132d39f223f8eec9830eb76131925143b2129c4bb01b37
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: b74247fad72c171381dbe700ca17da24deac637ab6d43c343b42867acb95c991
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "3945034e86ea203af7a056d98e98e42a5518fff200d6e8e6647e1886b07e936e"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  cross_scroll:
    dependency: "direct main"
    description:
      name: cross_scroll
      sha256: "50d2006d51c0640887928200c8561c6833a6f98d576f6fcb62f4f2f5916d0056"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.76"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csv:
    dependency: "direct main"
    description:
      name: csv
      sha256: c6aa2679b2a18cb57652920f674488d89712efaf4d3fdf2e537215b35fc19d6c
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  currency_text_input_formatter:
    dependency: "direct main"
    description:
      name: currency_text_input_formatter
      sha256: "9646f593a8bdfb9f471b56e79d5556bf49ff0f49c5f17481b28bb553e2592c47"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "99e066ce75c89d6b29903d788a7bb9369cf754f7b24bf70bf4b6d6d6b26853b9"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.6"
  dartz:
    dependency: "direct main"
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  data_table_2:
    dependency: "direct main"
    description:
      name: data_table_2
      sha256: fdb0551f103f1daf837bddfde14619fd9e683408833a618c9afabeb533fce88c
      url: "https://pub.dev"
    source: hosted
    version: "2.5.11"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  desktop:
    dependency: "direct main"
    description:
      path: "../front-of-house"
      relative: true
    source: path
    version: "1.0.0+1"
  desktop_window:
    dependency: transitive
    description:
      name: desktop_window
      sha256: "6256fb6feb7b5ec1311c76a3503f89202825bfe92c0458ec5fe7a728ffa216d5"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "5598aa796bbf4699afd5c67c0f5f6e2ed542afc956884b9cd58c306966efc260"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  easy_debounce:
    dependency: "direct main"
    description:
      name: easy_debounce
      sha256: f082609cfb8f37defb9e37fc28bc978c6712dedf08d4c5a26f820fa10165a236
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  email_sender:
    dependency: "direct main"
    description:
      name: email_sender
      sha256: "4f507b969894c69378c7f217b418bd95108878a0065b7a3161fd4eb33b25ac94"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  email_validator:
    dependency: "direct main"
    description:
      name: email_validator
      sha256: e9a90f27ab2b915a27d7f9c2a7ddda5dd752d6942616ee83529b686fc086221b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.17"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "44baa799834f4c803921873e7446a2add0f3efa45e101a054b1f0ab9b95f8edc"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  excel:
    dependency: "direct main"
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: d1d0ac3966b36dc3e66eeefb40280c17feb87fa2099c6e22e6a1fc959327bd03
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0+1"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: "direct main"
    description:
      name: fl_chart
      sha256: "00b74ae680df6b1135bdbea00a7d1fc072a9180b7c3f3702e4b19a9943f5ed7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.66.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_colorpicker:
    dependency: "direct main"
    description:
      name: flutter_colorpicker
      sha256: "458a6ed8ea480eb16ff892aedb4b7092b2804affd7e046591fb03127e8d8ef8b"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_email_sender:
    dependency: "direct main"
    description:
      name: flutter_email_sender
      sha256: "5001e9158f91a8799140fb30a11ad89cd587244f30b4f848d87085985c49b60f"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: "6a126f703b89499818d73305e4ce1e3de33b4ae1c5512e3b8eab4b986f46774c"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.6"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "8cf40eebf5dec866a6d1956ad7b4f7016e6c0cc69847ab946833b7d43743809f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.19"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      sha256: "698a037274a66dbae8697c265440e6acb6ab6cae9ac5f95c749e7944d8f28d41"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      path: "../shared/flutter_plugins/font_awesome_flutter"
      relative: true
    source: path
    version: "10.3.0"
  fpdart:
    dependency: "direct main"
    description:
      name: fpdart
      sha256: "2c8078716966f6bb8b723a73ba44842787860d0c8d83c6912e78f3d7b204e444"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  gql:
    dependency: transitive
    description:
      name: gql
      sha256: "998304fbb88a3956cfea10cd27a56f8e5d4b3bc110f03c952c18a9310774e8bb"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0"
  gql_dedupe_link:
    dependency: transitive
    description:
      name: gql_dedupe_link
      sha256: "89681048cf956348e865da872a40081499b8c087fc84dd4d4b9c134bd70d27b3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3+1"
  gql_error_link:
    dependency: transitive
    description:
      name: gql_error_link
      sha256: e7bfdd2b6232f3e15861cd96c2ad6b7c9c94693843b3dea18295136a5fb5b534
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+1"
  gql_exec:
    dependency: transitive
    description:
      name: gql_exec
      sha256: "0d1fdb2e4154efbfc1dcf3f35ec36d19c8428ff0d560eb4c45b354f8f871dc50"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.3"
  gql_http_link:
    dependency: transitive
    description:
      name: gql_http_link
      sha256: "89ef87b32947acf4189f564c095f1148b0ab9bb9996fe518716dbad66708b834"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.5"
  gql_link:
    dependency: transitive
    description:
      name: gql_link
      sha256: f7973279126bc922d465c4f4da6ed93d187085e597b3480f5e14e74d28fe14bd
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  gql_transform_link:
    dependency: transitive
    description:
      name: gql_transform_link
      sha256: b1735a9a92d25a92960002a8b40dfaede95ec1e5ed848906125d69efd878661f
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2+1"
  graphql:
    dependency: transitive
    description:
      name: graphql
      sha256: bda5b794345087ccbd16942045be8091e2ac4619285bb22e73555d5fd88c4043
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0-beta.1"
  graphql_flutter:
    dependency: "direct main"
    description:
      name: graphql_flutter
      sha256: "06059ac9e8417c71582f05e28a59b1416d43959d34a6a0d9565341e3a362e117"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  group_list_view:
    dependency: "direct main"
    description:
      name: group_list_view
      sha256: "58bfc7f4b818abff531c2b202fb18b08abfb503f1621b0e86137a4fa4b6d91dd"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  hex:
    dependency: transitive
    description:
      name: hex
      sha256: "4e7cd54e4b59ba026432a6be2dd9d96e4c5205725194997193bf871703b82c4a"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: "4c68bfd5ae83e700b5204c1e74451e7bf3cf750e6843c6e158289cf56bda018e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.7"
  infinite_listview:
    dependency: transitive
    description:
      name: infinite_listview
      sha256: f6062c1720eb59be553dfa6b89813d3e8dd2f054538445aaa5edaddfa5195ce6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: aa1f5a8912615733e0fdc7a02af03308933c93235bdc8d50d0b0c8a8ccb0b969
      url: "https://pub.dev"
    source: hosted
    version: "6.7.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "78eb209deea09858f5269f5a5b02be4049535f568c07b275096836f01ea323fa"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.0"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: b46c5e37c19120a8a01918cfaf293547f47269f7cb4b0058f21531c2465d6ef0
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: a597f72a664dbd293f3bfc51f9ba69816f84dcd403cdac7066cb3f6003f3ab47
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  lint:
    dependency: "direct dev"
    description:
      name: lint
      sha256: d758a5211fce7fd3f5e316f804daefecdc34c7e53559716125e6da7388ae8565
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  loading_animations:
    dependency: "direct main"
    description:
      name: loading_animations
      sha256: c62a8c1fbbe5ade3ac2814128a9aa92ee784756b11ad9e6a915b673b90005cc8
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  logging:
    dependency: "direct main"
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  logging_appenders:
    dependency: "direct main"
    description:
      name: logging_appenders
      sha256: "1fb8a008c04246f4677a0d034d69779a5975e56e02573a5162240239b247e239"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0+1"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "893da7a0022ec2fcaa616f34529a081f617e86cc501105b856e5a3184c58c7c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  mailer:
    dependency: "direct main"
    description:
      name: mailer
      sha256: d25d89555c1031abacb448f07b801d7c01b4c21d4558e944b12b64394c84a3cb
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  mask_text_input_formatter:
    dependency: "direct main"
    description:
      name: mask_text_input_formatter
      sha256: "978c58ec721c25621ceb468e633f4eef64b64d45424ac4540e0565d4f7c800cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "0e0a020085b65b6083975e499759762399b4475f766c21668c4ecca34ea74e5a"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: d584fa6707a52763a52446f02cc621b077888fb63b93bbcb1143a7be5a0c0c04
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  multi_select_flutter:
    dependency: "direct main"
    description:
      name: multi_select_flutter
      sha256: "503857b415d390d29159df8a9d92d83c6aac17aaf1c307fb7bcfc77d097d20ed"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.3"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  normalize:
    dependency: transitive
    description:
      name: normalize
      sha256: "8a60e37de5b608eeaf9b839273370c71ebba445e9f73b08eee7725e0d92dbc43"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.2+1"
  numberpicker:
    dependency: "direct main"
    description:
      name: numberpicker
      sha256: "4c129154944b0f6b133e693f8749c3f8bfb67c4d07ef9dcab48b595c22d1f156"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: fec0d61223fba3154d87759e3cc27fe2c8dc498f6386c6d6fc80d1afdd1bf378
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: a248d8146ee5983446bf03ed5ea8f6533129a12b11f12057ad1b4a67a2b3b41d
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pdf:
    dependency: "direct main"
    description:
      name: pdf
      sha256: "243f05342fc0bdf140eba5b069398985cdbdd3dbb1d776cf43d5ea29cc570ba6"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pool:
    dependency: "direct main"
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  postgrest:
    dependency: "direct main"
    description:
      name: postgrest
      sha256: "9a3b590cf123f8d323b6a918702e037f037027d12a01902f9dc6ee38fdc05d6c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c799b721d79eb6ee6fa56f00c04b472dcd44a30d258fac2174a6ec57302678f8
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  requests:
    dependency: transitive
    description:
      name: requests
      sha256: "18b5511c2d6cca922260d0c38e78ed76d4a359fea966fcb4aebe30d8351638ef"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  screen_retriever:
    dependency: transitive
    description:
      name: screen_retriever
      sha256: "6ee02c8a1158e6dae7ca430da79436e3b1c9563c8cf02f524af997c201ac2b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.9"
  settings_ui:
    dependency: transitive
    description:
      name: settings_ui
      sha256: d9838037cb554b24b4218b2d07666fbada3478882edefae375ee892b6c820ef3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: "89f967eca29607c933ba9571d838be31d67f53f6e4ee15147d5dc2934fee1b1e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: c87c3875f91262785dade62d135760c2c69cb217ac759485334c5857ad89f6e3
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: c0713a43e323c3302c2abe2a1cc89aa057a387101ebd280371d6a6c9fa68516b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "190222579a448b03896e0ca6eca5998fa810fda630c1d65e2f78b3f638f54812"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.13"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: e17dcc7a1d0701e84d0a83c0040503cdcc6c72e44db0d733ab4c706dd5b8b9f8
      url: "https://pub.dev"
    source: hosted
    version: "25.2.7"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      sha256: "90398b38bceedc34575823ea27d6a1927934852ff2de8f1c1326e35d5b186b58"
      url: "https://pub.dev"
    source: hosted
    version: "25.1.42"
  synchronized:
    dependency: "direct main"
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test:
    dependency: transitive
    description:
      name: test
      sha256: a1f7595805820fcc05e5c52e3a231aedd0b72972cb333e8c738a8b1239448b6f
      url: "https://pub.dev"
    source: hosted
    version: "1.24.9"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: a757b14fc47507060a162cc2530d9a4a2f92f5100a952c7443b5cad5ef5b106a
      url: "https://pub.dev"
    source: hosted
    version: "0.5.9"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: b3d56ff4341b8f182b96aceb2fa20e3dcb336b9f867bc0eafc0de10f1048e957
      url: "https://pub.dev"
    source: hosted
    version: "13.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "58c6666b342a38816b2e7e50ed0f1e261959630becd4c879c4f26bfa14aa5a42"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "87d3f2333bb240704cd3f1c6b5b7acd8a10e7f0bc28c28dcf14e782014f4a572"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "0eaf06e3446824099858367950a813472af675116bf63f008a4c2a75ae13e9cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.0"
  window_manager:
    dependency: transitive
    description:
      name: window_manager
      sha256: b3c895bdf936c77b83c5254bec2e6b3f066710c1f89c38b20b8acc382b525494
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: "direct main"
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  yet_another_json_isolate:
    dependency: transitive
    description:
      name: yet_another_json_isolate
      sha256: "56155e9e0002cc51ea7112857bbcdc714d4c35e176d43e4d3ee233009ff410c9"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
sdks:
  dart: "3.3.2"
  flutter: ">=3.19.4"
