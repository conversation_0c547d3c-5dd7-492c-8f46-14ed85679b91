// ignore_for_file: non_constant_identifier_names, prefer_const_declarations

import 'package:desktop/app/data/enums/action_config_fields.dart';
// ignore: unused_import
import 'package:desktop/app/data/enums/action_config_items_variables.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/action.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/pay_rate.dart';
import 'package:intl/intl.dart';

class Constants {
  static final int LEADER_TERMINAL_INDEX = 98;

  // Hardware interfaces
  static final String HW_HOST = "127.0.0.1";
  static final int HW_PRINTER_PORT = 8200;
  static final int HW_SCALE_PORT = 8201;
  static final int HW_POLE_DISPLAY_PORT = 8203;
  static final int HW_REPORT_ENGINE = 8207;
  static final int HW_SCANNER_SCALE_PORT = 8204;
  static final int HW_SERIAL_DRAWER_PORT = 8208;
  static final int HW_LIQCTL_REQUEST_PORT = 8206;

  // Hardware interfaces enums
  static final int HW_ENUM_SCALE_UOM_LB = 0;
  static final int HW_ENUM_SCALE_UOM_OZ = 1;
  static final int HW_ENUM_SCALE_UOM_G = 2;
  static final int HW_ENUM_SCALE_UOM_KG = 3;

  static final String PAYMENTS_API_ENDPOINT = "http://localhost:8890";

  static final DateFormat clockFormat = DateFormat("h:mm:ss a");

  static final String baseUrl = "http://localhost:8880/api/v1";

  static final int onlineTerminalNumber = -2;

  static final Employee onlineOrderEmployee = Employee(
    id: -2,
    employee: "00000000-0000-0000-0000-000000000002",
    employee_class: "dae2fdb8-8251-4b7c-98f9-58301e504cab",
    is_active: false,
    password: "",
    document: EmployeeDocument(
      firstName: "Online",
      lastName: "Order",
      payRates: <PayRate>[],
    ),
    created_at: DateTime.now(),
    created_by: "283203bf-a239-4f31-b161-a6851df4a9c4",
    updated_by: "283203bf-a239-4f31-b161-a6851df4a9c4",
  );

  static final List<int> restrictedFlags = <int>[
    SaleFlags.CANCEL.index,
    SaleFlags.COMBINED.index,
    SaleFlags.NONREFUNDABLE.index,
    SaleFlags.REFUNDED.index,
    SaleFlags.PAID_OUT.index,
    SaleFlags.VOIDED.index,
  ];

  // Default register menu buttons
  static final RegisterMenusJsonRecordDocument defaultRegisterMenus = RegisterMenusJsonRecordDocument(
    toolbar: RegisterMenusJsonRecordToolbar(
      custom_colors: true,
      menu: <RegisterMenusJsonRecordButton>[
        RegisterMenusJsonRecordButton(
          index: 0,
          text: "Tender",
          action: "TENDER",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 1,
          text: "Print All On One",
          action: "PRINT_CHECK",
          section: "SALE",
          config: <String, dynamic>{"prtOpts": 2},
        ),
        RegisterMenusJsonRecordButton(
          index: 2,
          text: "Select Seats to Print",
          action: "PRINT_CHECK",
          section: "SALE",
          config: <String, dynamic>{"prtOpts": 5},
        ),
        RegisterMenusJsonRecordButton(
          index: 3,
          text: "Sale Table/Description",
          action: "SALE_TABLE_AND_DESCRIPTION",
          section: "SALE",
          config: <String, dynamic>{"showFields": 1},
        ),
        RegisterMenusJsonRecordButton(
          index: 4,
          text: "New Seat",
          action: "NEW_SEAT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 5,
          text: "Attach Customer",
          action: "CUSTOMER",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 6,
          text: "Another Round",
          action: "ANOTHER_ROUND",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 7,
          text: "Void Item",
          action: "VOID_ITEM",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 8,
          text: "Send To Kitchen",
          action: "SEND_TO_PREP_DEVICES",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 9,
          text: "Liquor Control",
          action: "LIQUOR_CTL",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 10,
          text: "Item Qty",
          action: "QUANTITY",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 11,
          text: "Enter Comment",
          action: "ENTER_COMMENT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 12,
          text: "Sign Off",
          action: "EXIT",
          section: "GLOBAL",
        ),
      ],
    ),
    user: RegisterMenusJsonRecordUser(
      is_active: true,
      custom_colors: true,
      menu: <RegisterMenusJsonRecordButton>[
        RegisterMenusJsonRecordButton(
          index: 0,
          text: "Issue Gift Card",
          action: "ISSUE_GIFT_CARD",
          section: "GIFT",
        ),
        RegisterMenusJsonRecordButton(
          index: 1,
          text: "Print All Seats Separate",
          action: "PRINT_CHECK",
          section: "SALE",
          config: <String, dynamic>{"prtOpts": 3},
        ),
        RegisterMenusJsonRecordButton(
          index: 2,
          text: "Open Price",
          action: "OPEN_PRICE",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 3,
          text: "Takeout",
          action: "TAKEOUT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 4,
          text: "New ToGo Seat",
          action: "NEW_TOGO_SEAT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 5,
          text: "Add Modifier",
          action: "ADD_MODIFIER",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 6,
          text: "View Tables",
          action: "SALE_TABLE_AND_DESCRIPTION",
          section: "SALE",
          config: <String, dynamic>{"showFields": 4},
        ),
        RegisterMenusJsonRecordButton(
          index: 7,
          text: "Add Promised Time",
          action: "PROMISED_TIME",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 8,
          text: "Discount",
          action: "DISCOUNT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 9,
          text: "Split Sale",
          action: "SPLIT_SALE",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 10,
          text: "Combine Sales",
          action: "COMBINE_SALES",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 11,
          text: "Check EBT Balance",
          action: "EBT_BALANCE",
          section: "EBT",
        ),
        RegisterMenusJsonRecordButton(
          index: 12,
          text: "Gift Card Balance",
          action: "GIFT_CARD_BALANCE",
          section: "GIFT",
        ),
        RegisterMenusJsonRecordButton(
          index: 13,
          text: "Transaction History",
          action: "TRANSACTION_HISTORY",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 14,
          text: "Reprint Last Settled",
          action: "REPRINT_LAST",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 15,
          text: "No Sale",
          action: "NO_SALE",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 16,
          text: "Tip Adjust",
          action: "TIP_ADJUST",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 17,
          text: "Forgive Tax",
          action: "FORGIVE_TAX",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 18,
          text: "Order Type",
          action: "ORDER_TYPE",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 19,
          text: "Move Items",
          action: "MOVE_ITEMS",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 20,
          text: "Transfer Sales",
          action: "TRANSFER_SALES",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 21,
          text: "Pre Auth",
          action: "PRE_AUTH",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 22,
          text: "Sale Info",
          action: "SALE_INFO",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 23,
          text: "Split/Unsplit Items",
          action: "SPLIT_UNSPLIT_ITEMS",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 24,
          text: "Pick Up/Loan",
          action: "PICK_UP",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 25,
          text: "Prep Details",
          action: "PREP_DETAILS",
          section: "SALE",
        ),
      ],
    ),
    admin: RegisterMenusJsonRecordAdmin(
      menu: <RegisterMenusJsonRecordButton>[
        RegisterMenusJsonRecordButton(
          index: 0,
          text: "Cancel Sale",
          action: "CANCEL",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 1,
          text: "End of Day",
          action: "END_OF_DAY",
          section: "GLOBAL",
        ),
        RegisterMenusJsonRecordButton(
          index: 2,
          text: "Negative Sale",
          action: "NEGATIVE_SALE_MODE",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 3,
          text: "Paid Out/Loan",
          action: "PAID_OUT",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 4,
          text: "Set Cashier",
          action: "ASSIGN_CASHIER",
          section: "GLOBAL",
        ),
        RegisterMenusJsonRecordButton(
          index: 5,
          text: "Server Report",
          action: "SERVER_REPORT",
          section: "GLOBAL",
        ),
        RegisterMenusJsonRecordButton(
          index: 6,
          text: "Cashier Report",
          action: "CASHIER_REPORT",
          section: "GLOBAL",
        ),
        RegisterMenusJsonRecordButton(
          index: 7,
          text: "Add Gratuity",
          action: "ADD_GRATUITY",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 8,
          text: "Add Gratuity",
          action: "ADD_GRATUITY",
          section: "SALE",
        ),
        RegisterMenusJsonRecordButton(
          index: 9,
          text: "Hold and Fire",
          action: "HOLD_AND_FIRE",
          section: "SALE",
        ),
      ],
      is_active: true,
      custom_colors: true,
    ),
  );

  // Actions For use in making, editing and using buttons
  static final List<Action> actions = <Action>[
    Action(name: "TENDER"),
    Action(name: "GIFT_CARD_BALANCE"),
    Action(name: "END_OF_DAY"),
    Action(name: "ISSUE_GIFT_CARD"),
    Action(name: "NEGATIVE_SALE_MODE"),
    Action(name: "PAID_OUT"),
    Action(name: "OPEN_PRICE"),
    Action(name: "EBT_BALANCE"),
    Action(name: "SUSPEND_RETRIEVE"),
    Action(name: "DISCOUNT"),
    Action(name: "EXIT"),
    Action(name: "NO_SALE"),
    Action(name: "NEW_SEAT"),
    Action(name: "COMBINE_SALES"),
    Action(name: "CUSTOMER"),
    Action(name: "FORGIVE_TAX"),
    Action(name: "TIP_ADJUST"),
    Action(name: "QUANTITY"),
    Action(name: "SPLIT_SALE"),
    Action(name: "ASSIGN_CASHIER"),
    Action(name: "CASHIER_REPORT"),
    Action(name: "PROMISED_TIME"),
    Action(name: "ORDER_TYPE"),
    Action(name: "ENTER_COMMENT"),
    Action(name: "MOVE_ITEMS"),
    Action(name: "ANOTHER_ROUND"),
    Action(name: "SALE_INFO"),
    Action(name: "SERVER_REPORT"),
    Action(name: "TRANSFER_SALES"),
    Action(name: "PRE_AUTH"),
    Action(name: "SEND_TO_PREP_DEVICES"),
    Action(name: "ADD_GRATUITY"),
    Action(name: "TAKEOUT"),
    Action(name: "NEW_TOGO_SEAT"),
    Action(name: "ADD_MODIFIER"),
    Action(name: "SPLIT_UNSPLIT_ITEMS"),
    Action(name: "SEARCH_RECIPE"),
    Action(name: "QUICK_CASH"),
    Action(name: "PICK_UP"),
    Action(name: "LIQUOR_CTL"),
    Action(name: "TOGGLE_ONLINE_ORDERING"),
    Action(name: "HOLD_AND_FIRE"),
    Action(name: "PREP_DETAILS"),
    Action(
      name: "CANCEL",
      document: <String, ActionDocumentField>{
        "cancelOpts": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.RADIO.index,
          label: "Cancel options",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Allow canceling of all sales",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Only allow canceling sales with no items that have been sent to prep device",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Only allow canceling empty sales",
            ),
          ],
        ),
      },
    ),
    Action(
      name: "VOID_ITEM",
      document: <String, ActionDocumentField>{
        "voidOpts": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.RADIO.index,
          label: "Void/error correct options",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Both error correct and void contextually",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Only error correct before items is sent to prep devices",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Only void items after they're sent to prep devices",
            ),
          ],
        ),
      },
    ),
    Action(
      name: "REPRINT_LAST",
      document: <String, ActionDocumentField>{
        "copyOpts": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.RADIO.index,
          label: "Which Copies are printed on button press",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Print Customer and Merchant Copies",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Print Merchant Only",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Print Customer Only",
            ),
          ],
        ),
        "prntAuth": ActionDocumentField(
          idx: 2,
          field: ActionConfigFields.BOOL.index,
          label: "Reprint auth slip for customer copy on credit payments",
          default_value: true,
        ),
      },
    ),
    Action(
      name: "TRANSACTION_HISTORY",
      document: <String, ActionDocumentField>{
        "reprnt": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.BOOL.index,
          label: "Allow reprinting receipt through recall menu",
          default_value: true,
        ),
        "refund": ActionDocumentField(
          idx: 2,
          field: ActionConfigFields.BOOL.index,
          label: "Allow refund through recall menu",
          default_value: true,
        ),
        "reOpen": ActionDocumentField(
          idx: 3,
          field: ActionConfigFields.BOOL.index,
          label: "Allow reopening of sale through recall menu",
          default_value: true,
        ),
        "copy": ActionDocumentField(
          idx: 4,
          field: ActionConfigFields.BOOL.index,
          label: "Allow copying of sale through recall menu",
          default_value: true,
        ),
        "copyOpts": ActionDocumentField(
          idx: 5,
          field: ActionConfigFields.RADIO.index,
          label: "Which Copies are printed on reprint receipt",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Print Customer and Merchant Copies",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Print Merchant Only",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Print Customer Only",
            ),
          ],
        ),
        "tipAdjust": ActionDocumentField(
          idx: 6,
          field: ActionConfigFields.BOOL.index,
          label: "Allow tip adjust through recall menu",
          default_value: true,
        ),
        "void": ActionDocumentField(
          idx: 7,
          field: ActionConfigFields.BOOL.index,
          label: "Allow voiding the tender through recall menu",
          default_value: true,
        ),
      },
    ),
    Action(
      name: "SALE_TABLE_AND_DESCRIPTION",
      document: <String, ActionDocumentField>{
        "showFields": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.RADIO.index,
          label: "What menu is opened",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Change Table and Description (Only description if seating disabled)",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Change Table Only (Will not work if seating diabled)",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Change Description Only",
            ),
            ActionDocumentItem(
              value: 4,
              text: "View Tables Mode (Opens sales)",
            ),
          ],
        ),
      },
    ),
    Action(
      name: "PRINT_CHECK",
      document: <String, ActionDocumentField>{
        "prtOpts": ActionDocumentField(
          idx: 1,
          field: ActionConfigFields.RADIO.index,
          label: "Print options",
          default_value: 1,
          items: <ActionDocumentItem>[
            ActionDocumentItem(
              value: 1,
              text: "Print all seats, single check",
            ),
            ActionDocumentItem(
              value: 2,
              text: "Print all seats, single Check with subtotals",
            ),
            ActionDocumentItem(
              value: 3,
              text: "Print all seats, separate checks",
            ),
            ActionDocumentItem(
              value: 4,
              text: "Print all seats, no seat numbers",
            ),
            ActionDocumentItem(
              value: 5,
              text: "Print selected seat",
            ),
          ],
        ),
      },
    ),
  ];
}
