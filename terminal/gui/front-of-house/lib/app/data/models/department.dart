import 'package:clock/clock.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:json_annotation/json_annotation.dart';

part 'department.g.dart';

@JsonSerializable()
@DateTimeSerializer()
class Department {
  Department({
    required this.department,
    required this.title,
    required this.document,
    required this.created_by,
    required this.updated_by,
  });

  Department.empty();

  factory Department.fromJson(Map<String, dynamic> json) => _$DepartmentFromJson(json);

  String department = "";
  String title = "";
  DepartmentDocument document = DepartmentDocument.empty();

  DateTime? created_at;
  String created_by = "";
  DateTime? updated_at;
  String updated_by = "";

  @override
  bool operator ==(Object other) =>
      other is Department &&
      other.department == department &&
      other.title == title &&
      other.document == document &&
      other.created_at == created_at &&
      other.updated_at == updated_at &&
      other.created_by == created_by &&
      other.updated_by == updated_by;

  @override
  int get hashCode => Object.hash(
        department,
        title,
        document,
        created_at,
        created_by,
        updated_at,
        updated_by,
      );

  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}

@JsonSerializable()
class DepartmentDocument {
  DepartmentDocument({
    this.isTaxable,
    this.colorHash,
    this.order,
    this.majorGroup,
    this.prep = 0,
    this.restrictEnabled = false,
    this.timeWindows = const <String, ActiveWindow>{},
    required this.taxFlags,
    this.friendlyTitle,
    this.showOnline = true,
    this.printInRed = false,
    this.needsVoidPermission = false,
  });

  factory DepartmentDocument.fromJson(Map<String, dynamic> json) => _$DepartmentDocumentFromJson(json);

  DepartmentDocument.empty();
  bool? isTaxable;
  int? colorHash;
  int? order;
  String? majorGroup;
  int prep = 0;
  int taxFlags = 0;
  bool restrictEnabled = false;

  // Key = weekday ("mon", "tue" …), value = window
  Map<String, ActiveWindow> timeWindows = const <String, ActiveWindow>{};
  String? friendlyTitle;
  bool showOnline = true;
  // Should this department be marked red on the receipt?
  bool? printInRed = false;
  bool needsVoidPermission = false;

  @override
  bool operator ==(Object other) =>
      other is DepartmentDocument &&
      other.isTaxable == isTaxable &&
      other.colorHash == colorHash &&
      other.order == order &&
      other.majorGroup == majorGroup &&
      other.prep == prep &&
      other.taxFlags == taxFlags &&
      other.timeWindows == timeWindows &&
      other.restrictEnabled == restrictEnabled &&
      other.friendlyTitle == friendlyTitle &&
      other.showOnline == showOnline &&
      other.printInRed == printInRed;

  @override
  int get hashCode => hashValues(
        isTaxable,
        colorHash,
        order,
        majorGroup,
        prep,
        taxFlags,
        timeWindows,
        restrictEnabled,
        friendlyTitle,
        showOnline,
        printInRed,
        needsVoidPermission,
      );

  Map<String, dynamic> toJson() => _$DepartmentDocumentToJson(this);

  final int midnightMS = 86400000;

  bool isWithinTimeWindow() {
    if (!restrictEnabled) return true;

    final DateTime now = clock.now();
    final int nowInMs = now.hour * 3600000 + now.minute * 60000 + now.second * 1000 + now.millisecond;

    const Map<int, String> days = {
      1: 'mon',
      2: 'tue',
      3: 'wed',
      4: 'thu',
      5: 'fri',
      6: 'sat',
      7: 'sun',
    };

    String dayName(int weekday) => days[weekday] ?? '';

    // Check today’s window
    final ActiveWindow? timeWin = timeWindows[dayName(now.weekday)];
    if (timeWin != null) {
      return inWindow(timeWin, nowInMs);
    }

    // Check yesterday’s window for a cross‐midnight case.
    final ActiveWindow? yestWin = timeWindows[dayName(((now.weekday - 2 + 7) % 7) + 1)];
    if (yestWin != null && (yestWin.close) < (yestWin.open)) {
      // For an early time like Monday 03:00, shift nowInMs by 24h so that
      // it compares correctly to yesterday's window.
      final int adjustedNow = nowInMs + midnightMS; // 24*3600000
      return inWindow(yestWin, adjustedNow);
    }

    // No window defined for today or yesterday → unrestricted
    return true;
  }

  bool inWindow(ActiveWindow tw, int nowInMs) {
    final int open = tw.open;
    final int close = tw.close;

    // "Always open" when open and close are zero.
    if (open == -1 && close == -1) return true;

    // If the window does not span midnight, do a simple check.
    if (close >= open) {
      return nowInMs >= open && nowInMs < close;
    }

    // Cross‑midnight window (e.g. 22:00–02:00).
    if (nowInMs >= open && nowInMs < midnightMS) {
      // We're after open but before midnight.
      return true;
    }

    if (nowInMs - midnightMS < close) {
      // We're after midnight but before close.
      return true;
    }

    // We're outside the window.
    return false;
  }
}
