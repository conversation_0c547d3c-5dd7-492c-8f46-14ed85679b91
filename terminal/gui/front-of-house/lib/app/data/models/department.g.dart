// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'department.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      department: json['department'] as String,
      title: json['title'] as String,
      document:
          DepartmentDocument.fromJson(json['document'] as Map<String, dynamic>),
      created_by: json['created_by'] as String,
      updated_by: json['updated_by'] as String,
    )
      ..created_at = const DateTimeSerializer().fromJson(json['created_at'])
      ..updated_at = const DateTimeSerializer().fromJson(json['updated_at']);

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'department': instance.department,
      'title': instance.title,
      'document': instance.document,
      'created_at': const DateTimeSerializer().toJson(instance.created_at),
      'created_by': instance.created_by,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
      'updated_by': instance.updated_by,
    };

DepartmentDocument _$DepartmentDocumentFromJson(Map<String, dynamic> json) =>
    DepartmentDocument(
      isTaxable: json['isTaxable'] as bool?,
      colorHash: json['colorHash'] as int?,
      order: json['order'] as int?,
      majorGroup: json['majorGroup'] as String?,
      prep: json['prep'] as int? ?? 0,
      restrictEnabled: json['restrictEnabled'] as bool? ?? false,
      timeWindows: (json['timeWindows'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, ActiveWindow.fromJson(e as Map<String, dynamic>)),
          ) ??
          const <String, ActiveWindow>{},
      taxFlags: json['taxFlags'] as int,
      friendlyTitle: json['friendlyTitle'] as String?,
      showOnline: json['showOnline'] as bool? ?? true,
      printInRed: json['printInRed'] as bool? ?? false,
      needsVoidPermission: json['needsVoidPermission'] as bool? ?? false,
    );

Map<String, dynamic> _$DepartmentDocumentToJson(DepartmentDocument instance) =>
    <String, dynamic>{
      'isTaxable': instance.isTaxable,
      'colorHash': instance.colorHash,
      'order': instance.order,
      'majorGroup': instance.majorGroup,
      'prep': instance.prep,
      'taxFlags': instance.taxFlags,
      'restrictEnabled': instance.restrictEnabled,
      'timeWindows': instance.timeWindows,
      'friendlyTitle': instance.friendlyTitle,
      'showOnline': instance.showOnline,
      'printInRed': instance.printInRed,
      'needsVoidPermission': instance.needsVoidPermission,
    };
