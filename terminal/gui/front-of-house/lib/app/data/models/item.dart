// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';
// ignore: depend_on_referenced_packages
import 'package:json_annotation/json_annotation.dart';

part 'item.g.dart';

/// Note: Even though hashCode is defined (via Equatable), do not use this object as a key in a hash-based collection, as it is mutable.
@JsonSerializable()
@DateTimeSerializer()
// ignore: must_be_immutable
class Item extends Equatable {
  Item({
    required this.item,
    required this.long_desc,
    required this.upc,
    required this.department,
    required this.document,
    this.created_at,
    required this.created_by,
    this.updated_at,
    required this.updated_by,
    this.liq_ctl_plu,
    this.departmentByDepartment,
  });
  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);

  /// Call Item.empty() to construct an empty Item object.
  Item.empty();

  /// This is a UUID identifier of this item. Currently this is the primary key
  /// we want.
  String item = "";

  /// This is a unique description. This is the primary key we are deprecating.
  String long_desc = "";

  /// This is a store-specific identifier, not necessarily a UUID; usually it is somewhat human readable. Used for UPC scanning.
  String upc = "";

  /// UUID foreign key into the department table.
  String department = "";
  // ignore: prefer_const_literals_to_create_immutables
  ItemDocument document = ItemDocument(pricing: <String, int>{});

  /// This should be UTC, prefer getting it from the server.
  DateTime? created_at;
  String created_by = "";

  /// This should be UTC, prefer getting it from the server.
  DateTime? updated_at;
  String updated_by = "";
  int? liq_ctl_plu;

  // Sometimes (depending on where this Item comes from) this can be populated
  // with the associated Department object.
  Department? departmentByDepartment;

  Item copyWith({
    String? item,
    String? long_desc,
    String? upc,
    String? department,
    ItemDocument? document,
    Option<DateTime?>? created_at,
    String? created_by,
    Option<DateTime?>? updated_at,
    String? updated_by,
    int? liq_ctl_plu,
    Option<Department?>? departmentByDepartment,
  }) {
    final Item shallowCopy = Item(
      item: item ?? this.item,
      long_desc: long_desc ?? this.long_desc,
      upc: upc ?? this.upc,
      department: department ?? this.department,
      document: document ?? this.document,
      created_at: (created_at ?? some(this.created_at)).toNullable(),
      created_by: created_by ?? this.created_by,
      updated_at: (updated_at ?? some(this.updated_at)).toNullable(),
      updated_by: updated_by ?? this.updated_by,
      liq_ctl_plu: liq_ctl_plu ?? this.liq_ctl_plu,
      departmentByDepartment: (departmentByDepartment ?? some(this.departmentByDepartment)).toNullable(),
    );

    final Map<String, dynamic> itemDict = json.decode(json.encode(shallowCopy)) as Map<String, dynamic>;
    return Item.fromJson(itemDict);
  }

  @override
  List<Object?> get props => <Object?>[
        item,
        long_desc,
        upc,
        department,
        document,
        created_at,
        created_by,
        updated_at,
        updated_by,
        liq_ctl_plu,
        departmentByDepartment,
      ];

  Map<String, dynamic> toJson() => _$ItemToJson(this);
}

/// Note: Even though hashCode is defined (via Equatable), do not use this object as a key in a hash-based collection, as it is mutable.
@JsonSerializable()
// ignore: must_be_immutable
class ItemDocument extends Equatable {
  ItemDocument({
    required this.pricing,
    this.isWeighted = false,
    this.isOpenPrice = false,
    this.UOM,
    this.receiptDesc,
    this.modifierDesc,
    this.detailedDesc,
    this.onlineDesc,
    this.allowEbt = true,
    this.promptForPrice = false,
    this.pinToTop = false,
    this.negativeItem = false,
    this.isModifier = false,
    this.multiModLists = false,
    this.modMaxSel = 0,
    this.modMinSel = 0,
    this.takeOutSurcharge = false,
    this.modifiers = const <String, ModData>{},
    this.passDesc = false,
    this.overridePricing = false,
    this.defModPricing = const <String, int>{"S0L0C0": 0},
    this.count = -1,
    this.prep = 0,
    this.printSeparate = false,
    this.liquorList = const <String>[],
    this.showOnline = true,
    this.printInRed = false,
  });

  factory ItemDocument.fromJson(Map<String, dynamic> json) => _$ItemDocumentFromJson(json);

  ItemDocument copyWith({
    Map<String, int>? pricing,
    bool? isWeighted,
    bool? isOpenPrice,
    Option<int?>? UOM,
    Option<String?>? receiptDesc,
    Option<String?>? modifierDesc,
    Option<String?>? detailedDesc,
    Option<String?>? onlineDesc,
    bool? allowEbt,
    bool? promptForPrice,
    bool? pinToTop,
    bool? negativeItem,
    bool? isModifier,
    bool? multiModLists,
    int? modMaxSel,
    int? modMinSel,
    bool? takeOutSurcharge,
    Map<String, ModData>? modifiers,
    bool? passDesc,
    bool? overridePricing,
    Map<String, int>? defModPricing,
    int? count,
    int? prep,
    bool? printSeparate,
    List<String>? liquorList,
    bool? showOnline = true,
    bool? printInRed,
  }) {
    final ItemDocument shallowCopy = ItemDocument(
      pricing: pricing ?? this.pricing,
      isWeighted: isWeighted ?? this.isWeighted,
      isOpenPrice: isOpenPrice ?? this.isOpenPrice,
      UOM: (UOM ?? some(this.UOM)).toNullable(),
      receiptDesc: (receiptDesc ?? some(this.receiptDesc)).toNullable(),
      modifierDesc: (modifierDesc ?? some(this.modifierDesc)).toNullable(),
      detailedDesc: (detailedDesc ?? some(this.detailedDesc)).toNullable(),
      onlineDesc: (onlineDesc ?? some(this.onlineDesc)).toNullable(),
      allowEbt: allowEbt ?? this.allowEbt,
      promptForPrice: promptForPrice ?? this.promptForPrice,
      pinToTop: pinToTop ?? this.pinToTop,
      negativeItem: negativeItem ?? this.negativeItem,
      isModifier: isModifier ?? this.isModifier,
      multiModLists: multiModLists ?? this.multiModLists,
      modMaxSel: modMaxSel ?? this.modMaxSel,
      modMinSel: modMinSel ?? this.modMinSel,
      takeOutSurcharge: takeOutSurcharge ?? this.takeOutSurcharge,
      modifiers: modifiers ?? this.modifiers,
      passDesc: passDesc ?? this.passDesc,
      overridePricing: overridePricing ?? this.overridePricing,
      defModPricing: defModPricing ?? this.defModPricing,
      count: count ?? this.count,
      prep: prep ?? this.prep,
      printSeparate: printSeparate ?? this.printSeparate,
      liquorList: liquorList ?? this.liquorList,
      showOnline: showOnline ?? this.showOnline,
      printInRed: printInRed ?? this.printInRed,
    );

    final Map<String, dynamic> itemDocumentDict = json.decode(json.encode(shallowCopy)) as Map<String, dynamic>;
    return ItemDocument.fromJson(itemDocumentDict);
  }

  Map<String, int> pricing;
  bool isWeighted;
  bool isOpenPrice;
  int? UOM;
  String? receiptDesc;
  String? modifierDesc;
  String? detailedDesc;
  String? onlineDesc;
  bool allowEbt;
  bool promptForPrice;
  bool pinToTop;
  bool negativeItem;
  bool isModifier;
  bool multiModLists;
  int modMaxSel;
  int modMinSel;
  Map<String, ModData> modifiers = <String, ModData>{};
  bool passDesc;
  bool overridePricing;
  Map<String, int> defModPricing;
  int count;
  int prep;
  bool takeOutSurcharge = false;
  bool printSeparate = false;
  List<String> liquorList = <String>[];
  bool showOnline = true;
  // Should this item be marked red on the receipt?
  bool? printInRed = false;

  @override
  List<Object?> get props => <Object?>[
        pricing,
        isWeighted,
        isOpenPrice,
        UOM,
        receiptDesc,
        modifierDesc,
        detailedDesc,
        onlineDesc,
        allowEbt,
        promptForPrice,
        pinToTop,
        negativeItem,
        isModifier,
        multiModLists,
        modMaxSel,
        modMinSel,
        modifiers,
        passDesc,
        overridePricing,
        defModPricing,
        count,
        prep,
        takeOutSurcharge,
        printSeparate,
        liquorList,
        showOnline,
        printInRed,
      ];

  Map<String, dynamic> toJson() => _$ItemDocumentToJson(this);
}

/// Note: Even though hashCode is defined (via Equatable), do not use this object as a key in a hash-based collection, as it is mutable.
@JsonSerializable()
// ignore: must_be_immutable
class ModData extends Equatable {
  ModData({
    this.pricing = const <String, int>{},
    required this.idx,
    this.forceVisible = false,
  });

  factory ModData.fromJson(Map<String, dynamic> json) => _$ModDataFromJson(json);

  ModData copyWith({
    Map<String, int>? pricing,
    int? idx,
    bool? forceVisible,
  }) {
    final ModData shallowCopy = ModData(
      pricing: pricing ?? this.pricing,
      idx: idx ?? this.idx,
      forceVisible: forceVisible ?? this.forceVisible,
    );

    final Map<String, dynamic> modDataDict = json.decode(json.encode(shallowCopy)) as Map<String, dynamic>;
    return ModData.fromJson(modDataDict);
  }

  Map<String, int> pricing = <String, int>{};
  int idx;
  bool forceVisible = false;

  @override
  List<Object?> get props => <Object?>[
        pricing,
        idx,
        forceVisible,
      ];

  Map<String, dynamic> toJson() => _$ModDataToJson(this);
}
