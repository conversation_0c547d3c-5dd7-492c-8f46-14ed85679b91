// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Item _$ItemFromJson(Map<String, dynamic> json) => Item(
      item: json['item'] as String,
      long_desc: json['long_desc'] as String,
      upc: json['upc'] as String,
      department: json['department'] as String,
      document: ItemDocument.fromJson(json['document'] as Map<String, dynamic>),
      created_at: const DateTimeSerializer().fromJson(json['created_at']),
      created_by: json['created_by'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      updated_by: json['updated_by'] as String,
      liq_ctl_plu: json['liq_ctl_plu'] as int?,
      departmentByDepartment: json['departmentByDepartment'] == null
          ? null
          : Department.fromJson(
              json['departmentByDepartment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ItemToJson(Item instance) => <String, dynamic>{
      'item': instance.item,
      'long_desc': instance.long_desc,
      'upc': instance.upc,
      'department': instance.department,
      'document': instance.document,
      'created_at': const DateTimeSerializer().toJson(instance.created_at),
      'created_by': instance.created_by,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
      'updated_by': instance.updated_by,
      'liq_ctl_plu': instance.liq_ctl_plu,
      'departmentByDepartment': instance.departmentByDepartment,
    };

ItemDocument _$ItemDocumentFromJson(Map<String, dynamic> json) => ItemDocument(
      pricing: Map<String, int>.from(json['pricing'] as Map),
      isWeighted: json['isWeighted'] as bool? ?? false,
      isOpenPrice: json['isOpenPrice'] as bool? ?? false,
      UOM: json['UOM'] as int?,
      receiptDesc: json['receiptDesc'] as String?,
      modifierDesc: json['modifierDesc'] as String?,
      detailedDesc: json['detailedDesc'] as String?,
      onlineDesc: json['onlineDesc'] as String?,
      allowEbt: json['allowEbt'] as bool? ?? true,
      promptForPrice: json['promptForPrice'] as bool? ?? false,
      pinToTop: json['pinToTop'] as bool? ?? false,
      negativeItem: json['negativeItem'] as bool? ?? false,
      isModifier: json['isModifier'] as bool? ?? false,
      multiModLists: json['multiModLists'] as bool? ?? false,
      modMaxSel: json['modMaxSel'] as int? ?? 0,
      modMinSel: json['modMinSel'] as int? ?? 0,
      takeOutSurcharge: json['takeOutSurcharge'] as bool? ?? false,
      modifiers: (json['modifiers'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, ModData.fromJson(e as Map<String, dynamic>)),
          ) ??
          const <String, ModData>{},
      passDesc: json['passDesc'] as bool? ?? false,
      overridePricing: json['overridePricing'] as bool? ?? false,
      defModPricing: (json['defModPricing'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as int),
          ) ??
          const <String, int>{"S0L0C0": 0},
      count: json['count'] as int? ?? -1,
      prep: json['prep'] as int? ?? 0,
      printSeparate: json['printSeparate'] as bool? ?? false,
      liquorList: (json['liquorList'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
      showOnline: json['showOnline'] as bool? ?? true,
      printInRed: json['printInRed'] as bool? ?? false,
    );

Map<String, dynamic> _$ItemDocumentToJson(ItemDocument instance) =>
    <String, dynamic>{
      'pricing': instance.pricing,
      'isWeighted': instance.isWeighted,
      'isOpenPrice': instance.isOpenPrice,
      'UOM': instance.UOM,
      'receiptDesc': instance.receiptDesc,
      'modifierDesc': instance.modifierDesc,
      'detailedDesc': instance.detailedDesc,
      'onlineDesc': instance.onlineDesc,
      'allowEbt': instance.allowEbt,
      'promptForPrice': instance.promptForPrice,
      'pinToTop': instance.pinToTop,
      'negativeItem': instance.negativeItem,
      'isModifier': instance.isModifier,
      'multiModLists': instance.multiModLists,
      'modMaxSel': instance.modMaxSel,
      'modMinSel': instance.modMinSel,
      'modifiers': instance.modifiers,
      'passDesc': instance.passDesc,
      'overridePricing': instance.overridePricing,
      'defModPricing': instance.defModPricing,
      'count': instance.count,
      'prep': instance.prep,
      'takeOutSurcharge': instance.takeOutSurcharge,
      'printSeparate': instance.printSeparate,
      'liquorList': instance.liquorList,
      'showOnline': instance.showOnline,
      'printInRed': instance.printInRed,
    };

ModData _$ModDataFromJson(Map<String, dynamic> json) => ModData(
      pricing: (json['pricing'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as int),
          ) ??
          const <String, int>{},
      idx: json['idx'] as int,
      forceVisible: json['forceVisible'] as bool? ?? false,
    );

Map<String, dynamic> _$ModDataToJson(ModData instance) => <String, dynamic>{
      'pricing': instance.pricing,
      'idx': instance.idx,
      'forceVisible': instance.forceVisible,
    };
