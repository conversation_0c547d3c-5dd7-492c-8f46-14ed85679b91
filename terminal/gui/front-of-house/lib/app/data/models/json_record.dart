// ignore_for_file: deprecated_member_use, non_constant_identifier_names, sort_constructors_first, always_specify_types, depend_on_referenced_packages, hash_and_equals

import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'json_record.g.dart';

///
///
///
///
///
/// Model for the json_record table with record_key = systemDevice
@JsonSerializable()
@DateTimeSerializer()
class SystemDeviceJsonRecord {
  SystemDeviceJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  SystemDeviceJsonRecord.empty();
  factory SystemDeviceJsonRecord.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordFromJson(json);

  String record_key = "systemDevice";
  SystemDeviceJsonRecordDocument document = SystemDeviceJsonRecordDocument(
    prep: [],
    terminal: [],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is SystemDeviceJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordDocument {
  SystemDeviceJsonRecordDocument({
    required this.prep,
    required this.terminal,
  });
  SystemDeviceJsonRecordDocument.empty();
  factory SystemDeviceJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordDocumentFromJson(json);

  List<PrepDevice> prep = [];
  List<SystemDeviceJsonRecordTerminal> terminal = [];

  @override
  bool operator ==(Object other) => other is SystemDeviceJsonRecordDocument && other.prep == prep && other.terminal == terminal;

  @override
  int get hashCode => hashValues(
        prep,
        terminal,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordTerminal {
  SystemDeviceJsonRecordTerminal({
    required this.IP,
    required this.MAC,
    required this.desc,
    required this.idx,
    required this.scale,
    required this.rcptPrn,
    required this.poleDisp,
    required this.paymentDevice,
    required this.liqCtl,
    this.skipDevices = 0,
    this.section = 0,
    this.priceLevel = 0,
    this.quickSignIn = false,
    this.quickSignMinutes = 0,
    this.quickSignSection = 0,
    this.cashDrawers = const <TerminalDrawer>[],
    this.remotePrintIdx = 0,
    this.autoSignOutSeconds = 0,
  });
  SystemDeviceJsonRecordTerminal.empty();
  factory SystemDeviceJsonRecordTerminal.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordTerminalFromJson(json);

  String IP = "";
  String MAC = "";
  String desc = "";
  int idx = 0;
  int skipDevices = 0;
  int section = 0;
  int priceLevel = 0;
  bool quickSignIn = false;
  int quickSignMinutes = 0;
  int quickSignSection = 0;
  SystemDeviceJsonRecordScale scale = SystemDeviceJsonRecordScale.empty();
  SystemDeviceJsonRecordLiqCtl liqCtl = SystemDeviceJsonRecordLiqCtl.empty();
  SystemDeviceJsonRecordRcptPrn rcptPrn = SystemDeviceJsonRecordRcptPrn.empty();
  SystemDeviceJsonRecordPoleDisp poleDisp = SystemDeviceJsonRecordPoleDisp.empty();
  SystemDeviceJsonRecordPaymentDevice paymentDevice = SystemDeviceJsonRecordPaymentDevice.empty();
  List<TerminalDrawer> cashDrawers = <TerminalDrawer>[];
  int remotePrintIdx = 0;
  // the variable to store the total number of seconds to auto sign out the FoH.
  int autoSignOutSeconds = 0;

  @override
  bool operator ==(Object other) =>
      other is SystemDeviceJsonRecordTerminal &&
      other.IP == IP &&
      other.MAC == MAC &&
      other.desc == desc &&
      other.idx == idx &&
      other.scale == scale &&
      other.rcptPrn == rcptPrn &&
      other.liqCtl == liqCtl &&
      other.poleDisp == poleDisp &&
      other.paymentDevice == paymentDevice &&
      other.quickSignIn == quickSignIn &&
      other.quickSignMinutes == quickSignMinutes &&
      other.quickSignSection == quickSignSection &&
      other.remotePrintIdx == remotePrintIdx &&
      other.cashDrawers == cashDrawers &&
      other.skipDevices == skipDevices &&
      other.section == section &&
      other.priceLevel == priceLevel &&
      other.autoSignOutSeconds == autoSignOutSeconds;

  @override
  int get hashCode => hashValues(
        IP,
        MAC,
        desc,
        idx,
        scale,
        liqCtl,
        rcptPrn,
        poleDisp,
        paymentDevice,
        quickSignIn,
        quickSignMinutes,
        quickSignSection,
        remotePrintIdx,
        cashDrawers,
        skipDevices,
        section,
        priceLevel,
        autoSignOutSeconds,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordTerminalToJson(this);
}

@JsonSerializable()
class TerminalDrawer {
  TerminalDrawer({
    required this.idx,
    this.type = 0,
    this.port = 0,
  });
  TerminalDrawer.empty();
  factory TerminalDrawer.fromJson(Map<String, dynamic> json) => _$TerminalDrawerFromJson(json);

  int idx = 0;
  int type = 0;
  int port = 0;

  @override
  bool operator ==(Object other) => other is TerminalDrawer && other.idx == idx && other.type == type && other.port == port;

  @override
  int get hashCode => hashValues(
        idx,
        type,
        port,
      );

  Map<String, dynamic> toJson() => _$TerminalDrawerToJson(this);
}

@JsonSerializable()
class PrepDevice {
  PrepDevice({
    required this.idx,
    required this.desc,
    this.type = 0,
    this.IP = "",
    this.rcptTerm = 0,
    this.reRoute = 0,
  });
  PrepDevice.empty();
  factory PrepDevice.fromJson(Map<String, dynamic> json) => _$PrepDeviceFromJson(json);

  int idx = 0;
  String desc = "";
  int type = 0;
  String IP = "";
  int rcptTerm = 0;
  int reRoute = 0;

  @override
  bool operator ==(Object other) =>
      other is PrepDevice && other.idx == idx && other.desc == desc && other.IP == IP && other.rcptTerm == rcptTerm && other.reRoute == reRoute;

  @override
  int get hashCode => hashValues(
        idx,
        desc,
        IP,
        rcptTerm,
        reRoute,
      );

  Map<String, dynamic> toJson() => _$PrepDeviceToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordPaymentDevice {
  SystemDeviceJsonRecordPaymentDevice({
    required this.IP,
    required this.port,
    required this.epi,
    required this.appID,
    required this.appKey,
  });
  SystemDeviceJsonRecordPaymentDevice.empty();
  factory SystemDeviceJsonRecordPaymentDevice.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$SystemDeviceJsonRecordPaymentDeviceFromJson(json);

  String IP = "";
  String port = "";
  String epi = "";
  String appID = "";
  String appKey = "";

  @override
  bool operator ==(Object other) =>
      other is SystemDeviceJsonRecordPaymentDevice &&
      other.IP == IP &&
      other.port == port &&
      other.epi == epi &&
      other.appID == appID &&
      other.appKey == appKey;

  @override
  int get hashCode => hashValues(
        IP,
        port,
        epi,
        appID,
        appKey,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordPaymentDeviceToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordScale {
  SystemDeviceJsonRecordScale({
    required this.port,
    required this.type,
    required this.portType,
  });
  SystemDeviceJsonRecordScale.empty();
  factory SystemDeviceJsonRecordScale.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordScaleFromJson(json);

  int port = 0;
  int type = 0;
  int portType = 0;

  @override
  bool operator ==(Object other) => other is SystemDeviceJsonRecordScale && other.port == port && other.type == type && other.portType == portType;

  @override
  int get hashCode => hashValues(
        port,
        type,
        portType,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordScaleToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordRcptPrn {
  SystemDeviceJsonRecordRcptPrn({
    required this.baud,
    required this.port,
    required this.type,
    required this.portType,
  });
  SystemDeviceJsonRecordRcptPrn.empty();
  factory SystemDeviceJsonRecordRcptPrn.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordRcptPrnFromJson(json);

  int baud = 0;
  int port = 0;
  int type = 0;
  int portType = 0;

  @override
  bool operator ==(Object other) =>
      other is SystemDeviceJsonRecordRcptPrn && other.baud == baud && other.port == port && other.type == type && other.portType == portType;

  @override
  int get hashCode => hashValues(
        baud,
        port,
        type,
        portType,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordRcptPrnToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordLiqCtl {
  SystemDeviceJsonRecordLiqCtl({
    required this.port,
    required this.type,
    required this.dispenseMethod,
    required this.portType,
  });
  SystemDeviceJsonRecordLiqCtl.empty();
  factory SystemDeviceJsonRecordLiqCtl.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordLiqCtlFromJson(json);

  int port = 0;
  int type = 0;
  int dispenseMethod = 0;
  int portType = 1;

  @override
  bool operator ==(Object other) =>
      other is SystemDeviceJsonRecordLiqCtl &&
      other.port == port &&
      other.type == type &&
      other.dispenseMethod == dispenseMethod &&
      other.portType == portType;

  @override
  int get hashCode => hashValues(
        port,
        type,
        dispenseMethod,
        portType,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordLiqCtlToJson(this);
}

@JsonSerializable()
class SystemDeviceJsonRecordPoleDisp {
  SystemDeviceJsonRecordPoleDisp({
    required this.port,
    required this.type,
    required this.portType,
  });
  SystemDeviceJsonRecordPoleDisp.empty();
  factory SystemDeviceJsonRecordPoleDisp.fromJson(Map<String, dynamic> json) => _$SystemDeviceJsonRecordPoleDispFromJson(json);

  int port = 0;
  int type = 0;
  int portType = 0;

  @override
  bool operator ==(Object other) => other is SystemDeviceJsonRecordPoleDisp && other.port == port && other.portType == portType && other.type == type;

  @override
  int get hashCode => hashValues(
        port,
        type,
        portType,
      );

  Map<String, dynamic> toJson() => _$SystemDeviceJsonRecordPoleDispToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = systemSetting
@JsonSerializable()
@DateTimeSerializer()
class SystemSettingJsonRecord {
  SystemSettingJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  SystemSettingJsonRecord.empty();
  factory SystemSettingJsonRecord.fromJson(Map<String, dynamic> json) => _$SystemSettingJsonRecordFromJson(json);

  String record_key = "systemSetting";
  SystemSettingJsonRecordDocument document = SystemSettingJsonRecordDocument(
    ppdType: 0,
    tareUOM: false,
    jobCodes: <SystemSettingJsonRecordJobCode>[],
    storeURL: "",
    storeZip: "",
    bannerMsg: "",
    cashMedia: 0,
    dayDivide: "",
    storeCity: "",
    storeName: "",
    ppdRefDate: "",
    scaleTares: [],
    storePhone: "",
    storeState: "",
    cashRegMode: 1,
    noCCSlipUnder: 2500,
    storeAddress1: "",
    PEBHasChkDigit: 1,
    revenueCenters: [],
    waitForDrawerClosed: 1,
    priceLevels: <PriceLevel>[],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is SystemSettingJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$SystemSettingJsonRecordToJson(this);
}

@JsonSerializable()
class SystemSettingJsonRecordDocument {
  SystemSettingJsonRecordDocument({
    required this.ppdType,
    required this.tareUOM,
    required this.jobCodes,
    required this.storeURL,
    required this.storeZip,
    required this.bannerMsg,
    required this.cashMedia,
    required this.dayDivide,
    required this.storeCity,
    required this.storeName,
    required this.ppdRefDate,
    required this.scaleTares,
    required this.storePhone,
    required this.storeState,
    required this.cashRegMode,
    required this.noCCSlipUnder,
    required this.storeAddress1,
    required this.PEBHasChkDigit,
    required this.revenueCenters,
    required this.waitForDrawerClosed,
    required this.priceLevels,
  });
  SystemSettingJsonRecordDocument.empty();
  factory SystemSettingJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$SystemSettingJsonRecordDocumentFromJson(json);

  int ppdType = 0;
  bool tareUOM = false;
  List<SystemSettingJsonRecordJobCode> jobCodes = <SystemSettingJsonRecordJobCode>[];
  String storeURL = "";
  String storeZip = "";
  String bannerMsg = "";
  int cashMedia = 0;
  String dayDivide = "";
  String storeCity = "";
  String storeName = "";
  String ppdRefDate = "";
  List scaleTares = [];
  String storePhone = "";
  String storeState = "";
  int cashRegMode = 0;
  int noCCSlipUnder = 0;
  String storeAddress1 = "";
  int PEBHasChkDigit = 0;
  List revenueCenters = [];
  int waitForDrawerClosed = 0;
  List<PriceLevel> priceLevels = <PriceLevel>[];

  @override
  bool operator ==(Object other) =>
      other is SystemSettingJsonRecordDocument &&
      other.ppdType == ppdType &&
      other.tareUOM == tareUOM &&
      other.jobCodes == jobCodes &&
      other.storeURL == storeURL &&
      other.storeZip == storeZip &&
      other.bannerMsg == bannerMsg &&
      other.cashMedia == cashMedia &&
      other.dayDivide == dayDivide &&
      other.storeCity == storeCity &&
      other.storeName == storeName &&
      other.ppdRefDate == ppdRefDate &&
      other.scaleTares == scaleTares &&
      other.storePhone == storePhone &&
      other.storeState == storeState &&
      other.cashRegMode == cashRegMode &&
      other.noCCSlipUnder == noCCSlipUnder &&
      other.storeAddress1 == storeAddress1 &&
      other.PEBHasChkDigit == PEBHasChkDigit &&
      other.revenueCenters == revenueCenters &&
      other.waitForDrawerClosed == waitForDrawerClosed &&
      other.priceLevels == priceLevels;

  @override
  int get hashCode => hashValues(
        ppdType,
        tareUOM,
        jobCodes,
        storeURL,
        storeZip,
        bannerMsg,
        cashMedia,
        dayDivide,
        storeCity,
        storeName,
        ppdRefDate,
        scaleTares,
        storePhone,
        storeState,
        cashRegMode,
        noCCSlipUnder,
        storeAddress1,
        PEBHasChkDigit,
        revenueCenters,
        waitForDrawerClosed,
      );

  Map<String, dynamic> toJson() => _$SystemSettingJsonRecordDocumentToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = ecomSetting
@JsonSerializable()
@DateTimeSerializer()
class EcomSettingJsonRecord {
  EcomSettingJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  EcomSettingJsonRecord.empty();
  factory EcomSettingJsonRecord.fromJson(Map<String, dynamic> json) => _$EcomSettingJsonRecordFromJson(json);

  String record_key = "ecomSetting";
  DateTime? updated_at;
  EcomSettingJsonRecordDocument document = EcomSettingJsonRecordDocument(activeHours: ActiveHours(), delivery: Delivery());

  @override
  bool operator ==(Object other) =>
      other is EcomSettingJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        updated_at,
        document,
      );

  Map<String, dynamic> toJson() => _$EcomSettingJsonRecordToJson(this);
}

// Settings for online ordering
// Determines if online ordering is active
// and how it's handled when orders come in
@JsonSerializable()
class EcomSettingJsonRecordDocument {
  EcomSettingJsonRecordDocument({
    this.activeHours,
    this.ecomEnabled = false,
    this.printTerminal = 98,
    this.expediteSlip = false,
    this.customerReceipt = false,
    this.merchantReceipt = false,
    this.ecomPriceLevel = 0,
    this.delivery,
  }) {
    delivery ??= Delivery.empty();
  }
  EcomSettingJsonRecordDocument.empty();
  factory EcomSettingJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$EcomSettingJsonRecordDocumentFromJson(json);

  // What hours per day online ordering is active
  ActiveHours? activeHours;
  // Determines if online ordering is active
  bool ecomEnabled = false;
  // Determines which terminal handle online orders that come in
  int printTerminal = 98;
  // Determines if an expedite slip is print when online orders come in
  bool expediteSlip = false;
  // Determines if a customer receipt is printed when online orders come in
  bool customerReceipt = false;
  // Determines if a merchant receipt is printed when online orders come in
  bool merchantReceipt = false;
  // The price level to use for online orders
  int ecomPriceLevel = 0;
  // Delivery settings for online orders
  Delivery? delivery;

  @override
  bool operator ==(Object other) =>
      other is EcomSettingJsonRecordDocument &&
      other.activeHours == activeHours &&
      other.ecomEnabled == ecomEnabled &&
      other.printTerminal == printTerminal &&
      other.expediteSlip == expediteSlip &&
      other.customerReceipt == customerReceipt &&
      other.merchantReceipt == merchantReceipt &&
      other.ecomPriceLevel == ecomPriceLevel &&
      other.delivery == delivery;

  @override
  int get hashCode => hashValues(
        activeHours,
        ecomEnabled,
        printTerminal,
        expediteSlip,
        customerReceipt,
        merchantReceipt,
        ecomPriceLevel,
        delivery,
      );

  Map<String, dynamic> toJson() => _$EcomSettingJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class Delivery {
  Delivery({this.enabled = false, this.deliveryFee = 0});
  Delivery.empty();

  factory Delivery.fromJson(Map<String, dynamic> json) => _$DeliveryFromJson(json);

  bool enabled = false;
  int deliveryFee = 0;

  Map<String, dynamic> toJson() => _$DeliveryToJson(this);
}

@JsonSerializable()
class ActiveHours {
  ActiveHours({
    this.mon = const <ActiveWindow>[],
    this.tue = const <ActiveWindow>[],
    this.wed = const <ActiveWindow>[],
    this.thu = const <ActiveWindow>[],
    this.fri = const <ActiveWindow>[],
    this.sat = const <ActiveWindow>[],
    this.sun = const <ActiveWindow>[],
    this.xmas = const <ActiveWindow>[],
    this.xmasEve = const <ActiveWindow>[],
    this.nwYrs = const <ActiveWindow>[],
    this.nwYrsEve = const <ActiveWindow>[],
    this.thanks = const <ActiveWindow>[],
    this.ind = const <ActiveWindow>[],
    this.labor = const <ActiveWindow>[],
    this.memor = const <ActiveWindow>[],
    this.colum = const <ActiveWindow>[],
    this.vets = const <ActiveWindow>[],
    this.pres = const <ActiveWindow>[],
    this.mlk = const <ActiveWindow>[],
  });
  ActiveHours.empty();
  factory ActiveHours.fromJson(Map<String, dynamic> json) => _$ActiveHoursFromJson(json);

  // Monday
  List<ActiveWindow> mon = const <ActiveWindow>[];
  // Tuesday
  List<ActiveWindow> tue = const <ActiveWindow>[];
  // Wednesday
  List<ActiveWindow> wed = const <ActiveWindow>[];
  // Thursday
  List<ActiveWindow> thu = const <ActiveWindow>[];
  // Friday
  List<ActiveWindow> fri = const <ActiveWindow>[];
  // Saturday
  List<ActiveWindow> sat = const <ActiveWindow>[];
  // Sunday
  List<ActiveWindow> sun = const <ActiveWindow>[];
  // Christmas
  List<ActiveWindow> xmas = const <ActiveWindow>[];
  // Christmas Eve
  List<ActiveWindow> xmasEve = const <ActiveWindow>[];
  // New Years
  List<ActiveWindow> nwYrs = const <ActiveWindow>[];
  // New Years Eve
  List<ActiveWindow> nwYrsEve = const <ActiveWindow>[];
  // Thanksgiving
  List<ActiveWindow> thanks = const <ActiveWindow>[];
  // Independence Day
  List<ActiveWindow> ind = const <ActiveWindow>[];
  // Labor Day
  List<ActiveWindow> labor = const <ActiveWindow>[];
  // Memorial Day
  List<ActiveWindow> memor = const <ActiveWindow>[];
  // Columbus Day
  List<ActiveWindow> colum = const <ActiveWindow>[];
  // Veterans Day
  List<ActiveWindow> vets = const <ActiveWindow>[];
  // Presidents Day
  List<ActiveWindow> pres = const <ActiveWindow>[];
  // Martin Luther King Day
  List<ActiveWindow> mlk = const <ActiveWindow>[];

  @override
  bool operator ==(Object other) =>
      other is ActiveHours &&
      other.mon == mon &&
      other.tue == tue &&
      other.wed == wed &&
      other.thu == thu &&
      other.fri == fri &&
      other.sat == sat &&
      other.sun == sun &&
      other.xmas == xmas &&
      other.xmasEve == xmasEve &&
      other.nwYrs == nwYrs &&
      other.nwYrsEve == nwYrsEve &&
      other.thanks == thanks &&
      other.ind == ind &&
      other.labor == labor &&
      other.memor == memor &&
      other.colum == colum &&
      other.vets == vets &&
      other.pres == pres &&
      other.mlk == mlk;

  @override
  int get hashCode => hashValues(
        mon,
        tue,
        wed,
        thu,
        fri,
        sat,
        sun,
        xmas,
        xmasEve,
        nwYrs,
        nwYrsEve,
        thanks,
        ind,
        labor,
        memor,
        colum,
        vets,
        pres,
        mlk,
      );

  Map<String, dynamic> toJson() => _$ActiveHoursToJson(this);
}

@JsonSerializable()
class ActiveWindow {
  ActiveWindow({
    this.open = 0,
    this.close = 0,
  });
  ActiveWindow.empty();
  factory ActiveWindow.fromJson(Map<String, dynamic> json) => _$ActiveWindowFromJson(json);

  int open = 0;
  int close = 0;

  @override
  bool operator ==(Object other) => other is ActiveWindow && other.open == open && other.close == close;

  @override
  int get hashCode => hashValues(
        open,
        close,
      );

  Map<String, dynamic> toJson() => _$ActiveWindowToJson(this);
}

@JsonSerializable()
class PriceLevel {
  PriceLevel({
    required this.idx,
    required this.desc,
  });

  int idx = 0;
  String desc = "";

  PriceLevel.empty();

  @override
  bool operator ==(Object other) => other is PriceLevel && other.idx == idx && other.desc == desc;

  @override
  int get hashCode => hashValues(
        idx,
        desc,
      );

  factory PriceLevel.fromJson(Map<String, dynamic> json) => _$PriceLevelFromJson(json);

  Map<String, dynamic> toJson() => _$PriceLevelToJson(this);
}

@JsonSerializable()
class SystemSettingJsonRecordJobCode {
  SystemSettingJsonRecordJobCode({
    required this.index,
    required this.title,
    required this.revCtr,
    required this.isActive,
    required this.isDeliveryDriver,
    required this.laborGroup,
    required this.promptForSeat,
    required this.section,
    this.breaks = 0,
  });

  int index = 0;
  String title = "";
  int revCtr = 0;
  bool isActive = true;
  bool isDeliveryDriver = false;
  int laborGroup = 0;
  bool promptForSeat = true;
  int section = 0;
  int breaks = 0;

  SystemSettingJsonRecordJobCode.empty();

  @override
  bool operator ==(Object other) =>
      other is SystemSettingJsonRecordJobCode &&
      other.index == index &&
      other.title == title &&
      other.revCtr == revCtr &&
      other.isActive == isActive &&
      other.isDeliveryDriver == isDeliveryDriver &&
      other.laborGroup == laborGroup &&
      other.promptForSeat == promptForSeat &&
      other.section == section &&
      other.breaks == breaks;

  @override
  int get hashCode => hashValues(
        index,
        title,
        revCtr,
        isActive,
        isDeliveryDriver,
        laborGroup,
        promptForSeat,
        section,
        breaks,
      );

  factory SystemSettingJsonRecordJobCode.fromJson(Map<String, dynamic> json) => _$SystemSettingJsonRecordJobCodeFromJson(json);

  Map<String, dynamic> toJson() => _$SystemSettingJsonRecordJobCodeToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = hardware
@JsonSerializable()
@DateTimeSerializer()
class HardwareJsonRecord {
  HardwareJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  HardwareJsonRecord.empty();
  factory HardwareJsonRecord.fromJson(Map<String, dynamic> json) => _$HardwareJsonRecordFromJson(json);

  String record_key = "hardware";
  HardwareJsonRecordDocument document = HardwareJsonRecordDocument(
    onScreenKeyboard: true,
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is HardwareJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$HardwareJsonRecordToJson(this);
}

@JsonSerializable()
class HardwareJsonRecordDocument {
  HardwareJsonRecordDocument({
    required this.onScreenKeyboard,
  });
  factory HardwareJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$HardwareJsonRecordDocumentFromJson(json);

  bool onScreenKeyboard;

  Map<String, dynamic> toJson() => _$HardwareJsonRecordDocumentToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = merchant
@JsonSerializable()
@DateTimeSerializer()
class MerchantJsonRecord {
  MerchantJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  MerchantJsonRecord.empty();
  factory MerchantJsonRecord.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordFromJson(json);

  String record_key = "merchant";
  MerchantJsonRecordDocument document = MerchantJsonRecordDocument(
    ebt: false,
    tipping: false,
    giftcards: false,
    receiptHeader: [],
    dualPricing: false,
    dualPricingPercent: 0.0,
    refundReceiptSignatureLine: false,
    house: false,
    cashConfirmation: false,
    giftCardProgram: MerchantJsonRecordGiftCardProgram(),
    modules: MerchantJsonRecordModules.empty(),
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is MerchantJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$MerchantJsonRecordToJson(this);
}

@JsonSerializable()
class MerchantJsonRecordDocument {
  MerchantJsonRecordDocument({
    required this.ebt,
    required this.tipping,
    required this.giftcards,
    required this.receiptHeader,
    required this.dualPricing,
    required this.dualPricingPercent,
    this.dualPricingRoundAmount = 1,
    required this.refundReceiptSignatureLine,
    required this.house,
    this.preAuthAmount = 100,
    required this.cashConfirmation,
    this.showModPrice = true,
    this.takeOutSurchargeAmt = 0,
    this.printMerchantOnCash = true,
    this.printMerchantOnNonCash = true,
    this.printMerchantOnHouse = true,
    this.prepOnSaleChange = true,
    this.prepOnSignOut = true,
    this.prepOnCancel = true,
    this.giftCardProgram,
    this.modules,
    this.legacyGiftTender = false,
    this.legacyGiftName = "Legacy Gift",
    this.tipLinesOnCustCopy = true,
    this.orderTypePrintOpt = 0,
    this.customerCopyPrintOpt = 0,
    this.overlappingScheduleOpt = 0,
    this.paymentDeviceType = 2,
    this.multiCashDrawer = false,
    this.commentRowsOnReceipt = false,
    this.saleDescOnReceipt = false,
    this.paidOutPrintCustomer = false,
    this.tipConfirmLimit = -1,
    this.prepPrintSizing = 0,
    this.printPrepFooter = false,
    this.condensedAuthSlip = false,
    this.defaultRefundMedia = false,
    this.viewTablesOnSignOn = false,
    this.qrPayEnabled = false,
    this.markModifiersRed = false,
    this.markCommentsRed = false,
    this.markToGoRed = false,
    this.markOrderTypeRed = false,
    this.markPromisedTimeRed = false,
    this.gateway,
  });
  factory MerchantJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordDocumentFromJson(json);

  bool ebt = false;
  bool tipping = false;
  bool giftcards = false;
  List<String> receiptHeader = [];
  bool dualPricing = false;
  int preAuthAmount = 100;
  double dualPricingPercent = 0.0;
  int dualPricingRoundAmount = 1;
  bool refundReceiptSignatureLine = false;
  bool house = false;
  bool cashConfirmation = false;
  bool showModPrice = true;
  bool printMerchantOnCash = true;
  bool printMerchantOnNonCash = true;
  bool printMerchantOnHouse = true;
  bool prepOnSaleChange = true;
  bool prepOnSignOut = true;
  bool prepOnCancel = true;
  bool legacyGiftTender = false;
  String legacyGiftName = "Legacy Gift";
  MerchantJsonRecordModules? modules = MerchantJsonRecordModules.empty();
  MerchantJsonRecordGiftCardProgram? giftCardProgram = MerchantJsonRecordGiftCardProgram();
  int takeOutSurchargeAmt = 0;
  bool tipLinesOnCustCopy = true;
  int orderTypePrintOpt = 0;
  int customerCopyPrintOpt = 0;
  int overlappingScheduleOpt = 0;
  int paymentDeviceType = 2;
  bool multiCashDrawer = false;
  bool commentRowsOnReceipt = false;
  bool saleDescOnReceipt = false;
  bool paidOutPrintCustomer = false;
  int tipConfirmLimit = -1;
  int prepPrintSizing = 0;
  // If the prep printer should print a footer with server name on the bottom.
  // This was originally requested by the Flamingo.
  bool printPrepFooter = false;
  bool condensedAuthSlip = false;
  bool defaultRefundMedia = false;
  bool viewTablesOnSignOn = false;
  bool qrPayEnabled = false;
  bool markModifiersRed = false;
  bool markCommentsRed = false;
  bool markToGoRed = false;
  bool markOrderTypeRed = false;
  bool markPromisedTimeRed = false;
  Gateway? gateway;

  @override
  bool operator ==(Object other) =>
      other is MerchantJsonRecordDocument &&
      other.ebt == ebt &&
      other.tipping == tipping &&
      other.preAuthAmount == preAuthAmount &&
      other.giftcards == giftcards &&
      other.receiptHeader == receiptHeader &&
      other.dualPricing == dualPricing &&
      other.dualPricingPercent == dualPricingPercent &&
      other.refundReceiptSignatureLine == refundReceiptSignatureLine &&
      other.house == house &&
      other.cashConfirmation == cashConfirmation &&
      other.showModPrice == showModPrice &&
      other.printMerchantOnCash == printMerchantOnCash &&
      other.printMerchantOnNonCash == printMerchantOnNonCash &&
      other.printMerchantOnHouse == printMerchantOnHouse &&
      other.prepOnSaleChange == prepOnSaleChange &&
      other.prepOnSignOut == prepOnSignOut &&
      other.prepOnCancel == prepOnCancel &&
      other.modules == modules &&
      other.giftCardProgram == giftCardProgram &&
      other.takeOutSurchargeAmt == takeOutSurchargeAmt &&
      other.legacyGiftTender == legacyGiftTender &&
      other.legacyGiftName == legacyGiftName &&
      other.tipLinesOnCustCopy == tipLinesOnCustCopy &&
      other.orderTypePrintOpt == orderTypePrintOpt &&
      other.customerCopyPrintOpt == customerCopyPrintOpt &&
      other.overlappingScheduleOpt == overlappingScheduleOpt &&
      other.paymentDeviceType == paymentDeviceType &&
      other.multiCashDrawer == multiCashDrawer &&
      other.commentRowsOnReceipt == commentRowsOnReceipt &&
      other.saleDescOnReceipt == saleDescOnReceipt &&
      other.paidOutPrintCustomer == paidOutPrintCustomer &&
      other.tipConfirmLimit == tipConfirmLimit &&
      other.prepPrintSizing == prepPrintSizing &&
      other.printPrepFooter == printPrepFooter &&
      other.condensedAuthSlip == condensedAuthSlip &&
      other.defaultRefundMedia == defaultRefundMedia &&
      other.viewTablesOnSignOn == viewTablesOnSignOn &&
      other.qrPayEnabled == qrPayEnabled &&
      other.markModifiersRed == markModifiersRed &&
      other.markCommentsRed == markCommentsRed &&
      other.markToGoRed == markToGoRed &&
      other.markOrderTypeRed == markOrderTypeRed &&
      other.markPromisedTimeRed == markPromisedTimeRed &&
      other.gateway == gateway;

  @override
  int get hashCode => hashList(
        [
          ebt,
          tipping,
          giftcards,
          receiptHeader,
          takeOutSurchargeAmt,
          dualPricing,
          dualPricingPercent,
          refundReceiptSignatureLine,
          house,
          cashConfirmation,
          showModPrice,
          printMerchantOnCash,
          prepOnSaleChange,
          prepOnSignOut,
          modules,
          giftCardProgram,
          legacyGiftTender,
          tipLinesOnCustCopy,
          orderTypePrintOpt,
          customerCopyPrintOpt,
          overlappingScheduleOpt,
          paymentDeviceType,
          multiCashDrawer,
          commentRowsOnReceipt,
          saleDescOnReceipt,
          paidOutPrintCustomer,
          tipConfirmLimit,
          prepPrintSizing,
          printPrepFooter,
          condensedAuthSlip,
          defaultRefundMedia,
          viewTablesOnSignOn,
          qrPayEnabled,
          markModifiersRed,
          markCommentsRed,
          markToGoRed,
          markOrderTypeRed,
          markPromisedTimeRed,
          gateway,
        ],
      );
  Map<String, dynamic> toJson() => _$MerchantJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class Gateway {
  Gateway({
    this.tokenizationKey = "",
    this.directKey = "",
  });

  String tokenizationKey = "";
  String directKey = "";

  Gateway.empty();

  @override
  bool operator ==(Object other) => other is Gateway && other.tokenizationKey == tokenizationKey && other.directKey == directKey;

  @override
  int get hashCode => hashValues(
        tokenizationKey,
        directKey,
      );

  factory Gateway.fromJson(Map<String, dynamic> json) => _$GatewayFromJson(json);

  Map<String, dynamic> toJson() => _$GatewayToJson(this);
}

@JsonSerializable()
class MerchantJsonRecordModules {
  MerchantJsonRecordModules({
    required this.timeClock,
    required this.buttonManagement,
  });

  MerchantJsonRecordTimeClock timeClock = MerchantJsonRecordTimeClock(isActive: false);
  MerchantJsonRecordButtons buttonManagement = MerchantJsonRecordButtons(isActive: false);

  MerchantJsonRecordModules.empty();

  @override
  bool operator ==(Object other) => other is MerchantJsonRecordModules && other.timeClock == timeClock && other.buttonManagement == buttonManagement;

  @override
  int get hashCode => hashValues(
        timeClock,
        buttonManagement,
      );

  factory MerchantJsonRecordModules.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordModulesFromJson(json);

  Map<String, dynamic> toJson() => _$MerchantJsonRecordModulesToJson(this);
}

@JsonSerializable()
class MerchantJsonRecordGiftCardProgram {
  MerchantJsonRecordGiftCardProgram({
    this.clientId,
    this.locationId,
    this.terminalId,
    this.initiatorId,
    this.initiatorPass,
    this.integrationAuth,
    this.integrationPass,
  });

  String? clientId;
  String? locationId;
  String? terminalId;
  String? initiatorId;
  String? initiatorPass;
  String? integrationAuth;
  String? integrationPass;

  @override
  bool operator ==(Object other) =>
      other is MerchantJsonRecordGiftCardProgram &&
      other.clientId == clientId &&
      other.locationId == locationId &&
      other.terminalId == terminalId &&
      other.initiatorId == initiatorId &&
      other.initiatorPass == initiatorPass &&
      other.integrationAuth == integrationAuth &&
      other.integrationPass == integrationPass;

  @override
  int get hashCode => hashValues(
        clientId,
        locationId,
        terminalId,
        initiatorId,
        initiatorPass,
        integrationAuth,
        integrationPass,
      );

  factory MerchantJsonRecordGiftCardProgram.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordGiftCardProgramFromJson(json);

  Map<String, dynamic> toJson() => _$MerchantJsonRecordGiftCardProgramToJson(this);
}

@JsonSerializable()
class MerchantJsonRecordTimeClock {
  MerchantJsonRecordTimeClock({
    required this.isActive,
  });

  bool isActive = true;

  @override
  bool operator ==(Object other) => other is MerchantJsonRecordTimeClock && other.isActive == isActive;

  @override
  int get hashCode => isActive.hashCode;

  factory MerchantJsonRecordTimeClock.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordTimeClockFromJson(json);

  Map<String, dynamic> toJson() => _$MerchantJsonRecordTimeClockToJson(this);
}

@JsonSerializable()
class MerchantJsonRecordButtons {
  MerchantJsonRecordButtons({
    required this.isActive,
  });

  bool isActive = true;

  @override
  bool operator ==(Object other) => other is MerchantJsonRecordButtons && other.isActive == isActive;

  @override
  int get hashCode => isActive.hashCode;

  factory MerchantJsonRecordButtons.fromJson(Map<String, dynamic> json) => _$MerchantJsonRecordButtonsFromJson(json);

  Map<String, dynamic> toJson() => _$MerchantJsonRecordButtonsToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = registerMenus
@JsonSerializable()
@DateTimeSerializer()
class RegisterMenusJsonRecord {
  RegisterMenusJsonRecord({
    required this.record_key,
    required this.updated_at,
    this.document,
  });
  factory RegisterMenusJsonRecord.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordFromJson(json);

  RegisterMenusJsonRecord.empty();

  String record_key = "registerMenus";
  RegisterMenusJsonRecordDocument? document = RegisterMenusJsonRecordDocument(
    toolbar: RegisterMenusJsonRecordToolbar.empty(),
    user: RegisterMenusJsonRecordUser.empty(),
    admin: RegisterMenusJsonRecordAdmin.empty(),
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is RegisterMenusJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordToJson(this);
}

@JsonSerializable()
class RegisterMenusJsonRecordDocument {
  RegisterMenusJsonRecordDocument({
    required this.toolbar,
    required this.user,
    required this.admin,
  });
  factory RegisterMenusJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordDocumentFromJson(json);

  RegisterMenusJsonRecordToolbar toolbar = RegisterMenusJsonRecordToolbar.empty();
  RegisterMenusJsonRecordUser user = RegisterMenusJsonRecordUser.empty();
  RegisterMenusJsonRecordAdmin admin = RegisterMenusJsonRecordAdmin.empty();

  @override
  int get hashCode => hashValues(
        toolbar,
        user,
        admin,
      );

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class RegisterMenusJsonRecordToolbar {
  RegisterMenusJsonRecordToolbar({
    required this.menu,
    required this.custom_colors,
  });
  factory RegisterMenusJsonRecordToolbar.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordToolbarFromJson(json);

  RegisterMenusJsonRecordToolbar.empty();

  List<RegisterMenusJsonRecordButton> menu = [];
  bool custom_colors = true;

  @override
  bool operator ==(Object other) => other is RegisterMenusJsonRecordToolbar && other.menu == menu;
  @override
  int get hashCode => hashValues(
        menu,
        custom_colors,
      );

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordToolbarToJson(this);
}

@JsonSerializable()
class RegisterMenusJsonRecordUser {
  RegisterMenusJsonRecordUser({
    required this.menu,
    required this.is_active,
    required this.custom_colors,
  });
  factory RegisterMenusJsonRecordUser.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordUserFromJson(json);

  RegisterMenusJsonRecordUser.empty();

  List<RegisterMenusJsonRecordButton> menu = [];
  bool is_active = true;
  bool custom_colors = true;

  @override
  bool operator ==(Object other) =>
      other is RegisterMenusJsonRecordUser && other.menu == menu && other.is_active == is_active && other.custom_colors == custom_colors;
  @override
  int get hashCode => hashValues(
        menu,
        is_active,
        custom_colors,
      );

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordUserToJson(this);
}

@JsonSerializable()
class RegisterMenusJsonRecordAdmin {
  RegisterMenusJsonRecordAdmin({
    required this.menu,
    required this.is_active,
    required this.custom_colors,
  });
  factory RegisterMenusJsonRecordAdmin.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordAdminFromJson(json);

  RegisterMenusJsonRecordAdmin.empty();

  List<RegisterMenusJsonRecordButton> menu = [];
  bool is_active = true;
  bool custom_colors = true;

  @override
  bool operator ==(Object other) =>
      other is RegisterMenusJsonRecordAdmin && other.menu == menu && other.is_active == is_active && other.custom_colors == custom_colors;
  @override
  int get hashCode => hashValues(
        menu,
        is_active,
        custom_colors,
      );

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordAdminToJson(this);
}

@JsonSerializable()
class RegisterMenusJsonRecordButton {
  RegisterMenusJsonRecordButton({
    required this.index,
    required this.text,
    required this.action,
    required this.section,
    this.fGnd,
    this.bGnd,
    this.config,
  });
  factory RegisterMenusJsonRecordButton.fromJson(Map<String, dynamic> json) => _$RegisterMenusJsonRecordButtonFromJson(json);

  RegisterMenusJsonRecordButton.empty();

  int index = 0;
  String text = "";
  String action = "";
  String section = "";
  int? fGnd;
  int? bGnd;
  Map<String, dynamic>? config;

  @override
  bool operator ==(Object other) =>
      other is RegisterMenusJsonRecordButton &&
      other.index == index &&
      other.text == text &&
      other.action == action &&
      other.section == section &&
      other.fGnd == fGnd &&
      other.bGnd == bGnd &&
      other.config == config;

  @override
  int get hashCode => hashValues(index, text, action, section, fGnd, bGnd, config);

  Map<String, dynamic> toJson() => _$RegisterMenusJsonRecordButtonToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = merchant
@JsonSerializable()
@DateTimeSerializer()
class RoomsJsonRecord {
  RoomsJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  factory RoomsJsonRecord.fromJson(Map<String, dynamic> json) => _$RoomsJsonRecordFromJson(json);

  RoomsJsonRecord.empty();

  String record_key = "tableLayout";
  RoomsJsonRecordDocument document = RoomsJsonRecordDocument(
    text: <RoomsJsonRecordText>[],
    rooms: <RoomsJsonRecordRoom>[],
    tables: <RoomsJsonRecordTable>[],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is RoomsJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$RoomsJsonRecordToJson(this);
}

@JsonSerializable()
class RoomsJsonRecordDocument {
  RoomsJsonRecordDocument({
    required this.text,
    required this.rooms,
    required this.tables,
  });
  factory RoomsJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$RoomsJsonRecordDocumentFromJson(json);

  List<RoomsJsonRecordText> text = <RoomsJsonRecordText>[];
  List<RoomsJsonRecordRoom> rooms = <RoomsJsonRecordRoom>[];
  List<RoomsJsonRecordTable> tables = <RoomsJsonRecordTable>[];

  Map<String, dynamic> toJson() => _$RoomsJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class RoomsJsonRecordText {
  RoomsJsonRecordText({
    required this.X,
    required this.Y,
    required this.idx,
    required this.bgnd,
    required this.desc,
    required this.fgnd,
    required this.font,
    required this.width,
    required this.height,
    required this.orient,
    required this.roomIdx,
    this.rotation,
  });
  factory RoomsJsonRecordText.fromJson(Map<String, dynamic> json) => _$RoomsJsonRecordTextFromJson(json);

  double X;
  double Y;
  int idx;
  int bgnd;
  String desc;
  int fgnd;
  String font;
  double width;
  double height;
  int orient;
  int roomIdx;
  double? rotation;

  Map<String, dynamic> toJson() => _$RoomsJsonRecordTextToJson(this);
}

@JsonSerializable()
class RoomsJsonRecordRoom {
  RoomsJsonRecordRoom({
    required this.desc,
    required this.prcLvl,
    required this.idx,
  });
  factory RoomsJsonRecordRoom.fromJson(Map<String, dynamic> json) => _$RoomsJsonRecordRoomFromJson(json);

  String desc;
  int prcLvl;
  int idx;

  Map<String, dynamic> toJson() => _$RoomsJsonRecordRoomToJson(this);
}

@JsonSerializable()
class RoomsJsonRecordTable {
  RoomsJsonRecordTable({
    required this.desc,
    required this.X,
    required this.Y,
    required this.width,
    required this.height,
    required this.rotation,
    required this.idx,
    required this.roomIdx,
    required this.shape,
    required this.seatCnt,
    required this.sectIdx,
  });
  factory RoomsJsonRecordTable.fromJson(Map<String, dynamic> json) => _$RoomsJsonRecordTableFromJson(json);

  String desc;
  double X;
  double Y;
  double width;
  double height;
  double rotation;
  int idx;
  int roomIdx;
  int shape;
  int seatCnt;
  int sectIdx;

  Map<String, dynamic> toJson() => _$RoomsJsonRecordTableToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = hardware
@JsonSerializable()
@DateTimeSerializer()
class SectionsJsonRecord {
  SectionsJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  SectionsJsonRecord.empty();
  factory SectionsJsonRecord.fromJson(Map<String, dynamic> json) => _$SectionsJsonRecordFromJson(json);

  String record_key = "sections";
  SectionsJsonRecordDocument document = SectionsJsonRecordDocument(
    sections: <Section>[],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is SectionsJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$SectionsJsonRecordToJson(this);
}

@JsonSerializable()
class SectionsJsonRecordDocument {
  SectionsJsonRecordDocument({
    required this.sections,
  });
  factory SectionsJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$SectionsJsonRecordDocumentFromJson(json);

  List<Section> sections = <Section>[];

  @override
  int get hashCode => hashValues(
        sections,
        null,
      );

  Map<String, dynamic> toJson() => _$SectionsJsonRecordDocumentToJson(this);
}

//
///
///
///
///
///
@JsonSerializable()
@DateTimeSerializer()
class PrinterScheduleJsonRecord {
  PrinterScheduleJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  PrinterScheduleJsonRecord.empty();
  factory PrinterScheduleJsonRecord.fromJson(Map<String, dynamic> json) => _$PrinterScheduleJsonRecordFromJson(json);

  String record_key = "printerSchedule";
  PrinterScheduleDocument document = PrinterScheduleDocument(
    discs: <PrinterSchedule>[],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is PrinterScheduleJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$PrinterScheduleJsonRecordToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class PrinterScheduleDocument {
  PrinterScheduleDocument({
    required this.discs,
  });
  PrinterScheduleDocument.empty();
  factory PrinterScheduleDocument.fromJson(Map<String, dynamic> json) => _$PrinterScheduleDocumentFromJson(json);

  List<PrinterSchedule> discs = <PrinterSchedule>[];

  @override
  bool operator ==(Object other) => other is PrinterScheduleDocument && other.discs == discs;

  @override
  int get hashCode => hashValues(
        discs,
        null,
      );

  Map<String, dynamic> toJson() => _$PrinterScheduleDocumentToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class PrinterSchedule {
  PrinterSchedule({
    required this.idx,
    required this.day,
    this.startTime = 0,
    this.endTime = 0,
    this.fromPrinter = -1,
    this.toPrinter = -1,
    this.enabled = false,
  });
  PrinterSchedule.empty();

  int idx = 0;
  String day = "";
  int startTime = 0;
  int endTime = 0;
  int fromPrinter = -1;
  int toPrinter = -1;
  bool enabled = false;

  @override
  bool operator ==(Object other) =>
      other is PrinterSchedule &&
      other.idx == idx &&
      other.day == day &&
      other.startTime == startTime &&
      other.endTime == endTime &&
      other.fromPrinter == fromPrinter &&
      other.toPrinter == toPrinter &&
      other.enabled == enabled;

  @override
  int get hashCode => hashValues(idx, day, startTime, endTime, enabled);

  factory PrinterSchedule.fromJson(Map<String, dynamic> json) => _$PrinterScheduleFromJson(json);

  Map<String, dynamic> toJson() => _$PrinterScheduleToJson(this);
}

@JsonSerializable()
class Section {
  Section({
    required this.desc,
    required this.idx,
    this.forceCustCnt = false,
    this.autoAddSeats = false,
    this.forceTblNum = false,
    this.forceSaleDesc = false,
    this.redAfterMins,
    this.blinkAfterMins,
    this.priceLevel,
    this.trackBySeat = true,
    this.saleName = "Sale",
    this.openSalesList = true,
    // required this.newSaleSeat,
    // required this.newSaleTogo,
    this.forceOrdType = false,
    this.forceCustIfTogo = false,
    this.forceCustIfDelivery = false,
    this.custCountOption = 0,
    this.defOrdType = 0,
    // required this.zeroCustCnt,
    this.minGratCustCnt = 1,
    this.calcGratWDiscs = true,
    this.gratAmt = 0,
    this.salesByEmployee = false,
    this.clockOutWithOpenSales = true,
    this.allowTransferSales = true,
    this.printServerReport = false,
    this.showPromisedTimeAndType = true,
    this.printerSchedule = const <PrinterSchedule>[],
    // required this.priceLevel,
    // required this.skipPrepDevs,
    // this.sugTip1,
    // this.sugTip2,
    // this.sugTip3,
  }) {
    if (printerSchedule.isEmpty) {
      printerSchedule = <PrinterSchedule>[
        PrinterSchedule(day: "Monday", idx: 0),
        PrinterSchedule(day: "Tuesday", idx: 1),
        PrinterSchedule(day: "Wednesday", idx: 2),
        PrinterSchedule(day: "Thursday", idx: 3),
        PrinterSchedule(day: "Friday", idx: 4),
        PrinterSchedule(day: "Saturday", idx: 5),
        PrinterSchedule(day: "Sunday", idx: 6),
      ];
    }
  }
  factory Section.fromJson(Map<String, dynamic> json) => _$SectionFromJson(json);

  String desc;
  int idx;
  bool forceCustCnt = false;
  bool autoAddSeats = false;
  bool forceTblNum = false;
  bool forceSaleDesc = false;
  double? redAfterMins;
  double? blinkAfterMins;
  int? priceLevel = 0;
  bool trackBySeat = true;
  String? saleName = "Sale";
  bool openSalesList = true;
  // bool newSaleSeat;
  // bool newSaleTogo;
  bool forceOrdType = false;
  bool forceCustIfTogo = false;
  bool forceCustIfDelivery = false;
  int custCountOption = 0;
  int defOrdType = 0;
  // bool zeroCustCnt;
  int? minGratCustCnt = 1;
  bool calcGratWDiscs = true;
  double gratAmt = 0;
  bool salesByEmployee = false;
  bool clockOutWithOpenSales = true;
  bool allowTransferSales = true;
  bool printServerReport = false;
  bool showPromisedTimeAndType = true;
  List<PrinterSchedule> printerSchedule = <PrinterSchedule>[];
  // bool priceLevel;
  // bool skipPrepDevs;
  // double? sugTip1;
  // double? sugTip2;
  // double? sugTip3;

  Map<String, dynamic> toJson() => _$SectionToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class SalesTaxJsonRecord {
  SalesTaxJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  SalesTaxJsonRecord.empty();
  factory SalesTaxJsonRecord.fromJson(Map<String, dynamic> json) => _$SalesTaxJsonRecordFromJson(json);

  String record_key = "merchant";
  SalesTaxDocument document = SalesTaxDocument(taxes: <Tax>[]);

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is SalesTaxJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$SalesTaxJsonRecordToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class SalesTaxDocument {
  SalesTaxDocument({
    required this.taxes,
  });
  SalesTaxDocument.empty();
  factory SalesTaxDocument.fromJson(Map<String, dynamic> json) => _$SalesTaxDocumentFromJson(json);

  List<Tax> taxes = <Tax>[];

  @override
  bool operator ==(Object other) => other is SalesTaxDocument && other.taxes == taxes;

  @override
  int get hashCode => hashValues(
        taxes,
        null,
      );

  Map<String, dynamic> toJson() => _$SalesTaxDocumentToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class Tax {
  Tax({
    this.idx,
    this.desc,
    this.isVAT = false,
    this.taxType = 0,
    this.rowsUsed = 0,
    this.roundType = 0,
    this.noTaxUnder = 0,
    this.taxPercent = 0,
    this.addOddPenny = false,
    this.forgiveTakeout = false,
  });
  Tax.empty();

  int? idx;
  String? desc;
  bool isVAT = false;
  int taxType = 0;
  int? rowsUsed = 0;
  int? roundType = 0;
  int? noTaxUnder = 0;
  int? taxPercent = 0;
  bool? addOddPenny = false;
  bool? forgiveTakeout = false;

  @override
  bool operator ==(Object other) =>
      other is Tax &&
      other.idx == idx &&
      other.desc == desc &&
      other.isVAT == isVAT &&
      other.taxType == taxType &&
      other.rowsUsed == rowsUsed &&
      other.roundType == roundType &&
      other.noTaxUnder == noTaxUnder &&
      other.taxPercent == taxPercent &&
      other.addOddPenny == addOddPenny &&
      other.forgiveTakeout == forgiveTakeout;

  @override
  int get hashCode => hashValues(
        idx,
        desc,
        isVAT,
        taxType,
        rowsUsed,
        roundType,
        noTaxUnder,
        taxPercent,
        addOddPenny,
        forgiveTakeout,
      );

  factory Tax.fromJson(Map<String, dynamic> json) => _$TaxFromJson(json);

  Map<String, dynamic> toJson() => _$TaxToJson(this);
}

///
///
///
///
///
///
@JsonSerializable()
@DateTimeSerializer()
class PriceScheduleJsonRecord {
  PriceScheduleJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  PriceScheduleJsonRecord.empty();
  factory PriceScheduleJsonRecord.fromJson(Map<String, dynamic> json) => _$PriceScheduleJsonRecordFromJson(json);

  String record_key = "priceSchedule";
  PriceScheduleDocument document = PriceScheduleDocument(
    discs: <PriceSchedule>[],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is PriceScheduleJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$PriceScheduleJsonRecordToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class PriceScheduleDocument {
  PriceScheduleDocument({
    required this.discs,
  });
  PriceScheduleDocument.empty();
  factory PriceScheduleDocument.fromJson(Map<String, dynamic> json) => _$PriceScheduleDocumentFromJson(json);

  List<PriceSchedule> discs = <PriceSchedule>[];

  @override
  bool operator ==(Object other) => other is PriceScheduleDocument && other.discs == discs;

  @override
  int get hashCode => hashValues(
        discs,
        null,
      );

  Map<String, dynamic> toJson() => _$PriceScheduleDocumentToJson(this);
}

@JsonSerializable()
@DateTimeSerializer()
class PriceSchedule {
  PriceSchedule({
    required this.idx,
    this.desc = "",
    this.days = 0,
    this.startTime = 0,
    this.endTime = 0,
    this.isSeasonal = false,
    this.seasStMonth = 0,
    this.seasStDay = 0,
    this.seasEndMonth = 0,
    this.seasEndDay = 0,
  });
  PriceSchedule.empty();

  int idx = 0;
  String desc = "";
  int days = 0;
  int startTime = 0;
  int endTime = 0;
  bool isSeasonal = false;
  int seasStMonth = 0;
  int seasStDay = 0;
  int seasEndMonth = 0;
  int seasEndDay = 0;

  @override
  bool operator ==(Object other) =>
      other is PriceSchedule &&
      other.idx == idx &&
      other.desc == desc &&
      other.days == days &&
      other.startTime == startTime &&
      other.endTime == endTime &&
      other.isSeasonal == isSeasonal &&
      other.seasStMonth == seasStMonth &&
      other.seasStDay == seasStDay &&
      other.seasEndMonth == seasEndMonth &&
      other.seasEndDay == seasEndDay;

  @override
  int get hashCode => hashValues(
        idx,
        desc,
        days,
        startTime,
        endTime,
        isSeasonal,
        seasStMonth,
        seasStDay,
        seasEndMonth,
        seasEndDay,
      );

  factory PriceSchedule.fromJson(Map<String, dynamic> json) => _$PriceScheduleFromJson(json);

  Map<String, dynamic> toJson() => _$PriceScheduleToJson(this);
}

///
///
///
///
///
/// Model for the json_record table with record_key = systemDevice
@JsonSerializable()
@DateTimeSerializer()
class BreaksJsonRecord {
  BreaksJsonRecord({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });
  BreaksJsonRecord.empty();
  factory BreaksJsonRecord.fromJson(Map<String, dynamic> json) => _$BreaksJsonRecordFromJson(json);

  String record_key = "breaks";
  BreaksJsonRecordDocument document = BreaksJsonRecordDocument(
    breaks: [],
  );

  DateTime? updated_at;

  @override
  bool operator ==(Object other) =>
      other is BreaksJsonRecord && other.record_key == record_key && other.updated_at == updated_at && other.document == document;

  @override
  int get hashCode => hashValues(
        record_key,
        document,
        updated_at,
      );

  Map<String, dynamic> toJson() => _$BreaksJsonRecordToJson(this);
}

@JsonSerializable()
class BreaksJsonRecordDocument {
  BreaksJsonRecordDocument({
    required this.breaks,
  });
  BreaksJsonRecordDocument.empty();
  factory BreaksJsonRecordDocument.fromJson(Map<String, dynamic> json) => _$BreaksJsonRecordDocumentFromJson(json);

  List<Break> breaks = [];

  @override
  bool operator ==(Object other) => other is BreaksJsonRecordDocument && other.breaks == breaks;

  Map<String, dynamic> toJson() => _$BreaksJsonRecordDocumentToJson(this);
}

@JsonSerializable()
class Break {
  Break({
    required this.idx,
    this.desc = "",
    this.isPaid = 0,
    this.breakMins = 0,
    this.minsToQualify = 0,
  });

  int idx;
  String desc = "";
  int isPaid = 0;
  int breakMins = 0;
  int minsToQualify = 0;

  @override
  bool operator ==(Object other) =>
      other is Break &&
      other.idx == idx &&
      other.desc == desc &&
      other.isPaid == isPaid &&
      other.breakMins == breakMins &&
      other.minsToQualify == minsToQualify;

  @override
  int get hashCode => hashValues(
        idx,
        desc,
        isPaid,
        breakMins,
        minsToQualify,
      );

  factory Break.fromJson(Map<String, dynamic> json) => _$BreakFromJson(json);

  Map<String, dynamic> toJson() => _$BreakToJson(this);
}

@JsonSerializable()
class JsonRecordReports {
  JsonRecordReports({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });

  JsonRecordReports.empty();

  String record_key = 'Reports';
  DateTime updated_at = DateTime.now();
  JsonRecordReportDocument document = JsonRecordReportDocument(
    reportEmailAddresses: [],
    reportList: [],
  );

  @override
  bool operator ==(Object other) =>
      other is JsonRecordReports && other.record_key == record_key && other.updated_at == updated_at && other.document == document;
  @override
  int get hashCode => hashValues(
        record_key,
        updated_at,
        document,
      );
  factory JsonRecordReports.fromJson(Map<String, dynamic> json) => _$JsonRecordReportsFromJson(json);
  Map<String, dynamic> toJson() => _$JsonRecordReportsToJson(this);
}

@JsonSerializable()
class JsonRecordReportDocument {
  JsonRecordReportDocument({
    required this.reportList,
    required this.reportEmailAddresses,
    this.wageSignatureLines = true,
    this.wageDecimalHours = false,
  });

  JsonRecordReportDocument.empty();

  List<String> reportList = <String>[];
  List<String> reportEmailAddresses = <String>[];
  bool wageSignatureLines = true;
  bool wageDecimalHours = false;

  @override
  bool operator ==(Object other) =>
      other is JsonRecordReportDocument &&
      other.reportList == reportList &&
      other.reportEmailAddresses == reportEmailAddresses &&
      other.wageSignatureLines == wageSignatureLines &&
      other.wageDecimalHours == wageDecimalHours;
  @override
  int get hashCode => hashValues(
        reportList,
        reportEmailAddresses,
        wageSignatureLines,
        wageDecimalHours,
      );
  factory JsonRecordReportDocument.fromJson(Map<String, dynamic> json) => _$JsonRecordReportDocumentFromJson(json);
  Map<String, dynamic> toJson() => _$JsonRecordReportDocumentToJson(this);
}

@JsonSerializable()
class JsonRecordCashier {
  JsonRecordCashier({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });

  JsonRecordCashier.empty();

  String record_key = 'Reports';
  DateTime updated_at = DateTime.now();
  JsonRecordCashierDocument document = JsonRecordCashierDocument(
    cashiers: [],
  );

  @override
  bool operator ==(Object other) =>
      other is JsonRecordCashier && other.record_key == record_key && other.updated_at == updated_at && other.document == document;
  @override
  int get hashCode => hashValues(
        record_key,
        updated_at,
        document,
      );
  factory JsonRecordCashier.fromJson(Map<String, dynamic> json) => _$JsonRecordCashierFromJson(json);
  Map<String, dynamic> toJson() => _$JsonRecordCashierToJson(this);
}

@JsonSerializable()
class JsonRecordCashierDocument {
  JsonRecordCashierDocument({
    required this.cashiers,
    this.currentCashier,
  });

  JsonRecordCashierDocument.empty();

  List<String> cashiers = <String>[];
  String? currentCashier;
  @override
  bool operator ==(Object other) =>
      other is JsonRecordCashierDocument && other.currentCashier == currentCashier && other.currentCashier == currentCashier;
  @override
  int get hashCode => hashValues(
        currentCashier,
        currentCashier,
      );
  factory JsonRecordCashierDocument.fromJson(Map<String, dynamic> json) => _$JsonRecordCashierDocumentFromJson(json);
  Map<String, dynamic> toJson() => _$JsonRecordCashierDocumentToJson(this);
}

@JsonSerializable()
class JsonRecordReasonCode {
  JsonRecordReasonCode({
    required this.record_key,
    required this.updated_at,
    required this.document,
  });

  JsonRecordReasonCode.empty();

  String record_key = 'reasonCode';
  DateTime updated_at = DateTime.now();
  ReasonCodeDocument document = ReasonCodeDocument(
    paidOut: [],
  );

  @override
  bool operator ==(Object other) =>
      other is JsonRecordReasonCode && other.record_key == record_key && other.updated_at == updated_at && other.document == document;
  @override
  int get hashCode => hashValues(
        record_key,
        updated_at,
        document,
      );
  factory JsonRecordReasonCode.fromJson(Map<String, dynamic> json) => _$JsonRecordReasonCodeFromJson(json);
  Map<String, dynamic> toJson() => _$JsonRecordReasonCodeToJson(this);
}

@JsonSerializable()
class ReasonCodeDocument {
  ReasonCodeDocument({
    required this.paidOut,
  });

  ReasonCodeDocument.empty();

  List<String> paidOut = <String>[];
  @override
  bool operator ==(Object other) => other is ReasonCodeDocument && other.paidOut == paidOut;
  @override
  int get hashCode => paidOut.hashCode;
  factory ReasonCodeDocument.fromJson(Map<String, dynamic> json) => _$ReasonCodeDocumentFromJson(json);
  Map<String, dynamic> toJson() => _$ReasonCodeDocumentToJson(this);
}
