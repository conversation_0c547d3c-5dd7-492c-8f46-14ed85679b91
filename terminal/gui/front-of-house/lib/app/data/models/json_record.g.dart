// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'json_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SystemDeviceJsonRecord _$SystemDeviceJsonRecordFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: SystemDeviceJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SystemDeviceJsonRecordToJson(SystemDeviceJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

SystemDeviceJsonRecordDocument _$SystemDeviceJsonRecordDocumentFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordDocument(
      prep: (json['prep'] as List<dynamic>).map((e) => PrepDevice.fromJson(e as Map<String, dynamic>)).toList(),
      terminal: (json['terminal'] as List<dynamic>).map((e) => SystemDeviceJsonRecordTerminal.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$SystemDeviceJsonRecordDocumentToJson(SystemDeviceJsonRecordDocument instance) => <String, dynamic>{
      'prep': instance.prep,
      'terminal': instance.terminal,
    };

SystemDeviceJsonRecordTerminal _$SystemDeviceJsonRecordTerminalFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordTerminal(
      IP: json['IP'] as String,
      MAC: json['MAC'] as String,
      desc: json['desc'] as String,
      idx: json['idx'] as int,
      scale: SystemDeviceJsonRecordScale.fromJson(json['scale'] as Map<String, dynamic>),
      rcptPrn: SystemDeviceJsonRecordRcptPrn.fromJson(json['rcptPrn'] as Map<String, dynamic>),
      poleDisp: SystemDeviceJsonRecordPoleDisp.fromJson(json['poleDisp'] as Map<String, dynamic>),
      paymentDevice: SystemDeviceJsonRecordPaymentDevice.fromJson(json['paymentDevice'] as Map<String, dynamic>),
      liqCtl: SystemDeviceJsonRecordLiqCtl.fromJson(json['liqCtl'] as Map<String, dynamic>),
      skipDevices: json['skipDevices'] as int? ?? 0,
      section: json['section'] as int? ?? 0,
      priceLevel: json['priceLevel'] as int? ?? 0,
      quickSignIn: json['quickSignIn'] as bool? ?? false,
      quickSignMinutes: json['quickSignMinutes'] as int? ?? 0,
      quickSignSection: json['quickSignSection'] as int? ?? 0,
      cashDrawers: (json['cashDrawers'] as List<dynamic>?)?.map((e) => TerminalDrawer.fromJson(e as Map<String, dynamic>)).toList() ??
          const <TerminalDrawer>[],
      remotePrintIdx: json['remotePrintIdx'] as int? ?? 0,
      autoSignOutSeconds: json['autoSignOutSeconds'] as int? ?? 0,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordTerminalToJson(SystemDeviceJsonRecordTerminal instance) => <String, dynamic>{
      'IP': instance.IP,
      'MAC': instance.MAC,
      'desc': instance.desc,
      'idx': instance.idx,
      'skipDevices': instance.skipDevices,
      'section': instance.section,
      'priceLevel': instance.priceLevel,
      'quickSignIn': instance.quickSignIn,
      'quickSignMinutes': instance.quickSignMinutes,
      'quickSignSection': instance.quickSignSection,
      'scale': instance.scale,
      'liqCtl': instance.liqCtl,
      'rcptPrn': instance.rcptPrn,
      'poleDisp': instance.poleDisp,
      'paymentDevice': instance.paymentDevice,
      'cashDrawers': instance.cashDrawers,
      'remotePrintIdx': instance.remotePrintIdx,
      'autoSignOutSeconds': instance.autoSignOutSeconds,
    };

TerminalDrawer _$TerminalDrawerFromJson(Map<String, dynamic> json) => TerminalDrawer(
      idx: json['idx'] as int,
      type: json['type'] as int? ?? 0,
      port: json['port'] as int? ?? 0,
    );

Map<String, dynamic> _$TerminalDrawerToJson(TerminalDrawer instance) => <String, dynamic>{
      'idx': instance.idx,
      'type': instance.type,
      'port': instance.port,
    };

PrepDevice _$PrepDeviceFromJson(Map<String, dynamic> json) => PrepDevice(
      idx: json['idx'] as int,
      desc: json['desc'] as String,
      type: json['type'] as int? ?? 0,
      IP: json['IP'] as String? ?? "",
      rcptTerm: json['rcptTerm'] as int? ?? 0,
      reRoute: json['reRoute'] as int? ?? 0,
    );

Map<String, dynamic> _$PrepDeviceToJson(PrepDevice instance) => <String, dynamic>{
      'idx': instance.idx,
      'desc': instance.desc,
      'type': instance.type,
      'IP': instance.IP,
      'rcptTerm': instance.rcptTerm,
      'reRoute': instance.reRoute,
    };

SystemDeviceJsonRecordPaymentDevice _$SystemDeviceJsonRecordPaymentDeviceFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordPaymentDevice(
      IP: json['IP'] as String,
      port: json['port'] as String,
      epi: json['epi'] as String,
      appID: json['appID'] as String,
      appKey: json['appKey'] as String,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordPaymentDeviceToJson(SystemDeviceJsonRecordPaymentDevice instance) => <String, dynamic>{
      'IP': instance.IP,
      'port': instance.port,
      'epi': instance.epi,
      'appID': instance.appID,
      'appKey': instance.appKey,
    };

SystemDeviceJsonRecordScale _$SystemDeviceJsonRecordScaleFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordScale(
      port: json['port'] as int,
      type: json['type'] as int,
      portType: json['portType'] as int,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordScaleToJson(SystemDeviceJsonRecordScale instance) => <String, dynamic>{
      'port': instance.port,
      'type': instance.type,
      'portType': instance.portType,
    };

SystemDeviceJsonRecordRcptPrn _$SystemDeviceJsonRecordRcptPrnFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordRcptPrn(
      baud: json['baud'] as int,
      port: json['port'] as int,
      type: json['type'] as int,
      portType: json['portType'] as int,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordRcptPrnToJson(SystemDeviceJsonRecordRcptPrn instance) => <String, dynamic>{
      'baud': instance.baud,
      'port': instance.port,
      'type': instance.type,
      'portType': instance.portType,
    };

SystemDeviceJsonRecordLiqCtl _$SystemDeviceJsonRecordLiqCtlFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordLiqCtl(
      port: json['port'] as int,
      type: json['type'] as int,
      dispenseMethod: json['dispenseMethod'] as int,
      portType: json['portType'] as int,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordLiqCtlToJson(SystemDeviceJsonRecordLiqCtl instance) => <String, dynamic>{
      'port': instance.port,
      'type': instance.type,
      'dispenseMethod': instance.dispenseMethod,
      'portType': instance.portType,
    };

SystemDeviceJsonRecordPoleDisp _$SystemDeviceJsonRecordPoleDispFromJson(Map<String, dynamic> json) => SystemDeviceJsonRecordPoleDisp(
      port: json['port'] as int,
      type: json['type'] as int,
      portType: json['portType'] as int,
    );

Map<String, dynamic> _$SystemDeviceJsonRecordPoleDispToJson(SystemDeviceJsonRecordPoleDisp instance) => <String, dynamic>{
      'port': instance.port,
      'type': instance.type,
      'portType': instance.portType,
    };

SystemSettingJsonRecord _$SystemSettingJsonRecordFromJson(Map<String, dynamic> json) => SystemSettingJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: SystemSettingJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SystemSettingJsonRecordToJson(SystemSettingJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

SystemSettingJsonRecordDocument _$SystemSettingJsonRecordDocumentFromJson(Map<String, dynamic> json) => SystemSettingJsonRecordDocument(
      ppdType: json['ppdType'] as int,
      tareUOM: json['tareUOM'] as bool,
      jobCodes: (json['jobCodes'] as List<dynamic>).map((e) => SystemSettingJsonRecordJobCode.fromJson(e as Map<String, dynamic>)).toList(),
      storeURL: json['storeURL'] as String,
      storeZip: json['storeZip'] as String,
      bannerMsg: json['bannerMsg'] as String,
      cashMedia: json['cashMedia'] as int,
      dayDivide: json['dayDivide'] as String,
      storeCity: json['storeCity'] as String,
      storeName: json['storeName'] as String,
      ppdRefDate: json['ppdRefDate'] as String,
      scaleTares: json['scaleTares'] as List<dynamic>,
      storePhone: json['storePhone'] as String,
      storeState: json['storeState'] as String,
      cashRegMode: json['cashRegMode'] as int,
      noCCSlipUnder: json['noCCSlipUnder'] as int,
      storeAddress1: json['storeAddress1'] as String,
      PEBHasChkDigit: json['PEBHasChkDigit'] as int,
      revenueCenters: json['revenueCenters'] as List<dynamic>,
      waitForDrawerClosed: json['waitForDrawerClosed'] as int,
      priceLevels: (json['priceLevels'] as List<dynamic>).map((e) => PriceLevel.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$SystemSettingJsonRecordDocumentToJson(SystemSettingJsonRecordDocument instance) => <String, dynamic>{
      'ppdType': instance.ppdType,
      'tareUOM': instance.tareUOM,
      'jobCodes': instance.jobCodes,
      'storeURL': instance.storeURL,
      'storeZip': instance.storeZip,
      'bannerMsg': instance.bannerMsg,
      'cashMedia': instance.cashMedia,
      'dayDivide': instance.dayDivide,
      'storeCity': instance.storeCity,
      'storeName': instance.storeName,
      'ppdRefDate': instance.ppdRefDate,
      'scaleTares': instance.scaleTares,
      'storePhone': instance.storePhone,
      'storeState': instance.storeState,
      'cashRegMode': instance.cashRegMode,
      'noCCSlipUnder': instance.noCCSlipUnder,
      'storeAddress1': instance.storeAddress1,
      'PEBHasChkDigit': instance.PEBHasChkDigit,
      'revenueCenters': instance.revenueCenters,
      'waitForDrawerClosed': instance.waitForDrawerClosed,
      'priceLevels': instance.priceLevels,
    };

EcomSettingJsonRecord _$EcomSettingJsonRecordFromJson(Map<String, dynamic> json) => EcomSettingJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: EcomSettingJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EcomSettingJsonRecordToJson(EcomSettingJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
      'document': instance.document,
    };

EcomSettingJsonRecordDocument _$EcomSettingJsonRecordDocumentFromJson(Map<String, dynamic> json) => EcomSettingJsonRecordDocument(
      activeHours: json['activeHours'] == null ? null : ActiveHours.fromJson(json['activeHours'] as Map<String, dynamic>),
      ecomEnabled: json['ecomEnabled'] as bool? ?? false,
      printTerminal: json['printTerminal'] as int? ?? 98,
      expediteSlip: json['expediteSlip'] as bool? ?? false,
      customerReceipt: json['customerReceipt'] as bool? ?? false,
      merchantReceipt: json['merchantReceipt'] as bool? ?? false,
      ecomPriceLevel: json['ecomPriceLevel'] as int? ?? 0,
      delivery: json['delivery'] == null ? null : Delivery.fromJson(json['delivery'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EcomSettingJsonRecordDocumentToJson(EcomSettingJsonRecordDocument instance) => <String, dynamic>{
      'activeHours': instance.activeHours,
      'ecomEnabled': instance.ecomEnabled,
      'printTerminal': instance.printTerminal,
      'expediteSlip': instance.expediteSlip,
      'customerReceipt': instance.customerReceipt,
      'merchantReceipt': instance.merchantReceipt,
      'ecomPriceLevel': instance.ecomPriceLevel,
      'delivery': instance.delivery,
    };

Delivery _$DeliveryFromJson(Map<String, dynamic> json) => Delivery(
      enabled: json['enabled'] as bool? ?? false,
      deliveryFee: json['deliveryFee'] as int? ?? 0,
    );

Map<String, dynamic> _$DeliveryToJson(Delivery instance) => <String, dynamic>{
      'enabled': instance.enabled,
      'deliveryFee': instance.deliveryFee,
    };

ActiveHours _$ActiveHoursFromJson(Map<String, dynamic> json) => ActiveHours(
      mon: (json['mon'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      tue: (json['tue'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      wed: (json['wed'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      thu: (json['thu'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      fri: (json['fri'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      sat: (json['sat'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      sun: (json['sun'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      xmas: (json['xmas'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      xmasEve: (json['xmasEve'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      nwYrs: (json['nwYrs'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      nwYrsEve: (json['nwYrsEve'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      thanks: (json['thanks'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      ind: (json['ind'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      labor: (json['labor'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      memor: (json['memor'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      colum: (json['colum'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      vets: (json['vets'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      pres: (json['pres'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
      mlk: (json['mlk'] as List<dynamic>?)?.map((e) => ActiveWindow.fromJson(e as Map<String, dynamic>)).toList() ?? const <ActiveWindow>[],
    );

Map<String, dynamic> _$ActiveHoursToJson(ActiveHours instance) => <String, dynamic>{
      'mon': instance.mon,
      'tue': instance.tue,
      'wed': instance.wed,
      'thu': instance.thu,
      'fri': instance.fri,
      'sat': instance.sat,
      'sun': instance.sun,
      'xmas': instance.xmas,
      'xmasEve': instance.xmasEve,
      'nwYrs': instance.nwYrs,
      'nwYrsEve': instance.nwYrsEve,
      'thanks': instance.thanks,
      'ind': instance.ind,
      'labor': instance.labor,
      'memor': instance.memor,
      'colum': instance.colum,
      'vets': instance.vets,
      'pres': instance.pres,
      'mlk': instance.mlk,
    };

ActiveWindow _$ActiveWindowFromJson(Map<String, dynamic> json) => ActiveWindow(
      open: json['open'] as int? ?? 0,
      close: json['close'] as int? ?? 0,
    );

Map<String, dynamic> _$ActiveWindowToJson(ActiveWindow instance) => <String, dynamic>{
      'open': instance.open,
      'close': instance.close,
    };

PriceLevel _$PriceLevelFromJson(Map<String, dynamic> json) => PriceLevel(
      idx: json['idx'] as int,
      desc: json['desc'] as String,
    );

Map<String, dynamic> _$PriceLevelToJson(PriceLevel instance) => <String, dynamic>{
      'idx': instance.idx,
      'desc': instance.desc,
    };

SystemSettingJsonRecordJobCode _$SystemSettingJsonRecordJobCodeFromJson(Map<String, dynamic> json) => SystemSettingJsonRecordJobCode(
      index: json['index'] as int,
      title: json['title'] as String,
      revCtr: json['revCtr'] as int,
      isActive: json['isActive'] as bool,
      isDeliveryDriver: json['isDeliveryDriver'] as bool,
      laborGroup: json['laborGroup'] as int,
      promptForSeat: json['promptForSeat'] as bool,
      section: json['section'] as int,
      breaks: json['breaks'] as int? ?? 0,
    );

Map<String, dynamic> _$SystemSettingJsonRecordJobCodeToJson(SystemSettingJsonRecordJobCode instance) => <String, dynamic>{
      'index': instance.index,
      'title': instance.title,
      'revCtr': instance.revCtr,
      'isActive': instance.isActive,
      'isDeliveryDriver': instance.isDeliveryDriver,
      'laborGroup': instance.laborGroup,
      'promptForSeat': instance.promptForSeat,
      'section': instance.section,
      'breaks': instance.breaks,
    };

HardwareJsonRecord _$HardwareJsonRecordFromJson(Map<String, dynamic> json) => HardwareJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: HardwareJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HardwareJsonRecordToJson(HardwareJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

HardwareJsonRecordDocument _$HardwareJsonRecordDocumentFromJson(Map<String, dynamic> json) => HardwareJsonRecordDocument(
      onScreenKeyboard: json['onScreenKeyboard'] as bool,
    );

Map<String, dynamic> _$HardwareJsonRecordDocumentToJson(HardwareJsonRecordDocument instance) => <String, dynamic>{
      'onScreenKeyboard': instance.onScreenKeyboard,
    };

MerchantJsonRecord _$MerchantJsonRecordFromJson(Map<String, dynamic> json) => MerchantJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: MerchantJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MerchantJsonRecordToJson(MerchantJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

MerchantJsonRecordDocument _$MerchantJsonRecordDocumentFromJson(Map<String, dynamic> json) => MerchantJsonRecordDocument(
      ebt: json['ebt'] as bool,
      tipping: json['tipping'] as bool,
      giftcards: json['giftcards'] as bool,
      receiptHeader: (json['receiptHeader'] as List<dynamic>).map((e) => e as String).toList(),
      dualPricing: json['dualPricing'] as bool,
      dualPricingPercent: (json['dualPricingPercent'] as num).toDouble(),
      dualPricingRoundAmount: json['dualPricingRoundAmount'] as int? ?? 1,
      refundReceiptSignatureLine: json['refundReceiptSignatureLine'] as bool,
      house: json['house'] as bool,
      preAuthAmount: json['preAuthAmount'] as int? ?? 100,
      cashConfirmation: json['cashConfirmation'] as bool,
      showModPrice: json['showModPrice'] as bool? ?? true,
      takeOutSurchargeAmt: json['takeOutSurchargeAmt'] as int? ?? 0,
      printMerchantOnCash: json['printMerchantOnCash'] as bool? ?? true,
      printMerchantOnNonCash: json['printMerchantOnNonCash'] as bool? ?? true,
      printMerchantOnHouse: json['printMerchantOnHouse'] as bool? ?? true,
      prepOnSaleChange: json['prepOnSaleChange'] as bool? ?? true,
      prepOnSignOut: json['prepOnSignOut'] as bool? ?? true,
      prepOnCancel: json['prepOnCancel'] as bool? ?? true,
      giftCardProgram:
          json['giftCardProgram'] == null ? null : MerchantJsonRecordGiftCardProgram.fromJson(json['giftCardProgram'] as Map<String, dynamic>),
      modules: json['modules'] == null ? null : MerchantJsonRecordModules.fromJson(json['modules'] as Map<String, dynamic>),
      legacyGiftTender: json['legacyGiftTender'] as bool? ?? false,
      legacyGiftName: json['legacyGiftName'] as String? ?? "Legacy Gift",
      tipLinesOnCustCopy: json['tipLinesOnCustCopy'] as bool? ?? true,
      orderTypePrintOpt: json['orderTypePrintOpt'] as int? ?? 0,
      customerCopyPrintOpt: json['customerCopyPrintOpt'] as int? ?? 0,
      overlappingScheduleOpt: json['overlappingScheduleOpt'] as int? ?? 0,
      paymentDeviceType: json['paymentDeviceType'] as int? ?? 2,
      multiCashDrawer: json['multiCashDrawer'] as bool? ?? false,
      commentRowsOnReceipt: json['commentRowsOnReceipt'] as bool? ?? false,
      saleDescOnReceipt: json['saleDescOnReceipt'] as bool? ?? false,
      paidOutPrintCustomer: json['paidOutPrintCustomer'] as bool? ?? false,
      tipConfirmLimit: json['tipConfirmLimit'] as int? ?? -1,
      prepPrintSizing: json['prepPrintSizing'] as int? ?? 0,
      printPrepFooter: json['printPrepFooter'] as bool? ?? false,
      condensedAuthSlip: json['condensedAuthSlip'] as bool? ?? false,
      defaultRefundMedia: json['defaultRefundMedia'] as bool? ?? false,
      viewTablesOnSignOn: json['viewTablesOnSignOn'] as bool? ?? false,
      qrPayEnabled: json['qrPayEnabled'] as bool? ?? false,
      markModifiersRed: json['markModifiersRed'] as bool? ?? false,
      markCommentsRed: json['markCommentsRed'] as bool? ?? false,
      markToGoRed: json['markToGoRed'] as bool? ?? false,
      markOrderTypeRed: json['markOrderTypeRed'] as bool? ?? false,
      markPromisedTimeRed: json['markPromisedTimeRed'] as bool? ?? false,
      gateway: json['gateway'] == null ? null : Gateway.fromJson(json['gateway'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MerchantJsonRecordDocumentToJson(MerchantJsonRecordDocument instance) => <String, dynamic>{
      'ebt': instance.ebt,
      'tipping': instance.tipping,
      'giftcards': instance.giftcards,
      'receiptHeader': instance.receiptHeader,
      'dualPricing': instance.dualPricing,
      'preAuthAmount': instance.preAuthAmount,
      'dualPricingPercent': instance.dualPricingPercent,
      'dualPricingRoundAmount': instance.dualPricingRoundAmount,
      'refundReceiptSignatureLine': instance.refundReceiptSignatureLine,
      'house': instance.house,
      'cashConfirmation': instance.cashConfirmation,
      'showModPrice': instance.showModPrice,
      'printMerchantOnCash': instance.printMerchantOnCash,
      'printMerchantOnNonCash': instance.printMerchantOnNonCash,
      'printMerchantOnHouse': instance.printMerchantOnHouse,
      'prepOnSaleChange': instance.prepOnSaleChange,
      'prepOnSignOut': instance.prepOnSignOut,
      'prepOnCancel': instance.prepOnCancel,
      'legacyGiftTender': instance.legacyGiftTender,
      'legacyGiftName': instance.legacyGiftName,
      'modules': instance.modules,
      'giftCardProgram': instance.giftCardProgram,
      'takeOutSurchargeAmt': instance.takeOutSurchargeAmt,
      'tipLinesOnCustCopy': instance.tipLinesOnCustCopy,
      'orderTypePrintOpt': instance.orderTypePrintOpt,
      'customerCopyPrintOpt': instance.customerCopyPrintOpt,
      'overlappingScheduleOpt': instance.overlappingScheduleOpt,
      'paymentDeviceType': instance.paymentDeviceType,
      'multiCashDrawer': instance.multiCashDrawer,
      'commentRowsOnReceipt': instance.commentRowsOnReceipt,
      'saleDescOnReceipt': instance.saleDescOnReceipt,
      'paidOutPrintCustomer': instance.paidOutPrintCustomer,
      'tipConfirmLimit': instance.tipConfirmLimit,
      'prepPrintSizing': instance.prepPrintSizing,
      'printPrepFooter': instance.printPrepFooter,
      'condensedAuthSlip': instance.condensedAuthSlip,
      'defaultRefundMedia': instance.defaultRefundMedia,
      'viewTablesOnSignOn': instance.viewTablesOnSignOn,
      'qrPayEnabled': instance.qrPayEnabled,
      'markModifiersRed': instance.markModifiersRed,
      'markCommentsRed': instance.markCommentsRed,
      'markToGoRed': instance.markToGoRed,
      'markOrderTypeRed': instance.markOrderTypeRed,
      'markPromisedTimeRed': instance.markPromisedTimeRed,
      'gateway': instance.gateway,
    };

Gateway _$GatewayFromJson(Map<String, dynamic> json) => Gateway(
      tokenizationKey: json['tokenizationKey'] as String? ?? "",
      directKey: json['directKey'] as String? ?? "",
    );

Map<String, dynamic> _$GatewayToJson(Gateway instance) => <String, dynamic>{
      'tokenizationKey': instance.tokenizationKey,
      'directKey': instance.directKey,
    };

MerchantJsonRecordModules _$MerchantJsonRecordModulesFromJson(Map<String, dynamic> json) => MerchantJsonRecordModules(
      timeClock: MerchantJsonRecordTimeClock.fromJson(json['timeClock'] as Map<String, dynamic>),
      buttonManagement: MerchantJsonRecordButtons.fromJson(json['buttonManagement'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MerchantJsonRecordModulesToJson(MerchantJsonRecordModules instance) => <String, dynamic>{
      'timeClock': instance.timeClock,
      'buttonManagement': instance.buttonManagement,
    };

MerchantJsonRecordGiftCardProgram _$MerchantJsonRecordGiftCardProgramFromJson(Map<String, dynamic> json) => MerchantJsonRecordGiftCardProgram(
      clientId: json['clientId'] as String?,
      locationId: json['locationId'] as String?,
      terminalId: json['terminalId'] as String?,
      initiatorId: json['initiatorId'] as String?,
      initiatorPass: json['initiatorPass'] as String?,
      integrationAuth: json['integrationAuth'] as String?,
      integrationPass: json['integrationPass'] as String?,
    );

Map<String, dynamic> _$MerchantJsonRecordGiftCardProgramToJson(MerchantJsonRecordGiftCardProgram instance) => <String, dynamic>{
      'clientId': instance.clientId,
      'locationId': instance.locationId,
      'terminalId': instance.terminalId,
      'initiatorId': instance.initiatorId,
      'initiatorPass': instance.initiatorPass,
      'integrationAuth': instance.integrationAuth,
      'integrationPass': instance.integrationPass,
    };

MerchantJsonRecordTimeClock _$MerchantJsonRecordTimeClockFromJson(Map<String, dynamic> json) => MerchantJsonRecordTimeClock(
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$MerchantJsonRecordTimeClockToJson(MerchantJsonRecordTimeClock instance) => <String, dynamic>{
      'isActive': instance.isActive,
    };

MerchantJsonRecordButtons _$MerchantJsonRecordButtonsFromJson(Map<String, dynamic> json) => MerchantJsonRecordButtons(
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$MerchantJsonRecordButtonsToJson(MerchantJsonRecordButtons instance) => <String, dynamic>{
      'isActive': instance.isActive,
    };

RegisterMenusJsonRecord _$RegisterMenusJsonRecordFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: json['document'] == null ? null : RegisterMenusJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RegisterMenusJsonRecordToJson(RegisterMenusJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

RegisterMenusJsonRecordDocument _$RegisterMenusJsonRecordDocumentFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecordDocument(
      toolbar: RegisterMenusJsonRecordToolbar.fromJson(json['toolbar'] as Map<String, dynamic>),
      user: RegisterMenusJsonRecordUser.fromJson(json['user'] as Map<String, dynamic>),
      admin: RegisterMenusJsonRecordAdmin.fromJson(json['admin'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RegisterMenusJsonRecordDocumentToJson(RegisterMenusJsonRecordDocument instance) => <String, dynamic>{
      'toolbar': instance.toolbar,
      'user': instance.user,
      'admin': instance.admin,
    };

RegisterMenusJsonRecordToolbar _$RegisterMenusJsonRecordToolbarFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecordToolbar(
      menu: (json['menu'] as List<dynamic>).map((e) => RegisterMenusJsonRecordButton.fromJson(e as Map<String, dynamic>)).toList(),
      custom_colors: json['custom_colors'] as bool,
    );

Map<String, dynamic> _$RegisterMenusJsonRecordToolbarToJson(RegisterMenusJsonRecordToolbar instance) => <String, dynamic>{
      'menu': instance.menu,
      'custom_colors': instance.custom_colors,
    };

RegisterMenusJsonRecordUser _$RegisterMenusJsonRecordUserFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecordUser(
      menu: (json['menu'] as List<dynamic>).map((e) => RegisterMenusJsonRecordButton.fromJson(e as Map<String, dynamic>)).toList(),
      is_active: json['is_active'] as bool,
      custom_colors: json['custom_colors'] as bool,
    );

Map<String, dynamic> _$RegisterMenusJsonRecordUserToJson(RegisterMenusJsonRecordUser instance) => <String, dynamic>{
      'menu': instance.menu,
      'is_active': instance.is_active,
      'custom_colors': instance.custom_colors,
    };

RegisterMenusJsonRecordAdmin _$RegisterMenusJsonRecordAdminFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecordAdmin(
      menu: (json['menu'] as List<dynamic>).map((e) => RegisterMenusJsonRecordButton.fromJson(e as Map<String, dynamic>)).toList(),
      is_active: json['is_active'] as bool,
      custom_colors: json['custom_colors'] as bool,
    );

Map<String, dynamic> _$RegisterMenusJsonRecordAdminToJson(RegisterMenusJsonRecordAdmin instance) => <String, dynamic>{
      'menu': instance.menu,
      'is_active': instance.is_active,
      'custom_colors': instance.custom_colors,
    };

RegisterMenusJsonRecordButton _$RegisterMenusJsonRecordButtonFromJson(Map<String, dynamic> json) => RegisterMenusJsonRecordButton(
      index: json['index'] as int,
      text: json['text'] as String,
      action: json['action'] as String,
      section: json['section'] as String,
      fGnd: json['fGnd'] as int?,
      bGnd: json['bGnd'] as int?,
      config: json['config'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$RegisterMenusJsonRecordButtonToJson(RegisterMenusJsonRecordButton instance) => <String, dynamic>{
      'index': instance.index,
      'text': instance.text,
      'action': instance.action,
      'section': instance.section,
      'fGnd': instance.fGnd,
      'bGnd': instance.bGnd,
      'config': instance.config,
    };

RoomsJsonRecord _$RoomsJsonRecordFromJson(Map<String, dynamic> json) => RoomsJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: RoomsJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RoomsJsonRecordToJson(RoomsJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

RoomsJsonRecordDocument _$RoomsJsonRecordDocumentFromJson(Map<String, dynamic> json) => RoomsJsonRecordDocument(
      text: (json['text'] as List<dynamic>).map((e) => RoomsJsonRecordText.fromJson(e as Map<String, dynamic>)).toList(),
      rooms: (json['rooms'] as List<dynamic>).map((e) => RoomsJsonRecordRoom.fromJson(e as Map<String, dynamic>)).toList(),
      tables: (json['tables'] as List<dynamic>).map((e) => RoomsJsonRecordTable.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$RoomsJsonRecordDocumentToJson(RoomsJsonRecordDocument instance) => <String, dynamic>{
      'text': instance.text,
      'rooms': instance.rooms,
      'tables': instance.tables,
    };

RoomsJsonRecordText _$RoomsJsonRecordTextFromJson(Map<String, dynamic> json) => RoomsJsonRecordText(
      X: (json['X'] as num).toDouble(),
      Y: (json['Y'] as num).toDouble(),
      idx: json['idx'] as int,
      bgnd: json['bgnd'] as int,
      desc: json['desc'] as String,
      fgnd: json['fgnd'] as int,
      font: json['font'] as String,
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      orient: json['orient'] as int,
      roomIdx: json['roomIdx'] as int,
      rotation: (json['rotation'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$RoomsJsonRecordTextToJson(RoomsJsonRecordText instance) => <String, dynamic>{
      'X': instance.X,
      'Y': instance.Y,
      'idx': instance.idx,
      'bgnd': instance.bgnd,
      'desc': instance.desc,
      'fgnd': instance.fgnd,
      'font': instance.font,
      'width': instance.width,
      'height': instance.height,
      'orient': instance.orient,
      'roomIdx': instance.roomIdx,
      'rotation': instance.rotation,
    };

RoomsJsonRecordRoom _$RoomsJsonRecordRoomFromJson(Map<String, dynamic> json) => RoomsJsonRecordRoom(
      desc: json['desc'] as String,
      prcLvl: json['prcLvl'] as int,
      idx: json['idx'] as int,
    );

Map<String, dynamic> _$RoomsJsonRecordRoomToJson(RoomsJsonRecordRoom instance) => <String, dynamic>{
      'desc': instance.desc,
      'prcLvl': instance.prcLvl,
      'idx': instance.idx,
    };

RoomsJsonRecordTable _$RoomsJsonRecordTableFromJson(Map<String, dynamic> json) => RoomsJsonRecordTable(
      desc: json['desc'] as String,
      X: (json['X'] as num).toDouble(),
      Y: (json['Y'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      rotation: (json['rotation'] as num).toDouble(),
      idx: json['idx'] as int,
      roomIdx: json['roomIdx'] as int,
      shape: json['shape'] as int,
      seatCnt: json['seatCnt'] as int,
      sectIdx: json['sectIdx'] as int,
    );

Map<String, dynamic> _$RoomsJsonRecordTableToJson(RoomsJsonRecordTable instance) => <String, dynamic>{
      'desc': instance.desc,
      'X': instance.X,
      'Y': instance.Y,
      'width': instance.width,
      'height': instance.height,
      'rotation': instance.rotation,
      'idx': instance.idx,
      'roomIdx': instance.roomIdx,
      'shape': instance.shape,
      'seatCnt': instance.seatCnt,
      'sectIdx': instance.sectIdx,
    };

SectionsJsonRecord _$SectionsJsonRecordFromJson(Map<String, dynamic> json) => SectionsJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: SectionsJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SectionsJsonRecordToJson(SectionsJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

SectionsJsonRecordDocument _$SectionsJsonRecordDocumentFromJson(Map<String, dynamic> json) => SectionsJsonRecordDocument(
      sections: (json['sections'] as List<dynamic>).map((e) => Section.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$SectionsJsonRecordDocumentToJson(SectionsJsonRecordDocument instance) => <String, dynamic>{
      'sections': instance.sections,
    };

PrinterScheduleJsonRecord _$PrinterScheduleJsonRecordFromJson(Map<String, dynamic> json) => PrinterScheduleJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: PrinterScheduleDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PrinterScheduleJsonRecordToJson(PrinterScheduleJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

PrinterScheduleDocument _$PrinterScheduleDocumentFromJson(Map<String, dynamic> json) => PrinterScheduleDocument(
      discs: (json['discs'] as List<dynamic>).map((e) => PrinterSchedule.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$PrinterScheduleDocumentToJson(PrinterScheduleDocument instance) => <String, dynamic>{
      'discs': instance.discs,
    };

PrinterSchedule _$PrinterScheduleFromJson(Map<String, dynamic> json) => PrinterSchedule(
      idx: json['idx'] as int,
      day: json['day'] as String,
      startTime: json['startTime'] as int? ?? 0,
      endTime: json['endTime'] as int? ?? 0,
      fromPrinter: json['fromPrinter'] as int? ?? -1,
      toPrinter: json['toPrinter'] as int? ?? -1,
      enabled: json['enabled'] as bool? ?? false,
    );

Map<String, dynamic> _$PrinterScheduleToJson(PrinterSchedule instance) => <String, dynamic>{
      'idx': instance.idx,
      'day': instance.day,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'fromPrinter': instance.fromPrinter,
      'toPrinter': instance.toPrinter,
      'enabled': instance.enabled,
    };

Section _$SectionFromJson(Map<String, dynamic> json) => Section(
      desc: json['desc'] as String,
      idx: json['idx'] as int,
      forceCustCnt: json['forceCustCnt'] as bool? ?? false,
      autoAddSeats: json['autoAddSeats'] as bool? ?? false,
      forceTblNum: json['forceTblNum'] as bool? ?? false,
      forceSaleDesc: json['forceSaleDesc'] as bool? ?? false,
      redAfterMins: (json['redAfterMins'] as num?)?.toDouble(),
      blinkAfterMins: (json['blinkAfterMins'] as num?)?.toDouble(),
      priceLevel: json['priceLevel'] as int?,
      trackBySeat: json['trackBySeat'] as bool? ?? true,
      saleName: json['saleName'] as String? ?? "Sale",
      openSalesList: json['openSalesList'] as bool? ?? true,
      forceOrdType: json['forceOrdType'] as bool? ?? false,
      forceCustIfTogo: json['forceCustIfTogo'] as bool? ?? false,
      forceCustIfDelivery: json['forceCustIfDelivery'] as bool? ?? false,
      custCountOption: json['custCountOption'] as int? ?? 0,
      defOrdType: json['defOrdType'] as int? ?? 0,
      minGratCustCnt: json['minGratCustCnt'] as int? ?? 1,
      calcGratWDiscs: json['calcGratWDiscs'] as bool? ?? true,
      gratAmt: (json['gratAmt'] as num?)?.toDouble() ?? 0,
      salesByEmployee: json['salesByEmployee'] as bool? ?? false,
      clockOutWithOpenSales: json['clockOutWithOpenSales'] as bool? ?? true,
      allowTransferSales: json['allowTransferSales'] as bool? ?? true,
      printServerReport: json['printServerReport'] as bool? ?? false,
      showPromisedTimeAndType: json['showPromisedTimeAndType'] as bool? ?? true,
      printerSchedule: (json['printerSchedule'] as List<dynamic>?)?.map((e) => PrinterSchedule.fromJson(e as Map<String, dynamic>)).toList() ??
          const <PrinterSchedule>[],
    );

Map<String, dynamic> _$SectionToJson(Section instance) => <String, dynamic>{
      'desc': instance.desc,
      'idx': instance.idx,
      'forceCustCnt': instance.forceCustCnt,
      'autoAddSeats': instance.autoAddSeats,
      'forceTblNum': instance.forceTblNum,
      'forceSaleDesc': instance.forceSaleDesc,
      'redAfterMins': instance.redAfterMins,
      'blinkAfterMins': instance.blinkAfterMins,
      'priceLevel': instance.priceLevel,
      'trackBySeat': instance.trackBySeat,
      'saleName': instance.saleName,
      'openSalesList': instance.openSalesList,
      'forceOrdType': instance.forceOrdType,
      'forceCustIfTogo': instance.forceCustIfTogo,
      'forceCustIfDelivery': instance.forceCustIfDelivery,
      'custCountOption': instance.custCountOption,
      'defOrdType': instance.defOrdType,
      'minGratCustCnt': instance.minGratCustCnt,
      'calcGratWDiscs': instance.calcGratWDiscs,
      'gratAmt': instance.gratAmt,
      'salesByEmployee': instance.salesByEmployee,
      'clockOutWithOpenSales': instance.clockOutWithOpenSales,
      'allowTransferSales': instance.allowTransferSales,
      'printServerReport': instance.printServerReport,
      'showPromisedTimeAndType': instance.showPromisedTimeAndType,
      'printerSchedule': instance.printerSchedule,
    };

SalesTaxJsonRecord _$SalesTaxJsonRecordFromJson(Map<String, dynamic> json) => SalesTaxJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: SalesTaxDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SalesTaxJsonRecordToJson(SalesTaxJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

SalesTaxDocument _$SalesTaxDocumentFromJson(Map<String, dynamic> json) => SalesTaxDocument(
      taxes: (json['taxes'] as List<dynamic>).map((e) => Tax.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$SalesTaxDocumentToJson(SalesTaxDocument instance) => <String, dynamic>{
      'taxes': instance.taxes,
    };

Tax _$TaxFromJson(Map<String, dynamic> json) => Tax(
      idx: json['idx'] as int?,
      desc: json['desc'] as String?,
      isVAT: json['isVAT'] as bool? ?? false,
      taxType: json['taxType'] as int? ?? 0,
      rowsUsed: json['rowsUsed'] as int? ?? 0,
      roundType: json['roundType'] as int? ?? 0,
      noTaxUnder: json['noTaxUnder'] as int? ?? 0,
      taxPercent: json['taxPercent'] as int? ?? 0,
      addOddPenny: json['addOddPenny'] as bool? ?? false,
      forgiveTakeout: json['forgiveTakeout'] as bool? ?? false,
    );

Map<String, dynamic> _$TaxToJson(Tax instance) => <String, dynamic>{
      'idx': instance.idx,
      'desc': instance.desc,
      'isVAT': instance.isVAT,
      'taxType': instance.taxType,
      'rowsUsed': instance.rowsUsed,
      'roundType': instance.roundType,
      'noTaxUnder': instance.noTaxUnder,
      'taxPercent': instance.taxPercent,
      'addOddPenny': instance.addOddPenny,
      'forgiveTakeout': instance.forgiveTakeout,
    };

PriceScheduleJsonRecord _$PriceScheduleJsonRecordFromJson(Map<String, dynamic> json) => PriceScheduleJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: PriceScheduleDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PriceScheduleJsonRecordToJson(PriceScheduleJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

PriceScheduleDocument _$PriceScheduleDocumentFromJson(Map<String, dynamic> json) => PriceScheduleDocument(
      discs: (json['discs'] as List<dynamic>).map((e) => PriceSchedule.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$PriceScheduleDocumentToJson(PriceScheduleDocument instance) => <String, dynamic>{
      'discs': instance.discs,
    };

PriceSchedule _$PriceScheduleFromJson(Map<String, dynamic> json) => PriceSchedule(
      idx: json['idx'] as int,
      desc: json['desc'] as String? ?? "",
      days: json['days'] as int? ?? 0,
      startTime: json['startTime'] as int? ?? 0,
      endTime: json['endTime'] as int? ?? 0,
      isSeasonal: json['isSeasonal'] as bool? ?? false,
      seasStMonth: json['seasStMonth'] as int? ?? 0,
      seasStDay: json['seasStDay'] as int? ?? 0,
      seasEndMonth: json['seasEndMonth'] as int? ?? 0,
      seasEndDay: json['seasEndDay'] as int? ?? 0,
    );

Map<String, dynamic> _$PriceScheduleToJson(PriceSchedule instance) => <String, dynamic>{
      'idx': instance.idx,
      'desc': instance.desc,
      'days': instance.days,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'isSeasonal': instance.isSeasonal,
      'seasStMonth': instance.seasStMonth,
      'seasStDay': instance.seasStDay,
      'seasEndMonth': instance.seasEndMonth,
      'seasEndDay': instance.seasEndDay,
    };

BreaksJsonRecord _$BreaksJsonRecordFromJson(Map<String, dynamic> json) => BreaksJsonRecord(
      record_key: json['record_key'] as String,
      updated_at: const DateTimeSerializer().fromJson(json['updated_at']),
      document: BreaksJsonRecordDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BreaksJsonRecordToJson(BreaksJsonRecord instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'document': instance.document,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
    };

BreaksJsonRecordDocument _$BreaksJsonRecordDocumentFromJson(Map<String, dynamic> json) => BreaksJsonRecordDocument(
      breaks: (json['breaks'] as List<dynamic>).map((e) => Break.fromJson(e as Map<String, dynamic>)).toList(),
    );

Map<String, dynamic> _$BreaksJsonRecordDocumentToJson(BreaksJsonRecordDocument instance) => <String, dynamic>{
      'breaks': instance.breaks,
    };

Break _$BreakFromJson(Map<String, dynamic> json) => Break(
      idx: json['idx'] as int,
      desc: json['desc'] as String? ?? "",
      isPaid: json['isPaid'] as int? ?? 0,
      breakMins: json['breakMins'] as int? ?? 0,
      minsToQualify: json['minsToQualify'] as int? ?? 0,
    );

Map<String, dynamic> _$BreakToJson(Break instance) => <String, dynamic>{
      'idx': instance.idx,
      'desc': instance.desc,
      'isPaid': instance.isPaid,
      'breakMins': instance.breakMins,
      'minsToQualify': instance.minsToQualify,
    };

JsonRecordReports _$JsonRecordReportsFromJson(Map<String, dynamic> json) => JsonRecordReports(
      record_key: json['record_key'] as String,
      updated_at: DateTime.parse(json['updated_at'] as String),
      document: JsonRecordReportDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$JsonRecordReportsToJson(JsonRecordReports instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'updated_at': instance.updated_at.toIso8601String(),
      'document': instance.document,
    };

JsonRecordReportDocument _$JsonRecordReportDocumentFromJson(Map<String, dynamic> json) => JsonRecordReportDocument(
      reportList: (json['reportList'] as List<dynamic>).map((e) => e as String).toList(),
      reportEmailAddresses: (json['reportEmailAddresses'] as List<dynamic>).map((e) => e as String).toList(),
      wageSignatureLines: json['wageSignatureLines'] as bool? ?? true,
      wageDecimalHours: json['wageDecimalHours'] as bool? ?? false,
    );

Map<String, dynamic> _$JsonRecordReportDocumentToJson(JsonRecordReportDocument instance) => <String, dynamic>{
      'reportList': instance.reportList,
      'reportEmailAddresses': instance.reportEmailAddresses,
      'wageSignatureLines': instance.wageSignatureLines,
      'wageDecimalHours': instance.wageDecimalHours,
    };

JsonRecordCashier _$JsonRecordCashierFromJson(Map<String, dynamic> json) => JsonRecordCashier(
      record_key: json['record_key'] as String,
      updated_at: DateTime.parse(json['updated_at'] as String),
      document: JsonRecordCashierDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$JsonRecordCashierToJson(JsonRecordCashier instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'updated_at': instance.updated_at.toIso8601String(),
      'document': instance.document,
    };

JsonRecordCashierDocument _$JsonRecordCashierDocumentFromJson(Map<String, dynamic> json) => JsonRecordCashierDocument(
      cashiers: (json['cashiers'] as List<dynamic>).map((e) => e as String).toList(),
      currentCashier: json['currentCashier'] as String?,
    );

Map<String, dynamic> _$JsonRecordCashierDocumentToJson(JsonRecordCashierDocument instance) => <String, dynamic>{
      'cashiers': instance.cashiers,
      'currentCashier': instance.currentCashier,
    };

JsonRecordReasonCode _$JsonRecordReasonCodeFromJson(Map<String, dynamic> json) => JsonRecordReasonCode(
      record_key: json['record_key'] as String,
      updated_at: DateTime.parse(json['updated_at'] as String),
      document: ReasonCodeDocument.fromJson(json['document'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$JsonRecordReasonCodeToJson(JsonRecordReasonCode instance) => <String, dynamic>{
      'record_key': instance.record_key,
      'updated_at': instance.updated_at.toIso8601String(),
      'document': instance.document,
    };

ReasonCodeDocument _$ReasonCodeDocumentFromJson(Map<String, dynamic> json) => ReasonCodeDocument(
      paidOut: (json['paidOut'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ReasonCodeDocumentToJson(ReasonCodeDocument instance) => <String, dynamic>{
      'paidOut': instance.paidOut,
    };
