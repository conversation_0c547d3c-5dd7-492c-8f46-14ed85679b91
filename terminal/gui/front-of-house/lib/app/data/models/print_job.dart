import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'print_job.g.dart';

@JsonSerializable()
@DateTimeSerializer()
class PrintJob {
  PrintJob({
    required this.print_job,
    required this.document,
    this.created_at,
    this.prn_num = 0,
  });

  String print_job = "";

  PrintJobDocument document = PrintJobDocument.empty();

  DateTime? created_at;

  int prn_num = 0;

  PrintJob.empty();

  @override
  bool operator ==(Object other) =>
      other is PrintJob && other.print_job == print_job && other.created_at == created_at && other.prn_num == prn_num && other.document == document;

  @override
  int get hashCode => hashValues(
        created_at,
        print_job,
        document,
      );

  factory PrintJob.fromJson(Map<String, dynamic> json) => _$PrintJobFromJson(json);

  Map<String, dynamic> toJson() => _$PrintJobToJson(this);
}

@JsonSerializable()
class PrintJobDocument {
  PrintJobDocument({
    required this.pjHeader,
    required this.pjRows,
  });

  PjHeader pjHeader = PjHeader.empty();
  List<PjRows> pjRows = [];

  PrintJobDocument.empty();

  @override
  bool operator ==(Object other) => other is PrintJobDocument && other.pjHeader == pjHeader && other.pjRows == pjRows;

  @override
  int get hashCode => hashValues(
        pjHeader,
        pjRows,
      );

  factory PrintJobDocument.fromJson(Map<String, dynamic> json) => _$PrintJobDocumentFromJson(json);

  Map<String, dynamic> toJson() => _$PrintJobDocumentToJson(this);
}

@JsonSerializable()
class PjRows {
  PjRows({
    this.text,
    this.specOpts,
    this.font,
    this.fontSize,
    this.leftText,
    this.rightText,
    this.qty,
    this.price,
    this.dept,
    this.parent,
    this.printRed = 0,
  });

  String? text;
  int? specOpts;
  int? font;
  int? fontSize;
  String? leftText;
  String? rightText;
  int? qty;
  int? price;
  String? dept;
  int? parent;
  // https://github.com/Round2POS/hyperion/blob/3cd6468a25aa9d38a8f6faa0d21b47b87f1bdfdf/terminal/services/hardware/R2Helper/RcptPrintJobs.c#L203
  //
  // Should this item be marked red on the receipt? 0 = no, 1 = yes.
  int printRed = 0;

  @override
  bool operator ==(Object other) =>
      other is PjRows &&
      other.text == text &&
      other.specOpts == specOpts &&
      other.font == font &&
      other.fontSize == fontSize &&
      other.leftText == leftText &&
      other.rightText == rightText &&
      other.qty == qty &&
      other.price == price &&
      other.dept == dept &&
      other.parent == parent &&
      other.printRed == printRed;

  @override
  int get hashCode => hashValues(
        text,
        specOpts,
        font,
        fontSize,
        leftText,
        rightText,
        qty,
        price,
        dept,
        parent,
        printRed,
      );

  factory PjRows.fromJson(Map<String, dynamic> json) => _$PjRowsFromJson(json);

  Map<String, dynamic> toJson() => _$PjRowsToJson(this);
}

@JsonSerializable()
class PjHeader {
  PjHeader({
    this.dateTime,
    this.saleNum,
    this.empID = 1000,
    this.empName,
    this.saleDesc,
    this.saleTotal,
    this.rowsUsed,
    this.prnNum = 0,
    this.termNum,
    this.orderType,
    this.tableIdx,
  });

  String? dateTime;
  int? saleNum;
  int? empID = 1000;
  String? empName;
  String? saleDesc;
  int? saleTotal;
  int? rowsUsed;
  int? prnNum = 0;
  int? termNum;
  int? orderType;
  int? tableIdx = 0;

  PjHeader.empty();

  @override
  bool operator ==(Object other) =>
      other is PjHeader &&
      other.dateTime == dateTime &&
      other.saleNum == saleNum &&
      other.empID == empID &&
      other.empName == empName &&
      other.saleDesc == saleDesc &&
      other.saleTotal == saleTotal &&
      other.rowsUsed == rowsUsed &&
      other.prnNum == prnNum &&
      other.termNum == termNum &&
      other.orderType == orderType &&
      other.tableIdx == tableIdx;

  @override
  int get hashCode => hashValues(dateTime, saleNum, empID, empName, saleDesc, saleTotal, rowsUsed, prnNum, termNum, orderType, tableIdx);

  factory PjHeader.fromJson(Map<String, dynamic> json) => _$PjHeaderFromJson(json);

  Map<String, dynamic> toJson() => _$PjHeaderToJson(this);
}
