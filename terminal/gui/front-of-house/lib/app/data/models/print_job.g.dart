// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'print_job.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrintJob _$PrintJobFromJson(Map<String, dynamic> json) => PrintJob(
      print_job: json['print_job'] as String,
      document:
          PrintJobDocument.fromJson(json['document'] as Map<String, dynamic>),
      created_at: const DateTimeSerializer().fromJson(json['created_at']),
      prn_num: json['prn_num'] as int? ?? 0,
    );

Map<String, dynamic> _$PrintJobToJson(PrintJob instance) => <String, dynamic>{
      'print_job': instance.print_job,
      'document': instance.document,
      'created_at': const DateTimeSerializer().toJson(instance.created_at),
      'prn_num': instance.prn_num,
    };

PrintJobDocument _$PrintJobDocumentFromJson(Map<String, dynamic> json) =>
    PrintJobDocument(
      pjHeader: PjHeader.fromJson(json['pjHeader'] as Map<String, dynamic>),
      pjRows: (json['pjRows'] as List<dynamic>)
          .map((e) => PjRows.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PrintJobDocumentToJson(PrintJobDocument instance) =>
    <String, dynamic>{
      'pjHeader': instance.pjHeader,
      'pjRows': instance.pjRows,
    };

PjRows _$PjRowsFromJson(Map<String, dynamic> json) => PjRows(
      text: json['text'] as String?,
      specOpts: json['specOpts'] as int?,
      font: json['font'] as int?,
      fontSize: json['fontSize'] as int?,
      leftText: json['leftText'] as String?,
      rightText: json['rightText'] as String?,
      qty: json['qty'] as int?,
      price: json['price'] as int?,
      dept: json['dept'] as String?,
      parent: json['parent'] as int?,
      printRed: json['printRed'] as int? ?? 0,
    );

Map<String, dynamic> _$PjRowsToJson(PjRows instance) => <String, dynamic>{
      'text': instance.text,
      'specOpts': instance.specOpts,
      'font': instance.font,
      'fontSize': instance.fontSize,
      'leftText': instance.leftText,
      'rightText': instance.rightText,
      'qty': instance.qty,
      'price': instance.price,
      'dept': instance.dept,
      'parent': instance.parent,
      'printRed': instance.printRed,
    };

PjHeader _$PjHeaderFromJson(Map<String, dynamic> json) => PjHeader(
      dateTime: json['dateTime'] as String?,
      saleNum: json['saleNum'] as int?,
      empID: json['empID'] as int? ?? 1000,
      empName: json['empName'] as String?,
      saleDesc: json['saleDesc'] as String?,
      saleTotal: json['saleTotal'] as int?,
      rowsUsed: json['rowsUsed'] as int?,
      prnNum: json['prnNum'] as int? ?? 0,
      termNum: json['termNum'] as int?,
      orderType: json['orderType'] as int?,
      tableIdx: json['tableIdx'] as int?,
    );

Map<String, dynamic> _$PjHeaderToJson(PjHeader instance) => <String, dynamic>{
      'dateTime': instance.dateTime,
      'saleNum': instance.saleNum,
      'empID': instance.empID,
      'empName': instance.empName,
      'saleDesc': instance.saleDesc,
      'saleTotal': instance.saleTotal,
      'rowsUsed': instance.rowsUsed,
      'prnNum': instance.prnNum,
      'termNum': instance.termNum,
      'orderType': instance.orderType,
      'tableIdx': instance.tableIdx,
    };
