// ignore_for_file: non_constant_identifier_names

import 'package:desktop/helpers/serializers/datetime_serializer.dart';
import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:json_annotation/json_annotation.dart';

part 'sale.g.dart';

@JsonSerializable()
@DateTimeSerializer()
class Sale {
  Sale({
    required this.sale,
    required this.sale_number,
    required this.document,
    required this.created_by,
    required this.updated_by,
    this.suspended = false,
    this.end_at,
  });

  String sale = "";
  int sale_number = 0;

  SaleDocument document = SaleDocument.empty();

  DateTime? created_at;
  String created_by = "";
  DateTime? updated_at;
  String updated_by = "";
  bool suspended = false;

  DateTime? end_at;

  Sale.empty();

  @override
  bool operator ==(Object other) =>
      other is Sale &&
      other.sale == sale &&
      other.sale_number == sale_number &&
      other.end_at == end_at &&
      other.created_at == created_at &&
      other.updated_at == updated_at &&
      other.created_by == created_by &&
      other.updated_by == updated_by &&
      other.suspended == suspended &&
      other.document == document;

  @override
  int get hashCode => hashValues(
        sale,
        sale_number,
        end_at,
        document,
        created_at,
        created_by,
        updated_at,
        updated_by,
        suspended,
      );

  factory Sale.fromJson(Map<String, dynamic> json) => _$SaleFromJson(json);

  Map<String, dynamic> toJson() => _$SaleToJson(this);
}

@JsonSerializable()
class SaleDocument {
  SaleDocument({
    required this.saleHeader,
    required this.saleRows,
  });

  SaleHeader saleHeader = SaleHeader.empty();
  List<SaleRow> saleRows = <SaleRow>[];

  SaleDocument.empty();

  @override
  bool operator ==(Object other) => other is SaleDocument && other.saleHeader == saleHeader && other.saleRows == saleRows;

  @override
  int get hashCode => hashValues(
        saleHeader,
        saleRows,
      );

  factory SaleDocument.fromJson(Map<String, dynamic> json) => _$SaleDocumentFromJson(json);

  Map<String, dynamic> toJson() => _$SaleDocumentToJson(this);
}

@JsonSerializable()
class SaleHeader {
  SaleHeader({
    required this.saleNumber,
    required this.startEmployeeNumber,
    required this.currentEmployeeNumber,
    this.settleEmployeeNumber,
    required this.startTerminalNumber,
    this.currentTerminalNumber = 0,
    this.settleTerminalNumber,
    this.saleDescription,
    required this.saleFlags,
    this.total = 0,
    this.subTotal = 0,
    this.taxTotal = 0,
    this.cashTotal = 0,
    this.cashSubTotal = 0,
    this.cashTaxTotal = 0,
    this.dualPricingAmount = 0,
    this.dualPricingPercent = 0.0,
    this.discountTotal = 0,
    this.cashDiscountTotal = 0,
    this.discountReas,
    this.dueRound,
    this.overtAmount,
    this.overtMedia,
    this.customer,
    this.customerName,
    this.priceLevel = 0,
    this.cashDrawer,
    this.refundType,
    this.tableDesc,
    this.roomIdx,
    this.sectIdx,
    this.seatCnt,
    required this.tenders,
    this.originalSale,
    this.gratuityPercent = 0.0,
    this.gratuityTotal = 0,
    this.cashGratuityTotal = 0,
    this.addedGratuity,
    this.currentCashier,
    this.taxTotals = const <int>[],
    this.seatsTendered = const <int>[],
    this.seatsSettled = const <int>[],
    this.reservedSaleNumber,
    this.cashTaxTotals = const <int>[],
    this.promisedTime,
    this.orderType = 0,
    this.customerCount = 0,
    this.toGoSeatCnt = 0,
    this.takeOutSurchargeTotal = 0,
    this.tableIdx = 0,
    this.tableStarted,
  });

  int saleNumber = 0;

  int startEmployeeNumber = 0;
  int currentEmployeeNumber = 0;
  int? settleEmployeeNumber;

  int startTerminalNumber = 0;
  int currentTerminalNumber = 0;
  int? settleTerminalNumber;

  String? saleDescription;

  List<int> saleFlags = <int>[];

  int total = 0;
  int subTotal = 0;
  int taxTotal = 0;

  int cashTotal = 0;
  int cashSubTotal = 0;
  int cashTaxTotal = 0;
  int dualPricingAmount = 0;
  double dualPricingPercent = 0.0;

  double gratuityPercent = 0.0;
  int gratuityTotal = 0;
  int cashGratuityTotal = 0;

  double? addedGratuity;

  int discountTotal = 0;
  int cashDiscountTotal = 0;

  int? discountReas;
  int? dueRound;

  int? overtAmount;
  int? overtMedia;

  String? customer;
  String? customerName;
  int? cashDrawer;
  int? refundType;

  String? tableDesc;
  int? roomIdx;
  int? sectIdx;
  int? seatCnt;
  int? priceLevel = 0;
  String? currentCashier;

  List<SaleTender> tenders = <SaleTender>[];

  String? originalSale;

  List<int> taxTotals = <int>[];

  List<int> seatsTendered = <int>[];
  List<int> seatsSettled = <int>[];

  /// New sale number being reserved here for tendering out seats separately
  /// This number is used on the dummy sale in sale controller and the new sale that's created on completion
  /// If it's null then it's assumed that there aren't any seats currently being tendered separately
  int? reservedSaleNumber;

  List<int> cashTaxTotals = <int>[];

  int? promisedTime;
  int orderType = 0;
  int customerCount = 0;
  int toGoSeatCnt = 0;
  int takeOutSurchargeTotal = 0;
  int tableIdx = 0;

  DateTime? tableStarted;

  SaleHeader.empty();

  @override
  bool operator ==(Object other) =>
      other is SaleHeader &&
      other.saleNumber == saleNumber &&
      other.startEmployeeNumber == startEmployeeNumber &&
      other.currentEmployeeNumber == currentEmployeeNumber &&
      other.settleEmployeeNumber == settleEmployeeNumber &&
      other.startTerminalNumber == startTerminalNumber &&
      other.currentTerminalNumber == currentTerminalNumber &&
      other.settleTerminalNumber == settleTerminalNumber &&
      other.saleDescription == saleDescription &&
      other.saleFlags == saleFlags &&
      other.total == total &&
      other.currentCashier == currentCashier &&
      other.taxTotals == taxTotals &&
      other.seatsTendered == seatsTendered &&
      other.seatsSettled == seatsSettled &&
      other.reservedSaleNumber == reservedSaleNumber &&
      other.promisedTime == promisedTime &&
      other.orderType == orderType &&
      other.customerCount == customerCount &&
      other.seatCnt == seatCnt &&
      other.priceLevel == priceLevel &&
      other.subTotal == subTotal &&
      other.taxTotal == taxTotal &&
      other.cashTotal == cashTotal &&
      other.cashSubTotal == cashSubTotal &&
      other.cashTaxTotal == cashTaxTotal &&
      other.dualPricingAmount == dualPricingAmount &&
      other.dualPricingPercent == dualPricingPercent &&
      other.gratuityPercent == gratuityPercent &&
      other.gratuityTotal == gratuityTotal &&
      other.cashGratuityTotal == cashGratuityTotal &&
      other.addedGratuity == addedGratuity &&
      other.discountTotal == discountTotal &&
      other.cashDiscountTotal == cashDiscountTotal &&
      other.discountReas == discountReas &&
      other.dueRound == dueRound &&
      other.overtAmount == overtAmount &&
      other.overtMedia == overtMedia &&
      other.customer == customer &&
      other.customerName == customerName &&
      other.cashDrawer == cashDrawer &&
      other.refundType == refundType &&
      other.tableDesc == tableDesc &&
      other.roomIdx == roomIdx &&
      other.sectIdx == sectIdx &&
      other.tenders == tenders &&
      other.originalSale == originalSale &&
      other.cashTaxTotals == cashTaxTotals &&
      other.toGoSeatCnt == toGoSeatCnt &&
      other.takeOutSurchargeTotal == takeOutSurchargeTotal &&
      other.tableIdx == tableIdx &&
      other.tableStarted == tableStarted;

  @override
  int get hashCode => hashValues(
        saleNumber,
        startEmployeeNumber,
        currentEmployeeNumber,
        settleEmployeeNumber,
        startTerminalNumber,
        currentTerminalNumber,
        settleTerminalNumber,
        saleDescription,
        saleFlags,
        total,
        subTotal,
        taxTotal,
        discountReas,
        dueRound,
        overtAmount,
        overtMedia,
        customer,
        cashDrawer,
        refundType,
        tenders,
      );

  factory SaleHeader.fromJson(Map<String, dynamic> json) => _$SaleHeaderFromJson(json);

  Map<String, dynamic> toJson() => _$SaleHeaderToJson(this);
}

@JsonSerializable()
class SaleRowExtData {
  SaleRowExtData();

  factory SaleRowExtData.fromJson(Map<String, dynamic> json) => _$SaleRowExtDataFromJson(json);

  Map<String, dynamic> toJson() => _$SaleRowExtDataToJson(this);
}

@JsonSerializable()
class SaleRow {
  SaleRow({
    required this.item,
    required this.upc,
    this.index = 0,
    required this.receiptDescription,
    required this.department,
    this.isWeightedItem = false,
    required this.transactionFlags,
    required this.flags,
    this.employee,
    this.voidedEmployee,
    this.voidedReason,
    this.voidDtTm,
    this.sendDtTm,
    this.soldDtTm,
    this.UOM,
    this.weight,
    this.parent = -1,
    this.qty = 1,
    this.grossPrice = 0,
    this.cashGrossPrice = 0,
    this.actualPrice = 0,
    this.cashPrice = 0,
    this.creditPrice = 0,
    required this.basePrice,
    this.cashBasePrice = 0,
    required this.originalPrice,
    this.cashOriginalPrice = 0,
    this.VATAmount,
    this.selected = false,
    this.extData,
    this.seatNumber = 1,
    this.taxFlags = 0,
    this.origTaxFlags = 0,
    this.itemPricing = const <String, int>{},
    this.isVisible = true,
    this.hasChildren = false,
    this.prep = 0,
    this.discounts = const <SaleRowDiscount>[],
    this.giftTransactionData,
    this.takeOutSurcharge = false,
    this.takeOutSurchargeAmt = 0,
    this.splitData,
    this.printSeparate = false,
    this.liquorToPour = const <String>[],
    this.preppedAt,
  });

  String item = "";
  String upc = "";
  String receiptDescription = "";
  String department = "";
  List<int> flags = <int>[];
  List<int> transactionFlags = <int>[];
  List<SaleRowDiscount> discounts = <SaleRowDiscount>[];
  int index;
  int? employee;
  int? voidedEmployee;
  int? voidedReason;
  int? voidDtTm;
  int? sendDtTm;
  int? soldDtTm;
  bool isWeightedItem = false;
  int? UOM;
  double? weight;
  int parent = -1;
  int qty;
  int basePrice = 0;
  int cashBasePrice = 0;
  int originalPrice = 0;
  int cashOriginalPrice = 0;
  int grossPrice = 0;
  int cashGrossPrice = 0;
  int actualPrice = 0;
  int cashPrice = 0;
  int creditPrice = 0;
  int? VATAmount;
  bool selected;
  SaleRowExtData? extData;
  GiftTransactionData? giftTransactionData;
  int seatNumber = 0;
  int taxFlags = 0;
  int origTaxFlags = 0;
  Map<String, int>? itemPricing = <String, int>{};
  bool isVisible = true;
  bool hasChildren = false;
  int prep = 0; // Bitwise integer representing which printers this saleRow was sent to.
  bool takeOutSurcharge = false;
  int takeOutSurchargeAmt = 0;
  SplitData? splitData;
  bool printSeparate = false;
  List<String> liquorToPour = <String>[];
  Map<String, DateTime?>? preppedAt; // { Printer ID (Integer Index As String)  => Prep Time }

  @override
  bool operator ==(Object other) =>
      other is SaleRow &&
      other.item == item &&
      other.index == index &&
      other.upc == upc &&
      other.receiptDescription == receiptDescription &&
      other.department == department &&
      other.basePrice == basePrice &&
      other.cashBasePrice == cashBasePrice &&
      other.flags == flags &&
      other.transactionFlags == transactionFlags &&
      other.employee == employee &&
      other.voidedEmployee == voidedEmployee &&
      other.voidedReason == voidedReason &&
      other.discounts == discounts &&
      other.voidDtTm == voidDtTm &&
      other.sendDtTm == sendDtTm &&
      other.soldDtTm == soldDtTm &&
      other.isWeightedItem == isWeightedItem &&
      other.UOM == UOM &&
      other.weight == weight &&
      other.parent == parent &&
      other.qty == qty &&
      other.grossPrice == grossPrice &&
      other.cashGrossPrice == cashGrossPrice &&
      other.actualPrice == actualPrice &&
      other.cashPrice == cashPrice &&
      other.creditPrice == creditPrice &&
      other.originalPrice == originalPrice &&
      other.cashOriginalPrice == cashOriginalPrice &&
      other.VATAmount == VATAmount &&
      other.selected == selected &&
      other.extData == extData &&
      other.seatNumber == seatNumber &&
      other.taxFlags == taxFlags &&
      other.origTaxFlags == origTaxFlags &&
      other.itemPricing == itemPricing &&
      other.isVisible == isVisible &&
      other.hasChildren == hasChildren &&
      other.takeOutSurcharge == takeOutSurcharge &&
      other.takeOutSurchargeAmt == takeOutSurchargeAmt &&
      other.splitData == splitData &&
      other.printSeparate == printSeparate &&
      other.liquorToPour == liquorToPour &&
      other.preppedAt == preppedAt;

  @override
  int get hashCode => hashValues(
        item,
        receiptDescription,
        department,
        basePrice,
        flags,
        transactionFlags,
        employee,
        voidedEmployee,
        voidedReason,
        voidDtTm,
        sendDtTm,
        soldDtTm,
        isWeightedItem,
        UOM,
        weight,
        parent,
        qty,
        grossPrice,
        discounts,
        upc,
      );

  factory SaleRow.fromJson(Map<String, dynamic> json) => _$SaleRowFromJson(json);

  Map<String, dynamic> toJson() => _$SaleRowToJson(this);
}

@JsonSerializable()
class SplitData {
  SplitData({
    required this.qty,
    required this.key,
  });

  int qty;
  String key;

  @override
  bool operator ==(Object other) => other is SplitData && other.qty == qty && other.key == key;

  @override
  int get hashCode => hashValues(qty, key);

  factory SplitData.fromJson(Map<String, dynamic> json) => _$SplitDataFromJson(json);

  Map<String, dynamic> toJson() => _$SplitDataToJson(this);
}

@JsonSerializable()
class SaleTender {
  SaleTender({
    this.media,
    this.amount,
    this.cardTransactionData,
    this.giftTransactionData,
    this.checkNum,
    this.memo,
    required this.saleTenderFlags,
    this.tipAmount,
    this.tipMedia,
  });

  int? media;
  int? amount;
  String? checkNum;
  String? memo;
  List<int> saleTenderFlags = <int>[];
  int? tipAmount;
  int? tipMedia;

  CardTransactionData? cardTransactionData;
  GiftTransactionData? giftTransactionData;

  @override
  bool operator ==(Object other) =>
      other is SaleTender &&
      other.media == media &&
      other.amount == amount &&
      other.checkNum == checkNum &&
      other.memo == memo &&
      other.saleTenderFlags == saleTenderFlags &&
      other.cardTransactionData == cardTransactionData &&
      other.giftTransactionData == giftTransactionData &&
      other.tipAmount == tipAmount &&
      other.tipMedia == tipMedia;

  @override
  int get hashCode => hashValues(
        media,
        amount,
        checkNum,
        memo,
        saleTenderFlags,
        cardTransactionData,
        giftTransactionData,
        tipAmount,
        tipMedia,
      );

  factory SaleTender.fromJson(Map<String, dynamic> json) => _$SaleTenderFromJson(json);

  Map<String, dynamic> toJson() => _$SaleTenderToJson(this);
}

@JsonSerializable()
class SaleRowDiscount {
  SaleRowDiscount({
    required this.type,
    this.title = 'Discount',
    required this.amount,
    this.cashAmount = 0,
    required this.value,
    this.cashValue = 0,
  });

  int type;
  int amount;
  int cashAmount = 0;
  int value;
  int cashValue = 0;
  String? title;

  @override
  bool operator ==(Object other) =>
      other is SaleRowDiscount &&
      other.amount == amount &&
      other.cashAmount == cashAmount &&
      other.type == type &&
      other.value == value &&
      other.cashValue == cashValue;

  @override
  int get hashCode => hashValues(amount, cashAmount, type, value, cashValue);

  factory SaleRowDiscount.fromJson(Map<String, dynamic> json) => _$SaleRowDiscountFromJson(json);

  Map<String, dynamic> toJson() => _$SaleRowDiscountToJson(this);
}

@JsonSerializable()
class CardTransactionData {
  CardTransactionData({
    this.provider = 0,
    this.uid = "",
    this.amount = 0,
    this.authCode = "",
    this.batchNo = "",
    this.cardExp = "",
    this.cardPAN = "",
    this.cardHolder = "",
    this.cardBrand = 0,
    this.entryMode = 0,
    this.refECRID = "",
    this.refCustomID = "",
    this.refTxnID = "",
    this.tenderType = 0,
    this.transactionMode = 0,
  });

  int provider;
  String uid;
  int amount;
  String authCode;
  String batchNo;
  String cardExp;
  String cardPAN;
  String cardHolder;
  int cardBrand;
  int entryMode;
  String refECRID;
  String refCustomID;
  String refTxnID;
  int tenderType;
  int transactionMode;

  @override
  bool operator ==(Object other) => other is CardTransactionData;

  @override
  int get hashCode => hashValues(
        provider,
        uid,
        amount,
        authCode,
        batchNo,
        cardExp,
        cardPAN,
        cardHolder,
        cardBrand,
        entryMode,
        refECRID,
        refCustomID,
        refTxnID,
        tenderType,
        transactionMode,
      );

  factory CardTransactionData.fromJson(Map<String, dynamic> json) => _$CardTransactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$CardTransactionDataToJson(this);
}

@JsonSerializable()
class SettleBatchTransactionData {
  const SettleBatchTransactionData({
    this.date,
    this.state,
    this.amount,
    this.batchNo,
    this.totalTranCount,
  });

  final String? date;
  final String? state;
  final String? amount;
  final String? batchNo;
  final String? totalTranCount;

  @override
  bool operator ==(Object other) =>
      other is SettleBatchTransactionData &&
      other.date == date &&
      other.state == state &&
      other.amount == amount &&
      other.batchNo == batchNo &&
      other.totalTranCount == totalTranCount;

  @override
  int get hashCode => hashValues(
        date,
        state,
        amount,
        batchNo,
        totalTranCount,
      );

  factory SettleBatchTransactionData.fromJson(Map<String, dynamic> json) => _$SettleBatchTransactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$SettleBatchTransactionDataToJson(this);
}

@JsonSerializable()
class GiftTransactionData {
  GiftTransactionData({
    required this.standardHeader,
    required this.identification,
    required this.expirationDate,
    required this.balances,
    required this.hostMessage,
    required this.printCodes,
    this.errorMessage,
  });

  final StandardHeader standardHeader;
  final Identification identification;
  final String expirationDate;
  final Balances balances;
  final String hostMessage;
  final String printCodes;
  final ErrorMessage? errorMessage;

  factory GiftTransactionData.fromJson(Map<String, dynamic> json) => _$GiftTransactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$GiftTransactionDataToJson(this);
}

@JsonSerializable()
class StandardHeader {
  StandardHeader({
    required this.text,
    required this.requestId,
    required this.localeId,
    required this.systemId,
    required this.clientId,
    required this.locationId,
    required this.terminalId,
    required this.terminalDateTime,
    required this.initiatorType,
    required this.initiatorId,
    required this.initiatorPassword,
    required this.externalId,
    required this.batchId,
    required this.batchReference,
    required this.channel,
    required this.subChannel,
    required this.status,
  });

  final String text;
  final String requestId;
  final String localeId;
  final String systemId;
  final String clientId;
  final String locationId;
  final String terminalId;
  final String terminalDateTime;
  final String initiatorType;
  final String initiatorId;
  final String initiatorPassword;
  final String externalId;
  final String batchId;
  final String batchReference;
  final String channel;
  final String subChannel;
  final String status;

  factory StandardHeader.fromJson(Map<String, dynamic> json) => _$StandardHeaderFromJson(json);

  Map<String, dynamic> toJson() => _$StandardHeaderToJson(this);
}

@JsonSerializable()
class Identification {
  Identification({
    required this.transactionId,
    required this.approvalCode,
    required this.demonstration,
    required this.cardNumber,
  });

  final String transactionId;
  final String approvalCode;
  final String demonstration;
  final String cardNumber;

  factory Identification.fromJson(Map<String, dynamic> json) => _$IdentificationFromJson(json);

  Map<String, dynamic> toJson() => _$IdentificationToJson(this);
}

@JsonSerializable()
class Balances {
  Balances({
    this.balance,
  });

  final List<Balance>? balance;

  factory Balances.fromJson(Map<String, dynamic> json) => _$BalancesFromJson(json);

  Map<String, dynamic> toJson() => _$BalancesToJson(this);
}

@JsonSerializable()
class Balance {
  Balance({
    required this.valueCode,
    required this.amount,
    required this.difference,
    required this.exchangeRate,
  });

  final String valueCode;
  final String amount;
  final String difference;
  final String exchangeRate;

  factory Balance.fromJson(Map<String, dynamic> json) => _$BalanceFromJson(json);

  Map<String, dynamic> toJson() => _$BalanceToJson(this);
}

@JsonSerializable()
class ErrorMessage {
  ErrorMessage({
    this.rejectionId,
    this.errorCode,
    this.briefMessage,
    this.inDepthMessage,
  });

  final String? rejectionId;
  final String? errorCode;
  final String? briefMessage;
  final String? inDepthMessage;

  factory ErrorMessage.fromJson(Map<String, dynamic> json) => _$ErrorMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorMessageToJson(this);
}
