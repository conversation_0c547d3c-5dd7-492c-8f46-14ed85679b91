// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sale.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Sale _$SaleFromJson(Map<String, dynamic> json) => Sale(
      sale: json['sale'] as String,
      sale_number: json['sale_number'] as int,
      document: SaleDocument.fromJson(json['document'] as Map<String, dynamic>),
      created_by: json['created_by'] as String,
      updated_by: json['updated_by'] as String,
      suspended: json['suspended'] as bool? ?? false,
      end_at: const DateTimeSerializer().fromJson(json['end_at']),
    )
      ..created_at = const DateTimeSerializer().fromJson(json['created_at'])
      ..updated_at = const DateTimeSerializer().fromJson(json['updated_at']);

Map<String, dynamic> _$SaleToJson(Sale instance) => <String, dynamic>{
      'sale': instance.sale,
      'sale_number': instance.sale_number,
      'document': instance.document,
      'created_at': const DateTimeSerializer().toJson(instance.created_at),
      'created_by': instance.created_by,
      'updated_at': const DateTimeSerializer().toJson(instance.updated_at),
      'updated_by': instance.updated_by,
      'suspended': instance.suspended,
      'end_at': const DateTimeSerializer().toJson(instance.end_at),
    };

SaleDocument _$SaleDocumentFromJson(Map<String, dynamic> json) => SaleDocument(
      saleHeader:
          SaleHeader.fromJson(json['saleHeader'] as Map<String, dynamic>),
      saleRows: (json['saleRows'] as List<dynamic>)
          .map((e) => SaleRow.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SaleDocumentToJson(SaleDocument instance) =>
    <String, dynamic>{
      'saleHeader': instance.saleHeader,
      'saleRows': instance.saleRows,
    };

SaleHeader _$SaleHeaderFromJson(Map<String, dynamic> json) => SaleHeader(
      saleNumber: json['saleNumber'] as int,
      startEmployeeNumber: json['startEmployeeNumber'] as int,
      currentEmployeeNumber: json['currentEmployeeNumber'] as int,
      settleEmployeeNumber: json['settleEmployeeNumber'] as int?,
      startTerminalNumber: json['startTerminalNumber'] as int,
      currentTerminalNumber: json['currentTerminalNumber'] as int? ?? 0,
      settleTerminalNumber: json['settleTerminalNumber'] as int?,
      saleDescription: json['saleDescription'] as String?,
      saleFlags:
          (json['saleFlags'] as List<dynamic>).map((e) => e as int).toList(),
      total: json['total'] as int? ?? 0,
      subTotal: json['subTotal'] as int? ?? 0,
      taxTotal: json['taxTotal'] as int? ?? 0,
      cashTotal: json['cashTotal'] as int? ?? 0,
      cashSubTotal: json['cashSubTotal'] as int? ?? 0,
      cashTaxTotal: json['cashTaxTotal'] as int? ?? 0,
      dualPricingAmount: json['dualPricingAmount'] as int? ?? 0,
      dualPricingPercent:
          (json['dualPricingPercent'] as num?)?.toDouble() ?? 0.0,
      discountTotal: json['discountTotal'] as int? ?? 0,
      cashDiscountTotal: json['cashDiscountTotal'] as int? ?? 0,
      discountReas: json['discountReas'] as int?,
      dueRound: json['dueRound'] as int?,
      overtAmount: json['overtAmount'] as int?,
      overtMedia: json['overtMedia'] as int?,
      customer: json['customer'] as String?,
      customerName: json['customerName'] as String?,
      priceLevel: json['priceLevel'] as int? ?? 0,
      cashDrawer: json['cashDrawer'] as int?,
      refundType: json['refundType'] as int?,
      tableDesc: json['tableDesc'] as String?,
      roomIdx: json['roomIdx'] as int?,
      sectIdx: json['sectIdx'] as int?,
      seatCnt: json['seatCnt'] as int?,
      tenders: (json['tenders'] as List<dynamic>)
          .map((e) => SaleTender.fromJson(e as Map<String, dynamic>))
          .toList(),
      originalSale: json['originalSale'] as String?,
      gratuityPercent: (json['gratuityPercent'] as num?)?.toDouble() ?? 0.0,
      gratuityTotal: json['gratuityTotal'] as int? ?? 0,
      cashGratuityTotal: json['cashGratuityTotal'] as int? ?? 0,
      addedGratuity: (json['addedGratuity'] as num?)?.toDouble(),
      currentCashier: json['currentCashier'] as String?,
      taxTotals: (json['taxTotals'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          const <int>[],
      seatsTendered: (json['seatsTendered'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          const <int>[],
      seatsSettled: (json['seatsSettled'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          const <int>[],
      reservedSaleNumber: json['reservedSaleNumber'] as int?,
      cashTaxTotals: (json['cashTaxTotals'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          const <int>[],
      promisedTime: json['promisedTime'] as int?,
      orderType: json['orderType'] as int? ?? 0,
      customerCount: json['customerCount'] as int? ?? 0,
      toGoSeatCnt: json['toGoSeatCnt'] as int? ?? 0,
      takeOutSurchargeTotal: json['takeOutSurchargeTotal'] as int? ?? 0,
      tableIdx: json['tableIdx'] as int? ?? 0,
      tableStarted: json['tableStarted'] == null
          ? null
          : DateTime.parse(json['tableStarted'] as String),
    );

Map<String, dynamic> _$SaleHeaderToJson(SaleHeader instance) =>
    <String, dynamic>{
      'saleNumber': instance.saleNumber,
      'startEmployeeNumber': instance.startEmployeeNumber,
      'currentEmployeeNumber': instance.currentEmployeeNumber,
      'settleEmployeeNumber': instance.settleEmployeeNumber,
      'startTerminalNumber': instance.startTerminalNumber,
      'currentTerminalNumber': instance.currentTerminalNumber,
      'settleTerminalNumber': instance.settleTerminalNumber,
      'saleDescription': instance.saleDescription,
      'saleFlags': instance.saleFlags,
      'total': instance.total,
      'subTotal': instance.subTotal,
      'taxTotal': instance.taxTotal,
      'cashTotal': instance.cashTotal,
      'cashSubTotal': instance.cashSubTotal,
      'cashTaxTotal': instance.cashTaxTotal,
      'dualPricingAmount': instance.dualPricingAmount,
      'dualPricingPercent': instance.dualPricingPercent,
      'gratuityPercent': instance.gratuityPercent,
      'gratuityTotal': instance.gratuityTotal,
      'cashGratuityTotal': instance.cashGratuityTotal,
      'addedGratuity': instance.addedGratuity,
      'discountTotal': instance.discountTotal,
      'cashDiscountTotal': instance.cashDiscountTotal,
      'discountReas': instance.discountReas,
      'dueRound': instance.dueRound,
      'overtAmount': instance.overtAmount,
      'overtMedia': instance.overtMedia,
      'customer': instance.customer,
      'customerName': instance.customerName,
      'cashDrawer': instance.cashDrawer,
      'refundType': instance.refundType,
      'tableDesc': instance.tableDesc,
      'roomIdx': instance.roomIdx,
      'sectIdx': instance.sectIdx,
      'seatCnt': instance.seatCnt,
      'priceLevel': instance.priceLevel,
      'currentCashier': instance.currentCashier,
      'tenders': instance.tenders,
      'originalSale': instance.originalSale,
      'taxTotals': instance.taxTotals,
      'seatsTendered': instance.seatsTendered,
      'seatsSettled': instance.seatsSettled,
      'reservedSaleNumber': instance.reservedSaleNumber,
      'cashTaxTotals': instance.cashTaxTotals,
      'promisedTime': instance.promisedTime,
      'orderType': instance.orderType,
      'customerCount': instance.customerCount,
      'toGoSeatCnt': instance.toGoSeatCnt,
      'takeOutSurchargeTotal': instance.takeOutSurchargeTotal,
      'tableIdx': instance.tableIdx,
      'tableStarted': instance.tableStarted?.toIso8601String(),
    };

SaleRowExtData _$SaleRowExtDataFromJson(Map<String, dynamic> json) =>
    SaleRowExtData();

Map<String, dynamic> _$SaleRowExtDataToJson(SaleRowExtData instance) =>
    <String, dynamic>{};

SaleRow _$SaleRowFromJson(Map<String, dynamic> json) => SaleRow(
      item: json['item'] as String,
      upc: json['upc'] as String,
      index: json['index'] as int? ?? 0,
      receiptDescription: json['receiptDescription'] as String,
      department: json['department'] as String,
      isWeightedItem: json['isWeightedItem'] as bool? ?? false,
      transactionFlags: (json['transactionFlags'] as List<dynamic>)
          .map((e) => e as int)
          .toList(),
      flags: (json['flags'] as List<dynamic>).map((e) => e as int).toList(),
      employee: json['employee'] as int?,
      voidedEmployee: json['voidedEmployee'] as int?,
      voidedReason: json['voidedReason'] as int?,
      voidDtTm: json['voidDtTm'] as int?,
      sendDtTm: json['sendDtTm'] as int?,
      soldDtTm: json['soldDtTm'] as int?,
      UOM: json['UOM'] as int?,
      weight: (json['weight'] as num?)?.toDouble(),
      parent: json['parent'] as int? ?? -1,
      qty: json['qty'] as int? ?? 1,
      grossPrice: json['grossPrice'] as int? ?? 0,
      cashGrossPrice: json['cashGrossPrice'] as int? ?? 0,
      actualPrice: json['actualPrice'] as int? ?? 0,
      cashPrice: json['cashPrice'] as int? ?? 0,
      creditPrice: json['creditPrice'] as int? ?? 0,
      basePrice: json['basePrice'] as int,
      cashBasePrice: json['cashBasePrice'] as int? ?? 0,
      originalPrice: json['originalPrice'] as int,
      cashOriginalPrice: json['cashOriginalPrice'] as int? ?? 0,
      VATAmount: json['VATAmount'] as int?,
      selected: json['selected'] as bool? ?? false,
      extData: json['extData'] == null
          ? null
          : SaleRowExtData.fromJson(json['extData'] as Map<String, dynamic>),
      seatNumber: json['seatNumber'] as int? ?? 1,
      taxFlags: json['taxFlags'] as int? ?? 0,
      origTaxFlags: json['origTaxFlags'] as int? ?? 0,
      itemPricing: (json['itemPricing'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as int),
          ) ??
          const <String, int>{},
      isVisible: json['isVisible'] as bool? ?? true,
      hasChildren: json['hasChildren'] as bool? ?? false,
      prep: json['prep'] as int? ?? 0,
      discounts: (json['discounts'] as List<dynamic>?)
              ?.map((e) => SaleRowDiscount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <SaleRowDiscount>[],
      giftTransactionData: json['giftTransactionData'] == null
          ? null
          : GiftTransactionData.fromJson(
              json['giftTransactionData'] as Map<String, dynamic>),
      takeOutSurcharge: json['takeOutSurcharge'] as bool? ?? false,
      takeOutSurchargeAmt: json['takeOutSurchargeAmt'] as int? ?? 0,
      splitData: json['splitData'] == null
          ? null
          : SplitData.fromJson(json['splitData'] as Map<String, dynamic>),
      printSeparate: json['printSeparate'] as bool? ?? false,
      liquorToPour: (json['liquorToPour'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
      preppedAt: (json['preppedAt'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e == null ? null : DateTime.parse(e as String)),
      ),
    );

Map<String, dynamic> _$SaleRowToJson(SaleRow instance) => <String, dynamic>{
      'item': instance.item,
      'upc': instance.upc,
      'receiptDescription': instance.receiptDescription,
      'department': instance.department,
      'flags': instance.flags,
      'transactionFlags': instance.transactionFlags,
      'discounts': instance.discounts,
      'index': instance.index,
      'employee': instance.employee,
      'voidedEmployee': instance.voidedEmployee,
      'voidedReason': instance.voidedReason,
      'voidDtTm': instance.voidDtTm,
      'sendDtTm': instance.sendDtTm,
      'soldDtTm': instance.soldDtTm,
      'isWeightedItem': instance.isWeightedItem,
      'UOM': instance.UOM,
      'weight': instance.weight,
      'parent': instance.parent,
      'qty': instance.qty,
      'basePrice': instance.basePrice,
      'cashBasePrice': instance.cashBasePrice,
      'originalPrice': instance.originalPrice,
      'cashOriginalPrice': instance.cashOriginalPrice,
      'grossPrice': instance.grossPrice,
      'cashGrossPrice': instance.cashGrossPrice,
      'actualPrice': instance.actualPrice,
      'cashPrice': instance.cashPrice,
      'creditPrice': instance.creditPrice,
      'VATAmount': instance.VATAmount,
      'selected': instance.selected,
      'extData': instance.extData,
      'giftTransactionData': instance.giftTransactionData,
      'seatNumber': instance.seatNumber,
      'taxFlags': instance.taxFlags,
      'origTaxFlags': instance.origTaxFlags,
      'itemPricing': instance.itemPricing,
      'isVisible': instance.isVisible,
      'hasChildren': instance.hasChildren,
      'prep': instance.prep,
      'takeOutSurcharge': instance.takeOutSurcharge,
      'takeOutSurchargeAmt': instance.takeOutSurchargeAmt,
      'splitData': instance.splitData,
      'printSeparate': instance.printSeparate,
      'liquorToPour': instance.liquorToPour,
      'preppedAt':
          instance.preppedAt?.map((k, e) => MapEntry(k, e?.toIso8601String())),
    };

SplitData _$SplitDataFromJson(Map<String, dynamic> json) => SplitData(
      qty: json['qty'] as int,
      key: json['key'] as String,
    );

Map<String, dynamic> _$SplitDataToJson(SplitData instance) => <String, dynamic>{
      'qty': instance.qty,
      'key': instance.key,
    };

SaleTender _$SaleTenderFromJson(Map<String, dynamic> json) => SaleTender(
      media: json['media'] as int?,
      amount: json['amount'] as int?,
      cardTransactionData: json['cardTransactionData'] == null
          ? null
          : CardTransactionData.fromJson(
              json['cardTransactionData'] as Map<String, dynamic>),
      giftTransactionData: json['giftTransactionData'] == null
          ? null
          : GiftTransactionData.fromJson(
              json['giftTransactionData'] as Map<String, dynamic>),
      checkNum: json['checkNum'] as String?,
      memo: json['memo'] as String?,
      saleTenderFlags: (json['saleTenderFlags'] as List<dynamic>)
          .map((e) => e as int)
          .toList(),
      tipAmount: json['tipAmount'] as int?,
      tipMedia: json['tipMedia'] as int?,
    );

Map<String, dynamic> _$SaleTenderToJson(SaleTender instance) =>
    <String, dynamic>{
      'media': instance.media,
      'amount': instance.amount,
      'checkNum': instance.checkNum,
      'memo': instance.memo,
      'saleTenderFlags': instance.saleTenderFlags,
      'tipAmount': instance.tipAmount,
      'tipMedia': instance.tipMedia,
      'cardTransactionData': instance.cardTransactionData,
      'giftTransactionData': instance.giftTransactionData,
    };

SaleRowDiscount _$SaleRowDiscountFromJson(Map<String, dynamic> json) =>
    SaleRowDiscount(
      type: json['type'] as int,
      title: json['title'] as String? ?? 'Discount',
      amount: json['amount'] as int,
      cashAmount: json['cashAmount'] as int? ?? 0,
      value: json['value'] as int,
      cashValue: json['cashValue'] as int? ?? 0,
    );

Map<String, dynamic> _$SaleRowDiscountToJson(SaleRowDiscount instance) =>
    <String, dynamic>{
      'type': instance.type,
      'amount': instance.amount,
      'cashAmount': instance.cashAmount,
      'value': instance.value,
      'cashValue': instance.cashValue,
      'title': instance.title,
    };

CardTransactionData _$CardTransactionDataFromJson(Map<String, dynamic> json) =>
    CardTransactionData(
      provider: json['provider'] as int? ?? 0,
      uid: json['uid'] as String? ?? "",
      amount: json['amount'] as int? ?? 0,
      authCode: json['authCode'] as String? ?? "",
      batchNo: json['batchNo'] as String? ?? "",
      cardExp: json['cardExp'] as String? ?? "",
      cardPAN: json['cardPAN'] as String? ?? "",
      cardHolder: json['cardHolder'] as String? ?? "",
      cardBrand: json['cardBrand'] as int? ?? 0,
      entryMode: json['entryMode'] as int? ?? 0,
      refECRID: json['refECRID'] as String? ?? "",
      refCustomID: json['refCustomID'] as String? ?? "",
      refTxnID: json['refTxnID'] as String? ?? "",
      tenderType: json['tenderType'] as int? ?? 0,
      transactionMode: json['transactionMode'] as int? ?? 0,
    );

Map<String, dynamic> _$CardTransactionDataToJson(
        CardTransactionData instance) =>
    <String, dynamic>{
      'provider': instance.provider,
      'uid': instance.uid,
      'amount': instance.amount,
      'authCode': instance.authCode,
      'batchNo': instance.batchNo,
      'cardExp': instance.cardExp,
      'cardPAN': instance.cardPAN,
      'cardHolder': instance.cardHolder,
      'cardBrand': instance.cardBrand,
      'entryMode': instance.entryMode,
      'refECRID': instance.refECRID,
      'refCustomID': instance.refCustomID,
      'refTxnID': instance.refTxnID,
      'tenderType': instance.tenderType,
      'transactionMode': instance.transactionMode,
    };

SettleBatchTransactionData _$SettleBatchTransactionDataFromJson(
        Map<String, dynamic> json) =>
    SettleBatchTransactionData(
      date: json['date'] as String?,
      state: json['state'] as String?,
      amount: json['amount'] as String?,
      batchNo: json['batchNo'] as String?,
      totalTranCount: json['totalTranCount'] as String?,
    );

Map<String, dynamic> _$SettleBatchTransactionDataToJson(
        SettleBatchTransactionData instance) =>
    <String, dynamic>{
      'date': instance.date,
      'state': instance.state,
      'amount': instance.amount,
      'batchNo': instance.batchNo,
      'totalTranCount': instance.totalTranCount,
    };

GiftTransactionData _$GiftTransactionDataFromJson(Map<String, dynamic> json) =>
    GiftTransactionData(
      standardHeader: StandardHeader.fromJson(
          json['standardHeader'] as Map<String, dynamic>),
      identification: Identification.fromJson(
          json['identification'] as Map<String, dynamic>),
      expirationDate: json['expirationDate'] as String,
      balances: Balances.fromJson(json['balances'] as Map<String, dynamic>),
      hostMessage: json['hostMessage'] as String,
      printCodes: json['printCodes'] as String,
      errorMessage: json['errorMessage'] == null
          ? null
          : ErrorMessage.fromJson(json['errorMessage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GiftTransactionDataToJson(
        GiftTransactionData instance) =>
    <String, dynamic>{
      'standardHeader': instance.standardHeader,
      'identification': instance.identification,
      'expirationDate': instance.expirationDate,
      'balances': instance.balances,
      'hostMessage': instance.hostMessage,
      'printCodes': instance.printCodes,
      'errorMessage': instance.errorMessage,
    };

StandardHeader _$StandardHeaderFromJson(Map<String, dynamic> json) =>
    StandardHeader(
      text: json['text'] as String,
      requestId: json['requestId'] as String,
      localeId: json['localeId'] as String,
      systemId: json['systemId'] as String,
      clientId: json['clientId'] as String,
      locationId: json['locationId'] as String,
      terminalId: json['terminalId'] as String,
      terminalDateTime: json['terminalDateTime'] as String,
      initiatorType: json['initiatorType'] as String,
      initiatorId: json['initiatorId'] as String,
      initiatorPassword: json['initiatorPassword'] as String,
      externalId: json['externalId'] as String,
      batchId: json['batchId'] as String,
      batchReference: json['batchReference'] as String,
      channel: json['channel'] as String,
      subChannel: json['subChannel'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$StandardHeaderToJson(StandardHeader instance) =>
    <String, dynamic>{
      'text': instance.text,
      'requestId': instance.requestId,
      'localeId': instance.localeId,
      'systemId': instance.systemId,
      'clientId': instance.clientId,
      'locationId': instance.locationId,
      'terminalId': instance.terminalId,
      'terminalDateTime': instance.terminalDateTime,
      'initiatorType': instance.initiatorType,
      'initiatorId': instance.initiatorId,
      'initiatorPassword': instance.initiatorPassword,
      'externalId': instance.externalId,
      'batchId': instance.batchId,
      'batchReference': instance.batchReference,
      'channel': instance.channel,
      'subChannel': instance.subChannel,
      'status': instance.status,
    };

Identification _$IdentificationFromJson(Map<String, dynamic> json) =>
    Identification(
      transactionId: json['transactionId'] as String,
      approvalCode: json['approvalCode'] as String,
      demonstration: json['demonstration'] as String,
      cardNumber: json['cardNumber'] as String,
    );

Map<String, dynamic> _$IdentificationToJson(Identification instance) =>
    <String, dynamic>{
      'transactionId': instance.transactionId,
      'approvalCode': instance.approvalCode,
      'demonstration': instance.demonstration,
      'cardNumber': instance.cardNumber,
    };

Balances _$BalancesFromJson(Map<String, dynamic> json) => Balances(
      balance: (json['balance'] as List<dynamic>?)
          ?.map((e) => Balance.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BalancesToJson(Balances instance) => <String, dynamic>{
      'balance': instance.balance,
    };

Balance _$BalanceFromJson(Map<String, dynamic> json) => Balance(
      valueCode: json['valueCode'] as String,
      amount: json['amount'] as String,
      difference: json['difference'] as String,
      exchangeRate: json['exchangeRate'] as String,
    );

Map<String, dynamic> _$BalanceToJson(Balance instance) => <String, dynamic>{
      'valueCode': instance.valueCode,
      'amount': instance.amount,
      'difference': instance.difference,
      'exchangeRate': instance.exchangeRate,
    };

ErrorMessage _$ErrorMessageFromJson(Map<String, dynamic> json) => ErrorMessage(
      rejectionId: json['rejectionId'] as String?,
      errorCode: json['errorCode'] as String?,
      briefMessage: json['briefMessage'] as String?,
      inDepthMessage: json['inDepthMessage'] as String?,
    );

Map<String, dynamic> _$ErrorMessageToJson(ErrorMessage instance) =>
    <String, dynamic>{
      'rejectionId': instance.rejectionId,
      'errorCode': instance.errorCode,
      'briefMessage': instance.briefMessage,
      'inDepthMessage': instance.inDepthMessage,
    };
