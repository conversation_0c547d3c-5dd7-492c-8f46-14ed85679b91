//---------------------------------------------------CONFIG SERVICE
// ignore_for_file: always_specify_types, avoid_dynamic_calls
import 'dart:ui';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/payment.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String CONFIG_FIELDS_FRAGMENT = '''
  config
  type
  document
  created_at
  created_by
  updated_by
  updated_at
''';

const String JSON_RECORD_FIELDS_FRAGMENT = '''
  record_key
  updated_at
  document
''';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

const String UPSERT_JSON_RECORD_MUTATION = '''
  mutation UPSERT_JSON_RECORD(\$json_record: json_record_insert_input!) {
    insert_json_record_one(object: \$json_record, on_conflict: {constraint: json_record_pkey, update_columns: [ 
        record_key
        updated_at
        document
      ]}
    ) {
      $JSON_RECORD_FIELDS_FRAGMENT
    }
  }
''';

const String UPDATE_CONFIG_BY_PK_MUTATION = '''
  mutation UPDATE_CONFIG_BY_PK(\$configPk: uuid!, \$config: config_set_input) {
    update_config_by_pk(pk_columns: {config: \$configPk}, _set: \$config){
      $CONFIG_FIELDS_FRAGMENT
    }
  }
''';

const String GET_CUSTOMER_QUERY = '''
  query GET_CUSTOMER_QUERY(\$id: uuid = "") {
    customer(where: {customer: {_eq: \$id}}) {
      created_by
      created_at
      customer
      document
      updated_by
      updated_at
      search_customer_full_name
    }
  }
''';

const String GET_MERCHANT_CONFIG_QUERY = '''
  query GET_MERCHANT_CONFIG_QUERY{
    json_record(where: {record_key: {_eq: "merchant"}}) {
      record_key
      updated_at
      document
    }
  }
''';

final Logger _logger = Logger('ConfigService');

class ConfigService extends GetxService {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();

  MerchantJsonRecord merchantConfig = MerchantJsonRecord.empty();
  EcomSettingJsonRecord ecomConfig = EcomSettingJsonRecord.empty();
  List<Tax> salesTaxList = <Tax>[];
  List<RoomsJsonRecordRoom> roomList = <RoomsJsonRecordRoom>[];
  List<FlowElement> textList = <FlowElement>[];
  List<FlowElement> tableList = <FlowElement>[];
  List<Section> sectionList = <Section>[];
  Map<String, Section> tableToSection = <String, Section>{};

  Future<ConfigService> init() async {
    try {
      await getRecords();
    } catch (err, stack) {
      _logger.severe(
        "Error initializing config service",
        err,
        stack,
      );
      _notificationService.error("Error initializing config service");
    }
    return this;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getRecords() async {
    merchantConfig = (await getMerchantConfig()).fold(
      (ServiceError e) {
        _notificationService.error("Error getting merchant config");
        return MerchantJsonRecord.empty();
      },
      (MerchantJsonRecord m) => m,
    );
    ecomConfig = (await getEcomConfig()).fold(
      (ServiceError e) {
        _notificationService.error("Error getting ecommerce config");
        return EcomSettingJsonRecord.empty();
      },
      (EcomSettingJsonRecord e) => e,
    );
    await _getTableAndSectionConfig();
    tableToSection = _getTableSections();
    salesTaxList = (await getSalesTaxConfig()).fold(
      (l) {
        _notificationService.error("Error getting sales tax config");
        return <Tax>[];
      },
      (SalesTaxJsonRecord r) => r.document.taxes,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, SystemDeviceJsonRecordTerminal>> getSystemDeviceTerminalByIndex({
    required int index,
  }) async {
    try {
      final SystemDeviceJsonRecordDocument doc = (await _identityService.getSystemDeviceDocument()).fold(
        (ServiceError e) {
          _notificationService.error("Error getting system device config");
          throw e;
        },
        (SystemDeviceJsonRecordDocument d) => d,
      );

      return Right(
        doc.terminal.firstWhere((element) => element.idx == index),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error fetching terminal config! SystemDeviceJsonRecord failed to parse correctly.Note! This can happen if you have not migrated your database to the berg version. See https://github.com/Round2POS/hyperion/blob/cb036361e3b4e685d243007db53e76ebba4484b6/terminal/services/hasura/migrations/default/1720625625868_add_berg_data_to_terminals/up.sql.",
        err,
        stack,
      );
      return Left<ServiceError, SystemDeviceJsonRecordTerminal>(
        ServiceError("Failed to get terminal config: $err"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, MerchantJsonRecord>> getMerchantConfig() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'merchant',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List).isEmpty) {
        throw "No Json Records Found";
      }
      final MerchantJsonRecord merchantRecord = MerchantJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      return Right(merchantRecord);
    } catch (err, stack) {
      _logger.severe(
        "error fetching merchant config",
        err,
        stack,
      );
      return Left<ServiceError, MerchantJsonRecord>(
        ServiceError("Failed to get merchant config!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, EcomSettingJsonRecord>> getEcomConfig() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'ecomSetting',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List).isEmpty) {
        return Right(EcomSettingJsonRecord.empty());
      }
      final EcomSettingJsonRecord ecomRecord = EcomSettingJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      return Right(ecomRecord);
    } catch (err, stack) {
      _logger.severe(
        "error fetching ecommerce config",
        err,
        stack,
      );
      return Left<ServiceError, EcomSettingJsonRecord>(
        ServiceError("Failed to get ecommerce config!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, SalesTaxJsonRecord>> getSalesTaxConfig() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'salesTax',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List).isEmpty) {
        throw "No Json Records Found";
      }
      final SalesTaxJsonRecord salesTaxConfig = SalesTaxJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      return Right(salesTaxConfig);
    } catch (err, stack) {
      _logger.severe(
        "error fetching salesTax config",
        err,
        stack,
      );
      return Left<ServiceError, SalesTaxJsonRecord>(
        ServiceError("Failed to get salesTax config!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///

  Future<Either<ServiceError, SystemSettingJsonRecord>> getSystemSettings() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'systemSetting',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List<dynamic>).isEmpty) {
        throw "No Json Records Found";
      }

      final SystemSettingJsonRecord systemConfig = SystemSettingJsonRecord.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );

      return Right<ServiceError, SystemSettingJsonRecord>(systemConfig);
    } catch (err, stack) {
      _logger.severe(
        "error fetching system setting",
        err,
        stack,
      );
      return Left<ServiceError, SystemSettingJsonRecord>(
        ServiceError("Failed to get system setting!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, EcomSettingJsonRecord>> upsertEcomConfig(EcomSettingJsonRecord ecomJsonRecord) async {
    try {
      final List<String> fieldsToSanitize = ['updated_at'];

      final Map<String, dynamic> santizedConfig = Helpers.sanitizeEntity(
        ecomJsonRecord.toJson(),
        fieldsToSanitize,
      );

      final QueryResult<Object?> upsertConfigResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPSERT_JSON_RECORD_MUTATION),
          variables: <String, dynamic>{
            "json_record": santizedConfig,
          },
        ),
      );

      if (upsertConfigResult.hasException) {
        throw upsertConfigResult.exception.toString();
      }

      final EcomSettingJsonRecord updatedConfig = EcomSettingJsonRecord.fromJson(
        upsertConfigResult.data!['insert_json_record_one'] as Map<String, dynamic>,
      );

      return Right<ServiceError, EcomSettingJsonRecord>(updatedConfig);
    } catch (err, stack) {
      _logger.severe(
        "error upserting ecom config",
        err,
        stack,
      );
      return Left<ServiceError, EcomSettingJsonRecord>(
        ServiceError("Failed to update ecom config!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, RegisterMenusJsonRecord>> getMenuConfig() async {
    try {
      final RegisterMenusJsonRecord emptyRecord = RegisterMenusJsonRecord.empty();

      emptyRecord.document = Constants.defaultRegisterMenus;

      RegisterMenusJsonRecord registerMenuConfigs = emptyRecord;

      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'registerMenus',
          },
        ),
      );

      final List<dynamic> configList = configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        if (configList.first["document"] == null) {
          configList.first["document"] = registerMenuConfigs.document!.toJson();
        }
        registerMenuConfigs = configList
            .map(
              (config) => RegisterMenusJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;
      }

      return Right(registerMenuConfigs);
    } catch (err, stack) {
      _logger.severe(
        "error fetching toolbar config",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get toolbar config!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, SectionsJsonRecord>> getSectionsConfig() async {
    try {
      SectionsJsonRecord sectionsRecord = SectionsJsonRecord.empty();

      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'sections',
          },
        ),
      );

      final List<dynamic> configList = configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        sectionsRecord = configList
            .map(
              (config) => SectionsJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;
      }

      return Right(sectionsRecord);
    } catch (err, stack) {
      _logger.severe(
        "error fetching sections config",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get sections config!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, BreaksJsonRecord>> getBreaksConfig() async {
    try {
      BreaksJsonRecord breaksRecord = BreaksJsonRecord.empty();

      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'breaks',
          },
        ),
      );

      final List<dynamic> configList = configResult.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        breaksRecord = configList
            .map(
              (config) => BreaksJsonRecord.fromJson(
                config as Map<String, dynamic>,
              ),
            )
            .first;
      }

      return Right(breaksRecord);
    } catch (err, stack) {
      _logger.severe(
        "error fetching breaks config",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get breaks config!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<PriceSchedule>>> getPriceSchedules() async {
    try {
      List<PriceSchedule> psList = <PriceSchedule>[];

      final QueryResult<Object?> result = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'priceSchedule',
          },
        ),
      );

      final List<dynamic> configList = result.data!['json_record'] as List;

      if (configList.isNotEmpty) {
        final PriceScheduleJsonRecord psResult = PriceScheduleJsonRecord.fromJson(
          configList.first as Map<String, dynamic>,
        );

        psList = psResult.document.discs;
      }

      return Right(
        psList,
      );
    } catch (err, stack) {
      _logger.severe(
        "Error getting price schedules.",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get price schedules!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, PaymentProvider>> getPaymentProvider() async {
    final Either<ServiceError, MerchantJsonRecord> merchantConfigResult = await getMerchantConfig();
    return merchantConfigResult.fold(
      (ServiceError err) => Left<ServiceError, PaymentProvider>(err),
      (MerchantJsonRecord merchantConfig) {
        final PaymentProvider paymentProvider = PaymentProvider.values[merchantConfig.document.paymentDeviceType];
        return Right<ServiceError, PaymentProvider>(paymentProvider);
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _getTableAndSectionConfig() async {
    try {
      final QueryResult<Object?> roomResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{"record_key": "rooms"},
        ),
      );
      final QueryResult<Object?> sectionResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_JSON_RECORD_BY_RECORD_KEY_QUERY),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{"record_key": "sections"},
        ),
      );

      RoomsJsonRecord tableConfig = RoomsJsonRecord.empty();
      SectionsJsonRecord sectionConfig = SectionsJsonRecord.empty();

      final List<dynamic> roomResList = roomResult.data!['json_record']! as List<dynamic>;
      final List<dynamic> sectionResList = sectionResult.data!['json_record']! as List<dynamic>;

      if (roomResList.isNotEmpty) {
        if (roomResList.first["document"] == null) {
          roomResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
        }
        tableConfig = RoomsJsonRecord.fromJson(
          roomResList.first as Map<String, dynamic>,
        );
      }

      if (sectionResList.isNotEmpty) {
        if (sectionResList.first["document"] == null) {
          sectionResList.first["document"] = RoomsJsonRecord.empty().document.toJson();
        }
        sectionConfig = SectionsJsonRecord.fromJson(
          sectionResList.first as Map<String, dynamic>,
        );
      }

      roomList = tableConfig.document.rooms;
      sectionList = sectionConfig.document.sections;

      roomList.sort(
        (RoomsJsonRecordRoom a, RoomsJsonRecordRoom b) => a.idx.compareTo(b.idx),
      );
      sectionList.sort(
        (Section a, Section b) => a.idx.compareTo(b.idx),
      );
      textList = tableConfig.document.text
          .map(
            (RoomsJsonRecordText element) => FlowElement(
              position: Offset(
                (element.X + (24 ~/ 2)) ~/ 24 * 24,
                (element.Y + (24 ~/ 2)) ~/ 24 * 24,
              ),
              size: Size(
                (element.desc.length * 12) + 12,
                48,
              ),
              desc: element.desc,
              kind: ElementKind.text,
              rotation: element.rotation ?? 0,
              seatCnt: 0,
              idx: element.idx,
              fgnd: element.fgnd,
              bgnd: element.bgnd,
              roomIdx: element.roomIdx,
            ),
          )
          .toList();
      tableList = tableConfig.document.tables
          .map(
            (RoomsJsonRecordTable element) => FlowElement(
              position: Offset(
                (element.X + (24 ~/ 2)) ~/ 24 * 24,
                (element.Y + (24 ~/ 2)) ~/ 24 * 24,
              ),
              size: Size(element.width, element.height),
              desc: element.desc,
              kind: element.shape < 3 ? ElementKind.oval : ElementKind.rectangle,
              rotation: element.rotation,
              seatCnt: element.seatCnt,
              idx: element.idx,
              sectIdx: element.sectIdx,
              roomIdx: element.roomIdx,
            ),
          )
          .toList();
    } catch (err, stack) {
      _logger.severe(
        "error fetching merchant config",
        err,
        stack,
      );
    }
  }

  Section getSectionFromTableDesc(String tableDesc) {
    final Section emptySect = Section(desc: "", idx: 0);
    final FlowElement? table = tableList.firstWhereOrNull(
      (FlowElement table) => table.desc == tableDesc,
    );
    if (table == null) return emptySect;

    return sectionList.firstWhere(
      (section) => section.idx == table.sectIdx,
      orElse: () => emptySect,
    );
  }

  Map<String, Section> _getTableSections() {
    final Map<String, Section> tableToSection = {};
    for (final FlowElement table in tableList) {
      tableToSection[table.desc] = getSectionFromTableDesc(table.desc);
    }
    return tableToSection;
  }

  Future<void> toggleOnlineOrdering() async {
    const String errMsg = "Error toggling online ordering";
    try {
      await getRecords();
      ecomConfig.document.ecomEnabled = !ecomConfig.document.ecomEnabled;
      (await upsertEcomConfig(ecomConfig)).fold(
        (ServiceError e) => _notificationService.error(errMsg),
        (r) => _notificationService.success("Online ordering ${ecomConfig.document.ecomEnabled ? "enabled" : "disabled"}"),
      );
    } catch (err, stack) {
      _logger.severe(errMsg, err, stack);
      _notificationService.error(errMsg);
    }
  }
}
