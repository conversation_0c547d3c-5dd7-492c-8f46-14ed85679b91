import 'dart:async';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('EmployeeService');

Map<String, DateTime> lastSignIn = <String, DateTime>{};

const String GET_TIMECARDS_QUERY = '''
  query GET_TIMECARDS {
    timecard(order_by: [{emp_id: asc}, {punch_at: desc}], distinct_on: emp_id) {
      punch_type
      timecard
      round_at
      punch_at
      break_idx
      emp_id
      job_code
    }
  }
''';

const String GET_EMPLOYEES_USING_IDS = '''
  query GET_EMPLOYEES(\$ids: [Int!]) {
    employee(where: {is_active: {_eq: true}, _and: {is_active: {}, id: {_in: \$ids}}}) {
      created_at
      created_by
      document
      employee
      employee_class
      employee_full_name
      id
      is_active
      password
      updated_at
      updated_by
    }
  }
''';

const String GET_EMPLOYEE_USING_ID = '''
  query GET_EMPLOYEE_BY_ID(\$id: Int!){
    employee(where:{id:{_eq: \$id}}){
      employee
      employee_class
      document
      created_at
      created_by
      updated_at
      updated_by
      password
      id
      is_active
    }
  }
''';

const String GET_JOB_CODES_QUERY = '''
  subscription GET_JSON_RECORD {
    json_record(where: {record_key: {_eq: "systemSetting"}}) {
      record_key
      document
      updated_at
    }
  }
''';

const String GET_FOH_CLASSES_QUERY = '''
  query GET_FOH_CLASSES {
    employee_class(where: {permissions: {permissionActionByPermissionAction: {title: {_eq: "Access"}}, permissionObjectByPermissionObject: {title: {_eq: "Front Of House"}}}}) {
      document
      created_at
      created_by
      clearance
      employee_class
      title
      updated_at
      updated_by
    }
  }
''';

class EmployeeService extends GetxService {
  final GraphqlService _graphqlService = Get.find();
  final Map<int, DateTime> lastSignOff = <int, DateTime>{};

  Future<List<Employee>> getClockedInEmployees({
    bool quickSignIn = false,
    int quickSignSection = 0,
    int quickSignMinutes = 0,
    DateTime? start,
    DateTime? end,
  }) async {
    try {
      List<String> allowedClasses = <String>[];

      final QueryResult<Object?> cardRes = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_TIMECARDS_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if (cardRes.hasException) {
        throw cardRes.exception.toString();
      }

      List<TimeCard> cards = (cardRes.data!['timecard']! as List<dynamic>)
          .map(
            (dynamic card) => TimeCard.fromJson(
              card as Map<String, dynamic>,
            ),
          )
          .toList();

      // Filter for clocked in employees that have not yet clocked out.
      // This is defined in the DB as a new entry with punch_type 2 after a previous entry with punch_type 1.
      if (start != null) {
        cards = cards.where((TimeCard c) {
          final DateTime? punchAt = c.punch_at != null ? DateTime.parse(c.punch_at!) : null;
          // punchAt is the timestamp of the punch in the DB
          return punchAt != null && punchAt.isAfter(start);
        }).toList();
      }

      if (end != null) {
        cards = cards.where((TimeCard c) {
          final DateTime? punchAt = c.punch_at != null ? DateTime.parse(c.punch_at!) : null;
          // punchAt is the timestamp of the punch in the DB
          return punchAt != null && punchAt.isBefore(end);
        }).toList();
      }

      // punch_type "enum" is not true enum. (Defined in terminal/services/hardware/Include/Project.h)
      // 1: Clock In
      // 2: Clock Out
      // 3: Break Start
      // 4: Break End
      cards = cards.where((TimeCard c) => c.punch_type == 1).toList();

      if (cards.isEmpty) return <Employee>[];

      if (quickSignIn) {
        final QueryResult<Object?> classRes = await _graphqlService.client.query(
          QueryOptions<Object?>(
            document: g.parseString(GET_FOH_CLASSES_QUERY),
            fetchPolicy: FetchPolicy.noCache,
          ),
        );
        if (classRes.hasException) {
          throw classRes.exception.toString();
        }
        // ignore: avoid_dynamic_calls
        allowedClasses = (classRes.data!["employee_class"] as List<dynamic>).map((dynamic c) => c["employee_class"].toString()).toList();
      }

      if (cards.isEmpty) return <Employee>[];

      if (quickSignIn && quickSignSection > 0) {
        final QueryResult<Object?> jobRes = await _graphqlService.client.query(
          QueryOptions<Object?>(
            document: g.parseString(GET_JOB_CODES_QUERY),
            fetchPolicy: FetchPolicy.noCache,
          ),
        );
        if (jobRes.hasException) {
          throw jobRes.exception.toString();
        }
        final List<int> codes = SystemSettingJsonRecord.fromJson(((jobRes.data!)["json_record"] as List<dynamic>).first as Map<String, dynamic>)
            .document
            .jobCodes
            .where((SystemSettingJsonRecordJobCode c) => c.section == quickSignSection)
            .map((SystemSettingJsonRecordJobCode c) => c.index)
            .toList();
        cards = cards.where((TimeCard c) => codes.contains(c.job_code)).toList();
      }

      if (cards.isEmpty) return <Employee>[];

      if (quickSignIn && quickSignMinutes > 0) {
        final DateTime now = DateTime.now();
        cards = cards
            .where((TimeCard c) => lastSignOff[c.emp_id] != null && now.difference(lastSignOff[c.emp_id] ?? now).inMinutes < quickSignMinutes)
            .toList();
      }

      if (cards.isEmpty) return <Employee>[];

      final QueryResult<Object?> empRes = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_EMPLOYEES_USING_IDS),
          fetchPolicy: FetchPolicy.noCache,
          variables: <String, dynamic>{
            "ids": cards.map((TimeCard c) => c.emp_id).toList(),
          },
        ),
      );

      if (empRes.hasException) {
        throw empRes.exception.toString();
      }

      return (empRes.data!['employee']! as List<dynamic>)
          .map(
            (dynamic emp) => Employee.fromJson(
              emp as Map<String, dynamic>,
            ),
          )
          .where((Employee e) => !quickSignIn || allowedClasses.contains(e.employee_class))
          .toList();
    } catch (err, stack) {
      _logger.severe(
        "Error Fetching Employees",
        err,
        stack,
      );
      return <Employee>[];
    }
  }

  Future<Either<ServiceError, Employee>> getEmployeeById(int id) async {
    try {
      // Check if the employee is the online order employee
      if (id == Constants.onlineOrderEmployee.id) return Right<ServiceError, Employee>(Constants.onlineOrderEmployee);
      final QueryResult<Object?> empRes = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_EMPLOYEE_USING_ID),
          fetchPolicy: FetchPolicy.noCache,
          variables: <String, dynamic>{
            "id": id,
          },
        ),
      );
      if (empRes.hasException) {
        throw empRes.exception.toString();
      }
      final List<Employee> employeeList = (empRes.data!['employee']! as List<dynamic>)
          .map(
            (dynamic emp) => Employee.fromJson(
              emp as Map<String, dynamic>,
            ),
          )
          .toList();
      if (employeeList.isEmpty) {
        return Left<ServiceError, Employee>(ServiceError("Employee not found"));
      }
      return Right<ServiceError, Employee>(employeeList.first);
    } catch (err, stack) {
      _logger.severe(
        "Error Fetching Employees",
        err,
        stack,
      );
      return Left<ServiceError, Employee>(ServiceError(err.toString()));
    }
  }
}
