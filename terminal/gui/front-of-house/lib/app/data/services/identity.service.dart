// ignore_for_file: always_specify_types, avoid_dynamic_calls

import 'package:backoffice/app/data/enums/receipt_printer/type.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/system.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_SYSTEM_DEVICE_QUERY = '''
  query GET_SYSTEM_SETTING {
    json_record(where: {record_key: {_eq: "systemDevice"}}) {
      document
      record_key
      updated_at
    }
  }
''';

const String GET_CASHIER_RECORD = '''
  query GET_JSON_RECORD (\$record_key: String) {
    json_record(where: {record_key: {_eq: \$record_key}}) {
      record_key
      document
      updated_at
    }
  }
''';

const String GET_EMPLOYEE_QUERY = '''
  query GET_EMPLOYEE_QUERY(\$id: Int!){
    employee(where:{id:{_eq:\$id}}){
      employee
      employee_class
      document
      created_at
      created_by
      updated_at
      updated_by
      password
      id
      is_active
    }
  }
''';

const String UPSERT_USER_DOCUMENT = '''
  mutation UPDATE_EMPLOYEE(\$employee: uuid = "", \$document: jsonb) {
    update_employee_by_pk(pk_columns: {employee: \$employee}, _set: {document: \$document}) {
      employee
      employee_class
      document
      created_at
      created_by
      updated_at
      updated_by
      password
      id
      is_active
    }
  }
  ''';

const String UPDATE_CASHIER_RECORD = '''
 mutation UPDATE_BANNER_MSG(\$document: jsonb) {
  update_json_record(where: {record_key: {_eq: "cashier"}}, _append: {document: \$document}) {
    returning {
      document
      record_key
      updated_at
    }
  }
}
''';

final Logger _logger = Logger("identityservice");

class IdentityService extends GetxService {
  final SystemService _systemService = Get.find();
  Employee currentEmployee = Employee.empty();

  late int terminalNumber = 2;
  late int terminalSection = 0;
  late int terminalPriceLevel = 0;
  late int terminalSkipDevices = 0;
  late bool terminalQuickSignIn = false;
  late int terminalQuickSignMinutes = 0;
  late int terminalQuickSignSection = 0;
  late int terminalBergMethod = 0;
  late int terminalBergType = 0;
  late int terminalAutoSignOutSeconds = 0;
  int? remoteTermIdx;
  Map<int, String> terminalDescs = <int, String>{};
  List<SystemDeviceJsonRecordTerminal> terminalList = <SystemDeviceJsonRecordTerminal>[];
  late List<TerminalDrawer> deviceDrawers = [];
  TerminalDrawer? employeeDrawer;
  String? currentCashier;

  final GraphqlService _graphqlService = Get.find();

  Future<IdentityService> init() async {
    await getTerminalInfo();
    await setCurrentCashier();
    return this;
  }

  Future<void> setCurrentCashier() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_CASHIER_RECORD),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'cashier',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List<dynamic>).isEmpty) {
        throw "No Json Records Found";
      }

      final JsonRecordCashier systemConfig = JsonRecordCashier.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      currentCashier = systemConfig.document.currentCashier;
    } catch (err, stack) {
      logger.shout("error setting current cashier", err, stack);
    }
  }

  Future<Either<ServiceError, JsonRecordCashier>> getCashierDocument() async {
    try {
      final QueryResult<Object?> configResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_CASHIER_RECORD),
          fetchPolicy: FetchPolicy.noCache,
          variables: const <String, dynamic>{
            "record_key": 'cashier',
          },
        ),
      );

      if ((configResult.data!['json_record']! as List<dynamic>).isEmpty) {
        throw "No Json Records Found";
      }

      final JsonRecordCashier systemConfig = JsonRecordCashier.fromJson(
        configResult.data!['json_record'][0] as Map<String, dynamic>,
      );
      return Right(systemConfig);
    } catch (err, stack) {
      logger.shout("error getting casher record", err, stack);
      return Left(ServiceError("failed to get casher record"));
    }
  }

  Future<void> updateCurrentCashier(String? setCashier) async {
    final Either<ServiceError, JsonRecordCashier> sysSettingRes = await getCashierDocument();
    await sysSettingRes.fold((ServiceError l) {
      logger.shout("error getting system settings");
    }, (JsonRecordCashier r) async {
      r.document.currentCashier = setCashier;
      currentCashier = setCashier;
      try {
        final QueryResult<Object> updateSettingsResult = await _graphqlService.client.mutate(
          MutationOptions<Object>(
            document: g.parseString(
              UPDATE_CASHIER_RECORD,
            ),
            variables: <String, dynamic>{
              "document": r.document,
            },
          ),
        );

        if (updateSettingsResult.hasException) {
          throw "error setting cashier";
        }
      } catch (e, stack) {
        logger.shout("error setting cashier", e, stack);
      }
    });
  }

  Future<void> getTerminalInfo() async {
    terminalList = (await getSystemDeviceDocument()).fold(
      (ServiceError e) => throw e,
      (SystemDeviceJsonRecordDocument d) => d.terminal,
    );

    if (terminalList.isEmpty) throw "No terminals exist in database!";

    final networkInformation = await _systemService.loadNetworkInformation();

    if (networkInformation.envMacAddress != networkInformation.systemMacAddress) {
      throw "env and system MAC address do not match ${networkInformation.envMacAddress} != ${networkInformation.systemMacAddress}";
    }

    bool terminalFound = false;
    for (final terminal in terminalList) {
      terminalDescs[terminal.idx] = terminal.desc;

      if (networkInformation.envMacAddress == terminal.MAC) {
        terminalFound = true;

        terminalNumber = terminal.idx;
        terminalSection = terminal.section;
        terminalPriceLevel = terminal.priceLevel;
        terminalSkipDevices = terminal.skipDevices;
        terminalQuickSignIn = terminal.quickSignIn;
        terminalQuickSignMinutes = terminal.quickSignMinutes;
        terminalQuickSignSection = terminal.quickSignSection;
        terminalBergMethod = terminal.liqCtl.dispenseMethod;
        terminalBergType = terminal.liqCtl.type;
        terminalAutoSignOutSeconds = terminal.autoSignOutSeconds;
        deviceDrawers = terminal.cashDrawers;
        if (terminal.rcptPrn.type == ReceiptPrinterType.REMOTE_PRINT.index) {
          remoteTermIdx = terminal.remotePrintIdx;
        }
      }
    }

    if (!terminalFound) {
      throw "this terminal(env: ${networkInformation.envMacAddress} system: ${networkInformation.systemMacAddress}) is not found in the database!";
    }
  }

  Future<Option<ServiceError>> setCurrentUser({
    required Employee employee,
  }) async {
    try {
      currentEmployee = employee;
      final int employeeDrawerIdx = employee.document.terminalDrawers[terminalNumber.toString()] ?? -1;
      employeeDrawer = deviceDrawers.firstWhereOrNull((TerminalDrawer d) => d.idx == employeeDrawerIdx);
      return none();
    } catch (err, stack) {
      logger.shout("error setting current user", err, stack);
      return some(ServiceError("Failed to set current user!"));
    }
  }

  Future<void> refreshCurrentUser() async {
    if (currentEmployee.id == null) return;
    try {
      final QueryResult<Object?> employeeResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_EMPLOYEE_QUERY),
          variables: {
            "id": currentEmployee.id,
          },
        ),
      );

      if (employeeResult.hasException) {
        throw employeeResult.exception.toString();
      }
      if (employeeResult.data == null) throw "Error updating user!";

      final List<Employee> employees = (employeeResult.data!['employee'] as List)
          .map(
            (dynamic employee) => Employee.fromJson(employee as Map<String, dynamic>),
          )
          .toList();

      if (employees.isEmpty) {
        throw "Can't find current user!!";
      }

      if (!employees.first.is_active) {
        throw "Current user is inactive!";
      }

      await setCurrentUser(employee: employees.first);
    } catch (err, stack) {
      logger.shout("error updating user data", err, stack);
    }
  }

  Future<void> updateCurrentUserDocument() async {
    if (currentEmployee.id == null) return;
    try {
      final QueryResult<Object?> employeeResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(UPSERT_USER_DOCUMENT),
          variables: {
            "employee": currentEmployee.employee,
            "document": currentEmployee.document,
          },
        ),
      );

      if (employeeResult.hasException) {
        throw employeeResult.exception.toString();
      }
      if (employeeResult.data == null) throw "Error updating user!";

      if (employeeResult.data!['update_employee_by_pk'] == null) {
        throw "Can't find current user!!";
      }

      final Employee employee = Employee.fromJson(
        employeeResult.data!['update_employee_by_pk'] as Map<String, dynamic>,
      );

      if (!employee.is_active) {
        throw "Current user is inactive!";
      }

      await setCurrentUser(employee: employee);
    } catch (err, stack) {
      logger.shout("error updating user data", err, stack);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, SystemDeviceJsonRecordDocument>> getSystemDeviceDocument() async {
    try {
      final QueryResult<Object?> systemDeviceResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_SYSTEM_DEVICE_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if ((systemDeviceResult.data!['json_record']! as List<dynamic>).isEmpty) {
        throw "No System Device Record Found";
      }

      final SystemDeviceJsonRecord systemDevice = SystemDeviceJsonRecord.fromJson(
        systemDeviceResult.data!['json_record'][0] as Map<String, dynamic>,
      );

      return Right(systemDevice.document);
    } catch (err, stack) {
      _logger.severe(
        "Error fetching prep devices",
        err,
        stack,
      );
      return Left<ServiceError, SystemDeviceJsonRecordDocument>(
        ServiceError("Failed to get devices!"),
      );
    }
  }
}
