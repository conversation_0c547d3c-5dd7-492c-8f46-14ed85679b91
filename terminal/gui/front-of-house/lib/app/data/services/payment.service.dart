// ignore_for_file: always_specify_types

import 'dart:convert';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/payment.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

final Logger _logger = Logger('PaymentService');

class PaymentService {
  final ConfigService _configService = Get.find();
  PaymentProvider _provider = PaymentProvider.Unknown;

  Future<PaymentService> init() async {
    final Either<ServiceError, PaymentProvider> paymentProvider = await _configService.getPaymentProvider();
    _provider = paymentProvider.fold((l) {
      _logger.severe("error getting payment provider", l);
      return PaymentProvider.Unknown;
    }, (r) {
      return r;
    });
    return this;
  }

  Future<Either<ErrorResponse, CardTransactionData>> sale({
    required int amount,
    required PaymentMediaType tenderType,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "amount": amount,
        "provider": _provider.index,
        "tenderType": tenderType.index,
      };

      final http.Response resp = await http.post(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing sale", e);
      return left(ErrorResponse(message: "error processing sale: $e"));
    }
  }

  Future<Either<ErrorResponse, CardTransactionData>> refund({
    int amount = 0,
    required PaymentMediaType tenderType,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "amount": amount,
        "provider": _provider.index,
        "tenderType": tenderType.index,
      };

      final http.Response resp = await http.patch(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing refund", e);
      return left(ErrorResponse(message: "error processing refund: $e"));
    }
  }

  Future<Either<ErrorResponse, CardTransactionData>> txnVoid({
    required int settledTerminalNumber,
    required String refTxnId,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "refTxnId": refTxnId,
        "settledTerminalNumber": settledTerminalNumber,
        "provider": _provider.index,
      };

      final http.Response resp = await http.delete(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error voiding transaction", e);
      return left(ErrorResponse(message: "error voiding transaction: $e"));
    }
  }

  Future<Either<ErrorResponse, NMIResponseData>> nmiVoid({
    required String refTxnId,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "transactionID": refTxnId,
      };

      final http.Response resp = await http.post(
        Uri.parse("${Constants.baseUrl}/sale/online/nmi/void"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      final NMIResponseData data = NMIResponseData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>);

      if (data.response_code != "100") {
        return left(ErrorResponse(message: "NMI void failed: ${data.responsetext}"));
      }

      return right(data);
    } catch (e) {
      _logger.severe("error voiding transaction", e);
      return left(ErrorResponse(message: "error voiding transaction: $e"));
    }
  }

  Future<Either<ErrorResponse, CardTransactionData>> tipAdjust({
    required int amount,
    required String refTxnId,
    required int settledTerminalNumber,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "amount": amount,
        "refTxnId": refTxnId,
        "settledTerminalNumber": settledTerminalNumber,
        "provider": _provider.index,
      };

      final http.Response resp = await http.put(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error adjusting tip", e);
      return left(ErrorResponse(message: "error adjusting tip: $e"));
    }
  }

  Future<Either<ErrorResponse, CardTransactionData>> authorize({
    required int amount,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "amount": amount,
        "provider": _provider.index,
      };

      final http.Response resp = await http.post(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions/authorize"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing pre auth", e);
      return left(ErrorResponse(message: "error authorizing: $e"));
    }
  }

  Future<Either<ErrorResponse, CardTransactionData>> capture({
    required int amount,
    required String refTxnId,
    required int settledTerminalNumber,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "amount": amount,
        "refTxnId": refTxnId,
        "settledTerminalNumber": settledTerminalNumber,
        "provider": _provider.index,
      };

      final http.Response resp = await http.put(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/transactions/authorize"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(CardTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error completing preAuth", e);
      return left(ErrorResponse(message: "error capturing authorization: $e"));
    }
  }

  Future<Option<ErrorResponse>> settleBatch() async {
    try {
      final Map<String, dynamic> payload = {
        "provider": _provider.index,
      };

      final http.Response resp = await http.put(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/batch"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return some(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return none();
    } catch (e) {
      _logger.severe("error processing batchSettle", e);
      return some(ErrorResponse(message: "error settling batch: $e"));
    }
  }

  Future<Either<ErrorResponse, GiftTransactionData>> giftInquiry({required String cardNumber}) async {
    try {
      final http.Response resp = await http.get(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/factorfour/gift?cardNumber=$cardNumber"),
        headers: {
          "Content-Type": "application/json",
        },
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(GiftTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing giftInquiry", e);
      return left(ErrorResponse(message: "error inquiring gift card: $e"));
    }
  }

  Future<Either<ErrorResponse, GiftTransactionData>> giftIssue({
    required String cardNumber,
    required int amount,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "cardNumber": cardNumber,
        "amount": amount,
      };

      final http.Response resp = await http.post(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/factorfour/gift"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(GiftTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing giftIssue", e);
      return left(ErrorResponse(message: "error issuing gift card: $e"));
    }
  }

  Future<Either<ErrorResponse, GiftTransactionData>> giftRedemption({
    required String cardNumber,
    required int amount,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "cardNumber": cardNumber,
        "amount": amount,
      };

      final http.Response resp = await http.put(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/factorfour/gift"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(GiftTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing giftRedemption", e);
      return left(ErrorResponse(message: "error redeeming gift card: $e"));
    }
  }

  Future<Either<ErrorResponse, GiftTransactionData>> giftVoid({
    required String cardNumber,
    required String transactionId,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "cardNumber": cardNumber,
        "transactionId": transactionId,
      };

      final http.Response resp = await http.delete(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/factorfour/gift"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(GiftTransactionData.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error processing giftVoid", e);
      return left(ErrorResponse(message: "error voiding gift card: $e"));
    }
  }

  Future<Either<ErrorResponse, SalePaymentSessionSuccessResponse>> generateSalePaymentSession({
    required String sale,
  }) async {
    try {
      final Map<String, dynamic> payload = {
        "sale": sale,
      };

      final http.Response resp = await http
          .post(
        Uri.parse("${Constants.PAYMENTS_API_ENDPOINT}/v1/session"),
        headers: {
          "Content-Type": "application/json",
        },
        body: jsonEncode(payload),
      )
          .timeout(
        const Duration(seconds: 2),
        onTimeout: () {
          throw "timeout while creating payment session";
        },
      );

      if (resp.statusCode != 200) return left(ErrorResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));

      return right(SalePaymentSessionSuccessResponse.fromJson(jsonDecode(resp.body) as Map<String, dynamic>));
    } catch (e) {
      _logger.severe("error creating payment session", e);
      return left(ErrorResponse(message: "error creating sale payment session: $e"));
    }
  }
}

class SalePaymentSessionSuccessResponse {
  SalePaymentSessionSuccessResponse({
    required this.message,
    required this.data,
  });

  SalePaymentSessionSuccessResponse.fromJson(Map<String, dynamic> json)
      : message = json['message'] as String,
        data = SalePaymentSessionData.fromJson(json['data'] as Map<String, dynamic>);

  String message;
  SalePaymentSessionData data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'data': data,
      };
}

class SalePaymentSessionData {
  SalePaymentSessionData({
    required this.token,
    required this.balenaDeviceUUID,
  });

  SalePaymentSessionData.fromJson(Map<String, dynamic> json)
      : token = json['token'] as String,
        balenaDeviceUUID = json['balenaDeviceUUID'] as String;

  String token;
  String balenaDeviceUUID;

  Map<String, dynamic> toJson() => {
        'token': token,
        'balenaDeviceUUID': balenaDeviceUUID,
      };
}

class SettleBatchSuccessResponse {
  SettleBatchSuccessResponse({
    required this.message,
    this.data,
  });

  SettleBatchSuccessResponse.fromJson(Map<String, dynamic> json)
      : message = json['message'] as String,
        data = json['data'] == null ? null : SettleBatchTransactionData.fromJson(json['data'] as Map<String, dynamic>);

  String message;
  SettleBatchTransactionData? data;

  Map<String, dynamic> toJson() => {
        'message': message,
        'data': data,
      };
}

class ErrorResponse {
  ErrorResponse({
    required this.message,
  });

  ErrorResponse.fromJson(Map<String, dynamic> json) : message = json['message'] as String;

  String message;

  Map<String, dynamic> toJson() => {
        'message': message,
      };
}

class PaymentLineItem {
  PaymentLineItem({
    required this.productCode,
    required this.quantity,
    required this.total,
  });

  String productCode;
  String quantity;
  String total;

  Map<String, dynamic> toJson() => {
        "product_code": productCode,
        "quantity": quantity,
        "total": total,
      };

  Map<String, dynamic> serialize() => {
        "product_code": productCode,
        "quantity": quantity,
        "total": total,
      };
}

class NMIResponseData {
  NMIResponseData({
    this.authcode = "",
    this.avsresponse = "",
    this.cvvresponse = "",
    this.orderid = "",
    this.response = "",
    // ignore: non_constant_identifier_names
    this.response_code = "",
    this.responsetext = "",
    this.transactionid = "",
    this.type = "",
  });

  NMIResponseData.fromJson(Map<String, dynamic> json)
      : authcode = json['authcode'] as String,
        avsresponse = json['avsresponse'] as String,
        cvvresponse = json['cvvresponse'] as String,
        orderid = json['orderid'] as String,
        response = json['response'] as String,
        response_code = json['response_code'] as String,
        responsetext = json['responsetext'] as String,
        transactionid = json['transactionid'] as String,
        type = json['type'] as String;

  String authcode;
  String avsresponse;
  String cvvresponse;
  String orderid;
  String response;
  // ignore: non_constant_identifier_names
  String response_code;
  String responsetext;
  String transactionid;
  String type;

  Map<String, dynamic> toJson() => {
        'authcode': authcode,
        'avsresponse': avsresponse,
        'cvvresponse': cvvresponse,
        'orderid': orderid,
        'response': response,
        'response_code': response_code,
        'responsetext': responsetext,
        'transactionid': transactionid,
        'type': type,
      };
}
