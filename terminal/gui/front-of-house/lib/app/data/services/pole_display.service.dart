import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

const int POLE_DISPLAY_TOTAL_SPACING = 20;

const int MAX_ITEM_TEXT_LENGTH = 7;
const int MAX_ITEM_SPACING = 9;
const int MAX_ITEM_TOTAL_LENGTH = 13;

final Logger _logger = Logger('PoleDisplayService');

///
///
///
///TODO: Uncomment out pole display errors once it makes sense to do so
///
///
///
class PoleDisplayService extends GetxService {
  void banner() {
    unawaited(
      Future.sync(() async {
        try {
          await _write({
            "command": "banner",
          });
        } catch (err) {
          // _logger.shout("error displaying banner", err, stack);
        }
      }),
    );
  }

  void write({
    required String line1,
    String? line1Suffix,
    required String line2Prefix,
    required String line2Suffix,
  }) {
    //TODO: maybe do something to shorten if the text goes over the capacity of the display
    int totalSpacing1 = POLE_DISPLAY_TOTAL_SPACING - line1.length;
    if (line1Suffix != null) totalSpacing1 -= line1Suffix.length;

    final String spacesPadding1 = " " * totalSpacing1;
    final String line1Out = "$line1$spacesPadding1${line1Suffix ?? ""}";

    final int totalSpacing2 = POLE_DISPLAY_TOTAL_SPACING - line2Suffix.length - line2Prefix.length;

    final String spacesPadding2 = " " * totalSpacing2;
    final String line2Out = "$line2Prefix$spacesPadding2$line2Suffix";
    _backgroundWrite(line1Out, line2Out);
  }

  Future<void> writeSaleRow({
    required SaleRow saleRow,
    required Sale sale,
  }) async {
    try {
      final SaleRow parent = Helpers.getParentRow(saleRow, sale.document.saleRows);

      String itemText = parent.receiptDescription;

      if (itemText.length > MAX_ITEM_TEXT_LENGTH) {
        itemText = itemText.replaceRange(MAX_ITEM_TEXT_LENGTH, itemText.length, "...");
      }
      int leftoverSpace = 0;
      if (itemText.length <= MAX_ITEM_TEXT_LENGTH) {
        leftoverSpace = 10 - itemText.length;
      }

      int rowCreditTotal = 0;

      final List<SaleRow> children = Helpers.getChildRows(parent, sale.document.saleRows);

      for (final SaleRow child in children) {
        rowCreditTotal += child.basePrice;
      }

      //Pole display has 20 digits per line

      //Item spacing
      //@@@@@@@...****$17.00

      //Total spacing
      //Total:********$17.00

      final String itemCurrency = Helpers.formatCurrency(rowCreditTotal);
      final String itemTotalCurrency = Helpers.formatCurrency(sale.document.saleHeader.total);

      final int itemTextSpacing = (MAX_ITEM_SPACING - itemCurrency.length) + leftoverSpace;
      final int itemTotalSpacing = MAX_ITEM_TOTAL_LENGTH - itemTotalCurrency.length;

      final String line1 = "$itemText${" " * itemTextSpacing}\$$itemCurrency";
      final String line2 = "Total:${" " * itemTotalSpacing}\$$itemTotalCurrency";

      _backgroundWrite(line1, line2);
    } catch (err) {
      // _logger.shout("error writing sale row", err, stack);
    }
  }

  void _backgroundWrite(
    String line1,
    String line2,
  ) {
    unawaited(
      Future.sync(() async {
        try {
          _logger.info("pole display line1: $line1");
          _logger.info("pole display line2: $line2");

          await _write(
            {
              "command": "text",
              "line1": line1,
              "line2": line2,
              "justify": 1,
            },
          );
        } catch (err) {
          // _logger.shout("error writing in background", err, stack);
        }
      }),
    );
  }

  static Future<void> _write(Object payload) async {
    try {
      final Socket socket = await Socket.connect(
        Constants.HW_HOST,
        Constants.HW_POLE_DISPLAY_PORT,
        timeout: const Duration(
          seconds: 1,
        ),
      );
      socket.writeln(jsonEncode(payload));

      socket.destroy();
    } catch (err) {
      // _logger.shout("error writing to pole display", err, stack);
    }
  }
}
