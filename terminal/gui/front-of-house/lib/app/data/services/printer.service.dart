// ignore_for_file: always_specify_types, avoid_dynamic_calls

import 'dart:async';
import 'dart:io';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/prep_device_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/modules/register/dialogs/prep_errors/dialog.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

const String CREATE_PRINT_JOB_MUTATION = '''
  mutation CREATE_PRINT_JOB(\$printJobObject: print_job_insert_input!) {
    insert_print_job_one(object: \$printJobObject) {
      print_job
      document
      created_at
    }
  }
''';

const String GET_PRINT_JOB_QUERY = '''
  query GET_PRINT_JOB {
    print_job {
      created_at
      document
      print_job
      prn_num
    }
  }
''';

const String DELETE_PRINT_JOB_MUTATION = '''
  mutation DELETE_PRINT_JOB_BY_PK(\$print_job: uuid = "") {
    delete_print_job_by_pk(print_job: \$print_job) {
      prn_num
      print_job
        document
    created_at
    }
  }
''';

final Logger _logger = Logger('PrinterService');

class PrintJobReturn {
  PrintJobReturn({
    required this.printed,
    this.printJob,
    this.errors,
  });
  final bool printed;
  final PrintJob? printJob;
  final List<ServiceError>? errors;
}

class PrepReturn {
  PrepReturn({
    required this.deviceIdx,
    this.printTime,
  });
  final int deviceIdx;
  final DateTime? printTime;
}

class PrinterService extends GetxService {
  final GraphqlService _graphqlService = Get.find();
  final ConfigService _configService = Get.find();
  final NotificationService _notificationService = Get.find();
  final EmployeeService _employeeService = Get.find();
  final IdentityService _identityService = Get.find();
  final ActivityService _activityService = Get.find();
  final SaleService _saleService = Get.find();
  final CustomerService _customerService = Get.find();
  late Worker openSalesEver;
  List<PrepDevice> prepList = <PrepDevice>[];
  RxList<String> prepPrintingSales = <String>[].obs;
  final List<String> _removalList = <String>[];

  Future<PrinterService> init() async {
    try {
      await getRecords();

      // Fires off whenever the list of open sales in the sale service is updated.
      openSalesEver = ever(_saleService.allOpenSales, (List<Sale> openSales) async {
        removeSalesFromPrintingList();
        // Check if terminal is dedicated online order terminal
        if (_identityService.terminalNumber != _configService.ecomConfig.document.printTerminal) return;
        for (final Sale s in openSales) {
          // If sale is not an online order then continue to next one.
          if (!s.document.saleHeader.saleFlags.contains(SaleFlags.ONLINE_ORDER.index)) continue;
          // Remove suspended flag, change currentTerminalNumber to 0 and upsert sale
          s.document.saleHeader.saleFlags.removeWhere((int f) => f == SaleFlags.SUSPENDED.index);
          s.document.saleHeader.currentTerminalNumber = 0;

          await _saleService.upsert(
            sale: s,
            employee: Constants.onlineOrderEmployee,
            terminalNumber: _identityService.terminalNumber,
          );

          // Optionally print the expedite slip.
          if (_configService.ecomConfig.document.expediteSlip) {
            unawaited(
              printSale(
                sale: s,
                saleName: "Online",
                expediteSlip: true,
                showSeats: false,
              ),
            );
          }

          // Optionally print the customer receipt.
          if (_configService.ecomConfig.document.customerReceipt) {
            unawaited(
              printSale(
                sale: s,
                saleName: "Online",
                showSeats: false,
              ),
            );
          }

          // Optionally print the merchant receipt.
          if (_configService.ecomConfig.document.merchantReceipt) {
            unawaited(
              printSale(
                sale: s,
                saleName: "Online",
                showSeats: false,
                customerCopy: false,
              ),
            );
          }

          // Send to kitchen.
          unawaited(startPrepPrint(sale: s, saleName: "Online"));
        }
      });
    } catch (err, stack) {
      _logger.severe(
        "Error initializing printer service",
        err,
        stack,
      );
      _notificationService.error("Error initializing printer service");
    }
    return this;
  }

  @override
  void onClose() {
    openSalesEver.dispose();
    super.onClose();
  }

  Future<void> getRecords() async {
    final Either<ServiceError, SystemDeviceJsonRecordDocument> deviceRes = await _identityService.getSystemDeviceDocument();
    final SystemDeviceJsonRecordDocument deviceDoc =
        deviceRes.fold((ServiceError e) => SystemDeviceJsonRecordDocument.empty(), (SystemDeviceJsonRecordDocument d) => d);
    prepList = deviceDoc.prep;
  }

  Future<PrintJobReturn> printReceipt({
    required PrintJob printJob,
    PrepDevice? prepDevice,
    bool isReroute = false,
    int? remotePrintIdx,
  }) async {
    try {
      List<ServiceError>? errorReturn;
      bool printed = true;

      printJob.prn_num = printJob.document.pjHeader.prnNum ?? 0;

      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJob.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );
      if ((remotePrintIdx ?? 0) > 0) {
        return remotePrint(
          terminalIdx: remotePrintIdx!,
          printJob: printJob,
        );
      }
      final QueryResult<Object?> insertResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(
            CREATE_PRINT_JOB_MUTATION,
          ),
          variables: <String, dynamic>{
            "printJobObject": santizedPrintJob,
          },
        ),
      );

      if (insertResult.hasException) {
        throw insertResult.exception.toString();
      } else if (insertResult.data?["insert_print_job_one"] == null) {
        throw "Error inserting prep print job ${isReroute ? "for reroute" : ""}";
      }

      PrintJob newPrintJob = PrintJob.fromJson(insertResult.data?["insert_print_job_one"] as Map<String, dynamic>);

      if (prepDevice != null) {
        final String uuid = newPrintJob.print_job;
        _logger.severe(
          "Created ${isReroute ? "reroute" : ""} prep print job to ${prepDevice.desc} for sale #${printJob.document.pjHeader.saleNum} with uuid $uuid",
        );
        for (int i = 0; i < 5; i++) {
          await Future<void>.delayed(const Duration(seconds: 1)).then((void value) async {
            final Option<List<ServiceError>> err1 = await hasPrintErrors(
              createdAt: insertResult.data!["insert_print_job_one"]["created_at"] as String,
              dbClient: _graphqlService.client,
              prepDevice: prepDevice,
            );

            errorReturn = err1.match((List<ServiceError> t) => t, () => null);

            if (errorReturn == null) i = 5;
          });
        }
        if (errorReturn != null) {
          _logger.severe(
            "Unsuccessful ${isReroute ? "reroute" : "prep print"} to ${prepDevice.desc} for sale #${printJob.document.pjHeader.saleNum}${uuid == "" ? "" : " with uuid $uuid"}: ${errorReturn!.map((e) => e.message).join(", ")}",
          );
        } else {
          _logger.severe(
            "Successful ${isReroute ? "reroute" : "prep print"} to ${prepDevice.desc} for sale #${printJob.document.pjHeader.saleNum}${uuid == "" ? "" : " with uuid $uuid"}",
          );
        }
      } else {
        await Future<void>.delayed(const Duration(seconds: 1)).then((void value) async {
          final Option<List<ServiceError>> err2 = await hasPrintErrors(
            createdAt: insertResult.data!["insert_print_job_one"]["created_at"] as String,
            dbClient: _graphqlService.client,
          );

          errorReturn = err2.match(
            (List<ServiceError> t) => t,
            () => null,
          );
        });
      }

      if (errorReturn != null) {
        printed = false;

        /// on error, delete print job
        final bool deleted = await _deletePrintJob(newPrintJob, _graphqlService.client);

        if (deleted) {
          /// if not currently going through reroute flow and printer has reroute set, attempt to reroute
          if (!isReroute && (prepDevice?.reRoute ?? 0) > 0) {
            final PrepDevice? rerouteDevice = prepList.firstWhereOrNull((PrepDevice p) => p.idx == prepDevice!.reRoute - 1);
            if (rerouteDevice != null) {
              final PrintJobReturn rerouteRes = await _attemptReroute(printJob, prepDevice!);

              errorReturn!.addAll(rerouteRes.errors!);

              if (rerouteRes.printed) {
                printed = true;
                newPrintJob = rerouteRes.printJob ?? newPrintJob;
              }
            }
          }
        } else {
          printed = true;
          _logger.severe(
            "Could not delete ${isReroute ? "reroute" : "prep print"} to ${prepDevice?.desc} for sale #${printJob.document.pjHeader.saleNum}${newPrintJob.print_job == "" ? "" : " with uuid ${newPrintJob.print_job}"}",
          );
        }
      }

      return PrintJobReturn(printJob: newPrintJob, errors: errorReturn, printed: printed);
    } catch (err, stack) {
      _logger.shout("error printing receipt", err, stack);
      return PrintJobReturn(
        errors: <ServiceError>[ServiceError("Error printing receipt")],
        printed: false,
      );
    }
  }

  Future<PrintJobReturn> remotePrint({
    required int terminalIdx,
    required PrintJob printJob,
    PrepDevice? prepDevice,
    bool isReroute = false,
  }) async {
    try {
      final bool prepPrint = prepDevice != null;

      if (terminalIdx == 0) {
        return PrintJobReturn(
          errors: [
            ServiceError("Remote printer not set"),
          ],
          printed: false,
        );
      }

      final Policies policies = Policies(
        fetch: FetchPolicy.noCache,
      );

      printJob.document.pjHeader.prnNum = 0;
      printJob.prn_num = 0;

      final QueryResult<Object> jsonRecordResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString('''
            query GET_SYSTEM_SETTING {
              json_record(where: {record_key: {_eq: "systemDevice"}}) {
                document
                record_key
                updated_at
              }
            }
          '''),
        ),
      );

      if (jsonRecordResult.hasException) {
        _logger.severe(
          "Error getting systemDevice json record.",
        );

        final List<ServiceError> errRes = <ServiceError>[
          ServiceError(
            "Can't find terminal info for remote prep print.",
          ),
        ];

        return PrintJobReturn(errors: errRes, printed: false);
      }

      if ((jsonRecordResult.data!["json_record"] as List).isEmpty) {
        return PrintJobReturn(
          errors: <ServiceError>[ServiceError("Print failed. No terminals exist in database!")],
          printed: false,
        );
      }

      final List<SystemDeviceJsonRecordTerminal> terminals = (jsonRecordResult.data!["json_record"][0]["document"]["terminal"] as List)
          .map(
            (dynamic t) => SystemDeviceJsonRecordTerminal.fromJson(
              t as Map<String, dynamic>,
            ),
          )
          .toList();

      final SystemDeviceJsonRecordTerminal? terminal = terminals.firstWhereOrNull(
        (SystemDeviceJsonRecordTerminal t) => t.idx == terminalIdx,
      );

      if (terminal == null) {
        final List<ServiceError> errRes = <ServiceError>[
          ServiceError(
            "Can't find terminal info for remote prep print.",
          ),
        ];

        return PrintJobReturn(errors: errRes, printed: false);
      }

      final String hasuraHost = "${terminal.IP}:8080/v1/graphql";
      _logger.shout('remote IP: $hasuraHost');
      final HttpLink httpLink = HttpLink(
        'http://$hasuraHost',
      );

      final WebSocketLink webSocketLink = WebSocketLink(
        'ws://$hasuraHost',
      );

      final Link link = Link.split(
        (Request request) => request.isSubscription,
        webSocketLink,
        httpLink,
      );

      final GraphQLClient termClient = GraphQLClient(
        link: link,
        cache: GraphQLCache(),
        defaultPolicies: DefaultPolicies(
          watchQuery: policies,
          query: policies,
          mutate: policies,
        ),
      );

      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJob.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );
      _logger.shout('attempting remote print');
      final QueryResult<Object?> createPrintJobResult = await termClient.mutate(
        MutationOptions<Object?>(
          document: g.parseString(
            '''
            mutation CREATE_PRINT_JOB(\$printJobObject: print_job_insert_input!) {
              insert_print_job_one(object: \$printJobObject) {
                print_job
                document
                created_at
              }
            }
          ''',
          ),
          variables: <String, dynamic>{
            "printJobObject": santizedPrintJob,
          },
        ),
      );

      if (createPrintJobResult.hasException) {
        _logger.severe(
          "Error inserting remote print job.",
        );

        final List<ServiceError> res = <ServiceError>[
          ServiceError(
            "Failed to print remotely. Error inserting print job.",
          ),
        ];

        return PrintJobReturn(errors: res, printed: false);
      } else if (createPrintJobResult.data?["insert_print_job_one"] == null) {
        throw "Error inserting prep print job";
      }

      List<ServiceError>? errReturn;
      bool printed = true;

      PrintJob newPrintJob = PrintJob.fromJson(createPrintJobResult.data!["insert_print_job_one"] as Map<String, dynamic>);

      if (prepPrint) {
        final String uuid = newPrintJob.print_job;
        _logger.severe(
          "Created remote prep print job to terminal $terminalIdx for sale #${printJob.document.pjHeader.saleNum} with uuid $uuid",
        );
        for (int i = 0; i < 5; i++) {
          await Future<void>.delayed(
            const Duration(seconds: 1),
          ).then((void value) async {
            final Option<List<ServiceError>> err = await hasPrintErrors(
              createdAt: createPrintJobResult.data!["insert_print_job_one"]["created_at"] as String,
              dbClient: termClient,
            );

            errReturn = err.match(
              (List<ServiceError> t) => t,
              () => null,
            );

            if (errReturn == null) i = 5;
          });
        }

        if (errReturn != null) {
          printed = false;

          /// on error, delete print job
          final bool deleted = await _deletePrintJob(newPrintJob, termClient);
          _logger.severe(
            "Unsuccessful remote prep print to terminal $terminalIdx for sale #${printJob.document.pjHeader.saleNum}${uuid == "" ? "" : " with uuid $uuid"}: ${errReturn!.map((e) => e.message).join(", ")}",
          );

          if (deleted) {
            /// if not currently going through reroute flow and printer has reroute set, attempt to reroute
            if (!isReroute && prepDevice.reRoute > 0) {
              final PrintJobReturn rerouteRes = await _attemptReroute(printJob, prepDevice);
              printed = rerouteRes.printed;
              errReturn!.addAll(rerouteRes.errors ?? []);
              newPrintJob = rerouteRes.printJob ?? newPrintJob;
            }
          } else {
            printed = true;
            _logger.severe(
              "Could not delete remote prep print to terminal $terminalIdx for sale #${printJob.document.pjHeader.saleNum}${uuid == "" ? "" : " with uuid $uuid"}",
            );
          }
        } else {
          _logger.severe(
            "Successful remote prep print to terminal $terminalIdx for sale #${printJob.document.pjHeader.saleNum}${uuid == "" ? "" : " with uuid $uuid"}",
          );
        }
      }

      return PrintJobReturn(errors: errReturn, printJob: newPrintJob, printed: printed);
    } catch (err, stack) {
      _logger.shout("Remote print Error:", err, stack);
      return PrintJobReturn(
        errors: <ServiceError>[ServiceError("Remote printing error.")],
        printed: false,
      );
    }
  }

  Future<PrintJobReturn> _attemptReroute(PrintJob printJob, PrepDevice prepDevice) async {
    final List<ServiceError> errReturn = <ServiceError>[];
    final PrepDevice? rerouteDevice = prepList.firstWhereOrNull((PrepDevice p) => p.idx == prepDevice.reRoute - 1);
    PrintJobReturn? rerouteRes;
    bool printed = false;

    final List<PjRows> reRows = [
      PjRows(text: '*' * 33),
      PjRows(text: 'REROUTED FROM ${prepDevice.desc.capitalize}!' * 33),
      PjRows(text: '*' * 33),
      PjRows(text: ""),
    ];

    printJob.document.pjRows = <PjRows>[...reRows, ...printJob.document.pjRows];

    if (rerouteDevice != null) {
      errReturn.add(ServiceError("Attempting to reroute to ${rerouteDevice.desc}"));
      final int newPrnNum = rerouteDevice.type == PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex ? 0 : rerouteDevice.idx + 1;
      printJob.document.pjHeader.prnNum = newPrnNum;
      printJob.prn_num = newPrnNum;
      printJob.print_job = "";
      printJob.created_at = null;

      switch (PrepDeviceType.values[rerouteDevice.type]) {
        case PrepDeviceType.TerminalRecieptPrint:
          rerouteRes = rerouteDevice.rcptTerm == _identityService.terminalNumber
              ? await printReceipt(
                  printJob: printJob,
                  prepDevice: rerouteDevice,
                  isReroute: true,
                )
              : await remotePrint(
                  terminalIdx: rerouteDevice.rcptTerm,
                  printJob: printJob,
                  prepDevice: rerouteDevice,
                  isReroute: true,
                );
        case PrepDeviceType.Inactive:
          rerouteRes = PrintJobReturn(
            printed: true,
            printJob: printJob,
            errors: <ServiceError>[
              ServiceError("rerouted to inactive device: ${rerouteDevice.desc}"),
            ],
          );
        default:
          rerouteRes = await printReceipt(
            printJob: printJob,
            prepDevice: rerouteDevice,
            isReroute: true,
          );
      }

      errReturn.addAll(rerouteRes.errors ?? []);

      if (!rerouteRes.printed) {
        errReturn.add(ServiceError("Attempted to reroute to ${rerouteDevice.desc}"));
      } else {
        printed = true;
        errReturn.add(ServiceError("Successfully rerouted to ${rerouteDevice.desc}"));
      }
    } else {
      errReturn.add(ServiceError("Attempted to reroute from ${prepDevice.desc}"));
      errReturn.add(ServiceError("Reroute device not found"));
    }
    return PrintJobReturn(printJob: rerouteRes?.printJob ?? printJob, errors: errReturn, printed: printed);
  }

  Future<bool> _deletePrintJob(PrintJob printJob, GraphQLClient dbClient) async {
    try {
      final QueryResult<Object?> deleteResult = await dbClient.mutate(
        MutationOptions<Object?>(
          document: g.parseString(
            DELETE_PRINT_JOB_MUTATION,
          ),
          variables: <String, dynamic>{
            "print_job": printJob.print_job,
          },
        ),
      );

      if (deleteResult.hasException) {
        throw deleteResult.exception.toString();
      }
      if (deleteResult.data == null) {
        throw "Error deleting print job";
      }
      if (deleteResult.data!["delete_print_job_by_pk"] == null) {
        throw "Print job not found for deletion";
      }
      return true;
    } catch (err, stack) {
      _logger.shout("Error deleting print job", err, stack);
      return false;
    }
  }

  // Future<PrintJobReturn> _attemptLocalPrint(PrintJob printJob, List<ServiceError> errRes) async {
  //   final PrintJobReturn localPrint = await printReceipt(printJob: printJob);

  //   if (localPrint.errors != null) {
  //     errRes.add(
  //       ServiceError(
  //         "Failed to print locally. Job stored locally",
  //       ),
  //     );
  //     errRes.addAll(localPrint.errors!);
  //   }

  //   return PrintJobReturn(
  //     errors: errRes.isEmpty ? null : errRes,
  //     printJob: localPrint.printJob,
  //   );
  // }

  Future<Option<List<ServiceError>>> hasPrintErrors({
    required String createdAt,
    required GraphQLClient dbClient,
    PrepDevice? prepDevice,
  }) async {
    try {
      final QueryResult<Object?> printJobResult = await dbClient.query(
        QueryOptions<Object?>(
          document: g.parseString(
            '''
              query GET_PRINT_JOB(\$time: timestamp = "") {
                print_job(where: {created_at: {_lte: \$time}}) {
                  print_job
                  document
                  created_at
                }
              }
          ''',
          ),
          variables: <String, dynamic>{
            "time": createdAt,
          },
        ),
      );
      if (printJobResult.hasException) {
        throw printJobResult.exception.toString();
      }
      if (printJobResult.data == null) {
        return some(<ServiceError>[ServiceError("Print Error: Error getting print data!")]);
      }
      if (printJobResult.data!.isEmpty) {
        return some(<ServiceError>[ServiceError("Print Error: Error getting print data!")]);
      }

      if ((printJobResult.data!["print_job"] as List).isNotEmpty) {
        final List<ServiceError> resList = <ServiceError>[];
        if ((printJobResult.data!["print_job"] as List).firstWhereOrNull(
              (dynamic pj) => pj["created_at"] == createdAt,
            ) !=
            null) {
          resList.add(
            ServiceError("Print Error: Print Failed!"),
          );
        }
        if (resList.isNotEmpty) return some(resList);
      }
      return none();
    } catch (err, stack) {
      _logger.shout("Error checking for print", err, stack);
      return some(<ServiceError>[ServiceError("Error getting print data!")]);
    }
  }

  // ///
  // ///
  // ///
  // ///
  // ///
  // ///
  // void printGiftReceipt(SaleRow saleRow) {
  //   currentSale.value.match(
  //     (Sale sale) async {
  //       final PrintJob printJob = PrinterInterface.buildGiftIssue(
  //         currentEmployee: currentEmployee.value,
  //         saleRow: saleRow,
  //         saleNumber: sale.sale_number,
  //       );

  //       final Option<ServiceError> printResult = await _printerService.printReceipt(
  //         printJob: printJob,
  //       );

  //       printResult.match(
  //         (ServiceError error) => _notificationService.error(error.message),
  //         () => null,
  //       );
  //     },
  //     () => null,
  //   );
  // }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> startPrepPrint({
    required Sale sale,
    List<SaleRow>? voidedList,
    bool canceledSale = false,
    String? saleName,
  }) async {
    try {
      final bool isOnlineOrder = sale.document.saleHeader.saleFlags.contains(SaleFlags.ONLINE_ORDER.index);
      // If it is already being prep printed then nothing to do.
      if (prepPrintingSales.contains(sale.sale)) return;

      final bool normalPrep = voidedList == null && !canceledSale;
      final Map<int, Sale> sales = <int, Sale>{};
      final Map<int, List<String>> errMap = <int, List<String>>{};
      final Map<int, List<SaleRow>> prepDevToRows = <int, List<SaleRow>>{};

      int printed = 0;

      final Either<ServiceError, Employee> empRes = await _employeeService.getEmployeeById(sale.document.saleHeader.currentEmployeeNumber);
      final Employee currentEmployee = empRes.match(
        (l) {
          _notificationService.error(l.message);
          return isOnlineOrder ? Constants.onlineOrderEmployee : _identityService.currentEmployee;
        },
        (r) => r,
      );

      // Get only relevant rows for printing.
      final List<SaleRow> rowList = sale.document.saleRows.where((r) {
        if (voidedList != null) {
          if (voidedList.contains(r)) return true;
        }
        int totalPrice = 0;
        final SaleRow parent = Helpers.getParentRow(r, sale.document.saleRows);
        final List<SaleRow> childRows = Helpers.getChildRows(parent, sale.document.saleRows);
        for (final SaleRow childRow in childRows) {
          totalPrice += childRow.basePrice;
        }
        return totalPrice >= 0;
      }).toList();

      // If voided items print filter out irrelevant prep devices
      final List<PrepDevice> filteredPrepList = voidedList == null
          ? prepList
          : prepList.where((PrepDevice p) {
              for (final SaleRow row in voidedList) {
                final List<SaleRow> childList = Helpers.getChildRows(row, rowList);
                for (final SaleRow r in childList) {
                  if (r.prep & (1 << p.idx) == (1 << p.idx)) {
                    return true;
                  }
                }
              }
              return false;
            }).toList();

      for (final PrepDevice d in filteredPrepList) {
        final PrepDevice device = _checkDeviceSchedules(d, sale.document.saleHeader.tableDesc);
        final Map<int, int> originalParents = <int, int>{};
        final List<SaleRow> deviceRowList = rowList.map((SaleRow r) {
          // If row is to be print separately from parent make a copy and set it's parent int to -1
          if (r.printSeparate) {
            originalParents[r.index] = r.parent;
            return Helpers.makeRowCopy(row: r, newIdx: r.index, qty: r.qty, newParentIdx: -1);
          }
          return r;
        }).toList();

        for (final SaleRow row in deviceRowList) {
          // Check to see if row has already been printed on this device
          final bool alreadyPrepped = row.preppedAt?[d.idx.toString()] != null && normalPrep;
          final bool holdRow = row.flags.contains(SaleRowFlags.HOLD_AND_FIRE.index) && normalPrep;
          // Only check parent rows that still need printed
          // If voided item or canceled sale print we don't care about already printed
          if (row.parent >= 0 || alreadyPrepped || holdRow) continue;

          final List<SaleRow> childRows = Helpers.getChildRows(row, deviceRowList);

          // True if at least one child row needs printed on this device
          final bool addingRow = childRows.any((SaleRow c) {
            if (!c.isVisible && c.parent >= 0) return false;
            // if preppedAt is not null or empty, use that, else use prep bitwise int
            if ((c.preppedAt ?? {}).isNotEmpty) {
              if (c.preppedAt!.keys.contains(d.idx.toString())) return true;
            } else if (c.prep & (1 << d.idx) == (1 << d.idx)) {
              return true;
            }
            return false;
          });

          // If printings is needed add rows to a dummy sale and
          // add it to sales map using device idx as key
          if (addingRow) {
            // Change "print separate" row copy's parent back to original int if original parent is being printed with it
            if (originalParents[row.index] != null) {
              if ((sales[device.idx] ?? Sale.empty()).document.saleRows.any((SaleRow r) => r.index == originalParents[row.index])) {
                row.parent = originalParents[row.index]!;
              } else {
                row.isVisible = true;
              }
            }
            for (final SaleRow child in childRows) {
              final SaleRow actualRow =
                  originalParents[child.index] != null ? sale.document.saleRows.firstWhere((SaleRow r) => r.index == child.index) : child;
              prepDevToRows.putIfAbsent(device.idx, () => <SaleRow>[]).add(actualRow);
              if (!actualRow.flags.contains(SaleRowFlags.PREP_PRINTED.index)) {
                if (actualRow.parent < 0) printed += 1;
                actualRow.flags.add(SaleRowFlags.PREP_PRINTED.index);
              }
              if (sales[device.idx] == null) {
                sales[device.idx] = Sale(
                  sale: sale.sale,
                  sale_number: sale.sale_number,
                  document: SaleDocument(
                    saleHeader: Helpers.makeDeepSaleHeaderCopy(sale),
                    saleRows: [child],
                  ),
                  created_by: sale.created_by,
                  updated_by: sale.updated_by,
                );
              } else {
                sales[device.idx]!.document.saleRows.add(child);
              }
            }
          }
        }
      }

      if (sales.isEmpty && normalPrep) {
        return _notificationService.error("No items to print!");
      }

      // Variable used to determine which sales are currently being printed
      prepPrintingSales.value = [sale.sale, ...prepPrintingSales];

      if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.PREP_PRINTED.index)) {
        sale.document.saleHeader.saleFlags.add(SaleFlags.PREP_PRINTED.index);
      }

      // Sets preppedAt times for each row being printed
      // Times are set to null until prints are successful
      _updateSaleRowSentTimes(prepDevToRows, []);

      // Upserts the sale
      final Either<ServiceError, Sale> upRes = await _saleService.upsert(
        sale: sale,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );
      upRes.match(
        (l) => _notificationService.error("Error updating sale!"),
        (r) => null,
      );

      // Stores the print futures
      final List<Future<PrepReturn>> promiseList = <Future<PrepReturn>>[];

      // Find devices using sale keys and build prints using them
      for (final int key in sales.keys.toList()) {
        final PrepDevice? dev = filteredPrepList.firstWhereOrNull((PrepDevice pd) => pd.idx == key);
        if (dev != null) {
          final PrintJob printJob = canceledSale
              ? PrinterInterface.saleCancelPrep(
                  currentEmployee: _identityService.currentEmployee,
                  sale: sales[key]!,
                  headerText: saleName ?? "Sale",
                  printNum: dev.type == PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex ? 0 : dev.idx + 1,
                )
              : voidedList == null
                  ? PrinterInterface.buildPrepTicket(
                      sale: sales[key]!,
                      saleName: saleName ?? "Sale",
                      printNum: dev.type == PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex ? 0 : dev.idx + 1,
                      currentEmployee: currentEmployee,
                      currentUser: _identityService.currentEmployee,
                      prepDev: dev,
                      orderTypePrintOpt: _configService.merchantConfig.document.orderTypePrintOpt,
                      prepPrintSizing: _configService.merchantConfig.document.prepPrintSizing,
                      printPrepFooter: _configService.merchantConfig.document.printPrepFooter,
                      markModifiersRed: _configService.merchantConfig.document.markModifiersRed,
                      markCommentsRed: _configService.merchantConfig.document.markCommentsRed,
                      markToGoRed: _configService.merchantConfig.document.markToGoRed,
                      markOrderTypeRed: _configService.merchantConfig.document.markOrderTypeRed,
                      markPromisedTimeRed: _configService.merchantConfig.document.markPromisedTimeRed,
                    )
                  : PrinterInterface.buildPrepVoided(
                      voidedRows: sales[key]!
                          .document
                          .saleRows
                          .where((SaleRow s) => s.parent < 0 && voidedList.firstWhereOrNull((SaleRow v) => s.index == v.index) != null)
                          .toList(),
                      addedRows: [],
                      currentEmployee: _identityService.currentEmployee,
                      sale: sales[key]!,
                      headerText: saleName ?? "Sale",
                      printNum: dev.type == PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex ? 0 : dev.idx + 1,
                    );
          // Add pending print to promise list
          promiseList.add(
            printPrep(
              printJob: printJob,
              prepDevice: dev,
              errMap: errMap,
              isOnlineOrder: isOnlineOrder,
            ),
          );
        }
      }

      // Await all pending prints
      final List<PrepReturn> printResults = await Future.wait(promiseList);

      markSaleForRemovalFromPrintingList(sale.sale);

      // Check for errors and display dialog if any are found
      if (errMap.isNotEmpty) {
        Helpers.blockAutoSignout = true;
        await Get.defaultDialog(
          title: "Prep Alerts for $saleName #${sale.sale_number}",
          content: PrepErrorsDialog(
            errMap: errMap,
            sales: sales,
            sale: sale,
            showRetryBttn: printResults.any((PrepReturn r) => r.printTime == null),
          ),
        );
        Helpers.blockAutoSignout = false;
        Helpers.lastActivityTime.value = DateTime.now();
      }

      // If not a void print update the preppedAt times for each row and insert activity
      if (normalPrep) {
        _updateSaleRowSentTimes(prepDevToRows, printResults);
        unawaited(
          _activityService.insertActivity(
            activity: Activity(
              activity: ActivityFlags.PREP_PRINT.index,
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              sale_num: sale.sale_number,
              str_data: "Sent $printed items to prep devices",
              short_data: printed,
            ),
          ),
        );
        (await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        ))
            .match((l) => _notificationService.error("Error updating sale!"), (r) => null);
      }
    } catch (err, stack) {
      markSaleForRemovalFromPrintingList(sale.sale);
      _logger.severe(
        "Prep print error.",
        err,
        stack,
      );
    }
  }

  /// Description
  /// - Checks to see if there is a printer on a specific section schedule.
  ///
  /// Parameters
  /// - PrepDevice prepDevice - The device to check if it is set to be rerouted via a schedule.
  ///
  /// Returns
  /// - The original PrepDevice if we should not reroute.
  /// - The PrepDevice that is scheduled to be rerouted to.
  PrepDevice _checkDeviceSchedules(PrepDevice prepDevice, String? tableDesc) {
    if (tableDesc != null) {
      final Section section = _configService.getSectionFromTableDesc(tableDesc);

      if (section.desc.isNotEmpty && section.printerSchedule.isNotEmpty) {
        final DateTime now = DateTime.now();

        final DateTime yesterday = now.subtract(const Duration(days: 1));

        final String currentDayName = DateFormat('EEEE').format(now);
        final String yesterdayDayName = DateFormat('EEEE').format(yesterday);

        final int currentTimeMS = Helpers.timeToMilliseconds(now);

        final PrinterSchedule todaySchedule = section.printerSchedule.firstWhere((PrinterSchedule ps) => ps.day == currentDayName);
        final PrinterSchedule yesterdaySchedule = section.printerSchedule.firstWhere((PrinterSchedule ps) => ps.day == yesterdayDayName);

        final bool isYesterdayActive =
            yesterdaySchedule.enabled && yesterdaySchedule.endTime < yesterdaySchedule.startTime && yesterdaySchedule.endTime > currentTimeMS;

        final bool checkYesterday = prepDevice.idx == yesterdaySchedule.fromPrinter && yesterdaySchedule.toPrinter != -1 && isYesterdayActive;

        // Check yesterday first and if the schedule is still active, use it.
        if (checkYesterday) {
          return prepList.firstWhere((PrepDevice pd) => pd.idx == yesterdaySchedule.toPrinter);
        }

        final bool currentDay = currentTimeMS >= todaySchedule.startTime && currentTimeMS < todaySchedule.endTime;
        final bool enabledValidPrinterCheck = todaySchedule.enabled && prepDevice.idx == todaySchedule.fromPrinter && todaySchedule.toPrinter != -1;

        // If the day's schedule is only active for current day.
        if (enabledValidPrinterCheck && currentDay) {
          return prepList.firstWhere((PrepDevice pd) => pd.idx == todaySchedule.toPrinter);
        }

        final bool intoNextDay = todaySchedule.endTime < todaySchedule.startTime;

        // If today's schedule is active over to the next day.
        if (enabledValidPrinterCheck && intoNextDay) {
          return prepList.firstWhere((PrepDevice pd) => pd.idx == todaySchedule.toPrinter);
        }
      }
    }

    return prepDevice;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<PrepReturn> printPrep({
    required PrintJob printJob,
    required PrepDevice prepDevice,
    required Map<int, List<String>> errMap,
    bool isOnlineOrder = false,
  }) async {
    try {
      // Devices that get skipped during printing
      // This is set on the terminal itself and stored in systemDevice jsonRecord
      final int terminalSkipDevices = isOnlineOrder ? 0 : _identityService.terminalSkipDevices;

      final bool inactiveDevice = prepDevice.type == PrepDeviceType.Inactive.prepDeviceTypeIndex;
      final bool skippingDevice = (terminalSkipDevices & (1 << prepDevice.idx)) == (1 << prepDevice.idx);
      // If inactive automatically send out successful print
      if (inactiveDevice || skippingDevice) {
        return PrepReturn(deviceIdx: prepDevice.idx, printTime: DateTime.now().toUtc());
      }

      // Check the device type and send using proper function.
      final PrintJobReturn printResult =
          prepDevice.type != PrepDeviceType.Inactive.prepDeviceTypeIndex && prepDevice.type != PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex
              ? await printReceipt(
                  printJob: printJob,
                  prepDevice: prepDevice,
                )
              : prepDevice.rcptTerm == _identityService.terminalNumber
                  ? await printReceipt(printJob: printJob)
                  : await remotePrint(
                      terminalIdx: prepDevice.rcptTerm,
                      printJob: printJob,
                      prepDevice: prepDevice,
                    );

      // Add print errors to error map
      final List<ServiceError> errs = printResult.errors ?? [];
      if (errs.isNotEmpty) {
        if (errMap[prepDevice.idx] == null) {
          errMap[prepDevice.idx] = errs.map((ServiceError e) => e.message).toList();
        } else {
          errMap[prepDevice.idx]!.addAll(errs.map((ServiceError e) => e.message).toList());
        }
      }

      // Successful prints return a print time so if not printed set to null
      return PrepReturn(
        deviceIdx: prepDevice.idx,
        printTime: printResult.printed ? printResult.printJob?.created_at : null,
      );
    } catch (err, stack) {
      _logger.severe(
        "Prep print error.",
        err,
        stack,
      );
      return PrepReturn(deviceIdx: prepDevice.idx);
    }
  }

  void _updateSaleRowSentTimes(
    Map<int, List<SaleRow>> prepDevToRows,
    List<PrepReturn> printResults,
  ) {
    for (final int key in prepDevToRows.keys) {
      for (final SaleRow row in prepDevToRows[key]!) {
        if (row.preppedAt != null && row.preppedAt![key.toString()] != null) {
          continue;
        }
        final PrepReturn? printRes = printResults.firstWhereOrNull((PrepReturn p) => p.deviceIdx == key);
        if (printRes != null) {
          row.preppedAt ??= <String, DateTime?>{};
          final DateTime? dt = printRes.printTime;
          // printTime is UTC but is initially parsed as local time on import, so converting to proper UTC DateTime here
          row.preppedAt![key.toString()] =
              dt == null ? null : DateTime.utc(dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second, dt.millisecond, dt.microsecond);
        } else {
          row.preppedAt ??= <String, DateTime?>{};
          row.preppedAt![key.toString()] = null;
        }
      }
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> printSale({
    required Sale sale,
    required String saleName,
    bool qrAllowed = false,
    bool expediteSlip = false,
    Map<int, List<int>> subtotals = const <int, List<int>>{},
    bool showSeats = true,
    bool authSlip = false,
    bool customerCopy = true,
    List<SaleTender>? tenders,
  }) async {
    try {
      final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;

      final bool isOnlineOrder = sale.document.saleHeader.saleFlags.contains(SaleFlags.ONLINE_ORDER.index);

      final Either<ServiceError, Employee> empRes = await _employeeService.getEmployeeById(sale.document.saleHeader.currentEmployeeNumber);
      final Employee currentEmployee = isOnlineOrder
          ? Constants.onlineOrderEmployee
          : empRes.match(
              (l) {
                _notificationService.error(l.message);
                return _identityService.currentEmployee;
              },
              (r) => r,
            );

      Customer? cust;

      if ((sale.document.saleHeader.customer ?? "") != "") {
        final Either<ServiceError, Customer> custRes = await _customerService.getCustomerByPk(sale.document.saleHeader.customer!);

        cust = custRes.match(
          (ServiceError l) {
            _notificationService.error(l.message);
            return null;
          },
          (Customer r) => r,
        );
      }

      String? qrPayUrl;
      if (qrAllowed && merchantDoc.qrPayEnabled) {
        try {
          final Either<ErrorResponse, SalePaymentSessionSuccessResponse> salePaymentSessionResult =
              await Get.find<PaymentService>().generateSalePaymentSession(sale: sale.sale);
          salePaymentSessionResult.fold(
            (ErrorResponse error) {
              _logger.severe("error generating sale payment session", error);
            },
            (SalePaymentSessionSuccessResponse response) {
              _logger.info("sale payment session generated successfully", response.data.token);

              final String urlHost = Platform.environment["QR_HOST"] ?? "http://**************:5173";

              qrPayUrl = "$urlHost/${response.data.balenaDeviceUUID.replaceAll("-", "")}/${response.data.token}";
            },
          );
        } catch (e) {
          _logger.shout("error generating sale payment session", e);
        }
      }

      final PrintJob printJob = expediteSlip
          ? PrinterInterface.buildPrepTicket(
              sale: sale,
              saleName: "Online Order",
              currentEmployee: currentEmployee,
              currentUser: isOnlineOrder ? Constants.onlineOrderEmployee : _identityService.currentEmployee,
              printNum: 0,
              prepDev: PrepDevice(idx: 0, desc: "Online Order"),
              orderTypePrintOpt: _configService.merchantConfig.document.orderTypePrintOpt,
              prepPrintSizing: _configService.merchantConfig.document.prepPrintSizing,
              printPrepFooter: _configService.merchantConfig.document.printPrepFooter,
              showSeats: false,
              customer: cust,
              markModifiersRed: merchantDoc.markModifiersRed,
              markCommentsRed: merchantDoc.markCommentsRed,
              markToGoRed: merchantDoc.markToGoRed,
              markOrderTypeRed: merchantDoc.markOrderTypeRed,
              markPromisedTimeRed: merchantDoc.markPromisedTimeRed,
            )
          : PrinterInterface.buildReceipt(
              currentEmployee: currentEmployee,
              sale: sale,
              saleName: saleName,
              subtotals: subtotals,
              showSeats: showSeats,
              taxList: _configService.salesTaxList,
              receiptHeader: merchantDoc.receiptHeader,
              isMerchantDualPricing: merchantDoc.dualPricing,
              tipping: authSlip,
              customerCopy: customerCopy,
              customer: cust,
              currentUser: isOnlineOrder ? Constants.onlineOrderEmployee : _identityService.currentEmployee,
              terminalDescs: _identityService.terminalDescs,
              qrCodeUrl: qrPayUrl,
              legacyGiftName: merchantDoc.legacyGiftName,
              showCommentRows: merchantDoc.commentRowsOnReceipt,
              showSaleDesc: merchantDoc.saleDescOnReceipt,
              condensedAuth: merchantDoc.condensedAuthSlip,
              tenders: tenders,
            );

      final PrintJobReturn printResult = await printReceipt(
        printJob: printJob,
        remotePrintIdx: _identityService.remoteTermIdx,
      );

      final List<ServiceError> errs = printResult.errors ?? [];

      if (errs.isNotEmpty) {
        _notificationService.error(errs.first.message);
      }
    } catch (err, stack) {
      _logger.severe(
        "Error printing ${saleName.toLowerCase()}",
        err,
        stack,
      );
      _notificationService.error("Error printing ${saleName.toLowerCase()}!");
    }
  }

  void markSaleForRemovalFromPrintingList(String saleId) {
    _removalList.add(saleId);
  }

  void removeSalesFromPrintingList() {
    prepPrintingSales.removeWhere((String s) => _removalList.contains(s));
    _removalList.clear();
    prepPrintingSales.refresh();
  }
}
