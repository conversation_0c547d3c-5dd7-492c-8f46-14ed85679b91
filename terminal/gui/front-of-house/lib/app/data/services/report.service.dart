import 'package:backoffice/app/data/models/cashier_discounts.dart';
import 'package:backoffice/app/data/models/cashier_media.dart';
import 'package:backoffice/app/data/models/cashier_sales.dart';
import 'package:backoffice/app/data/models/cashier_stats.dart';
import 'package:backoffice/app/data/models/emp_media_breakdown.dart';
import 'package:backoffice/app/data/models/emp_record.dart';
import 'package:backoffice/app/data/models/emp_sales_by_dept.dart';
import 'package:backoffice/app/data/models/emp_sales_tax.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_CASHIER_SALES = '''
  	mutation GET_CASHIER_SALES(\$terminal_index: Int, \$start_date: timestamp, \$selected_cashier: String, \$end_date: timestamp, \$sale_number: order_by = asc) {
  get_cashier_sales(args: {terminal_index: \$terminal_index, start_date: \$start_date, selected_cashier: \$selected_cashier, end_date: \$end_date}, order_by: {sale_number: \$sale_number}) {
    employee
    end_at
    media
    sale_number
    tender_amount
    tip_amount
    total
    total_with_tip
  }
}
''';

const String GET_CASHIER_DISCOUNTS = '''
  	mutation GET_CASHIER_DISCOUNTS(\$start_date: timestamp , \$end_date: timestamp , \$selected_cashier: String , \$terminal_index: Int) {
      get_cashier_discounts(args: {start_date: \$start_date, end_date: \$end_date, selected_cashier: \$selected_cashier, terminal_index: \$terminal_index}) {
        title
        sum
        disc_type
        disc_amount
      }
    }
''';

const String GET_CASHIER_STATS = '''
  	mutation GET_CASHIER_STATS(\$start_date: timestamp , \$end_date: timestamp , \$selected_cashier: String , \$terminal_index: Int) {
      get_cashier_stats(args: {terminal_index: \$terminal_index, start_date: \$start_date, selected_cashier: \$selected_cashier, end_date: \$end_date}) {
        cashier
        cancel_amount
        refunded_total
        voided_total
      }
    }
''';
const String GET_CASHIER_MEDIA = '''
  	mutation CASHIER_MEDIA(\$start_date: timestamp , \$end_date: timestamp , \$selected_cashier: String , \$terminal_index: Int) {
      get_cashier_media(args: {start_date: \$start_date, end_date: \$end_date, selected_cashier: \$selected_cashier, terminal_index: \$terminal_index}) {
        media
        sum
        tips
      }
    }
  ''';

const String GET_CASHIER_PAID_OUT = '''
  	query GET_CASHIER_PAID_OUT(\$flag: jsonb, \$startDate: timestamptz, \$endDate: timestamptz, \$selectedCashier: jsonb, \$terminal_index: jsonb) {
      sale(where: {document: {_contains: \$flag}, _and: {end_at: {_gte: \$startDate}, _and: {end_at: {_lte: \$endDate}, _and: {document: {_contains: \$selectedCashier}, _and: {document: {_contains: \$terminal_index}}}}}}) {
        updated_by
        updated_at
        suspended
        sale_number
        sale
        end_at
        document
        created_by
        created_at
      }
    }


  ''';

const String GET_CASHIER_LOAN = '''
  	query GET_CASHIER_LOAN(\$flag: jsonb, \$startDate: timestamptz, \$endDate: timestamptz, \$selectedCashier: jsonb, \$terminal_index: jsonb) {
      sale(where: {document: {_contains: \$flag}, _and: {end_at: {_gte: \$startDate}, _and: {end_at: {_lte: \$endDate}, _and: {document: {_contains: \$selectedCashier}, _and: {document: {_contains: \$terminal_index}}}}}}) {
        updated_by
        updated_at
        suspended
        sale_number
        sale
        end_at
        document
        created_by
        created_at
      }
    }


  ''';

const String GET_LOAN = '''
  	query GET_LOAN(\$flag: jsonb, \$startDate: timestamptz, \$endDate: timestamptz, \$selectedCashier: jsonb, \$terminal_index: jsonb) {
      sale(where: {document: {_contains: \$flag}, _and: {end_at: {_gte: \$startDate}, _and: {end_at: {_lte: \$endDate}, _and: {document: {_contains: \$selectedCashier}, _and: {document: {_contains: \$terminal_index}}}}}}) {
        updated_by
        updated_at
        suspended
        sale_number
        sale
        end_at
        document
        created_by
        created_at
      }
    }


  ''';

const String GET_CASHIER_PICK_UP = '''
  	query GET_CASHIER_PICK_UP(\$flag: jsonb, \$startDate: timestamptz, \$endDate: timestamptz, \$selectedCashier: jsonb, \$terminal_index: jsonb) {
      sale(where: {document: {_contains: \$flag}, _and: {end_at: {_gte: \$startDate}, _and: {end_at: {_lte: \$endDate}, _and: {document: {_contains: \$selectedCashier}, _and: {document: {_contains: \$terminal_index}}}}}}) {
        updated_by
        updated_at
        suspended
        sale_number
        sale
        end_at
        document
        created_by
        created_at
      }
    }


  ''';

const String GET_PAID_OUT = '''
  	mutation	GET_PAID_OUT(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
      get_paid_out(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
        created_at
        created_by 
        document
        end_at
        sale
        sale_number
        suspended
        updated_at
        updated_by
      }
    }
''';

const String GET_PICK_UP = '''
  	query GET_PICK_UP(\$flag: jsonb, \$startDate: timestamptz, \$endDate: timestamptz, \$selectedCashier: jsonb, \$terminal_index: jsonb) {
      sale(where: {document: {_contains: \$flag}, _and: {end_at: {_gte: \$startDate}, _and: {end_at: {_lte: \$endDate}, _and: {document: {_contains: \$selectedCashier}, _and: {document: {_contains: \$terminal_index}}}}}}) {
        updated_by
        updated_at
        suspended
        sale_number
        sale
        end_at
        document
        created_by
        created_at
      }
    }


  ''';

const String GET_HOUSE_CHARGES = '''
  	mutation GET_HOUSE_CHARGE_SALES(\$end_date: timestamp, \$start_date: timestamp, \$media_type: Int, \$terminal_index: Int) {
              get_sale_by_media_tender(args: {end_date: \$end_date, start_date: \$start_date, media_type: \$media_type, terminal_index: \$terminal_index}) {
                sale
                document
                created_by
                created_at
                end_at
                sale_number
                suspended
                updated_at
                updated_by
              }
            }
''';

const String GET_EMPLOYEE_BY_ID = '''
    query GET_EMPLOYEE_BY_ID(\$_eq: Int) {
      employee(where: {id: {_eq: \$_eq}}) {
        employee
        document
        id
        is_active
        password
        updated_at
        updated_by
        employee_full_name
        employee_class
        created_by
        created_at
  }
}
''';

const String GET_JSON_RECORD_BY_RECORD_KEY_QUERY = '''
    query GET_JSON_RECORD (\$record_key: String) {
      json_record(where: {record_key: {_eq: \$record_key}}) {
        record_key
        document
        updated_at
      }
    }
  ''';

final Logger _logger = Logger('ReportService');

class ReportService extends GetxService {
  final GraphqlService _graphqlService = Get.find();

  Future<Employee> getEmpByID({required int id}) async {
    try {
      final QueryResult<Object> empRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_EMPLOYEE_BY_ID,
          ),
          variables: <String, dynamic>{
            "_eq": id,
          },
        ),
      );

      if (empRes.hasException) throw empRes.exception.toString();

      final List<Employee> emp =
          (empRes.data!['employee'] as List<dynamic>).map((dynamic i) => Employee.fromJson(i as Map<String, dynamic>)).toList();

      return emp.first;
    } catch (err, stack) {
      _logger.shout("error fetching Employee", err, stack);
      return Employee.empty();
    }
  }

  Future<Either<ServiceError, List<CashierSales>>> getCashierSales({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierSalesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_SALES,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "selected_cashier": selectedCashier,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (cashierSalesResult.hasException) throw cashierSalesResult.exception.toString();

      final List<CashierSales> cashierSales = (cashierSalesResult.data!['get_cashier_sales'] as List<dynamic>)
          .map((dynamic i) => CashierSales.fromJson(i as Map<String, dynamic>))
          .toList();

      return Right<ServiceError, List<CashierSales>>(cashierSales);
    } catch (err, stack) {
      _logger.shout("error fetching cashier sales", err, stack);
      return Left<ServiceError, List<CashierSales>>(ServiceError("Failed to get Cashier Sales"));
    }
  }

  Future<Either<ServiceError, List<CashierDiscounts>>> getCashierDiscounts({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierDiscountsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_DISCOUNTS,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "selected_cashier": selectedCashier,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (cashierDiscountsResult.hasException) throw cashierDiscountsResult.exception.toString();

      final List<CashierDiscounts> cashierDiscounts = (cashierDiscountsResult.data!['get_cashier_discounts'] as List<dynamic>)
          .map((dynamic i) => CashierDiscounts.fromJson(i as Map<String, dynamic>))
          .toList();

      return Right<ServiceError, List<CashierDiscounts>>(cashierDiscounts);
    } catch (err, stack) {
      _logger.shout("error fetching cashier discounts", err, stack);
      return Left<ServiceError, List<CashierDiscounts>>(ServiceError("Failed to get Cashier Discounts"));
    }
  }

  Future<Either<ServiceError, List<CashierStats>>> getCashierStats({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierStatsResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_STATS,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "selected_cashier": selectedCashier,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (cashierStatsResult.hasException) throw cashierStatsResult.exception.toString();

      final List<CashierStats> cashierStats = (cashierStatsResult.data!['get_cashier_stats'] as List<dynamic>)
          .map((dynamic i) => CashierStats.fromJson(i as Map<String, dynamic>))
          .toList();

      return Right<ServiceError, List<CashierStats>>(cashierStats);
    } catch (err, stack) {
      _logger.shout("error fetching cashier stats", err, stack);
      return Left<ServiceError, List<CashierStats>>(ServiceError("Failed to get Cashier Stats"));
    }
  }

  Future<Either<ServiceError, List<CashierMedia>>> getCashierMedia({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierMediaResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_MEDIA,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "selected_cashier": selectedCashier,
            "terminal_index": selectedTerminal,
          },
        ),
      );

      if (cashierMediaResult.hasException) throw cashierMediaResult.exception.toString();

      final List<CashierMedia> cashierMedia = (cashierMediaResult.data!['get_cashier_media'] as List<dynamic>)
          .map((dynamic i) => CashierMedia.fromJson(i as Map<String, dynamic>))
          .toList();

      return Right<ServiceError, List<CashierMedia>>(cashierMedia);
    } catch (err, stack) {
      _logger.shout("error fetching cashier stats", err, stack);
      return Left<ServiceError, List<CashierMedia>>(ServiceError("Failed to get Cashier Media"));
    }
  }

  Future<Either<ServiceError, int>> getCashierPaidOut({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierMediaResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_PAID_OUT,
          ),
          variables: <String, dynamic>{
            "flag": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[5],
              },
            },
            "selectedCashier": <String, dynamic>{
              "saleHeader": <String, dynamic>{"currentCashier": selectedCashier},
            },
            "terminal_index": <String, dynamic>{
              "saleHeader": <String, dynamic>{"settleTerminalNumber": selectedTerminal},
            },
            "startDate": startDate,
            "endDate": endDate,
          },
        ),
      );

      if (cashierMediaResult.hasException) throw cashierMediaResult.exception.toString();

      final List<Sale> cashierMedia =
          (cashierMediaResult.data!['sale'] as List<dynamic>).map((dynamic i) => Sale.fromJson(i as Map<String, dynamic>)).toList();

      int paidOutTotal = 0;

      for (final Sale sale in cashierMedia) {
        paidOutTotal += sale.document.saleHeader.total;
      }

      return Right<ServiceError, int>(paidOutTotal);
    } catch (err, stack) {
      _logger.shout("error fetching cashier stats", err, stack);
      return Left<ServiceError, int>(ServiceError("Failed to get Cashier paid Out"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getPaidOut({
    required String startDate,
    required String endDate,
    required String? terminalIndex,
  }) async {
    try {
      final QueryResult<Object> paidOutResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_PAID_OUT,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": terminalIndex,
          },
        ),
      );

      if (paidOutResult.hasException) throw paidOutResult.exception.toString();

      final List<Sale> paidOutData =
          (paidOutResult.data!['get_paid_out'] as List<dynamic>).map((dynamic i) => Sale.fromJson(i as Map<String, dynamic>)).toList();
      return Right<ServiceError, List<Sale>>(paidOutData);
    } catch (err, stack) {
      _logger.shout("error fetching Paid Out Data", err, stack);
      return Left<ServiceError, List<Sale>>(ServiceError("Failed to get Paid Out Data"));
    }
  }

  Future<Either<ServiceError, int>> getCashierPickUp({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierMediaResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_PICK_UP,
          ),
          variables: <String, dynamic>{
            "flag": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[16],
              },
            },
            "selectedCashier": <String, dynamic>{
              "saleHeader": <String, dynamic>{"currentCashier": selectedCashier},
            },
            "terminal_index": <String, dynamic>{
              "saleHeader": <String, dynamic>{"settleTerminalNumber": selectedTerminal},
            },
            "startDate": startDate,
            "endDate": endDate,
          },
        ),
      );

      if (cashierMediaResult.hasException) throw cashierMediaResult.exception.toString();

      final List<Sale> cashierMedia =
          (cashierMediaResult.data!['sale'] as List<dynamic>).map((dynamic i) => Sale.fromJson(i as Map<String, dynamic>)).toList();

      int pickUpTotal = 0;

      for (final Sale sale in cashierMedia) {
        pickUpTotal += sale.document.saleHeader.total;
      }

      return Right<ServiceError, int>(pickUpTotal);
    } catch (err, stack) {
      _logger.shout("error fetching cashier stats", err, stack);
      return Left<ServiceError, int>(ServiceError("Failed to get Cashier Pick Up"));
    }
  }

  Future<Either<ServiceError, int>> getCashierLoan({
    required String startDate,
    required String endDate,
    required String? selectedCashier,
    required int? selectedTerminal,
  }) async {
    try {
      final QueryResult<Object> cashierMediaResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_CASHIER_LOAN,
          ),
          variables: <String, dynamic>{
            "flag": const <String, dynamic>{
              "saleHeader": <String, dynamic>{
                "saleFlags": <int>[17],
              },
            },
            "selectedCashier": <String, dynamic>{
              "saleHeader": <String, dynamic>{"currentCashier": selectedCashier},
            },
            "terminal_index": <String, dynamic>{
              "saleHeader": <String, dynamic>{"settleTerminalNumber": selectedTerminal},
            },
            "startDate": startDate,
            "endDate": endDate,
          },
        ),
      );

      if (cashierMediaResult.hasException) throw cashierMediaResult.exception.toString();

      final List<Sale> cashierMedia =
          (cashierMediaResult.data!['sale'] as List<dynamic>).map((dynamic i) => Sale.fromJson(i as Map<String, dynamic>)).toList();

      int loanTotal = 0;

      for (final Sale sale in cashierMedia) {
        loanTotal += sale.document.saleHeader.total;
      }

      return Right<ServiceError, int>(loanTotal);
    } catch (err, stack) {
      _logger.shout("error fetching cashier stats", err, stack);
      return Left<ServiceError, int>(ServiceError("Failed to get Cashier Pick Up"));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getHouseCharges({
    required String startDate,
    required String endDate,
    required String? terminalIndex,
  }) async {
    try {
      final QueryResult<Object> houseChargesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_HOUSE_CHARGES,
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
            "media_type": PaymentMediaType.House.index,
          },
        ),
      );

      if (houseChargesResult.hasException) throw houseChargesResult.exception.toString();

      final List<Sale> houseChargeData = (houseChargesResult.data!['get_sale_by_media_tender'] as List<dynamic>)
          .map((dynamic i) => Sale.fromJson(i as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<Sale>>(houseChargeData);
    } catch (err, stack) {
      _logger.shout("error fetching house charge data", err, stack);
      return Left<ServiceError, List<Sale>>(ServiceError("Failed to get house charge Data"));
    }
  }

  // Future<Either<ServiceError, EmployeeRecord>> getServerData({required String startDate, required String endDate, required Employee employee}) async {
  //   try {
  //     final EmployeeRecord record = EmployeeRecord(
  //         employeeID: employee.id!,
  //         mediaBreakdownList: <EmployeeMediaBreakdown>[],
  //         salesByDeptList: <EmployeeSalesByDepartment>[],
  //         employee: employee,
  //         taxRows: <EmployeeSalesTax>[],
  //         empStats: EmployeeStatistics.empty(),
  //         tipBreakdownList: <EmployeeTipBreakdown>[]);
  //     final Either<ServiceError, List<EmployeeSalesByDepartment>> empSalesByDeptRes = await getServerDeptReport(
  //       startDate,
  //       endDate,
  //       null,
  //       employee.id,
  //     );
  //     empSalesByDeptRes.fold((ServiceError l) {
  //       throw l.message;
  //     }, (List<EmployeeSalesByDepartment> r) {
  //       record.salesByDeptList = r;
  //     });
  //     final Either<ServiceError, List<EmployeeMediaBreakdown>> empMediaRes = await getServerMediaReport(
  //       startDate,
  //       endDate,
  //       null,
  //       employee.id,
  //     );
  //     empMediaRes.fold((ServiceError l) {
  //       throw l.message;
  //     }, (List<EmployeeMediaBreakdown> r) {
  //       record.mediaBreakdownList = r;
  //     });
  //     final Either<ServiceError, EmployeeStatistics> employeeStatsRes = await getServerStatsReport(
  //       startDate,
  //       endDate,
  //       null,
  //       employee.id,
  //     );

  //     employeeStatsRes.fold((ServiceError l) {
  //       throw l.message;
  //     }, (EmployeeStatistics r) {
  //       record.empStats = r;
  //     });

  //     final Either<ServiceError, List<EmployeeTipBreakdown>> employeeTipRes = await getServerTipReport(
  //       startDate,
  //       endDate,
  //       null,
  //       employee.id,
  //     );

  //     employeeTipRes.fold((ServiceError l) {
  //       throw l.message;
  //     }, (List<EmployeeTipBreakdown> r) {
  //       record.tipBreakdownList = r;
  //     });

  //     final Either<ServiceError, List<EmployeeSalesTax>> getEmpTaxRes = await getEmployeeSalesTax(
  //       startDate,
  //       endDate,
  //       null,
  //       employee.id,
  //     );

  //     getEmpTaxRes.fold((ServiceError l) {
  //       throw l.message;
  //     }, (List<EmployeeSalesTax> r) {
  //       record.taxRows = r;
  //     });

  //     record.employeeID = employee.id!;
  //     record.employee = employee;

  //     return Right<ServiceError, EmployeeRecord>(record);
  //   } catch (e) {
  //     return Left<ServiceError, EmployeeRecord>(ServiceError(e.toString()));
  //   }
  // }

  Future<Either<ServiceError, List<EmployeeSalesByDepartment>>> getServerDeptReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empSalesByDeptRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMP_SALES_BY_DEPT(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_sales_by_dept(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  department
                  count
                  actual_price
                  gross_price
                  id
                }
              }

            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empSalesByDeptRes.hasException) throw empSalesByDeptRes.exception.toString();

      final List<EmployeeSalesByDepartment> empSalesByDeptList = (empSalesByDeptRes.data!['get_employee_sales_by_dept'] as List<dynamic>)
          .map((dynamic jc) => EmployeeSalesByDepartment.fromJson(jc as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<EmployeeSalesByDepartment>>(empSalesByDeptList);
    } catch (e, stack) {
      _logger.shout(e.toString(), e, stack);
      return Left<ServiceError, List<EmployeeSalesByDepartment>>(ServiceError('Failed to employee sales by department(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeMediaBreakdown>>> getServerMediaReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    List<int> employeeIDs,
  ) async {
    try {
      final List<EmployeeMediaBreakdown> employeeBreakdownList = <EmployeeMediaBreakdown>[];

      for (final int employeeID in employeeIDs) {
        final QueryResult<Object> empMediaRes = await _graphqlService.client.query(
          QueryOptions<Object>(
            document: g.parseString(
              '''
               mutation GET_EMPLOYEE_MEDIA_BREAKDOWN(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_media_breakdown(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}, order_by: {net_total: desc}) {
                  id
                  refunded_sum
                  net_total
                  media
                  count
                  amount
                }
              }
            ''',
            ),
            variables: <String, dynamic>{
              "employee_id": employeeID,
              "start_date": startDate,
              "end_date": endDate,
              "terminal_index": selectedTerminal,
            },
          ),
        );
        if (empMediaRes.hasException) throw empMediaRes.exception.toString();

        employeeBreakdownList.addAll(
          (empMediaRes.data!['get_employee_media_breakdown'] as List<dynamic>)
              .map((dynamic bd) => EmployeeMediaBreakdown.fromJson(bd as Map<String, dynamic>))
              .toList(),
        );
      }
      return Right<ServiceError, List<EmployeeMediaBreakdown>>(employeeBreakdownList);
    } catch (e, stack) {
      _logger.shout("error fetching employee Media(service)", e, stack);
      return Left<ServiceError, List<EmployeeMediaBreakdown>>(ServiceError('Failed to employee Media(Serivce)'));
    }
  }

  Future<Either<ServiceError, EmployeeStatistics>> getServerStatsReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empStatsRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_STATS(\$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int , \$terminal_index: Int ) {
                get_employee_stats(args: {start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id, terminal_index: \$terminal_index}) {
                  id
                  guest_count
                  guest_average
                  check_count
                  check_average
                  cancel_sale_count
                  cancel_sale_amount
                  duration
                  no_sale_count
                  refunded_amount
                  refunded_count
                  reopened_total
                  reopened_count
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empStatsRes.hasException) throw empStatsRes.exception.toString();

      final List<EmployeeStatistics> employeeStatsList = (empStatsRes.data!['get_employee_stats'] as List<dynamic>)
          .map((dynamic bd) => EmployeeStatistics.fromJson(bd as Map<String, dynamic>))
          .toList();
      if (employeeStatsList.isEmpty) {
        return Right<ServiceError, EmployeeStatistics>(EmployeeStatistics.empty());
      } else {
        return Right<ServiceError, EmployeeStatistics>(employeeStatsList.first);
      }
    } catch (e, stack) {
      _logger.shout("Error fecthing Employee Stats", e, stack);
      return Left<ServiceError, EmployeeStatistics>(ServiceError('Failed to Server Stats(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeTipBreakdown>>> getServerTipReport(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empTipRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_TIP(\$employee_id: Int, \$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int) {
                get_employee_tip_breakdown(args: {employee_id: \$employee_id, start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index}) {
                  emp_id
                  tender_media
                  grat_amount
                  tip_amount
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
          },
        ),
      );
      if (empTipRes.hasException) throw empTipRes.exception.toString();

      final List<EmployeeTipBreakdown> employeeTipList = (empTipRes.data!['get_employee_tip_breakdown'] as List<dynamic>)
          .map((dynamic bd) => EmployeeTipBreakdown.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<EmployeeTipBreakdown>>(employeeTipList);
    } catch (e, stack) {
      _logger.shout("error fetching tip Report(service)", e, stack);
      return Left<ServiceError, List<EmployeeTipBreakdown>>(ServiceError('Failed to Tip Report (Serivce)'));
    }
  }

  // Future<Either<ServiceError, List<EmployeeCashDiscount>>> getEmployeeDualPrice(
  //   String startDate,
  //   String endDate,
  //   int? selectedTerminal,
  //   int? employeeID,
  // ) async {
  //   try {
  //     final QueryResult<Object> empCDRes = await _graphqlService.client.query(
  //       QueryOptions<Object>(
  //         document: g.parseString(
  //           '''
  //          mutation GET_EMPLOYEE_CASH_DSCOUNT(\$employee_id: Int, \$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
  //           get_employee_cash_discount(args: {employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
  //             emp_id
  //             amount
  //           }
  //         }
  //         ''',
  //         ),
  //         variables: <String, dynamic>{
  //           "employee_id": employeeID,
  //           "start_date": startDate,
  //           "end_date": endDate,
  //           "terminal_index": null,
  //         },
  //       ),
  //     );
  //     if (empCDRes.hasException) throw empCDRes.exception.toString();

  //     final List<EmployeeCashDiscount> employeeCDList = (empCDRes.data!['get_employee_cash_discount'] as List<dynamic>)
  //         .map((dynamic bd) => EmployeeCashDiscount.fromJson(bd as Map<String, dynamic>))
  //         .toList();
  //     return Right<ServiceError, List<EmployeeCashDiscount>>(employeeCDList);
  //   } catch (e, stack) {
  //     _logger.shout("error fecthing employe dual pricing", e, stack);
  //     return Left<ServiceError, List<EmployeeCashDiscount>>(ServiceError('Failed to Employee Dual Pricing(Serivce)'));
  //   }
  // }

  Future<Either<ServiceError, List<EmployeeSalesTax>>> getEmployeeSalesTax(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empSalesTaxRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
           mutation GET_EMPLOYEE_SALES_TAX(\$employee_id: Int, \$end_date: timestamp, \$start_date: timestamp, \$terminal_index: Int) {
            get_employee_sales_tax(args: {employee_id: \$employee_id, end_date: \$end_date, start_date: \$start_date, terminal_index: \$terminal_index}) {
              emp_id
              description
              index
              tax_amount
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": null,
          },
        ),
      );
      if (empSalesTaxRes.hasException) throw empSalesTaxRes.exception.toString();

      final List<EmployeeSalesTax> employeeCDList = (empSalesTaxRes.data!['get_employee_sales_tax'] as List<dynamic>)
          .map((dynamic bd) => EmployeeSalesTax.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right<ServiceError, List<EmployeeSalesTax>>(employeeCDList);
    } catch (e, stack) {
      _logger.shout("error fecthing employee sales tax (service)", e, stack);
      return Left<ServiceError, List<EmployeeSalesTax>>(ServiceError('Failed to Employee Sales Tax(Serivce)'));
    }
  }

  Future<Either<ServiceError, int>> getTakeOutFee({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    int? employee_id,
  }) async {
    try {
      final QueryResult<Object> takeoutRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
             mutation GET_TAKEOUT_FEE_TOTAL(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int, \$employee_id: Int) {
              get_surcharge_total(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index, employee_id: \$employee_id}) {
                total
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
            "employee_id": employee_id,
          },
        ),
      );
      if (takeoutRes.hasException) throw takeoutRes.exception.toString();

      final int? discounts = int.tryParse((takeoutRes.data!['get_surcharge_total'][0]['total'] as dynamic).toString());
      return Right<ServiceError, int>(discounts ?? 0);
    } catch (e, stack) {
      _logger.shout("Error fetching takeout fee(service)", e, stack);
      return Left<ServiceError, int>(ServiceError("Error fetching takeout fee"));
    }
  }

  Future<Either<ServiceError, List<EmployeeRecord>>> getColeServerReport({
    required String startDate,
    required String endDate,
    int? empID,
    int? termIdx,
  }) async {
    try {
      final List<EmployeeRecord> empRecordList = <EmployeeRecord>[];
      final Either<ServiceError, List<EmployeeSalesByDepartment>> empSalesByDeptRes = await getServerDeptReport(
        startDate,
        endDate,
        termIdx,
        empID,
      );
      await empSalesByDeptRes.fold((ServiceError l) {
        _logger.shout("error getting server department data");
        throw l.message;
      }, (List<EmployeeSalesByDepartment> r) async {
        for (final EmployeeSalesByDepartment deptRecord in r) {
          if (deptRecord.id != null && !empRecordList.any((EmployeeRecord element) => element.employeeID == deptRecord.id)) {
            empRecordList.add(
              EmployeeRecord(
                employeeID: deptRecord.id!,
                employee: await getEmpByID(id: deptRecord.id!),
                taxRows: <EmployeeSalesTax>[],
                salesByDeptList: <EmployeeSalesByDepartment>[],
                mediaBreakdownList: <EmployeeMediaBreakdown>[],
                tipBreakdownList: <EmployeeTipBreakdown>[],
                empStats: EmployeeStatistics.empty(),
              ),
            );
          }
        }

        for (final EmployeeRecord rec in empRecordList) {
          for (final EmployeeSalesByDepartment element in r) {
            if (rec.employeeID == element.id) {
              rec.salesByDeptList.add(element);
            }
          }
        }
      });

      final Either<ServiceError, List<EmployeeMediaBreakdown>> empMediaRes = await getServerMediaReport(
        startDate,
        endDate,
        termIdx,
        empID != null ? <int>[empID] : empRecordList.map((EmployeeRecord e) => e.employeeID).toList(),
      );

      empMediaRes.fold((ServiceError l) {
        _logger.shout('error getting server media');
        throw l.message;
      }, (List<EmployeeMediaBreakdown> r) {
        for (final EmployeeRecord empRec in empRecordList) {
          for (final EmployeeMediaBreakdown element in r) {
            if (empRec.employeeID == element.id) {
              empRec.mediaBreakdownList.add(element);
              if (element.media == 0.toString()) {
                empRec.serverCashTotal = element.net_total;
              }
            }
          }
        }
      });

      final Either<ServiceError, List<EmployeeStatistics>> employeeStatsRes = await getServerStatsReportList(
        startDate,
        endDate,
        termIdx,
        empID,
      );

      employeeStatsRes.fold((ServiceError l) {
        _logger.shout('error getting server stats');
        throw l.message;
      }, (List<EmployeeStatistics> r) {
        for (final EmployeeRecord empRecord in empRecordList) {
          final EmployeeStatistics currentRec = r.firstWhere((EmployeeStatistics element) => element.id == empRecord.employeeID);
          empRecord.empStats.cancel_sale_amount = currentRec.cancel_sale_amount;
          empRecord.empStats.cancel_sale_count = currentRec.cancel_sale_count;
          empRecord.empStats.check_average = currentRec.check_average;
          empRecord.empStats.check_count = currentRec.check_count;
          empRecord.empStats.duration = currentRec.duration;
          empRecord.empStats.guest_average = currentRec.guest_average;
          empRecord.empStats.guest_count = currentRec.guest_count;
          empRecord.empStats.no_sale_count = currentRec.no_sale_count;
          empRecord.empStats.refunded_amount = currentRec.refunded_amount;
          empRecord.empStats.refunded_count = currentRec.refunded_count;
          empRecord.empStats.reopened_count = currentRec.reopened_count;
          empRecord.empStats.reopened_total = currentRec.reopened_total;
        }
      });

      final Either<ServiceError, List<EmployeeTipBreakdown>> employeeTipRes = await getServerTipReport(
        startDate,
        endDate,
        termIdx,
        empID,
      );

      employeeTipRes.fold((ServiceError l) {
        _logger.shout("error getting server tip data");
        throw l.message;
      }, (List<EmployeeTipBreakdown> r) {
        for (final EmployeeRecord empRecord in empRecordList) {
          for (final EmployeeTipBreakdown element in r) {
            if (empRecord.employeeID == element.emp_id) {
              empRecord.tipBreakdownList.add(element);
              if (element.tender_media == 2) {
                empRecord.ccTips += element.tip_amount;
                empRecord.ccGratuity += element.grat_amount;
              }
            }
          }
        }
      });

      for (final EmployeeRecord empRecord in empRecordList) {
        final Either<ServiceError, List<EmployeeSalesTax>> employeeTaxRes = await getEmployeeSalesTax(
          startDate,
          endDate,
          termIdx,
          empRecord.employeeID,
        );

        employeeTaxRes.fold((ServiceError l) {
          _logger.shout("error getting server tax data");

          empRecord.taxRows = <EmployeeSalesTax>[];
          throw l.message;
        }, (List<EmployeeSalesTax> r) {
          empRecord.taxRows.addAll(r);
        });
      }

      for (final EmployeeRecord empRecord in empRecordList) {
        final Either<ServiceError, int> takeoutFeeRes =
            await getTakeOutFee(startDate: startDate, endDate: endDate, employee_id: empRecord.employeeID);

        takeoutFeeRes.fold((ServiceError l) {
          _logger.shout('error getting server takeout fees');
          empRecord.takeoutFeesTotal = 0;
          throw l.message;
        }, (int r) {
          empRecord.takeoutFeesTotal = r;
        });
      }

      return Right<ServiceError, List<EmployeeRecord>>(empRecordList);
    } catch (err, stack) {
      _logger.shout(err.toString(), err, stack);
      return Left<ServiceError, List<EmployeeRecord>>(ServiceError('Failed to Server Stats(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<EmployeeStatistics>>> getServerStatsReportList(
    String startDate,
    String endDate,
    int? selectedTerminal,
    int? employeeID,
  ) async {
    try {
      final QueryResult<Object> empStatsRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              mutation GET_EMPLOYEE_STATS(\$start_date: timestamp, \$end_date: timestamp, \$employee_id: Int , \$terminal_index: Int ) {
                get_employee_stats(args: {start_date: \$start_date, end_date: \$end_date, employee_id: \$employee_id, terminal_index: \$terminal_index}) {
                  id
                  guest_count
                  guest_average
                  check_count
                  check_average
                  cancel_sale_count
                  cancel_sale_amount
                  duration
                  no_sale_count
                  refunded_amount
                  refunded_count
                  reopened_total
                  reopened_count
                }
              }
            ''',
          ),
          variables: <String, dynamic>{
            "employee_id": employeeID,
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
          },
        ),
      );
      if (empStatsRes.hasException) throw empStatsRes.exception.toString();

      final List<EmployeeStatistics> employeeBreakdownList = (empStatsRes.data!['get_employee_stats'] as List<dynamic>)
          .map((dynamic bd) => EmployeeStatistics.fromJson(bd as Map<String, dynamic>))
          .toList();
      return Right(employeeBreakdownList);
    } catch (e, stack) {
      _logger.shout("Error fecthing Employee Stats", e, stack);
      return Left(ServiceError('Failed to Server Stats(Serivce)'));
    }
  }

  Future<Either<ServiceError, JsonRecordReasonCode>> getReasonCodeRecord() async {
    try {
      final QueryResult<Object> reasonCodeRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            GET_JSON_RECORD_BY_RECORD_KEY_QUERY,
          ),
          variables: const <String, dynamic>{"record_key": 'reasonCode'},
        ),
      );
      if (reasonCodeRes.hasException) throw reasonCodeRes.exception.toString();

      final List<dynamic> recordList = reasonCodeRes.data!['json_record'] as List<dynamic>;

      final JsonRecordReasonCode reasonCodeRecord =
          recordList.isEmpty ? JsonRecordReasonCode.empty() : JsonRecordReasonCode.fromJson(recordList[0] as Map<String, dynamic>);

      return Right<ServiceError, JsonRecordReasonCode>(reasonCodeRecord);
    } catch (e, stack) {
      _logger.shout("Error fecthing reason codes", e, stack);
      return Left<ServiceError, JsonRecordReasonCode>(ServiceError('Failed to Server Stats(Serivce)'));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getEmployeeSalesFiltered({
    required String startDate,
    required String endDate,
    int? selectedTerminal,
    required List<int> employee_id,
    required List<int> sale_flags,
    required List<int> mediaFlags,
  }) async {
    final String selectedEmployees = '{${employee_id.map((int e) => e.toString()).join(',')}}';
    final String selectedFlags = '{${sale_flags.map((int e) => e.toString()).join(',')}}';
    final String selectedFlagsAsString = '{${mediaFlags.map((int e) => e.toString()).join(',')}}';
    print(selectedEmployees);
    print(selectedFlags);
    print(selectedFlagsAsString);
    print(startDate);
    print(endDate);

    try {
      final QueryResult<Object> employeeSalesRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
          mutation GET_EMPLOYEE_SALES(\$start_date: timestamp, \$end_date: timestamp, \$terminal_index: Int, \$selected_employees: _int4, \$selected_flags: _int4, \$media_filter: _int4) {
            get_employee_sales(args: {start_date: \$start_date, end_date: \$end_date, terminal_index: \$terminal_index, selected_employees: \$selected_employees, selected_flags: \$selected_flags, media_filter: \$media_filter}) {
              created_at
              created_by
              document
              end_at
              sale
              sale_number
              suspended
              updated_at
              updated_by
            }
          }
          ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
            "terminal_index": selectedTerminal,
            "selected_employees": selectedEmployees,
            "selected_flags": selectedFlags,
            "media_filter": selectedFlagsAsString,
          },
        ),
      );
      if (employeeSalesRes.hasException) throw employeeSalesRes.exception.toString();

      final List<Sale> saleList =
          (employeeSalesRes.data!['get_employee_sales'] as List<dynamic>).map((dynamic bd) => Sale.fromJson(bd as Map<String, dynamic>)).toList();
      return Right(saleList);
    } catch (e, stack) {
      _logger.shout("error fetching transaction history(service)", e, stack);
      return Left(ServiceError('Failed to fetch Employee Sales)'));
    }
  }

  Future<Either<ServiceError, List<Sale>>> getEmployeeCompletedSalesAll({
    required String startDate,
    required String endDate,
    required int employeeID,
  }) async {
    try {
      final QueryResult<Object> employeeSalesRes = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(
            '''
              query GET_EMPLOYEE_SALES(\$start_date: timestamptz, \$end_date: timestamptz) {
                sale(where: {_and: [{end_at: { _gt: \$start_date, _lt: \$end_date}}, {suspended: {_eq: false}}, {document: {_cast: {String:{_ilike: "%currentEmployeeNumber_: $employeeID%"}}}}]}, order_by: {end_at: desc}) 
                  {
                    created_at
                    created_by
                    document
                    end_at
                    sale
                    sale_number
                    suspended
                    updated_at
                    updated_by
                  }
                }
            ''',
          ),
          variables: <String, dynamic>{
            "start_date": startDate,
            "end_date": endDate,
          },
        ),
      );
      if (employeeSalesRes.hasException) throw employeeSalesRes.exception.toString();

      final List<Sale> saleList = (employeeSalesRes.data!['sale'] as List<dynamic>)
          .map((dynamic bd) => Sale.fromJson(bd as Map<String, dynamic>))
          .where(
            (Sale s) =>
                s.document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index) &&
                !s.document.saleHeader.saleFlags.contains(SaleFlags.PAID_OUT.index),
          )
          .toList();
      return Right(saleList);
    } catch (e, stack) {
      _logger.shout("error fetching transaction history(service)", e, stack);
      return Left(ServiceError('Failed to fetch Employee Sales'));
    }
  }
}
