// ignore_for_file: always_specify_types, avoid_dynamic_calls

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

const String DEFAULT_SALE_NUMBER_PK = "7d7dfbb7-2448-4160-b151-666bcdabfea7";

const String SALE_FIELDS_FRAGMENT = '''
  created_at
  created_by
  sale
  end_at
  document
  updated_by
  updated_at
  sale_number
  suspended
''';

const String CREATE_SALE_MUTATION = '''
  mutation CREATE_SALE(\$saleObject: sale_insert_input!) {
    insert_sale_one(object: \$saleObject) {
      $SALE_FIELDS_FRAGMENT
    }
  }
''';

const String UPSERT_SALE_MUTATION = '''
  mutation UPSERT_SALE(\$sale: sale_insert_input!) {
    insert_sale_one(object: \$sale, on_conflict: {constraint: sale_pkey, update_columns: [ 
        created_by
        end_at
        document
        updated_by
        suspended
      ]}
    ) {
      $SALE_FIELDS_FRAGMENT
    }
  }
''';

const String UPSERT_MULTIPLE_MUTATION = '''
  mutation UPSERT_MULTIPLE(\$sales: [sale_insert_input!]!) {
    insert_sale(objects: \$sales, on_conflict: {constraint: sale_pkey, update_columns: [ 
        created_by
        end_at
        document
        updated_by
        suspended
      ]}
    ) {returning{
    created_at
  created_by
  sale
  end_at
  document
  updated_by
  updated_at
  sale_number
  suspended
    }
  }
  }
''';

const String UPDATE_SALE_BY_PK_MUTATION = '''
  mutation UPDATE_SALE_BY_PK(\$salePk: uuid!, \$sale: sale_set_input) {
    update_sale_by_pk(pk_columns: {sale: \$salePk}, _set: \$sale) {
      $SALE_FIELDS_FRAGMENT
    }
  }
''';

const String GET_SALE_BY_PK_QUERY = '''
  query GET_SALE_BY_PK(\$sale: uuid!) {
    sale_by_pk(sale: \$sale) {
      $SALE_FIELDS_FRAGMENT
    }
  }
''';

const String GET_SALES_BY_PK_QUERY = '''
  query GET_SALES_BY_IDS(\$saleIDs: [uuid!]!) {
    sale(where: {sale: {_in: \$saleIDs}}) {
      $SALE_FIELDS_FRAGMENT
    }
  }
''';

const String GET_SALE_BY_SALE_NUMBER_LANE_DATE_QUERY = '''
  query GET_SALE_BY_SALE_NUMBER_LANE_DATE(\$sale_number: Int, \$start_date: timestamptz, \$end_date: timestamptz, \$lane_number_string: String) {
    sale(where: {_and: [{sale_number: {_eq: \$sale_number}}, {end_at: {_gte: \$start_date, _lte: \$end_date}}], document: {_cast: {String: {_ilike: \$lane_number_string}}}}) {
      end_at
      sale_number
      sale
      created_at
      created_by
      document
      suspended
      updated_at
      updated_by
    }
  }
''';

const String GET_SUSPENDED_SALES_QUERY = '''
  query GET_SUSPENDED_SALES(\$saleHeader: jsonb) {
    sale(where: {document: {_contains: \$saleHeader}}, order_by: {updated_at: desc}) {
      document
      sale
      created_at
      created_by
      end_at
      sale_number
      suspended
      updated_at
      updated_by
    }
  }
''';

const String GET_OPEN_SALES_QUERY = """
  query GET_OPEN_SALES {
    sale(where: {suspended: {_eq: true}}, order_by: {updated_at: desc}) {
      document
      sale
      created_at
      created_by
      end_at
      sale_number
      suspended
      updated_at
      updated_by
    }
  }
  """;

const String INCREMENT_CURRENT_SALE_NUMBER_QUERY = '''
  mutation INCREMENT_CURRENT_SALE_NUMBER {
    update_sale_number_by_pk(pk_columns:{sale_number:"$DEFAULT_SALE_NUMBER_PK"} _inc:{current: 1}){
      current
      sale_number
    }
  }
''';

const String GET_UPDATED_AT_QUERY = '''
  query MyQuery {
    sale_updated {
      updated_at
      sale_updated
    }
  }
''';

const String RESET_SALE_NUMBER = '''
  mutation RESET_SALE_NUMBER{
    update_sale_number(where:{sale_number:{_eq:"$DEFAULT_SALE_NUMBER_PK"}},_set:{current:0}){
      affected_rows
    }
  }
''';

const String GET_TIPPABLE_SALES_TODAY = '''
query GET_TIPPABLE_SALES_TODAY(\$completeHeader: jsonb, \$start_date: timestamptz, \$sale_number: Int, \$end_date: timestamptz, \$limit: Int, \$offset: Int) {
  sale(where: {_and: [{sale_number:{_eq:\$sale_number}}, {end_at: {_gte: \$start_date, _lte: \$end_date}}, {document: {_contains: \$completeHeader}}]}, limit: \$limit, offset: \$offset, order_by: {end_at: desc_nulls_last}) {
    end_at
    sale_number
    sale
    created_at
    created_by
    document
    suspended
    updated_at
    updated_by
  }
}
''';

// ignore: non_constant_identifier_names
final int TAX_RATE_TEMP = int.parse(Platform.environment["TAX_RATE"] ?? "6");
final Logger _logger = Logger('SaleService');
String currentUpdatedAt = "";

class SaleService extends GetxService {
  final GraphqlService _graphqlService = Get.find();
  final NotificationService _notificationService = Get.find();

  // Used to lock the sales in the openSalesController while sale data is being refreshed
  final RxBool locked = false.obs;
  final RxList<Sale> allOpenSales = <Sale>[].obs;

  late Timer salesTimer;

  @override
  Future<void> onReady() async {
    // Timer that checks if open sales list needs refetched.
    salesTimer = Timer.periodic(const Duration(seconds: 1), (Timer t) async {
      final bool needsUpdate = await checkCurrentSaleUpdatedAt();
      if (needsUpdate) {
        locked.value = true;
        await fetchSales();
      }
    });
    await fetchSales();
    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onClose() async {
    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> fetchSales() async {
    final Either<ServiceError, List<Sale>> loadSuspendedResult = await getOpenSales();
    loadSuspendedResult.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (List<Sale> resultSales) {
        allOpenSales.value = resultSales;
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, int>> getCurrentSaleNumber() async {
    try {
      final QueryResult incrementCurrentSaleNumberResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(INCREMENT_CURRENT_SALE_NUMBER_QUERY),
        ),
      );

      if (incrementCurrentSaleNumberResult.hasException) {
        throw incrementCurrentSaleNumberResult.exception.toString();
      }

      final currentSaleNumber = incrementCurrentSaleNumberResult.data!['update_sale_number_by_pk']["current"];

      return Right(int.parse(currentSaleNumber.toString()));
    } catch (err, stack) {
      _logger.severe(
        "error getting current sale number",
        err,
        stack,
      );

      return Left(ServiceError("Failed to get sale!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<bool> checkCurrentSaleUpdatedAt() async {
    try {
      final QueryResult updatedResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(GET_UPDATED_AT_QUERY),
        ),
      );
      if (updatedResult.hasException) {
        throw updatedResult.exception.toString();
      }
      final String res = (updatedResult.data!['sale_updated'] as List).first["updated_at"] as String;

      if (currentUpdatedAt.isEmpty) {
        currentUpdatedAt = res;
        return false;
      }
      if (res == currentUpdatedAt) return false;
      currentUpdatedAt = res;
      return true;
    } catch (err, stack) {
      _logger.severe(
        "error getting current sale number",
        err,
        stack,
      );
      return false;
    }
  }

  ///{end_at: {_gte: $dateLimit}},
  ///
  /// $dateLimit: timestamptz = ""
  ///
  ///
  ///
  Future<Either<ServiceError, Sale>> getLastSettledSale(int employeeID) async {
    try {
      final QueryResult<Object> suspendedSalesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString("""
            query GET_LAST_COMPLETED_SALE(\$saleHeader1: jsonb, \$saleHeader2: jsonb, \$saleHeader3: jsonb, \$dateLimit: timestamptz = "") {
              sale(where: {_and: [{end_at: {_gt: \$dateLimit}}, {document: {_contains: \$saleHeader1}}, {document: {_cast: {String: {_ilike: "%settleEmployeeNumber_: $employeeID%"}}}}], _not: {_or: [{document: {_contains: \$saleHeader2}}, {document: {_contains: \$saleHeader3}}]}}, order_by: {end_at: desc}, limit: 1) {
                document
                sale
                created_at
                created_by
                end_at
                sale_number
                suspended
                updated_at
                updated_by
              }
            }
          """),
          variables: {
            "saleHeader1": {
              "saleHeader": {
                "saleFlags": [
                  SaleFlags.COMPLETED.index,
                ], // Completed flag
              },
            },
            "saleHeader2": {
              "saleHeader": {
                "saleFlags": [
                  SaleFlags.NONREFUNDABLE.index,
                ], // Reopened flag
              },
            },
            "saleHeader3": {
              "saleHeader": {
                "saleFlags": [
                  SaleFlags.REFUNDED.index,
                ], // Reopened flag
              },
            },
            "dateLimit": DateTime.now().toUtc().subtract(const Duration(days: 1)).toString(),
          },
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<Sale> incomingSaleList = (suspendedSalesResult.data!['sale'] as List).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList();

      if (incomingSaleList.isEmpty) {
        return Left(ServiceError("No completed sales found!"));
      }

      return Right(
        incomingSaleList.first,
      );
    } catch (err, stack) {
      _logger.severe(
        "error fetching last completed sale",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get last settled sale!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> upsertMultiple({
    required List<Sale> sales,
    required Employee employee,
    required int terminalNumber,
  }) async {
    final List<Map<String, dynamic>> sanList = <Map<String, dynamic>>[];
    try {
      for (final Sale sale in sales) {
        final bool isNewSale = sale.sale.isEmpty || sale.sale == "";

        sale.updated_by = employee.employee;

        final List<String> fieldsToSanitize = ['created_at', 'updated_at'];

        if (isNewSale) {
          sale.created_by = employee.employee;
          if (sale.document.saleHeader.currentEmployeeNumber == 0) {
            sale.document.saleHeader.currentEmployeeNumber = employee.id ?? 0;
          }
          if (sale.document.saleHeader.startEmployeeNumber == 0) {
            sale.document.saleHeader.startEmployeeNumber = employee.id ?? 0;
          }
          if (sale.document.saleHeader.startTerminalNumber == 0) {
            sale.document.saleHeader.startTerminalNumber = terminalNumber;
          }
          fieldsToSanitize.add('sale');

          final Either<ServiceError, int> currentSaleNumberResult = await getCurrentSaleNumber();
          final int saleNumberToUse = currentSaleNumberResult.getOrElse((ServiceError error) => 1);
          sale.sale_number = saleNumberToUse;
          sale.document.saleHeader.saleNumber = saleNumberToUse;
        }

        sale.suspended =
            sale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index) || sale.document.saleHeader.currentTerminalNumber != 0;

        final Map<String, dynamic> santizedSale = Helpers.sanitizeEntity(
          sale.toJson(),
          fieldsToSanitize,
        );
        sanList.add(santizedSale);
      }

      final QueryResult upsertSaleResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(UPSERT_MULTIPLE_MUTATION),
          variables: <String, dynamic>{
            "sales": sanList,
          },
        ),
      );

      unawaited(updateSaleUpdated());

      if (upsertSaleResult.hasException) {
        throw upsertSaleResult.exception.toString();
      }

      final List<Sale> updatedSales =
          (upsertSaleResult.data!['insert_sale']['returning'] as List).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList();

      return Right(updatedSales);
    } catch (err, stack) {
      _logger.severe(
        "error upsert sale",
        err,
        stack,
      );

      return Left(ServiceError("Failed to upsert sales!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, Sale>> upsert({
    required Sale sale,
    required Employee employee,
    required int terminalNumber,
  }) async {
    try {
      final bool isNewSale = sale.sale.isEmpty || sale.sale == "";

      sale.updated_by = employee.employee;

      final List<String> fieldsToSanitize = ['created_at', 'updated_at'];

      if (isNewSale) {
        sale.created_by = employee.employee;
        if (sale.document.saleHeader.currentEmployeeNumber == 0) {
          sale.document.saleHeader.currentEmployeeNumber = employee.id ?? 0;
        }
        if (sale.document.saleHeader.startEmployeeNumber == 0) {
          sale.document.saleHeader.startEmployeeNumber = employee.id ?? 0;
        }
        if (sale.document.saleHeader.startTerminalNumber == 0) {
          sale.document.saleHeader.startTerminalNumber = terminalNumber;
        }
        if (sale.sale_number == 0) {
          final Either<ServiceError, int> currentSaleNumberResult = await getCurrentSaleNumber();
          final int saleNumberToUse = currentSaleNumberResult.getOrElse((ServiceError error) => 1);
          sale.sale_number = saleNumberToUse;
          sale.document.saleHeader.saleNumber = saleNumberToUse;
        }
        fieldsToSanitize.add('sale');
      }

      sale.suspended = sale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index) || sale.document.saleHeader.currentTerminalNumber != 0;

      final Map<String, dynamic> santizedSale = Helpers.sanitizeEntity(
        sale.toJson(),
        fieldsToSanitize,
      );

      final QueryResult upsertSaleResult = await _graphqlService.client.mutate(
        MutationOptions(
          document: g.parseString(UPSERT_SALE_MUTATION),
          variables: <String, dynamic>{
            "sale": santizedSale,
          },
        ),
      );

      unawaited(updateSaleUpdated());

      if (upsertSaleResult.hasException) {
        throw upsertSaleResult.exception.toString();
      }

      final Sale updatedSale = Sale.fromJson(
        upsertSaleResult.data!['insert_sale_one'] as Map<String, dynamic>,
      );

      return Right(updatedSale);
    } catch (err, stack) {
      _logger.severe(
        "error upsert sale",
        err,
        stack,
      );

      return Left(ServiceError("Failed to update sale!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> getSuspendedSales({
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> suspendedSalesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_SUSPENDED_SALES_QUERY),
          variables: {
            "saleHeader": {
              "saleHeader": {
                "saleFlags": [
                  SaleFlags.SUSPENDED.index,
                ], // Suspended flag
              },
            },
          },
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<Sale> incomingSuspendedSales =
          (suspendedSalesResult.data!['sale'] as List).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList();
      return Right(
        employeeId != null
            ? incomingSuspendedSales
                .where(
                  (Sale s) => s.document.saleHeader.currentEmployeeNumber == employeeId,
                )
                .toList()
            : incomingSuspendedSales,
      );
    } catch (err, stack) {
      _logger.severe(
        "error fetching suspended sales",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get suspended sales!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updateSaleUpdated() async {
    try {
      final QueryResult<Object> updatedResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString('''
            mutation MyQuery {
              update_sale_updated(where: {}, _set: {updated_at: now}) {
                returning {
                  sale_updated
                  updated_at
                }
              }
            }
            '''),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if (updatedResult.hasException) {
        throw updatedResult.exception.toString();
      }
    } catch (err, stack) {
      _logger.severe(
        "error fetching suspended sales",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> getOpenSales({
    int? employeeId,
  }) async {
    try {
      final QueryResult<Object> suspendedSalesResult = await _graphqlService.client.query(
        QueryOptions<Object>(
          document: g.parseString(GET_OPEN_SALES_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      final List<Sale> incomingSuspendedSales =
          (suspendedSalesResult.data!['sale'] as List).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList();
      return Right(
        employeeId != null
            ? incomingSuspendedSales
                .where(
                  (Sale s) => s.document.saleHeader.currentEmployeeNumber == employeeId,
                )
                .toList()
            : incomingSuspendedSales,
      );
    } catch (err, stack) {
      _logger.severe(
        "error fetching open sales",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get open sales!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, Sale>> getSaleByPk(String uuid) async {
    try {
      final QueryResult saleResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(GET_SALE_BY_PK_QUERY),
          variables: {
            "sale": uuid,
          },
        ),
      );
      if (saleResult.data?['sale_by_pk'] == null) throw "sale not found";
      return Right(Sale.fromJson(saleResult.data!['sale_by_pk'] as Map<String, dynamic>));
    } catch (err, stack) {
      _logger.severe(
        "error fetching sale by primary key",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get sale!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> getSalesByPk(List<String> ids) async {
    try {
      final QueryResult saleResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(GET_SALES_BY_PK_QUERY),
          variables: {
            "saleIDs": ids,
          },
        ),
      );
      if (((saleResult.data?['sale'] ?? []) as List<dynamic>).isEmpty) throw "sales not found";
      return Right((saleResult.data!['sale'] as List<dynamic>).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList());
    } catch (err, stack) {
      _logger.severe(
        "error fetching sale by primary key",
        err,
        stack,
      );
      return Left(ServiceError("Failed to get sale!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> getTodaysTippableSalesBySaleNumber(
    int saleNumber,
  ) async {
    try {
      final DateFormat timezoneFormatter = DateFormat("yyyy-MM-ddTHH:mm:ss");

      final DateTime now = DateTime.now();

      // Set the start time to look for sales from 5AM to now.
      // If it's before 5AM today, use yesterday's 5AM as a start point
      DateTime startDateTime = DateTime(now.year, now.month, now.day, 5);
      if (now.hour < 5) {
        startDateTime = startDateTime.subtract(const Duration(days: 1));
      }

      final String startString = timezoneFormatter.format(startDateTime.toUtc());
      final String endString = timezoneFormatter.format(now.toUtc());

      final QueryResult saleResult = await _graphqlService.client.query(
        QueryOptions(
          document: g.parseString(GET_TIPPABLE_SALES_TODAY),
          variables: {
            "start_date": startString,
            "end_date": endString,
            "completeHeader": {
              "saleHeader": {
                "saleFlags": [SaleFlags.COMPLETED.index],
              },
            },
            "sale_number": saleNumber,
            "limit": 10,
            "offset": 0,
          },
        ),
      );

      if (saleResult.hasException) throw saleResult.exception.toString();

      final List<Sale> sales = (saleResult.data!['sale'] as List).map((s) => Sale.fromJson(s as Map<String, dynamic>)).toList();

      if (sales.isEmpty) return Left(ServiceError("No Tippable Sales Found!"));

      return Right(sales);
    } catch (err, stack) {
      _logger.severe(
        "error fetching today's tippable sales",
        err,
        stack,
      );
      return Left(ServiceError("No Tippable Sales Found!"));
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///Joe's old way of calculating sales tax
  ///this has errors in rounding because it rounds each row, as opposed
  ///to rounding the final result
  // int calculateSalesTax(int price, double taxRate) {
  //   final double tax = taxRate / 100;
  //   final double priceWithTax = (price * (tax / 100)) * 100;

  //   return priceWithTax.round();
  // }

  //Cole and Jordan's way of calculating sales tax
  //this is version of tax calculation will return doubles that are not rounded
  double calculateSalesTax(int price, double taxRate) {
    // Calculate the tax amount
    final double taxAmount = (price * ((taxRate / 100) / 100)) * 100;
    return taxAmount;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  int percentChange(int price, double percent) {
    final double percentChange = percent / 100;
    final double newPrice = (price * (percentChange / 100)) * 100;

    return newPrice.round();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, String>> completeSale({
    required Sale sale,
    SaleTender? tender,
  }) async {
    final http.Response resp = await http.post(
      Uri.parse("${Constants.baseUrl}/sale/complete"),
      headers: {
        "Content-Type": "application/json",
      },
      body: json.encode({
        "sale": sale.toJson(),
        if (tender != null) "tender": tender.toJson(),
        "paymentRoute": 0,
      }),
    );
    if (resp.statusCode != 200) {
      return Left(ServiceError("Failed to complete sale: ${resp.statusCode}"));
    } else {
      return Right(resp.body);
    }
  }
}
