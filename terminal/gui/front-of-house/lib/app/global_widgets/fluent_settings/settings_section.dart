import 'package:desktop/app/global_widgets/fluent_settings/settings_option.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FluentSettingsSection extends StatelessWidget {
  const FluentSettingsSection({
    required this.options,
    required this.heading,
    this.backgroundColor,
    this.visible = true,
  });
  final List<FluentSettingsOption> options;
  final String heading;
  final Color? backgroundColor;
  final bool visible;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: ColoredBox(
        color: backgroundColor ?? Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(5, 10, 5, 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                heading,
                style: Get.textTheme.headlineSmall,
              ),
              ...options.map((FluentSettingsOption e) {
                return Padding(
                  padding: const EdgeInsets.fromLTRB(10, 2, 10, 2),
                  child: e,
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }
}
