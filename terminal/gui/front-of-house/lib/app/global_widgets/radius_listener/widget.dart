import 'package:desktop/app/global_widgets/radius_listener/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RadiusListener extends StatelessWidget {
  const RadiusListener({
    required this.child,
    required this.onTap,
    this.tapRadius = 30,
  });

  final Widget child;
  final Function onTap;
  final double tapRadius;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RadiusListenerController>(
      init: RadiusListenerController(),
      builder: (RadiusListenerController controller) {
        return Listener(
          onPointerDown: (PointerEvent event) {
            controller.setPosition(event.position.dx, event.position.dy);
          },
          onPointerUp: (PointerEvent event) async {
            controller.tryClick(event.position.dx, event.position.dy, tapRadius, onTap);
          },
          child: child,
        );
      },
    );
  }
}
