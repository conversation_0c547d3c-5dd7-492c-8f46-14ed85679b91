// ignore_for_file: always_specify_types, no_leading_underscores_for_local_identifiers
import 'dart:async';
import 'dart:convert';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/error_correct_type.dart';
import 'package:desktop/app/data/enums/num_pad_type.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/enums/register_menu_options.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_mode.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/seat_select_types.dart';
import 'package:desktop/app/data/models/action.dart' as a;
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/cash_drawer.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/department.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/item.service.dart';
import 'package:desktop/app/data/services/liquor_control.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/app/data/services/pole_display.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/header.dart';
import 'package:desktop/app/global_widgets/num_pad/controller.dart';
import 'package:desktop/app/global_widgets/num_pad/widget.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:desktop/app/global_widgets/widget/confirmation_dialog.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/global_widgets/widget/thin_bottom_sheet.dart';
import 'package:desktop/app/modules/register/dialogs/add_gratuity/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/admin_permission/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/combine_checks/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/forgive_tax/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/modify/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/quantity_dialog.dart';
import 'package:desktop/app/modules/register/dialogs/retrieve/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/sale_info/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/seat_select/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/select_sale/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/suspend/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/dialogs/tables/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tender/new.dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tip_adjust/dialog.dart';
import 'package:desktop/app/modules/register/widgets/departments/controller.dart';
import 'package:desktop/app/modules/register/widgets/items/controller.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/sales.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/transaction_history/recall_items.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/widget.dart';
import 'package:desktop/app/modules/time_clock/transfer_sales/dialog.dart';
import 'package:desktop/app/routes/app_pages.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:lottie/lottie.dart';

final Logger _logger = Logger('RegisterController');
late SaleController _saleController;
late OpenSalesController _openSalesController;
late NumPadController _numpadController;

class RegisterController extends GetxController {
  final ItemService _itemService = Get.find();
  final ConfigService _configService = Get.find();
  final CashDrawerService _cashDrawerService = Get.find();
  final ActivityService _activityService = Get.find();
  final NotificationService _notificationService = Get.find();
  final PoleDisplayService _poleDisplayService = Get.find();
  final IdentityService _identityService = Get.find();
  final SaleService _saleService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final PrinterService _printerService = Get.find();
  final EmployeeService _employeeService = Get.find();
  final LiquorControlService _liquorControlService = Get.find();
  final DepartmentService _departmentService = Get.find();

  Timer? _autoSignOutTimer;

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  final Rx<RegisterMenusJsonRecord> registerMenusConfig = RegisterMenusJsonRecord.empty().obs;
  final RxDouble drawerSize = 400.0.obs;
  final RxString processingResultText = "".obs;
  final RxBool processing = false.obs;
  late RxBool userMenuActive = true.obs;
  late RxBool adminMenuActive = true.obs;
  bool menuOpen = false;
  bool viewSuperMenuPermission = false;
  RxBool runningMacro = Helpers.runningMacro;

  final TextEditingController commentController = TextEditingController();

  Map<String, Map<String, a.ActionDocumentField>> actionMap = <String, Map<String, a.ActionDocumentField>>{};

  bool get canOpenCashDrawer => !_configService.merchantConfig.document.multiCashDrawer || _identityService.employeeDrawer != null;

  RegisterMenuOptions lastMenuScreen = RegisterMenuOptions.ROOT;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    await Get.putAsync(() => PaymentService().init());

    await _identityService.getTerminalInfo();
    await _printerService.getRecords();
    await _configService.getRecords();

    await getMenuSettings();

    viewSuperMenuPermission = await PermissionService.enforce(
      _identityService.currentEmployee.employee_class,
      "Access",
      "Supervisor Menu",
      _graphqlService,
    );

    Helpers.blockAutoSignout = false;

    // Initialize auto sign-out timer if enabled
    // Using a single timer time defined here to avoid the possibility unused timers sticking around
    _autoSignOutTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (Helpers.blockAutoSignout) return;
      if (_identityService.terminalAutoSignOutSeconds > 0) {
        final Duration idleTime = DateTime.now().difference(Helpers.lastActivityTime.value);

        if (idleTime.inSeconds >= _identityService.terminalAutoSignOutSeconds) {
          timer.cancel();
          await exit();
        }
      }
    });

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  void onReady() {
    _saleController = Get.find();
    _openSalesController = Get.find();
    _numpadController = Get.find();

    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  void onClose() {
    _autoSignOutTimer?.cancel();
    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Color getToolBarBackgroundColor(SaleMode mode) {
    switch (mode) {
      case SaleMode.SALE:
        return R2Colors.neutral700;
      case SaleMode.NEGATIVE_SALE:
        return R2Colors.negativeRed;
      default:
        return R2Colors.neutral700;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void expandDrawer() {
    if (drawerSize.value < 850.0) drawerSize.value = 850.0;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> shrinkDrawer() async {
    if (drawerSize.value > 400.0) drawerSize.value = 400.0;
    await Future.delayed(
      const Duration(milliseconds: 200),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getMenuSettings() async {
    if (!(_configService.merchantConfig.document.modules ?? MerchantJsonRecordModules.empty()).buttonManagement.isActive) {
      final RegisterMenusJsonRecord regConfig = RegisterMenusJsonRecord.empty();
      regConfig.document = Constants.defaultRegisterMenus;
      registerMenusConfig.value = regConfig;
      return;
    }

    final Either<ServiceError, RegisterMenusJsonRecord> result = await _configService.getMenuConfig();

    result.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (RegisterMenusJsonRecord config) {
        registerMenusConfig.value = config;
        userMenuActive.value = config.document!.user.is_active;
        adminMenuActive.value = config.document!.admin.is_active;
      },
    );

    final List<a.Action> actionList = <a.Action>[...Constants.actions];

    for (final a.Action action in actionList) {
      actionMap[action.name] = action.document ?? {};
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> openCustomDrawer({
    RegisterMenuOptions? screen,
    bool isSuperMenu = false,
    Map<String, dynamic>? fields,
  }) async {
    if (!menuOpen && scaffoldKey.currentContext != null) {
      await showGeneralDialog(
        context: scaffoldKey.currentContext!,
        barrierLabel: MaterialLocalizations.of(scaffoldKey.currentContext!).dialogLabel,
        barrierDismissible: true,
        transitionDuration: const Duration(milliseconds: 500),
        barrierColor: Colors.black.withOpacity(0.5),
        pageBuilder: (context, _, __) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              UserMenuWidget(
                option: screen,
                isSuperMenu: isSuperMenu,
                fields: fields,
              ),
            ],
          );
        },
        transitionBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOut,
            ).drive(
              Tween<Offset>(
                begin: const Offset(1.0, 0),
                end: Offset.zero,
              ),
            ),
            child: child,
          );
        },
      ).then((value) {
        menuOpen = false;
        shrinkDrawer();
      });
    } else if (screen != null) {
      final UserMenuController _userMenuController = Get.find();
      _userMenuController.currentSettingScreen.value = screen;
      _userMenuController.fields = fields;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> switchFunctions(RegisterMenusJsonRecordButton button) async {
    final Map<String, a.ActionDocumentField> actionConfig = actionMap[button.action] ?? {};
    final Map<String, dynamic> buttonConfig = button.config ?? {};
    switch (button.action) {
      case "TENDER":
        return handleTender();
      case "DISCOUNT":
        return handleDiscount();
      case "NO_SALE":
        return handleNoSale(button.config);
      case "SUSPEND_RETRIEVE":
        return handleSuspend();
      case "NEGATIVE_SALE_MODE":
        return _saleController.toggleNegativeSaleMode();
      case "CANCEL":
        return handleCancel(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "OPEN_PRICE":
        await openCustomDrawer(
          screen: RegisterMenuOptions.SALE_OPEN_PRICE_ITEM,
        );
      case "PAID_OUT":
        canOpenCashDrawer
            ? await openCustomDrawer(
                screen: RegisterMenuOptions.PAID_OUT,
              )
            : _notificationService.error("No cash drawer assigned on terminal for current employee!");
      case "PICK_UP":
        await openCustomDrawer(
          screen: RegisterMenuOptions.PICK_UP,
        );
      case "ISSUE_GIFT_CARD":
        await openCustomDrawer(
          screen: RegisterMenuOptions.GIFT_CARD_ISSUE,
        );
      case "GIFT_CARD_BALANCE":
        await openCustomDrawer(
          screen: RegisterMenuOptions.GIFT_CARD_BALANCE,
        );
      case "END_OF_DAY":
        return showEndOfDayDialog();
      case "TRANSACTION_HISTORY":
        await handleTransactionHistory(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "CUSTOMER":
        await openCustomDrawer(
          screen: RegisterMenuOptions.CUSTOMER_ATTACH,
        );
      case "PRINT_CHECK":
        await handlePrintCheck(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "SALE_TABLE_AND_DESCRIPTION":
        await handleChangeTableAndDesc(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "NEW_SEAT":
        if (checkIfSeatingEnabled()) {
          await _saleController.addSeat();
        }
      case "NEW_TOGO_SEAT":
        if (checkIfSeatingEnabled()) {
          await _saleController.addToGoSeat();
        }
      case "SPLIT_SALE":
        if (checkIfSeatingEnabled()) {
          await handleSplitChecks();
        }
      case "ANOTHER_ROUND":
        await handleAnotherRound();
      case "COMBINE_SALES":
        await handleCombineChecks();
      case "VOID_ITEM":
        return handleVoidItem(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "TIP_ADJUST":
        return handleTipAdjust();
      case "FORGIVE_TAX":
        await handleForgiveTax();
      case "QUANTITY":
        await handleQuantity();
      case "REPRINT_LAST":
        await handleReprintLast(
          buttonConfig: buttonConfig,
          actionConfig: actionConfig,
        );
      case "ASSIGN_CASHIER":
        await openCustomDrawer(
          screen: RegisterMenuOptions.ASSIGN_CASHIER,
        );
      case "SERVER_REPORT":
        await openCustomDrawer(
          screen: RegisterMenuOptions.SERVER_REPORT,
        );
      case "CASHIER_REPORT":
        await openCustomDrawer(
          screen: RegisterMenuOptions.CASHIER_REPORT,
        );
      case "PROMISED_TIME":
        await handlePromisedTime();
      case "ENTER_COMMENT":
        await handleEnterComment();
      case "ORDER_TYPE":
        await _saleController.changeOrderType();
      case "MOVE_ITEMS":
        await handleMoveItems();
      case "SALE_INFO":
        await handleSaleInfo();
      case "TRANSFER_SALES":
        await handleTransferSales();
      case "PRE_AUTH":
        await openCustomDrawer(
          screen: RegisterMenuOptions.PRE_AUTH,
        );
      case "SEND_TO_PREP_DEVICES":
        handleSendToPrepDevices();
      case "ADD_GRATUITY":
        await handleAddGratuity();
      case "TAKEOUT":
        await handleTakeout();
      case "ADD_MODIFIER":
        await _saleController.handleAddOrEditModifier(addMod: true);
      case "SPLIT_UNSPLIT_ITEMS":
        await _saleController.handleSplitUnsplitItems();
      case "SEARCH_RECIPE":
        await openCustomDrawer(screen: RegisterMenuOptions.SEARCH_RECIPE);
      case "QUICK_CASH":
        await handleQuickCash();
      case "LIQUOR_CTL":
        await _saleController.openLiqCtlMenu();
      case "TOGGLE_ONLINE_ORDERING":
        await handleToggleOnlineOrdering();
      case "HOLD_AND_FIRE":
        await handleHoldAndFire();
      case "PREP_DETAILS":
        await handlePrepDetails();
      case "BEER_BEER_CASH":
        await handleBeerBeerCash();
      case "EXIT":
        await exit();
      default:
        _notificationService.error("Button action not found!");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleBeerBeerCash() async {
    if (runningMacro.value) {
      runningMacro.value = false;
      return;
    }

    runningMacro.value = true;

    final ItemsController _itemsController = Get.find();
    final DepartmentsController _departmentsController = Get.find();

    const Duration delay = Duration(milliseconds: 200);

    final Item beerItem = Item(
      item: "00000000-0000-0000-0000-000000000001",
      long_desc: "Beer",
      upc: "",
      department: _departmentsController.currentDepartment.value.department,
      document: ItemDocument(
        pricing: const {"S0L0C0": 250},
        isOpenPrice: true,
      ),
      created_by: "283203bf-a239-4f31-b161-a6851df4a9c4",
      updated_by: "283203bf-a239-4f31-b161-a6851df4a9c4",
      departmentByDepartment: _departmentsController.currentDepartment.value,
    );

    while (runningMacro.value) {
      if (runningMacro.value) await Future.delayed(delay);
      if (runningMacro.value) await _itemsController.handleAddItem(beerItem);
      if (runningMacro.value) await Future.delayed(delay);
      if (runningMacro.value) await _itemsController.handleAddItem(beerItem);
      if (runningMacro.value) await Future.delayed(delay);
      if (runningMacro.value) {
        await Get.bottomSheet(
          const NewTenderDialog(<int>[], beerBeerCash: true),
          enableDrag: false,
          isScrollControlled: true,
          ignoreSafeArea: true,
          backgroundColor: Colors.transparent,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(40.0),
              topRight: Radius.circular(40.0),
            ),
          ),
        );
      }
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePrepDetails() async {
    await _saleController.currentSale.value.match(
      (Sale s) async {
        final ScrollController scrollCtrlr = ScrollController();
        RxBool scrollActive = false.obs;
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          await Future<void>.delayed(const Duration(milliseconds: 20));
          scrollActive.value = scrollCtrlr.hasClients && scrollCtrlr.position.maxScrollExtent > 0;
        });

        await Get.dialog(
          Dialog(
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10))),
            child: SizedBox(
              width: 500,
              height: 600,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    Header(
                      title: "Prep Details",
                      transparentBackground: false,
                      backgroundColor: R2Colors.white,
                      leftButton: DialogButton(
                        buttonType: EDialogButtonType.BACK,
                        buttonText: "Close",
                        onTapped: () => Get.back(),
                      ),
                    ),
                    Expanded(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          border: Border.all(color: R2Colors.neutral300, width: 2),
                        ),
                        child: Obx(
                          () => Padding(
                            padding: const EdgeInsets.fromLTRB(4, 2, 4, 2),
                            child: RawScrollbar(
                              thumbColor: R2Colors.neutral400,
                              radius: const Radius.circular(3),
                              thickness: 10,
                              thumbVisibility: true,
                              controller: scrollCtrlr,
                              child: SingleChildScrollView(
                                controller: scrollCtrlr,
                                child: Padding(
                                  padding: EdgeInsets.only(top: 2, right: scrollActive.value ? 14 : 2, left: 2),
                                  child: RecallItems(
                                    sale: s,
                                    isPrepBreakdown: true,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      () async => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleToggleOnlineOrdering() async {
    await _configService.getRecords();
    final bool onlineActive = _configService.ecomConfig.document.ecomEnabled;
    await Get.dialog<bool?>(
      ConfirmationDialog(
        confirmColor: R2Colors.primary500,
        declineColor: R2Colors.negativeRed,
        title: Container(),
        content: Text(
          "Turn Online Ordering ${onlineActive ? "OFF" : "ON"}?",
          style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 18),
        ),
        declineText: "Cancel",
        confirmText: "Yes",
      ),
    ).then((value) async {
      if (value == true) {
        await _configService.toggleOnlineOrdering();
      }
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleHoldAndFire() async {
    final Sale? sale = _saleController.currentSale.value.match((Sale s) => s, () => null);
    if (sale == null) return _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!");

    final List<SaleRow> selectedRows =
        sale.document.saleRows.where((SaleRow sr) => sr.selected && !sr.flags.contains(SaleRowFlags.VOIDED.index)).toList();
    if (selectedRows.isEmpty) return _notificationService.error("No valid Items selected!");

    final bool alreadyPrepped = selectedRows.any((SaleRow sr) => sr.flags.contains(SaleRowFlags.PREP_PRINTED.index));
    if (alreadyPrepped) return _notificationService.error("Can't hold items that have already been sent to prep device!");

    final bool noPrep = selectedRows.any((SaleRow sr) => sr.prep <= 0);
    if (noPrep) return _notificationService.error("Can't hold items that don't need to be printed!");

    for (final SaleRow row in selectedRows) {
      final SaleRow parent = Helpers.getParentRow(row, sale.document.saleRows);
      if (parent.flags.contains(SaleRowFlags.HOLD_AND_FIRE.index)) {
        parent.flags.removeWhere((int f) => f == SaleRowFlags.HOLD_AND_FIRE.index);
      } else {
        parent.flags.add(SaleRowFlags.HOLD_AND_FIRE.index);
      }
    }

    _saleController.currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleNoSale(Map<String, dynamic>? config) async {
    if (!canOpenCashDrawer) {
      return _notificationService.error("No cash drawer assigned on terminal for current employee!");
    }

    final Sale sale = Sale.empty();

    sale.document.saleHeader.saleFlags.add(SaleFlags.NO_SALE.index);
    sale.document.saleHeader.saleFlags.removeWhere((int x) => x == SaleFlags.SUSPENDED.index);
    sale.end_at = DateTime.now().toUtc();
    sale.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id ?? 0;
    sale.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;

    final Either<ServiceError, Sale> result = await _saleService.upsert(
      sale: sale,
      employee: _identityService.currentEmployee,
      terminalNumber: _identityService.terminalNumber,
    );

    result.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (Sale sale) async {
        await openCashDrawer();
        await _activityService.insertActivity(
          activity: Activity(
            emp_id: _identityService.currentEmployee.id,
            sale_num: sale.sale_number,
            term_num: _identityService.terminalNumber,
            activity: ActivityFlags.NO_SALE.index,
            str_data: "No sale",
          ),
        );
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> openCashDrawer({int? idx}) async {
    if (_configService.merchantConfig.document.multiCashDrawer) {
      final TerminalDrawer? drawer = idx == null ? null : _identityService.deviceDrawers.firstWhereOrNull((TerminalDrawer d) => d.idx == idx);
      await _cashDrawerService.openMulti(drawer ?? _identityService.employeeDrawer!);
    } else {
      await _cashDrawerService.openSingle();
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleQuickCash() async {
    await _saleController.currentSale.value.match(
      (Sale s) async {
        if (s.document.saleRows.isEmpty) return _notificationService.error("Current sale is empty!");
        if (Helpers.saleNeedsPrinted(s)) unawaited(_saleController.handlePrepPrint(sale: s));
        await Get.dialog(
          const NewTenderDialog(<int>[], quickCash: true),
        );
      },
      () async => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleAddGratuity() async {
    _saleController.currentSale.value.match(
      (Sale s) async {
        if (s.document.saleHeader.addedGratuity != null) {
          _numpadController.text.value = some(s.document.saleHeader.addedGratuity!.toStringAsFixed(1));
        }
        await Get.defaultDialog(
          content: AddGratuityDialog(s),
          title: "Add Gratuity",
        ).then((value) {
          _numpadController.reset();
        });
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void handleSendToPrepDevices() {
    _saleController.currentSale.value.match(
      (Sale s) async {
        _notificationService.success("Sending to Prep Devices");
        unawaited(_saleController.handlePrepPrint(sale: s));
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePromisedTime() async {
    _saleController.currentSale.value.match(
      (Sale s) async {
        await openCustomDrawer(
          screen: RegisterMenuOptions.PROMISED_TIME,
        );
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleAnotherRound() async {
    _saleController.currentSale.value.match(
      (Sale sale) async {
        final List<SaleRow> selectedRows = sale.document.saleRows
            .where(
              (SaleRow sr) => sr.selected && !sr.flags.contains(SaleRowFlags.VOIDED.index),
            )
            .toList();
        if (selectedRows.isEmpty) {
          return _notificationService.error("No editable items selected!");
        }
        if (selectedRows.firstWhereOrNull((SaleRow r) => r.splitData != null) != null) {
          return _notificationService.error("Can not increment split items!");
        }
        await _saleController.addToQty(rows: selectedRows, sale: sale, withPrepPrintedFlag: false);
        _saleController.deselectAll();
        _saleController.refreshCurrentSaleModels();
        _saleController.deselectAll();
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleSaleInfo() async {
    _saleController.currentSale.value.match(
      (Sale s) async {
        await Get.bottomSheet(
          ThinBottomSheet(
            child: SaleInfoDialog(
              sale: s,
              saleName: _saleController.saleName.value,
            ),
          ),
          isScrollControlled: true,
        );
        if (menuOpen) Get.back();
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleMoveItems() async {
    _saleController.currentSale.value.match(
      (Sale sale) async {
        final List<SaleRow> selected = sale.document.saleRows.where((SaleRow r) => r.selected).toList();
        if (selected.isEmpty) {
          return _notificationService.error("No items selected!");
        }
        if (selected.firstWhereOrNull((SaleRow r) => r.splitData != null) != null) {
          return _notificationService.error("Can not move split items!");
        }
        if (_openSalesController.buildList.where((SaleElement e) => !e.saleLock && e.sale.sale_number > 0).length < 2) {
          _notificationService.error(
            "No open ${_saleController.saleName.toLowerCase()}s to move to",
          );
          return;
        }
        await Get.defaultDialog(
          title: "Select ${_saleController.saleName} To Move To!",
          content: SelectSaleDialog(),
        ).then((value) async {
          if (value is Sale) {
            await _saleController.moveSaleRows(toSale: value, items: selected);
          }
        });
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  /// No need for a try/finally here as there's no need to update sale if value from dialog is null
  Future<void> handleEnterComment() async {
    _saleController.currentSale.value.match(
      (Sale s) async {
        final List<SaleRow> selectedItems = s.document.saleRows.where((SaleRow sr) => sr.selected).toList();

        if (selectedItems.isEmpty) {
          _notificationService.error("No Items Selected");
          return;
        }
        if (selectedItems.firstWhereOrNull((SaleRow r) => r.splitData != null) != null) {
          return _notificationService.error("Can not add comment to split items!");
        }
        String newMessage = "";

        await Get.bottomSheet(
          VirtualKeyboard(
            description: "Enter Comment",
            textEditingController: commentController,
            isBOH: false,
            onConfirm: (String value) {
              newMessage = value;
            },
          ),
          backgroundColor: Colors.transparent,
          isDismissible: false,
          enableDrag: false,
        );

        commentController.text = "";

        if (newMessage == "") return;

        final List<String> strList = [];
        for (int i = 0; i < newMessage.length; i += 12) {
          strList.add(newMessage.substring(i, i + 12 > newMessage.length ? newMessage.length : i + 12));
        }

        for (final SaleRow sr in selectedItems) {
          final List<SaleRow> childRows = Helpers.getChildRows(sr, s.document.saleRows);
          childRows.sort(
            (
              SaleRow a,
              SaleRow b,
            ) =>
                a.index.compareTo(b.index),
          );

          final int newIdx = (childRows.last.index) + 1;

          for (final SaleRow r in s.document.saleRows) {
            if (r.index >= newIdx) r.index = r.index + strList.length;
            if (r.parent >= newIdx && r.parent >= 0) r.parent = r.parent + strList.length;
          }

          for (int i = 0; i < strList.length; i++) {
            s.document.saleRows.insert(
              newIdx + i,
              SaleRow(
                item: "00000000-0000-0000-0000-000000000003",
                upc: "",
                receiptDescription: strList[i],
                department: "COMMENT",
                transactionFlags: [],
                flags: [
                  SaleRowFlags.COMMENT.index,
                  if (sr.flags.contains(SaleRowFlags.PREP_PRINTED.index)) SaleRowFlags.PREP_PRINTED.index,
                  if (sr.flags.contains(SaleRowFlags.VOIDED.index)) SaleRowFlags.VOIDED.index,
                ],
                basePrice: 0,
                originalPrice: 0,
                seatNumber: sr.seatNumber,
                parent: sr.index,
                itemPricing: {"S0L0C0": 0},
                prep: sr.prep,
                index: newIdx + i,
              ),
            );
          }

          sr.hasChildren = true;
        }
        _saleController.currentSale.refresh();
        await _saleController.upsertCurrentSale();
      },
      () => _notificationService.error("No Active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleQuantity() async {
    await Get.dialog(
      AlertDialog(
        title: const Text(
          "Next Item Quantity",
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 22),
        ),
        content: SizedBox(
          height: Get.height * 0.7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              const Expanded(
                flex: 6,
                child: NumPadWidget(
                  label: "Quantity",
                  type: NumPadType.pin,
                ),
              ),
              Expanded(
                child: DialogButton(
                  buttonType: EDialogButtonType.CONFIRM,
                  onTapped: () async {
                    if (_numpadController.text.value.match(
                          (String numpadValue) => int.parse(
                            numpadValue.replaceAll(RegExp(r'\.|\,|\$'), ''),
                          ),
                          () => 0,
                        ) <=
                        0) {
                      _saleController.globalQuantity.value = 1;
                      _numpadController.reset();
                    } else {
                      _numpadController.text.value.match(
                        (String numpadValue) {
                          _saleController.globalQuantity.value = int.parse(
                            numpadValue.replaceAll(RegExp(r'\.|\,|\$'), ''),
                          );
                        },
                        () => null,
                      );
                    }
                    Get.back();
                    _numpadController.reset();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool checkIfSeatingEnabled({bool showError = true}) {
    if (_saleController.getProperSection().trackBySeat) {
      return true;
    }
    if (showError) _notificationService.error("Seating disabled");
    return false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleTransferSales() async {
    await Get.defaultDialog(
      backgroundColor: R2Colors.white,
      title: "Transfer ${_saleController.saleName}s",
      content: TransferSaleDialog(
        openSales: [
          ..._openSalesController.openSales.where(
            (Sale s) => s.document.saleHeader.currentEmployeeNumber == _identityService.currentEmployee.id,
          ),
        ],
        saleName: _saleController.saleName.value,
        canDecline: true,
        skipPrompt: true,
      ),
    ).then((dynamic value) async {
      if (value is List<Sale>) {
        await _saleController.currentSale.value.match(
          (Sale s) async {
            final Sale? saleMatch = value.firstWhereOrNull((Sale sale) => s.sale == sale.sale);
            if (saleMatch != null) {
              s.document.saleHeader.currentEmployeeNumber = saleMatch.document.saleHeader.currentEmployeeNumber;
              if (_saleController.getProperSection().salesByEmployee) {
                await _openSalesController.deselect();
              }
            }
          },
          () async => null,
        );
      }
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleVoidItem({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    final int voidOptsDefault = actionConfig["voidOpts"]!.default_value as int;
    final int voidOpts = (buttonConfig["voidOpts"] ?? voidOptsDefault) as int;

    _saleController.currentSale.value.match(
      (Sale sale) async {
        try {
          if (_saleController.selectedSeat.value != 0) {
            await _saleController.removeSeat();
            return;
          } else {
            final List<SaleRow> voidedList = [];
            final List<SaleRow> selectedRows = sale.document.saleRows
                .where(
                  (SaleRow row) => row.selected && !row.flags.contains(SaleRowFlags.VOIDED.index),
                )
                .toList();
            final int errorCorrectAmt = selectedRows.where((SaleRow r) => !r.flags.contains(SaleRowFlags.PREP_PRINTED.index)).length;
            if (selectedRows.isEmpty) {
              return _notificationService.error("No seat or voidable items selected!");
            }
            if (selectedRows.firstWhereOrNull((SaleRow r) => r.splitData != null) != null) {
              return _notificationService.error("Can not void split items!");
            }

            // Get all departments to check if any selected items require manager approval
            final Either<ServiceError, List<Department>> departmentsResult = await _departmentService.getDepartments();
            if (departmentsResult.isLeft()) {
              return _notificationService.error("Failed to get department information!");
            }

            final List<Department> departments = departmentsResult.getOrElse((error) => []);
            final Map<String, Department> departmentMap = {for (final dept in departments) dept.title: dept};

            // Check if any selected items require manager approval
            bool needsManagerApproval = false;
            for (final SaleRow row in selectedRows) {
              final Department? dept = departmentMap[row.department];
              if (dept?.document.needsVoidPermission == true) {
                needsManagerApproval = true;
                break;
              }
            }
            // If any selected items require manager approval, show the dialog for manager pin.
            if (needsManagerApproval) {
              bool allow = false;
              await Get.defaultDialog(
                title: "Need Manager Permission",
                content: const AdminPermissionDialog(),
                barrierDismissible: true,
              ).then((value) {
                if (value == true) {
                  allow = true;
                }
              });

              if (!allow) {
                return;
              }
            }

            if (voidOpts > 1) {
              for (final SaleRow r in selectedRows) {
                final bool liquorControlled = Helpers.getChildRows(r, sale.document.saleRows)
                        .firstWhereOrNull((SaleRow sr) => sr.flags.contains(SaleRowFlags.LIQUOR_POURED.index)) !=
                    null;
                if (r.flags.contains(SaleRowFlags.PREP_PRINTED.index)) {
                  if (voidOpts == 2) return _notificationService.error("Can't error correct items after they've been sent to a prep device");
                } else {
                  if (voidOpts == 3 && !liquorControlled) {
                    return _notificationService.error("Can't void items before they've been sent to a prep device");
                  }
                }
                if (voidOpts == 2 && liquorControlled) {
                  return _notificationService.error("Can't error correct liquor items after they've been poured");
                }
              }
            }
            if (selectedRows.first.parent >= 0) {
              if (selectedRows.first.flags.contains(SaleRowFlags.COMMENT.index)) {
                final SaleRow pRow = sale.document.saleRows[selectedRows.first.parent];
                sale.document.saleRows.remove(selectedRows.first);
                for (final SaleRow r in sale.document.saleRows) {
                  if (r.index > selectedRows.first.index) r.index = r.index - 1;
                  if (r.parent > selectedRows.first.parent && r.parent >= 0) {
                    r.parent = r.parent - 1;
                  }
                }
                final List<SaleRow> cRows = Helpers.getChildRows(pRow, sale.document.saleRows);
                if (cRows.length == 1) {
                  pRow.hasChildren = false;
                }
              } else {
                await _saleController.handleAddOrEditModifier();
              }
            } else {
              Option<ServiceError> countResult = none();
              for (final SaleRow r in selectedRows) {
                final List<SaleRow> children = Helpers.getChildRows(r, sale.document.saleRows);
                bool beforePrep = !r.flags.contains(SaleRowFlags.PREP_PRINTED.index);
                for (final SaleRow child in children) {
                  if (child.flags.contains(SaleRowFlags.LIQUOR_POURED.index)) beforePrep = false;
                }
                if (r.qty > 1) {
                  await Get.dialog(
                    QuantitySelectDialog(saleRow: r, title: "Void Quantity"),
                  ).then((dynamic value) async {
                    if (value is! int) {
                      return;
                    }
                    final int voidQty = value;
                    if (r.liquorToPour.isNotEmpty) {
                      for (int i = 0; i < voidQty; i++) {
                        (await _liquorControlService.deleteLiquorToPourByPK(r.liquorToPour))
                            .fold((l) => _notificationService.error(l.message), (r) => null);
                      }
                    }
                    if (voidQty >= r.qty) {
                      if (beforePrep) {
                        _saleController.removeSaleRows(
                          [r],
                          sale.document.saleRows,
                        );
                        countResult = await _itemService.addToItemCountUsingSaleRows([r]);
                      } else {
                        r.flags.add(SaleRowFlags.VOIDED.index);
                      }
                    } else {
                      if (r.hasChildren) {
                        final List<SaleRow> children = Helpers.getChildRows(r, sale.document.saleRows);
                        for (final SaleRow child in children) {
                          child.qty -= voidQty;
                          if (!beforePrep) {
                            await _saleController.addRowCopy(
                              row: child,
                              qty: voidQty,
                              newIdx: sale.document.saleRows.length,
                              voidedCopy: true,
                            );
                            voidedList.add(sale.document.saleRows.last);
                          } else {
                            countResult = await _itemService.addToItemCountUsingItemUUIDAndQty(
                              child.item,
                              voidQty,
                            );
                          }
                        }
                      } else {
                        r.qty -= voidQty;
                        if (!beforePrep) {
                          await _saleController.addRowCopy(
                            row: r,
                            qty: voidQty,
                            newIdx: sale.document.saleRows.length,
                            voidedCopy: true,
                          );
                          voidedList.add(sale.document.saleRows.last);
                        } else {
                          countResult = await _itemService.addToItemCountUsingItemUUIDAndQty(
                            r.item,
                            voidQty,
                          );
                        }
                      }
                    }
                  });
                } else {
                  final List<SaleRow> children = Helpers.getChildRows(r, sale.document.saleRows);
                  if (r.liquorToPour.isNotEmpty) {
                    (await _liquorControlService.deleteLiquorToPourByPK(r.liquorToPour))
                        .fold((l) => _notificationService.error(l.message), (r) => null);
                  }
                  if (beforePrep) {
                    _saleController.removeSaleRows(
                      [r],
                      sale.document.saleRows,
                    );
                    countResult = await _itemService.addToItemCountUsingSaleRows(children);
                  } else {
                    final List<SaleRow> children = Helpers.getChildRows(r, sale.document.saleRows);
                    if (beforePrep) {
                      _saleController.removeSaleRows(
                        [r],
                        sale.document.saleRows,
                      );
                      countResult = await _itemService.addToItemCountUsingSaleRows(children);
                    } else {
                      for (final SaleRow child in children) {
                        child.flags.add(SaleRowFlags.VOIDED.index);
                        voidedList.add(child);
                      }
                    }
                  }
                }
                if (countResult.isSome()) {
                  _notificationService.error(
                    countResult.match(
                      (t) => t.message,
                      () => "Error updating item count!",
                    ),
                  );
                }
              }

              if (voidedList.isNotEmpty) {
                unawaited(
                  _saleController.handlePrepPrint(
                    sale: sale,
                    voidedList: voidedList.where((SaleRow r) => r.parent < 0 || r.printSeparate).toList(),
                  ),
                );
                if (errorCorrectAmt > 0) {
                  unawaited(
                    _activityService.insertActivity(
                      activity: Activity(
                        activity: ActivityFlags.ERROR_CORRECT.index,
                        emp_id: _identityService.currentEmployee.id,
                        term_num: _identityService.terminalNumber,
                        sale_num: sale.sale_number,
                        str_data: "Error Corrected $errorCorrectAmt item(s)",
                        short_data: ErrorCorrectTypes.CANCELED_ITEMS.index,
                      ),
                    ),
                  );
                }
                if (selectedRows.length - errorCorrectAmt > 0) {
                  unawaited(
                    _activityService.insertActivity(
                      activity: Activity(
                        activity: ActivityFlags.VOID_ITEM.index,
                        emp_id: _identityService.currentEmployee.id,
                        term_num: _identityService.terminalNumber,
                        sale_num: sale.sale_number,
                        str_data: "Voided ${selectedRows.length - errorCorrectAmt} item(s)",
                      ),
                    ),
                  );
                }
              }
            }
          }
        } finally {
          _saleController.refreshCurrentSaleModels();
          await _saleController.upsertCurrentSale();
        }
      },
      () => _notificationService.error("No active sale!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleSplitChecks() async {
    _saleController.currentSale.value.match(
      (Sale sale) async {
        if (((sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt) < 2) {
          _notificationService.error("At least 2 seats required.");
          return;
        }
        _saleController.deselectAll();
        await Get.defaultDialog(
          title: "Select Seats to Split",
          content: const SeatSelectDialog(SeatSelectType.SPLIT_SALE),
          backgroundColor: R2Colors.white,
        );
      },
      () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleCombineChecks() async {
    if (_openSalesController.buildList.where((SaleElement e) => !e.saleLock && e.sale.sale_number > 0).length < 2) {
      _notificationService.error("Not enough sales able to be combined!");
      return;
    }
    await Get.defaultDialog(
      title: "Combine ${_saleController.saleName}s",
      content: CombineChecksDialog(),
      backgroundColor: R2Colors.white,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleCancel({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    final int cancelOptsDefault = actionConfig["cancelOpts"]!.default_value as int;
    final int cancelOpts = (buttonConfig["cancelOpts"] ?? cancelOptsDefault) as int;
    _saleController.currentSale.value.match(
      (Sale sale) async {
        if (cancelOpts == 3 && sale.document.saleRows.firstWhereOrNull((SaleRow r) => !r.flags.contains(SaleRowFlags.COMMENT.index)) != null) {
          if (sale.document.saleRows.firstWhereOrNull((SaleRow r) => !r.flags.contains(SaleRowFlags.COMMENT.index)) != null) {
            return _notificationService.error("Can't cancel sale with items!");
          }
        }
        if (cancelOpts == 2) {
          if (sale.document.saleRows.firstWhereOrNull((SaleRow r) => r.flags.contains(SaleRowFlags.PREP_PRINTED.index)) != null) {
            return _notificationService.error("Can't cancel sale with items that have been sent to prep device!");
          }
        }
        await _saleController.cancelCurrentSale();
        _saleController.resetSale();
      },
      () => _notificationService.error("No ${_saleController.saleName.toLowerCase()} open to cancel!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  /// No need for try/finally block here as the sale is unchange if result from dialog is null
  Future<void> handleDiscount() async {
    try {
      _saleController.currentSale.value.match(
        (Sale sale) async {
          final List<SaleRow> currentRows = sale.document.saleRows;

          final SaleRow? selected = currentRows.firstWhereOrNull(
            (SaleRow sr) => sr.selected && !sr.flags.contains(SaleRowFlags.VOIDED.index) && sr.splitData == null,
          );

          if (currentRows.isEmpty || selected == null) {
            return _notificationService.error("No discountable items selected!");
          }

          if (selected.parent > 0) {
            await _saleController.selectSaleRow(Helpers.getParentRow(selected, currentRows));
          }
          final String saleRowsJson = jsonEncode(currentRows);

          final List<SaleRow>? modifiedSaleRows = await Get.bottomSheet(
            ThinBottomSheet(
              child: ModifySaleDialog(),
            ),
            isScrollControlled: true,
            ignoreSafeArea: true,
            isDismissible: false,
            backgroundColor: Colors.transparent,
            settings: RouteSettings(
              name: "EDIT SALE BOTTOM SHEET",
              arguments: {
                "saleRows": saleRowsJson,
              },
            ),
          );

          if (modifiedSaleRows == null) return;

          sale.document.saleRows = modifiedSaleRows;

          final bool dp = _configService.merchantConfig.document.dualPricing;

          _poleDisplayService.write(
            line1: dp ? "Cash Total:" : "",
            line1Suffix: dp ? "\$${Helpers.formatCurrency(sale.document.saleHeader.cashTotal)}" : null,
            line2Prefix: dp ? "Card Total:" : "Total:",
            line2Suffix: "\$${Helpers.formatCurrency(sale.document.saleHeader.total)}",
          );

          _saleController.refreshCurrentSaleModels();
          await _saleController.upsertCurrentSale();
        },
        () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error handling discount.",
        err,
        stack,
      );
      _notificationService.error("Error handling discount");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void handleTender() {
    try {
      if (!canOpenCashDrawer) {
        return _notificationService.error("No cash drawer assigned on terminal for current employee!");
      }
      _saleController.currentSale.value.match(
        (Sale sale) async {
          final bool isCartEmpty = sale.document.saleRows.isEmpty;

          if (isCartEmpty) return _notificationService.error("Cart is Empty!");

          if (Helpers.saleNeedsPrinted(sale)) unawaited(_saleController.handlePrepPrint(sale: sale));

          List<int>? seats;

          final int remainingSeats =
              ((sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt) - sale.document.saleHeader.seatsSettled.length;

          bool multipleSeats =
              remainingSeats > 1 && remainingSeats > sale.document.saleHeader.seatsTendered.length && sale.document.saleRows.length > 1;

          if (multipleSeats) {
            multipleSeats = sale.document.saleRows.firstWhereOrNull(
                  (SaleRow r) => r.seatNumber != sale.document.saleRows.first.seatNumber && !r.flags.contains(SaleRowFlags.VOIDED.index),
                ) !=
                null;
          }

          if (multipleSeats) {
            if (sale.document.saleHeader.tenders.isNotEmpty) {
              seats = sale.document.saleHeader.seatsTendered;
            } else {
              await Get.defaultDialog(
                title: "Select Seats to Settle",
                content: const SeatSelectDialog(SeatSelectType.TENDER),
                backgroundColor: R2Colors.white,
              ).then((value) {
                if (value is List<int>) {
                  if (sale.document.saleRows.firstWhereOrNull(
                        (SaleRow sr) => !value.contains(sr.seatNumber),
                      ) ==
                      null) {
                    seats = <int>[];
                  } else {
                    seats = value;
                  }
                }
              });
            }
          } else {
            seats = <int>[];
          }

          if (seats == null) return;

          await noSignOffBottomSheet(
            NewTenderDialog(seats!),
            enableDrag: false,
            isScrollControlled: true,
            ignoreSafeArea: true,
            backgroundColor: Colors.transparent,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.0),
                topRight: Radius.circular(40.0),
              ),
            ),
          );
        },
        () => _notificationService.error("No active ${_saleController.saleName.toLowerCase()}!"),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error handling tender.",
        err,
        stack,
      );
      _notificationService.error("Error handling tender");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleTransactionHistory({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    final bool reprntDefault = actionConfig["reprnt"]!.default_value as bool;
    final bool refundDefault = actionConfig["refund"]!.default_value as bool;
    final bool reOpenDefault = actionConfig["reOpen"]!.default_value as bool;
    final bool voidDefault = actionConfig["void"]!.default_value as bool;
    final bool copyDefault = actionConfig["copy"]!.default_value as bool;
    final int copyOptsDefault = actionConfig["copyOpts"]!.default_value as int;
    final bool tipAdjustDefault = actionConfig["tipAdjust"]!.default_value as bool;

    final bool reprnt = (buttonConfig["reprnt"] ?? reprntDefault) as bool;
    final bool refund = (buttonConfig["refund"] ?? refundDefault) as bool;
    final bool reOpen = (buttonConfig["reOpen"] ?? reOpenDefault) as bool;
    final bool voidd = (buttonConfig["void"] ?? voidDefault) as bool;
    final bool copy = (buttonConfig["copy"] ?? copyDefault) as bool;
    final int copyOpts = (buttonConfig["copyOpts"] ?? copyOptsDefault) as int;
    final bool tipAdjust = (buttonConfig["tipAdjust"] ?? tipAdjustDefault) as bool;

    await openCustomDrawer(
      screen: RegisterMenuOptions.TRANSACTION_HISTORY,
      fields: {
        "reprnt": reprnt,
        "refund": refund,
        "reOpen": reOpen,
        "void": voidd,
        "copy": copy,
        "copyOpts": copyOpts,
        "tipAdjust": tipAdjust,
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePrintCheck({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    try {
      final int prtOptsDefault = actionConfig["prtOpts"]!.default_value as int;
      final int prtOpts = (buttonConfig["prtOpts"] ?? prtOptsDefault) as int;

      final bool printAllSeatsSeperate = prtOpts == 3;
      final bool printSelectedSeats = prtOpts == 5;
      final bool showSubtotals = prtOpts == 2;
      final bool showSeats = prtOpts != 4 && _saleController.getProperSection().trackBySeat;
      List<int> selectedSeats = [];

      _saleController.currentSale.value.match(
        (Sale sale) async {
          final bool isCartEmpty = sale.document.saleRows.isEmpty;
          if (isCartEmpty) {
            _notificationService.error("${_saleController.saleName.value} is empty!");
            return;
          }

          final Map<int, List<int>> subtotals = <int, List<int>>{};

          if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.RECEIPT_PRINTED.index)) {
            sale.document.saleHeader.saleFlags.add(SaleFlags.RECEIPT_PRINTED.index);
          }

          if (printSelectedSeats) {
            await Get.defaultDialog(
              title: "Select Seats to Print",
              content: const SeatSelectDialog(SeatSelectType.PRINT),
            ).then((value) {
              if (value is List<int>) {
                selectedSeats = value;
              }
            });
            if (selectedSeats.isEmpty) return;
          }

          if (printAllSeatsSeperate || showSubtotals) {
            final int endingIdx = (sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt;

            for (int i = 0; i < endingIdx; i++) {
              final int index = i >= (sale.document.saleHeader.seatCnt ?? 1) ? (sale.document.saleHeader.seatCnt ?? 1) - (i + 1) : i + 1;
              final Sale seatSale = Sale(
                sale: sale.sale,
                sale_number: sale.sale_number,
                document: SaleDocument(
                  saleHeader: Helpers.makeDeepSaleHeaderCopy(sale),
                  saleRows: sale.document.saleRows
                      .where(
                        (SaleRow row) => (row.seatNumber == index) || i == 0 && row.seatNumber == 0,
                      )
                      .toList(),
                ),
                created_by: sale.created_by,
                updated_by: sale.updated_by,
              );

              _saleController.refreshSaleViewModels(seatSale);

              if (showSubtotals) {
                subtotals[index] = [seatSale.document.saleHeader.subTotal, seatSale.document.saleHeader.cashSubTotal];
              } else if (seatSale.document.saleRows.isNotEmpty) {
                unawaited(
                  _printerService.printSale(
                    sale: seatSale,
                    saleName: _saleController.saleName.value,
                  ),
                );
              }
            }
          }

          if (!printAllSeatsSeperate) {
            Sale saleRef = sale;

            if (printSelectedSeats) {
              saleRef = Sale.empty();
              saleRef.document.saleHeader = Helpers.makeDeepSaleHeaderCopy(sale);
              saleRef.sale_number = sale.sale_number;
              saleRef.created_at = sale.created_at;
              saleRef.updated_at = sale.updated_at;

              saleRef.document.saleRows = sale.document.saleRows
                  .where(
                    (SaleRow row) => selectedSeats.contains(row.seatNumber) || row.seatNumber == 0 && selectedSeats.contains(1),
                  )
                  .toList();

              _saleController.refreshSaleViewModels(saleRef);
            }

            final bool allSeatsSelected = selectedSeats.length == (sale.document.saleHeader.seatCnt ?? 1);

            unawaited(
              _printerService.printSale(
                sale: saleRef,
                subtotals: subtotals,
                showSeats: showSeats,
                saleName: _saleController.saleName.value,
                qrAllowed: !printAllSeatsSeperate || allSeatsSelected,
              ),
            );
          }

          final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
            sale: sale,
            employee: _identityService.currentEmployee,
            terminalNumber: _identityService.terminalNumber,
          );

          upsertRes.match(
            (ServiceError l) => _notificationService.error(l.message),
            (Sale r) {
              if (Helpers.saleNeedsPrinted(sale)) {
                _saleController.handlePrepPrint(sale: sale);
              }
            },
          );
        },
        () => _notificationService.error("No active ${_saleController.saleName}!"),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error printing check.",
        err,
        stack,
      );
      _notificationService.error("Error printing check");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleReprintLast({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    final int copyOptsDefault = actionConfig["copyOpts"]!.default_value as int;
    final bool prntAuthDefault = actionConfig["prntAuth"]!.default_value as bool;

    final int copyOpts = (buttonConfig["copyOpts"] ?? copyOptsDefault) as int;
    final bool prntAuth = (buttonConfig["prntAuth"] ?? prntAuthDefault) as bool;

    final Either<ServiceError, Sale> lastSaleRes = await _saleService.getLastSettledSale(_identityService.currentEmployee.id ?? 0);

    final Sale? lastSale = lastSaleRes.match(
      (l) {
        _notificationService.error(l.message);
        return null;
      },
      (r) => r,
    );

    if (lastSale == null) return;

    final List<Future<void>> printResults = <Future<void>>[];
    if (copyOpts != 3) {
      printResults.add(
        _printerService.printSale(
          sale: lastSale,
          authSlip: prntAuth,
          customerCopy: false,
          showSeats: _saleController.getProperSection().trackBySeat,
          saleName: _saleController.saleName.value,
        ),
      );
    }
    if (copyOpts != 2) {
      printResults.add(
        _printerService.printSale(
          sale: lastSale,
          showSeats: _saleController.getProperSection().trackBySeat,
          saleName: _saleController.saleName.value,
        ),
      );
    }
    await Future.wait(printResults);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleRetrieval() async {
    await Get.bottomSheet(
      const ThinBottomSheet(
        child: RetrieveDialog(),
      ),
      isScrollControlled: true,
      ignoreSafeArea: true,
      backgroundColor: Colors.transparent,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleSuspend() async {
    await _saleController.currentSale.value.match(
      (Sale sale) async => Get.dialog(
        const SuspendDialog(),
      ),
      () async => handleRetrieval(),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleChangeTableAndDesc({
    required Map<String, a.ActionDocumentField> actionConfig,
    required Map<String, dynamic> buttonConfig,
  }) async {
    try {
      final int showFieldsDefault = actionConfig["showFields"]!.default_value as int;

      final int showFields = (buttonConfig["showFields"] ?? showFieldsDefault) as int;

      final Sale? currSale = _saleController.currentSale.value.match((Sale s) => s, () => null);

      if (checkIfSeatingEnabled(showError: false) && showFields != 3) {
        await Get.bottomSheet(
          TablesDialog(
            tablesOnly: showFields == 2 || showFields == 4,
            viewTables: showFields == 4,
          ),
          isScrollControlled: true,
          ignoreSafeArea: false,
          backgroundColor: Colors.transparent,
        ).then((value) async {
          if (showFields == 4) return;
          if (currSale == null) {
            if (value is FlowElement) {
              _saleController.newSaleTable.value = value;
            } else {
              if (_saleController.requireTable.value && _saleController.newSaleDescController.text != "") {
                _saleController.newSaleDescController.text = "";
                return _notificationService.error("Table required for new ${_saleController.saleName.toLowerCase()}");
              }
              if (_saleController.newSaleDescController.text == "") return;
            }
            await _saleController.startEmptySale();
          }
        });
        if (_saleController.currentSale.value.match((Sale s) => false, () => true)) return;
      } else if (showFields != 2 && showFields != 4) {
        _saleController.newSaleDescController.text = currSale?.document.saleHeader.saleDescription ?? "";
        await Get.bottomSheet(
          VirtualKeyboard(
            description: "Change Description",
            textEditingController: _saleController.newSaleDescController,
            isBOH: false,
            onConfirm: (String value) async {
              if (value != "") {
                if (currSale != null) {
                  _saleController.handleChangeSaleDesc(value);
                  _saleController.newSaleDescController.text = "";
                } else {
                  await _saleController.startEmptySale();
                }
              }
            },
          ),
          backgroundColor: Colors.transparent,
          isDismissible: false,
          enableDrag: false,
        );
      } else {
        _notificationService.error("Seating disabled");
      }
    } catch (err, stack) {
      _logger.severe(
        "Error handling change of table and/or description.",
        err,
        stack,
      );
      _notificationService.error("Error handling change of table and/or description");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> showEndOfDayDialog() async {
    //if (processing.value) return _notificationService.error("Terminal busy");

    final bool? result = await Get.dialog<bool?>(
      ConfirmationDialog(
        confirmColor: R2Colors.primary500,
        declineColor: R2Colors.negativeRed,
        title: Container(),
        content: const Text(
          "Perform End of Day?",
          style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18),
        ),
        declineText: "Cancel",
        confirmText: "Yes",
      ),
    );
    if (result == true) {
      unawaited(
        noSignOffDialog(
          ConfirmationDialog(
            confirmColor: R2Colors.primary500,
            declineColor: R2Colors.negativeRed,
            title: const Text(
              "Performing End of Day",
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18),
            ),
            content: const Padding(
              padding: EdgeInsets.only(top: 20.0, bottom: 20.0),
              child: CupertinoActivityIndicator(radius: 40),
            ),
          ),
          barrierDismissible: false,
        ),
      );
      await closeBatch();
      Get.back();
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> closeBatch() async {
    processing.value = true;

    final Option<ErrorResponse> settleBatchResponse = await Get.find<PaymentService>().settleBatch();
    settleBatchResponse.match((ErrorResponse error) {
      processing.value = false;
      processingResultText.value = error.message;
      _notificationService.error(processingResultText.value);
    }, () {
      processingResultText.value = "End of day finished successfully!";
      _notificationService.success(processingResultText.value);
      processing.value = false;
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> exit() async {
    await _saleController.currentSale.value.match(
      (Sale sale) async {
        _saleController.deselectAll();
        if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index)) {
          sale.document.saleHeader.saleFlags.add(SaleFlags.SUSPENDED.index);
        }
        sale.document.saleHeader.currentTerminalNumber = 0;
        final Either<ServiceError, Sale> result = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );
        result.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (Sale sale) => null,
        );
        if (_configService.merchantConfig.document.prepOnSignOut) {
          if (Helpers.saleNeedsPrinted(sale)) {
            unawaited(
              noSignOffDialog(
                ColoredBox(
                  color: Colors.black.withOpacity(0.5),
                  child: Lottie.asset(
                    "packages/desktop/assets/lottie/loading-animation.json",
                    frameRate: FrameRate.max,
                    height: 150,
                  ),
                ),
              ),
            );
            await _saleController.handlePrepPrint(sale: sale).then((value) {
              Get.back();
            });
          }
        }
      },
      () => null,
    );

    _employeeService.lastSignOff[_identityService.currentEmployee.id ?? 0] = DateTime.now();

    unawaited(Get.offAllNamed(AppRoutes.SIGN_ON));

    _poleDisplayService.banner();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleTipAdjust() async {
    try {
      await noSignOffBottomSheet(
        const ThinBottomSheet(
          sideFlex: 1,
          child: TipAdjustDialog(),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
        isDismissible: false,
        enableDrag: false,
        backgroundColor: Colors.transparent,
        settings: const RouteSettings(
          name: "TIP ADJUST BOTTOM SHEET",
          // arguments: {
          //   "saleRows": saleRowsJson,
          // },
        ),
      );

      _poleDisplayService.write(
        line1: "",
        line2Prefix: "Tip Adjusted",
        line2Suffix: "",
      );
      // ignore: empty_catches
    } catch (err, stack) {
      _logger.severe(
        "Error opening tip adjust.",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleTakeout() async {
    await _saleController.currentSale.value.match(
      (Sale sale) async {
        if (sale.document.saleHeader.orderType == OrderType.TAKEOUT.index) {
          return _notificationService.error("${_saleController.saleName} is already takeout!");
        }
        if ((sale.document.saleHeader.customer ?? "") == "" && _saleController.requireCustIfTogo.value) {
          await openCustomDrawer(
            screen: RegisterMenuOptions.CUSTOMER_ATTACH,
          );
          if (sale.document.saleHeader.customer == null) {
            _notificationService.error("Customer Required for takeout ${_saleController.saleName}!");
            return;
          }
        }
        sale.document.saleHeader.orderType = OrderType.TAKEOUT.index;

        final Either<ServiceError, Sale> upsert = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );
        upsert.match(
          (ServiceError e) {
            _notificationService.error(e.message);
          },
          (Sale s) {
            _notificationService.success(
              "Order type changed to Takekout!",
            );

            _saleController.refreshCurrentSaleModels();
            if (menuOpen) Get.back();
          },
        );
      },
      () async {
        if (menuOpen) Get.back();
        _saleController.newOrdType.value = OrderType.TAKEOUT.index;
        await _saleController.startEmptySale();
        _saleController.currentSale.refresh();
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleForgiveTax() async {
    try {
      final String saleTaxesJson = jsonEncode(_configService.salesTaxList);

      final int? newMask = await Get.bottomSheet(
        const ThinBottomSheet(
          child: ForgiveTaxDialog(),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
        isDismissible: false,
        backgroundColor: Colors.transparent,
        settings: RouteSettings(
          name: "FORGIVE TAX BOTTOM SHEET",
          arguments: {
            "saleTaxes": saleTaxesJson,
          },
        ),
      );

      _saleController.currentSale.value.match(
        (Sale sale) {
          if (newMask != null) {
            _saleController.modifyTaxForgiveness(sale, newMask);
          }
        },
        () => _notificationService.error("No current ${_saleController.saleName.toLowerCase()}!"),
      );

      _saleController.refreshCurrentSaleModels();
      // ignore: empty_catches
    } catch (err, stack) {
      _logger.severe(
        "Error opening forgive tax menu.",
        err,
        stack,
      );
    }
  }

  Future<T?> noSignOffDialog<T>(
    Widget child, {
    bool barrierDismissible = true,
    Color? barrierColor,
    bool useSafeArea = true,
    GlobalKey<NavigatorState>? navigatorKey,
    Object? arguments,
    Duration? transitionDuration,
    Curve? transitionCurve,
    String? name,
    RouteSettings? routeSettings,
  }) async {
    Helpers.blockAutoSignout = true;
    final T? result = await Get.dialog<T>(
      child,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      useSafeArea: useSafeArea,
      navigatorKey: navigatorKey,
      arguments: arguments,
      transitionDuration: transitionDuration,
      transitionCurve: transitionCurve,
      name: name,
      routeSettings: routeSettings,
    );
    Helpers.blockAutoSignout = false;
    Helpers.lastActivityTime.value = DateTime.now();
    return result;
  }

  Future<T?> noSignOffBottomSheet<T>(
    Widget child, {
    Color? backgroundColor,
    double? elevation,
    bool persistent = true,
    ShapeBorder? shape,
    Clip? clipBehavior,
    Color? barrierColor,
    bool? ignoreSafeArea,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    RouteSettings? settings,
    Duration? enterBottomSheetDuration,
    Duration? exitBottomSheetDuration,
  }) async {
    Helpers.blockAutoSignout = true;
    final T? result = await Get.bottomSheet<T>(
      child,
      backgroundColor: backgroundColor,
      elevation: elevation,
      persistent: persistent,
      shape: shape,
      clipBehavior: clipBehavior,
      barrierColor: barrierColor,
      ignoreSafeArea: ignoreSafeArea,
      isScrollControlled: isScrollControlled,
      useRootNavigator: useRootNavigator,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      settings: settings,
      enterBottomSheetDuration: enterBottomSheetDuration,
      exitBottomSheetDuration: exitBottomSheetDuration,
    );
    Helpers.blockAutoSignout = false;
    Helpers.lastActivityTime.value = DateTime.now();
    return result;
  }
}
