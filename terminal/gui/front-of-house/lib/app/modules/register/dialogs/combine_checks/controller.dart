import 'dart:async';

import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/sales.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';

class CombineChecksController extends GetxController {
  final NotificationService _notificationService = Get.find();

  final OpenSalesController _openSalesController = Get.find();
  final SaleController _saleController = Get.find();

  final RxList<SaleElement> openChecks = <SaleElement>[].obs;
  final RxList<String> combineFrom = <String>[].obs;
  final RxString combineInto = "".obs;

  @override
  Future<void> onInit() async {
    openChecks.value = <SaleElement>[..._openSalesController.buildList];

    super.onInit();
  }

  Future<void> combineChecks() async {
    try {
      if (combineFrom.isEmpty) return _notificationService.error("No checks selected to combine");
      if (combineInto.value.isEmpty) return _notificationService.error("No check selected to combine into");
      final Sale? currentSale = _saleController.currentSale.value.match((Sale s) => s, () => null);
      final Either<ServiceError, Sale> combineRes = await _saleController.combineSales(
        combineFrom: combineFrom,
        combineInto: combineInto.value,
      );
      final Sale finishedSale = combineRes.fold((ServiceError l) => throw l.message, (Sale r) => r);
      if (combineFrom.contains(currentSale?.sale)) {
        // If the current sale is one of the combined and cancelled sales, set it to one being combined into
        await _openSalesController.changeSelection(finishedSale);
      } else if (currentSale?.sale == combineInto.value) {
        // If the current sale is the one being combined into, update it
        _saleController.currentSale.value = Some<Sale>(finishedSale);
      }
      _notificationService.success("Checks combined successfully");
      Get.back();
    } catch (e) {
      _notificationService.error(e.toString());
    }
  }
}
