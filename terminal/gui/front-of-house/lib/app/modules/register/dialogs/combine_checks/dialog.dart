import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/combine_checks/controller.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/sales.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CombineChecksDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<CombineChecksController>(
      init: CombineChecksController(),
      builder: (CombineChecksController controller) {
        return SizedBox(
          width: 500,
          height: 550,
          child: Scaffold(
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: <Widget>[
                        Expanded(
                          child: Column(
                            children: <Widget>[
                              Text(
                                "Combine Into",
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: R2Colors.neutral700,
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: R2Colors.neutral200,
                                    border: Border.all(width: 2, color: R2Colors.neutral300),
                                  ),
                                  child: Obx(
                                    () => SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.all(3),
                                        child: Column(
                                          children: controller.openChecks.map(
                                            (SaleElement el) {
                                              final Sale sale = el.sale;
                                              final bool selected = sale.sale == controller.combineInto.value;
                                              return sale.sale_number == 0
                                                  ? const SizedBox.shrink()
                                                  : Padding(
                                                      padding: const EdgeInsets.all(3),
                                                      child: RadiusListener(
                                                        onTap: () {
                                                          if (selected) {
                                                            controller.combineInto.value = "";
                                                            return;
                                                          }
                                                          controller.combineInto.value = sale.sale;
                                                          controller.combineFrom.removeWhere((String id) => id == sale.sale);
                                                        },
                                                        child: ColoredBox(
                                                          color: selected ? R2Colors.primary200 : R2Colors.white,
                                                          child: Padding(
                                                            padding: const EdgeInsets.symmetric(
                                                              vertical: 16,
                                                            ),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: <Widget>[
                                                                Text(
                                                                  el.title,
                                                                  style: TextStyle(
                                                                    fontWeight: FontWeight.bold,
                                                                    color: selected ? R2Colors.primary500 : null,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                            },
                                          ).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            children: <Widget>[
                              Text(
                                "From",
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: R2Colors.neutral700,
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: R2Colors.neutral200,
                                    border: Border.all(width: 2, color: R2Colors.neutral300),
                                  ),
                                  child: Obx(
                                    () => SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.all(3),
                                        child: Column(
                                          children: controller.openChecks.map(
                                            (SaleElement el) {
                                              final Sale sale = el.sale;
                                              final bool selected = controller.combineFrom.contains(sale.sale);
                                              final bool locked = controller.combineInto.value == sale.sale;
                                              return sale.sale_number == 0
                                                  ? const SizedBox.shrink()
                                                  : Padding(
                                                      padding: const EdgeInsets.all(3),
                                                      child: RadiusListener(
                                                        onTap: () {
                                                          if (locked) return;
                                                          if (selected) {
                                                            controller.combineFrom.removeWhere((String id) => id == sale.sale);
                                                            return;
                                                          }
                                                          controller.combineFrom.add(sale.sale);
                                                        },
                                                        child: ColoredBox(
                                                          color: selected ? R2Colors.primary200 : R2Colors.white,
                                                          child: Padding(
                                                            padding: const EdgeInsets.symmetric(
                                                              vertical: 16,
                                                            ),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: <Widget>[
                                                                Text(
                                                                  el.title,
                                                                  style: TextStyle(
                                                                    fontWeight: FontWeight.bold,
                                                                    color: selected
                                                                        ? R2Colors.primary500
                                                                        : locked
                                                                            ? R2Colors.neutral300
                                                                            : null,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                            },
                                          ).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: <Widget>[
                        DialogButton(
                          buttonType: EDialogButtonType.CANCEL,
                          onTapped: Get.back,
                        ),
                        Obx(
                          () => DialogButton(
                            disabled: controller.combineFrom.isEmpty || controller.combineInto.isEmpty,
                            buttonType: EDialogButtonType.CONFIRM,
                            onTapped: controller.combineChecks,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
