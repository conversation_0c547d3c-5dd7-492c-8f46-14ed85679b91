import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/liquor_to_pour.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/global_widgets/header.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/liquor_ctl/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LiquorCtlDialog extends GetView<LiquorCtlController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<LiquorCtlController>(
      init: LiquorCtlController(),
      builder: (LiquorCtlController controller) {
        return SizedBox(
          width: Get.width - 20,
          height: Get.height - 20,
          child: Scaffold(
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Header(
                  title: "Liquor Control",
                  leftButton: DialogButton(
                    buttonType: EDialogButtonType.BACK,
                    buttonText: "Close",
                    onTapped: Get.back,
                  ),
                  rightButton: Padding(
                    padding: const EdgeInsets.only(right: 25),
                    child: TextButton(
                      onPressed: () {
                        controller.selectedPourItem.value = "";
                      },
                      child: Text(
                        "Clear Selection",
                        style: TextStyle(
                          color: R2Colors.neutral500,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  backgroundColor: R2Colors.white,
                  transparentBackground: false,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        flex: 2,
                        child: Text(
                          "Sale",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          "Item Name",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Text(
                          "Liquor to Pour",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          "Elapsed Time",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Obx(() {
                      return controller.liquorToPour.isEmpty
                          ? const Center(
                              child: Text("No liquor to pour found"),
                            )
                          : Container(
                              decoration: BoxDecoration(
                                color: R2Colors.neutral200,
                                border: Border.all(width: 2, color: R2Colors.neutral300),
                              ),
                              child: SingleChildScrollView(
                                child: Column(
                                  children: controller.liquorToPour.keys.map((int saleNum) {
                                    final List<String> splitSelect = controller.selectedPourItem.value.split(" ");
                                    final Map<String, List<LiquorToPour>> saleMap = controller.liquorToPour[saleNum]!;
                                    final Sale sale = saleMap[saleMap.keys.first]!.first.saleBySale!;
                                    final String title = Helpers.getSaleTitle(sale, controller.saleName.value);
                                    final bool saleIsSelected = splitSelect.length == 1 && splitSelect[0] == saleNum.toString();
                                    return Column(
                                      children: <Widget>[
                                        Column(
                                          children: saleMap.keys.map((String itemId) {
                                            final List<LiquorToPour> ltpList = saleMap[itemId]!;
                                            final Item item = ltpList.first.itemByItem!;
                                            final List<int> numList = ltpList.map((LiquorToPour l) => l.item_num).toSet().toList();

                                            return Column(
                                              children: numList.map((int i) {
                                                bool itemIsSelected = false;
                                                final List<LiquorToPour> currentLtps = ltpList.where((LiquorToPour l) => l.item_num == i).toList();
                                                final DateTime createdAt = currentLtps.first.created_at.toLocal();
                                                final Duration difference = DateTime.now().difference(createdAt);

                                                String twoDigits(int n) => n.toString().padLeft(2, '0');
                                                final String minutes = twoDigits(difference.inMinutes);
                                                final String seconds = twoDigits(difference.inSeconds.remainder(60));
                                                final String timeSittingString = "$minutes:$seconds";

                                                final StringBuffer ltpBuffer = StringBuffer();
                                                for (int i = 0; i < currentLtps.length; i++) {
                                                  ltpBuffer.write(controller.ltpMap[currentLtps[i].liq_ctl_plu]?.document.receiptDesc ?? "");
                                                  if (i < currentLtps.length - 1) ltpBuffer.write("  |  ");
                                                }

                                                if (splitSelect.length > 2) {
                                                  itemIsSelected = splitSelect[0] == saleNum.toString() &&
                                                      splitSelect[1] == itemId &&
                                                      i.toString() == splitSelect[2];
                                                }
                                                String onTapSale() => controller.selectedPourItem.value = saleIsSelected ? "" : "$saleNum";
                                                String onTapItem() => controller.selectedPourItem.value = itemIsSelected ? "" : "$saleNum $itemId $i";

                                                final bool selected = itemIsSelected || saleIsSelected;
                                                final Color selectedColor = R2Colors.primary200;
                                                return IntrinsicHeight(
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.stretch,
                                                    children: <Widget>[
                                                      Expanded(
                                                        flex: 2,
                                                        child: RadiusListener(
                                                          onTap: onTapSale,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: selected ? selectedColor : R2Colors.white,
                                                              border: Border(
                                                                top: BorderSide(color: R2Colors.neutral400),
                                                                bottom: BorderSide(color: R2Colors.neutral400),
                                                                right: BorderSide(color: R2Colors.neutral400),
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: <Widget>[
                                                                  Text(
                                                                    title,
                                                                    style: TextStyle(
                                                                      fontWeight: FontWeight.bold,
                                                                      fontSize: 18,
                                                                      color: selected ? R2Colors.primary500 : null,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 3,
                                                        child: RadiusListener(
                                                          onTap: onTapItem,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: selected ? selectedColor : R2Colors.neutral100,
                                                              border: Border(
                                                                top: BorderSide(color: R2Colors.neutral400),
                                                                bottom: BorderSide(color: R2Colors.neutral400),
                                                                right: BorderSide(color: R2Colors.neutral400),
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: <Widget>[
                                                                  Text(
                                                                    item.document.receiptDesc ?? "",
                                                                    style: TextStyle(
                                                                      fontSize: 18,
                                                                      color: selected ? R2Colors.primary500 : null,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 6,
                                                        child: RadiusListener(
                                                          onTap: onTapItem,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: selected ? selectedColor : R2Colors.white,
                                                              border: Border(
                                                                top: BorderSide(color: R2Colors.neutral400),
                                                                bottom: BorderSide(color: R2Colors.neutral400),
                                                                right: BorderSide(color: R2Colors.neutral400),
                                                              ),
                                                            ),
                                                            child: Column(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: <Widget>[
                                                                Padding(
                                                                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                                                                  child: Text(
                                                                    ltpBuffer.toString(),
                                                                    style: TextStyle(
                                                                      fontSize: 17,
                                                                      color: selected ? R2Colors.primary500 : null,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 2,
                                                        child: RadiusListener(
                                                          onTap: onTapItem,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: selected ? selectedColor : R2Colors.neutral200,
                                                              border: Border(
                                                                top: BorderSide(color: R2Colors.neutral400),
                                                                bottom: BorderSide(color: R2Colors.neutral400),
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                crossAxisAlignment: CrossAxisAlignment.end,
                                                                children: <Widget>[
                                                                  Text(
                                                                    timeSittingString,
                                                                    style: TextStyle(
                                                                      fontSize: 17,
                                                                      color: selected ? R2Colors.primary500 : null,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              }).toList(),
                                            );
                                          }).toList(),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),
                              ),
                            );
                    }),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
