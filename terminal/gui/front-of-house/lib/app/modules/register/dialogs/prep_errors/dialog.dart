import 'dart:async';

import 'package:collection/collection.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PrepErrorsDialog extends GetView<SaleController> {
  const PrepErrorsDialog({
    required this.errMap,
    required this.sales,
    required this.sale,
    required this.showRetryBttn,
  });
  final Map<int, List<String>> errMap;
  final Map<int, Sale> sales;
  final Sale sale;
  final bool showRetryBttn;

  @override
  Widget build(BuildContext context) {
    final PrinterService printerService = Get.find();
    final SaleService _saleService = Get.find();
    final Iterable<int> keyList = errMap.keys;
    final List<PrepDevice> prepList = <PrepDevice>[...printerService.prepList];
    return SizedBox(
      width: 500,
      height: 550,
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            children: <Widget>[
              Expanded(
                // ignore: use_decorated_box
                child: Container(
                  decoration: BoxDecoration(
                    color: R2Colors.neutral200,
                    border: Border.all(width: 2, color: R2Colors.neutral300),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      children: keyList.map(
                        (int key) {
                          final Iterable<SaleRow> filteredRows = (sales[key]?.document.saleRows ?? <SaleRow>[]).where(
                            (SaleRow sr) => sr.parent < 0,
                          );

                          final PrepDevice? pDev = prepList.firstWhereOrNull(
                            (PrepDevice pd) => pd.idx == key,
                          );
                          return Column(
                            children: <Widget>[
                              if (pDev != null)
                                Text(
                                  pDev.desc,
                                  style: const TextStyle(
                                    fontSize: 22,
                                  ),
                                ),
                              Text(
                                "${filteredRows.length} Items:",
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                ),
                                child: Column(
                                  children: filteredRows
                                      .sorted(
                                        (SaleRow a, SaleRow b) => a.seatNumber.compareTo(b.seatNumber),
                                      )
                                      .map(
                                        (SaleRow sr) => Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: <Widget>[
                                            Text(
                                              ((sales[key]!.document.saleHeader.seatCnt ?? 1) > 1
                                                      ? "Seat ${sr.seatNumber} - ${sr.receiptDescription}"
                                                      : sr.receiptDescription) +
                                                  (sr.qty > 1 ? " (${sr.qty})" : ""),
                                              style: const TextStyle(
                                                fontSize: 16,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                      .toList(),
                                ),
                              ),
                              ...errMap[key]!.map(
                                (String err) => Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                  ),
                                  child: Row(
                                    children: <Widget>[
                                      const Text(
                                        "• ",
                                        style: TextStyle(fontSize: 22),
                                      ),
                                      Text(
                                        err,
                                        style: TextStyle(
                                          color: R2Colors.neutral700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (key != keyList.last)
                                Container(
                                  width: double.infinity,
                                  height: 1,
                                  color: R2Colors.neutral300,
                                ),
                            ],
                          );
                        },
                      ).toList(),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    DialogButton(
                      buttonType: EDialogButtonType.CLOSE,
                      onTapped: Get.back,
                    ),
                    if (showRetryBttn)
                      Obx(
                        () => DialogButton(
                          buttonType: EDialogButtonType.CONFIRM,
                          buttonText: "Try Reprint",
                          disabled: printerService.prepPrintingSales.contains(sale.sale),
                          onTapped: () {
                            final Sale reprintSale = _saleService.allOpenSales.firstWhere(
                              (Sale s) => s.sale == sale.sale,
                            );
                            unawaited(controller.handlePrepPrint(sale: reprintSale));
                            Get.back();
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
