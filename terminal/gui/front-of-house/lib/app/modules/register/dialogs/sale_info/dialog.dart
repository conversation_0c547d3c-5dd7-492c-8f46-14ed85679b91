import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/global_widgets/header.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/sale_info/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';

class SaleInfoDialog extends StatelessWidget {
  const SaleInfoDialog({
    required this.sale,
    this.saleName = "Sale",
  });
  final Sale sale;
  final String saleName;

  @override
  Widget build(BuildContext context) {
    final Color naColor = R2Colors.neutral500;
    final Widget divider = Container(
      width: double.infinity,
      height: 2,
      color: R2Colors.neutral300,
    );
    final Widget naText = Text(
      "N/A",
      style: TextStyle(
        fontSize: 20,
        color: R2Colors.neutral500,
      ),
    );
    return GetBuilder<SaleInfoController>(
      init: SaleInfoController(sale: sale),
      builder: (SaleInfoController controller) {
        return ColoredBox(
          color: R2Colors.white,
          child: Obx(
            () {
              return controller.loading.value
                  ? Center(
                      child: Lottie.asset(
                        'lib/assets/lottie/loading-animation.json',
                        height: 100,
                      ),
                    )
                  : Column(
                      children: <Widget>[
                        Header(
                          title: "${saleName.capitalizeFirst} Info",
                          leftButton: DialogButton(
                            buttonType: EDialogButtonType.CANCEL,
                            onTapped: () {
                              Get.back();
                            },
                          ),
                          backgroundColor: R2Colors.white,
                          transparentBackground: false,
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 25),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: <Widget>[
                                Text(
                                  "${saleName.toUpperCase()} #${sale.sale_number}",
                                  style: const TextStyle(
                                    fontSize: 25,
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Description: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if ((sale.document.saleHeader.saleDescription ?? "") != "")
                                      Text(
                                        sale.document.saleHeader.saleDescription!,
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Order Type: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      OrderType.values[sale.document.saleHeader.orderType].friendlyString,
                                      style: const TextStyle(
                                        fontSize: 22,
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Table: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if ((sale.document.saleHeader.tableDesc ?? "") != "")
                                      Text(
                                        sale.document.saleHeader.tableDesc!,
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Table Started: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (sale.document.saleHeader.tableStarted != null)
                                      Text(
                                        DateFormat('MM-dd-yy h:mm a').format(sale.document.saleHeader.tableStarted!.toLocal()),
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Seat Count: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      (sale.document.saleHeader.seatCnt ?? 1).toString(),
                                      style: const TextStyle(
                                        fontSize: 20,
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Takeout Seat Count: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      sale.document.saleHeader.toGoSeatCnt.toString(),
                                      style: const TextStyle(
                                        fontSize: 20,
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Customer Count: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      sale.document.saleHeader.customerCount.toString(),
                                      style: const TextStyle(
                                        fontSize: 20,
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Promised Time: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (sale.document.saleHeader.promisedTime != null)
                                      Text(
                                        controller.millisecondsToTimeString(sale.document.saleHeader.promisedTime!),
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Added Gratuity: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      "${sale.document.saleHeader.addedGratuity ?? 0}%",
                                      style: const TextStyle(
                                        fontSize: 20,
                                      ),
                                    ),
                                  ],
                                ),
                                divider,
                                const Text(
                                  "Customer",
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if ((sale.document.saleHeader.customer ?? "") != "")
                                  Column(
                                    children: <Widget>[
                                      Text(
                                        controller.customer != null ? sale.document.saleHeader.customerName ?? "" : "",
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      ),
                                      Text(
                                        (controller.customer?.document.phone1 ?? "") != ""
                                            ? Helpers.formatPhoneNumber(
                                                controller.customer!.document.phone1!,
                                              )
                                            : "(___)___-____",
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      ),
                                      if ((controller.customer?.document.address ?? "") != "")
                                        Text(
                                          controller.customer!.document.address!,
                                          style: const TextStyle(
                                            fontSize: 18,
                                          ),
                                        )
                                      else
                                        Text(
                                          "No current address",
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: naColor,
                                          ),
                                        ),
                                      if ((controller.customer?.document.company ?? "") != "")
                                        Text(
                                          controller.customer!.document.company!,
                                          style: const TextStyle(
                                            fontSize: 18,
                                          ),
                                        )
                                      else
                                        Text(
                                          "No current company",
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: naColor,
                                          ),
                                        ),
                                    ],
                                  )
                                else
                                  Text(
                                    "No Customer Attached",
                                    style: TextStyle(
                                      fontSize: 20,
                                      color: R2Colors.neutral500,
                                    ),
                                  ),
                                divider,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Start Employee: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (controller.initEmployee != null)
                                      Text(
                                        "${controller.initEmployee!.document.firstName} ${controller.initEmployee!.document.lastName}",
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Assigned Employee: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (controller.currEmployee != null)
                                      Text(
                                        "${controller.currEmployee!.document.firstName} ${controller.currEmployee!.document.lastName}",
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Cash Out Employee: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (controller.settleEmployee != null)
                                      Text(
                                        "${controller.settleEmployee!.document.firstName} ${controller.settleEmployee!.document.lastName}",
                                        style: const TextStyle(
                                          fontSize: 20,
                                        ),
                                      )
                                    else
                                      naText,
                                  ],
                                ),
                                divider,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    const Text(
                                      "Time Created: ",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      DateFormat('MM-dd-yy h:mm a').format(sale.created_at!.toLocal()),
                                      style: const TextStyle(
                                        fontSize: 20,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
            },
          ),
        );
      },
    );
  }
}
