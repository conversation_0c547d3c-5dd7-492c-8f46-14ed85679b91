import 'package:backoffice/app/modules/settings/children/hardware/controller.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/uom.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/scale_weight_result.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/scale.service.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/routes/app_pages.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger("ScaleWeightDialog");

class ScaleWeightDialogController extends GetxController {
  RxBool isWeighing = true.obs;

  RxDouble weight = 0.00.obs;

  Rx<UOM> uom = UOM.NA.obs;

  RxBool isScaleConnected = false.obs;

  RxBool isSteady = false.obs;

  RxBool isAbsoluteZero = false.obs;

  RxBool isOverCapacity = false.obs;

  RxBool isTareEnabled = false.obs;
  RxList<Map> scaleTares = <Map>[].obs;
  RxMap? selectedTare;

  final GraphqlService _graphqlService = Get.find();

  final ScaleService _scaleService = Get.find();

  final SaleController _saleController = Get.find();

  @override
  void onClose() {
    isWeighing.value = false;

    super.onClose();
  }

  @override
  Future<void> onInit() async {
    super.onInit();

    await loadSystemSettings();

    isWeighing.value = true;

    dynamic previousWeight = 0.0.obs;

    bool isFirstRun = true;
    while (true) {
      try {
        if (!isWeighing.value) break;

        final ScaleWeightResult scaleRead = await _scaleService.read();
        if (scaleRead.result == 0) {
          // false positive from shitty code
          if (scaleRead.message == "Scale is UnStable") continue;

          if (scaleRead.message == "Scale is Over Capacity") {
            isOverCapacity.value = true;
            isScaleConnected.value = true;

            continue;
          }
        }

        if (scaleRead.result == 1) {
          isOverCapacity.value = false;
          isScaleConnected.value = true;

          uom.value = UOM.values[int.parse(scaleRead.uom!)];
          final double weighedDouble = double.parse(scaleRead.weight.toString());

          if (isFirstRun && weighedDouble > 0) {
            isAbsoluteZero.value = false;
            continue;
          } else {
            isAbsoluteZero.value = true;
            isFirstRun = false;
          }
          switch (uom.value) {
            case UOM.OZ:
            case UOM.KG:
            case UOM.LB:
              if (isTareEnabled.value && selectedTare != null) {
                // ignore: invalid_use_of_protected_member
                weight.value = weighedDouble - double.parse(selectedTare!.value["weight"].toString());
              } else {
                weight.value = weighedDouble;
              }
              break;
            case UOM.G:
              weight.value = double.parse(
                int.parse(scaleRead.weight.toString()).toString(),
              );
              break;
            case UOM.NA:
              // TODO: Handle this case.
              break;
          }

          if (previousWeight == weight.value) {
            isSteady.value = true;
          } else {
            isSteady.value = false;
          }
          previousWeight = weight.value;
        } else {
          weight.value = 0;

          isScaleConnected.value = false;
        }
      } catch (err) {
        await Future.delayed(const Duration(seconds: 1));
        isScaleConnected.value = false;
        _logger.info("isScaleConnected: ${isScaleConnected.value}");
      }
    }
  }

  Future<void> confirm(Item item) async {
    final String pKey = await _saleController.findItemCurrentPrice(item.document.pricing, 0, item.long_desc);
    final String dualKey = "${pKey.substring(0, pKey.length - 1)}1";
    final int itemPrice = item.document.pricing[pKey] ?? 0;
    final int dualPrice = (item.document.pricing[dualKey] ?? 0) == 0 ? item.document.pricing[pKey] ?? 0 : item.document.pricing[dualKey]!;
    final int weighedPrice = (itemPrice * weight.value).round();
    final int dualWeighedPrice = (dualPrice * weight.value).round();

    Get.back(
      result: SaleRow(
        flags: [
          if (item.departmentByDepartment!.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
          SaleRowFlags.WEIGHED.index,
          if (item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
          if ((item.document.printInRed ?? false) || (item.departmentByDepartment?.document.printInRed ?? false)) SaleRowFlags.PRINT_RED.index,
        ],
        receiptDescription: item.document.receiptDesc ?? item.long_desc,
        UOM: uom.value.index,
        weight: weight.value,
        department: item.departmentByDepartment!.title,
        item: item.item,
        upc: item.upc,
        transactionFlags: [],
        isWeightedItem: true,
        basePrice: weighedPrice,
        cashBasePrice: dualWeighedPrice,
        originalPrice: itemPrice,
        cashOriginalPrice: dualPrice,
        grossPrice: weighedPrice,
        taxFlags: item.departmentByDepartment!.document.taxFlags,
        origTaxFlags: item.departmentByDepartment!.document.taxFlags,
        prep: item.document.prep > 0 ? item.document.prep : item.departmentByDepartment?.document.prep ?? 0,
      ),
    );
  }

  void cancel() {
    Get.back(result: null);
  }

  void setTare(Map? tare) {
    selectedTare = tare!.obs;
  }

  Future<void> loadSystemSettings() async {
    final QueryResult<Object> getSystemSettingResult = await _graphqlService.client.query(
      QueryOptions<Object>(
        document: g.parseString(
          GET_SYSTEM_SETTING_QUERY,
        ),
      ),
    );

    if (getSystemSettingResult.hasException) {
      throw getSystemSettingResult.exception.toString();
    }

    final SystemSettingJsonRecordDocument systemSettingDocument = SystemSettingJsonRecordDocument.fromJson(
      // ignore: avoid_dynamic_calls
      getSystemSettingResult.data!['json_record'][0]['document'] as Map<String, dynamic>,
    );

    isTareEnabled.value = systemSettingDocument.tareUOM;
    scaleTares.value = List<Map>.from(systemSettingDocument.scaleTares);
  }

  Future<void> exit() async {
    await _saleController.saveSale();

    Get.offAllNamed(AppRoutes.SETTINGS);
  }
}
