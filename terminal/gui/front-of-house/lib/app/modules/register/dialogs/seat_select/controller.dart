import 'dart:async';

import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/seat_select_types.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';

class SeatSelectController extends GetxController {
  final SaleService _saleService = Get.find();
  final IdentityService _identityService = Get.find();
  final NotificationService _notificationService = Get.find();

  final SaleController _saleController = Get.find();

  final TextEditingController descController = TextEditingController();

  final RxList<List<SaleRow>> saleRows = <List<SaleRow>>[].obs;
  final RxList<int> toSplit = <int>[].obs;

  int nonTakeoutSeats = 1;
  int currentSeatCnt = 1;
  List<int> seatsSettled = <int>[];

  @override
  Future<void> onInit() async {
    _saleController.currentSale.value.match(
      (Sale sale) {
        saleRows.value = _saleController.organizeBySeat(sale);
        nonTakeoutSeats = sale.document.saleHeader.seatCnt ?? 1;
        currentSeatCnt = sale.document.saleHeader.toGoSeatCnt + (sale.document.saleHeader.seatCnt ?? 1);
        seatsSettled = sale.document.saleHeader.seatsSettled;
      },
      () {
        _notificationService.error("No active sale.");
      },
    );
    super.onInit();
  }

  // ignore: avoid_positional_boolean_parameters
  Future<void> confirm(SeatSelectType type) async {
    toSplit.sort((int a, int b) => a.compareTo(b));
    final List<int> returnList = toSplit.map((int i) => i >= nonTakeoutSeats ? ((i - nonTakeoutSeats) + 1) * -1 : i += 1).toList();

    if (type != SeatSelectType.SPLIT_SALE) {
      Get.back(result: returnList);
    } else {
      Sale currSale = Sale.empty();

      _saleController.currentSale.value.match(
        (Sale sale) => currSale = sale,
        () => _notificationService.error("Error Splitting Sale."),
      );

      if (currSale.sale_number < 1) return;

      if (currSale.document.saleRows.firstWhereOrNull((SaleRow r) => returnList.contains(r.seatNumber) && r.splitData != null) != null) {
        return _notificationService.error("Can not split seat with split items!");
      }

      if (descController.text != "") {
        _saleController.newSaleDescController.text = descController.text;
      }

      _saleController.currentSale.value = none<Sale>();
      final Sale? newSale = await _saleController.makeNewSale();
      _saleController.currentSale.value = some(currSale);

      if (newSale == null) return;

      int newSaleSeatNum = 1;
      int newTakeoutSeatNum = -1;
      int oldSaleSeatNum = 1;
      int oldTakeoutSeatNum = -1;

      for (int i = 0; i < saleRows.length; i++) {
        final List<SaleRow> rows = saleRows[i];
        final bool togo = i >= nonTakeoutSeats;
        if (toSplit.contains(i)) {
          for (final SaleRow row in rows) {
            row.seatNumber = togo ? newTakeoutSeatNum : newSaleSeatNum;
            currSale.document.saleRows.remove(row);
            newSale.document.saleRows.add(row);
          }
          if (togo) {
            newTakeoutSeatNum--;
          } else {
            newSaleSeatNum++;
          }
        } else {
          for (final SaleRow row in rows) {
            row.seatNumber = togo ? oldTakeoutSeatNum : oldSaleSeatNum;
          }
          if (togo) {
            oldTakeoutSeatNum--;
          } else {
            oldSaleSeatNum++;
          }
        }
      }

      final int splitCnt = toSplit.where((int i) => i < nonTakeoutSeats).length;
      final int takeOutSplitCnt = toSplit.where((int i) => i >= nonTakeoutSeats).length;
      currSale.document.saleHeader.seatCnt = nonTakeoutSeats - splitCnt;
      currSale.document.saleHeader.toGoSeatCnt = currSale.document.saleHeader.toGoSeatCnt - takeOutSplitCnt;
      newSale.document.saleHeader.seatCnt = splitCnt == 0 ? 1 : splitCnt;
      newSale.document.saleHeader.toGoSeatCnt = takeOutSplitCnt;

      if (!newSale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index)) {
        newSale.document.saleHeader.saleFlags.add(SaleFlags.SUSPENDED.index);
      }

      final List<SaleRow> currRows = currSale.document.saleRows;
      final List<SaleRow> newRows = newSale.document.saleRows;

      for (int i = 0; i < currRows.length; i++) {
        if (currRows[i].index > i) {
          final int difference = currRows[i].index - i;
          if (currRows[i].parent > -1) {
            currRows[i].parent = currRows[i].parent - difference;
          }
          currRows[i].index = i;
        } else if (currRows[i].index < i) {
          final int difference = i - currRows[i].index;
          if (currRows[i].parent > -1) {
            currRows[i].parent = currRows[i].parent + difference;
          }
          currRows[i].index = i;
        }
      }
      for (int i = 0; i < newRows.length; i++) {
        if (newRows[i].index > i) {
          final int difference = newRows[i].index - i;
          if (newRows[i].parent > -1) {
            newRows[i].parent = newRows[i].parent - difference;
          }
          newRows[i].index = i;
        } else if (newRows[i].index < i) {
          final int difference = i - newRows[i].index;
          if (newRows[i].parent > -1) {
            newRows[i].parent = newRows[i].parent + difference;
          }
          newRows[i].index = i;
        }
      }

      _saleController.refreshSaleViewModels(newSale);

      final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
        employee: _identityService.currentEmployee,
        sale: newSale,
        terminalNumber: _identityService.terminalNumber,
      );

      upsertRes.fold(
        (ServiceError left) => _notificationService.error("Error making new sale: ${left.message}"),
        (Sale right) async {
          unawaited(_saleController.updateSaleOnLiquorToPour(right));
          _saleController.refreshCurrentSaleModels();
          _saleController.currentSale.refresh();
          toSplit.value = <int>[];
          descController.text = "";
          saleRows.value = _saleController.organizeBySeat(currSale);
        },
      );
    }
  }
}
