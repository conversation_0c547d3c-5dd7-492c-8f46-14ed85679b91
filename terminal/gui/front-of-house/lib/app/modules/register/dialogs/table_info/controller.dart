import 'dart:async';

import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/modules/register/dialogs/table_info/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TableInfoController extends GetxController {
  ConfigService get _configService => Get.find();

  final RxList<TableInfoRow> tableRows = <TableInfoRow>[].obs;
  late Timer timer;
  final ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    _buildRows();
    timer = Timer.periodic(const Duration(seconds: 1), (Timer t) => _buildRows());
    super.onInit();
  }

  @override
  void onClose() {
    timer.cancel();
    super.onClose();
  }

  void _buildRows() {
    final List<FlowElement> tables = <FlowElement>[..._configService.tableList];
    tables.sort((FlowElement a, FlowElement b) {
      final DateTime now = DateTime.now();
      return (a.started ?? now).compareTo(b.started ?? now);
    });
    tableRows.value = tables.map((FlowElement e) => TableInfoRow(element: e)).toList();
  }
}
