import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/header.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/table_info/controller.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

ConfigService get _configService => Get.find();
SaleService get _saleService => Get.find();

SaleController get _saleController => Get.find();

class TableInfoDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<TableInfoController>(
      init: TableInfoController(),
      builder: (TableInfoController controller) {
        return Dialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Header(
                    title: 'Table Audit',
                    leftButton: DialogButton(
                      buttonType: EDialogButtonType.CANCEL,
                      buttonColor: R2Colors.negativeRed,
                      onTapped: Get.back,
                    ),
                    transparentBackground: false,
                    backgroundColor: R2Colors.white,
                  ),
                  const TableInfoRow(),
                  const SizedBox(height: 10),
                  Expanded(
                    child: Obx(
                      () => DecoratedBox(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: R2Colors.neutral300,
                            width: 2,
                          ),
                          color: R2Colors.neutral200,
                        ),
                        child: Scrollbar(
                          thumbVisibility: true,
                          controller: controller.scrollController,
                          child: ListView.builder(
                            controller: controller.scrollController,
                            padding: EdgeInsets.zero,
                            itemCount: controller.tableRows.length,
                            itemBuilder: (_, int i) => controller.tableRows[i],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class TableInfoRow extends StatelessWidget {
  const TableInfoRow({this.element});
  final FlowElement? element;

  @override
  Widget build(BuildContext context) {
    final FontWeight fWeight = element == null ? FontWeight.bold : FontWeight.normal;
    String tableTitle = _saleController.saleName.value;
    String timeSeated = "Time Seated";
    String tableStatus = "Table Status";
    String tableSeatCnt = "Seats";
    String tableCustomerCnt = "Customers";
    bool noSaleFound = false;

    if (element != null) {
      final Sale sale = _saleService.allOpenSales.firstWhereOrNull((Sale s) => s.document.saleHeader.tableDesc == element!.desc) ?? Sale.empty();
      if (sale.sale_number < 1) noSaleFound = true;
      final DateTime now = DateTime.now();
      final DateTime tableStart = sale.document.saleHeader.tableStarted?.toLocal() ?? now;
      element!.started = tableStart;
      timeSeated = Helpers.getTimeDifferenceString(tableStart, now);
      tableStatus = Helpers.getTableStatus(sale).name;
      tableTitle = sale.sale_number < 1 ? "-" : Helpers.getSaleTitle(sale, _saleController.saleName.value);
      tableSeatCnt = (sale.document.saleHeader.seatCnt ?? 1).toString();
      tableCustomerCnt = sale.document.saleHeader.customerCount.toString();
    }

    if (element != null && noSaleFound) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Expanded(
          child: Center(
            child: Text(
              element?.desc ?? "Table",
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              element == null ? "Room" : _configService.roomList[(element!.roomIdx) - 1].desc,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              tableTitle,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              tableSeatCnt,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              tableCustomerCnt,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              tableStatus,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              timeSeated,
              style: TextStyle(fontSize: 16, fontWeight: fWeight),
            ),
          ),
        ),
      ],
    );
  }
}
