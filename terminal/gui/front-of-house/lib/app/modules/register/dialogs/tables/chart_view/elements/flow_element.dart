import 'dart:convert';

import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';

enum ElementKind {
  rectangle,
  oval,
  text,
}

enum TableStatus {
  open,
  seated,
  ordered,
  printed,
}

enum Handler {
  topCenter,
  bottomCenter,
  rightCenter,
  leftCenter,
}

class FlowElement extends ChangeNotifier {
  FlowElement({
    this.position = Offset.zero,
    this.size = Size.zero,
    this.desc = '',
    this.textColor,
    this.kind = ElementKind.rectangle,
    this.backgroundColor,
    this.borderColor,
    this.rotation = 0,
    this.seatCnt = 4,
    this.idx = 0,
    this.sectIdx = 0,
    this.shape = 10,
    this.fgnd = 0xffFFFFFF,
    this.bgnd = 0,
    this.roomIdx = 0,
    this.status = TableStatus.open,
    this.selectable = false,
    this.started,
  })  : id = '',
        isDragging = false;

  factory FlowElement.fromJson(String source) => FlowElement.fromMap(json.decode(source) as Map<String, dynamic>);

  factory FlowElement.fromMap(Map<String, dynamic> map) {
    final FlowElement e = FlowElement(
      position: Offset(
        map['positionDx'] as double,
        map['positionDy'] as double,
      ),
      size: Size(map['size.width'] as double, map['size.height'] as double),
      desc: map['text'] as String,
      textColor: Color(map['textColor'] as int),
      kind: ElementKind.values[map['kind'] as int],
      backgroundColor: Color(map['backgroundColor'] as int),
      borderColor: Color(map['borderColor'] as int),
      rotation: map['rotation'] as double,
      seatCnt: map['seats'] as int,
    );
    e.setId(map['id'] as String);
    return e;
  }

  String id;
  Offset position;
  Size size;
  String desc;
  Color? textColor;
  ElementKind kind;
  Color? backgroundColor;
  Color? borderColor;
  bool isDragging;
  double rotation;
  int seatCnt;
  int idx;
  int sectIdx;
  int shape;
  int fgnd;
  int bgnd;
  int roomIdx;
  TableStatus status;
  bool selectable;
  DateTime? started;

  @override
  String toString() {
    return 'kind: $kind  text: $desc';
  }

  /// Used internally to set an unique Uuid to this element
  void setId(String id) {
    this.id = id;
  }

  void setText(String text) {
    desc = text;
    notifyListeners();
  }

  void setTextColor(Color color) {
    textColor = color;
    notifyListeners();
  }

  void setBackgroundColor(Color color) {
    backgroundColor = color;
    notifyListeners();
  }

  void setBorderColor(Color color) {
    borderColor = color;
    notifyListeners();
  }

  void changeStatus(TableStatus tableStatus) {
    if (status == tableStatus) return;
    status = tableStatus;
    switch (status) {
      case TableStatus.open:
        return setBackgroundColor(R2Colors.white);
      case TableStatus.seated:
        return setBackgroundColor(R2Colors.tableSeated);
      case TableStatus.ordered:
        return setBackgroundColor(R2Colors.tableOrdered);
      case TableStatus.printed:
        return setBackgroundColor(R2Colors.tablePrinted);
    }
  }
}
