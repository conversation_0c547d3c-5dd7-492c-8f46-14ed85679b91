// ignore_for_file: no_leading_underscores_for_local_identifiers, prefer_null_aware_method_calls

import 'dart:math' as math;

import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/dashboard.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/objects/element_text_widget.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/objects/oval_widget.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/objects/rectangle_widget.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/objects/room_text_widget.dart';
import 'package:desktop/app/modules/register/dialogs/tables/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Widget that use [element] properties to display it on the dashboard scene
// ignore: must_be_immutable
class ElementWidget extends GetView<TablesController> {
  ElementWidget({
    super.key,
    required this.dashboard,
    required this.element,
    this.onElementPressed,
    this.onElementLongPressed,
    this.onHandlerPressed,
    this.onHandlerLongPressed,
  });
  final Dashboard dashboard;
  final FlowElement element;
  final Function(BuildContext context, Offset position)? onElementPressed;
  final Function(BuildContext context, Offset position)? onElementLongPressed;
  final Function(
    BuildContext context,
    Offset position,
    Handler handler,
    FlowElement element,
  )? onHandlerPressed;
  final Function(
    BuildContext context,
    Offset position,
    Handler handler,
    FlowElement element,
  )? onHandlerLongPressed;

  // local widget touch position when start dragging
  Offset delta = Offset.zero;

  final IdentityService _identityService = Get.find();
  final SaleController _saleController = Get.find();

  @override
  Widget build(BuildContext context) {
    Offset finalPosition = element.position;

    double displaceFactor = 0;

    if (element.kind != ElementKind.oval) {
      finalPosition = Offset(
        element.position.dx - 5,
        element.position.dy - 5,
      );

      if (element.size.width > element.size.height && element.rotation > 12) {
        displaceFactor = (element.rotation > 90 ? 180 - element.rotation : element.rotation) / 5;
        finalPosition = Offset(
          finalPosition.dx - displaceFactor,
          finalPosition.dy - displaceFactor,
        );
      }
    }

    Offset tapLocation = Offset.zero;

    return Transform.translate(
      offset: finalPosition,
      child: Transform.rotate(
        angle: element.rotation * math.pi / 180,
        child: Obx(
          () {
            bool locked = false;
            final Sale? userSalesMatch = controller.openSales
                .firstWhereOrNull((Sale s) => s.document.saleHeader.tableDesc == element.desc && s.document.saleHeader.roomIdx == element.roomIdx);
            final Sale? allSalesMatch = controller.allOpenSales
                .firstWhereOrNull((Sale s) => s.document.saleHeader.tableDesc == element.desc && s.document.saleHeader.roomIdx == element.roomIdx);

            if (userSalesMatch != null && controller.viewTables) {
              locked = userSalesMatch.document.saleHeader.currentTerminalNumber != 0 &&
                  userSalesMatch.document.saleHeader.currentTerminalNumber != _identityService.terminalNumber;
            }

            final bool selectable = !controller.viewTables || allSalesMatch == null || (userSalesMatch != null && !locked);
            final bool isCurrent = _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale == userSalesMatch?.sale;

            if (allSalesMatch != null) {
              element.changeStatus(Helpers.getTableStatus(allSalesMatch));
              element.started = allSalesMatch.document.saleHeader.tableStarted;
            } else {
              element.changeStatus(TableStatus.open);
            }

            return SizedBox(
              width: element.kind != ElementKind.oval ? element.size.width + 10 : element.size.width,
              height: element.kind != ElementKind.oval ? element.size.height + 10 : element.size.height,
              child: GestureDetector(
                onTapDown: (TapDownDetails details) {
                  tapLocation = details.globalPosition;
                },
                onTap: () {
                  if (onElementPressed != null) {
                    if (!selectable) return;
                    onElementPressed!(context, tapLocation);
                  }
                },
                onLongPress: () {
                  if (onElementLongPressed != null) {
                    if (!selectable) return;
                    onElementLongPressed!(context, tapLocation);
                  }
                },
                child: Listener(
                  onPointerDown: (PointerDownEvent event) {
                    delta = event.localPosition;
                  },
                  child: Stack(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.all(5),
                        child: element.kind == ElementKind.text
                            ? RoomTextWidget(element: element)
                            : element.kind == ElementKind.oval
                                ? OvalWidget(
                                    element: element,
                                    selectable: selectable,
                                    locked: locked,
                                    isCurrent: isCurrent,
                                  )
                                : RectangleWidget(
                                    element: element,
                                    selectable: selectable,
                                    locked: locked,
                                    isCurrent: isCurrent,
                                  ),
                      ),
                      if (element.kind != ElementKind.text)
                        ElementTextWidget(
                          element: element,
                          selectable: selectable,
                          locked: locked,
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
