// ignore_for_file: depend_on_referenced_packages

import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/dashboard.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TablesController extends GetxController {
  TablesController({this.viewTables = false});

  final bool viewTables;

  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();
  final ConfigService _configService = Get.find();
  final SaleService _saleService = Get.find();

  final OpenSalesController _openSaleController = Get.find();
  final SaleController _saleController = Get.find();

  final RxInt currentRoomIdx = 1.obs;
  final RxBool isLoading = true.obs;
  final RxBool descNeeded = false.obs;
  final RxBool onCreation = false.obs;
  final RxBool requireDesc = false.obs;
  final RxBool requireSeatCnt = false.obs;
  final RxBool requireOrdType = false.obs;
  final Rx<FlowElement> selectedElement = FlowElement().obs;
  List<RoomsJsonRecordRoom> roomList = <RoomsJsonRecordRoom>[];
  List<FlowElement> textList = <FlowElement>[];
  List<FlowElement> tableList = <FlowElement>[];
  List<Sale> allOpenSales = <Sale>[];

  late RxList<Sale> openSales;

  SaleHeader currentSaleHeader = SaleHeader.empty();

  final ScrollController verticalScrollController = ScrollController();
  final ScrollController horizontalScrollController = ScrollController();
  final TextEditingController descController = TextEditingController();
  TextEditingController newSaleSeatCntController = TextEditingController(text: "1");

  final GlobalKey chartKey = GlobalKey();
  Dashboard dashboard = Dashboard();

  @override
  Future<void> onInit() async {
    openSales = _openSaleController.openSales;
    await _saleController.currentSale.value.match(
      (Sale sale) async {
        currentSaleHeader = sale.document.saleHeader;
        if (currentSaleHeader.roomIdx != null && currentSaleHeader.roomIdx != 0) {
          if (currentSaleHeader.roomIdx! - 1 < _configService.roomList.length) {
            currentRoomIdx.value = currentSaleHeader.roomIdx!;
          }
        }
        descController.text = currentSaleHeader.saleDescription ?? "";
      },
      () {
        onCreation.value = true;
        descController.text = _saleController.newSaleDescController.text;
        requireDesc.value = _saleController.requireDesc.value;
        requireSeatCnt.value = _saleController.requireSeatCnt.value;
        requireOrdType.value = _saleController.requireOrdType.value;
      },
    );
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    allOpenSales = _saleService.allOpenSales;
    await getTableConfig();
    await setRoomScroll();
    super.onReady();
  }

  Future<void> changeRooms(int idx) async {
    await saveScroll();
    dashboard.removeAllElements();
    currentRoomIdx.value = idx;
    addDashboardElements();
    await setRoomScroll();
  }

  Future<void> selectTable(FlowElement element) async {
    await saveScroll(saveRoom: true);
    _saleController.currentSale.value.match(
      (Sale sale) {
        _saleController.handleChangeSaleTable(element);
        Get.back();
      },
      () {
        if (descController.text != "") {
          _saleController.newSaleDescController.text = descController.text;
        }
        if (newSaleSeatCntController.text != "1") {
          _saleController.newSaleSeatCntController.text = newSaleSeatCntController.text;
        }
        Get.back(result: element);
      },
    );
  }

  void changeDescription(String? value) {
    _saleController.currentSale.value.match(
      (Sale sale) {
        _saleController.handleChangeSaleDesc(value);
      },
      () => _saleController.newSaleDescController.text = value ?? "",
    );
  }

  Future<void> handleElementPressed({
    required FlowElement element,
  }) async {
    if (element.kind == ElementKind.text || element.status != TableStatus.open) {
      return;
    }

    if (onCreation.value && requireDesc.value && !requireSeatCnt.value && descController.text == "" && !requireOrdType.value) {
      _notificationService.error("Description Required");
      descNeeded.value = true;
      return;
    }

    await selectTable(element);
  }

  Future<void> handleOpenSale({
    required FlowElement element,
  }) async {
    if (element.kind == ElementKind.text) return;

    Sale? sale = _openSaleController.openSales.firstWhereOrNull((Sale s) => s.document.saleHeader.tableDesc == element.desc);
    if (sale == null) {
      _saleController.newSaleTable.value = element;
      sale = await _saleController.makeNewSale();
    }
    if (sale == null) return;
    sale.document.saleHeader.tableDesc = element.desc;
    sale.document.saleHeader.roomIdx = element.roomIdx;
    sale.document.saleHeader.tableStarted ??= DateTime.now().toUtc();
    final bool saleSelected = sale.sale != "" && _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale == sale.sale;
    if (saleSelected) return _openSaleController.deselect();
    await _openSaleController.changeSelection(sale);
    Get.back();
  }

  Size getRoomSize(int idx) {
    const double buffer = 168;
    double maxWidth = 600;
    double maxHeight = 600;
    bool widthChange = false;
    bool heightChange = false;

    for (final FlowElement text in textList) {
      if (text.roomIdx == idx) {
        if (((text.position.dx + (24 ~/ 2)) ~/ 24 * 24) > maxWidth) {
          maxWidth = (text.position.dx + (24 ~/ 2)) ~/ 24 * 24;
          widthChange = true;
        }
        if (((text.position.dy + (24 ~/ 2)) ~/ 24 * 24) > maxHeight) {
          maxHeight = (text.position.dy + (24 ~/ 2)) ~/ 24 * 24;
          heightChange = true;
        }
      }
    }
    for (final FlowElement table in tableList) {
      if (table.roomIdx == idx) {
        if (((table.position.dx + (24 ~/ 2)) ~/ 24 * 24) > maxWidth) {
          maxWidth = (table.position.dx + (24 ~/ 2)) ~/ 24 * 24;
          widthChange = true;
        }
        if (((table.position.dy + (24 ~/ 2)) ~/ 24 * 24) > maxHeight) {
          maxHeight = (table.position.dy + (24 ~/ 2)) ~/ 24 * 24;
          heightChange = true;
        }
      }
    }
    return Size(
      widthChange ? maxWidth + buffer : maxWidth,
      heightChange ? maxHeight + buffer : maxHeight,
    );
  }

  void addDashboardElements() {
    if (roomList.isEmpty) {
      dashboard.setDashboardSize(Size.zero);
      isLoading.value = false;
      return;
    }
    final Size dashSize = getRoomSize(currentRoomIdx.value);
    dashboard.setDashboardSize(dashSize);
    for (final FlowElement element in textList) {
      if (element.roomIdx == currentRoomIdx.value) {
        dashboard.addElement(element);
      }
    }
    for (final FlowElement element in tableList) {
      if (element.roomIdx == currentRoomIdx.value) {
        if (element.borderColor != R2Colors.black) {
          element.setBorderColor(R2Colors.black);
        }
        dashboard.addElement(element);
      }
    }
    isLoading.value = false;
  }

  Future<void> getTableConfig() async {
    roomList = _configService.roomList;
    textList = _configService.textList;
    tableList = _configService.tableList;

    if ((_identityService.currentEmployee.document.tablesRoom ?? 0) > roomList.length ||
        roomList.length < _identityService.currentEmployee.document.tablesScroll.keys.length) {
      await resetUsersData();
    } else if (_identityService.currentEmployee.document.tablesRoom != null) {
      currentRoomIdx.value = _identityService.currentEmployee.document.tablesRoom!;
    }

    await Future<void>.delayed(
      const Duration(milliseconds: 500),
      () => addDashboardElements(),
    );
  }

  Future<void> saveScroll({bool saveRoom = false}) async {
    await _identityService.refreshCurrentUser();
    final Map<int, TablesScrollPosition> newMap = <int, TablesScrollPosition>{..._identityService.currentEmployee.document.tablesScroll};
    newMap[currentRoomIdx.value] = TablesScrollPosition(
      x: horizontalScrollController.offset,
      y: verticalScrollController.offset,
    );
    _identityService.currentEmployee.document.tablesScroll = newMap;
    if (saveRoom) {
      _identityService.currentEmployee.document.tablesRoom = currentRoomIdx.value;
    }
    await _identityService.updateCurrentUserDocument();
  }

  Future<void> resetUsersData() async {
    await _identityService.refreshCurrentUser();
    _identityService.currentEmployee.document.tablesScroll = <int, TablesScrollPosition>{};
    _identityService.currentEmployee.document.tablesRoom = null;
    await _identityService.updateCurrentUserDocument();
  }

  Future<void> setRoomScroll() async {
    horizontalScrollController.jumpTo(
      0,
    );
    verticalScrollController.jumpTo(
      0,
    );
    isLoading.refresh();
    final TablesScrollPosition pos = _identityService.currentEmployee.document.tablesScroll[currentRoomIdx.value] ?? TablesScrollPosition();
    await horizontalScrollController.animateTo(
      pos.x,
      duration: const Duration(milliseconds: 20),
      curve: Curves.linear,
    );
    await verticalScrollController.animateTo(
      pos.y,
      duration: const Duration(milliseconds: 20),
      curve: Curves.linear,
    );
  }
}
