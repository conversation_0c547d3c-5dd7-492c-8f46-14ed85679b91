// ignore_for_file: use_colored_box

import 'package:cross_scroll/cross_scroll.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/global_widgets/menu/menu_dropdown.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/table_info/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/flow_chart.dart';
import 'package:desktop/app/modules/register/dialogs/tables/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class TablesDialog extends GetView<TablesController> {
  const TablesDialog({
    this.tablesOnly = false,
    this.viewTables = false,
  });
  final bool tablesOnly;
  final bool viewTables;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TablesController>(
      init: TablesController(viewTables: viewTables),
      builder: (TablesController controller) {
        return Container(
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
            color: R2Colors.white,
          ),
          child: Column(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: Row(
                        children: <Widget>[
                          DialogButton(
                            buttonType: EDialogButtonType.CANCEL,
                            onTapped: () async {
                              await controller.saveScroll(saveRoom: true);
                              Get.back();
                            },
                            buttonText: "Close",
                          ),
                          Expanded(
                            child: IconButton(
                              icon: Icon(
                                Icons.table_view,
                                size: 50,
                                color: R2Colors.primary500,
                              ),
                              onPressed: () async {
                                await Get.dialog(TableInfoDialog());
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    Obx(
                      () => Column(
                        children: <Widget>[
                          const Text(
                            "Room:",
                            style: TextStyle(
                              fontSize: 22,
                            ),
                          ),
                          Row(
                            children: <Widget>[
                              IconButton(
                                onPressed: controller.currentRoomIdx.value < 2
                                    ? null
                                    : () async {
                                        await controller.changeRooms(
                                          controller.currentRoomIdx.value - 1,
                                        );
                                      },
                                icon: const Icon(Icons.arrow_back_ios_sharp),
                                iconSize: 25,
                              ),
                              SizedBox(
                                width: 200,
                                child: controller.isLoading.value || controller.roomList.isEmpty
                                    ? MenuDropdown<dynamic>(
                                        items: <DropdownMenuItem<dynamic>>[
                                          DropdownMenuItem<dynamic>(
                                            child: Container(),
                                          ),
                                        ],
                                      )
                                    : MenuDropdown<int>(
                                        value: controller.currentRoomIdx.value,
                                        onChanged: (int? value) async {
                                          if (value != null) {
                                            await controller.changeRooms(value);
                                          }
                                        },
                                        items: controller.roomList
                                            .map(
                                              (RoomsJsonRecordRoom room) => DropdownMenuItem<int>(
                                                value: room.idx,
                                                child: Text(room.desc),
                                              ),
                                            )
                                            .toList(),
                                      ),
                              ),
                              IconButton(
                                onPressed: controller.currentRoomIdx.value > controller.roomList.length - 1
                                    ? null
                                    : () async {
                                        await controller.changeRooms(
                                          controller.currentRoomIdx.value + 1,
                                        );
                                      },
                                icon: const Icon(Icons.arrow_forward_ios_sharp),
                                iconSize: 25,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    if (tablesOnly)
                      Expanded(child: Container())
                    else if (controller.onCreation.value && controller.requireSeatCnt.value && !controller.requireDesc.value)
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: <Widget>[
                            Text(
                              "Seat Count: ",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: controller.descNeeded.value ? R2Colors.negativeRed : null,
                              ),
                            ),
                            IconButton(
                              iconSize: 45,
                              onPressed: () {
                                final int currentVal = int.parse(
                                  controller.newSaleSeatCntController.text,
                                );
                                if (currentVal < 2) {
                                  controller.newSaleSeatCntController.text = "40";
                                  return;
                                }
                                controller.newSaleSeatCntController.text = (currentVal - 1).toString();
                              },
                              icon: const Icon(Icons.chevron_left),
                            ),
                            SizedBox(
                              width: 80,
                              height: 40,
                              child: Center(
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: R2Colors.neutral200,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 2),
                                    child: VirtualKeyboardWrapper(
                                      isBOH: false,
                                      textEditingController: controller.newSaleSeatCntController,
                                      maxLength: 2,
                                      inputFormatters: <TextInputFormatter>[
                                        FilteringTextInputFormatter.allow(
                                          RegExp('[0-9]'),
                                        ),
                                      ],
                                      onConfirm: (String val) {
                                        if (val == "") {
                                          controller.newSaleSeatCntController.text = "1";
                                          return;
                                        }
                                        final int parsedVal = int.parse(val);
                                        if (parsedVal < 1) {
                                          controller.newSaleSeatCntController.text = "1";
                                          return;
                                        }
                                        if (parsedVal > 40) {
                                          controller.newSaleSeatCntController.text = "40";
                                          return;
                                        }
                                        controller.newSaleSeatCntController.text = val;
                                      },
                                      child: TextField(
                                        decoration: const InputDecoration(
                                          border: InputBorder.none,
                                        ),
                                        textAlign: TextAlign.center,
                                        controller: controller.newSaleSeatCntController,
                                        style: const TextStyle(fontSize: 32),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            IconButton(
                              iconSize: 45,
                              onPressed: () {
                                final int currentVal = int.parse(
                                  controller.newSaleSeatCntController.text,
                                );
                                if (currentVal > 39) {
                                  controller.newSaleSeatCntController.text = "1";
                                  return;
                                }
                                controller.newSaleSeatCntController.text = (currentVal + 1).toString();
                              },
                              icon: const Icon(Icons.chevron_right),
                            ),
                          ],
                        ),
                      )
                    else
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: <Widget>[
                            Text(
                              "Description: ",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: controller.descNeeded.value ? R2Colors.negativeRed : null,
                              ),
                            ),
                            SizedBox(
                              width: 200,
                              height: 45,
                              child: VirtualKeyboardWrapper(
                                isBOH: false,
                                textEditingController: controller.descController,
                                child: TextField(
                                  controller: controller.descController,
                                ),
                                onConfirm: (String? value) {
                                  if (value == "") {
                                    value = null;
                                  }
                                  controller.changeDescription(value);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 12,
                  ),
                  child: ClipRect(
                    child: Center(
                      child: Obx(
                        () => Stack(
                          children: <Widget>[
                            Center(
                              child: Text(
                                "No Rooms Created",
                                style: TextStyle(
                                  fontSize: 30,
                                  color: R2Colors.neutral600,
                                ),
                              ),
                            ),
                            Center(
                              child: ConstrainedBox(
                                constraints: BoxConstraints.loose(
                                  Size(
                                    controller.roomList.isEmpty ? 0 : controller.dashboard.dashboardSize.width + 2,
                                    controller.roomList.isEmpty ? 0 : controller.dashboard.dashboardSize.height + 2,
                                  ),
                                ),
                                // ignore: use_decorated_box
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: R2Colors.neutral500,
                                    ),
                                    color: R2Colors.neutral100,
                                  ),
                                  child: CrossScroll(
                                    verticalScrollController: controller.verticalScrollController,
                                    horizontalScrollController: controller.horizontalScrollController,
                                    child: SizedBox(
                                      key: controller.chartKey,
                                      width: controller.dashboard.dashboardSize.width,
                                      height: controller.dashboard.dashboardSize.height,
                                      child: FlowChart(
                                        dashboard: controller.dashboard,
                                        onDashboardTapped: (
                                          BuildContext context,
                                          Offset position,
                                        ) {},
                                        onDashboardLongtTapped: (
                                          BuildContext context,
                                          Offset position,
                                        ) {},
                                        onElementLongPressed: (
                                          BuildContext context,
                                          Offset position,
                                          FlowElement element,
                                        ) {},
                                        onElementPressed: (
                                          BuildContext context,
                                          Offset position,
                                          FlowElement element,
                                        ) async {
                                          if (viewTables) {
                                            await controller.handleOpenSale(
                                              element: element,
                                            );
                                            return;
                                          }
                                          await controller.handleElementPressed(
                                            element: element,
                                          );
                                        },
                                        onHandlerPressed: (
                                          BuildContext context,
                                          Offset position,
                                          Handler handler,
                                          FlowElement element,
                                        ) {},
                                        onHandlerLongPressed: (
                                          BuildContext context,
                                          Offset position,
                                          Handler handler,
                                          FlowElement element,
                                        ) {},
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            if (controller.isLoading.value)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0,
                                  vertical: 12,
                                ),
                                child: Container(
                                  color: R2Colors.neutral300,
                                ),
                              ),
                            if (controller.isLoading.value)
                              Center(
                                child: Text(
                                  "Loading...",
                                  style: TextStyle(
                                    fontSize: 50,
                                    color: R2Colors.neutral600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
