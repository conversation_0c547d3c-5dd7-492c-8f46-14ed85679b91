import 'dart:async';

import 'package:backoffice/app/data/enums/customer_copy_print_options.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/receipt_print_settings.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/pre_auth.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/pole_display.service.dart';
import 'package:desktop/app/data/services/pre_auth.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/num_pad/controller.dart';
import 'package:desktop/app/global_widgets/widget/confirmation_dialog.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('TenderDialogController');

enum ExternalTenderStatus {
  Initial,
  Running,
  Success,
  Error,
}

class ExternalTenderStatusViewModel {
  ExternalTenderStatusViewModel({
    required this.status,
    this.error = "",
    this.content = "",
  });
  final ExternalTenderStatus status;
  final String error;
  final String content;
}

class NewTenderDialogController extends GetxController {
  NewTenderDialogController(this.seats, {this.quickCash = false, this.beerBeerCash = false});

  List<int> seats = <int>[];
  final bool quickCash;
  final bool beerBeerCash;

  final SaleController _saleController = Get.find();
  final NumPadController numPadController = Get.find();
  final RegisterController _registerController = Get.find();

  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();
  final PaymentService _paymentService = Get.find();
  final PoleDisplayService _poleDisplayService = Get.find();
  final PrinterService _printerService = Get.find();
  final SaleService _saleService = Get.find();
  final CustomerService _customerService = Get.find();
  final EmployeeService _employeeService = Get.find();
  final PreAuthService _preAuthService = Get.find();
  final ConfigService _configService = Get.find();
  final ActivityService _activityService = Get.find();

  final RxBool disableSwipe = false.obs;

  final Rx<MerchantJsonRecord> merchantConfig = MerchantJsonRecord.empty().obs;
  final Rx<Sale> sale = Sale.empty().obs;
  final Rx<ExternalTenderStatusViewModel> externalTenderStatusVM = ExternalTenderStatusViewModel(
    status: ExternalTenderStatus.Initial,
  ).obs;
  final RxList<PreAuth> preAuths = <PreAuth>[].obs;

  String legacyGiftName = PaymentMediaType.LegacyGift.string;

  ReceiptPrintSettings printReceiptSetting = ReceiptPrintSettings.PROMPT;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    merchantConfig.value = _configService.merchantConfig;

    legacyGiftName = merchantConfig.value.document.legacyGiftName;

    sale.value = _saleController.currentSale.value.match(
      (Sale s) {
        if (seats.isEmpty) {
          return s;
        } else {
          final Sale saleCopy = Sale.empty();
          saleCopy.document.saleHeader = Helpers.makeDeepSaleHeaderCopy(s);
          saleCopy.document.saleRows = s.document.saleRows.where((SaleRow sr) => seats.contains(sr.seatNumber)).toList();
          saleCopy.sale_number = s.document.saleHeader.reservedSaleNumber ?? 0;
          saleCopy.document.saleHeader.saleNumber = s.document.saleHeader.reservedSaleNumber ?? 0;
          _saleController.refreshSaleViewModels(saleCopy);
          return saleCopy;
        }
      },
      () => Sale.empty(),
    );

    final bool dp = merchantConfig.value.document.dualPricing;

    _poleDisplayService.write(
      line1: dp ? "Cash Total:" : "",
      line1Suffix: dp ? "\$${Helpers.formatCurrency(sale.value.document.saleHeader.cashTotal)}" : null,
      line2Prefix: dp ? "Card Total:" : "Total:",
      line2Suffix: "\$${Helpers.formatCurrency(sale.value.document.saleHeader.total)}",
    );

    // tenders.value = sale.value.document.saleHeader.tenders
    //     .where((SaleTender tender) => !tender.saleTenderFlags.contains(SaleTenderFlags.PRE_AUTH.index))
    //     .toList();

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onReady() async {
    // Pull in existing tenders from the register controller
    _saleController.currentSale.refresh();

    preAuths.value = await loadUnfinalizedPreAuths();

    // TODO: not even sure if this is needed
    // if (currentTenders.isEmpty && balanceDueInt == 0) {
    //   Future<void>.delayed(
    //     Duration.zero,
    //     () async {
    //       final bool? refund = await Get.dialog<bool?>(
    //         ConfirmationDialog(
    //           confirmText: "Proceed",
    //           declineText: "Cancel",
    //           title: const Text(
    //             "Process Exchange",
    //             style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
    //           ),
    //           content: Column(
    //             children: const <Widget>[
    //               Padding(
    //                 padding: EdgeInsets.only(bottom: 20.0),
    //                 child: Text(
    //                   "This transaction has a \$0.00 value.",
    //                   style: TextStyle(
    //                     fontSize: 24,
    //                   ),
    //                 ),
    //               ),
    //               Text(
    //                 "Would you like to proceed?",
    //                 style: TextStyle(
    //                   fontSize: 18,
    //                 ),
    //               ),
    //             ],
    //           ),
    //         ),
    //       );

    //       if (refund == true) {
    //         // await runRefund(MediaType.CREDIT, 0);
    //       } else {
    //         Get.back();
    //       }
    //     },
    //   );
    // }

    if (beerBeerCash) await Future<void>.delayed(const Duration(milliseconds: 300));

    if (quickCash || beerBeerCash) {
      if (!await tenderCashFlow(dualPricingBalance)) {
        Get.back();
      } else if (!beerBeerCash) {
        _notificationService.success("Sale Completed Successfully!");
      }
    }

    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onClose() async {
    numPadController.text.value = none<String>();
    sale.value = Sale.empty();

    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  String get balanceDueString => Helpers.formatCurrency(balance);

  ///
  ///
  ///
  ///
  ///
  ///
  int get balance {
    int tenderAppliedAmount = 0;

    if (sale.value.document.saleHeader.tenders.isNotEmpty) {
      for (final SaleTender tender in sale.value.document.saleHeader.tenders) {
        if (!tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index)) {
          tenderAppliedAmount += tender.amount ?? 0;
        }
      }
    }

    return sale.value.document.saleHeader.total - tenderAppliedAmount;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool get merchantTipping => merchantConfig.value.document.tipping;

  ///
  ///
  ///
  ///
  ///
  ///
  bool get hasNonCreditTender => sale.value.document.saleHeader.tenders.any((SaleTender element) => element.media != PaymentMediaType.Credit.index);

  bool get hasCreditTender => sale.value.document.saleHeader.tenders.any((SaleTender element) => element.media == PaymentMediaType.Credit.index);

  bool get hasCashTender => sale.value.document.saleHeader.tenders.any((SaleTender t) => t.media == PaymentMediaType.Cash.index);

  bool get hasNonCashTender => sale.value.document.saleHeader.tenders.any((SaleTender t) => t.media != PaymentMediaType.Cash.index);

  bool get hasHouseTender => sale.value.document.saleHeader.tenders.any((SaleTender t) => t.media == PaymentMediaType.House.index);

  ///
  ///
  ///
  ///
  ///
  ///
  bool get saleCompleted => (isDualPricingEnabled ? dualPricingBalance : balance) <= 0;

  ///
  ///
  ///
  ///
  ///
  ///
  int get dualPricingBalance {
    int tenderAppliedAmount = 0;

    if (sale.value.document.saleHeader.tenders.isNotEmpty) {
      for (final SaleTender tender in sale.value.document.saleHeader.tenders) {
        if (!tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index)) {
          tenderAppliedAmount += tender.amount ?? 0;
        }
      }
    }

    return sale.value.document.saleHeader.cashTotal - tenderAppliedAmount;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  int nextDollarInt({bool isDualPricing = false}) {
    final double currentDouble = isDualPricing
        ? double.parse(
            Helpers.formatCurrency(dualPricingBalance).replaceAll(",", ""),
          )
        : double.parse(balanceDueString.replaceAll(",", ""));
    if (currentDouble % 1 == 0) {
      return balance;
    } else {
      final int roundedUpVal = currentDouble.ceil();
      return roundedUpVal * 100;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> printReceipt({bool customerCopy = false, SaleTender? creditTender}) async {
    final List<Future<PrintJobReturn>> printFutures = <Future<PrintJobReturn>>[];

    final Either<ServiceError, Employee> empRes = await _employeeService.getEmployeeById(
      sale.value.document.saleHeader.currentEmployeeNumber,
    );
    final Employee currentEmployee = empRes.match(
      (ServiceError l) {
        _notificationService.error(l.message);
        return _identityService.currentEmployee;
      },
      (Employee r) => r,
    );

    Customer? cust;
    if ((sale.value.document.saleHeader.customer ?? "") != "") {
      final Either<ServiceError, Customer> custRes = await _customerService.getCustomerByPk(sale.value.document.saleHeader.customer!);
      cust = custRes.match(
        (ServiceError l) {
          _notificationService.error(l.message);
          return null;
        },
        (Customer r) => r,
      );
    }

    printFutures.addAll(
      _makeReceipts(
        customerCopy: customerCopy,
        currentEmployee: currentEmployee,
        customer: cust,
        tenders: creditTender != null ? <SaleTender>[creditTender] : null,
      ),
    );

    if (saleCompleted) {
      final List<SaleRow> giftIssues =
          sale.value.document.saleRows.where((SaleRow r) => r.flags.contains(SaleRowFlags.GIFT.index) && r.originalPrice > 0).toList();

      for (final SaleRow giftRow in giftIssues) {
        printFutures.add(
          _printerService.printReceipt(
            remotePrintIdx: _identityService.remoteTermIdx,
            printJob: PrinterInterface.buildGiftIssue(
              currentEmployee: _identityService.currentEmployee,
              saleRow: giftRow,
              saleNumber: sale.value.sale_number,
              receiptHeader: merchantConfig.value.document.receiptHeader,
            ),
          ),
        );
      }
    }

    // putting this in a .then() to avoid the Future.wait() so
    // that its not blocking the UI
    unawaited(
      Future.wait(printFutures).then(
        (List<PrintJobReturn> printResults) {
          for (int i = 0; i < printResults.length; i++) {
            final List<ServiceError> errs = printResults[i].errors ?? <ServiceError>[];
            if (errs.isNotEmpty) {
              _notificationService.error(errs.first.message);
              i = printResults.length;
            }
          }
        },
      ),
    );
  }

  List<Future<PrintJobReturn>> _makeReceipts({
    required bool customerCopy,
    required Employee currentEmployee,
    List<SaleTender>? tenders,
    Customer? customer,
  }) {
    final List<Future<PrintJobReturn>> printFutures = <Future<PrintJobReturn>>[];

    if (customerCopy) {
      printFutures.add(
        // ignore: discarded_futures
        _printerService.printReceipt(
          remotePrintIdx: _identityService.remoteTermIdx,
          printJob: PrinterInterface.buildReceipt(
            currentEmployee: currentEmployee,
            sale: sale.value,
            saleName: _saleController.saleName.value,
            receiptHeader: merchantConfig.value.document.receiptHeader,
            isMerchantDualPricing: merchantConfig.value.document.dualPricing,
            taxList: _configService.salesTaxList,
            currentUser: _identityService.currentEmployee,
            customer: customer,
            terminalDescs: _identityService.terminalDescs,
            tipping: merchantTipping && merchantConfig.value.document.tipLinesOnCustCopy,
            legacyGiftName: merchantConfig.value.document.legacyGiftName,
            showCommentRows: merchantConfig.value.document.commentRowsOnReceipt,
            showSaleDesc: merchantConfig.value.document.saleDescOnReceipt,
            showSeats: _saleController.getProperSection().trackBySeat,
            tenders: tenders,
            condensedAuth: merchantConfig.value.document.condensedAuthSlip,
          ),
        ),
      );
    }

    final bool refundedLines = (sale.value.document.saleRows.any((SaleRow r) => r.flags.contains(SaleRowFlags.REFUNDED.index)) ||
            sale.value.document.saleHeader.saleFlags.contains(SaleFlags.REFUNDED.index)) &&
        merchantConfig.value.document.refundReceiptSignatureLine;

    final bool printMerchantHouse = merchantConfig.value.document.printMerchantOnHouse && hasHouseTender;
    final bool printMerchantCash = merchantConfig.value.document.printMerchantOnCash && hasCashTender;
    final bool printMerchantNonCash = merchantConfig.value.document.printMerchantOnNonCash && hasNonCashTender;

    final bool printMerchant = printMerchantHouse || printMerchantCash || (printMerchantNonCash && !(hasNonCreditTender && hasCreditTender));

    if (refundedLines || printMerchant) {
      printFutures.add(
        // ignore: discarded_futures
        _printerService.printReceipt(
          remotePrintIdx: _identityService.remoteTermIdx,
          printJob: PrinterInterface.buildReceipt(
            currentEmployee: currentEmployee,
            sale: sale.value,
            saleName: _saleController.saleName.value,
            receiptHeader: merchantConfig.value.document.receiptHeader,
            refundReceiptSignatureLine: merchantConfig.value.document.refundReceiptSignatureLine,
            isMerchantDualPricing: merchantConfig.value.document.dualPricing,
            taxList: _configService.salesTaxList,
            currentUser: _identityService.currentEmployee,
            customerCopy: false,
            customer: customer,
            terminalDescs: _identityService.terminalDescs,
            tipping: merchantTipping,
            legacyGiftName: merchantConfig.value.document.legacyGiftName,
            showCommentRows: merchantConfig.value.document.commentRowsOnReceipt,
            showSaleDesc: merchantConfig.value.document.saleDescOnReceipt,
            showSeats: _saleController.getProperSection().trackBySeat,
            tenders: tenders,
            condensedAuth: merchantConfig.value.document.condensedAuthSlip,
          ),
        ),
      );
    }

    return printFutures;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> reset() async {
    if (seats.isEmpty) {
      _saleController.resetSale();
    } else {
      await _updateIfSplitSeats();
    }
    Get.back();
    sale.value = Sale.empty();
  }

  ///
  ///
  ///
  ///
  ///
  /// If dualPricing is enabled.
  bool get isDualPricingEnabled {
    if (merchantConfig.value.document.dualPricing &&
        sale.value.document.saleHeader.tenders.firstWhereOrNull((SaleTender t) => !isCashPrice(PaymentMediaType.values[t.media!])) == null) {
      return true;
    } else {
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool isCashPrice(PaymentMediaType type) => type != PaymentMediaType.Credit && type != PaymentMediaType.Debit;

  ///
  ///
  ///
  ///
  ///
  ///
  bool get isRefundMode {
    return balance < 0 || (isDualPricingEnabled && dualPricingBalance < 0);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool get isNegativeOrZeroBalance {
    return balance <= 0 || (isDualPricingEnabled && dualPricingBalance <= 0);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool get creditRemainingInactive {
    return sale.value.document.saleHeader.tenders.isNotEmpty && isDualPricingEnabled && isRefundMode;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<int?> getTenderAmount(PaymentMediaType type) async {
    final List<PaymentMediaType> typesToCheck = <PaymentMediaType>[
      PaymentMediaType.Check,
      PaymentMediaType.House,
      PaymentMediaType.Gift,
      PaymentMediaType.LegacyGift,
    ];
    // Otherwise, try to use the plain pin value
    int amount = numPadController.text.value.match(
      (String textString) {
        final int intPin = int.parse(textString.replaceAll(RegExp(r'\.|\,|\$'), ''));

        if (intPin == 0) {
          // If no pin typed, use the balanceDueInt()
          return balance;
        }
        // If pin typed, use pin
        return intPin;
      },
      // On none, use balanceDueInt() or dualPricingTotal if dual pricing enabled
      () {
        return creditRemainingInactive || (isDualPricingEnabled && isCashPrice(type)) ? dualPricingBalance : balance;
      },
    );
    // Check to see if balance is higher than given amount for certain types, prompt for adjustment
    if (typesToCheck.contains(type)) {
      final int balanceToUse = isDualPricingEnabled ? dualPricingBalance : balance;
      if (amount > balanceToUse) {
        final bool? result = await Get.dialog<bool?>(
          ConfirmationDialog(
            confirmColor: R2Colors.primary500,
            declineColor: R2Colors.negativeRed,
            confirmText: "Confirm",
            declineText: "Cancel",
            onConfirm: () {
              Get.back(result: true);
            },
            title: Text(
              type == PaymentMediaType.LegacyGift
                  ? "Overtender by \$${Helpers.formatCurrency(amount - balanceToUse)}"
                  : "Payment amount will be adjusted to remaining balance of \$${Helpers.formatCurrency(balanceToUse)}",
              style: const TextStyle(
                fontWeight: FontWeight.w300,
                fontSize: 18,
              ),
            ),
          ),
        );
        if (result != true) return null;
        if (type != PaymentMediaType.LegacyGift) amount = balanceToUse;
        numPadController.text.value = none();
      }
    }

    return amount;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> receiptPrompt({SaleTender? creditTender}) async {
    final bool? custConfirm = merchantConfig.value.document.customerCopyPrintOpt != CustomerCopyPrintOptions.PROMPT_FOR_PRINT.index
        ? merchantConfig.value.document.customerCopyPrintOpt == CustomerCopyPrintOptions.ALWAYS_PRINT.index
        : await Get.dialog<bool?>(
            ConfirmationDialog(
              confirmColor: R2Colors.primary500,
              declineColor: R2Colors.negativeRed,
              confirmText: "Print",
              declineText: "Don't Print",
              title: Text(
                creditTender != null && !saleCompleted ? "Card Payment Successful" : "Transaction Complete",
                style: const TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
              ),
              content: Column(
                children: <Widget>[
                  if (printReceiptSetting == ReceiptPrintSettings.PROMPT)
                    const Text(
                      "Print customer receipt?",
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
                    ),
                ],
              ),
              noOptions: printReceiptSetting != ReceiptPrintSettings.PROMPT,
            ),
          );
    await printReceipt(
      customerCopy: custConfirm ?? false,
      creditTender: creditTender,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  // Future<void> changeTenderedSeats(List<int> newSeats) async {
  //   seats = newSeats;
  //   if (sale.value.document.saleHeader.tenders.isNotEmpty) {
  //     sale.value.document.saleHeader.seatsTendered = newSeats;
  //     _saleController.currentSale.value.match(
  //       (Sale s) {
  //         s.document.saleHeader.seatsTendered = newSeats;
  //         // s.document.saleHeader.tenders = tenders;
  //         _saleController.currentSale.value = some(s);
  //       },
  //       () => null,
  //     );
  //   }
  //   await onReady();
  // }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<List<PreAuth>> loadUnfinalizedPreAuths() async {
    try {
      final Sale? currentSale = _saleController.currentSale.value.toNullable();
      // NOTE(real-z-r2): Note this is a design choice we made that preauth should work even with split seating. In the future we might want to change
      // this.
      final Either<ServiceError, List<PreAuth>> preAuthsBySaleResponse = await _preAuthService.getPreAuthsBySale(
        sale: currentSale?.sale ?? "",
        finalized: false,
      );

      return preAuthsBySaleResponse.fold(
        (ServiceError error) {
          _logger.severe("error fetching pre auths by sale: ${error.message}");
          return <PreAuth>[];
        },
        (List<PreAuth> p) => p,
      );
    } catch (e) {
      _logger.severe("error loading pre auths", e);
      return <PreAuth>[];
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender cash
  Future<bool> tenderCash(int amount) async {
    _logger.info("tendering cash (amount: $amount)");

    try {
      _setTender(PaymentMediaType.Cash, amount);

      final int changeDue = isDualPricingEnabled ? dualPricingBalance : balance;
      if (changeDue < 0) {
        _setTender(PaymentMediaType.Cash, changeDue, changeDue: true);

        await Future<void>.delayed(Duration.zero, () {
          _poleDisplayService.write(
            line1: "",
            line2Prefix: "Change Due:",
            line2Suffix: "\$${Helpers.formatCurrency(changeDue.abs())}",
          );
        });
      }

      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Initial,
      );

      return await _postTender(PaymentMediaType.Cash, amount, popCashDrawer: true);
    } catch (e) {
      _logger.severe("error processing cash payment", e);
      return false;
    }
  }

  Future<bool> cashConfirm() async {
    if (merchantConfig.value.document.cashConfirmation) {
      return (await Get.dialog<bool?>(
            ConfirmationDialog(
              confirmColor: R2Colors.primary500,
              declineColor: R2Colors.negativeRed,
              confirmText: "Confirm",
              declineText: "Cancel",
              onConfirm: () {
                Get.back(result: true);
              },
              title: const Text(
                "Cash Payment",
                style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
              ),
              content: const Padding(
                padding: EdgeInsets.only(top: 10.0, bottom: 10.0),
                child: Text(
                  "Would you like to tender in cash? ",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          )) ==
          true;
    }
    return true;
  }

  Future<void> changeConfirm() async {
    // combine all negative tenders into one variable
    final List<SaleTender> negativeTenders = sale.value.document.saleHeader.tenders.where((SaleTender e) => (e.amount ?? 0) < 0).toList();
    final bool hasRefund = negativeTenders.any((SaleTender tender) => tender.saleTenderFlags.contains(SaleTenderFlags.REFUNDED.index));
    if (negativeTenders.isNotEmpty) {
      final int negativeTenderTotal = negativeTenders.map((SaleTender e) => e.amount ?? 0).reduce((int a, int b) => a + b);

      if (negativeTenderTotal < 0) {
        String dialogText = "Change due: ";
        if (hasRefund) {
          dialogText = "Refund due: ";
        }
        await Get.dialog(
          ConfirmationDialog(
            confirmColor: R2Colors.primary500,
            declineColor: R2Colors.negativeRed,
            confirmText: "Confirm",
            title: const Text(
              "Transaction Complete",
              style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
            ),
            content: Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(bottom: 20.0),
                  child: Text(
                    "$dialogText \$${Helpers.formatCurrency(negativeTenderTotal.abs())}",
                    style: const TextStyle(
                      fontSize: 34,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }
  }

  Future<bool> tenderCashFlow(int amount) async {
    if (!await cashConfirm()) return false;
    if (await tenderCash(amount)) {
      await changeConfirm();
      if (!quickCash && !beerBeerCash) await receiptPrompt();
      await reset();
    }
    return true;
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender credit
  Future<bool> tenderCredit(int amount) async {
    _logger.info("tendering credit (amount: $amount)");

    try {
      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Running,
        content: "Processing credit payment...",
      );

      bool transactionSuccessful = false;

      Either<ErrorResponse, CardTransactionData> resp;
      if (amount < 0) {
        resp = await _paymentService.refund(
          amount: amount.abs(),
          tenderType: PaymentMediaType.Credit,
        );
      } else {
        resp = await _paymentService.sale(
          amount: amount,
          tenderType: PaymentMediaType.Credit,
        );
      }

      SaleTender? newTender;

      transactionSuccessful = await resp.fold<Future<bool>>((ErrorResponse error) async {
        _logger.severe("error processing credit payment: ${error.message}");
        // _notificationService.error(error.message);

        externalTenderStatusVM.value = ExternalTenderStatusViewModel(
          status: ExternalTenderStatus.Error,
          error: error.message,
        );

        return false;
      }, (CardTransactionData success) async {
        _logger.info("credit payment processed successfully", success);

        newTender = _setTender(PaymentMediaType.Credit, amount, cardTransactionData: success);

        externalTenderStatusVM.value = ExternalTenderStatusViewModel(
          status: ExternalTenderStatus.Initial,
        );

        return true;
      });

      if (!transactionSuccessful) return false;

      final bool saleComplete = await _postTender(PaymentMediaType.Credit, amount);

      if (merchantTipping) {
        await receiptPrompt(creditTender: newTender);
      }

      return saleComplete;
    } catch (e) {
      _logger.severe("error processing credit payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender check
  Future<bool> tenderCheck(int amount, {String checkNo = ""}) async {
    _logger.info("tendering check (checkNo: $checkNo, amount: $amount)");

    try {
      // TODO: handle check change?

      int transactionAmount = amount;

      final int balanceToUse = isDualPricingEnabled ? dualPricingBalance : balance;

      // This should never happen, because getTenderAmount() would have already adjusted the amount downward to the correct balance after asking the
      // cashier.
      if (transactionAmount > balanceToUse) {
        _notificationService.error("Check amount is greater than balance due, adjusting to balance due");
        transactionAmount = balanceToUse;
      }

      _setTender(PaymentMediaType.Check, transactionAmount, checkNo: checkNo, changeDue: amount < 0);
      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Initial,
      );
      return await _postTender(PaymentMediaType.Check, amount, popCashDrawer: true);
    } catch (e) {
      _logger.severe("error processing check payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender gift
  Future<bool> tenderGiftCard(String cardNumber, int amount) async {
    _logger.info("tendering gift card (amount: $amount)");

    try {
      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Running,
        content: "Processing gift card payment...",
      );

      bool transactionSuccessful = false;
      int giftAmountApplied = 0;
      // logic between pre and post tender
      {
        final Either<ErrorResponse, GiftTransactionData> inquiryResponse = await _paymentService.giftInquiry(
          cardNumber: cardNumber,
        );

        transactionSuccessful = await inquiryResponse.fold((ErrorResponse error) async {
          _logger.severe("error processing gift inquiry: ${error.message}");

          externalTenderStatusVM.value = ExternalTenderStatusViewModel(
            status: ExternalTenderStatus.Error,
            error: error.message,
          );

          return false;
        }, (GiftTransactionData success) async {
          int balanceInt = 0;
          success.balances.balance?.forEach((Balance element) {
            balanceInt += int.parse(element.amount.replaceAll(".", ""));
          });

          if (balanceInt < amount) {
            giftAmountApplied = balanceInt;
          } else {
            giftAmountApplied = amount;
          }

          if (giftAmountApplied == 0) {
            externalTenderStatusVM.value = ExternalTenderStatusViewModel(
              status: ExternalTenderStatus.Error,
              error: "Error processing gift payment",
              content: "Gift card balance is not enough (\$${Helpers.formatCurrency(balanceInt)})",
            );

            return false;
          }

          final Either<ErrorResponse, GiftTransactionData> resp = await _paymentService.giftRedemption(
            cardNumber: cardNumber,
            amount: giftAmountApplied,
          );

          return resp.fold<Future<bool>>((ErrorResponse error) async {
            _logger.severe("error processing gift payment: ${error.message}");

            externalTenderStatusVM.value = ExternalTenderStatusViewModel(
              status: ExternalTenderStatus.Error,
              error: error.message,
            );

            return false;
          }, (GiftTransactionData success) async {
            _logger.info("gift payment processed successfully");

            _setTender(PaymentMediaType.Gift, giftAmountApplied, giftTransactionData: success);

            externalTenderStatusVM.value = ExternalTenderStatusViewModel(
              status: ExternalTenderStatus.Initial,
            );

            return true;
          });
        });
      }

      if (!transactionSuccessful) return false;

      return await _postTender(PaymentMediaType.Gift, giftAmountApplied);
    } catch (e) {
      _logger.severe("error processing gift payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender EBT
  Future<bool> tenderEBT(int amount) async {
    _logger.info("tendering EBT (amount: $amount)");

    try {
      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Running,
        content: "Processing EBT payment...",
      );

      bool transactionSuccessful = false;

      Either<ErrorResponse, CardTransactionData> resp;
      if (amount < 0) {
        resp = await _paymentService.refund(
          amount: amount.abs(),
          tenderType: PaymentMediaType.EBT,
        );
      } else {
        resp = await _paymentService.sale(
          amount: amount,
          tenderType: PaymentMediaType.EBT,
        );
      }

      transactionSuccessful = await resp.fold<Future<bool>>((ErrorResponse error) async {
        _logger.severe("error processing EBT payment: ${error.message}");
        // _notificationService.error(error.message);

        externalTenderStatusVM.value = ExternalTenderStatusViewModel(
          status: ExternalTenderStatus.Error,
          error: error.message,
        );

        return false;
      }, (CardTransactionData success) async {
        _logger.info("EBT payment processed successfully", success);

        _setTender(PaymentMediaType.EBT, amount, cardTransactionData: success);

        externalTenderStatusVM.value = ExternalTenderStatusViewModel(
          status: ExternalTenderStatus.Initial,
        );

        return true;
      });

      if (!transactionSuccessful) return false;

      return await _postTender(PaymentMediaType.EBT, amount);
    } catch (e) {
      _logger.severe("error processing EBT payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tender house
  Future<bool> tenderHouseOrLegacy(int amount, {String memo = "", bool isHouse = true}) async {
    _logger.info("tendering house (memo: $memo, amount: $amount)");

    try {
      // TODO: handle house change and dual pricing?

      final PaymentMediaType type = isHouse ? PaymentMediaType.House : PaymentMediaType.LegacyGift;

      _setTender(type, amount, memo: memo, changeDue: !isHouse);

      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Initial,
      );

      return await _postTender(type, amount, popCashDrawer: true);
    } catch (e) {
      _logger.severe("error processing house payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<bool> tenderPreAuth(int amount) async {
    _logger.info("tendering pre auth (amount: $amount)");

    try {
      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Running,
        content: "Processing pre auth(s)...",
      );

      bool captured = false;
      for (final PreAuth preAuth in preAuths) {
        final String preAuthRefTxnId = preAuth.document.refTxnID;
        final String preAuthID = preAuth.pre_auth!;

        if (captured) {
          // void any additionals and then stop if we've captured enough
          final Either<ErrorResponse, CardTransactionData> preAuthVoidResult = await _paymentService.txnVoid(
            settledTerminalNumber: _identityService.terminalNumber,
            refTxnId: preAuthRefTxnId,
          );

          await preAuthVoidResult.fold(
            (ErrorResponse error) async {
              _logger.severe("error voiding pre auth", error);
            },
            (CardTransactionData success) async {
              _logger.info("pre auth voided successfully", success);

              final Either<ServiceError, bool> preAuthVoidDatabaseResult = await _preAuthService.setPreAuthVoided(
                preAuth: preAuthID,
              );

              preAuthVoidDatabaseResult.fold((ServiceError error) {
                _logger.severe("error setting pre auth to voided", error);
              }, (bool success) {
                _logger.info("pre auth voided successfully", success);
              });
            },
          );

          continue;
        }

        final Either<ErrorResponse, CardTransactionData> preAuthCompletionResponse = await _paymentService.capture(
          amount: amount,
          refTxnId: preAuthRefTxnId,
          settledTerminalNumber: _identityService.terminalNumber,
        );

        await preAuthCompletionResponse.fold(
          (ErrorResponse error) {
            _logger.severe("error processing pre auth completion: ${error.message}");
          },
          (CardTransactionData success) async {
            final Either<ServiceError, PreAuth> preAuthFinalizedResult = await _preAuthService.setPreAuthFinalized(
              preAuth: preAuthID,
            );

            await preAuthFinalizedResult.fold(
              (ServiceError error) {
                _logger.severe("error finalizing pre auth", error);
              },
              (PreAuth finalizedPreAuth) async {
                _logger.info("pre auth completion processed successfully", success);
                _setTender(PaymentMediaType.Credit, amount, cardTransactionData: success);
                captured = true;
              },
            );
          },
        );
      }

      preAuths.value = await loadUnfinalizedPreAuths();

      externalTenderStatusVM.value = ExternalTenderStatusViewModel(
        status: ExternalTenderStatus.Initial,
      );

      return await _postTender(PaymentMediaType.Credit, amount);
    } catch (e) {
      _logger.severe("error processing credit payment", e);
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<bool> _completeSale() async {
    try {
      sale.value.document.saleHeader.currentCashier = _identityService.currentCashier;

      bool containsForgiveTax = false;
      for (final SaleRow saleRow in sale.value.document.saleRows) {
        if (saleRow.flags.contains(SaleRowFlags.FORGIVE_TAX.index)) {
          containsForgiveTax = true;
        }
      }

      if (containsForgiveTax && !sale.value.document.saleHeader.saleFlags.contains(SaleFlags.TAX_FORGIVEN.index)) {
        sale.value.document.saleHeader.saleFlags.add(SaleFlags.TAX_FORGIVEN.index);
      }

      sale.value.document.saleHeader.dualPricingPercent = merchantConfig.value.document.dualPricingPercent;

      // If the only tenders are cash, check, EBT or gift, and dual pricing is enabled, add the dual pricing sale flag
      if (isDualPricingEnabled && !sale.value.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
        // if any of the sale tenders are not dual pricing eligible then we cant add the dual pricing flag
        if (sale.value.document.saleHeader.tenders.any((SaleTender t) => !isCashPrice(PaymentMediaType.values[t.media!]))) {
          _logger.info("not all tenders are dual pricing eligible");
        } else {
          _logger.info("all tenders are dual pricing eligible");
          sale.value.document.saleHeader.saleFlags.add(SaleFlags.DUAL_PRICING.index);
        }
      }

      if (sale.value.document.saleHeader.subTotal < 0 && !sale.value.document.saleHeader.saleFlags.contains(SaleFlags.REFUNDED.index)) {
        sale.value.document.saleHeader.saleFlags.add(SaleFlags.REFUNDED.index);
      }

      if (!sale.value.document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
        sale.value.document.saleHeader.saleFlags.add(SaleFlags.COMPLETED.index);
      }
      sale.value.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id ?? 0;
      sale.value.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;
      sale.value.document.saleHeader.currentTerminalNumber = 0;
      sale.value.end_at = DateTime.now().toUtc();
      if (seats.isNotEmpty) sale.value.document.saleHeader.originalSale = _saleController.currentSale.value.match((Sale s) => s.sale, () => null);

      if (sale.value.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
        for (final SaleRow r in sale.value.document.saleRows) {
          r.actualPrice = r.cashPrice;
        }
      }

      final Either<ServiceError, Sale> saleResult = await _saleService.upsert(
        sale: sale.value,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );

      return await saleResult.fold<Future<bool>>(
        (ServiceError error) async {
          throw error.message;
        },
        (Sale updatedSale) async {
          unawaited(
            _activityService.insertActivity(
              activity: Activity(
                activity: ActivityFlags.COMPLETED_SALE.index,
                emp_id: _identityService.currentEmployee.id,
                term_num: _identityService.terminalNumber,
                sale_num: updatedSale.sale_number,
                str_data: "Completed sale: ${Helpers.getSaleTitle(updatedSale, _saleController.saleName.value)}}",
              ),
            ),
          );
          _logger.info("sale saved successfully");
          return true;
        },
      );
    } catch (e) {
      _logger.severe("error completing sale", e);
      _notificationService.error("Error completing sale");

      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _updateIfSplitSeats() async {
    try {
      final Sale activeSale = await _saleController.currentSale.value.match(
        (Sale s) => s,
        () {
          throw "No active sale";
        },
      );

      final List<SaleRow> removeList = activeSale.document.saleRows.where((SaleRow sr) => seats.contains(sr.seatNumber)).toList();
      _saleController.removeSaleRows(removeList, activeSale.document.saleRows);
      activeSale.document.saleHeader.seatsSettled = <int>[...seats, ...activeSale.document.saleHeader.seatsSettled];
      activeSale.document.saleHeader.seatsTendered = <int>[];
      activeSale.document.saleHeader.tenders = <SaleTender>[];
      activeSale.document.saleHeader.reservedSaleNumber = null;
      _saleController.refreshCurrentSaleModels();

      final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
        sale: activeSale,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );

      upsertRes.fold(
        (ServiceError e) {
          throw e.message;
        },
        (Sale s) => _saleController.currentSale.refresh(),
      );
    } catch (e) {
      _logger.severe("error updating sale", e);
      _notificationService.error("Error updating sale");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  SaleTender _setTender(
    PaymentMediaType type,
    int amount, {
    bool changeDue = false,
    String checkNo = "",
    String memo = "",
    CardTransactionData? cardTransactionData,
    GiftTransactionData? giftTransactionData,
  }) {
    final List<int> extraSaleTenderFlags = <int>[];

    if (amount < 0 && !changeDue) {
      _logger.info("tendering refund (amount: $amount)");

      extraSaleTenderFlags.add(SaleTenderFlags.REFUNDED.index);

      if (type == PaymentMediaType.Cash || type == PaymentMediaType.Credit) {
        sale.value.document.saleHeader.saleFlags.add(SaleFlags.REFUNDED.index);
      }
    }

    if (type == PaymentMediaType.Gift) {
      if (!sale.value.document.saleHeader.saleFlags.contains(SaleFlags.GIFT_REDEMPTION.index)) {
        sale.value.document.saleHeader.saleFlags.add(SaleFlags.GIFT_REDEMPTION.index);
      }
    }

    final SaleTender newTender = SaleTender(
      amount: amount,
      media: type.index,
      cardTransactionData: cardTransactionData,
      giftTransactionData: giftTransactionData,
      saleTenderFlags: <int>[
        ...extraSaleTenderFlags,
      ],
      checkNum: checkNo,
      memo: memo,
    );

    if (seats.isNotEmpty) sale.value.document.saleHeader.tenders.add(newTender);

    _saleController.currentSale.value.match(
      (Sale s) {
        s.document.saleHeader.tenders.add(newTender);
        s.document.saleHeader.seatsTendered = <int>[...seats];
      },
      () => null,
    );

    return newTender;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<bool> _postTender(
    PaymentMediaType type,
    int amount, {
    bool popCashDrawer = false,
  }) async {
    numPadController.text.value = none();

    /// if current sale number is 0 then it's a split seat sale and needs a new sale number
    /// new number is stored in the current active sale as reservedSaleNumber
    if (sale.value.sale_number == 0) {
      final Either<ServiceError, int> saleNumberResult = await _saleService.getCurrentSaleNumber();
      saleNumberResult.fold(
        (ServiceError error) {
          _logger.severe("error getting new sale number", error);
        },
        (int newNum) {
          sale.value.sale_number = newNum;
          sale.value.document.saleHeader.saleNumber = newNum;
          _saleController.currentSale.value.match(
            (Sale s) {
              s.document.saleHeader.reservedSaleNumber = newNum;
            },
            () => null,
          );
        },
      );
    }

    // If the sale isn't over after this tender, do this
    if (balance > 0 && (!isDualPricingEnabled || dualPricingBalance > 0)) {
      if (isDualPricingEnabled) {
        _poleDisplayService.write(
          line1: "Cash Balance",
          line1Suffix: "\$${Helpers.formatCurrency(dualPricingBalance)}",
          line2Prefix: "Card Balance",
          line2Suffix: "\$${Helpers.formatCurrency(balance)}",
        );
      } else {
        _poleDisplayService.write(
          line1: type.string,
          line1Suffix: "\$${Helpers.formatCurrency(amount)}",
          line2Prefix: "Remaining",
          line2Suffix: "\$${Helpers.formatCurrency(balance)}",
        );
      }
    }

    if (popCashDrawer) {
      try {
        await _registerController.openCashDrawer(idx: sale.value.document.saleHeader.cashDrawer);
        sale.value.document.saleHeader.cashDrawer ??= merchantConfig.value.document.multiCashDrawer ? _identityService.employeeDrawer?.idx : 0;
      } catch (e) {
        _logger.severe("error opening cash drawer", e);
      }
    }

    // skip checks and complete if type is legacy gift and balance is equal to or under 0
    if (type != PaymentMediaType.LegacyGift || (balance > 0 && (!isDualPricingEnabled || dualPricingBalance > 0))) {
      // if balance (credit price) is not 0 and dual pricing
      // is not enabled then the sale is not over, return false
      if (balance != 0 && !isDualPricingEnabled) return false;

      // if balance (credit price) is not 0 and dual pricing
      // is enabled but the media type (credit, house)
      // is not eligible for dual price then
      // the sale is not over, return false
      if (balance != 0 && isDualPricingEnabled && !isCashPrice(type)) return false;

      // if dual pricing is enabled and the dual pricing balance
      // is not 0 then the sale is also not over, return false
      if (dualPricingBalance != 0 && isDualPricingEnabled && isCashPrice(type)) return false;
    }

    _saleController.deselectAll();

    final bool transactionComplete = await _completeSale();

    if (!transactionComplete) return false;

    externalTenderStatusVM.value = ExternalTenderStatusViewModel(
      status: ExternalTenderStatus.Success,
      content: "Sale completed successfully!",
    );

    await Future<void>.delayed(const Duration(seconds: 1));

    return true;
  }
}
