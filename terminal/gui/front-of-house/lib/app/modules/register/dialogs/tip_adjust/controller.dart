import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/num_pad/controller.dart';
import 'package:desktop/app/global_widgets/widget/confirmation_dialog.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';
import 'package:lottie/lottie.dart';

enum TipDialogState { SEARCH_SALE, SELECT_SALE, TIP_SALE }

class TipAdjustDialogController extends GetxController {
  TipAdjustDialogController({
    this.transaction,
  });
  final Sale? transaction;

  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();
  final Logger _logger = Logger('TipAdjustDialogController');
  final SaleService _saleService = Get.find();
  final ConfigService _configService = Get.find();
  final PaymentService _paymentService = Get.find();
  final NumPadController numPadController = Get.find();

  final Rx<Sale> focusedSale = Sale.empty().obs;
  final Rxn<SaleTender> adjustingTender = Rxn<SaleTender>();
  final RxList<Sale> searchedSales = <Sale>[].obs;
  final ScrollController searchSaleScrollController = ScrollController();
  final ScrollController tenderScrollController = ScrollController();

  RxBool processingTip = false.obs;
  RxBool loadingSales = false.obs;
  Rx<TipDialogState> tipDialogState = TipDialogState.SEARCH_SALE.obs;

  @override
  void onInit() {
    if (transaction != null) {
      numPadController.text.value = none();
      focusedSale.value = transaction!;
      tipDialogState.value = TipDialogState.TIP_SALE;
    }
    super.onInit();
  }

  @override
  void onClose() {
    numPadController.text.value = none();
    super.onClose();
  }

  void cancel() {
    Get.back();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<List<Sale>> getTippableSales() async {
    loadingSales.value = true;
    final List<Sale> returnableSales = <Sale>[];
    final int saleNumber = await numPadController.text.value.match(
      (String numpadValue) => int.parse(numpadValue.replaceAll(RegExp(r'\.|\,|\$'), '')),
      () => 0,
    );
    final Either<ServiceError, List<Sale>> loadSuspendedResult = await _saleService.getTodaysTippableSalesBySaleNumber(saleNumber);

    loadSuspendedResult.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (List<Sale> resultSales) {
        for (final Sale sale in resultSales) {
          // If the sale contains a credit tender, add it to the tippable sales
          if (sale.document.saleHeader.tenders
              .where(
                (SaleTender tender) => tender.media == PaymentMediaType.Credit.index,
              )
              .isNotEmpty) {
            returnableSales.add(sale);
          }
        }
      },
    );
    if (returnableSales.isEmpty) _notificationService.error("No tippable sales found!");
    loadingSales.value = false;
    return returnableSales;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> tipAdjust(Option<SaleTender> tender, Option<Sale> sale) async {
    try {
      if (sale.isNone()) return _notificationService.error("No sale detected!");
      final Sale currentSale = sale.getOrElse(() => Sale.empty());

      if (tender.isNone()) return _notificationService.error("No tender detected!");

      int tipAmount = 0;
      await numPadController.text.value.match(
        (String numpadString) async {
          tipAmount = int.parse(numpadString.replaceAll(RegExp(r'\.|\,|\$'), ''));
        },
        () async {
          return _notificationService.error("No tip amount detected!");
        },
      );

      final int limit = _configService.merchantConfig.document.tipConfirmLimit;

      if (limit >= 0 && tipAmount > limit) {
        final bool tipConfirm = await Get.dialog<bool?>(
              ConfirmationDialog(
                confirmColor: R2Colors.primary500,
                declineColor: R2Colors.negativeRed,
                confirmText: "Confirm",
                declineText: "Cancel",
                onConfirm: () {
                  Get.back(result: true);
                },
                onDecline: () {
                  Get.back(result: false);
                },
                title: Text(
                  limit == 0 ? "Confirm tip of \$${Helpers.formatCurrency(tipAmount)}" : "Tip amount is over \$${Helpers.formatCurrency(limit)}",
                  style: const TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
                ),
              ),
            ) ??
            false;

        if (!tipConfirm) return;
      }

      await tender.match((SaleTender t) async {
        if (PaymentMediaType.values[t.media!] == PaymentMediaType.Credit) {
          processingTip.value = true;
          if (t.cardTransactionData == null) {
            processingTip.value = false;
            return _notificationService.error("Credit tender has no transaction data!");
          }

          final String refTxnId = t.cardTransactionData!.refTxnID;
          final Either<ErrorResponse, CardTransactionData> tipAdjustResult = await _paymentService.tipAdjust(
            amount: tipAmount.abs(),
            refTxnId: refTxnId,
            settledTerminalNumber: currentSale.document.saleHeader.settleTerminalNumber ?? 98,
          );

          await tipAdjustResult.fold((ErrorResponse error) async {
            _logger.shout(error.message);
            processingTip.value = false;
            return showErrorDialog(error: error.message);
          }, (CardTransactionData success) async {
            Sale tippedSale = Sale.empty();

            tippedSale = currentSale;
            final SaleTender tempTender = t;
            tempTender.tipAmount = tipAmount;
            tempTender.tipMedia = t.media;

            if (!tempTender.saleTenderFlags.contains(SaleTenderFlags.TIP_ADJUSTED.index)) {
              tempTender.saleTenderFlags.add(SaleTenderFlags.TIP_ADJUSTED.index);
            }
            if (!tippedSale.document.saleHeader.saleFlags.contains(SaleFlags.TIP_ADJUSTED.index)) {
              tippedSale.document.saleHeader.saleFlags.add(SaleFlags.TIP_ADJUSTED.index);
            }
            final int alterIndex = tippedSale.document.saleHeader.tenders.indexWhere((SaleTender newTender) => newTender == t);
            tippedSale.document.saleHeader.tenders[alterIndex] = tempTender;

            // Upsert the sale with the new tip stuff on it
            final Either<ServiceError, Sale> saleUpsertResult = await _saleService.upsert(
              sale: tippedSale,
              employee: _identityService.currentEmployee,
              terminalNumber: _identityService.terminalNumber,
            );

            saleUpsertResult.fold(
              (ServiceError error) => _notificationService.error(error.message),
              (_) => null,
            );
            processingTip.value = false;
            if (saleUpsertResult.isLeft()) return;

            _notificationService.success("Tip Adjust Complete!");
            numPadController.text.value = none();
            adjustingTender.value = null;
          });
        } else {
          _notificationService.error("Cannot tip adjust a cash tender!");
        }
      }, () async {
        return _notificationService.error("Tender is null!");
      });
    } catch (err, stack) {
      _logger.severe(
        "Error adjusting tip",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Tries to cancel the currently running transaction
  Future<void> cancelTransaction() async {
    processingTip.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  /// Shows the error dialog.
  Future<void> showErrorDialog({String error = "Something went wrong!"}) async {
    String displayError = error;

    // Make connection error friendlier
    if (error.contains("SocketException")) {
      displayError = "Unable to connect to payment device";
    }

    await Get.dialog(
      ConfirmationDialog(
        confirmColor: R2Colors.primary500,
        declineColor: R2Colors.negativeRed,
        title: const Text(
          "Transaction Error!",
          style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
        ),
        content: Column(
          children: <Widget>[
            Lottie.asset(
              "lib/assets/lottie/failed.json",
              frameRate: FrameRate.max,
              height: 300,
            ),
            Text(
              displayError,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
          ],
        ),
        noOptions: true,
      ),
    );
  }
}
