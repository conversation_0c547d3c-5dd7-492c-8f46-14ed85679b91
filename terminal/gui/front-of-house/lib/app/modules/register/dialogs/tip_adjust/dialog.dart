import 'package:desktop/app/data/enums/num_pad_type.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/global_widgets/num_pad/widget.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/widget/bottom_sheet_dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tip_adjust/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';

class TipAdjustDialog extends StatelessWidget {
  const TipAdjustDialog({
    this.transaction,
  });
  final Sale? transaction;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TipAdjustDialogController>(
      init: TipAdjustDialogController(transaction: transaction),
      builder: (TipAdjustDialogController controller) {
        return Obx(() {
          return BottomSheetDialog(
            onCancel: controller.cancel,
            onConfirm: () async {
              switch (controller.tipDialogState.value) {
                case TipDialogState.SEARCH_SALE:
                  controller.searchedSales.value = await controller.getTippableSales();
                  if (controller.searchedSales.length == 1) {
                    controller.numPadController.text.value = none();
                    controller.focusedSale.value = controller.searchedSales.first;
                    controller.tipDialogState.value = TipDialogState.TIP_SALE;
                  } else if (controller.searchedSales.length > 1) {
                    controller.numPadController.text.value = none();
                    controller.tipDialogState.value = TipDialogState.SELECT_SALE;
                  }
                case TipDialogState.TIP_SALE:
                  await controller.tipAdjust(
                    some(controller.adjustingTender.value!),
                    some(controller.focusedSale.value),
                  );
                default:
                  break;
              }
            },
            showConfirm: controller.tipDialogState.value != TipDialogState.SELECT_SALE && !controller.processingTip.value,
            confirmText: controller.tipDialogState.value == TipDialogState.TIP_SALE ? "Adjust Tip" : "Confirm",
            cancelText: controller.tipDialogState.value == TipDialogState.TIP_SALE ? "Done" : "Cancel",
            disabled: controller.numPadController.text.value.isNone() ||
                (controller.tipDialogState.value == TipDialogState.TIP_SALE && controller.adjustingTender.value == null),
            title: "Tip Adjust",
            child: Obx(
              () => !controller.processingTip.value
                  ? Obx(() {
                      switch (controller.tipDialogState.value) {
                        case TipDialogState.SEARCH_SALE:
                          return const Column(
                            children: <Widget>[
                              NumPadWidget(
                                label: "Sale #",
                                type: NumPadType.cardNum,
                              ),
                            ],
                          );
                        case TipDialogState.SELECT_SALE:
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              if (controller.searchedSales.isEmpty)
                                if (controller.loadingSales.value)
                                  const Column(
                                    children: <Widget>[
                                      SizedBox(
                                        height: 50,
                                        width: 50,
                                        child: CircularProgressIndicator(),
                                      ),
                                    ],
                                  )
                                else
                                  const Center(
                                    child: Text("No Sales Found"),
                                  )
                              else
                                Expanded(
                                  child: Obx(
                                    () => Column(
                                      children: <Widget>[
                                        Expanded(
                                          child: ListView.builder(
                                            shrinkWrap: true,
                                            controller: controller.searchSaleScrollController,
                                            itemCount: controller.searchedSales.length,
                                            itemBuilder: (
                                              BuildContext context,
                                              int index,
                                            ) {
                                              return SaleOverviewCard(
                                                sale: controller.searchedSales[index],
                                                tipAdjustDialogController: controller,
                                                onCardTap: () {
                                                  controller.focusedSale.value = controller.searchedSales[index];
                                                  controller.tipDialogState.value = TipDialogState.TIP_SALE;
                                                },
                                              );
                                            },
                                          ),
                                        ),
                                        if (controller.loadingSales.value)
                                          const SizedBox(
                                            height: 70,
                                            width: 70,
                                            child: Padding(
                                              padding: EdgeInsets.all(20.0),
                                              child: CircularProgressIndicator(),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          );
                        case TipDialogState.TIP_SALE:
                          return Flex(
                            direction: Axis.vertical,
                            children: <Widget>[
                              Flexible(
                                child: Flex(
                                  direction: Axis.vertical,
                                  children: <Widget>[
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          controller.focusedSale.value.end_at == null
                                              ? ""
                                              : DateFormat.yMMMd('en_US').add_jm().format(
                                                    controller.focusedSale.value.end_at!.toLocal(),
                                                  ),
                                          style: const TextStyle(
                                            color: Colors.black,
                                            fontWeight: FontWeight.normal,
                                            fontSize: 15,
                                          ),
                                        ),
                                        Row(
                                          children: <Widget>[
                                            const Text(
                                              "Items: ",
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.normal,
                                                fontSize: 18,
                                              ),
                                            ),
                                            Text(
                                              controller.focusedSale.value.document.saleRows.length.toString(),
                                              style: const TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.normal,
                                                fontSize: 18,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Text(
                                          "Sale #${controller.focusedSale.value.sale_number}",
                                          style: const TextStyle(
                                            color: Colors.black,
                                            fontWeight: FontWeight.normal,
                                            fontSize: 20,
                                          ),
                                        ),
                                        Row(
                                          children: <Widget>[
                                            const Text(
                                              "Total: ",
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.normal,
                                                fontSize: 20,
                                              ),
                                            ),
                                            if (controller.focusedSale.value.document.saleHeader.saleFlags.contains(
                                              SaleFlags.DUAL_PRICING.index,
                                            ))
                                              Text(
                                                "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.cashTotal)}",
                                                style: const TextStyle(
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.normal,
                                                  fontSize: 20,
                                                ),
                                              )
                                            else
                                              Text(
                                                "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.total)}",
                                                style: const TextStyle(
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.normal,
                                                  fontSize: 20,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(),
                              Flexible(
                                flex: 9,
                                child: Flex(
                                  direction: Axis.horizontal,
                                  children: <Widget>[
                                    Flexible(
                                      flex: 2,
                                      child: Flex(
                                        direction: Axis.vertical,
                                        children: <Widget>[
                                          // Row(
                                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          //   children: [
                                          //     Text(
                                          //       controller.focusedSale.value.end_at == null
                                          //           ? ""
                                          //           : DateFormat.yMMMd('en_US').add_jm().format(controller.focusedSale.value.end_at!.toLocal()),
                                          //       style: const TextStyle(
                                          //         color: Colors.black,
                                          //         fontWeight: FontWeight.normal,
                                          //         fontSize: 15,
                                          //       ),
                                          //     ),
                                          //     Row(
                                          //       children: [
                                          //         const Text(
                                          //           "Items: ",
                                          //           style: TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 18,
                                          //           ),
                                          //         ),
                                          //         Text(
                                          //           controller.focusedSale.value.document.saleRows.length.toString(),
                                          //           style: const TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 18,
                                          //           ),
                                          //         )
                                          //       ],
                                          //     ),
                                          //   ],
                                          // ),
                                          // Row(
                                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          //   children: [
                                          //     Text(
                                          //       "Sale #${controller.focusedSale.value.sale_number}",
                                          //       style: const TextStyle(
                                          //         color: Colors.black,
                                          //         fontWeight: FontWeight.normal,
                                          //         fontSize: 20,
                                          //       ),
                                          //     ),
                                          //     Row(
                                          //       children: [
                                          //         const Text(
                                          //           "Total: ",
                                          //           style: TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 20,
                                          //           ),
                                          //         ),
                                          //         if (controller.focusedSale.value.document.saleHeader.saleFlags
                                          //             .contains(SaleFlags.DUAL_PRICING.index))
                                          //           Text(
                                          //             "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.cashDiscountTotal)}",
                                          //             style: const TextStyle(
                                          //               color: Colors.black,
                                          //               fontWeight: FontWeight.normal,
                                          //               fontSize: 20,
                                          //             ),
                                          //           )
                                          //         else
                                          //           Text(
                                          //             "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.total)}",
                                          //             style: const TextStyle(
                                          //               color: Colors.black,
                                          //               fontWeight: FontWeight.normal,
                                          //               fontSize: 20,
                                          //             ),
                                          //           ),
                                          //       ],
                                          //     ),
                                          //   ],
                                          // ),
                                          const Expanded(
                                            child: Text(
                                              "Media Tenders",
                                              style: TextStyle(fontSize: 20),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 9,
                                            child: SingleChildScrollView(
                                              child: Column(
                                                children: <Widget>[
                                                  ListView.builder(
                                                    clipBehavior: Clip.antiAlias,
                                                    shrinkWrap: true,
                                                    itemCount: controller.focusedSale.value.document.saleHeader.tenders
                                                        .where(
                                                          (SaleTender tender) => tender.media == PaymentMediaType.Credit.index,
                                                        )
                                                        .length,
                                                    itemBuilder: (
                                                      BuildContext context,
                                                      int index,
                                                    ) {
                                                      final List<SaleTender> tippableTenders =
                                                          controller.focusedSale.value.document.saleHeader.tenders
                                                              .where(
                                                                (SaleTender tender) => tender.media == PaymentMediaType.Credit.index,
                                                              )
                                                              .toList();

                                                      final String subtitle =
                                                          // ignore: prefer_interpolation_to_compose_strings
                                                          "Tip Amount:  \$${Helpers.formatCurrency(tippableTenders[index].tipAmount ?? 0)}" +
                                                              "\nTender Amount:  \$${Helpers.formatCurrency(tippableTenders[index].amount ?? 0)}" +
                                                              "\nTotal Amount:  \$${Helpers.formatCurrency((tippableTenders[index].amount ?? 0) + (tippableTenders[index].tipAmount ?? 0))}" +
                                                              "\nCard Number: *${tippableTenders[index].cardTransactionData?.cardPAN ?? "****"}";

                                                      return Card(
                                                        elevation: 0,
                                                        child: ListTile(
                                                          isThreeLine: true,
                                                          selected: controller.adjustingTender.value == tippableTenders[index],
                                                          selectedTileColor: R2Colors.primary100,
                                                          shape: RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.circular(
                                                              10,
                                                            ),
                                                            side: BorderSide(
                                                              color: controller.adjustingTender.value == tippableTenders[index]
                                                                  ? R2Colors.primary500
                                                                  : R2Colors.neutral500,
                                                              width: 1.5,
                                                            ),
                                                          ),
                                                          subtitle: Text(
                                                            subtitle,
                                                            style: TextStyle(
                                                              color: controller.adjustingTender.value == tippableTenders[index]
                                                                  ? R2Colors.primary500
                                                                  : R2Colors.neutral700,
                                                            ),
                                                          ),
                                                          onTap: () {
                                                            controller.adjustingTender.value = tippableTenders[index];
                                                            controller.numPadController.text.value = some(
                                                              "\$${Helpers.formatCurrency(controller.adjustingTender.value?.tipAmount ?? 0)}",
                                                            );
                                                          },
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const VerticalDivider(),
                                    Flexible(
                                      flex: 3,
                                      child: Flex(
                                        direction: Axis.vertical,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: <Widget>[
                                          // Row(
                                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          //   children: [
                                          //     Text(
                                          //       controller.focusedSale.value.end_at == null
                                          //           ? ""
                                          //           : DateFormat.yMMMd('en_US').add_jm().format(controller.focusedSale.value.end_at!.toLocal()),
                                          //       style: const TextStyle(
                                          //         color: Colors.black,
                                          //         fontWeight: FontWeight.normal,
                                          //         fontSize: 15,
                                          //       ),
                                          //     ),
                                          //     Row(
                                          //       children: [
                                          //         const Text(
                                          //           "Items: ",
                                          //           style: TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 18,
                                          //           ),
                                          //         ),
                                          //         Text(
                                          //           controller.focusedSale.value.document.saleRows.length.toString(),
                                          //           style: const TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 18,
                                          //           ),
                                          //         )
                                          //       ],
                                          //     ),
                                          //   ],
                                          // ),
                                          // Row(
                                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          //   children: [
                                          //     Text(
                                          //       "Sale #${controller.focusedSale.value.sale_number}",
                                          //       style: const TextStyle(
                                          //         color: Colors.black,
                                          //         fontWeight: FontWeight.normal,
                                          //         fontSize: 20,
                                          //       ),
                                          //     ),
                                          //     Row(
                                          //       children: [
                                          //         const Text(
                                          //           "Total: ",
                                          //           style: TextStyle(
                                          //             color: Colors.black,
                                          //             fontWeight: FontWeight.normal,
                                          //             fontSize: 20,
                                          //           ),
                                          //         ),
                                          //         if (controller.focusedSale.value.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index))
                                          //           Text(
                                          //             "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.cashDiscountTotal)}",
                                          //             style: const TextStyle(
                                          //               color: Colors.black,
                                          //               fontWeight: FontWeight.normal,
                                          //               fontSize: 20,
                                          //             ),
                                          //           )
                                          //         else
                                          //           Text(
                                          //             "\$${Helpers.formatCurrency(controller.focusedSale.value.document.saleHeader.total)}",
                                          //             style: const TextStyle(
                                          //               color: Colors.black,
                                          //               fontWeight: FontWeight.normal,
                                          //               fontSize: 20,
                                          //             ),
                                          //           ),
                                          //       ],
                                          //     ),
                                          //   ],
                                          // ),
                                          // Expanded(
                                          //   child: Column(
                                          //     children: [
                                          //       DropdownButtonFormField<SaleTender>(
                                          //         value: controller.adjustingTender.value,
                                          //         dropdownColor: R2Colors.neutral200,
                                          //         elevation: 0,
                                          //         hint: const Text("Select Tender"),
                                          //         focusColor: Colors.transparent,
                                          //         icon: FaIcon(
                                          //           FontAwesomeIcons.chevronDown,
                                          //           size: 16,
                                          //           color: R2Colors.neutral400,
                                          //         ),
                                          //         isExpanded: true,
                                          //         decoration: InputDecoration(
                                          //           enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: R2Colors.primary500)),
                                          //           focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: R2Colors.primary500)),
                                          //           isDense: true,
                                          //           labelText: "Tender",
                                          //           labelStyle: TextStyle(color: R2Colors.primary500),
                                          //         ),
                                          //         style: TextStyle(
                                          //           color: R2Colors.neutral700,
                                          //           fontSize: 16,
                                          //         ),
                                          //         onChanged: (SaleTender? newValue) {
                                          //           controller.adjustingTender.value = newValue;
                                          //         },
                                          //         items: controller.focusedSale.value.document.saleHeader.tenders
                                          //             .where(
                                          //           (SaleTender tender) => tender.media == MediaType.CREDIT.index,
                                          //         )
                                          //             .map((SaleTender saleTender) {
                                          //           return DropdownMenuItem<SaleTender>(
                                          //             value: saleTender,
                                          //             child: Row(
                                          //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          //               children: [
                                          //                 Row(
                                          //                   children: [
                                          //                     Text(
                                          //                       controller.mediaTypeAsString(MediaType.values[saleTender.media!]),
                                          //                       style: const TextStyle(
                                          //                         color: Colors.black,
                                          //                         fontWeight: FontWeight.normal,
                                          //                         fontSize: 20,
                                          //                       ),
                                          //                     ),
                                          //                     if (saleTender.bogusAccountNum != null)
                                          //                       Text(
                                          //                         " (****${saleTender.bogusAccountNum})",
                                          //                         style: const TextStyle(
                                          //                           color: Colors.black,
                                          //                           fontWeight: FontWeight.normal,
                                          //                           fontSize: 15,
                                          //                         ),
                                          //                       ),
                                          //                   ],
                                          //                 ),
                                          //                 Text(
                                          //                   "\$${Helpers.formatCurrency(saleTender.amount!)}",
                                          //                   style: const TextStyle(
                                          //                     color: Colors.black,
                                          //                     fontWeight: FontWeight.normal,
                                          //                     fontSize: 20,
                                          //                   ),
                                          //                 ),
                                          //               ],
                                          //             ),
                                          //           );
                                          //         }).toList(),
                                          //       ),
                                          //     ],
                                          //   ),
                                          // ),
                                          // const Divider(),
                                          NumPadWidget(
                                            label: "",
                                            disabled: controller.adjustingTender.value == null,
                                            type: NumPadType.currency,
                                            buttonHeight: 75,
                                            buttonWidth: 130,
                                            allowZeroCurrency: true,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        default:
                          return Container();
                      }
                    })
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        const Text(
                          "Processing Tip Adjust",
                          style: TextStyle(
                            fontSize: 15,
                          ),
                        ),
                        Lottie.asset(
                          "lib/assets/lottie/loading-animation.json",
                          frameRate: FrameRate.max,
                          height: 300,
                        ),
                      ],
                    ),
            ),
          );
        });
      },
    );
  }
}

class SaleOverviewCard extends StatelessWidget {
  const SaleOverviewCard({
    required this.sale,
    required this.tipAdjustDialogController,
    required this.onCardTap,
  });
  final Sale sale;
  final TipAdjustDialogController tipAdjustDialogController;
  final Function onCardTap;

  @override
  Widget build(BuildContext context) {
    // Spin up an empty string buffer
    final StringBuffer tenderStringBuffer = StringBuffer();
    final List<String> tenderStrings = <String>[];
    final StringBuffer customerStringBuffer = StringBuffer();
    final List<String> customerStrings = <String>[];

    // Loop through tenders and convert media types to strings in an array, also get customers
    // for (final SaleTender tender in sale.document.saleHeader.tenders) {
    // if (!tenderStrings.contains(
    //   tipAdjustDialogController.mediaTypeAsString(MediaType.values[tender.media!]),
    // )) {
    //   tenderStrings.add(
    //     tipAdjustDialogController.mediaTypeAsString(MediaType.values[tender.media!]),
    //   );
    // }
    // if (tender.fullName != null) {
    //   if (!customerStrings.contains(tender.fullName!.replaceAll(" ", ""))) {
    //     customerStrings.add(tender.fullName!.replaceAll(" ", ""));
    //   }
    // }
    // }

    String totalString = Helpers.formatCurrency(sale.document.saleHeader.total);

    if (sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
      totalString = Helpers.formatCurrency(sale.document.saleHeader.cashTotal);
    }

    // Write array contents to string buffer seperated by a comma
    tenderStringBuffer.writeAll(tenderStrings, ", ");
    customerStringBuffer.writeAll(customerStrings, ",");

    return RadiusListener(
      onTap: onCardTap,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          side: BorderSide(color: R2Colors.neutral500),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 8, 8, 0),
              child: Column(
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            "#${sale.sale_number}",
                            style: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                              fontSize: 24,
                            ),
                          ),
                          if (sale.document.saleHeader.settleTerminalNumber != null)
                            Text(
                              "Term. ${sale.document.saleHeader.settleTerminalNumber}",
                              style: const TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.normal,
                                fontSize: 13,
                              ),
                            ),
                        ],
                      ),
                      Text(
                        tenderStringBuffer.toString(),
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.normal,
                          fontSize: 17,
                        ),
                      ),
                      Text(
                        "\$$totalString",
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.normal,
                          fontSize: 20,
                        ),
                      ),
                      Text(
                        DateFormat.yMMMd('en_US').add_jm().format(sale.end_at!.toLocal()),
                        style: TextStyle(
                          color: R2Colors.neutral500,
                          fontWeight: FontWeight.normal,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                  if (customerStringBuffer.toString() != "")
                    Text(
                      customerStringBuffer.toString(),
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.normal,
                        fontSize: 13,
                      ),
                    ),
                ],
              ),
            ),
            const Divider(),
            for (int i = 0; i < sale.document.saleRows.length; i++)
              if (i < 3)
                Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Row(
                    children: <Widget>[
                      const Spacer(),
                      Expanded(
                        child: Text(
                          sale.document.saleRows[i].qty.toString(),
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                            fontSize: 17,
                          ),
                        ),
                      ),
                      const Spacer(),
                      //TODO: Clip the receipt description over a certain length
                      //to keep everything in one line.
                      Expanded(
                        flex: 13,
                        child: Text(
                          sale.document.saleRows[i].receiptDescription,
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                            fontSize: 17,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Expanded(
                        flex: 4,
                        child: Text(
                          "\$${Helpers.formatCurrency(sale.document.saleRows[i].actualPrice)}",
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                            fontSize: 17,
                          ),
                        ),
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
            if (sale.document.saleRows.length > 3)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  "${sale.document.saleRows.length - 3} more item${(sale.document.saleRows.length - 3) > 1 ? "s" : ""}",
                  style: TextStyle(
                    color: R2Colors.neutral500,
                    fontWeight: FontWeight.normal,
                    fontSize: 17,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
