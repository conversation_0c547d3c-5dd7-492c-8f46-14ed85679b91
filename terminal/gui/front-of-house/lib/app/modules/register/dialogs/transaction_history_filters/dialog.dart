import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/transaction_history/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

const double rowGapSize = 8;

class TransactionHistoryFilters extends GetView<TransactionHistoryController> {
  TransactionHistoryFilters({
    required this.dateRangeController,
    required this.refundSaleNumController,
    required this.refundLaneNumController,
    required this.refundCardNumController,
    required this.refundCustomerNameController,
    required this.refundCustomerName2Controller,
    required this.refundCustomerPhoneController,
    required this.refundCustomerAddressController,
    required this.refundCustomerCompanyController,
  });
  final Rx<DateRangePickerController> dateRangeController;
  final Rx<TextEditingController> refundSaleNumController;
  final Rx<TextEditingController> refundLaneNumController;
  final Rx<TextEditingController> refundCardNumController;
  final Rx<TextEditingController> refundCustomerNameController;
  final Rx<TextEditingController> refundCustomerName2Controller;
  final Rx<TextEditingController> refundCustomerPhoneController;
  final Rx<TextEditingController> refundCustomerAddressController;
  final Rx<TextEditingController> refundCustomerCompanyController;

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.filteredSalesIsFetching.value
          ? AlertDialog(
              title: const Text(
                "Modify Filters",
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 22),
              ),
              content: SizedBox(
                height: Get.height * 0.3,
                width: Get.width * 0.3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SizedBox(
                        height: 100,
                        width: 100,
                        child: CircularProgressIndicator(
                          color: R2Colors.primary500,
                        ),
                      ),
                    ),
                    const Text(
                      "Applying filters...",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 22,
                      ),
                    ),
                  ],
                ),
              ),
            )
          : AlertDialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 100),
              title: Row(
                children: <Widget>[
                  Expanded(
                    flex: 7,
                    child: TextButton(
                      onPressed: () {
                        final DateTime now = DateTime.now();
                        controller.refundStartDate.value = DateTime(
                          now.subtract(const Duration(days: 4)).year,
                          now.subtract(const Duration(days: 4)).month,
                          now.subtract(const Duration(days: 4)).day,
                        );
                        controller.refundEndDate.value = now.add(const Duration(days: 1));
                        dateRangeController.refresh();
                        refundSaleNumController.value.text = "";
                        refundSaleNumController.refresh();
                        refundLaneNumController.value.text = "";
                        refundLaneNumController.refresh();
                        refundCardNumController.value.text = "";
                        refundCardNumController.refresh();
                        refundCustomerNameController.value.text = "";
                        refundCustomerNameController.refresh();
                        refundCustomerName2Controller.value.text = "";
                        refundCustomerName2Controller.refresh();
                        dateRangeController.value.selectedRange = PickerDateRange(
                          controller.refundStartDate.value,
                          controller.refundEndDate.value.subtract(const Duration(days: 1)),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          "Clear All",
                          style: TextStyle(
                            fontSize: 18,
                            color: R2Colors.negativeRed,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Spacer(flex: 5),
                  const Expanded(
                    flex: 10,
                    child: Text(
                      "Modify Filters",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 22,
                      ),
                    ),
                  ),
                  const Spacer(flex: 10),
                ],
              ),
              content: SizedBox(
                height: Get.height * 0.6,
                width: Get.width * 0.5,
                child: Column(
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: R2Colors.neutral300,
                            width: 2,
                          ),
                        ),
                        clipBehavior: Clip.hardEdge,
                        child: Scrollbar(
                          thumbVisibility: true,
                          controller: _scrollController,
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              child: Column(
                                children: <Widget>[
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    children: <Widget>[
                                      const Text(
                                        "Only Show Online Orders:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.all(10),
                                          child: Checkbox(
                                              value: controller.onlineSalesFilter.value,
                                              onChanged: (bool? value) => controller.onlineSalesFilter.toggle()),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Date:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  TextButton(
                                                    style: ButtonStyle(
                                                      backgroundColor: MaterialStateProperty.all(
                                                        Colors.transparent,
                                                      ),
                                                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                        const RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.only(
                                                            topLeft: Radius.circular(
                                                              8.0,
                                                            ),
                                                            bottomLeft: Radius.circular(
                                                              8.0,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    child: Row(
                                                      children: <Widget>[
                                                        Padding(
                                                          padding: const EdgeInsets.all(
                                                            13.0,
                                                          ),
                                                          child: Text(
                                                            "${DateFormat.yMd().format(dateRangeController.value.selectedRange!.startDate!)} - ${DateFormat.yMd().format(dateRangeController.value.selectedRange!.endDate ?? dateRangeController.value.selectedRange!.startDate!)}",
                                                            style: TextStyle(
                                                              fontSize: 20,
                                                              color: R2Colors.primary500,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    onPressed: () {
                                                      final DateTime? holdStart = dateRangeController.value.selectedRange?.startDate;
                                                      final DateTime? holdEnd = dateRangeController.value.selectedRange?.endDate;

                                                      Get.dialog(
                                                        AlertDialog(
                                                          title: const Text(
                                                            "Select date range",
                                                          ),
                                                          content: SizedBox(
                                                            width: Get.width * 0.7,
                                                            height: Get.height * 0.7,
                                                            child: Column(
                                                              children: <Widget>[
                                                                Expanded(
                                                                  flex: 6,
                                                                  child: SfDateRangePicker(
                                                                    backgroundColor: R2Colors.white,
                                                                    headerStyle: DateRangePickerHeaderStyle(
                                                                      backgroundColor: R2Colors.white,
                                                                    ),
                                                                    selectionColor: R2Colors.primary500,
                                                                    rangeSelectionColor: R2Colors.primary300,
                                                                    startRangeSelectionColor: R2Colors.primary500,
                                                                    endRangeSelectionColor: R2Colors.primary500,
                                                                    todayHighlightColor: R2Colors.primary500,
                                                                    controller: dateRangeController.value,
                                                                    selectionMode: DateRangePickerSelectionMode.range,
                                                                    onSelectionChanged: (
                                                                      DateRangePickerSelectionChangedArgs value,
                                                                    ) {
                                                                      dateRangeController.value.selectedRange = value.value as PickerDateRange;
                                                                    },
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  child: Row(
                                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                    children: <Widget>[
                                                                      DialogButton(
                                                                        buttonType: EDialogButtonType.CANCEL,
                                                                        onTapped: () {
                                                                          dateRangeController.value.selectedRange = PickerDateRange(
                                                                            holdStart,
                                                                            holdEnd,
                                                                          );
                                                                          Get.back();
                                                                        },
                                                                      ),
                                                                      DialogButton(
                                                                        buttonType: EDialogButtonType.CONFIRM,
                                                                        onTapped: () {
                                                                          Get.back();
                                                                          dateRangeController.refresh();
                                                                        },
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Sale Num:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundSaleNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 100,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundSaleNumController.value,
                                                      onConfirm: (_) => refundSaleNumController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundSaleNumController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundSaleNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundSaleNumController.value.text = "";
                                                      refundSaleNumController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color:
                                                                refundSaleNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Terminal Num:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundLaneNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 100,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundLaneNumController.value,
                                                      onConfirm: (_) => refundLaneNumController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundLaneNumController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundLaneNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundLaneNumController.value.text = "";
                                                      refundLaneNumController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color:
                                                                refundLaneNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Card Num (last 4):",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCardNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 100,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCardNumController.value,
                                                      onConfirm: (_) => refundCardNumController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCardNumController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCardNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCardNumController.value.text = "";
                                                      refundCardNumController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color:
                                                                refundCardNumController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Customer First Name:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCustomerNameController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 205,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCustomerNameController.value,
                                                      onConfirm: (_) => refundCustomerNameController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCustomerNameController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCustomerNameController.value.text == ""
                                                              ? R2Colors.neutral400
                                                              : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCustomerNameController.value.text = "";
                                                      refundCustomerNameController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color: refundCustomerNameController.value.text == ""
                                                                ? R2Colors.neutral400
                                                                : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Customer Last Name:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCustomerName2Controller.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 205,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCustomerName2Controller.value,
                                                      onConfirm: (_) => refundCustomerName2Controller.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCustomerName2Controller.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCustomerName2Controller.value.text == ""
                                                              ? R2Colors.neutral400
                                                              : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCustomerName2Controller.value.text = "";
                                                      refundCustomerName2Controller.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color: refundCustomerName2Controller.value.text == ""
                                                                ? R2Colors.neutral400
                                                                : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Customer Phone:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCustomerPhoneController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 205,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCustomerPhoneController.value,
                                                      onConfirm: (_) => refundCustomerPhoneController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCustomerPhoneController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCustomerPhoneController.value.text == ""
                                                              ? R2Colors.neutral400
                                                              : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCustomerPhoneController.value.text = "";
                                                      refundCustomerPhoneController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color: refundCustomerPhoneController.value.text == ""
                                                                ? R2Colors.neutral400
                                                                : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Customer Address:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCustomerAddressController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 205,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCustomerAddressController.value,
                                                      onConfirm: (_) => refundCustomerAddressController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCustomerAddressController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCustomerAddressController.value.text == ""
                                                              ? R2Colors.neutral400
                                                              : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCustomerAddressController.value.text = "";
                                                      refundCustomerAddressController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color: refundCustomerAddressController.value.text == ""
                                                                ? R2Colors.neutral400
                                                                : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      const Text(
                                        "Customer Company:",
                                        style: TextStyle(fontSize: 20),
                                      ),
                                      Row(
                                        children: <Widget>[
                                          Obx(
                                            () => DecoratedBox(
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(8.0),
                                                ),
                                                border: Border.all(
                                                  width: 2,
                                                  color: refundCustomerCompanyController.value.text == "" ? R2Colors.neutral400 : R2Colors.primary500,
                                                ),
                                              ),
                                              child: Row(
                                                children: <Widget>[
                                                  SizedBox(
                                                    width: 205,
                                                    child: VirtualKeyboardWrapper(
                                                      isBOH: false,
                                                      textEditingController: refundCustomerCompanyController.value,
                                                      onConfirm: (_) => refundCustomerCompanyController.refresh(),
                                                      child: TextField(
                                                        readOnly: true,
                                                        textAlign: TextAlign.center,
                                                        controller: refundCustomerCompanyController.value,
                                                        style: TextStyle(
                                                          fontSize: 20,
                                                          color: refundCustomerCompanyController.value.text == ""
                                                              ? R2Colors.neutral400
                                                              : R2Colors.primary500,
                                                        ),
                                                        decoration: const InputDecoration(
                                                          border: InputBorder.none,
                                                          hintText: 'Any',
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  RadiusListener(
                                                    onTap: () {
                                                      refundCustomerCompanyController.value.text = "";
                                                      refundCustomerCompanyController.refresh();
                                                    },
                                                    child: TextButton(
                                                      style: ButtonStyle(
                                                        backgroundColor: MaterialStateProperty.all(
                                                          Colors.transparent,
                                                        ),
                                                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                                                          const RoundedRectangleBorder(
                                                            borderRadius: BorderRadius.only(
                                                              topRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                              bottomRight: Radius.circular(
                                                                8.0,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      onPressed: () {},
                                                      child: Padding(
                                                        padding: const EdgeInsets.all(
                                                          15.0,
                                                        ),
                                                        child: Obx(
                                                          () => FaIcon(
                                                            FontAwesomeIcons.solidXmarkLarge,
                                                            color: refundCustomerCompanyController.value.text == ""
                                                                ? R2Colors.neutral400
                                                                : R2Colors.primary500,
                                                            size: 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: rowGapSize,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 32.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          DialogButton(
                            buttonType: EDialogButtonType.CANCEL,
                            onTapped: () {
                              Get.back();
                            },
                          ),
                          DialogButton(
                            buttonType: EDialogButtonType.CONFIRM,
                            onTapped: () async {
                              if (dateRangeController.value.selectedRange?.startDate != null) {
                                controller.refundStartDate.value = dateRangeController.value.selectedRange!.startDate!;
                                controller.refundEndDate.value = dateRangeController.value.selectedRange?.endDate == null
                                    ? controller.refundStartDate.value.add(const Duration(days: 1))
                                    : dateRangeController.value.selectedRange!.endDate!.add(const Duration(days: 1));
                              }

                              if (refundSaleNumController.value.text != "") {
                                controller.refundSaleNumber.value = some(refundSaleNumController.value.text);
                              } else {
                                controller.refundSaleNumber.value = none();
                              }

                              if (refundLaneNumController.value.text != "") {
                                controller.refundLaneNumber.value = some(refundLaneNumController.value.text);
                              } else {
                                controller.refundLaneNumber.value = none();
                              }

                              if (refundCardNumController.value.text != "") {
                                controller.refundCardNumber.value = some(refundCardNumController.value.text);
                              } else {
                                controller.refundCardNumber.value = none();
                              }

                              if (refundCustomerNameController.value.text != "") {
                                controller.refundCustomerFirstName.value = some(
                                  refundCustomerNameController.value.text,
                                );
                              } else {
                                controller.refundCustomerFirstName.value = none();
                              }

                              if (refundCustomerName2Controller.value.text != "") {
                                controller.refundCustomerLastName.value = some(
                                  refundCustomerName2Controller.value.text,
                                );
                              } else {
                                controller.refundCustomerLastName.value = none();
                              }

                              if (refundCustomerPhoneController.value.text != "") {
                                controller.refundCustomerPhone.value = some(
                                  refundCustomerPhoneController.value.text,
                                );
                              } else {
                                controller.refundCustomerPhone.value = none();
                              }

                              if (refundCustomerAddressController.value.text != "") {
                                controller.refundCustomerAddress.value = some(
                                  refundCustomerAddressController.value.text,
                                );
                              } else {
                                controller.refundCustomerAddress.value = none();
                              }

                              if (refundCustomerCompanyController.value.text != "") {
                                controller.refundCustomerCompany.value = some(
                                  refundCustomerCompanyController.value.text,
                                );
                              } else {
                                controller.refundCustomerCompany.value = none();
                              }

                              Get.back();
                              controller.resetSales();
                              await controller.loadSales();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
