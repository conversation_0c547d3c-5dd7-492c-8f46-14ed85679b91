// ignore_for_file: prefer_interpolation_to_compose_strings, always_specify_types

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/num_pad_type.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/item.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/global_widgets/num_pad/controller.dart';
import 'package:desktop/app/global_widgets/num_pad/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/global_widgets/widget/thin_bottom_sheet.dart';
import 'package:desktop/app/modules/register/dialogs/modifiers/controller.dart';
import 'package:desktop/app/modules/register/dialogs/modifiers/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/scale_weight/dialog.dart';
import 'package:desktop/app/modules/register/widgets/departments/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('ItemsController');

const String notModClause = '{document: {_cast: {String: {_nlike: "%isModifier_: true%"}}}}';

const String GET_ITEMS_BY_DEPARTMENT_QUERY = '''
  query GET_ITEMS_BY_DEPARTMENT(\$department: uuid!, \$limit: Int, \$offset: Int) {
    register_get_items_by_department(args: {param: \$department}, limit: \$limit, offset: \$offset, where: $notModClause) {
      department
      long_desc
      item
      upc
      document
      created_at
      created_by
      updated_at
      updated_by
      liq_ctl_plu
      departmentByDepartment {
        created_at
        created_by
        department
        department_order
        document
        title
        updated_at
        updated_by
      }
    }
  }
''';

const String GET_ITEMS_BY_MAJOR_GROUP_QUERY = '''
  query GET_ITEMS_BY_MAJOR_GROUP(\$majorGroup: String, \$limit: Int, \$offset: Int) {   
    get_items_by_major_group(args: {param:\$majorGroup}, offset:\$offset, limit: \$limit, where: $notModClause) {
      department
      long_desc
      item
      upc
      document
      created_at
      created_by
      updated_at
      updated_by
      liq_ctl_plu
      departmentByDepartment {
        created_at
        created_by
        department
        department_order
        document
        title
        updated_at
        updated_by
      }
    }
  }
''';

const String GET_ITEMS_SEARCHABLE_QUERY = '''
    query GET_ITEMS_SEARCHABLE(\$search: String, \$limit: Int, \$offset: Int) {
    item(where: {_and: [$notModClause, {_or: [{long_desc: {_ilike: \$search}}, {upc: {_ilike: \$search}}]}]} order_by: {departmentByDepartment: {title: asc}}, limit: \$limit, offset: \$offset) {
      department
      long_desc
      item
      upc
      document
      created_at
      created_by
      updated_at
      updated_by
      liq_ctl_plu
      departmentByDepartment {
        created_at
        created_by
        department
        department_order
        document
        title
        updated_at
        updated_by
      }
    }
  }
''';

const int PAGE_SIZE = 48;

class ItemsController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final ItemService _itemService = Get.find();
  final NotificationService _notificationService = Get.find();
  final ConfigService _configService = Get.find();

  final DepartmentsController _departmentsController = Get.find();
  final SaleController _saleController = Get.find();
  final NumPadController _numpadController = Get.find();

  final TextEditingController searchItemsTextController = TextEditingController();

  final RxString searchTerm = "".obs;
  final RxBool isLoading = true.obs;
  RxString errorString = "".obs;
  bool isFetching = false;
  bool isLastPage = false;
  int currentPage = 0;

  List<String> currentDepartments = <String>[];

  RxMap<String, List<Item>> allItemsGrouped = <String, List<Item>>{}.obs;
  final ScrollController itemsScrollController = ScrollController();

  double pointerCoordX = 0.0;
  double pointerCoordY = 0.0;

  final RxString currentTime = Constants.clockFormat.format(DateTime.now()).obs;
  late Timer clockTimer;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    ever(_departmentsController.selectedDepartmentGroupItem, (_) async {
      if (searchTerm.isNotEmpty) isFetching = false;
      searchTerm.value = "";
      searchItemsTextController.text = "";
      resetItems();
      if (!isFetching) await _fetchPage(0);
    });

    itemsScrollController.addListener(() async {
      if ((itemsScrollController.position.pixels > (itemsScrollController.position.maxScrollExtent * .8)) &&
          !isFetching &&
          !isLoading.value) {
        await _fetchPage(currentPage);
      }
    });

    ever(searchTerm, (_) async {
      resetItems();
      if (!isFetching) await _fetchPage(0);
    });

    currentTime.value = Constants.clockFormat.format(DateTime.now());
    clockTimer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      currentTime.value = Constants.clockFormat.format(DateTime.now());
    });

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  void onClose() {
    clockTimer.cancel();

    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void resetItems() {
    allItemsGrouped.value = {};
    currentDepartments = [];
    currentPage = 0;
    isLastPage = false;
    errorString.value = "";
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _fetchPage(int pageNum) async {
    try {
      if (pageNum == 0) resetItems();
      if (isLastPage) return;
      isFetching = true;
      // ignore: avoid_print
      print("FETCHING PAGE. OFFSET IS ${pageNum * PAGE_SIZE}");

      String queryToRun = "";
      Map<String, dynamic> variables = {};
      String resultKey = "";

      if (searchTerm.isNotEmpty) {
        queryToRun = GET_ITEMS_SEARCHABLE_QUERY;
        variables = {
          "search": "${int.tryParse(searchTerm.value) == null ? "%" : ""}${searchTerm.value}%",
        };
        resultKey = "item";
      } else {
        if (_departmentsController.selectedDepartmentGroupItem.value.department != null) {
          queryToRun = GET_ITEMS_BY_DEPARTMENT_QUERY;
          variables = {
            "department": _departmentsController.selectedDepartmentGroupItem.value.department!.department,
          };
          resultKey = "register_get_items_by_department";
        } else {
          queryToRun = GET_ITEMS_BY_MAJOR_GROUP_QUERY;
          variables = {
            "majorGroup": _departmentsController.selectedDepartmentGroupItem.value.title,
          };
          resultKey = "get_items_by_major_group";
        }
      }

      variables = {
        ...variables,
        "offset": pageNum * PAGE_SIZE,
        "limit": PAGE_SIZE,
      };
      isLoading.value = true;
      final QueryResult<Object?> result = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(
            queryToRun,
          ),
          variables: variables,
        ),
      );

      List<Item> items = <Item>[];

      if (result.hasException) {
        _logger.severe(
          result.exception.toString(),
        );
        _notificationService.error("Error getting items");
      } else {
        items = (result.data![resultKey] as List).map((item) => Item.fromJson(item as Map<String, dynamic>)).toList();
      }

      final Map<String, List<Item>> incomingItems = groupBy(
        items,
        (Item item) => item.departmentByDepartment!.department,
      );

      if (items.length < PAGE_SIZE) isLastPage = true;

      for (final String key in incomingItems.keys) {
        if (currentDepartments.contains(key)) {
          allItemsGrouped[key]!.addAll(incomingItems[key]!);
          allItemsGrouped.refresh();
        } else {
          allItemsGrouped[key] = incomingItems[key]!;
          currentDepartments.add(key);
          allItemsGrouped.refresh();
        }
      }

      currentPage++;
      isLoading.value = false;
      isFetching = false;
    } catch (err, stack) {
      _logger.severe(
        "Error getting items",
        err,
        stack,
      );
      _notificationService.error("Error getting items");
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// if "isDialog" is true, this function will return (in a Get.back) a SaleRow object
  /// as opposed to add it to sale (therefore no changes to be saved in this instance)
  Future<void> handleAddItem(Item incomingItem, {bool isDialog = false}) async {
    final Item item = incomingItem;

    // Check if the department is restricted
    final department = item.departmentByDepartment;
    if (department != null) {
      final bool isWithinTimeWindow = department.document.isWithinTimeWindow();

      if (!isWithinTimeWindow && department.document.restrictEnabled) {
        // Show a notification when the department is not available
        _notificationService.error(
          "This department is currently not available.",
        );

        // Prevent adding the item
        return;
      }
    }

    final int qty = _saleController.globalQuantity.value;
    bool liquorControlled = item.liq_ctl_plu != null || item.document.liquorList.isNotEmpty;
    int loopAmt = liquorControlled ? qty : 1;

    if (item.document.count == 0) return _notificationService.error("Item count too low for ${item.long_desc}");
    try {
      // Check if promptForPrice flag and open a price modifier dialog
      if (item.document.promptForPrice) {
        final int? setPrice = await _openSetPriceDialog();
        if (setPrice == null) return;
        final MerchantJsonRecordDocument doc = _configService.merchantConfig.document;
        item.document.pricing["S0L0C0"] = doc.dualPricing
            ? Helpers.getCreditPrice(
                cashAmount: setPrice,
                creditAmount: 0,
                dualPercent: doc.dualPricingPercent,
                roundAmount: 1,
              )
            : setPrice;
        item.document.pricing["S0L0C1"] = setPrice;
      }

      if (item.document.negativeItem) {
        for (final String key in item.document.pricing.keys) {
          if ((item.document.pricing[key] ?? 0) > 0) {
            item.document.pricing[key] = item.document.pricing[key]! * -1;
          }
        }
      }

      if (item.document.isWeighted) {
        final SaleRow? result = await Get.bottomSheet<SaleRow>(
          ThinBottomSheet(
            child: ScaleWeightDialog(item: item),
          ),
          isScrollControlled: true,
          ignoreSafeArea: true,
          backgroundColor: Colors.transparent,
        );
        if (result != null) {
          result.qty = qty;

          /// Used in the instance this is a dialog and we need to return the result as opossed to adding it to the cart
          if (isDialog) {
            Get.back(result: <SaleRow>[result]);
            return;
          }

          if (liquorControlled) result.qty = 1;

          for (int i = 0; i < loopAmt; i++) {
            await _saleController.addSaleRowToCart(
              saleRow: Helpers.makeRowCopy(row: result, newIdx: result.index, qty: liquorControlled ? 1 : result.qty),
              liquorList: item.document.liquorList,
              liquorPLU: item.liq_ctl_plu,
            );
          }
        }
      } else {
        if (incomingItem.document.modifiers.isNotEmpty) {
          int cartLength = 0;

          Sale? newSale;
          bool activeSale = false;

          await _saleController.currentSale.value.match(
            (Sale sale) {
              cartLength = sale.document.saleRows.length;
              activeSale = true;
            },
            () async {
              cartLength = 0;
              newSale = await _saleController.makeNewSale();
            },
          );

          if (!activeSale && newSale == null) return;

          final List<ModObject> itemWithModList = await _handleItemMods(incomingItem);

          if (itemWithModList.isEmpty) return;

          if (itemWithModList.length == 1) {
            await _handleSaleRowWithNoMods(
              item: item,
              newSale: newSale,
              isDialog: isDialog,
            );
            return;
          }

          if (itemWithModList.firstWhereOrNull(
                  (ModObject o) => o.item.liq_ctl_plu != null || o.item.document.liquorList.isNotEmpty) !=
              null) {
            liquorControlled = true;
            loopAmt = qty;
          }

          for (int i = 0; i < loopAmt; i++) {
            final List<SaleRow> newRows = <SaleRow>[];

            for (final ModObject element in itemWithModList) {
              Map<String, int> pricing = element.item.document.pricing;
              bool? forceVisible;

              if (element.parentIdx >= 0) {
                final ModObject parent = itemWithModList[element.parentIdx];
                if (parent.item.document.overridePricing) {
                  pricing =
                      (parent.item.document.modifiers[element.item.item] ?? ModData(pricing: <String, int>{}, idx: 0))
                              .pricing
                              .isEmpty
                          ? parent.item.document.defModPricing
                          : parent.item.document.modifiers[element.item.item]!.pricing;
                }
                forceVisible = parent.item.document.modifiers[element.item.item]?.forceVisible;
                if (parent.item.document.passDesc) {
                  element.item.document.receiptDesc =
                      "${(parent.item.document.modifierDesc ?? "") == "" ? parent.item.document.receiptDesc ?? parent.item.long_desc : parent.item.document.modifierDesc} - ${element.item.document.receiptDesc ?? element.item.long_desc}";
                }
              }

              if (item.document.negativeItem) {
                for (final String key in pricing.keys) {
                  if ((pricing[key] ?? 0) > 0) {
                    pricing[key] = pricing[key]! * -1;
                  }
                }
              }

              newRows.add(
                await _saleController.modObjToSaleRow(
                  obj: element,
                  parentIdx: isDialog || element.parentIdx == -1 ? element.parentIdx : element.parentIdx + cartLength,
                  pricing: pricing,
                  qty: liquorControlled ? 1 : qty,
                  forceVisible: forceVisible,
                  rowIdx: element.idx,
                ),
              );
            }

            final Option<ServiceError> itemCountRes = await _itemService.subtractFromItemCountUsingSaleRows(newRows);

            if (itemCountRes.isSome()) {
              _notificationService.error(
                itemCountRes.match(
                  (ServiceError t) => t.message,
                  () => "Error updating Item Count",
                ),
              );
              return;
            }

            if (isDialog) {
              Get.back(result: newRows);
              return;
            }

            for (int i = 0; i < newRows.length; i++) {
              await _saleController.addSaleRowToCart(
                saleRow: newRows[i],
                newSale: newSale,
                showOnPole: i == newRows.length - 1,
                liquorList: itemWithModList[i].item.document.liquorList,
                liquorPLU: itemWithModList[i].item.liq_ctl_plu,
                parentItem: item,
              );
            }
            if (newRows.isNotEmpty) {
              _saleController.checkForDuplicates(newRows.first);
            }
            cartLength += newRows.length;
          }
        } else {
          await _handleSaleRowWithNoMods(
            item: item,
            isDialog: isDialog,
          );
        }
      }
    } finally {
      if (!isDialog) await _saleController.upsertCurrentSale();
    }
  }

  Future<void> _handleSaleRowWithNoMods({
    required Item item,
    Sale? newSale,
    bool isDialog = false,
  }) async {
    final int qty = _saleController.globalQuantity.value;
    final String priceKey = item.document.promptForPrice
        ? "S0L0C0"
        : await _saleController.findItemCurrentPrice(item.document.pricing, 0, item.long_desc);
    final int dualPrice = _saleController.getDualPrice(item.document.pricing, priceKey);
    final bool takeOutSurcharge = item.document.takeOutSurcharge;
    final bool liquorControlled = item.liq_ctl_plu != null || item.document.liquorList.isNotEmpty;
    final int loopAmt = liquorControlled ? qty : 1;

    for (int i = 0; i < loopAmt; i++) {
      final SaleRow newRow = SaleRow(
        receiptDescription: item.document.receiptDesc ?? item.long_desc,
        department: item.departmentByDepartment!.title,
        flags: <int>[
          if (item.departmentByDepartment!.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
          if (item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
          if (item.document.promptForPrice || item.document.isOpenPrice) SaleRowFlags.OPEN_PRICE.index,
          if (priceKey[1] != "0") SaleRowFlags.PRICE_SCHEDULE.index,
          if ((item.document.printInRed ?? false) || (item.departmentByDepartment?.document.printInRed ?? false)) SaleRowFlags.PRINT_RED.index,
        ],
        item: item.item,
        upc: item.upc,
        transactionFlags: <int>[],
        originalPrice: item.document.pricing[priceKey] ?? 0,
        cashOriginalPrice: dualPrice,
        basePrice: item.document.pricing[priceKey] ?? 0,
        cashBasePrice: dualPrice,
        taxFlags: item.departmentByDepartment!.document.taxFlags,
        origTaxFlags: item.departmentByDepartment!.document.taxFlags,
        itemPricing: item.document.pricing,
        prep: item.document.prep > 0 ? item.document.prep : item.departmentByDepartment?.document.prep ?? 0,
        qty: liquorControlled ? 1 : qty,
        takeOutSurcharge: takeOutSurcharge,
      );

      final Option<ServiceError> itemCountRes =
          await _itemService.subtractFromItemCountUsingSaleRows(<SaleRow>[newRow]);

      if (itemCountRes.isSome()) {
        _notificationService.error(
          itemCountRes.match(
            (ServiceError t) => t.message,
            () => "Error updating Item Count",
          ),
        );
        return;
      }

      if (isDialog) {
        Get.back(result: <SaleRow>[newRow]);
        return;
      }

      await _saleController.addSaleRowToCart(
        saleRow: newRow,
        newSale: newSale,
        liquorList: item.document.liquorList,
        liquorPLU: item.liq_ctl_plu,
      );
    }
  }

  Future<List<ModObject>> _handleItemMods(Item selectedItem) async {
    List<ModObject> itemModList = <ModObject>[];
    if (selectedItem.document.modifiers.isNotEmpty) {
      await Get.dialog<List<ModObject>>(
        ModifiersDialog(
          selectedItem: selectedItem,
        ),
      ).then((List<ModObject>? value) {
        if (value != null) {
          itemModList = value;
        }
      });
    }

    return itemModList;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<int?> _openSetPriceDialog() async {
    // Initialize and load up the controllers for the dialog
    _numpadController.text.value = none();
    final int? newPrice = await Get.dialog<int?>(
      AlertDialog(
        title: const Text(
          "Set Price",
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 22),
        ),
        content: SizedBox(
          height: Get.height * 0.7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              const Expanded(
                flex: 6,
                child: NumPadWidget(
                  label: "price",
                  type: NumPadType.currency,
                ),
              ),
              Expanded(
                child: DialogButton(
                  buttonType: EDialogButtonType.CONFIRM,
                  onTapped: () async {
                    Get.back(
                      result: _numpadController.text.value.match(
                        (String numpadValue) => int.parse(
                          numpadValue.replaceAll(RegExp(r'\.|\,|\$'), ''),
                        ),
                        () => null,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
    _numpadController.text.value = none();
    return newPrice;
  }
}
