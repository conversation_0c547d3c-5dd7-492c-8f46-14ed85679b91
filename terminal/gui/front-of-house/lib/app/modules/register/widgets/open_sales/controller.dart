import 'dart:async';

import 'package:backoffice/main.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/liquor_to_pour.dart';
// ignore: unused_import
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/liquor_control.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/sales.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';

enum SortOrder {
  alphabet,
  reverseAlphabet,
  newestFirst,
  oldestFirst,
}

class OpenSalesController extends GetxController {
  final SaleService _saleService = Get.find();
  final LiquorControlService _liquorControlService = Get.find();
  final NotificationService _notificationService = Get.find();
  final IdentityService _identityService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final ConfigService _configService = Get.find();

  ScrollController checkListController = ScrollController();

  late SaleController saleController;
  late RegisterController registerController;

  late Timer blinkTimer;
  late Timer checkTimer;
  late Worker openSalesEver;

  RxList<Sale> openSales = <Sale>[].obs;

  final RxBool secretDrawerOpen = false.obs;
  final Rx<SortOrder> sortOrder = SortOrder.newestFirst.obs;
  final RxBool showPricePreview = false.obs;
  final RxBool blink = false.obs;
  final RxBool loading = true.obs;
  RxBool visible = false.obs;
  bool blinkDelay = true;
  List<SaleElement> buildList = <SaleElement>[];

  final RxBool hasForceUnlockPerm = false.obs;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    saleController = Get.find();
    registerController = Get.find();

    // Fetch and filter open sales.
    setOpenChecks(_saleService.allOpenSales);

    showPricePreview.value = _identityService.currentEmployee.document.showPricePreview ?? false;

    final String? userOrder = _identityService.currentEmployee.document.salesSortOrder;

    if (userOrder != null) {
      sortOrder.value = SortOrder.values.firstWhere(
        // ignore: sdk_version_since
        (SortOrder val) => val.name == userOrder,
      );
    }

    // Fires off whenever list of open sales in the sale service is updated.
    openSalesEver = ever(_saleService.allOpenSales, (List<Sale> sales) {
      setOpenChecks(sales);
    });

    // Check for force unlock permission.
    {
      final bool view = await PermissionService.enforce(
        CURRENT_EMPLOYEE.value.employee_class,
        "view",
        "Supervisor Menu",
        _graphqlService,
      );

      hasForceUnlockPerm.value = view;
    }

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onReady() async {
    visible = saleController.showOpenSales;

    blinkTimer = visible.value
        ? Timer.periodic(
            const Duration(milliseconds: 500),
            (Timer t) {
              if (blinkDelay && !blink.value) {
                blinkDelay = false;
                return;
              }
              blinkDelay = true;
              blink.value = !blink.value;
            },
          )
        : Timer(Duration.zero, () {});

    checkTimer = visible.value
        ? Timer.periodic(const Duration(seconds: 10), (Timer t) {
            checkOpenTime();
            loading.refresh();
          })
        : Timer(Duration.zero, () {});

    saleController.showOpenSales.refresh();

    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  void onClose() {
    blinkTimer.cancel();
    checkTimer.cancel();
    openSalesEver.dispose();
    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void sortSales() {
    switch (sortOrder.value) {
      case SortOrder.alphabet:
        buildList.sort(
          (SaleElement a, SaleElement b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()),
        );
      case SortOrder.reverseAlphabet:
        buildList.sort(
          (SaleElement a, SaleElement b) => b.title.toLowerCase().compareTo(a.title.toLowerCase()),
        );
      case SortOrder.newestFirst:
        buildList.sort(
          (SaleElement a, SaleElement b) => b.sale.sale_number.compareTo(a.sale.sale_number),
        );
      case SortOrder.oldestFirst:
        buildList.sort(
          (SaleElement a, SaleElement b) => a.sale.sale_number.compareTo(b.sale.sale_number),
        );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void buildElementList() {
    final List<Sale> openList = <Sale>[...openSales];

    final Sale currSale = saleController.currentSale.value.match(
      (Sale sale) {
        if (openSales.firstWhereOrNull((Sale s) => s.sale == sale.sale) == null) {
          openList.add(sale);
        }
        return sale;
      },
      () => Sale.empty(),
    );

    buildList = openList.map((Sale sale) {
      final String title = Helpers.getSaleTitle(sale, saleController.saleName.value);
      final SaleHeader header = sale.document.saleHeader;
      final int roomIdx = header.roomIdx ?? 0;

      return SaleElement(
        sale: sale.sale == currSale.sale ? currSale : sale,
        key: Key(sale.sale),
        title: title,
        roomIdx: roomIdx,
        created: sale.created_at ?? DateTime.now(),
      );
    }).toList();

    sortSales();

    buildList.insert(
      0,
      SaleElement(
        sale: Sale.empty(),
        key: const Key("0"),
        title: "New ${saleController.saleName.value}",
        roomIdx: 0,
        created: DateTime.now(),
      ),
    );

    checkOpenTime();
  }

  ///
  ///
  ///
  ///
  ///
  /// Gets and filters open sales from the sale service.
  void setOpenChecks(List<Sale> incomingSales) {
    unawaited(checkCurrentSaleForConflictingUpdates(incomingSales));
    // If filtering by current user is needed
    if (saleController.getProperSection().salesByEmployee) {
      openSales.value = incomingSales
          .where(
            (Sale s) =>
                !s.document.saleHeader.saleFlags.contains(SaleFlags.ONLINE_ORDER.index) &&
                s.document.saleHeader.currentEmployeeNumber == _identityService.currentEmployee.id,
          )
          .toList();
    } else {
      openSales.value = incomingSales.where((Sale s) => !s.document.saleHeader.saleFlags.contains(SaleFlags.ONLINE_ORDER.index)).toList();
    }
    _saleService.locked.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void checkOpenTime() {
    final DateTime now = DateTime.now();

    for (final SaleElement element in buildList) {
      if (element.sale.sale_number > 1) {
        final String tableDesc = element.sale.document.saleHeader.tableDesc ?? "";

        final Section tableSection = _configService.tableToSection[tableDesc] ?? Section(desc: "", idx: 0);

        Section section = saleController.getProperSection();

        if (tableSection.idx > 0) section = tableSection;

        if (!element.blinking) {
          double? blinkTime;

          if ((section.blinkAfterMins ?? 0) > 0) {
            blinkTime ??= section.blinkAfterMins!;
            if (section.blinkAfterMins! < blinkTime) {
              blinkTime = section.blinkAfterMins;
            }
          }

          if (blinkTime != null && element.sale.created_at != null) {
            if (now.difference(element.sale.created_at!).inSeconds / 60 > blinkTime) {
              element.blinking = true;
            }
          }
        }

        if (!element.red) {
          double? redTime;

          if ((section.redAfterMins ?? 0) > 0) {
            redTime ??= section.redAfterMins!;
            if (section.redAfterMins! < redTime) {
              redTime = section.redAfterMins;
            }
          }

          if (redTime != null && element.sale.created_at != null) {
            if (now.difference(element.sale.created_at!).inSeconds / 60 > redTime) {
              element.red = true;
            }
          }
        }
      }
    }
    loading.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onElementPressed({
    required Sale sale,
    required bool elementSelected,
  }) async {
    if (loading.value || _saleService.locked.value) return;
    if (elementSelected && sale.sale_number < 1) return;
    if (elementSelected || sale.sale_number < 1) {
      await deselect();
      return;
    }
    await changeSelection(sale);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> changeSelection(Sale selectedCheck) async {
    saleController.deselectAll();
    final List<Sale> upsertList = <Sale>[];
    final bool dupedSale = await saleController.currentSale.value.match(
      (Sale sale) async {
        if (sale.sale == selectedCheck.sale) {
          return true;
        }
        if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index)) {
          sale.document.saleHeader.saleFlags.add(SaleFlags.SUSPENDED.index);
        }
        sale.document.saleHeader.currentTerminalNumber = 0;
        upsertList.add(sale);
        return false;
      },
      () async => false,
    );

    if (dupedSale) {
      return;
    }
    _saleService.locked.value = true;
    selectedCheck.document.saleHeader.saleFlags.removeWhere((int i) => i == SaleFlags.SUSPENDED.index);
    selectedCheck.document.saleHeader.currentTerminalNumber = _identityService.terminalNumber;

    upsertList.add(selectedCheck);

    final Either<ServiceError, List<Sale>> retrieveSaleResult = await _saleService.upsertMultiple(
      sales: upsertList,
      employee: _identityService.currentEmployee,
      terminalNumber: _identityService.terminalNumber,
    );

    await retrieveSaleResult.fold(
      (ServiceError error) async {
        _saleService.locked.value = false;
        _notificationService.error(error.message);
      },
      (List<Sale> saleRes) async {
        final Sale? current = saleRes.firstWhereOrNull(
          (Sale s) => ((selectedCheck.sale == "") && s.sale_number == selectedCheck.sale_number) || s.sale == selectedCheck.sale,
        );
        final Sale? notCurrent = saleRes.firstWhereOrNull((Sale s) => s.sale != selectedCheck.sale);
        if (current != null) {
          saleController.currentSale.value = some(current);
        }
        if (notCurrent != null) {
          openSales.value = openSales.map((Sale s) => s.sale == notCurrent.sale ? notCurrent : s).toList();

          if (_configService.merchantConfig.document.prepOnSaleChange) {
            if (Helpers.saleNeedsPrinted(notCurrent)) {
              unawaited(saleController.handlePrepPrint(sale: notCurrent));
            }
          }
        }
      },
    );

    saleController.refreshCurrentSaleModels();
    secretDrawerOpen.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> deselect() async {
    final Sale? currSale = await saleController.currentSale.value.match(
      (Sale sale) async => sale,
      () async => null,
    );
    if (currSale == null) {
      return;
    }
    _saleService.locked.value = true;
    saleController.deselectAll();
    await saleController.saveSale();
    openSales.value = openSales.map((Sale s) => s.sale == currSale.sale ? currSale : s).toList();
    saleController.resetSale();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void scrollToTop() {
    try {
      unawaited(
        checkListController.animateTo(
          checkListController.position.minScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.fastOutSlowIn,
        ),
      );
      // ignore: empty_catches
    } catch (err) {}
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void scrollToBottom() {
    try {
      unawaited(
        checkListController.animateTo(
          checkListController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.fastOutSlowIn,
        ),
      );
      // ignore: empty_catches
    } catch (err) {}
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> changeSortOrder(SortOrder order) async {
    await _identityService.refreshCurrentUser();
    // ignore: sdk_version_since
    _identityService.currentEmployee.document.salesSortOrder = order.name;
    await _identityService.updateCurrentUserDocument();
    sortOrder.value = order;
    sortSales();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> changeShowPricePreview() async {
    await _identityService.refreshCurrentUser();
    _identityService.currentEmployee.document.showPricePreview = !showPricePreview.value;
    await _identityService.updateCurrentUserDocument();
    showPricePreview.value = !showPricePreview.value;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool getLockStatus(Sale sale) {
    return sale.sale_number > 0 &&
        sale.document.saleHeader.currentTerminalNumber != 0 &&
        sale.document.saleHeader.currentTerminalNumber != _identityService.terminalNumber;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> checkCurrentSaleForConflictingUpdates(List<Sale> sales) async {
    if (sales.isNotEmpty) {
      await saleController.currentSale.value.match(
        (Sale current) async {
          final Sale? currentMatch = sales.firstWhereOrNull((Sale s) => s.sale == current.sale);

          if (currentMatch == null) {
            /// If match not found checks to see if sale is older than 5 seconds before kicking user off
            /// This is mainly to prevent the check with old data overlapping with the creation of a new sale
            /// This bug was found through stress lab testing
            if (current.created_at != null && DateTime.now().toUtc().difference(current.created_at!).inSeconds > 5) {
              saleController.currentSale.value = none();
            }
            return;
          }
          if (currentMatch.document.saleHeader.currentTerminalNumber != _identityService.terminalNumber) {
            saleController.currentSale.value = none();
          } else {
            final List<String> ltpList = (await _liquorControlService.getLiquorToPour())
                .match(
                  (ServiceError l) {
                    _notificationService.error(l.message);
                    return <LiquorToPour>[];
                  },
                  (List<LiquorToPour> r) => r,
                )
                .map((LiquorToPour l) => l.liquor_to_pour)
                .toList();
            bool pourFound = false;
            for (final SaleRow r in current.document.saleRows) {
              final int initLength = r.liquorToPour.length;
              r.liquorToPour = r.liquorToPour.where((String ltp) => ltpList.contains(ltp)).toList();
              if (r.liquorToPour.length != initLength) {
                if (!r.flags.contains(SaleRowFlags.LIQUOR_POURED.index)) r.flags.add(SaleRowFlags.LIQUOR_POURED.index);
                pourFound = true;
              }
            }
            if (pourFound) {
              saleController.currentSale.refresh();
            }
          }
        },
        () => null,
      );
    }
  }

  Future<void> forceUnlockSale(Sale sale) async {
    // This is basically a nil terminal, which represents it being unlocked.
    sale.document.saleHeader.currentTerminalNumber = 0;
    // This is necessary for it to be shown in the sale register list.
    sale.document.saleHeader.saleFlags.add(SaleFlags.SUSPENDED.index);

    await _saleService.upsert(
      sale: sale,
      employee: _identityService.currentEmployee,
      terminalNumber: _identityService.terminalNumber,
    );
  }
}
