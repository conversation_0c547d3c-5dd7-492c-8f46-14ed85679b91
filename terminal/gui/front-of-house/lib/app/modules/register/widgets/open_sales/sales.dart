import 'package:desktop/app/data/enums/admin_permission_types.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/modules/register/dialogs/admin_permission/dialog.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class OpenSalesList extends GetView<OpenSalesController> {
  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        controller.buildElementList();

        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: controller.visible.value ? 2 : 0,
          ),
          child: Material(
            color: R2Colors.white,
            child: ListView.builder(
              controller: controller.checkListController,
              itemCount: controller.buildList.length,
              itemBuilder: (BuildContext context, int index) {
                return controller.visible.value
                    ? Padding(
                        padding: const EdgeInsets.only(
                          top: 2,
                          bottom: 2,
                        ),
                        child: controller.buildList[index],
                      )
                    : Container();
              },
            ),
          ),
        );
      },
    );
  }
}

// ignore: must_be_immutable
class SaleElement extends GetView<OpenSalesController> {
  SaleElement({
    required this.sale,
    required this.key,
    required this.title,
    required this.roomIdx,
    required this.created,
    this.blinking = false,
    this.red = false,
    this.saleLock = false,
  });

  final Sale sale;
  final String title;
  final int roomIdx;
  final DateTime created;
  bool blinking;
  bool red;
  bool saleLock;

  final NotificationService _notificationService = Get.find();
  final PrinterService _printerService = Get.find();
  final SaleService _saleService = Get.find();

  @override
  // ignore: overridden_fields
  final Key key;

  @override
  Widget build(BuildContext context) {
    bool elementSelected = false;
    saleLock = controller.getLockStatus(sale);
    controller.saleController.currentSale.value.match(
      (Sale currSale) {
        elementSelected = sale.sale == currSale.sale;
      },
      () {
        elementSelected = sale.sale_number < 1;
      },
    );

    final Color fontColor = elementSelected
        ? R2Colors.white
        : _saleService.locked.value || saleLock || _printerService.prepPrintingSales.contains(sale.sale)
            ? R2Colors.neutral500
            : red
                ? R2Colors.negativeRed
                : R2Colors.neutral700;

    final Color tileColor = elementSelected
        ? R2Colors.primary400
        : saleLock
            ? R2Colors.lockedSale
            : red
                ? R2Colors.negativeRedDisabled
                : R2Colors.neutral200;

    return _wrapWithLockOverride(
      RadiusListener(
        onTap: () async {
          if (saleLock || _printerService.prepPrintingSales.contains(sale.sale)) return;
          await controller.onElementPressed(
            sale: sale,
            elementSelected: elementSelected,
          );
        },
        tapRadius: 50,
        child: sale.sale_number < 1
            ? ListTile(
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                tileColor: tileColor,
                title: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 20,
                    color: fontColor,
                  ),
                ),
              )
            : ListTile(
                key: key,
                dense: true,
                contentPadding: EdgeInsets.symmetric(
                  vertical: controller.loading.value ? 0 : (!controller.showPricePreview.value ? 10 : 8) + (title.length > 15 ? 4 : 0),
                  horizontal: 5,
                ),
                tileColor: tileColor,
                title: Column(
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Expanded(
                          child: Obx(() {
                            final bool blink = blinking && controller.blink.value && !elementSelected && !saleLock;
                            final bool needPrepped =
                                sale.document.saleRows.any((SaleRow r) => r.prep > 0 && !r.flags.contains(SaleRowFlags.PREP_PRINTED.index));
                            final bool prepError = sale.document.saleRows
                                .any((SaleRow r) => r.preppedAt != null && r.preppedAt!.keys.any((String k) => r.preppedAt![k] == null));

                            Color getPrepDotColor(Sale sale) {
                              if (prepError && _printerService.prepPrintingSales.contains(sale.sale)) {
                                return R2Colors.neutral400;
                              }
                              if (prepError) {
                                return R2Colors.negativeRed;
                              }
                              if (needPrepped) {
                                return Colors.amber;
                              }
                              if (sale.document.saleHeader.saleFlags.contains(SaleFlags.PREP_PRINTED.index)) {
                                return const Color(0xFF6BCE96);
                              }
                              return Colors.transparent;
                            }

                            return controller.loading.value
                                ? Lottie.asset("lib/assets/lottie/loading-animation.json", height: 60)
                                : AnimatedDefaultTextStyle(
                                    duration: Duration.zero,
                                    style: blink
                                        ? TextStyle(
                                            color: Colors.transparent,
                                            fontWeight: FontWeight.bold,
                                            fontSize: title.length > 15 ? 12 : 14,
                                          )
                                        : TextStyle(
                                            color: fontColor,
                                            fontWeight: FontWeight.bold,
                                            fontSize: title.length > 15 ? 12 : 14,
                                          ),
                                    child: Row(
                                      children: <Widget>[
                                        Expanded(
                                          child: Icon(
                                            Icons.circle,
                                            size: 10,
                                            color: getPrepDotColor(sale),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 5,
                                          child: Text(
                                            title,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        Expanded(
                                          child: saleLock
                                              ? const Icon(
                                                  Icons.lock,
                                                  size: 12,
                                                  color: Color(0xFFA36719),
                                                )
                                              : Container(),
                                        ),
                                      ],
                                    ),
                                  );
                          }),
                        ),
                      ],
                    ),
                    if (_printerService.prepPrintingSales.contains(sale.sale)) const LinearProgressIndicator(),
                  ],
                ),
                subtitle: controller.showPricePreview.value && !controller.loading.value
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              "${sale.document.saleHeader.total < 0 ? "-" : ""}\$${Helpers.formatCurrency(sale.document.saleHeader.total.abs())}",
                              textAlign: TextAlign.center,
                              style: TextStyle(color: fontColor),
                            ),
                          ),
                        ],
                      )
                    : null,
              ),
      ),
    );
  }

  Widget _wrapWithLockOverride(Widget child) {
    return GestureDetector(
      onLongPress: _promptForForceUnlock,
      child: child,
    );
  }

  Future<void> _promptForForceUnlock() async {
    if (!saleLock) {
      return;
    }

    if (controller.hasForceUnlockPerm.value != true) {
      // If the user doesn't have the permission to unlock the sale, alllow the
      // manager to unlock the sale.
      final bool? managerOverride = await Get.defaultDialog<bool>(
        title: "Need Admin Permission",
        content: const AdminPermissionDialog(
          permissionType: AdminPermissionType.SUPERVISOR_MENU,
        ),
        barrierDismissible: true,
      );

      if (managerOverride != true) {
        return;
      }
    }

    // Now give a very stern warning.
    final bool? confirm = await Get.dialog<bool>(
      AlertDialog(
        title: const Text(
          "Force Unlock Sale",
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: 300,
          child: Text(
            "Are you sure you want to force unlock this sale? This can result in loss of data if it conflicts with another terminal.",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: R2Colors.negativeRed,
            ),
          ),
        ),
        actions: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              DialogButton(
                buttonType: EDialogButtonType.CANCEL,
                onTapped: () => Get.back(result: false),
              ),
              DialogButton(
                buttonType: EDialogButtonType.CONFIRM,
                onTapped: () => Get.back(result: true),
              ),
            ],
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    await controller.forceUnlockSale(sale);

    _notificationService.success("Sale force unlocked");
  }
}
