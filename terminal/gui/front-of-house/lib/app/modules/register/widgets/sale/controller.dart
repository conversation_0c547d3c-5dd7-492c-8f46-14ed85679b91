// ignore_for_file: always_specify_types
import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:backoffice/app/data/enums/berg_item_maintenance.dart';
import 'package:backoffice/app/data/enums/berg_type.dart';
import 'package:backoffice/app/data/enums/overlapping_schedule_options.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/error_correct_type.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/enums/prep_device_type.dart';
import 'package:desktop/app/data/enums/register_menu_options.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_mode.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/seat_select_types.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/liquor_to_pour.dart';
import 'package:desktop/app/data/models/pay_rate.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/item.service.dart';
import 'package:desktop/app/data/services/liquor_control.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/pole_display.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/data/services/scanner.service.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/dialogs/change_order_type_dialog.dart';
import 'package:desktop/app/modules/register/dialogs/liquor_ctl/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/modifiers/controller.dart';
import 'package:desktop/app/modules/register/dialogs/modifiers/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/overlapping_schedules_dialog.dart';
import 'package:desktop/app/modules/register/dialogs/prep_errors/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/quantity_dialog.dart';
import 'package:desktop/app/modules/register/dialogs/sale_fields/sale_fields.dart';
import 'package:desktop/app/modules/register/dialogs/seat_select/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:desktop/app/modules/register/dialogs/tables/dialog.dart';
import 'package:desktop/app/modules/register/widgets/items/widget.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/items.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_TABLE_RECORD = '''
  query GET_TABLE_RECORD{
    json_record(where: {record_key: {_eq: "rooms"}}) {
      record_key
      updated_at
      document
    }
  }
''';

class SaleController extends GetxController {
  final GraphqlService _graphqlService = Get.find();
  final SaleService _saleService = Get.find();
  final ItemService _itemService = Get.find();
  final ScannerService _scannerService = Get.find();
  final LiquorControlService _liquorControlService = Get.find();
  final PoleDisplayService _poleDisplayService = Get.find();
  final NotificationService _notificationService = Get.find();
  final PrinterService _printerService = Get.find();
  final IdentityService _identityService = Get.find();
  final ConfigService _configService = Get.find();
  final CustomerService _customerService = Get.find();

  final ActivityService _activityService = Get.find();

  final RegisterController _registerController = Get.find();

  final Logger _logger = Logger('SaleController');

  late final StreamSubscription scannerSubscription;
  late final StreamSubscription liquorSubscription;

  Rx<Option<Sale>> currentSale = none<Sale>().obs;
  Rx<Option<Customer>> currentCustomer = none<Customer>().obs;
  RxInt globalQuantity = 1.obs;

  Rx<SaleMode> currentSaleMode = SaleMode.SALE.obs;

  Section jobCodeSection = Section(idx: 0, desc: "");
  Section terminalSection = Section(idx: 0, desc: "");

  final RxBool autoRefund = false.obs;
  final RxBool requireTable = false.obs;
  final RxBool requireDesc = false.obs;
  final RxBool requireSeatCnt = false.obs;
  final RxBool autoAddSeats = false.obs;
  final RxBool showOpenSales = false.obs;
  final RxBool requireOrdType = false.obs;
  final RxInt custCountOption = 0.obs;
  final RxBool requireCustIfTogo = false.obs;
  final RxBool requireCustIfDelivery = false.obs;
  final RxBool showPromisedTimeAndType = false.obs;
  final RxString testDescString = "".obs;
  final RxString selectedPourItem = "".obs;

  int defaultOrdType = 0;

  bool showModPrices = true;
  bool liqCtlMenuOpen = false;

  final RxString autoRefundNumber = "".obs;
  final RxString autoRefundBatch = "".obs;
  final RxString saleName = "Sale".obs;

  final RxInt cashDiscountSubtotal = 0.obs;
  final RxInt cashDiscountTax = 0.obs;
  final RxInt cashDiscountTotal = 0.obs;
  final RxInt selectedSeat = 0.obs;

  List<PriceSchedule> psList = <PriceSchedule>[];
  List<PriceLevel> levelList = <PriceLevel>[];
  final Rx<FlowElement> newSaleTable = FlowElement().obs;
  final RxInt newOrdType = 0.obs;
  final RxInt newCustCount = 1.obs;

  /// {"sale": {"item": [LiquorToPour]}}
  final RxMap<int, Map<String, List<LiquorToPour>>> liquorToPour = <int, Map<String, List<LiquorToPour>>>{}.obs;
  Map<int, Item> ltpMap = <int, Item>{};

  late OpenSalesController _openSalesController;

  TextEditingController newSaleDescController = TextEditingController();
  TextEditingController newSaleSeatCntController = TextEditingController(text: "1");

  final Map<int, int> week = <int, int>{
    1: 64, //Monday
    2: 32, //Tuesday
    3: 16, //Wednesday
    4: 8, //Thursday
    5: 4, //Friday
    6: 2, //Saturday
    7: 1, //Sunday
  };

  List<String> get getPrepPrintingSales => _printerService.prepPrintingSales;

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    await _getUserSections();
    await _getPriceSchedulesAndLevels();

    final Section section = getProperSection();

    requireTable.value = section.forceTblNum && section.trackBySeat;
    requireSeatCnt.value = section.forceCustCnt && section.trackBySeat;
    autoAddSeats.value = section.autoAddSeats && section.trackBySeat;
    requireDesc.value = section.forceSaleDesc;
    showOpenSales.value = section.openSalesList;
    requireOrdType.value = section.forceOrdType;
    custCountOption.value = section.custCountOption;
    requireCustIfTogo.value = section.forceCustIfTogo;
    requireCustIfDelivery.value = section.forceCustIfDelivery;
    saleName.value = section.saleName ?? "Sale";
    defaultOrdType = section.defOrdType;
    newOrdType.value = defaultOrdType;
    showPromisedTimeAndType.value = section.showPromisedTimeAndType;

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onReady() async {
    _openSalesController = Get.find();

    scannerSubscription = _scannerService.eventBus.on<ScannerEvent>().listen((ScannerEvent event) async {
      if (Get.isBottomSheetOpen ?? false) return;
      if (_registerController.scaffoldKey.currentState?.isEndDrawerOpen ?? false) return;
      if (_registerController.menuOpen && _registerController.lastMenuScreen != RegisterMenuOptions.SALE_OPEN_PRICE_ITEM) return;

      await _handleScanResult(event.output);
    });

    liquorSubscription = _liquorControlService.eventBus.on<int>().listen((int event) {
      handleBergScan(event);
    });

    showModPrices = _configService.merchantConfig.document.showModPrice;

    if (_configService.merchantConfig.document.viewTablesOnSignOn) {
      await Get.bottomSheet(
        const TablesDialog(
          tablesOnly: true,
          viewTables: true,
        ),
        isScrollControlled: true,
        ignoreSafeArea: false,
        backgroundColor: Colors.transparent,
      );
    }

    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onClose() async {
    await scannerSubscription.cancel();
    await liquorSubscription.cancel();
    super.onClose();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void clearSaleFields() {
    testDescString.value = "";
    newSaleDescController.text = "";
    newSaleSeatCntController.text = "1";
    newSaleTable.value = FlowElement();
    newOrdType.value = defaultOrdType;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePrepPrint({
    required Sale sale,
    List<SaleRow>? voidedList,
    bool canceledSale = false,
  }) async {
    if (getPrepPrintingSales.contains(sale.sale)) return;
    await _printerService.startPrepPrint(
      sale: sale,
      voidedList: voidedList,
      canceledSale: canceledSale,
      saleName: getProperSection().saleName,
    );
    _openSalesController.openSales.refresh();
    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _getUserSections() async {
    // ignore: non_constant_identifier_names
    final String GET_RECENT_TIMECARD = '''
      query GET_RECENT_TIMECARD {
        timecard(order_by: {punch_at: desc}, where: {emp_id: {_eq: "${_identityService.currentEmployee.id}"}}, limit: 1) {
          timecard
          round_at
          punch_type
          punch_at
          job_code
          emp_id
          break_idx
        }
      }
      ''';

    try {
      int employeeJobCodeIdx = 0;
      final List<PayRate> userPayRates = _identityService.currentEmployee.document.payRates;

      final QueryResult<Object?> cardQueryRes = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_RECENT_TIMECARD),
        ),
      );

      if (cardQueryRes.hasException) {
        throw cardQueryRes.exception.toString();
      }

      final List<dynamic> cardQueryList = cardQueryRes.data?["timecard"] as List<dynamic>;

      if (cardQueryList.isNotEmpty) {
        final TimeCard recentTimeCard = TimeCard.fromJson(cardQueryList[0] as Map<String, dynamic>);

        if (recentTimeCard.punch_type == 1) {
          employeeJobCodeIdx = recentTimeCard.job_code;
        }
      }

      if (employeeJobCodeIdx < 1 && userPayRates.isNotEmpty) {
        userPayRates.sort(
          (PayRate a, PayRate b) => a.index.compareTo(b.index),
        );
        if (_identityService.currentEmployee.document.payRates.isNotEmpty) {
          employeeJobCodeIdx = userPayRates.first.jobCode;
        }
      }

      if (employeeJobCodeIdx > 0) {
        final Either<ServiceError, SystemSettingJsonRecord> settingsRes = await _configService.getSystemSettings();

        settingsRes.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (SystemSettingJsonRecord settingsConfig) {
            final SystemSettingJsonRecordJobCode currentJobCode = settingsConfig.document.jobCodes.firstWhere(
              (code) => code.index == employeeJobCodeIdx,
            );

            if (currentJobCode.section > 0) {
              jobCodeSection = _configService.sectionList.firstWhereOrNull(
                    (section) => section.idx == currentJobCode.section,
                  ) ??
                  Section(idx: 0, desc: "");
            }
          },
        );
      }

      if (_identityService.terminalSection > 0) {
        terminalSection = _configService.sectionList.firstWhereOrNull(
              (section) => section.idx == _identityService.terminalSection,
            ) ??
            Section(idx: 0, desc: "");
      }
    } catch (err, stack) {
      _logger.severe(
        "Error Getting User's JobCode",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Sale?> makeNewSale() async {
    final Sale newSale = Sale.empty();
    testDescString.value = newSaleDescController.text;

    if (requireTable.value &&
        newSaleTable.value.desc == "" &&
        newOrdType.value != OrderType.DELIVERY.index &&
        newOrdType.value != OrderType.TAKEOUT.index &&
        !(requireSeatCnt.value || requireOrdType.value || custCountOption.value == 1)) {
      await Get.bottomSheet(
        const TablesDialog(),
        isScrollControlled: true,
        ignoreSafeArea: false,
        backgroundColor: Colors.transparent,
      ).then((dynamic element) {
        testDescString.value = "";
        if (element is FlowElement) {
          newSaleTable.value = element;
        }
      });
      if (newSaleTable.value.desc == "") {
        clearSaleFields();
        return null;
      }
    } else if (requireSeatCnt.value || requireOrdType.value || custCountOption.value == 1) {
      final dynamic dialogRes = await Get.dialog(
        Dialog(
          // ignore: avoid_dynamic_calls
          backgroundColor: R2Colors.white,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(25.0)),
          ),
          child: const SaleFields(),
        ),
      );
      if (dialogRes == null) {
        _notificationService.error("${saleName.value} Canceled!");
        clearSaleFields();
        return null;
      }
    } else if (requireDesc.value) {
      if (newSaleDescController.text == "") {
        await Get.bottomSheet(
          VirtualKeyboard(
            description: "Change Description",
            textEditingController: newSaleDescController,
            isBOH: false,
            onConfirm: (String value) {
              if (value != "") {
                newSaleDescController.text = value;
              }
            },
          ),
          backgroundColor: Colors.transparent,
          isDismissible: false,
          enableDrag: false,
        );
      }
      if (newSaleDescController.text == "") {
        _notificationService.error("Description Required!");
        clearSaleFields();
        return null;
      }
    }

    if (requireOrdType.value &&
        ((requireCustIfTogo.value && newOrdType.value == OrderType.TAKEOUT.index) ||
            (requireCustIfDelivery.value && newOrdType.value == OrderType.DELIVERY.index))) {
      await _registerController.openCustomDrawer(
        screen: RegisterMenuOptions.CUSTOMER_ATTACH,
        fields: {"newSale": newSale},
      );
      if (newSale.document.saleHeader.customer == null) {
        if (newOrdType.value == OrderType.TAKEOUT.index) {
          _notificationService.error("Customer Required for to-go order!");
        } else {
          _notificationService.error("Customer Required for delivery order!");
        }
        clearSaleFields();
        return null;
      }
    }

    newSale.document.saleHeader.orderType = newOrdType.value;

    if (newSaleDescController.text != "") {
      newSale.document.saleHeader.saleDescription = newSaleDescController.text;
    }

    if (newSaleTable.value.desc != "") {
      newSale.document.saleHeader.tableDesc = newSaleTable.value.desc;
      newSale.document.saleHeader.tableStarted ??= DateTime.now().toUtc();
      newSale.document.saleHeader.roomIdx = newSaleTable.value.roomIdx;
    }

    if (autoAddSeats.value && newSaleTable.value.desc != "" && !requireSeatCnt.value) {
      newSale.document.saleHeader.seatCnt = newSaleTable.value.seatCnt;
    } else {
      newSale.document.saleHeader.seatCnt = int.parse(newSaleSeatCntController.text);
    }

    if (custCountOption.value == 1) {
      newSale.document.saleHeader.customerCount = newCustCount.value;
      newCustCount.value = 1;
    } else if (custCountOption.value == 2) {
      newSale.document.saleHeader.customerCount = newSale.document.saleHeader.seatCnt ?? 1;
    }

    newSale.document.saleHeader.priceLevel =
        _identityService.terminalPriceLevel > 0 ? _identityService.terminalPriceLevel : getProperSection().priceLevel;

    clearSaleFields();
    return newSale;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Section getProperSection() {
    return terminalSection.idx > 0 ? terminalSection : jobCodeSection;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleAddOrEditModifier({bool addMod = false}) async {
    try {
      currentSale.value.match(
        (Sale sale) async {
          ///
          ///
          /// DECLARE VARIABLES
          /////////////////////////////////////////////////////////////
          final List<SaleRow> voidedList = [];
          final Map<int, List<String>> errMap = <int, List<String>>{};
          final Map<int, Sale> printSales = <int, Sale>{};
          final List<SaleRow> currentRows = sale.document.saleRows;
          final SaleRow? selected = currentRows.firstWhereOrNull(
            (SaleRow sr) => sr.selected && !sr.flags.contains(SaleRowFlags.VOIDED.index) && sr.splitData == null,
          );
          final List<SaleRow> selectedList = currentRows
              .where(
                (SaleRow sr) => sr.selected && !sr.flags.contains(SaleRowFlags.VOIDED.index),
              )
              .toList();
          if (selectedList.isEmpty) {
            return _notificationService.error("Please select an editable${addMod ? " item or" : " "} modifier}!");
          }
          if (selected == null || selectedList.firstWhereOrNull((SaleRow r) => r.splitData != null) != null) {
            return _notificationService.error("Can not edit split items!");
          }

          final List<PrepDevice> prepList = [..._printerService.prepList];
          final List<SaleRow> removeRows = addMod ? <SaleRow>[] : Helpers.getChildRows(selected, sale.document.saleRows);
          final List<SaleRow> newRows = <SaleRow>[];
          final List<Future<PrepReturn>> promiseList = <Future<PrepReturn>>[];
          /////////////////////////////////////////////////////////////

          ///
          ///
          /// GET ROWS TO ADD
          /////////////////////////////////////////////////////////////
          if (!addMod) {
            Item modItem = Item.empty();

            final Either<ServiceError, Item> itemRes =
                await _itemService.getItemFromUUID(itemUUID: !selected.hasChildren ? currentRows[selected.parent].item : selected.item);

            itemRes.match(
              (ServiceError l) => _notificationService.error(l.message),
              (Item r) => modItem = r,
            );

            if (modItem.item == "") return;

            final List<ModObject>? value = await Get.dialog<List<ModObject>>(
              ModifiersDialog(
                selectedItem: modItem,
              ),
            );

            if (value == null) return;

            if (!selected.hasChildren || value.length == 1) {
              value.removeAt(0);
              for (final ModObject mo in value) {
                mo.idx -= 1;
                mo.parentIdx -= 1;
              }
            }

            for (final ModObject element in value) {
              Map<String, int> pricing = element.item.document.pricing;
              bool? forceVisible;

              if (element.parentIdx >= 0 || value.length < 2) {
                final ModObject parent = value.length < 2 ? ModObject(item: modItem, idx: 0, parentIdx: -1) : value[element.parentIdx];
                if (parent.item.document.overridePricing) {
                  pricing = parent.item.document.modifiers[element.item.item]!.pricing.isEmpty
                      ? parent.item.document.defModPricing
                      : parent.item.document.modifiers[element.item.item]!.pricing;
                }
                forceVisible = parent.item.document.modifiers[element.item.item]?.forceVisible;
                if (parent.item.document.passDesc) {
                  element.item.document.receiptDesc =
                      "${(parent.item.document.modifierDesc ?? "") == "" ? parent.item.document.receiptDesc ?? parent.item.long_desc : parent.item.document.modifierDesc} - ${element.item.document.receiptDesc ?? element.item.long_desc}";
                }
              }

              final SaleRow createdRow = await modObjToSaleRow(
                obj: element,
                rowIdx: selected.index + element.idx,
                parentIdx: element.parentIdx < 0 ? selected.parent : selected.index + element.parentIdx,
                pricing: pricing,
                forceVisible: forceVisible == true || selected.isVisible && value.isNotEmpty && element.idx < 1,
              );

              createdRow.seatNumber = selected.seatNumber;
              createdRow.qty = selected.qty;

              if (currentSaleMode.value == SaleMode.NEGATIVE_SALE) {
                createdRow.basePrice = createdRow.basePrice * -1;
                createdRow.cashBasePrice = createdRow.cashBasePrice * -1;
                createdRow.originalPrice = createdRow.originalPrice * -1;
                createdRow.cashOriginalPrice = createdRow.cashOriginalPrice * -1;
                createdRow.actualPrice = createdRow.actualPrice * -1;
                createdRow.cashPrice = createdRow.cashPrice * -1;
                createdRow.creditPrice = createdRow.creditPrice * -1;
              }

              if (createdRow.index >= -1) newRows.add(createdRow);
            }
          } else {
            if (_registerController.menuOpen) {
              Get.back();
              await Future.delayed(const Duration(milliseconds: 150));
            }
            final List<SaleRow>? addModRes = await Get.dialog<List<SaleRow>?>(
              Column(
                children: <Widget>[
                  Expanded(
                    flex: 2,
                    child: Container(),
                  ),
                  Expanded(
                    flex: 16,
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          flex: showOpenSales.value ? 9 : 6,
                          child: Container(),
                        ),
                        const Expanded(
                          flex: 11,
                          child: Scaffold(
                            body: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 2),
                              child: ItemsWidget(
                                isDialog: true,
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 4,
                          child: Container(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
            if (addModRes == null) return;
            if (addModRes.isEmpty) return;
            newRows.addAll(addModRes);
          }
          /////////////////////////////////////////////////////////////

          ///
          ///
          /// REMOVE VOIDED
          /////////////////////////////////////////////////////////////
          for (final SaleRow rRow in removeRows) {
            (await _liquorControlService.deleteLiquorToPourByPK(rRow.liquorToPour)).fold((l) => _notificationService.error(l.message), (r) => null);
            if (rRow.flags.contains(SaleRowFlags.PREP_PRINTED.index) || (rRow.flags.contains(SaleRowFlags.LIQUOR_POURED.index))) {
              rRow.flags.add(SaleRowFlags.VOIDED.index);
              voidedList.add(rRow);
            } else {
              currentRows.remove(rRow);
              (await _itemService.addToItemCountUsingSaleRows([rRow])).match(
                (t) => _notificationService.error(t.message),
                () => null,
              );
            }
          }
          /////////////////////////////////////////////////////////////

          ///
          ///
          /// ADD ROWS AND UPDATE INDEXES
          /////////////////////////////////////////////////////////////
          for (final SaleRow row in selectedList) {
            final int addIdx = row.index;

            final List<SaleRow> addRows = addMod
                ? newRows
                    .map(
                      (SaleRow r) => Helpers.makeRowCopy(
                        row: r,
                        newIdx: r.index + addIdx + 1,
                        newParentIdx: r.parent < 0 ? row.index : addIdx + r.parent + 1,
                        qty: r.qty,
                        seatNum: row.seatNumber,
                      ),
                    )
                    .toList()
                : newRows;

            if (addMod) row.hasChildren = true;

            final Option<ServiceError> countResult = await _itemService.subtractFromItemCountUsingSaleRows(addRows);
            if (countResult.isSome()) {
              _notificationService.error(
                countResult.match(
                  (t) => t.message,
                  () => "Error updating item count!",
                ),
              );
              return;
            }

            for (final SaleRow sr in addRows) {
              currentRows.insert(sr.index, sr);
            }

            for (int i = addIdx; i < currentRows.length; i++) {
              if (currentRows[i].index > i) {
                final int difference = currentRows[i].index - i;
                if (currentRows[i].parent > addIdx) {
                  currentRows[i].parent = currentRows[i].parent - difference;
                }
                currentRows[i].index = i;
              } else if (currentRows[i].index < i) {
                final int difference = i - currentRows[i].index;
                if (currentRows[i].parent > addIdx) {
                  currentRows[i].parent = currentRows[i].parent + difference;
                }
                currentRows[i].index = i;
              }
            }
          }
          /////////////////////////////////////////////////////////////

          ///
          ///
          /// PREP PRINT
          /////////////////////////////////////////////////////////////
          final List<SaleRow> topList = selectedList.length > 1 ? selectedList : <SaleRow>[Helpers.getParentRow(selected, currentRows)];
          final List<SaleRow> topChildren = <SaleRow>[];
          for (final SaleRow r in topList) {
            topChildren.addAll(Helpers.getChildRows(r, currentRows));
          }

          if (prepList.isNotEmpty) {
            final List<PrepDevice> devList = prepList
                .where(
                  (PrepDevice pd) =>
                      topChildren.firstWhereOrNull(
                        (SaleRow c) => c.flags.contains(SaleRowFlags.PREP_PRINTED.index) && c.prep & (1 << pd.idx) == (1 << pd.idx),
                      ) !=
                      null,
                )
                .toList();
            for (final PrepDevice dev in devList) {
              printSales[dev.idx] = Sale(
                sale: sale.sale,
                sale_number: sale.sale_number,
                document: SaleDocument(
                  saleHeader: Helpers.makeDeepSaleHeaderCopy(sale),
                  saleRows: sale.document.saleRows
                      .where(
                        (SaleRow r) => (r.prep & (1 << dev.idx) == (1 << dev.idx)) && r.isVisible,
                      )
                      .toList(),
                ),
                created_by: sale.created_by,
                updated_by: sale.updated_by,
              );
              final PrintJob printJob = PrinterInterface.buildPrepVoided(
                sale: sale,
                voidedRows: voidedList,
                addedRows: newRows,
                headerText: saleName.value,
                currentEmployee: _identityService.currentEmployee,
                printNum: dev.type == PrepDeviceType.TerminalRecieptPrint.prepDeviceTypeIndex ? 0 : dev.idx + 1,
                parentRows: topList.where((SaleRow r) => r.prep & (1 << dev.idx) == (1 << dev.idx)).toList(),
              );
              promiseList.add(
                _printerService.printPrep(
                  printJob: printJob,
                  prepDevice: dev,
                  errMap: errMap,
                ),
              );
              for (final SaleRow r in topChildren) {
                if (!r.flags.contains(SaleRowFlags.PREP_PRINTED.index)) {
                  r.flags.add(SaleRowFlags.PREP_PRINTED.index);
                }
              }
            }
          }
          /////////////////////////////////////////////////////////////

          ///
          ///
          /// WRAP UP
          /////////////////////////////////////////////////////////////
          refreshCurrentSaleModels();

          /// Upsert would be in finally block but it's sfae to put here in the wrap-up portion of function
          final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
            sale: sale,
            employee: _identityService.currentEmployee,
            terminalNumber: _identityService.terminalNumber,
          );

          upsertRes.match((ServiceError e) => throw e.message, (Sale s) => null);

          final List<PrepReturn> printResults = await Future.wait(promiseList);

          if (voidedList.isNotEmpty) {
            unawaited(
              _activityService.insertActivity(
                activity: Activity(
                  activity: ActivityFlags.ERROR_CORRECT.index,
                  emp_id: _identityService.currentEmployee.id,
                  term_num: _identityService.terminalNumber,
                  sale_num: sale.sale_number,
                  str_data: "Voided ${voidedList.length} modifier(s)",
                  short_data: ErrorCorrectTypes.CHANGED_MODIFIERS.index,
                ),
              ),
            );
          }

          if (errMap.isNotEmpty) {
            Helpers.blockAutoSignout = true;
            await Get.defaultDialog(
              title: "Prep Alerts for ${saleName.value} #${sale.sale_number}",
              content: PrepErrorsDialog(
                errMap: errMap,
                sales: printSales,
                sale: sale,
                showRetryBttn: printResults.any((PrepReturn r) => r.printTime == null),
              ),
            );
            Helpers.blockAutoSignout = false;
            Helpers.lastActivityTime.value = DateTime.now();
          }
          /////////////////////////////////////////////////////////////
        },
        () => _notificationService.error("No active ${saleName.toLowerCase()}!"),
      );
    } catch (err, stack) {
      _logger.severe(
        "Error ${addMod ? "adding" : "editing"} modifiers.",
        err,
        stack,
      );
      _notificationService.error("Error ${addMod ? "adding" : "editing"} modifiers");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleSplitUnsplitItems() async {
    await currentSale.value.match(
      (Sale sale) async {
        try {
          List<SaleRow> selectedRows = sale.document.saleRows
              .where(
                (SaleRow r) => r.selected && !r.flags.contains(SaleRowFlags.VOIDED.index),
              )
              .toList();
          if (selectedRows.isEmpty) return _notificationService.error("No active items selected!");
          List<int> selectedSeats = <int>[];

          if (selectedRows.firstWhereOrNull((SaleRow r) => r.splitData == null && r.prep > 0 && !r.flags.contains(SaleRowFlags.PREP_PRINTED.index)) !=
              null) {
            return _notificationService.error("Can't split items that still need prepped");
          }

          if (selectedRows.firstWhereOrNull((SaleRow r) => r.splitData == null && r.liquorToPour.isNotEmpty) != null) {
            return _notificationService.error("Can't split items with liquor that needs poured");
          }

          if (selectedRows.first.parent >= 0) {
            selectedRows = <SaleRow>[Helpers.getParentRow(selectedRows.first, sale.document.saleRows)];
          }

          /// If one or more selected row will be split show seat select dialog
          if (selectedRows.firstWhereOrNull((SaleRow r) => r.splitData == null) != null) {
            await Get.defaultDialog(
              title: "Select Seats to Split to",
              content: const SeatSelectDialog(SeatSelectType.SPLIT_ITEM),
            ).then((value) {
              if (value is List<int>) {
                selectedSeats = value;
              }
            });
            if (selectedSeats.isEmpty) return;
          }

          final List<String> alreadyRemoved = [];

          for (final SaleRow row in selectedRows) {
            /// If row has not been split split it to selected seats
            if (row.splitData == null) {
              _splitRow(sale, selectedSeats, row);

              /// If row has already been split unsplit it
            } else {
              if (!alreadyRemoved.contains(row.splitData!.key)) {
                alreadyRemoved.add(row.splitData!.key);
                _unsplitRow(sale, row);
              }
            }
          }
          refreshCurrentSaleModels();
        } finally {
          await upsertCurrentSale();
        }
      },
      () async => _notificationService.error("No active ${saleName.toLowerCase()}!"),
    );
  }

  void _splitRow(Sale sale, List<int> selectedSeats, SaleRow row) {
    final List<SaleRow> children = Helpers.getChildRows(row, sale.document.saleRows);
    final String key = String.fromCharCodes(List.generate(5, (index) => Random().nextInt(33) + 89));
    row.splitData = SplitData(qty: selectedSeats.length + 1, key: key);

    for (final seat in selectedSeats) {
      for (final SaleRow child in children) {
        final SaleRow newCopy = Helpers.makeRowCopy(
          row: child,
          newIdx: sale.document.saleRows.length,
          qty: child.qty,
          seatNum: seat,
          withPrepPrintedFlag: true,
        );
        newCopy.flags.add(SaleRowFlags.SPLIT_ITEM.index);
        sale.document.saleRows.add(newCopy);
        if (child.parent >= 0 && !child.flags.contains(SaleRowFlags.SPLIT_ITEM.index)) {
          child.flags.add(SaleRowFlags.SPLIT_PARENT.index);
        }
      }
    }
    row.flags.add(SaleRowFlags.SPLIT_PARENT.index);
  }

  void _unsplitRow(Sale sale, SaleRow row) {
    final List<SaleRow> splitRows = sale.document.saleRows
        .where(
          (SaleRow r) => r.splitData?.key == row.splitData!.key && r.parent < 0,
        )
        .toList();
    if (splitRows.length == row.splitData!.qty) {
      final SaleRow splitParent = splitRows.firstWhere((SaleRow r) => r.flags.contains(SaleRowFlags.SPLIT_PARENT.index) && r.parent < 0);
      removeSaleRows(
        splitRows.where((SaleRow r) => !r.flags.contains(SaleRowFlags.SPLIT_PARENT.index)).toList(),
        sale.document.saleRows,
      );
      refreshCurrentSaleModels();
      splitParent.splitData = null;
      final List<SaleRow> children = Helpers.getChildRows(splitParent, sale.document.saleRows);
      for (final SaleRow child in children) {
        child.flags.removeWhere((int f) => f == SaleRowFlags.SPLIT_PARENT.index);
      }
    } else {
      _notificationService.error("Unable to unsplit incomplete items");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addSaleRowToCart({
    required SaleRow saleRow,
    Sale? newSale,
    int? seatNum,
    bool showOnPole = true,
    List<String> liquorList = const <String>[],
    int? liquorPLU,
    Item? parentItem,
  }) async {
    try {
      // TODO:
      // - pull in the department tax flag here
      // - parse the flag into a bit array
      // - compare the array to saleFlags json_record
      // - add the tax percentages together and then attach to sale row

      if (currentSaleMode.value == SaleMode.NEGATIVE_SALE) {
        saleRow.basePrice = saleRow.basePrice * -1;
        saleRow.cashBasePrice = saleRow.cashBasePrice * -1;
        saleRow.originalPrice = saleRow.originalPrice * -1;
        saleRow.cashOriginalPrice = saleRow.cashOriginalPrice * -1;
        saleRow.actualPrice = saleRow.actualPrice * -1;
        saleRow.cashPrice = saleRow.cashPrice * -1;
        saleRow.creditPrice = saleRow.creditPrice * -1;
      }
      final Sale sale = await currentSale.value.match((Sale sale) async {
        return sale;
      }, () async {
        newSale ??= await makeNewSale();

        if (newSale == null) return Sale.empty();

        newSale!.document.saleHeader.currentTerminalNumber = _identityService.terminalNumber;

        final Either<ServiceError, Sale> upsertResult = await _saleService.upsert(
          employee: _identityService.currentEmployee,
          sale: newSale!,
          terminalNumber: _identityService.terminalNumber,
        );

        return upsertResult.fold(
          (ServiceError error) {
            throw error;
          },
          (Sale sale) => sale,
        );
      });

      if (sale.sale_number < 1) return;

      final bool hasLiquorControl = (liquorPLU != null || liquorList.isNotEmpty) && !saleRow.flags.contains(SaleRowFlags.LIQUOR_CONTROLLED.index);

      if (hasLiquorControl) {
        final List<String> liquorAdd = [
          if (liquorPLU != null) saleRow.item,
          if (liquorList.isNotEmpty) ...liquorList,
        ];
        if (liquorAdd.isNotEmpty) {
          final List<Item> items = (await _itemService.getItemFromListUUID(itemUUIDList: liquorAdd)).match(
            (ServiceError e) {
              _notificationService.error(e.message);
              return <Item>[];
            },
            (List<Item> l) => l,
          );
          for (int i = 0; i < saleRow.qty; i++) {
            final int itemNum = await _liquorControlService.getNewLiquorToPourItemNum(
              sale: sale.sale,
              item: parentItem?.item ?? saleRow.item,
            );
            for (final Item item in items) {
              final int itemQty = liquorAdd.where((String s) => s == item.item).length;
              for (int i = 0; i < itemQty; i++) {
                (await _liquorControlService.insertLiquorToPour(
                  LiquorToPour(
                    liquor_to_pour: "",
                    sale: sale.sale,
                    item: parentItem?.item ?? saleRow.item,
                    liq_ctl_plu: item.liq_ctl_plu ?? 0,
                    created_at: DateTime.now(),
                    created_by: _identityService.currentEmployee.employee,
                    item_num: itemNum,
                  ),
                ))
                    .match(
                  (l) => _notificationService.error(l.message),
                  (r) => saleRow.liquorToPour = [...saleRow.liquorToPour, r.liquor_to_pour],
                );
              }
            }
          }
          saleRow.flags.add(SaleRowFlags.LIQUOR_CONTROLLED.index);
          _notificationService.success("Liquor Added to Pour");
        }
      }

      if (saleRow.basePrice < 0 && !saleRow.flags.contains(SaleRowFlags.REFUNDED.index)) {
        saleRow.flags.add(SaleRowFlags.REFUNDED.index);
      }

      final List<SaleRow> existingRows = saleRow.hasChildren ? <SaleRow>[] : _findExistingRows(saleRow, seatNum, sale);

      if (existingRows.isNotEmpty) {
        existingRows.first.qty += saleRow.qty;
        currentSale.value.match(
          (Sale sale) {
            for (final SaleRowDiscount discount in existingRows.first.discounts) {
              sale.document.saleHeader.discountTotal += discount.value;
            }
          },
          () => _notificationService.error("Something went wrong adding discounted item!"),
        );
      } else {
        saleRow.index = sale.document.saleRows.length;
        if (seatNum != null) {
          saleRow.seatNumber = seatNum;
        } else if (selectedSeat.value != 0) {
          saleRow.seatNumber = selectedSeat.value;
        } else {
          saleRow.seatNumber = _getDefaultSeat(sale);
        }
        sale.document.saleRows.add(saleRow);

        if (selectedSeat.value == 0) {
          Future<void>.delayed(const Duration(milliseconds: 100), () {
            SaleItems.scrollToBottom();
          });
        }
      }

      sale.document.saleHeader.currentTerminalNumber = _identityService.terminalNumber;

      currentSale.value = some(sale);

      refreshCurrentSaleModels();

      globalQuantity.value = 1;

      if (showOnPole) {
        _logger.info("sale total from addSaleRowToCart: \$${Helpers.formatCurrency(sale.document.saleHeader.total)}");
        await _poleDisplayService.writeSaleRow(
          saleRow: existingRows.isNotEmpty ? existingRows.first : Helpers.getParentRow(saleRow, sale.document.saleRows),
          sale: sale,
        );
      }

      if (hasLiquorControl) {
        (await _saleService.upsert(
          employee: _identityService.currentEmployee,
          sale: sale,
          terminalNumber: _identityService.terminalNumber,
        ))
            .match(
          (ServiceError l) => _notificationService.error(l.message),
          (Sale r) => currentSale.value = some(r),
        );
      }
    } catch (error) {
      _notificationService.error(error.toString());
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  List<SaleRow> _findExistingRows(SaleRow saleRow, int? seatNum, Sale sale) {
    final List<int> restrictedFlags = [
      SaleRowFlags.OPEN_PRICE.index,
      SaleRowFlags.GIFT.index,
      SaleRowFlags.RANDOM_WEIGHT.index,
      SaleRowFlags.DISCOUNT.index,
      SaleRowFlags.WEIGHED.index,
    ];
    if (saleRow.liquorToPour.isNotEmpty ||
        saleRow.parent >= 0 ||
        saleRow.splitData != null ||
        saleRow.flags.firstWhereOrNull((int f) => restrictedFlags.contains(f)) != null) {
      return <SaleRow>[];
    }
    return sale.document.saleRows.where((SaleRow r) {
      return r.flags.contains(SaleRowFlags.VOIDED.index) == saleRow.flags.contains(SaleRowFlags.VOIDED.index) &&
          r.flags.contains(SaleRowFlags.PREP_PRINTED.index) == saleRow.flags.contains(SaleRowFlags.PREP_PRINTED.index) &&
          r.splitData == null &&
          (r.item == saleRow.item) &&
          (r.parent < 0) &&
          r.flags.firstWhereOrNull((int f) => restrictedFlags.contains(f)) == null &&
          r.hasChildren == saleRow.hasChildren &&
          r.receiptDescription == saleRow.receiptDescription &&
          r.isVisible &&
          r.flags.contains(SaleRowFlags.COMMENT.index) == saleRow.flags.contains(SaleRowFlags.COMMENT.index) &&
          r.flags.contains(SaleRowFlags.PRICE_SCHEDULE.index) == saleRow.flags.contains(SaleRowFlags.PRICE_SCHEDULE.index) &&
          (selectedSeat.value == 0 ? (r.seatNumber) == (seatNum ?? _getDefaultSeat(sale)) : (r.seatNumber) == selectedSeat.value) &&
          (r.basePrice == saleRow.basePrice) &&
          (r.cashBasePrice == saleRow.cashBasePrice) &&
          (r.liquorToPour.isEmpty);
    }).toList();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void checkForDuplicates(SaleRow createdRow) {
    currentSale.value.match(
      (Sale sale) {
        final List<SaleRow> currentRows = sale.document.saleRows;
        final SaleRow parent = Helpers.getParentRow(createdRow, currentRows);
        final List<SaleRow> parentChildren = Helpers.getChildRows(parent, currentRows);
        parentChildren.sort((SaleRow a, SaleRow b) => a.index.compareTo(b.index));
        parentChildren.sort((SaleRow a, SaleRow b) => a.item.compareTo(b.item));
        final String parentString1 = parentChildren.map((e) => e.item).join("-");
        final String parentString2 = parentChildren.map((e) => e.basePrice).join("-");
        final String parentString3 = parentChildren.map((e) => e.flags.contains(SaleRowFlags.PRICE_SCHEDULE.index)).join("-");
        final String parentString4 = parentChildren.map((e) => e.receiptDescription).join("-");
        final String parentString5 = parentChildren.map((e) => e.flags.contains(SaleRowFlags.VOIDED.index)).join("-");
        final String parentString6 = parentChildren.map((e) => e.flags.contains(SaleRowFlags.COMMENT.index)).join("-");
        final String parentString7 = parentChildren.map((e) => e.liquorToPour.join()).join("-");

        bool foundMatch = false;

        final List<SaleRow> existingRows = _findExistingRows(parent, parent.seatNumber, sale).where((SaleRow r) => r.index != parent.index).toList();

        for (final SaleRow existing in existingRows) {
          final List<SaleRow> existingChildren = Helpers.getChildRows(existing, currentRows);
          existingChildren.sort((SaleRow a, SaleRow b) => a.index.compareTo(b.index));
          existingChildren.sort((SaleRow a, SaleRow b) => a.item.compareTo(b.item));
          final String existingString1 = existingChildren.map((e) => e.item).join("-");
          final String existingString2 = existingChildren.map((e) => e.basePrice).join("-");
          final String existingString3 = existingChildren.map((e) => e.flags.contains(SaleRowFlags.PRICE_SCHEDULE.index)).join("-");
          final String existingString4 = existingChildren.map((e) => e.receiptDescription).join("-");
          final String existingString5 = existingChildren.map((e) => e.flags.contains(SaleRowFlags.VOIDED.index)).join("-");
          final String existingString6 = existingChildren.map((e) => e.flags.contains(SaleRowFlags.COMMENT.index)).join("-");
          final String existingString7 = existingChildren.map((e) => e.liquorToPour.join()).join("-");

          if (parentString1 == existingString1 &&
              parentString2 == existingString2 &&
              parentString3 == existingString3 &&
              parentString4 == existingString4 &&
              parentString5 == existingString5 &&
              parentString6 == existingString6 &&
              parentString7 == existingString7) {
            foundMatch = true;
            for (final SaleRow child in existingChildren) {
              for (final SaleRowDiscount discount in child.discounts) {
                sale.document.saleHeader.discountTotal += discount.value;
              }
              child.qty += createdRow.qty;
            }
            break;
          }
        }

        if (foundMatch) {
          removeSaleRows([parent], currentRows);
          refreshCurrentSaleModels();
        }
      },
      () => null,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<SaleRow> modObjToSaleRow({
    required ModObject obj,
    required Map<String, int> pricing,
    int rowIdx = 0,
    int parentIdx = -1,
    int qty = 1,
    bool? forceVisible,
  }) async {
    final String pKey = await findItemCurrentPrice(pricing, 0, obj.item.long_desc);
    final int cashPrice = getDualPrice(pricing, pKey);
    final bool takeOutSurcharge = obj.item.document.takeOutSurcharge;

    forceVisible ??= false;

    return SaleRow(
      index: rowIdx,
      parent: parentIdx,
      receiptDescription: obj.item.document.receiptDesc ?? obj.item.long_desc,
      department: obj.item.departmentByDepartment!.title,
      flags: <int>[
        if (obj.item.departmentByDepartment!.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
        if (obj.item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
        if (obj.item.document.promptForPrice) SaleRowFlags.OPEN_PRICE.index,
        if (pKey[1] != "0") SaleRowFlags.PRICE_SCHEDULE.index,
        if ((obj.item.document.printInRed ?? false) || (obj.item.departmentByDepartment?.document.printInRed ?? false)) SaleRowFlags.PRINT_RED.index,
      ],
      item: obj.item.item,
      upc: obj.item.upc,
      transactionFlags: <int>[],
      originalPrice: pricing["S0L0C0"] ?? 0,
      cashOriginalPrice: cashPrice,
      basePrice: pricing[pKey] ?? 0,
      cashBasePrice: cashPrice,
      taxFlags: obj.item.departmentByDepartment!.document.taxFlags,
      origTaxFlags: obj.item.departmentByDepartment!.document.taxFlags,
      itemPricing: pricing,
      isVisible: forceVisible || obj.isSelection || !obj.item.document.isModifier || obj.item.document.modifiers.isEmpty || (pricing[pKey] ?? 0) > 0,
      hasChildren: obj.item.document.modifiers.isNotEmpty,
      prep: obj.item.document.prep != 0 ? obj.item.document.prep : obj.item.departmentByDepartment?.document.prep ?? 0,
      qty: qty,
      takeOutSurcharge: takeOutSurcharge,
      printSeparate: obj.item.document.printSeparate,
    );
  }

  ///
  ///
  ///
  ///
  ///
  /// Starts a new completely empty sale. This is used to create a base sale to add non-item properties to
  Future<bool> startEmptySale() async {
    try {
      final Sale? newSale = await makeNewSale();

      if (newSale == null) return false;

      newSale.document.saleHeader.currentTerminalNumber = _identityService.terminalNumber;

      final Either<ServiceError, Sale> upsertResult = await _saleService.upsert(
        employee: _identityService.currentEmployee,
        sale: newSale,
        terminalNumber: _identityService.terminalNumber,
      );

      upsertResult.fold(
        (ServiceError error) {
          throw error;
        },
        (Sale sale) {
          clearSaleFields();
          currentSale.value = some(sale);
        },
      );

      return true;
    } catch (error) {
      _notificationService.error(error.toString());
      return false;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _handleScanResult(String output) async {
    Option<String> upc = some(output);

    Option<int> randomWeightPrice = none();

    final Option<bool> isRandomWeight = some(output.startsWith("2"));

    // starting digit of 2 is a random weighted item
    if (isRandomWeight.getOrElse(() => false)) {
      if (output.length == 13) {
        upc = some(int.parse(output.substring(1, 6)).toString());
        randomWeightPrice = some(int.parse(output.substring(7, 12)));
      } else {
        upc = some(int.parse(output.substring(1, 6)).toString());
        randomWeightPrice = some(int.parse(output.substring(7, 11)));
      }

      if (upc.getOrElse(() => "0") == "0") {
        _notificationService.error("Invalid item UPC!");
      }
    }

    upc.match(
      (String upcString) async {
        final Either<ServiceError, Item> itemResult = await _itemService.getItemFromUPC(code: upc.getOrElse(() => ""));

        await itemResult.fold(
          (ServiceError error) async {
            try {
              List<String> beepConfigList = ["-l", "300", "-f", "2100", "-d", "50", "-r", "3"];
              final String? beepConfigEnv = Platform.environment["BEEP_CONFIG"];
              if (beepConfigEnv != null) {
                beepConfigList = beepConfigEnv.split(" ");
              }

              // best settings:
              // -l 300 (length/duration)
              // -f 2100 (frequency)
              // -d 50 (delay)
              // -r 3 (replications)
              // https://manpages.debian.org/buster/beep/beep.1.en.html
              _logger.info("beep config: $beepConfigList");
              final ProcessResult audioResult = await Process.run("beep", beepConfigList);

              if (audioResult.exitCode != 0) {
                _logger.warning("Failed to play audio tone for failed item lookup: stdout: ${audioResult.stdout} stderr: ${audioResult.stderr}");
              }
            } catch (e) {
              _logger.shout("Error playing audio tone for failed item lookup", e);
            }

            _notificationService.error(error.message);
          },
          (Item item) async {
            item.document.pricing["S0L0C0"] ??= 0;
            final String priceKey = await findItemCurrentPrice(item.document.pricing, 0, item.long_desc);
            final int cashPrice = getDualPrice(item.document.pricing, priceKey);

            if (isRandomWeight.getOrElse(() => false)) {
              // double itemWeight = 1.0;
              // itemWeight = randomWeightPrice.getOrElse(() => 1) / item.price;
              // itemWeight = Helpers.roundDouble(itemWeight, 2);

              await addSaleRowToCart(
                saleRow: SaleRow(
                  item: item.item,
                  upc: item.upc,
                  receiptDescription: item.document.receiptDesc ?? item.long_desc,
                  department: item.departmentByDepartment!.title,
                  transactionFlags: [],
                  flags: [
                    if (item.departmentByDepartment?.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
                    if (item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
                    SaleRowFlags.RANDOM_WEIGHT.index,
                    if ((item.document.printInRed ?? false) || (item.departmentByDepartment?.document.printInRed ?? false))
                      SaleRowFlags.PRINT_RED.index,
                  ],
                  basePrice: randomWeightPrice.getOrElse(() => 0),
                  cashBasePrice: randomWeightPrice.getOrElse(() => 0),
                  originalPrice: item.document.pricing[priceKey]!,
                  cashOriginalPrice: cashPrice,
                  taxFlags: item.departmentByDepartment!.document.taxFlags,
                  origTaxFlags: item.departmentByDepartment!.document.taxFlags,
                  qty: globalQuantity.value,
                  // UOM: item.document.UOM,
                  // weight: itemWeight,
                ),
                liquorList: item.document.liquorList,
                liquorPLU: item.liq_ctl_plu,
              );
            } else {
              await addSaleRowToCart(
                saleRow: SaleRow(
                  item: item.item,
                  upc: item.upc,
                  receiptDescription: item.document.receiptDesc ?? item.long_desc,
                  department: item.departmentByDepartment!.title,
                  transactionFlags: [],
                  flags: [
                    if (item.departmentByDepartment?.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
                    if (item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
                    SaleRowFlags.SCANNED.index,
                    if ((item.document.printInRed ?? false) || (item.departmentByDepartment?.document.printInRed ?? false))
                      SaleRowFlags.PRINT_RED.index,
                  ],
                  basePrice: item.document.pricing[priceKey]!,
                  cashBasePrice: cashPrice,
                  originalPrice: item.document.pricing[priceKey]!,
                  cashOriginalPrice: cashPrice,
                  taxFlags: item.departmentByDepartment!.document.taxFlags,
                  origTaxFlags: item.departmentByDepartment!.document.taxFlags,
                  qty: globalQuantity.value,
                ),
                liquorList: item.document.liquorList,
                liquorPLU: item.liq_ctl_plu,
              );
            }
          },
        );
      },
      () => _notificationService.error("Failed to find an item!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handleBergScan(int output) async {
    try {
      if (_identityService.terminalBergType == BergType.NONE.index) {
        return _notificationService.error("Liquor Control not active!");
      }
      if (_identityService.terminalBergMethod == BergItemMaintenance.RING_THEN_POUR.index || liqCtlMenuOpen) {
        if (selectedPourItem.isNotEmpty) _checkForLtpSelection();

        if (!liqCtlMenuOpen) {
          _liquorControlService.pourLiquor(false);
          return _notificationService.error("Open liquor control menu to pour rung up items!");
        }

        LiquorToPour? liqMatch;

        if (selectedPourItem.value.isEmpty) {
          final List<int> saleKeys = liquorToPour.keys.toList();
          saleKeys.sort((int a, int b) => a.compareTo(b));

          for (final int saleKey in saleKeys) {
            for (final String itemKey in liquorToPour[saleKey]!.keys) {
              for (final LiquorToPour l in liquorToPour[saleKey]![itemKey]!) {
                if (l.liq_ctl_plu == output) {
                  liqMatch = l;
                  break;
                }
              }
              if (liqMatch != null) break;
            }
            if (liqMatch != null) break;
          }
        } else {
          final List<String> selectSplit = selectedPourItem.value.split(" ");

          if (selectSplit.length == 1) {
            final int saleKey = int.parse(selectSplit[0]);
            for (final String itemKey in liquorToPour[saleKey]!.keys) {
              for (final LiquorToPour l in liquorToPour[saleKey]![itemKey]!) {
                if (l.liq_ctl_plu == output) {
                  liqMatch = l;
                  break;
                }
              }
              if (liqMatch != null) break;
            }
          } else {
            liqMatch = liquorToPour[int.parse(selectSplit[0])]![selectSplit[1]]!.firstWhereOrNull(
              (LiquorToPour l) => l.liq_ctl_plu == output && l.item_num.toString() == selectSplit[2],
            );
          }
        }

        if (liqMatch == null) {
          _liquorControlService.pourLiquor(false);
          return _notificationService.error("No matching liquor found for PLU $output!");
        }

        final LiquorToPour deleted = await removeLiqToPour(liqMatch);
        if (deleted.liquor_to_pour.isEmpty) {
          return _liquorControlService.pourLiquor(false);
        } else {
          await _updateSaleRowAfterPour(liqMatch);
          return _liquorControlService.pourLiquor(true);
        }
      } else {
        final Either<ServiceError, Item> itemResult = await _itemService.getItemFromUPC(
          code: output.toString(),
          berg: true,
        );

        final Item item = await itemResult.fold((ServiceError e) => throw e.message, (Item i) => i);
        item.document.pricing["S0L0C0"] ??= 0;
        final String priceKey = await findItemCurrentPrice(item.document.pricing, null, item.long_desc);
        final int cashPrice = getDualPrice(item.document.pricing, priceKey);

        await addSaleRowToCart(
          saleRow: SaleRow(
            item: item.item,
            upc: item.upc,
            receiptDescription: item.document.receiptDesc ?? item.long_desc,
            department: item.departmentByDepartment!.title,
            transactionFlags: [],
            flags: [
              if (item.departmentByDepartment?.document.isTaxable ?? false) SaleRowFlags.TAXABLE.index,
              if (item.document.allowEbt) SaleRowFlags.ALLOW_EBT.index,
              SaleRowFlags.PREP_PRINTED.index,
              SaleRowFlags.LIQUOR_CONTROLLED.index,
              if ((item.document.printInRed ?? false) || (item.departmentByDepartment?.document.printInRed ?? false)) SaleRowFlags.PRINT_RED.index,
            ],
            basePrice: item.document.pricing[priceKey]!,
            cashBasePrice: cashPrice,
            originalPrice: item.document.pricing[priceKey]!,
            cashOriginalPrice: cashPrice,
            taxFlags: item.departmentByDepartment!.document.taxFlags,
            origTaxFlags: item.departmentByDepartment!.document.taxFlags,
          ),
          liquorList: item.document.liquorList,
        );

        (await _saleService.upsert(
          sale: currentSale.value.match((Sale s) => s, () => throw "Error updating $saleName"),
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        ))
            .match(
          (ServiceError e) => throw e.message,
          (Sale s) => null,
        );

        _liquorControlService.pourLiquor(true);
      }
    } catch (err, stack) {
      _liquorControlService.pourLiquor(false);
      _notificationService.error("Failed to find item with PLU $output!");
      _logger.severe(
        "Error finding item through liquor control",
        err,
        stack,
      );
    }
  }

  void _checkForLtpSelection() {
    bool hasSelection = false;
    final List<int> saleKeys = liquorToPour.keys.toList();
    for (final saleNum in saleKeys) {
      if (hasSelection) break;
      final List<String> splitSelect = selectedPourItem.value.split(" ");
      final Map<String, List<LiquorToPour>> saleMap = liquorToPour[saleNum]!;
      if (splitSelect.length == 1 && splitSelect[0] == saleNum.toString()) {
        hasSelection = true;
        break;
      }
      final List<String> itemKeys = saleMap.keys.toList();
      for (final String itemId in itemKeys) {
        if (hasSelection) break;
        final List<LiquorToPour> ltpList = saleMap[itemId]!;
        final List<int> numList = ltpList.map((LiquorToPour l) => l.item_num).toSet().toList();
        for (final int i in numList) {
          if (hasSelection) break;
          if (splitSelect.length > 2) {
            if (splitSelect[0] == saleNum.toString() && splitSelect[1] == itemId && i.toString() == splitSelect[2]) {
              hasSelection = true;
              break;
            }
          }
        }
      }
    }
    if (!hasSelection) selectedPourItem.value = "";
  }

  Future<void> _updateSaleRowAfterPour(LiquorToPour ltp) async {
    try {
      final Sale activeSale = currentSale.value.match((Sale s) => s, () => Sale.empty());
      final Sale saleToUpdate = activeSale.sale == ltp.sale
          ? activeSale
          : (await _saleService.getSaleByPk(ltp.sale)).match((ServiceError e) => throw e.message, (Sale s) => s);
      for (final SaleRow row in saleToUpdate.document.saleRows) {
        if (row.liquorToPour.contains(ltp.liquor_to_pour)) row.liquorToPour.remove(ltp.liquor_to_pour);
        if (!row.flags.contains(SaleRowFlags.LIQUOR_POURED.index)) row.flags.add(SaleRowFlags.LIQUOR_POURED.index);
      }
      if (activeSale.sale == ltp.sale) return currentSale.refresh();
      await _saleService.upsert(
        sale: saleToUpdate,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );
    } catch (err, stack) {
      _notificationService.error("Error updating sale after liquor pour!");
      _logger.severe(
        "Error updating sale after liquor pour!",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> openLiqCtlMenu() async {
    try {
      await fetchLiquorToPour();
      liqCtlMenuOpen = true;
      await Get.bottomSheet(
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: LiquorCtlDialog(),
        ),
        isScrollControlled: true,
      );
      selectedPourItem.value = "";
      liqCtlMenuOpen = false;
    } catch (err, stack) {
      _notificationService.error("Error opening liquor control menu!");
      _logger.severe(
        "Error opening liquor control menu!",
        err,
        stack,
      );
    }
  }

  Future<void> fetchLiquorToPour() async {
    liquorToPour.value = await (await _liquorControlService.getLiquorToPour()).match(
      (ServiceError e) async => throw e.message,
      (List<LiquorToPour> resList) async {
        final List<Item> pluItemList =
            (await _itemService.getItemFromListPlu(pluList: resList.map((LiquorToPour ltp) => ltp.liq_ctl_plu).toSet().toList())).match(
          (ServiceError e) {
            _notificationService.error(e.message);
            return <Item>[];
          },
          (List<Item> r) => r,
        );
        ltpMap.clear();
        for (final Item i in pluItemList) {
          if (i.liq_ctl_plu != null) ltpMap[i.liq_ctl_plu!] = i;
        }
        final Map<int, Map<String, List<LiquorToPour>>> newMap = {};
        for (final LiquorToPour ltp in resList) {
          final int saleNum = ltp.saleBySale?.sale_number ?? 0;
          if (newMap[saleNum] == null) {
            newMap[saleNum] = {
              ltp.item: [],
            };
          } else if (newMap[saleNum]![ltp.item] == null) {
            newMap[saleNum]![ltp.item] = [];
          }
          newMap[saleNum]![ltp.item]!.add(ltp);
        }
        return newMap;
      },
    );
  }

  Future<LiquorToPour> removeLiqToPour(LiquorToPour ltp) async {
    try {
      final LiquorToPour deleted = (await _liquorControlService.deleteLiquorToPourByPK([ltp.liquor_to_pour])).match(
        (ServiceError e) => throw e.message,
        (List<LiquorToPour> l) {
          if (l.isEmpty) throw "Unable to find liquor to pour!";
          return l.first;
        },
      );
      await fetchLiquorToPour();
      if (deleted.sale.isNotEmpty) {
        if (liquorToPour[deleted.saleBySale?.sale_number ?? 0] == null) {
          selectedPourItem.value = "";
        } else if (liquorToPour[deleted.saleBySale?.sale_number ?? 0]![deleted.item] == null) {
          selectedPourItem.value = "";
        }
      }
      return deleted;
    } catch (err, stack) {
      _notificationService.error("Error pouring liquor!");
      _logger.severe(
        "Error pouring liquor!",
        err,
        stack,
      );
      return LiquorToPour.empty();
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void openRefund(String refundNumber, String refundBatch) {
    autoRefund.value = true;
    autoRefundNumber.value = refundNumber;
    autoRefundBatch.value = refundBatch;
    // void openRefund(String refundNumber, String refundBatch) {
    //   autoRefund.value = true;
    //   autoRefundNumber.value = refundNumber;
    //   autoRefundBatch.value = refundBatch;

    //   _registerController.scaffoldKey.currentState?.openEndDrawer();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void _changeRowSeat(SaleRow saleRow, int seat) {
    currentSale.value.match(
      (Sale sale) {
        final SaleRow parent = Helpers.getParentRow(saleRow, sale.document.saleRows);

        final List<SaleRow> childRows = Helpers.getChildRows(parent, sale.document.saleRows);

        for (final SaleRow sr in childRows) {
          sr.seatNumber = seat;
        }

        checkForDuplicates(parent);
        refreshCurrentSaleModels();
        return;
      },
      () => _notificationService.error("No active sale!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _changeRowSeatWithQuantity(SaleRow saleRow, int seat) async {
    await currentSale.value.match(
      (Sale sale) async {
        if (saleRow.seatNumber == seat) return;
        final SaleRow parent = saleRow.parent > -1 ? Helpers.getParentRow(saleRow, sale.document.saleRows) : saleRow;
        await Get.dialog(
          QuantitySelectDialog(
            saleRow: parent,
            title: "Change Seat Quantity",
          ),
        ).then((dynamic value) async {
          if (value is! int) {
            return;
          }
          if (value < 1) return;
          if (value >= parent.qty) return _changeRowSeat(parent, seat);
          final List<SaleRow> children = Helpers.getChildRows(parent, sale.document.saleRows);
          for (final SaleRow child in children) {
            child.qty -= value;
          }
          for (final SaleRow child in children) {
            await addRowCopy(
              row: child,
              newIdx: sale.document.saleRows.length,
              qty: value,
              seatNum: seat,
              withPrepPrintedFlag: true,
            );
          }
          refreshCurrentSaleModels();
        });
      },
      () async => _notificationService.error("No active sale!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addToQty({
    int addQty = 1,
    int? newSeat,
    required List<SaleRow> rows,
    required Sale sale,
    bool withPrepPrintedFlag = true,
  }) async {
    for (final SaleRow sr in rows) {
      final SaleRow parent = rows.first.parent >= 0
          ? Helpers.getParentRow(
              rows.first,
              sale.document.saleRows,
            )
          : sr;
      final List<SaleRow> childRows = Helpers.getChildRows(parent, sale.document.saleRows);

      final List<int> toRemove = <int>[];

      for (final SaleRow child in childRows) {
        if (child.flags.contains(SaleRowFlags.VOIDED.index)) {
          toRemove.add(sale.document.saleRows.length);
        }
        await addRowCopy(
          row: child,
          newIdx: sale.document.saleRows.length,
          qty: addQty,
          seatNum: newSeat ?? child.seatNumber,
          withPrepPrintedFlag: withPrepPrintedFlag,
          updatePricing: true,
        );
      }
      if (toRemove.isEmpty) {
        checkForDuplicates(sale.document.saleRows.last);
      } else {
        for (final SaleRow sr in sale.document.saleRows) {
          if (toRemove.contains(sr.index)) {
            sale.document.saleRows.remove(sr);
            for (final SaleRow r in sale.document.saleRows) {
              if (r.index > sr.index) r.index = r.index - 1;
              if (r.parent > sr.parent && r.parent >= 0) r.parent = r.parent - 1;
            }
          }
        }
      }
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addRowCopy({
    required SaleRow row,
    required int newIdx,
    required int qty,
    int? seatNum,
    bool voidedCopy = false,
    bool withTransactionFlags = false,
    bool withPrepPrintedFlag = false,
    bool updatePricing = false,
  }) async {
    final SaleRow copy = Helpers.makeRowCopy(
      row: row,
      newIdx: newIdx,
      voidedCopy: voidedCopy,
      qty: qty,
      seatNum: seatNum,
      withTransactionFlags: withTransactionFlags,
      withPrepPrintedFlag: withPrepPrintedFlag,
    );
    List<String> liquorList = <String>[];
    int? liquorPLU;
    if (updatePricing) {
      await updateRowPricing(copy);
      if (_identityService.terminalBergType != BergType.NONE.index && row.flags.contains(SaleRowFlags.LIQUOR_CONTROLLED.index)) {
        (await _itemService.getItemFromUUID(itemUUID: row.item)).match(
          (ServiceError e) => _notificationService.error("Error getting liquor control data for ${row.receiptDescription}!"),
          (Item i) {
            liquorPLU = i.liq_ctl_plu;
            liquorList = i.document.liquorList;
          },
        );
      }
    }

    await addSaleRowToCart(
      saleRow: copy,
      seatNum: seatNum,
      showOnPole: false,
      liquorList: liquorList,
      liquorPLU: liquorPLU,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void removeSaleRows(List<SaleRow> saleRows, List<SaleRow> rowList) {
    final List<SaleRow> removedRows = <SaleRow>[];
    for (final SaleRow saleRow in saleRows) {
      if (removedRows.contains(saleRow)) continue;

      final SaleRow root = Helpers.getParentRow(saleRow, rowList);
      final List<SaleRow> childRows = Helpers.getChildRows(root, rowList);

      for (final SaleRow child in childRows) {
        rowList.remove(child);

        for (int i = child.index; i < rowList.length; i++) {
          final SaleRow row = rowList[i];
          row.index = row.index - 1;
          if (row.parent >= 0) {
            row.parent = row.parent - 1;
          }
        }

        removedRows.add(child);
      }
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> moveSaleRows({
    required Sale toSale,
    required List<SaleRow> items,
  }) async {
    try {
      currentSale.value.match(
        (Sale sale) async {
          if (items.isEmpty) return;
          if (items.first.parent >= 0) {
            items = [Helpers.getParentRow(items.first, sale.document.saleRows)];
          }
          for (final SaleRow item in items) {
            final List<SaleRow> childRows = Helpers.getChildRows(item, sale.document.saleRows);
            for (final SaleRow child in childRows) {
              final int currentCount = child.seatNumber < 0 ? toSale.document.saleHeader.toGoSeatCnt : toSale.document.saleHeader.seatCnt ?? 1;
              int newSeatNum = child.seatNumber.abs() > currentCount ? currentCount : child.seatNumber;
              if (child.seatNumber < 0) newSeatNum = newSeatNum * -1;
              if (toSale.document.saleHeader.seatsSettled.contains(newSeatNum)) {
                newSeatNum = _getDefaultSeat(toSale);
              }
              toSale.document.saleRows.add(
                Helpers.makeRowCopy(
                  row: child,
                  newIdx: toSale.document.saleRows.length,
                  withTransactionFlags: true,
                  qty: child.qty,
                  withPrepPrintedFlag: true,
                  seatNum: newSeatNum,
                ),
              );
            }
          }
          refreshSaleViewModels(toSale);
          final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
            sale: toSale,
            employee: _identityService.currentEmployee,
            terminalNumber: _identityService.terminalNumber,
          );
          upsertRes.match(
            (ServiceError e) => _notificationService.error("Error moving item(s): ${e.message}"),
            (Sale s) async {
              _notificationService.success("Item moved!");
              removeSaleRows(items, sale.document.saleRows);
              unawaited(updateSaleOnLiquorToPour(s));
              await _activityService.insertActivity(
                activity: Activity(
                  emp_id: _identityService.currentEmployee.id,
                  term_num: _identityService.terminalNumber,
                  activity: ActivityFlags.MOVE_ITEMS.index,
                  str_data: "${items.length} items moved to $saleName #${toSale.sale_number} from #${sale.sale_number}",
                  sale_num: sale.sale_number,
                  data1: toSale.sale_number,
                  data2: items.length,
                ),
              );

              /// only updates current sale if other sale was successfully updated
              refreshCurrentSaleModels();
              await upsertCurrentSale();
            },
          );
        },
        () => _notificationService.error("No active ${saleName.toLowerCase()}!"),
      );
    } catch (err, stack) {
      _notificationService.error("Error moving item(s)");
      _logger.severe(
        "Error moving item(s)",
        err,
        stack,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> selectSaleRow(SaleRow saleRow) async {
    if (selectedSeat.value != 0) {
      if (saleRow.qty > 1) {
        await _changeRowSeatWithQuantity(saleRow, selectedSeat.value);
      } else {
        _changeRowSeat(saleRow, selectedSeat.value);
      }
      deselectSeat();
    } else {
      if (saleRow.parent >= 0) {
        deselectAll();
      } else {
        deselectAll(modifiersOnly: true);
      }
      saleRow.selected = true;
    }
    await upsertCurrentSale();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void deselectSaleRow(SaleRow saleRow) {
    saleRow.selected = false;

    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addSeat() async {
    await currentSale.value.match(
      (Sale sale) async {
        if (sale.document.saleHeader.seatCnt == null) {
          sale.document.saleHeader.seatCnt = 2;
        } else {
          sale.document.saleHeader.seatCnt = sale.document.saleHeader.seatCnt! + 1;
        }
        if (custCountOption.value == 2) {
          sale.document.saleHeader.customerCount = sale.document.saleHeader.seatCnt ?? 1;
        }
        if (sale.document.saleHeader.tenders.isNotEmpty) {
          if (sale.document.saleHeader.seatsTendered.isEmpty) {
            for (int i = 1; i < sale.document.saleHeader.seatCnt!; i++) {
              if (!sale.document.saleHeader.seatsSettled.contains(i)) {
                sale.document.saleHeader.seatsTendered.add(i);
              }
            }
          }
        }
        refreshCurrentSaleModels();
        await upsertCurrentSale();
      },
      () async => await startEmptySale(),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addToGoSeat() async {
    await currentSale.value.match(
      (Sale sale) async {
        sale.document.saleHeader.toGoSeatCnt = sale.document.saleHeader.toGoSeatCnt + 1;
        if (sale.document.saleHeader.tenders.isNotEmpty) {
          if (sale.document.saleHeader.seatsTendered.isEmpty) {
            for (int i = 1; i < sale.document.saleHeader.toGoSeatCnt; i++) {
              if (!sale.document.saleHeader.seatsSettled.contains(i * -1)) {
                sale.document.saleHeader.seatsTendered.add(i * -1);
              }
            }
          }
        }
        refreshCurrentSaleModels();
        await upsertCurrentSale();
      },
      () async {
        if (await startEmptySale()) {
          await addToGoSeat();
        }
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  /// returns false if an error was returned from upsert function.
  ///
  /// specifically if there is no current sale it's not considered an error.
  Future<bool> upsertCurrentSale() async {
    return await currentSale.value.match(
      (Sale sale) async {
        return (await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        ))
            .fold(
          (l) {
            _notificationService.error("Error updating $saleName");
            return false;
          },
          (r) => true,
        );
      },
      () async => true,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> removeSeat() async {
    final int seat = selectedSeat.value;
    final bool togo = seat < 0;
    await currentSale.value.match(
      (Sale sale) async {
        final int currentCnt = togo ? sale.document.saleHeader.toGoSeatCnt : sale.document.saleHeader.seatCnt ?? 1;

        if (seat == 0 || seat.abs() > currentCnt) {
          _notificationService.error("No Seat Selected!");
          return;
        }

        int lowestSeatNum = seat < 0 ? -1 : 1;

        if (sale.document.saleHeader.seatsSettled.contains(lowestSeatNum)) {
          for (int i = 2; i < currentCnt + 1; i++) {
            lowestSeatNum = togo ? i * -1 : i;
          }
        }

        if (!togo && (currentCnt == 1 || seat == lowestSeatNum)) {
          return;
        }

        final List<SaleRow> rowsToMove = <SaleRow>[];

        final List<SaleRow> saleRows = sale.document.saleRows;
        for (final SaleRow row in saleRows) {
          if (row.seatNumber == seat) {
            rowsToMove.add(row);
          } else if ((togo && row.seatNumber < seat) || (!togo && row.seatNumber > seat)) {
            final int newNum = togo ? row.seatNumber + 1 : row.seatNumber - 1;
            if (sale.document.saleHeader.seatsSettled.contains(newNum)) {
              if (togo) {
                for (int i = newNum; i < 0; i++) {
                  if (!sale.document.saleHeader.seatsSettled.contains(i)) {
                    row.seatNumber = i;
                  }
                }
                if (row.seatNumber == newNum - 1) {
                  rowsToMove.add(row);
                }
              } else {
                for (int i = newNum; i > 0; i--) {
                  if (!sale.document.saleHeader.seatsSettled.contains(i)) {
                    row.seatNumber = i;
                  }
                }
                if (row.seatNumber == newNum + 1) {
                  rowsToMove.add(row);
                }
              }
            } else {
              row.seatNumber = newNum;
            }
          }
        }

        if (togo) {
          sale.document.saleHeader.toGoSeatCnt = sale.document.saleHeader.toGoSeatCnt - 1;
        } else {
          sale.document.saleHeader.seatCnt = sale.document.saleHeader.seatCnt! - 1;
        }

        final int defaultSeat = _getDefaultSeat(sale);

        for (final SaleRow row in rowsToMove) {
          _changeRowSeat(row, defaultSeat);
        }

        if (custCountOption.value == 2) {
          sale.document.saleHeader.customerCount = sale.document.saleHeader.seatCnt ?? 1;
        }
        deselectSeat();
      },
      () async => _notificationService.error("No active ${saleName.toLowerCase()}"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  int _getDefaultSeat(Sale sale) {
    int defaultSeat = sale.document.saleHeader.seatCnt ?? 1;
    if (sale.document.saleHeader.seatsSettled.contains(defaultSeat)) {
      for (int i = (sale.document.saleHeader.seatCnt ?? 1) - 1; i > 0; i--) {
        if (!sale.document.saleHeader.seatsSettled.contains(i)) {
          defaultSeat = i;
          break;
        }
      }
    }
    return defaultSeat;
  }

  ///
  ///
  ///
  ///
  ///
  /// no early return so we don't need a try/finally block here
  Future<void> selectSeat(int? seat) async {
    List<SaleRow> saleRows = <SaleRow>[];
    currentSale.value.match((Sale sale) {
      saleRows = sale.document.saleRows;
    }, () {
      _notificationService.error(
        "Something went wrong retreiving ${saleName.toLowerCase()} info!",
      );
      return;
    });

    final List<SaleRow> selectedRows = saleRows.where((SaleRow row) => row.selected).toList();

    if (selectedRows.isEmpty) {
      selectedSeat.value = seat ?? 1;
    } else {
      for (final SaleRow row in selectedRows) {
        if (row.qty > 1) {
          await _changeRowSeatWithQuantity(row, seat ?? 1);
        } else {
          _changeRowSeat(row, seat ?? 1);
        }
        deselectSaleRow(row);
      }
    }
    await upsertCurrentSale();
    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void deselectSeat() {
    selectedSeat.value = 0;
    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void resetSale() {
    currentSale.value = none();
    _poleDisplayService.banner();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void deselectAll({bool modifiersOnly = false}) {
    selectedSeat.value = 0;
    currentSale.value.match(
      (Sale sale) {
        for (final SaleRow row in sale.document.saleRows) {
          if (modifiersOnly) {
            if (row.parent >= 0) {
              row.selected = false;
            }
          } else {
            row.selected = false;
          }
        }
      },
      () => null,
    );
    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  List<List<SaleRow>> organizeBySeat(Sale sale) {
    final List<List<SaleRow>> rowLists = <List<SaleRow>>[
      sale.document.saleRows
          .where(
            (SaleRow row) => row.seatNumber == 1 || row.seatNumber == 0,
          )
          .toList(),
    ];
    for (int i = 1; i < (sale.document.saleHeader.seatCnt ?? 1); i++) {
      rowLists.add(
        sale.document.saleRows
            .where(
              (SaleRow row) => row.seatNumber == i + 1,
            )
            .toList(),
      );
    }
    for (int i = 0; i < sale.document.saleHeader.toGoSeatCnt; i++) {
      rowLists.add(
        sale.document.saleRows
            .where(
              (SaleRow row) => row.seatNumber == (i + 1) * -1,
            )
            .toList(),
      );
    }
    return rowLists;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> saveSale() async {
    await currentSale.value.match(
      (Sale sale) async {
        if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.SUSPENDED.index)) {
          sale.document.saleHeader.saleFlags.add(SaleFlags.SUSPENDED.index);
        }
        sale.document.saleHeader.currentTerminalNumber = 0;
        final Either<ServiceError, Sale> result = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );
        result.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (Sale sale) => null,
        );
        if (_configService.merchantConfig.document.prepOnSaleChange) {
          if (Helpers.saleNeedsPrinted(sale)) {
            unawaited(handlePrepPrint(sale: sale));
          }
        }
      },
      () => null,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void refreshSaleViewModels(Sale sale) {
    int newTotal = 0;
    int cashNewTotal = 0;
    int takeOutSurchargeTotal = 0;
    final List<int> taxSubtotals = [];
    final List<double> taxSubtotalsAsDouble = [];
    final List<int> cashTaxSubtotals = [];
    final List<double> cashTaxSubtotalsAsDouble = [];
    final Section section = getProperSection();
    final List<SaleRow> rowList = sale.document.saleRows.where((SaleRow r) => !r.flags.contains(SaleRowFlags.VOIDED.index)).toList();
    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;
    final bool taxForgiven = sale.document.saleRows.firstWhereOrNull((SaleRow r) => r.flags.contains(SaleRowFlags.FORGIVE_TAX.index)) != null;
    final List<Tax> salesTaxList = _configService.salesTaxList;

    for (final SaleRow saleRow in rowList) {
      final SaleRow parent = Helpers.getParentRow(saleRow, sale.document.saleRows);
      final num qty = parent.splitData == null ? saleRow.qty : saleRow.qty / parent.splitData!.qty;
      saleRow.creditPrice = (qty * saleRow.basePrice).round();
      saleRow.cashPrice = (qty * saleRow.cashBasePrice).round();

      if (parent.splitData != null && saleRow.flags.contains(SaleRowFlags.SPLIT_PARENT.index)) {
        final int originalTotalCredit = saleRow.basePrice * saleRow.qty;
        final int originalTotalCash = saleRow.cashBasePrice * saleRow.qty;
        final int remainderCredit = (saleRow.creditPrice * parent.splitData!.qty) - originalTotalCredit;
        final int remainderCash = (saleRow.cashPrice * parent.splitData!.qty) - originalTotalCash;
        saleRow.creditPrice = saleRow.creditPrice - remainderCredit;
        saleRow.cashPrice = saleRow.cashPrice - remainderCash;
      }

      saleRow.actualPrice = saleRow.creditPrice;

      newTotal += saleRow.creditPrice;
      cashNewTotal += saleRow.cashPrice;

      int itemDiscountTotal = 0;
      int cashItemDiscountTotal = 0;
      for (final SaleRowDiscount discount in saleRow.discounts) {
        itemDiscountTotal += discount.value;
        cashItemDiscountTotal += discount.cashValue;
      }

      if (saleRow.parent < 0) {
        final List<SaleRow> children = Helpers.getChildRows(saleRow, sale.document.saleRows);
        final int overallCreditBase = children.fold(0, (int sum, SaleRow r) => sum + r.basePrice);
        final int overallCashBase = children.fold(0, (int sum, SaleRow r) => sum + r.cashBasePrice);
        if (overallCreditBase < 0 || overallCashBase < 0) {
          for (final SaleRow child in children) {
            if (!child.flags.contains(SaleRowFlags.REFUNDED.index)) {
              child.flags.add(SaleRowFlags.REFUNDED.index);
            }
          }
        } else {
          for (final SaleRow child in children) {
            child.flags.removeWhere((int f) => f == SaleRowFlags.REFUNDED.index);
          }
        }
        // If takeout surcharge applies update salerow surcharge amount and add to surcharge total
        if (sale.document.saleHeader.orderType == OrderType.TAKEOUT.index || saleRow.seatNumber < 0) {
          if (children.any((SaleRow r) => r.takeOutSurcharge && !r.flags.contains(SaleRowFlags.VOIDED.index))) {
            saleRow.takeOutSurchargeAmt = (merchantDoc.takeOutSurchargeAmt * qty).ceil();
            if (saleRow.basePrice < 0) saleRow.takeOutSurchargeAmt = saleRow.takeOutSurchargeAmt * -1;
            takeOutSurchargeTotal += saleRow.takeOutSurchargeAmt;
          } else {
            saleRow.takeOutSurchargeAmt = 0;
          }
        } else {
          saleRow.takeOutSurchargeAmt = 0;
        }
      }

      /*
      * JOE CHANGED THIS AGAIN FOR ZERO PRICED ITEMS AND TO FIX ROUNDING 
      * ERROR THAT LEADS TO INCORRECT GROSS PRICE SOMETIMES.
      */

      // Use a temp originalPrice to replace 0 price random weight barcode pricing
      final int originalPrice = saleRow.basePrice + itemDiscountTotal;
      final int cashOriginalPrice = saleRow.cashBasePrice + cashItemDiscountTotal;
      saleRow.grossPrice = (originalPrice * qty).round();
      saleRow.cashGrossPrice = (cashOriginalPrice * qty).round();

      if (parent.splitData != null && saleRow.flags.contains(SaleRowFlags.SPLIT_PARENT.index)) {
        final int originalGrossCredit = originalPrice * saleRow.qty;
        final int originalGrossCash = cashOriginalPrice * saleRow.qty;
        final int remainderGrossCredit = (saleRow.grossPrice * parent.splitData!.qty) - originalGrossCredit;
        final int remainderGrossCash = (saleRow.cashGrossPrice * parent.splitData!.qty) - originalGrossCash;
        saleRow.grossPrice = saleRow.grossPrice - remainderGrossCredit;
        saleRow.cashGrossPrice = saleRow.cashGrossPrice - remainderGrossCash;
      }

      /*
      * END JOE
      */

      // Check if there are any tax forgiven rows and adjust the sale header accoringly
      if (taxForgiven) {
        if (!sale.document.saleHeader.saleFlags.contains(SaleFlags.TAX_FORGIVEN.index)) {
          sale.document.saleHeader.saleFlags.add(SaleFlags.TAX_FORGIVEN.index);
        }
      } else {
        sale.document.saleHeader.saleFlags.removeWhere((int f) => f == SaleFlags.TAX_FORGIVEN.index);
      }

      // Loop through salesTaxList length for all possible taxes
      if (saleRow.flags.contains(SaleRowFlags.TAXABLE.index)) {
        for (int i = 0; i < salesTaxList.length; i++) {
          final int checkDigit = 1 << i;
          // If the current index doesnt exist on the subtotal list, create it

          // The following lines were part of the original code, but were commented out
          // because of tax rounding errors
          // if (taxSubtotals.length == i) {
          // taxSubtotals.add(0);
          // cashTaxSubtotals.add(0);
          //}
          if (taxSubtotalsAsDouble.length == i) {
            taxSubtotalsAsDouble.add(0);
            cashTaxSubtotalsAsDouble.add(0);
          }

          // Determine if sale is takeout and this tax should be forgiven.
          final bool forgiveTakeout =
              (sale.document.saleHeader.orderType == OrderType.TAKEOUT.index || saleRow.seatNumber < 0) && salesTaxList[i].forgiveTakeout == true;

          // If the current tax is enabled, calculate it and add it to the subtotals array
          if (saleRow.taxFlags & checkDigit == checkDigit && !forgiveTakeout) {
            taxSubtotalsAsDouble[i] += _saleService.calculateSalesTax(
              saleRow.actualPrice,
              salesTaxList[i].taxPercent! / 10000,
            );
            cashTaxSubtotalsAsDouble[i] += _saleService.calculateSalesTax(
              (saleRow.cashBasePrice * qty).round(),
              salesTaxList[i].taxPercent! / 10000,
            );
          }
        }
      }
    }

    // Add all the subtotals together to get one tax total
    int taxableTotal = 0;
    int cashTaxableTotal = 0;
    for (int i = 0; i < taxSubtotalsAsDouble.length; i++) {
      //
      //Old code that caused rounding errors in tax calculations
      // for (int i = 0; i < taxSubtotals.length; i++) {
      //   taxableTotal += taxSubtotals[i];
      //   cashTaxableTotal += cashTaxSubtotals[i];
      // }

      //set tax totals for traditional or card prices
      taxableTotal += double.parse(taxSubtotalsAsDouble[i].toStringAsFixed(3)).round();
      taxSubtotals.add(double.parse(taxSubtotalsAsDouble[i].toStringAsFixed(3)).round());
      //set cash price tax totals
      cashTaxableTotal += double.parse(cashTaxSubtotalsAsDouble[i].toStringAsFixed(3)).round();
      cashTaxSubtotals.add(double.parse(cashTaxSubtotalsAsDouble[i].toStringAsFixed(3)).round());
    }

    // Calculate gratuity
    final bool sectionGrat = section.gratAmt > 0 && (section.minGratCustCnt ?? 1) <= (sale.document.saleHeader.customerCount);
    final double addedGrat = sale.document.saleHeader.addedGratuity ?? 0;

    if (sectionGrat || addedGrat > 0) {
      final int calcTotal = section.calcGratWDiscs != true ? newTotal - sale.document.saleHeader.discountTotal : newTotal;
      final int cashCalcTotal = section.calcGratWDiscs != true ? cashNewTotal - sale.document.saleHeader.cashDiscountTotal : cashNewTotal;

      final double percentToUse = (sectionGrat ? section.gratAmt : 0.0) + addedGrat;
      sale.document.saleHeader.gratuityTotal = ((percentToUse / 100) * calcTotal).round();
      sale.document.saleHeader.cashGratuityTotal = ((percentToUse / 100) * cashCalcTotal).round();
      sale.document.saleHeader.gratuityPercent = percentToUse;
    } else {
      sale.document.saleHeader.gratuityPercent = 0;
      sale.document.saleHeader.gratuityTotal = 0;
      sale.document.saleHeader.cashGratuityTotal = 0;
    }

    sale.document.saleHeader.subTotal = newTotal;
    sale.document.saleHeader.cashSubTotal = cashNewTotal;
    sale.document.saleHeader.taxTotals = taxSubtotals;
    sale.document.saleHeader.cashTaxTotals = cashTaxSubtotals;
    sale.document.saleHeader.taxTotal = taxableTotal;
    sale.document.saleHeader.cashTaxTotal = cashTaxableTotal;
    sale.document.saleHeader.takeOutSurchargeTotal = takeOutSurchargeTotal;
    sale.document.saleHeader.total = sale.document.saleHeader.subTotal +
        sale.document.saleHeader.gratuityTotal +
        sale.document.saleHeader.taxTotal +
        sale.document.saleHeader.takeOutSurchargeTotal;
    sale.document.saleHeader.cashTotal = sale.document.saleHeader.cashSubTotal +
        sale.document.saleHeader.cashGratuityTotal +
        sale.document.saleHeader.cashTaxTotal +
        sale.document.saleHeader.takeOutSurchargeTotal;
    sale.document.saleHeader.dualPricingAmount = sale.document.saleHeader.subTotal - sale.document.saleHeader.cashSubTotal;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void refreshCurrentSaleModels() {
    currentSale.value.match(
      (Sale sale) {
        refreshSaleViewModels(sale);
      },
      () => null,
    );
    currentSale.refresh();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> cancelSale(Sale sale, {SaleFlags cancelFlag = SaleFlags.CANCEL}) async {
    final List<String> toDelete = [];
    sale.document.saleHeader.currentCashier = _identityService.currentCashier;
    sale.document.saleHeader.saleFlags.add(cancelFlag.index);
    sale.document.saleHeader.saleFlags.removeWhere((int x) => x == SaleFlags.SUSPENDED.index);
    sale.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id;
    sale.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;
    sale.document.saleHeader.currentTerminalNumber = 0;
    sale.end_at = DateTime.now().toUtc();

    for (final SaleRow row in sale.document.saleRows) {
      toDelete.addAll(row.liquorToPour);
    }
    if (toDelete.isNotEmpty && cancelFlag == SaleFlags.CANCEL) {
      (await _liquorControlService.deleteLiquorToPourByPK(toDelete)).fold((l) => _notificationService.error(l.message), (r) => null);
    }

    // TODO: should we be voiding any tenders that have been applied
    // to the sale?  I think we should, but we need to be careful

    final Either<ServiceError, Sale> result1 = await _saleService.upsert(
      sale: sale,
      employee: _identityService.currentEmployee,
      terminalNumber: _identityService.terminalNumber,
    );

    result1.fold(
      (ServiceError error) => _notificationService.error(error.message),
      (Sale sale) {
        final Sale? currSale = currentSale.value.match((Sale s) => s, () => null);
        if (currSale != null && currSale.sale == sale.sale) {
          deselectAll();
          resetSale();
        }
        _openSalesController.openSales.removeWhere((Sale s) => s.sale == sale.sale);
      },
    );

    final Option<ServiceError> result2 = await _itemService.addToItemCountUsingSaleRows(
      sale.document.saleRows
          .where(
            (SaleRow r) => !r.flags.contains(SaleRowFlags.PREP_PRINTED.index),
          )
          .toList(),
    );

    if (result2.isSome()) {
      _notificationService.error(
        result2.match((t) => t.message, () => "Error updating item count"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> cancelCurrentSale() async {
    currentSale.value.match(
      (Sale sale) async {
        await cancelSale(sale);
        if (_configService.merchantConfig.document.prepOnCancel && sale.document.saleHeader.saleFlags.contains(SaleFlags.PREP_PRINTED.index)) {
          unawaited(handlePrepPrint(sale: sale, canceledSale: true));
        }
        unawaited(
          _activityService.insertActivity(
            activity: Activity(
              activity: ActivityFlags.CANCELED_SALE.index,
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              sale_num: sale.sale_number,
              data1: sale.document.saleHeader.total,
              str_data: "Cancelled Sale",
            ),
          ),
        );
      },
      () => _notificationService.error("No ${saleName.toLowerCase()} open to cancel!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void handleChangeSaleTable(FlowElement element) {
    currentSale.value.match(
      (Sale sale) async {
        sale.document.saleHeader.tableDesc = element.desc;
        sale.document.saleHeader.tableStarted ??= DateTime.now().toUtc();
        sale.document.saleHeader.roomIdx = element.roomIdx;
        sale.document.saleHeader.sectIdx = element.sectIdx;
        sale.document.saleHeader.tableIdx = element.idx + 1;

        if (autoAddSeats.value) {
          if ((sale.document.saleHeader.seatCnt ?? 1) < element.seatCnt) {
            sale.document.saleHeader.seatCnt = element.seatCnt;
          }
        }

        final Either<ServiceError, Sale> result = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );
        result.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (Sale sale) {
            currentSale.value = some(sale);
            _notificationService.success("Table Assigned");
          },
        );
      },
      () => _notificationService.error("No ${saleName.toLowerCase()} open for table assignment!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void handleChangeSaleDesc(String? value) {
    currentSale.value.match(
      (Sale sale) async {
        sale.document.saleHeader.saleDescription = value;
        if (!(await upsertCurrentSale())) return _notificationService.error("Error updating sale");
        _notificationService.success("Description Changed");
      },
      () => _notificationService.error("No ${saleName.toLowerCase()} open for description change!"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void toggleNegativeSaleMode() {
    if (currentSaleMode.value != SaleMode.NEGATIVE_SALE) {
      currentSaleMode.value = SaleMode.NEGATIVE_SALE;
      Get.back();
      _notificationService.success("Entered Negative Sale Mode");
    } else {
      currentSaleMode.value = SaleMode.SALE;
      Get.back();
      _notificationService.success("Entered Sale Mode");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getCustomer() async {
    try {
      final Sale searchSale = currentSale.value.match(
        (Sale sale) => sale,
        () => throw "No active ${saleName.toLowerCase()} found",
      );
      if (searchSale.document.saleHeader.customer == null) {
        throw "No customer attached to ${saleName.toLowerCase()}";
      }

      final Either<ServiceError, Customer> customerResult = await _customerService.getCustomerByPk(searchSale.document.saleHeader.customer!);

      customerResult.fold(
        (ServiceError error) => throw error,
        (Customer customer) => currentCustomer.value = some(customer),
      );
    } catch (err, stack) {
      _logger.shout("Error getting customer", err, stack);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void modifyTaxForgiveness(Sale sale, int forgiveTaxMask) {
    for (final SaleRow saleRow in sale.document.saleRows) {
      saleRow.taxFlags = saleRow.origTaxFlags & ~forgiveTaxMask;

      if (saleRow.taxFlags != saleRow.origTaxFlags) {
        saleRow.flags.add(SaleRowFlags.FORGIVE_TAX.index);
      } else {
        saleRow.flags.removeWhere(
          (element) => element == SaleRowFlags.FORGIVE_TAX.index,
        );
      }
    }

    refreshSaleViewModels(sale);
    _notificationService.success("Tax modified!");
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> _getPriceSchedulesAndLevels() async {
    final Either<ServiceError, List<PriceSchedule>> psConfigResult = await _configService.getPriceSchedules();
    final Either<ServiceError, SystemSettingJsonRecord> levelConfigResult = await _configService.getSystemSettings();

    psConfigResult.fold(
      (ServiceError error) {
        _notificationService.error(error.message);
      },
      (List<PriceSchedule> listRes) {
        psList = listRes;
        psList.sort((PriceSchedule a, PriceSchedule b) => a.idx.compareTo(b.idx));
      },
    );
    levelConfigResult.fold(
      (ServiceError error) {
        _notificationService.error(error.message);
      },
      (SystemSettingJsonRecord settingsRes) {
        levelList = settingsRes.document.priceLevels;
        levelList.sort((PriceLevel a, PriceLevel b) => a.idx.compareTo(b.idx));
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updateRowPricing(SaleRow row) async {
    final String priceKey = await findItemCurrentPrice(row.itemPricing ?? {}, row.basePrice, row.receiptDescription);
    final int cashPrice = getDualPrice(row.itemPricing ?? {}, priceKey);
    final int? newPrice = (row.itemPricing ?? {})[priceKey];
    row.basePrice = newPrice ?? row.basePrice;
    row.originalPrice = newPrice ?? row.originalPrice;
    row.cashBasePrice = newPrice == null ? row.cashBasePrice : cashPrice;
    row.cashOriginalPrice = newPrice == null ? row.cashOriginalPrice : cashPrice;
    if (priceKey[1] != "0") {
      if (!row.flags.contains(SaleRowFlags.PRICE_SCHEDULE.index)) row.flags.add(SaleRowFlags.PRICE_SCHEDULE.index);
    } else {
      row.flags.removeWhere((int f) => f == SaleRowFlags.PRICE_SCHEDULE.index);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<String> findItemCurrentPrice(
    Map<String, int> pricing,
    int? currentPrice,
    String itemDesc,
  ) async {
    String resultKey = "S0L0C0";
    int currentLevel = 0;
    bool useLvl = false;
    final List<String> pricingKeys = pricing.keys.where((String k) => k == "S0L0C0" || (pricing[k] != 0 && k[k.length - 1] != "1")).toList();
    final DateTime currentTime = DateTime.now();
    final List<PriceSchedule> levelList = <PriceSchedule>[];
    final List<PriceSchedule> defaultList = <PriceSchedule>[];
    final List<PriceSchedule> activeList = <PriceSchedule>[];

    currentSale.value.match(
      (Sale sale) => currentLevel = sale.document.saleHeader.priceLevel ?? 0,
      () => currentLevel = _identityService.terminalPriceLevel > 0 ? _identityService.terminalPriceLevel : getProperSection().priceLevel ?? 0,
    );

    if (pricing["S0L${currentLevel}C0"] != null) {
      useLvl = true;
      resultKey = "S0L${currentLevel}C0";
    }

    for (final String key in pricingKeys) {
      if (key != "S0L0C0" && key != resultKey) {
        final List<String> splitStr = key.substring(1).split("L");
        final int lvl = int.parse(splitStr[1].substring(0, 1));
        if (lvl == currentLevel) {
          final PriceSchedule ps = psList[int.parse(splitStr[0]) - 1];
          levelList.add(ps);
        } else if (splitStr[1].substring(0, 1) == "0") {
          final PriceSchedule ps = psList[int.parse(splitStr[0]) - 1];
          defaultList.add(ps);
        }
      }
    }

    /// check price schedules related to current active price level
    for (final PriceSchedule ps in levelList) {
      final String newKey = "S${ps.idx}L${currentLevel}C0";
      final int newPrice = pricing[newKey] ?? 0;
      if (_checkIfScheduleActive(ps, currentTime) && newPrice != 0) {
        activeList.add(ps);
        useLvl = true;
      }
    }

    /// if no active price schedules for current price level, check default schedules
    if (!useLvl) {
      for (final PriceSchedule ps in defaultList) {
        final String newKey = "S${ps.idx}L0C0";
        final int newPrice = pricing[newKey] ?? 0;
        if (!levelList.contains(ps)) {
          if (_checkIfScheduleActive(ps, currentTime) && newPrice != 0) {
            activeList.add(ps);
          }
        }
      }
    }

    /// if salerow already has price automatically return any schedule-associated keys with prices that match
    /// (this is to avoid having to show overlapping price dialog when not needed)
    if ((currentPrice ?? 0) > 0) {
      for (final PriceSchedule ps in activeList) {
        final String newKey = "S${ps.idx}L${useLvl ? currentLevel : 0}C0";
        if (pricing[newKey] == currentPrice) return newKey;
      }
    }

    /// if no active schedules found, return default price key
    if (activeList.isEmpty) return resultKey;

    /// if only one active schedule found, return associated key
    if (activeList.length == 1) return "S${activeList.first.idx}L${useLvl ? currentLevel : 0}C0";

    /// deal with overlapping schedules
    /// if always use lowest price, return key with lowest price, else return highest
    if (_configService.merchantConfig.document.overlappingScheduleOpt != OverlappingScheduleOptions.SELECT_SCHEDULE.index) {
      final bool lowestPrice = _configService.merchantConfig.document.overlappingScheduleOpt == OverlappingScheduleOptions.ALWAYS_LOWEST_PRICE.index;
      for (final PriceSchedule ps in activeList) {
        final String newKey = "S${ps.idx}L${useLvl ? currentLevel : 0}C0";
        if (pricing[newKey] == null) continue;
        if (lowestPrice && (pricing[newKey]! < (pricing[resultKey] ?? 0))) resultKey = newKey;
        if (!lowestPrice && (pricing[newKey]! > (pricing[resultKey] ?? 0))) resultKey = newKey;
      }
      return resultKey;
    }

    /// else show overlapping schedules dialog prompting user to select which price to use
    final PriceSchedule? selected = await Get.dialog<PriceSchedule?>(
      OverlappingSchedulesDialog(
        scheduleList: activeList,
        pricing: pricing,
        priceLevel: useLvl ? currentLevel : 0,
        dualPricing: _configService.merchantConfig.document.dualPricing,
        itemDesc: itemDesc,
      ),
      barrierDismissible: false,
    );

    return selected == null ? resultKey : "S${selected.idx}L${useLvl ? currentLevel : 0}C0";
  }

  ///
  ///
  ///
  ///
  ///
  ///
  int getDualPrice(Map<String, int> pricing, String creditKey) {
    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;
    if (!merchantDoc.dualPricing) return pricing[creditKey] ?? 0;
    final String cashKey = "${creditKey.substring(0, creditKey.length - 1)}1";
    return Helpers.getCashPrice(
      cashAmount: pricing[cashKey],
      creditAmount: pricing[creditKey] ?? 0,
      dualPercent: merchantDoc.dualPricingPercent,
      roundAmount: merchantDoc.dualPricingRoundAmount,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool _checkIfScheduleActive(PriceSchedule ps, DateTime now) {
    bool afterStart = false;
    bool beforeEnd = false;

    if (ps.isSeasonal) {
      final bool intoNextYear = ps.seasStMonth < ps.seasEndMonth || (ps.seasStMonth == ps.seasEndMonth && ps.seasStDay > ps.seasEndDay);

      if (!intoNextYear) {
        if (now.month > ps.seasStMonth) afterStart = true;
        if (now.month < ps.seasEndMonth) beforeEnd = true;
        if (now.month == ps.seasStMonth) {
          if (now.day >= ps.seasStDay) afterStart = true;
        }
        if (now.month == ps.seasEndMonth) {
          if (now.day <= ps.seasEndDay) beforeEnd = true;
        }
      } else {
        final bool onSecondYear = ps.seasStMonth > now.month || (ps.seasStMonth == now.month && ps.seasStDay > now.day);

        if (onSecondYear) {
          afterStart = true;
          if (now.month < ps.seasEndMonth || (now.month == ps.seasEndMonth && now.day <= ps.seasEndDay)) {
            beforeEnd = true;
          }
        } else {
          beforeEnd = true;
          if (now.month > ps.seasStMonth || (now.month == ps.seasStMonth && now.day >= ps.seasStDay)) {
            afterStart = true;
          }
        }
      }
    } else {
      final int nowMilliseconds = Helpers.timeToMilliseconds(now);
      final bool intoNextDay = ps.endTime < ps.startTime;
      final bool onSecondDay = ps.startTime > nowMilliseconds;
      final int weekday = intoNextDay && onSecondDay ? now.subtract(const Duration(days: 1)).weekday : now.weekday;
      if (ps.days & week[weekday]! == week[weekday]) {
        if (!intoNextDay) {
          if (nowMilliseconds > ps.startTime) afterStart = true;
          if (nowMilliseconds < ps.endTime) beforeEnd = true;
        } else {
          if (onSecondDay) {
            afterStart = true;
            if (nowMilliseconds < ps.endTime) {
              beforeEnd = true;
            }
          } else {
            beforeEnd = true;
            if (nowMilliseconds > ps.startTime) {
              afterStart = true;
            }
          }
        }
      }
    }

    return afterStart && beforeEnd;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> changeOrderType() async {
    final RxInt currentType = currentSale.value.match(
      (Sale sale) => sale.document.saleHeader.orderType.obs,
      () => defaultOrdType.obs,
    );
    await Get.dialog(
      ChangeOrderTypeDialog(
        currentType: currentType,
      ),
    ).then((value) async {
      if (value == true) {
        await currentSale.value.match(
          (Sale sale) async {
            if ((sale.document.saleHeader.customer ?? "") == "" &&
                ((requireCustIfTogo.value && currentType.value == OrderType.TAKEOUT.index) ||
                    (requireCustIfDelivery.value && currentType.value == OrderType.DELIVERY.index))) {
              await _registerController.openCustomDrawer(
                screen: RegisterMenuOptions.CUSTOMER_ATTACH,
              );
              if (sale.document.saleHeader.customer == null) {
                if (currentType.value == OrderType.TAKEOUT.index) {
                  _notificationService.error("Customer Required for to-go $saleName!");
                } else {
                  _notificationService.error("Customer Required for delivery $saleName!");
                }
                return;
              }
            } else if (requireTable.value &&
                _registerController.checkIfSeatingEnabled(showError: false) &&
                (sale.document.saleHeader.tableDesc ?? "") == "" &&
                currentType.value == OrderType.EAT_IN.index) {
              await Get.bottomSheet(
                const TablesDialog(
                  tablesOnly: true,
                ),
                isScrollControlled: true,
                ignoreSafeArea: false,
                backgroundColor: Colors.transparent,
              );
              if ((sale.document.saleHeader.tableDesc ?? "") == "") {
                return _notificationService.error("Table required!");
              }
            }
            sale.document.saleHeader.orderType = currentType.value;
            refreshCurrentSaleModels();
            if (!(await upsertCurrentSale())) return;
            _notificationService.success("Order type changed!");
            if (_registerController.menuOpen) Get.back();
          },
          () async {
            if (_registerController.menuOpen) Get.back();
            newOrdType.value = currentType.value;
            await startEmptySale();
          },
        );
      }
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updateSaleOnLiquorToPour(Sale sale) async {
    final List<String> toUpdate = <String>[];
    for (final SaleRow row in sale.document.saleRows) {
      toUpdate.addAll(row.liquorToPour);
    }
    if (toUpdate.isNotEmpty) {
      (await _liquorControlService.updateLiquorToPourSaleByPK(toUpdate, sale.sale)).match(
        (ServiceError l) => _notificationService.error(l.message),
        (List<LiquorToPour> r) => null,
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, Sale>> combineSales({
    required List<String> combineFrom,
    required String combineInto,
  }) async {
    try {
      final bool seatingEnabled = getProperSection().trackBySeat;

      // Get sale to combine into
      final Either<ServiceError, Sale> intoSaleRes = await _saleService.getSaleByPk(combineInto);
      final Sale intoSale = intoSaleRes.fold(
        (l) => throw Left(ServiceError("Failed to get sale to combine into!")),
        (r) => r,
      );
      int newSeatCnt = intoSale.document.saleHeader.seatCnt ?? 1;
      int newTakeoutSeatCnt = intoSale.document.saleHeader.toGoSeatCnt;

      // Get sales to pull rows from
      final Either<ServiceError, List<Sale>> fromSalesRes = await _saleService.getSalesByPk(combineFrom);
      final List<Sale> fromSales = fromSalesRes.fold(
        (l) => throw Left(ServiceError("Failed to get sales to combine!")),
        (r) => r,
      );

      // if no sales found in fromSales, return the unaltered sale
      if (fromSales.isEmpty) return Right(intoSale);

      for (final Sale fromSale in fromSales) {
        // Add copies of rows with new index and seat number from each sale in fromSales to intoSale
        for (final SaleRow sr in fromSale.document.saleRows) {
          final int seatNum = sr.seatNumber == 0 ? 1 : sr.seatNumber;
          final SaleRow rowCopy = Helpers.makeRowCopy(
            row: sr,
            newIdx: intoSale.document.saleRows.length + 1,
            qty: sr.qty,
            withPrepPrintedFlag: true,
            withTransactionFlags: true,
            seatNum: !seatingEnabled ? 1 : (seatNum + (seatNum < 0 ? newTakeoutSeatCnt : newSeatCnt)),
          );
          intoSale.document.saleRows.add(rowCopy);
        }
        // Adjust new seat counts for sale by adding seat counts from the sale in fromSales
        final int seatCnt = fromSale.document.saleHeader.seatCnt ?? 1;
        newSeatCnt += (seatCnt == 0 ? 1 : seatCnt);
        newTakeoutSeatCnt += fromSale.document.saleHeader.toGoSeatCnt;
      }
      // Set new seat counts then refresh view models
      intoSale.document.saleHeader.seatCnt = newSeatCnt;
      intoSale.document.saleHeader.toGoSeatCnt = newTakeoutSeatCnt;
      refreshSaleViewModels(intoSale);

      // Upsert the combined sale
      final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
        sale: intoSale,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );
      await upsertRes.fold(
        (ServiceError error) async => throw error.message,
        (Sale s) async {
          _notificationService.success("Combined ${fromSales.length} ${saleName.toLowerCase()}(s) into ${s.sale_number}");
          // Cancel leftover sales using combined flag
          for (final Sale fromSale in fromSales) {
            await cancelSale(fromSale, cancelFlag: SaleFlags.COMBINED);
          }
        },
      );
      return Right(intoSale);
    } catch (err, stack) {
      _logger.severe(
        "error combining sales",
        err,
        stack,
      );
      return Left(ServiceError(err.toString()));
    }
  }
}
