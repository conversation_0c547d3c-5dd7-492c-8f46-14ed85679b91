import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

ConfigService _configService = Get.find();

class SaleFooter extends GetView<SaleController> {
  const SaleFooter();

  @override
  Widget build(BuildContext context) {
    // ignore: use_decorated_box
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            width: 2,
            color: R2Colors.neutral300,
          ),
        ),
      ),
      child: Obx(() {
        final Sale sale = controller.currentSale.value.getOrElse(() => Sale.empty());
        final SaleHeader saleHeader = sale.document.saleHeader;
        double fontSub = 0;
        if (saleHeader.gratuityTotal != 0) fontSub = 2;
        if (saleHeader.takeOutSurchargeTotal != 0) fontSub = fontSub == 0 ? 2 : 5;

        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  "Subtotal",
                  style: TextStyle(
                    fontSize: 20 - fontSub,
                  ),
                ),
                Text(
                  "${saleHeader.subTotal < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.subTotal.abs())}",
                  style: TextStyle(
                    color: saleHeader.subTotal < 0 ? R2Colors.negativeRed : R2Colors.black,
                    fontFamily: "Roboto Mono",
                    fontSize: 20 - fontSub,
                  ),
                ),
              ],
            ),
            if (saleHeader.gratuityPercent != 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    "Gratuity",
                    style: TextStyle(
                      fontSize: 20 - fontSub,
                    ),
                  ),
                  Text(
                    "${saleHeader.gratuityTotal < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.gratuityTotal.abs())}",
                    style: TextStyle(
                      color: saleHeader.gratuityTotal < 0 ? R2Colors.negativeRed : R2Colors.black,
                      fontFamily: "Roboto Mono",
                      fontSize: 20 - fontSub,
                    ),
                  ),
                ],
              ),
            if (saleHeader.takeOutSurchargeTotal != 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    "Takeout Fee",
                    style: TextStyle(
                      fontSize: 20 - fontSub,
                    ),
                  ),
                  Text(
                    "${saleHeader.takeOutSurchargeTotal < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.takeOutSurchargeTotal.abs())}",
                    style: TextStyle(
                      color: saleHeader.takeOutSurchargeTotal < 0 ? R2Colors.negativeRed : R2Colors.black,
                      fontFamily: "Roboto Mono",
                      fontSize: 20 - fontSub,
                    ),
                  ),
                ],
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Text(
                      "Tax",
                      style: TextStyle(
                        fontSize: 20 - fontSub,
                      ),
                    ),
                    if (saleHeader.saleFlags.contains(SaleFlags.TAX_FORGIVEN.index))
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: FaIcon(
                          FontAwesomeIcons.handHoldingDollar,
                          size: 20 - fontSub,
                          color: R2Colors.primary500,
                        ),
                      ),
                  ],
                ),
                Text(
                  "${saleHeader.taxTotal < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.taxTotal.abs())}",
                  style: TextStyle(
                    color: saleHeader.taxTotal < 0 ? R2Colors.negativeRed : R2Colors.black,
                    fontFamily: "Roboto Mono",
                    fontSize: 20 - fontSub,
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  "Total",
                  style: TextStyle(
                    fontSize: 20 - fontSub,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  "${saleHeader.total < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.total.abs())}",
                  style: TextStyle(
                    color: saleHeader.total < 0 ? R2Colors.negativeRed : R2Colors.black,
                    fontFamily: "Roboto Mono",
                    fontSize: 20 - fontSub,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (_configService.merchantConfig.document.dualPricing)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    "Cash Total",
                    style: TextStyle(
                      fontSize: 18 - fontSub,
                      fontWeight: FontWeight.bold,
                      color: R2Colors.neutral500,
                    ),
                  ),
                  Text(
                    "${saleHeader.cashTotal < 0 ? "-" : ""}\$${Helpers.formatCurrency(saleHeader.cashTotal.abs())}",
                    style: TextStyle(
                      color: saleHeader.cashTotal < 0 ? R2Colors.negativeRed : R2Colors.neutral500,
                      fontFamily: "Roboto Mono",
                      fontSize: 18 - fontSub,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
          ],
        );
      }),
    );
  }
}
