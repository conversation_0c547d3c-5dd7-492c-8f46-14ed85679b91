import 'dart:async';
import 'dart:ui';

import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/uom.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sliver_tools/sliver_tools.dart';

/// Controller that drives the scroll view in the cart pane.
final ScrollController cartListController = ScrollController();

// ignore: must_be_immutable
class SaleItems extends GetView<SaleController> {
  SaleItems({super.key});

  // Not adding this to the controller because it's to be used here and only here.
  RxBool scrollActive = false.obs;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await Future<void>.delayed(const Duration(milliseconds: 20));
        scrollActive.value = cartListController.hasClients && cartListController.position.maxScrollExtent > 0;
      });
      return controller.currentSale.value.match(
        (Sale sale) {
          final int seatCnt = (sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt;
          final List<List<SaleRow>> rowLists = controller.organizeBySeat(sale);

          return Material(
            color: R2Colors.white,
            child: ScrollConfiguration(
              // Turn off the platform-provided scrollbar
              behavior: const MaterialScrollBehavior().copyWith(
                // keep the implicit scrollbar turned off…
                scrollbars: false,
                // …but accept drags from all the usual devices.
                dragDevices: <PointerDeviceKind>{
                  PointerDeviceKind.touch,
                  PointerDeviceKind.mouse,
                  PointerDeviceKind.stylus,
                  PointerDeviceKind.trackpad,
                  PointerDeviceKind.invertedStylus,
                },
              ),
              // Adding an additional scrollbar that will remain persistent
              child: Scrollbar(
                controller: cartListController,
                thumbVisibility: true, // the thumb that stays visible
                child: Padding(
                  padding: EdgeInsets.only(top: 2, right: scrollActive.value ? 14 : 2),
                  child: CustomScrollView(
                    controller: cartListController,
                    slivers: <Widget>[
                      for (int i = 0; i < seatCnt; ++i)
                        if (!sale.document.saleHeader.seatsSettled.contains(_seatNumber(i, sale)))

                          /// One seat = header + rows, treated as one sliver
                          MultiSliver(
                            pushPinnedChildren: true,
                            children: <Widget>[
                              // sticky seat banner
                              SliverPersistentHeader(
                                pinned: true, // keeps it pinned to the top
                                floating: true, // lets it pop back
                                delegate: _SeatHeaderDelegate(
                                  selected: controller.selectedSeat.value == _seatNumber(i, sale),
                                  seat: _seatNumber(i, sale),
                                  onTap: () async {
                                    final int seat = _seatNumber(i, sale);
                                    if (controller.selectedSeat.value == seat) {
                                      controller.deselectSeat();
                                    } else {
                                      await controller.selectSeat(seat);
                                    }
                                  },
                                  isLastNonTakeout: i == ((sale.document.saleHeader.seatCnt ?? 1) - 1),
                                ),
                              ),

                              // rows for that seat
                              SliverList(
                                delegate: SliverChildBuilderDelegate(
                                  (BuildContext context, int rowIndex) => SaleItem(saleRow: rowLists[i][rowIndex]),
                                  childCount: rowLists[i].length,
                                ),
                              ),
                            ],
                          ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        () => const SizedBox.shrink(),
      );
    });
  }

  /// Converts the 0‑based loop index into the real seat number the rest of the
  /// app expects.  Mirrors the old math from the column‑based version.
  int _seatNumber(int i, Sale sale) {
    if (i < (sale.document.saleHeader.seatCnt ?? 1)) {
      return i + 1; // dine‑in seat 1‑N
    } else {
      // To‑go seats are stored as negative numbers (‑1, ‑2, …).
      return (sale.document.saleHeader.seatCnt ?? 1) - (i + 1);
    }
  }

  /// Convenience helper so other widgets (eg. payment dialog) can scroll the
  /// list programmatically.
  static void scrollToBottom() {
    try {
      unawaited(
        Future<void>.delayed(Duration.zero).then(
          (dynamic value) {
            if (cartListController.hasClients) {
              unawaited(
                cartListController.animateTo(
                  cartListController.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 100),
                  curve: Curves.fastOutSlowIn,
                ),
              );
            }
          },
        ),
      );

      // ignore: empty_catches
    } catch (err) {}
  }
}

class _SeatHeaderDelegate extends SliverPersistentHeaderDelegate {
  _SeatHeaderDelegate({
    required this.selected,
    required this.seat,
    required this.onTap,
    required this.isLastNonTakeout,
  });

  final bool selected;
  final int seat;
  final VoidCallback onTap;
  final bool isLastNonTakeout;

  @override
  double get minExtent => 44;
  @override
  double get maxExtent => 44;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return RadiusListener(
      onTap: onTap,
      child: ColoredBox(
        color: selected ? R2Colors.flash500 : R2Colors.white,
        child: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(8),
              child: Center(
                child: Text(
                  seat < 0 ? 'ToGo ${-seat}' : 'Seat $seat',
                  style: TextStyle(
                    fontSize: 18,
                    color: selected ? R2Colors.white : R2Colors.neutral900,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool shouldRebuild(covariant _SeatHeaderDelegate old) => old.selected != selected || old.seat != seat || old.isLastNonTakeout != isLastNonTakeout;
}

class SaleItem extends GetView<SaleController> {
  const SaleItem({
    required this.saleRow,
  });
  final SaleRow saleRow;

  @override
  Widget build(BuildContext context) {
    const double modHeight = 30;
    const double parentHeight = 40;
    const double tabWidth = 4;
    const double selBorderWith = 2;

    Color fontColor = saleRow.selected
        ? saleRow.parent >= 0
            ? R2Colors.white.withOpacity(0.9)
            : R2Colors.primary500
        : R2Colors.neutral700;

    if (saleRow.flags.contains(SaleRowFlags.VOIDED.index)) {
      fontColor = saleRow.selected
          ? saleRow.parent >= 0
              ? R2Colors.white.withOpacity(0.5)
              : R2Colors.primary300
          : R2Colors.neutral400;
    } else if (saleRow.parent < 0 && !saleRow.hasChildren && saleRow.basePrice < 0) {
      fontColor = R2Colors.negativeRed;
    }

    final bool isLiquor = saleRow.flags.contains(SaleRowFlags.LIQUOR_POURED.index);
    final bool printFlag = saleRow.flags.contains(SaleRowFlags.PREP_PRINTED.index);
    final bool missingPrep = saleRow.preppedAt != null && saleRow.preppedAt!.keys.any((String k) => saleRow.preppedAt![k] == null);
    final bool holdRow = saleRow.flags.contains(SaleRowFlags.HOLD_AND_FIRE.index);
    final bool isComment = saleRow.flags.contains(SaleRowFlags.COMMENT.index);
    final String currentSalePK = controller.currentSale.value.match((Sale s) => s.sale, () => "");

    Color tabColor = Colors.transparent;

    if (isLiquor) {
      tabColor = const Color.fromARGB(255, 144, 154, 196);
    }

    if (printFlag || (isLiquor && saleRow.liquorToPour.isEmpty)) {
      tabColor = const Color(0xFF6BCE96);
    } else if (!isLiquor && saleRow.prep > 0) {
      tabColor = Colors.amber;
    }

    if (printFlag && missingPrep) {
      if (controller.getPrepPrintingSales.contains(currentSalePK)) {
        tabColor = R2Colors.neutral400;
      } else {
        tabColor = R2Colors.negativeRed;
      }
    }

    Widget weightSlot = Container();

    if (saleRow.flags.contains(SaleRowFlags.RANDOM_WEIGHT.index) || saleRow.isWeightedItem) {
      String uomText = "";
      String uomWeight = "";

      if (saleRow.UOM != null) {
        switch (UOM.values[saleRow.UOM!]) {
          case UOM.LB:
            uomText = UOM.LB.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(2);
            }
          case UOM.OZ:
            uomText = UOM.OZ.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(1);
            }
          case UOM.G:
            uomText = UOM.G.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(0);
            }
          case UOM.KG:
            uomText = UOM.KG.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(3);
            }
          case UOM.NA:
            // TODO: Handle this case.
            break;
        }
      }

      if (uomText != "") {
        weightSlot = Text(
          "gross ($uomWeight $uomText @ \$${Helpers.formatCurrency(saleRow.originalPrice.abs())}/$uomText)",
          style: TextStyle(
            color: R2Colors.neutral600,
            fontSize: 12,
          ),
        );
      }
    }

    int indent = 0;
    bool groupSelected = false;
    int displayPrice = saleRow.basePrice;
    bool isLastChild = true;
    SaleRow parentRow = saleRow;

    if ((saleRow.parent >= 0 || saleRow.hasChildren) && saleRow.isVisible) {
      final List<SaleRow> currentRows = controller.currentSale.value.match(
        (Sale sale) => sale.document.saleRows,
        () => <SaleRow>[],
      );

      parentRow = Helpers.getParentRow(saleRow, currentRows);

      final List<SaleRow> childRows = Helpers.getChildRows(parentRow, currentRows);

      for (final SaleRow childRow in childRows) {
        if (childRow.parent >= 0 && (saleRow.flags.contains(SaleRowFlags.VOIDED.index) || !childRow.flags.contains(SaleRowFlags.VOIDED.index))) {
          displayPrice += childRow.basePrice;
        }
      }

      if (displayPrice < 0) {
        fontColor = R2Colors.negativeRed;
      }

      groupSelected = saleRow.selected ? saleRow.selected : childRows.firstWhereOrNull((SaleRow r) => r.selected) != null;
      if (childRows.isNotEmpty) isLastChild = childRows.last.index == saleRow.index;

      if (saleRow.parent >= 0) {
        indent = Helpers.getSaleRowIndent(saleRow: saleRow, rowList: currentRows);
        if (indent > 5) indent = 5;
      }
    }

    final int newdisplayPrice = (displayPrice * (parentRow.splitData == null ? saleRow.qty : saleRow.qty / parentRow.splitData!.qty)).round();

    if (parentRow.splitData != null && saleRow.flags.contains(SaleRowFlags.SPLIT_PARENT.index)) {
      final int remainderDisplay = (newdisplayPrice * parentRow.splitData!.qty) - (displayPrice * saleRow.qty);
      displayPrice = newdisplayPrice - remainderDisplay;
    } else {
      displayPrice = newdisplayPrice;
    }

    return RadiusListener(
      onTap: () async {
        if (saleRow.selected) {
          controller.deselectSaleRow(saleRow);
        } else {
          await controller.selectSaleRow(saleRow);
        }
      },
      tapRadius: 50.0,
      child: saleRow.parent >= 0
          ? Visibility(
              visible: saleRow.isVisible,
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: DecoratedBox(
                      key: key,
                      decoration: BoxDecoration(
                        border: groupSelected
                            ? isLastChild
                                ? Border(
                                    right: BorderSide(
                                      width: selBorderWith,
                                      color: R2Colors.primary300,
                                    ),
                                    left: BorderSide(
                                      width: selBorderWith,
                                      color: R2Colors.primary300,
                                    ),
                                    bottom: BorderSide(
                                      width: selBorderWith,
                                      color: R2Colors.primary300,
                                    ),
                                  )
                                : Border(
                                    right: BorderSide(
                                      width: selBorderWith,
                                      color: R2Colors.primary300,
                                    ),
                                    left: BorderSide(
                                      width: selBorderWith,
                                      color: R2Colors.primary300,
                                    ),
                                  )
                            : null,
                        color: saleRow.selected ? R2Colors.secondary500 : R2Colors.neutral200,
                      ),
                      child: Stack(
                        children: <Widget>[
                          SizedBox(
                            height: modHeight,
                            child: Row(
                              children: <Widget>[
                                Container(width: (15 * indent).toDouble()),
                                if (isComment)
                                  Text(
                                    "! ",
                                    style: TextStyle(
                                      color: saleRow.selected ? R2Colors.primary500 : R2Colors.secondary500,
                                    ),
                                  ),
                                Expanded(
                                  child: Text(
                                    saleRow.receiptDescription,
                                    style: TextStyle(
                                      color: fontColor,
                                      fontSize: 12,
                                      fontWeight: saleRow.selected ? FontWeight.bold : null,
                                      decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                                    ),
                                  ),
                                ),
                                if (controller.showModPrices && saleRow.basePrice != 0)
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      right: selBorderWith,
                                    ),
                                    child: Text(
                                      "${saleRow.basePrice > 0 ? "+" : "-"}\$${Helpers.formatCurrency((saleRow.basePrice * saleRow.qty).abs())}",
                                      textAlign: TextAlign.end,
                                      style: TextStyle(
                                        fontFamily: "Roboto Mono",
                                        fontSize: saleRow.basePrice > 9999 ? 10 : 12,
                                        fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.normal,
                                        color: saleRow.basePrice > 0
                                            ? saleRow.selected
                                                ? R2Colors.white.withOpacity(0.5)
                                                : R2Colors.neutral400
                                            : R2Colors.negativeRedDisabled,
                                        decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: groupSelected ? selBorderWith : 0),
                            child: Container(
                              height: isLastChild && groupSelected ? modHeight - selBorderWith : modHeight,
                              width: tabWidth,
                              color: tabColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          : Padding(
              padding: const EdgeInsets.only(top: 2),
              child: DecoratedBox(
                decoration: BoxDecoration(
                  border: groupSelected
                      ? Border(
                          top: BorderSide(
                            width: selBorderWith,
                            color: R2Colors.primary300,
                          ),
                          left: BorderSide(
                            width: selBorderWith,
                            color: R2Colors.primary300,
                          ),
                          right: BorderSide(
                            width: selBorderWith,
                            color: R2Colors.primary300,
                          ),
                        )
                      : null,
                  color: saleRow.selected ? R2Colors.primary200 : R2Colors.neutral200,
                ),
                child: Stack(
                  alignment: Alignment.bottomLeft,
                  children: <Widget>[
                    ListTile(
                      key: key,
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      title: Column(
                        children: <Widget>[
                          Row(
                            children: <Widget>[
                              Expanded(
                                flex: 2,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: <Widget>[
                                    if (saleRow.flags.contains(SaleRowFlags.VOIDED.index))
                                      Text(
                                        "${saleRow.qty}",
                                        style: TextStyle(
                                          fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.bold,
                                          color: fontColor,
                                          fontSize: saleRow.qty > 999
                                              ? 10
                                              : saleRow.qty > 99
                                                  ? 11
                                                  : 12,
                                          decoration: saleRow.flags.contains(
                                            SaleRowFlags.VOIDED.index,
                                          )
                                              ? TextDecoration.lineThrough
                                              : null,
                                        ),
                                      )
                                    else
                                      Text(
                                        "${saleRow.qty}",
                                        style: TextStyle(
                                          fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.bold,
                                          color: fontColor,
                                          fontSize: saleRow.qty > 999
                                              ? 10
                                              : saleRow.qty > 99
                                                  ? 11
                                                  : 12,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 11,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Expanded(
                                      child: Text(
                                        saleRow.receiptDescription,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: saleRow.receiptDescription.length > 30
                                              ? 10
                                              : saleRow.receiptDescription.length > 20
                                                  ? 12
                                                  : 14,
                                          fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.normal,
                                          color: fontColor,
                                        ),
                                      ),
                                    ),
                                    if (saleRow.flags.contains(SaleRowFlags.VOIDED.index))
                                      Padding(
                                        padding: const EdgeInsets.only(left: 3),
                                        child: ColoredBox(
                                          color: fontColor,
                                          child: Text(
                                            "VOID",
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.normal,
                                              color: saleRow.selected ? R2Colors.primary200 : const Color.fromARGB(255, 245, 245, 245),
                                            ),
                                          ),
                                        ),
                                      )
                                    else if (saleRow.splitData != null)
                                      Padding(
                                        padding: const EdgeInsets.only(left: 3),
                                        child: ColoredBox(
                                          color: R2Colors.secondary500,
                                          child: Text(
                                            "SPLIT",
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: saleRow.selected ? R2Colors.primary200 : const Color.fromARGB(255, 245, 245, 245),
                                            ),
                                          ),
                                        ),
                                      ),
                                    if (holdRow)
                                      Padding(
                                        padding: const EdgeInsets.only(left: 3),
                                        child: ColoredBox(
                                          color: R2Colors.negativeRedDark,
                                          child: Text(
                                            "HOLD",
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: saleRow.selected ? R2Colors.primary200 : const Color.fromARGB(255, 245, 245, 245),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 5,
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    right: selBorderWith,
                                  ),
                                  child: Text(
                                    "${displayPrice < 0 ? "-" : ""}\$${Helpers.formatCurrency(displayPrice.abs())}",
                                    textAlign: TextAlign.end,
                                    style: TextStyle(
                                      fontFamily: "Roboto Mono",
                                      fontSize: displayPrice > 99999
                                          ? 10
                                          : displayPrice > 9999
                                              ? 12
                                              : 14,
                                      fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.normal,
                                      color: fontColor,
                                      decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: <Widget>[
                              weightSlot,
                            ],
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: groupSelected ? selBorderWith : 0),
                      child: Container(
                        height: groupSelected ? parentHeight - selBorderWith : parentHeight,
                        width: tabWidth,
                        color: tabColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
