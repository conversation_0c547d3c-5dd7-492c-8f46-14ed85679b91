import 'dart:async';
import 'dart:convert';

import 'package:backoffice/app/data/models/cashier_discounts.dart';
import 'package:backoffice/app/data/models/cashier_media.dart';
import 'package:backoffice/app/data/models/cashier_sales.dart';
import 'package:backoffice/app/data/models/cashier_stats.dart';
import 'package:backoffice/app/data/models/emp_media_breakdown.dart';
import 'package:backoffice/app/data/models/emp_record.dart';
import 'package:backoffice/app/data/models/emp_sales_by_dept.dart';
import 'package:backoffice/app/data/models/emp_sales_tax.dart';
import 'package:backoffice/app/data/models/emp_statistics.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/sale_window.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/register_menu_options.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/item.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/department.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/num_pad/controller.dart';
import 'package:desktop/app/global_widgets/widget/confirmation_dialog.dart';
import 'package:desktop/app/global_widgets/widget/thin_bottom_sheet.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/dialogs/new_customer/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/scale_weight/dialog.dart';
import 'package:desktop/app/modules/register/widgets/open_sales/controller.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/hardware-interfaces/printer_interface.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:lottie/lottie.dart';

final Logger _logger = Logger('MenuController');

const int REFUND_PAGE_SIZE = 15;

const String GET_CUSTOMER_BY_NAME_QUERY = '''
  query GET_CUSTOMER_BY_NAME(\$param: String) {
    search_customer(args: {param: \$param}) {
      customer
      document
      created_at
      created_by
      updated_at
      updated_by
      name:search_customer_full_name
      phone: document(path: "phone1")
    }
  }
''';

const String INSERT_CUSTOMER_ONE = '''
  mutation INSERT_CUSTOMER_ONE(\$object: customer_insert_input!) {
    insert_customer_one(object: \$object) {
      created_at
      created_by
      customer
      document
      updated_at
      updated_by
    }
  }
''';

const String GET_REASON_CODE = '''
        query GET_SYSTEM_SETTING {
            json_record(where: {record_key: {_eq: "reasonCode"}}) {
              document
              record_key
              updated_at
            }
          }
        ''';

class UserMenuController extends GetxController {
  UserMenuController({
    this.fields,
  });

  Map<String, dynamic>? fields;

  final NotificationService _notificationService = Get.find();
  final ConfigService _configService = Get.find();
  final SaleService _saleService = Get.find();
  final PrinterService _printerService = Get.find();
  final DepartmentService _departmentService = Get.find();
  final IdentityService _identityService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final CustomerService _customerService = Get.find();
  final ReportService _reportService = Get.find();
  final ActivityService _activityService = Get.find();
  final PaymentService _paymentService = Get.find();
  final SaleController _saleController = Get.find();
  final OpenSalesController _openSalesController = Get.find();
  final RegisterController _registerController = Get.find();
  final NumPadController numPadController = Get.find();

  final FocusNode saleDescriptionFocusNode = FocusNode();

  final RxInt promiseHourController = 1.obs;
  final RxInt promiseMinuteController = 0.obs;
  final RxBool promisePM = false.obs;
  String? currentPromisedTime;

  RxBool processing = false.obs;
  RxBool refundDialog = false.obs;
  Rx<Container> processingPromptWidget = Container().obs;
  RxString processingResultText = "".obs;
  bool fromToolbar = false;

  // OPEN PRICE MENU
  RxBool openPriceTaxable = false.obs;
  RxList<Department> availableDepartments = <Department>[].obs;
  RxString assignedDepartmentId = "".obs;
  RxInt openPriceQty = 1.obs;
  RxBool weighedToggle = false.obs;
  RxBool allowEBTToggle = false.obs;
  // ignore: non_constant_identifier_names
  RxBool EBTEnabled = false.obs;
  RxInt openPriceUOM = 0.obs;

  GlobalKey<FormState> cashierFormKey = GlobalKey<FormState>();

  final RxBool menuLoaded = false.obs;
  final RxBool superMenuOpen = false.obs;

  final Rx<RegisterMenuOptions> currentSettingScreen = RegisterMenuOptions.ROOT.obs;

  // TODO: maybe turn into a Rx<Config>? - JJL
  RegisterMenusJsonRecord registerToolBarConfig = RegisterMenusJsonRecord.empty();

  Rxn<SaleTender> adjustingTender = Rxn<SaleTender>();

  // ATTACH CUSTOMER
  TextEditingController attachCustomerController = TextEditingController();
  Rx<Customer> currentCustomer = Customer.empty().obs;
  RxList<Customer> customerSearchResults = <Customer>[].obs;
  final Rx<Customer> newCustomer = Customer.empty().obs;
  RxBool isLoadingCustomers = false.obs;
  ScrollController scrollController = ScrollController();
  RxString previousCashier = "".obs;

  //Cashier variables
  RxnString initialCashier = RxnString();
  RxnString selectedCashier = RxnString();
  RxString cashierReportStartDate = "".obs;
  RxInt cashierSalesTotal = 0.obs;
  RxInt cashierMediaTotal = 0.obs;
  RxInt cashierMediaCCTips = 0.obs;
  RxInt cashierSalesWithTipTotal = 0.obs;
  RxInt cashierDiscountTotal = 0.obs;
  RxInt cashierCashTotal = 0.obs;
  RxInt cashierPaidOut = 0.obs;
  RxInt cashierPickUp = 0.obs;
  RxInt cashierLoan = 0.obs;
  RxInt pickUpValue = 0.obs;
  RxInt cashDrawerValue = 0.obs;

  DateTime serverStart = DateTime.now();
  DateTime serverEnd = DateTime.now();

  // RxInt serverDeptCount = 0.obs;
  // RxInt serverDeptTotal = 0.obs;
  // RxInt serverMediaCount = 0.obs;
  // RxInt serverMediaTotal = 0.obs;
  // RxInt serverGratTotal = 0.obs;
  // RxInt serverTipTotal = 0.obs;
  // RxInt serverTipGratTotal = 0.obs;
  // RxInt serverTaxTotal = 0.obs;

  String saleName = "Sale";

  final Rx<JsonRecordCashierDocument> cashierDocument = JsonRecordCashierDocument.empty().obs;

  final RxList<SystemDeviceJsonRecordTerminal> terminalList = <SystemDeviceJsonRecordTerminal>[].obs;
  final RxnString printCashierScope = RxnString();
  RxList<String> cashierList = <String>[].obs;
  RxList<CashierSales> cashierSalesList = <CashierSales>[].obs;
  RxList<CashierDiscounts> cashierDiscountList = <CashierDiscounts>[].obs;
  RxList<CashierMedia> cashierMediaList = <CashierMedia>[].obs;

  RxList<CashierStats> cashierStatsList = <CashierStats>[].obs;

  final GlobalKey<FormState> addCustomerFormKey = GlobalKey<FormState>();

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController phone1Controller = TextEditingController();
  TextEditingController phone2Controller = TextEditingController();
  TextEditingController badgeController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  TextEditingController addressController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController zipCodeController = TextEditingController();
  TextEditingController companyController = TextEditingController();
  TextEditingController directionsController = TextEditingController();
  TextEditingController deliveryZoneController = TextEditingController();

  TextEditingController midInitController = TextEditingController();
  TextEditingController ytdYearController = TextEditingController();
  TextEditingController ytdTotalController = TextEditingController();
  TextEditingController ytxTotalController = TextEditingController();
  TextEditingController availCreditController = TextEditingController();
  TextEditingController creditLimitController = TextEditingController();
  TextEditingController userDataController = TextEditingController();
  TextEditingController ext1Controller = TextEditingController();
  TextEditingController ext2Controller = TextEditingController();
  TextEditingController numberController = TextEditingController();

  SaleWindow saleWindow = SaleWindow.empty();

  JsonRecordReasonCode reasonCodeRecord = JsonRecordReasonCode.empty();
  RxnString? reasonValue = RxnString();
  final Rx<TextEditingController> saleDescriptionController = TextEditingController().obs;

  EmployeeRecord employeeServerRecord = EmployeeRecord(
    employeeID: 0,
    employee: Employee.empty(),
    taxRows: <EmployeeSalesTax>[],
    empStats: EmployeeStatistics.empty(),
    salesByDeptList: <EmployeeSalesByDepartment>[],
    mediaBreakdownList: <EmployeeMediaBreakdown>[],
    tipBreakdownList: <EmployeeTipBreakdown>[],
  );

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onReady() async {
    if (currentSettingScreen.value == RegisterMenuOptions.CUSTOMER_ATTACH) {
      // Fetch the customers or something?
    }

    Future<void>.delayed(Duration.zero, () async {
      final Either<ServiceError, List<Department>> result = await _departmentService.getDepartments();

      result.fold(
        (ServiceError error) => _notificationService.error(error.message),
        (List<Department> incomingDepartments) => availableDepartments.value = incomingDepartments,
      );
    });

    super.onReady();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    EBTEnabled.value = _configService.merchantConfig.document.ebt;

    _saleController.currentSale.value.match(
      (Sale s) {
        if (s.document.saleHeader.promisedTime != null) {
          millisecondsToTimeInts(s.document.saleHeader.promisedTime!);
        }
      },
      () => null,
    );

    if (_saleController.autoRefund.value) {
      currentSettingScreen.value = RegisterMenuOptions.TRANSACTION_HISTORY;
      Future<void>.delayed(Duration.zero, () async {
        _saleController.autoRefund.value = false;
        _saleController.autoRefundNumber.value = "";
        _saleController.autoRefundBatch.value = "";
      });
    }

    if (currentSettingScreen.value == RegisterMenuOptions.ROOT) {
      _registerController.menuOpen = true;
    }

    menuLoaded.value = true;

    saleWindow = Helpers.getReportWindow(DateTime.now(), scope: 'Day');

    await loadCashiers();
    await loadPreviousCashier();
    await getServerTimes();
    await getReasonCodes();

    saleName = _saleController.saleName.value;

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onClose() async {
    _registerController.shrinkDrawer();
    // numPadController.text.value = none();
    if (processing.value) await cancelTransaction();
    currentSettingScreen.value = RegisterMenuOptions.ROOT;
    _registerController.menuOpen = false;

    super.onClose();
  }

  Future<void> getServerTimes() async {
    try {
      TimeCard? lastPunchIn;

      final QueryResult<Object?> cardQueryRes = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString('''
        query GET_RECENT_TIMECARD {
          timecard(order_by: {punch_at: desc}, where: {emp_id: {_eq: "${_identityService.currentEmployee.id}"}, punch_type: {_eq: 1}, break_idx: {_eq: 0}}, limit: 1) {
              timecard
              round_at
              punch_type
              punch_at
              job_code
              emp_id
              break_idx
            }
          }
          '''),
        ),
      );

      if (cardQueryRes.hasException) {
        throw cardQueryRes.exception.toString();
      }

      final List<dynamic> cardQueryList = cardQueryRes.data?["timecard"] as List<dynamic>;

      if (cardQueryList.isNotEmpty) {
        lastPunchIn = TimeCard.fromJson(cardQueryList[0] as Map<String, dynamic>);
      }

      serverEnd = DateTime.now();
      serverStart = DateTime.parse(lastPunchIn == null ? serverEnd.toString() : lastPunchIn.punch_at ?? serverEnd.toString());

      serverStart = serverStart.subtract(serverStart.toLocal().timeZoneOffset);
      serverEnd = serverEnd.toUtc();
    } catch (e) {
      _notificationService.error('Error getting Server Times');
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> setCashierTitle(String cashier) async {
    try {
      final Activity insertCashierChange = Activity(
        activity: ActivityFlags.SET_CASHIER.index,
        emp_id: _identityService.currentEmployee.id,
        term_num: _identityService.terminalNumber,
        str_data: cashier,
      );

      await _activityService.insertActivity(activity: insertCashierChange);
      await _identityService.updateCurrentCashier(selectedCashier.value);
      initialCashier.value = cashier;

      // await loadPreviousCashier();
      // await getCashierReportData(initialCashier.value!);

      return _notificationService.success("Successfully set $cashier");
    } catch (err, stack) {
      _logger.severe(
        "Error fetching sales",
        err,
        stack,
      );
      return _notificationService.error("Failed to set Cashier");
    }
  }

  ///
  ///
  ///
  ///
  ///
  Future<void> printCashierReport() async {
    final PrintJob printJobType = PrinterInterface.printCashierReport(
      terminalNumber: _identityService.terminalNumber,
      cashier: initialCashier.value ?? "",
      cashierDiscounts: cashierDiscountList,
      cashierSales: cashierSalesList,
      cashierStats: cashierStatsList,
      cashierMedia: cashierMediaList,
      paidOutTotal: cashierPaidOut.value,
      pickUpTotal: cashierPickUp.value,
      loanTotal: cashierLoan.value,
      periodEnd: DateTime.now().toUtc().toString(),
      periodStart: DateTime.parse(cashierReportStartDate.toString()).toLocal().toString(),
    );

    try {
      final Map<String, dynamic> santizedPrintJob = Helpers.sanitizeEntity(
        printJobType.toJson(),
        <String>[
          'print_job',
          'created_at',
        ],
      );
      final QueryResult<Object> printJobResult = await _graphqlService.client.mutate(
        MutationOptions<Object>(
          document: g.parseString(
            '''
             mutation INSERT_PRINT_JOB(\$printJob: print_job_insert_input!) {
              insert_print_job_one(object: \$printJob) {
                print_job
                document
                created_at
              }
            }
            ''',
          ),
          variables: <String, dynamic>{
            "printJob": santizedPrintJob,
          },
        ),
      );

      if (printJobResult.hasException) {
        return _notificationService.error(printJobResult.exception.toString());
      }
      _notificationService.success("Printed Cashier Report");
    } catch (err, stack) {
      _notificationService.error("Failed to Print Report");
      _logger.severe('Printing error', err, stack);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getCashierReportData(String cashier) async {
    try {
      final Either<ServiceError, List<CashierSales>> cashierSales = await _reportService.getCashierSales(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierSales.fold(
        (ServiceError l) {
          _notificationService.error("error gettin cashier sales");
          throw l.message;
        },
        (List<CashierSales> r) => cashierSalesList.value = r,
      );

      final Either<ServiceError, List<CashierStats>> cashierStats = await _reportService.getCashierStats(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierStats.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier stats");
          throw l.message;
        },
        (List<CashierStats> r) => cashierStatsList.value = r,
      );

      final Either<ServiceError, List<CashierDiscounts>> cashierDiscounts = await _reportService.getCashierDiscounts(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierDiscounts.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier Discounts");
          throw l.message;
        },
        (List<CashierDiscounts> r) => cashierDiscountList.value = r,
      );

      final Either<ServiceError, List<CashierMedia>> cashierMedia = await _reportService.getCashierMedia(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierMedia.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier Media");
          throw l.message;
        },
        (List<CashierMedia> r) => cashierMediaList.value = r,
      );

      final Either<ServiceError, int> cashierPaidOutRes = await _reportService.getCashierPaidOut(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierPaidOutRes.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier Media");
          throw l.message;
        },
        (int r) => cashierPaidOut.value = r,
      );
      final Either<ServiceError, int> cashierPickUpRes = await _reportService.getCashierPickUp(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierPickUpRes.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier Media");
          throw l.message;
        },
        (int r) => cashierPickUp.value = r,
      );
      final Either<ServiceError, int> cashierLoanRes = await _reportService.getCashierLoan(
        startDate: cashierReportStartDate.value,
        endDate: DateTime.now().toUtc().toString(),
        selectedCashier: cashier,
        selectedTerminal: _identityService.terminalNumber,
      );
      cashierLoanRes.fold(
        (ServiceError l) {
          _notificationService.error("failed to get cashier Media");
          throw l.message;
        },
        (int r) => cashierLoan.value = r,
      );
    } catch (err, stack) {
      _logger.severe(
        "Error fetching sales",
        err,
        stack,
      );
      return _notificationService.error("Failed to get cashier data");
    }
  }

  ///
  ///
  ///
  ///
  int getTerminaNumber() {
    return _identityService.terminalNumber;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> loadCashiers() async {
    final Either<ServiceError, List<String>> cashierResult = await getCashiers();

    cashierResult.fold((ServiceError l) => _notificationService.error(l.message), (List<String> r) {
      cashierList.value = r;
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> loadPreviousCashier() async {
    final Either<ServiceError, Activity> setCashierResult = await _activityService.getLastCashier(_identityService.terminalNumber);

    setCashierResult.fold(
      (ServiceError l) => _notificationService.error(l.message),
      (Activity r) {
        initialCashier.value = r.str_data;
        selectedCashier.value = r.str_data;
        printCashierScope.value = r.str_data;
        cashierReportStartDate.value = r.created_at!.toUtc().toString();
      },
    );
  }

  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<String>>> getCashiers() async {
    const String cashierQueryString = '''
        query GET_SYSTEM_SETTING {
            json_record(where: {record_key: {_eq: "cashier"}}) {
              document
              record_key
              updated_at
            }
          }
        ''';

    try {
      final QueryResult<Object?> cashierResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(cashierQueryString),
        ),
      );

      cashierDocument.value = JsonRecordCashierDocument.fromJson(
        // ignore: avoid_dynamic_calls
        cashierResult.data!['json_record'][0]['document'] as Map<String, dynamic>,
      );

      // ignore: always_specify_types
      return Right(cashierDocument.value.cashiers);
    } catch (err, stack) {
      _logger.severe(
        "Error fetching sales",
        err,
        stack,
      );
      return Left<ServiceError, List<String>>(
        ServiceError("Failed to get cashiers!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePaidOut() async {
    if (!_registerController.canOpenCashDrawer) return _notificationService.error("No cash drawer assigned on terminal for current employee!");
    if (reasonCodeRecord.document.paidOut.isEmpty) {
      reasonValue?.value = saleDescriptionController.value.text;
      saleDescriptionController.value.clear();
    }

    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;

    numPadController.text.value.match(
      (String textString) async {
        final int amount = int.parse(textString.replaceAll(RegExp(r'\.|\,|\$'), ''));
        if (amount == 0) {
          return _notificationService.error("You must enter an amount");
        }

        if (reasonValue?.value == null || reasonValue?.value == "") {
          return _notificationService.error("You must enter a memo");
        }

        final Sale sale = Sale.empty();
        sale.document.saleHeader.total = amount;
        sale.document.saleHeader.subTotal = amount;
        sale.end_at = DateTime.now().toUtc();
        sale.document.saleHeader.saleFlags.add(
          SaleFlags.PAID_OUT.index,
        );
        sale.document.saleHeader.saleFlags.add(
          SaleFlags.COMPLETED.index,
        );
        sale.document.saleHeader.tenders.add(
          SaleTender(
            amount: amount,
            media: PaymentMediaType.Cash.index,
            saleTenderFlags: <int>[],
          ),
        );
        sale.document.saleHeader.currentCashier = _identityService.currentCashier;
        sale.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id;
        sale.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;
        sale.document.saleHeader.saleDescription = reasonValue?.value;
        sale.document.saleHeader.cashDrawer = _configService.merchantConfig.document.multiCashDrawer ? _identityService.employeeDrawer?.idx : 0;
        final Either<ServiceError, Sale> result = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );

        result.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (Sale sale) async {
            await _registerController.openCashDrawer();

            numPadController.text.value = none();
            _notificationService.success("Successfully Paid Out!");
            unawaited(
              _activityService.insertActivity(
                activity: Activity(
                  activity: ActivityFlags.PAID_OUT.index,
                  data1: sale.document.saleHeader.total,
                  emp_id: _identityService.currentEmployee.id,
                  sale_num: _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale_number,
                  term_num: _identityService.terminalNumber,
                  str_data: "Paid Out to ${sale.document.saleHeader.saleDescription}",
                ),
              ),
            );

            // Make merchant print job
            final PrintJob merchantCopyPrintJob = PrinterInterface.buildReceipt(
              currentEmployee: _identityService.currentEmployee,
              sale: sale,
              saleName: "Paid Out",
              customerCopy: false,
              receiptHeader: merchantDoc.receiptHeader,
              refundReceiptSignatureLine: merchantDoc.refundReceiptSignatureLine,
              currentUser: _identityService.currentEmployee,
              terminalDescs: _identityService.terminalDescs,
              legacyGiftName: merchantDoc.legacyGiftName,
              showCommentRows: merchantDoc.commentRowsOnReceipt,
              showSaleDesc: merchantDoc.saleDescOnReceipt,
              showSeats: _saleController.getProperSection().trackBySeat,
              condensedAuth: merchantDoc.condensedAuthSlip,
            );

            final PrintJobReturn merchantPrintResult = await _printerService.printReceipt(
              remotePrintIdx: _identityService.remoteTermIdx,
              printJob: merchantCopyPrintJob,
            );

            final List<ServiceError> errs = merchantPrintResult.errors ?? <ServiceError>[];

            if (errs.isNotEmpty) {
              _notificationService.error(errs.first.message);
            }

            // Make customer print job
            if (merchantDoc.paidOutPrintCustomer == true) {
              final PrintJob customerCopyPrintJob = PrinterInterface.buildReceipt(
                currentEmployee: _identityService.currentEmployee,
                sale: sale,
                saleName: "Paid Out",
                receiptHeader: merchantDoc.receiptHeader,
                currentUser: _identityService.currentEmployee,
                terminalDescs: _identityService.terminalDescs,
                legacyGiftName: merchantDoc.legacyGiftName,
                showCommentRows: merchantDoc.commentRowsOnReceipt,
                showSaleDesc: merchantDoc.saleDescOnReceipt,
                showSeats: _saleController.getProperSection().trackBySeat,
                condensedAuth: merchantDoc.condensedAuthSlip,
              );

              final PrintJobReturn customerPrintResult = await _printerService.printReceipt(
                printJob: customerCopyPrintJob,
                remotePrintIdx: _identityService.remoteTermIdx,
              );

              final List<ServiceError> errors = customerPrintResult.errors ?? <ServiceError>[];

              if (errors.isNotEmpty) {
                _notificationService.error(errors.first.message);
              }
            }
          },
        );
      },
      () => _notificationService.error("You must enter an amount"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> handlePickUp() async {
    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;

    numPadController.text.value.match(
      (String textString) async {
        final int amount = int.parse(textString.replaceAll(RegExp(r'\.|\,|\$'), ''));
        if (amount == 0) {
          return _notificationService.error("You must enter an amount");
        }

        final Sale sale = Sale.empty();
        sale.document.saleHeader.total = amount;
        sale.document.saleHeader.subTotal = amount;
        sale.end_at = DateTime.now().toUtc();
        if (pickUpValue == 1) {
          sale.document.saleHeader.saleFlags.add(
            SaleFlags.PICK_UP.index,
          );
        } else {
          sale.document.saleHeader.saleFlags.add(
            SaleFlags.LOAN_OUT.index,
          );
        }

        sale.document.saleHeader.tenders.add(
          SaleTender(
            amount: amount,
            media: PaymentMediaType.Cash.index,
            saleTenderFlags: <int>[],
          ),
        );
        sale.document.saleHeader.currentCashier = _identityService.currentCashier;
        sale.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id;
        sale.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;

        sale.document.saleHeader.saleDescription = saleDescriptionController.value.text;
        saleDescriptionController.value.clear();
        final Either<ServiceError, Sale> result = await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );

        result.fold(
          (ServiceError error) => _notificationService.error(error.message),
          (Sale sale) async {
            await _registerController.openCashDrawer();

            numPadController.text.value = none();
            if (pickUpValue == 1) {
              _notificationService.success("Successfully Paid Out!");
            } else {
              _notificationService.success("Successfully Paid Out!");
            }
            unawaited(
              _activityService.insertActivity(
                activity: Activity(
                  activity: ActivityFlags.PAID_OUT.index,
                  data1: sale.document.saleHeader.total,
                  emp_id: _identityService.currentEmployee.id,
                  sale_num: _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale_number,
                  term_num: _identityService.terminalNumber,
                  str_data: "Paid Out to ${sale.document.saleHeader.saleDescription}",
                ),
              ),
            );
            if (pickUpValue.value == 1) {
              // Make merchant print job
              final PrintJob merchantCopyPrintJob = PrinterInterface.buildPickUpReceipt(
                currentEmployee: _identityService.currentEmployee,
                sale: sale,
                cashDrawer: cashDrawerValue.value,
                headerText: "Pick Up",
                receiptHeader: merchantDoc.receiptHeader,
                currentUser: _identityService.currentEmployee,
                terminalDescs: _identityService.terminalDescs,
                legacyGiftName: merchantDoc.legacyGiftName,
              );

              // Make customer print job

              final PrintJobReturn merchantPrintResult = await _printerService.printReceipt(
                remotePrintIdx: _identityService.remoteTermIdx,
                printJob: merchantCopyPrintJob,
              );

              final List<ServiceError> errs = merchantPrintResult.errors ?? <ServiceError>[];

              if (errs.isNotEmpty) {
                _notificationService.error(errs.first.message);
              }
            } else {
              final PrintJob merchantCopyPrintJob = PrinterInterface.buildPickUpReceipt(
                currentEmployee: _identityService.currentEmployee,
                sale: sale,
                cashDrawer: cashDrawerValue.value,
                headerText: "Loan",
                receiptHeader: merchantDoc.receiptHeader,
                currentUser: _identityService.currentEmployee,
                terminalDescs: _identityService.terminalDescs,
                legacyGiftName: merchantDoc.legacyGiftName,
              );

              final PrintJobReturn merchantPrintResult = await _printerService.printReceipt(
                printJob: merchantCopyPrintJob,
                remotePrintIdx: _identityService.remoteTermIdx,
              );

              final List<ServiceError> errs = merchantPrintResult.errors ?? <ServiceError>[];

              if (errs.isNotEmpty) {
                _notificationService.error(errs.first.message);
              }
            }
          },
        );
      },
      () => _notificationService.error("You must enter an amount"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addOpenPriceSaleRow({
    bool isWeighed = false,
    bool ebtAllowed = false,
  }) async {
    if (assignedDepartmentId.value.isEmpty) {
      return _notificationService.error("Select a department!");
    }

    final Department selectedDepartment = availableDepartments.firstWhere(
      (Department department) => department.department == assignedDepartmentId.value,
    );

    numPadController.text.value.match(
      (String textString) async {
        final int globalQty = _saleController.globalQuantity.value;
        final int amount = int.parse(textString.replaceAll(RegExp(r'\.|\,|\$'), ''));
        if (amount == 0) {
          return _notificationService.error("You must enter a price amount");
        }

        final MerchantJsonRecordDocument doc = _configService.merchantConfig.document;
        final int creditAmount = doc.dualPricing
            ? Helpers.getCreditPrice(
                cashAmount: amount,
                creditAmount: 0,
                dualPercent: doc.dualPricingPercent,
                roundAmount: 1,
              )
            : amount;

        if (isWeighed) {
          // ignore: unnecessary_null_comparison
          if (openPriceUOM.value == null) {
            return _notificationService.error("Select a UOM!");
          }
          final Item openPriceItem = Item.empty();
          openPriceItem.departmentByDepartment = selectedDepartment;
          openPriceItem.long_desc = "OPEN PRICE (${selectedDepartment.title})";
          openPriceItem.item = '00000000-0000-0000-0000-000000000001';
          openPriceItem.upc = '';
          openPriceItem.document.receiptDesc = openPriceItem.long_desc;
          openPriceItem.document.pricing["S0L0C0"] = creditAmount;
          openPriceItem.document.pricing["S0L0C1"] = amount;
          openPriceItem.document.UOM = openPriceUOM.value;
          openPriceItem.document.isWeighted = true;
          openPriceItem.document.allowEbt = true;

          final SaleRow? result = await Get.bottomSheet<SaleRow>(
            ThinBottomSheet(
              child: ScaleWeightDialog(item: openPriceItem),
            ),
            isScrollControlled: true,
            ignoreSafeArea: true,
            backgroundColor: Colors.transparent,
          );

          if (result == null) return;
          result.flags.add(SaleRowFlags.OPEN_PRICE.index);

          if (ebtAllowed) {
            result.flags.add(SaleRowFlags.ALLOW_EBT.index);
          }
          result.taxFlags = selectedDepartment.document.taxFlags;
          result.origTaxFlags = selectedDepartment.document.taxFlags;

          await _saleController.addSaleRowToCart(saleRow: result);
          await _activityService.insertActivity(
            activity: Activity(
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              str_data: "Insert Open Price item(Weighted)",
              data1: openPriceItem.document.pricing["S0L0C0"],
              data2: openPriceItem.document.pricing["S0L0C1"],
              activity: ActivityFlags.OPEN_PRICE.index,
              sale_num: _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale_number,
            ),
          );
          Get.back();
        } else {
          await _saleController.addSaleRowToCart(
            saleRow: SaleRow(
              receiptDescription: "OPEN PRICE (${selectedDepartment.title})",
              department: selectedDepartment.title,
              flags: <int>[
                SaleRowFlags.OPEN_PRICE.index,
                if (selectedDepartment.document.isTaxable!) SaleRowFlags.TAXABLE.index,
                if (ebtAllowed) SaleRowFlags.ALLOW_EBT.index,
                if ((selectedDepartment.document.printInRed ?? false) || (selectedDepartment.document.printInRed ?? false))
                  SaleRowFlags.PRINT_RED.index,
              ],
              item: '00000000-0000-0000-0000-000000000001',
              upc: '',
              transactionFlags: <int>[],
              originalPrice: creditAmount,
              cashOriginalPrice: amount,
              basePrice: creditAmount,
              cashBasePrice: amount,
              qty: openPriceQty.value,
              taxFlags: selectedDepartment.document.taxFlags,
              origTaxFlags: selectedDepartment.document.taxFlags,
            ),
          );
          await _activityService.insertActivity(
            activity: Activity(
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              str_data: "Insert Open Price item",
              data1: creditAmount,
              data2: amount,
              activity: ActivityFlags.OPEN_PRICE.index,
              sale_num: _saleController.currentSale.value.getOrElse(() => Sale.empty()).sale_number,
            ),
          );
        }

        _saleController.globalQuantity.value = globalQty;
        numPadController.text.value = none();
        openPriceQty.value = 1;
      },
      () => _notificationService.error("You must enter a price amount"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> inquireEBTBalance() async {
    processing.value = true;

    // TODO: make this work
    // final Either<ServiceError, SystemDeviceJsonRecordTerminal> getSystemDeviceTerminalResult = await _configService.getSystemDeviceTerminalByIndex(
    //   index: _identityService.terminalNumber,
    // );

    // final Option<SystemDeviceJsonRecordTerminal> systemDeviceTerminal = getSystemDeviceTerminalResult.fold(
    //   (ServiceError error) {
    //     _notificationService.error(error.message);
    //     processing.value = false;

    //     return none();
    //   },
    //   (SystemDeviceJsonRecordTerminal systemDeviceTerminal) => some(systemDeviceTerminal),
    // );

    // if (systemDeviceTerminal.isNone()) return;

    // final PaymentRequest request = PaymentRequest.fromJson(
    //   {"action": "INQUIRE_EBT"},
    // );
    // request.extData = ExtData.fromJson({"ebtType": "F"});

    // //pass transaction off to payments terminal and wait for reply
    // final PaymentResponse result = await PaymentService().run(
    //   request,
    //   systemDeviceTerminal.getOrElse(
    //     () => SystemDeviceJsonRecordTerminal.empty(),
    //   ),
    // );

    // if (result.returnCode == RegisterResponseCode.SUCCESS) {
    //   processingResultText.value = "EBT Balance: \$${Helpers.formatCurrency(result.data!.remainingBalance!)}";
    // } else {
    //   processingResultText.value = result.data!.message ?? "Unable to Get Balance!";
    // }
    processing.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  /// Tries to cancel the currently running transaction
  Future<void> cancelTransaction() async {
    processing.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  /// Accepts a string and searches the customer table. Returns a list of [Customer].
  Future<List<Customer>> searchCustomer(String searchString) async {
    try {
      if (searchString.isEmpty) return <Customer>[];
      isLoadingCustomers.value = true;
      //TODO: maybe add a case to show a load wheel while searching and a no data result

      final Either<ServiceError, List<Customer>> searchResult = await _customerService.searchCustomer(searchString);

      List<Customer> returnCustomers = <Customer>[];

      searchResult.fold(
        (ServiceError error) => _notificationService.error(error.message),
        (List<Customer> customers) async {
          returnCustomers = customers;
        },
      );
      isLoadingCustomers.value = false;
      return returnCustomers;
    } catch (err, stack) {
      _logger.severe('Error searching customer', err, stack);
      isLoadingCustomers.value = false;
      return <Customer>[];
    }
  }

  ///
  ///
  ///
  ///
  ///
  /// Attaches a customer to the current sale
  Future<void> attachCustomer() async {
    if (fields != null) {
      if (fields!["newSale"] != null) {
        final Sale s = fields!["newSale"] as Sale;
        s.document.saleHeader.customer = currentCustomer.value.customer;
        s.document.saleHeader.customerName = "${currentCustomer.value.document.firstName} ${currentCustomer.value.document.lastName}";
        s.document.saleHeader.priceLevel = currentCustomer.value.document.priceLevel ?? 0;
        attachCustomerController.text = "";
        Get.back();
        return;
      }
    }
    await _saleController.currentSale.value.match((Sale t) async {
      final int originalPriceLevel = t.document.saleHeader.priceLevel ?? 0;
      t.document.saleHeader.customer = currentCustomer.value.customer;
      t.document.saleHeader.customerName = "${currentCustomer.value.document.firstName} ${currentCustomer.value.document.lastName}";
      t.document.saleHeader.priceLevel = currentCustomer.value.document.priceLevel ?? 0;
      _saleController.currentCustomer.value = some(currentCustomer.value);
      _saleController.currentSale.refresh();
      attachCustomerController.text = "";
      currentCustomer.value = Customer.empty();
      if (originalPriceLevel != t.document.saleHeader.priceLevel) {
        for (final SaleRow r in t.document.saleRows) {
          await _saleController.updateRowPricing(r);
        }
        _saleController.refreshCurrentSaleModels();
      }
      customerSearchResults.value = <Customer>[];
      await _saleService
          .upsert(
            sale: t,
            employee: _identityService.currentEmployee,
            terminalNumber: _identityService.terminalNumber,
          )
          .then(
            (Either<ServiceError, Sale> value) => value.match(
              (ServiceError e) => _notificationService.error(e.message),
              (Sale s) => _notificationService.success("Customer attached!"),
            ),
          );
      if (fromToolbar) {
        Get.back();
        return;
      }
      currentSettingScreen.value = RegisterMenuOptions.ROOT;
    }, () async {
      await _saleController.startEmptySale();
      await attachCustomer();
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> showNewCustomerDialog() async {
    await Get.bottomSheet(
      NewCustomerBottomSheet(),
      isScrollControlled: true,
      ignoreSafeArea: true,
    );
    firstNameController.clear();
    lastNameController.clear();
    phone1Controller.clear();
    phone2Controller.clear();
    badgeController.clear();
    emailController.clear();
    addressController.clear();
    cityController.clear();
    stateController.clear();
    companyController.clear();
    return;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addCustomer() async {
    try {
      newCustomer.value.created_by = _identityService.currentEmployee.employee;
      newCustomer.value.updated_by = _identityService.currentEmployee.employee;

      newCustomer.value.document.firstName = firstNameController.text;
      newCustomer.value.document.lastName = lastNameController.text;
      newCustomer.value.document.phone1 = phone1Controller.text;
      newCustomer.value.document.phone2 = phone2Controller.text;
      newCustomer.value.document.badge = badgeController.text;
      newCustomer.value.document.email = emailController.text;

      newCustomer.value.document.address = addressController.text;
      newCustomer.value.document.city = cityController.text;
      newCustomer.value.document.state = stateController.text;
      newCustomer.value.document.zip = zipCodeController.text;
      newCustomer.value.document.company = companyController.text;
      newCustomer.value.document.directions = directionsController.text;
      newCustomer.value.document.deliveryZone = deliveryZoneController.text != "" ? int.parse(deliveryZoneController.text) : 0;

      newCustomer.value.document.midInit = midInitController.text;
      newCustomer.value.document.ytdYear = ytdYearController.text;
      newCustomer.value.document.ytdTotal = ytdTotalController.text != "" ? int.parse(ytdTotalController.text) : 0;
      newCustomer.value.document.ytxTotal = ytxTotalController.text != "" ? int.parse(ytxTotalController.text) : 0;
      newCustomer.value.document.availCredit = availCreditController.text != "" ? int.parse(availCreditController.text) : 0;
      newCustomer.value.document.creditLimit = creditLimitController.text != "" ? int.parse(creditLimitController.text) : 0;
      newCustomer.value.document.ext1 = ext1Controller.text;
      newCustomer.value.document.ext2 = ext2Controller.text;
      newCustomer.value.document.number = numberController.text != "" ? int.parse(numberController.text) : 0;

      if (!addCustomerFormKey.currentState!.validate()) throw "Invalid form";

      final Either<ServiceError, Customer> addCustomerResult = await _customerService.insertCustomer(customer: newCustomer.value);

      addCustomerResult.fold(
        (ServiceError error) => _notificationService.error(error.message),
        (Customer customer) async {
          currentCustomer.value = customer;
        },
      );

      Get.back();

      await attachCustomer();
    } catch (err, stack) {
      _logger.severe('error adding customer', err, stack);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> reprintCheck({
    required Option<Sale> sale,
    int? copyOpts,
    bool authSlip = false,
  }) async {
    if (!sale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      return _notificationService.error("This is an incomplete sale!");
    }

    final Sale incomingSale = sale.match((Sale t) => t, () => Sale.empty());

    final List<Future<void>> printResults = <Future<void>>[];

    if (copyOpts != 2 && !authSlip) {
      printResults.add(
        _printerService.printSale(
          sale: incomingSale,
          saleName: _saleController.saleName.value,
        ),
      );
    }
    if (copyOpts != 3 || authSlip) {
      printResults.add(
        _printerService.printSale(
          sale: incomingSale,
          customerCopy: false,
          authSlip: authSlip,
          saleName: _saleController.saleName.value,
        ),
      );
    }

    await Future.wait(printResults);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> makeOpenCopy({
    required Sale sale,
    bool negativeCopy = false,
    bool reOpening = true,
  }) async {
    final Sale? saleCopy;

    /// if a reopened sale make a deep copy and add reopened flag, else start fresh
    if (reOpening) {
      saleCopy = Sale.empty();
      saleCopy.document.saleHeader = Helpers.makeDeepSaleHeaderCopy(sale);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.REFUNDED.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.COMPLETED.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.NONREFUNDABLE.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.CANCEL.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.COMBINED.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.REOPENED.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.NONREFUNDABLE.index);
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.PAID_OUT.index);
      if (!saleCopy.document.saleHeader.saleFlags.contains(SaleFlags.REOPENED.index)) {
        saleCopy.document.saleHeader.saleFlags.add(SaleFlags.REOPENED.index);
      }
      saleCopy.document.saleHeader.saleNumber = 0;
      saleCopy.document.saleHeader.tenders = <SaleTender>[];
      saleCopy.document.saleHeader.seatsSettled = <int>[];
      saleCopy.document.saleHeader.seatsTendered = <int>[];
      saleCopy.document.saleHeader.saleDescription = "REOPENED - $saleName #${sale.sale_number}";
      saleCopy.document.saleHeader.startEmployeeNumber = sale.document.saleHeader.startEmployeeNumber;
      saleCopy.document.saleHeader.currentEmployeeNumber = sale.document.saleHeader.currentEmployeeNumber;
      saleCopy.document.saleHeader.startTerminalNumber = sale.document.saleHeader.startTerminalNumber;
    } else {
      saleCopy = await _saleController.makeNewSale();
    }

    if (saleCopy == null) return;

    saleCopy.document.saleRows = <SaleRow>[...sale.document.saleRows];

    /// if not a reopened sale remove print flags and voided rows
    if (!reOpening) {
      saleCopy.document.saleHeader.saleFlags.remove(SaleFlags.PREP_PRINTED.index);
      for (final SaleRow r in saleCopy.document.saleRows) {
        if (r.flags.contains(SaleRowFlags.VOIDED.index)) {
          for (final SaleRow sr in saleCopy.document.saleRows) {
            if (sr.index > r.index) {
              sr.index = sr.index - 1;
            }
            if (sr.parent > r.index) {
              sr.parent = sr.parent - 1;
            }
          }
        }
        r.flags.remove(SaleRowFlags.PREP_PRINTED.index);
      }
      saleCopy.document.saleRows.removeWhere(
        (SaleRow r) => r.flags.contains(SaleRowFlags.VOIDED.index),
      );
      if (_saleController.custCountOption.value == 2) {
        saleCopy.document.saleHeader.customerCount = sale.document.saleHeader.seatCnt ?? 1;
      }
    }

    saleCopy.document.saleHeader.dualPricingAmount = sale.document.saleHeader.dualPricingAmount;
    saleCopy.document.saleHeader.dualPricingPercent = sale.document.saleHeader.dualPricingPercent;
    saleCopy.document.saleHeader.subTotal = sale.document.saleHeader.subTotal;
    saleCopy.document.saleHeader.cashSubTotal = sale.document.saleHeader.cashSubTotal;
    saleCopy.document.saleHeader.taxTotal = sale.document.saleHeader.taxTotal;
    saleCopy.document.saleHeader.cashTaxTotal = sale.document.saleHeader.cashTaxTotal;
    saleCopy.document.saleHeader.total = sale.document.saleHeader.total;
    saleCopy.document.saleHeader.cashTotal = sale.document.saleHeader.cashTotal;
    saleCopy.document.saleHeader.discountTotal = sale.document.saleHeader.discountTotal;
    saleCopy.document.saleHeader.cashDiscountTotal = sale.document.saleHeader.cashDiscountTotal;
    saleCopy.document.saleHeader.seatCnt = sale.document.saleHeader.seatCnt;
    saleCopy.document.saleHeader.toGoSeatCnt = sale.document.saleHeader.toGoSeatCnt;

    saleCopy.document.saleHeader.cashDrawer = null;

    if (negativeCopy) {
      for (final SaleRow saleRow in saleCopy.document.saleRows) {
        saleRow.actualPrice = saleRow.actualPrice * -1;
        saleRow.grossPrice = saleRow.grossPrice * -1;
        saleRow.creditPrice = saleRow.creditPrice * -1;
        saleRow.basePrice = saleRow.basePrice * -1;
        saleRow.originalPrice = saleRow.originalPrice * -1;
        saleRow.cashGrossPrice = saleRow.cashGrossPrice * -1;
        saleRow.cashPrice = saleRow.cashPrice * -1;
        saleRow.cashBasePrice = saleRow.cashBasePrice * -1;
        saleRow.cashOriginalPrice = saleRow.cashOriginalPrice * -1;
      }
      saleCopy.document.saleHeader.dualPricingAmount *= -1;
      saleCopy.document.saleHeader.dualPricingPercent *= -1;
      saleCopy.document.saleHeader.subTotal *= -1;
      saleCopy.document.saleHeader.cashSubTotal *= -1;
      saleCopy.document.saleHeader.taxTotal *= -1;
      saleCopy.document.saleHeader.cashTaxTotal *= -1;
      saleCopy.document.saleHeader.total *= -1;
      saleCopy.document.saleHeader.cashTotal *= -1;
      saleCopy.document.saleHeader.discountTotal *= -1;
      saleCopy.document.saleHeader.cashDiscountTotal *= -1;
    }

    await _openSalesController.changeSelection(saleCopy);

    if (reOpening) {
      _saleController.currentSale.value.match(
        (Sale sale) async {
          _activityService.insertActivity(
            activity: Activity(
              activity: ActivityFlags.REOPENED_SALE.index,
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              sale_num: sale.sale_number,
              str_data: sale.sale,
            ),
          );
        },
        () => _notificationService.error("Error while reopening sale"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> runRefund({
    required Option<Sale> refundingSale,
    required PaymentMediaType refundMedia,
    bool reopen = false,
  }) async {
    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;

    if (!refundingSale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      return _notificationService.error("This is an incomplete sale!");
    }

    if (refundingSale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.REFUNDED.index)) {
      return _notificationService.error("This sale has already been refunded!");
    }

    if (refundingSale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.NONREFUNDABLE.index)) {
      return _notificationService.error("This sale can not be refunded!");
    }

    final Sale incomingSale = refundingSale.match((Sale t) => t, () => Sale.empty());
    final Sale newNegativeSale = Sale.empty();

    //====================================================================================
    //= LOAD UP THE NEW SALE WITH NEGATIVE VERSIONS OF EVERYTHING FROM THE INCOMING SALE =
    //====================================================================================

    newNegativeSale.document.saleHeader.dualPricingAmount = incomingSale.document.saleHeader.dualPricingAmount * -1;
    newNegativeSale.document.saleHeader.dualPricingPercent = incomingSale.document.saleHeader.dualPricingPercent * -1;
    newNegativeSale.document.saleHeader.cashDiscountTotal = incomingSale.document.saleHeader.cashDiscountTotal * -1;
    newNegativeSale.document.saleHeader.cashSubTotal = incomingSale.document.saleHeader.cashSubTotal * -1;
    newNegativeSale.document.saleHeader.cashTaxTotal = incomingSale.document.saleHeader.cashTaxTotal * -1;
    newNegativeSale.document.saleHeader.cashTotal = incomingSale.document.saleHeader.cashTotal * -1;
    newNegativeSale.document.saleHeader.discountTotal = incomingSale.document.saleHeader.discountTotal * -1;
    newNegativeSale.document.saleHeader.subTotal = incomingSale.document.saleHeader.subTotal * -1;
    newNegativeSale.document.saleHeader.taxTotal = incomingSale.document.saleHeader.taxTotal * -1;
    newNegativeSale.document.saleHeader.total = incomingSale.document.saleHeader.total * -1;
    newNegativeSale.document.saleHeader.seatCnt = incomingSale.document.saleHeader.seatCnt;
    newNegativeSale.document.saleHeader.toGoSeatCnt = incomingSale.document.saleHeader.toGoSeatCnt;
    newNegativeSale.document.saleHeader.customerCount = incomingSale.document.saleHeader.seatCnt ?? 1;

    final List<int> negativeTaxTotals = <int>[];
    final List<int> negativeDualPricingTaxTotals = <int>[];
    for (final int tax in incomingSale.document.saleHeader.taxTotals) {
      negativeTaxTotals.add(tax * -1);
    }
    for (final int cdTax in incomingSale.document.saleHeader.cashTaxTotals) {
      negativeDualPricingTaxTotals.add(cdTax * -1);
    }
    newNegativeSale.document.saleHeader.taxTotals = negativeTaxTotals;
    newNegativeSale.document.saleHeader.cashTaxTotals = negativeDualPricingTaxTotals;

    Map<String, dynamic> jsonSale;
    Sale cloneSale = Sale.empty();
    jsonSale = json.decode(json.encode(incomingSale)) as Map<String, dynamic>;
    cloneSale = Sale.fromJson(jsonSale);

    for (final SaleRow saleRow in cloneSale.document.saleRows) {
      final SaleRow negativeSaleRow = SaleRow(
        item: saleRow.item,
        upc: saleRow.upc,
        receiptDescription: saleRow.receiptDescription,
        department: saleRow.department,
        isWeightedItem: saleRow.isWeightedItem,
        transactionFlags: saleRow.transactionFlags,
        flags: saleRow.flags,
        index: saleRow.index,
        hasChildren: saleRow.hasChildren,
        isVisible: saleRow.isVisible,
        itemPricing: saleRow.itemPricing,
        origTaxFlags: saleRow.origTaxFlags,
        taxFlags: saleRow.taxFlags,
        seatNumber: saleRow.seatNumber,
        employee: saleRow.employee,
        voidedEmployee: saleRow.voidedEmployee,
        voidedReason: saleRow.voidedReason,
        voidDtTm: saleRow.voidDtTm,
        sendDtTm: saleRow.sendDtTm,
        soldDtTm: saleRow.soldDtTm,
        UOM: saleRow.UOM,
        weight: saleRow.weight,
        parent: saleRow.parent,
        qty: saleRow.qty,
        grossPrice: saleRow.grossPrice * -1,
        cashGrossPrice: saleRow.cashGrossPrice * -1,
        actualPrice: saleRow.actualPrice * -1,
        cashPrice: saleRow.cashPrice * -1,
        creditPrice: saleRow.creditPrice * -1,
        basePrice: saleRow.basePrice * -1,
        cashBasePrice: saleRow.cashBasePrice * -1,
        originalPrice: saleRow.originalPrice * -1,
        cashOriginalPrice: saleRow.cashOriginalPrice * -1,
        VATAmount: saleRow.VATAmount,
        selected: saleRow.selected,
        extData: saleRow.extData,
      );

      negativeSaleRow.flags.add(SaleRowFlags.REFUNDED.index);

      newNegativeSale.document.saleRows.add(negativeSaleRow);
    }

    // Check if the sale was dual pricing, if so use the lesser dual pricing total for refund
    int refundAmount = 0;

    if (incomingSale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
      refundAmount = newNegativeSale.document.saleHeader.cashTotal;
    } else {
      refundAmount = newNegativeSale.document.saleHeader.total;
    }

    // Add the tip total into the refund amount (it's in the negative so subtract here)
    // THIS WAS COMMENTED OUT BY COLE FOR NATILI'S SHOULD NOT DEFAULT REFUND THE TIP AS WELL
    // int tipTotal = 0;
    // for (final SaleTender tender in incomingSale.document.saleHeader.tenders) {
    //   tipTotal += tender.tipAmount ?? 0;
    // }
    // refundAmount -= tipTotal;

    if (refundAmount != 0) {
      if (refundMedia == PaymentMediaType.Cash) {
        // Add tender to currentTenders
        newNegativeSale.document.saleHeader.tenders.add(
          SaleTender(
            amount: refundAmount,
            media: refundMedia.index,
            saleTenderFlags: <int>[SaleTenderFlags.REFUNDED.index],
          ),
        );
      } else {
        processing.value = true;

        final Either<ServiceError, SystemDeviceJsonRecordTerminal> getSystemDeviceTerminalResult =
            await _configService.getSystemDeviceTerminalByIndex(
          index: _identityService.terminalNumber,
        );

        final Option<SystemDeviceJsonRecordTerminal> systemDeviceTerminal = getSystemDeviceTerminalResult.fold(
          (ServiceError error) {
            _notificationService.error(error.message);
            processing.value = false;

            return none();
          },
          (SystemDeviceJsonRecordTerminal systemDeviceTerminal) => some(systemDeviceTerminal),
        );

        if (systemDeviceTerminal.isNone()) {
          return _notificationService.error("Unable to get connection info!");
        }

        // Added this in here because debit refunds aren't possible

        final Either<ErrorResponse, CardTransactionData> result = await _paymentService.refund(
          amount: refundAmount.abs(),
          tenderType: PaymentMediaType.Credit,
        );

        if (result.isLeft()) {
          final Option<ErrorResponse> errorOpt = result.getLeft();
          final ErrorResponse error = errorOpt.getOrElse(() => ErrorResponse(message: "Unknown"));
          _logger.shout("Error running refund: ${error.message}");
          await showErrorDialog(error: error.message);
          processing.value = false;
          return;
        }

        final Option<CardTransactionData> responseOpt = result.getRight();
        final CardTransactionData response = responseOpt.getOrElse(
          () => CardTransactionData(),
        );

        // TODO: are these variables really ~necessary~? 👀
        // These could potentially become options maybe 🤷

        // On success add a tender to the list
        newNegativeSale.document.saleHeader.tenders.add(
          SaleTender(
            amount: refundAmount,
            media: refundMedia.index,
            cardTransactionData: response,
            saleTenderFlags: <int>[SaleTenderFlags.REFUNDED.index],
          ),
        );

        _notificationService.success("Refund issued Successfully!");
        numPadController.text.value = none();

        processing.value = false;
      }
    }

    try {
      newNegativeSale.document.saleHeader.saleFlags.addAll(<int>[
        SaleFlags.COMPLETED.index,
        SaleFlags.REFUNDED.index,
      ]);

      if (incomingSale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
        newNegativeSale.document.saleHeader.saleFlags.add(SaleFlags.DUAL_PRICING.index);
      }
      newNegativeSale.document.saleHeader.currentCashier = _identityService.currentCashier;
      newNegativeSale.end_at = DateTime.now().toUtc();
      newNegativeSale.document.saleHeader.settleEmployeeNumber = _identityService.currentEmployee.id ?? 0;
      newNegativeSale.document.saleHeader.settleTerminalNumber = _identityService.terminalNumber;
      if (refundMedia == PaymentMediaType.Cash) {
        newNegativeSale.document.saleHeader.cashDrawer = merchantDoc.multiCashDrawer ? _identityService.employeeDrawer?.idx : 0;
      }

      _saleController.currentSale.refresh();

      newNegativeSale.document.saleHeader.originalSale = incomingSale.sale;

      final Either<ServiceError, Sale> newNegativeSaleUpsertResult = await _saleService.upsert(
        sale: newNegativeSale,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );

      Sale newSaleRes = Sale.empty();

      newNegativeSaleUpsertResult.fold((ServiceError l) => _notificationService.error("Error saving refunded sale!"), (Sale newSale) async {
        newSaleRes = newSale;
        // Always print merchant receipt
        final PrintJob merchantPrintJob = PrinterInterface.buildReceipt(
          currentEmployee: _identityService.currentEmployee,
          sale: newSale,
          saleName:
              refundAmount == 0 ? "Exchange" : "${PaymentMediaType.values[newNegativeSale.document.saleHeader.tenders[0].media!].string} Refund",
          customerCopy: false,
          refundReceiptSignatureLine: merchantDoc.refundReceiptSignatureLine,
          receiptHeader: merchantDoc.receiptHeader,
          taxList: _configService.salesTaxList,
          isMerchantDualPricing: merchantDoc.dualPricing,
          currentUser: _identityService.currentEmployee,
          terminalDescs: _identityService.terminalDescs,
          legacyGiftName: merchantDoc.legacyGiftName,
          showCommentRows: merchantDoc.commentRowsOnReceipt,
          showSaleDesc: merchantDoc.saleDescOnReceipt,
          showSeats: _saleController.getProperSection().trackBySeat,
          condensedAuth: merchantDoc.condensedAuthSlip,
        );

        final List<Future<PrintJobReturn>> printResults = <Future<PrintJobReturn>>[];

        printResults.add(
          _printerService.printReceipt(
            printJob: merchantPrintJob,
            remotePrintIdx: _identityService.remoteTermIdx,
          ),
        );

        // Always print customer receipt
        final PrintJob customerPrintJob = PrinterInterface.buildReceipt(
          currentEmployee: _identityService.currentEmployee,
          sale: newSale,
          saleName:
              refundAmount == 0 ? "Exchange" : "${PaymentMediaType.values[newNegativeSale.document.saleHeader.tenders[0].media!].string} Refund",
          receiptHeader: merchantDoc.receiptHeader,
          taxList: _configService.salesTaxList,
          isMerchantDualPricing: merchantDoc.dualPricing,
          currentUser: _identityService.currentEmployee,
          terminalDescs: _identityService.terminalDescs,
          legacyGiftName: merchantDoc.legacyGiftName,
          showCommentRows: merchantDoc.commentRowsOnReceipt,
          showSaleDesc: merchantDoc.saleDescOnReceipt,
          showSeats: _saleController.getProperSection().trackBySeat,
          condensedAuth: merchantDoc.condensedAuthSlip,
        );

        printResults.add(
          _printerService.printReceipt(
            printJob: customerPrintJob,
            remotePrintIdx: _identityService.remoteTermIdx,
          ),
        );

        final List<PrintJobReturn> awaitedList = await Future.wait(printResults);

        for (final PrintJobReturn op in awaitedList) {
          final List<ServiceError> errs = op.errors ?? <ServiceError>[];

          if (errs.isNotEmpty) {
            _notificationService.error(errs.first.message);
          }
        }
      });

      if (refundMedia == PaymentMediaType.Cash) {
        // Open cash drawer if the refund is in cash
        await _registerController.openCashDrawer();
        await showTransactionDialog(
          refund: true,
          refundAmountString: Helpers.formatCurrency(refundAmount.abs()),
        );
      }

      // Update the original sale to show it has been refunded
      incomingSale.document.saleHeader.saleFlags.add(SaleFlags.NONREFUNDABLE.index);

      final Either<ServiceError, Sale> saleUpsertResult = await _saleService.upsert(
        sale: incomingSale,
        employee: _identityService.currentEmployee,
        terminalNumber: _identityService.terminalNumber,
      );

      saleUpsertResult.fold(
        (ServiceError error) => _notificationService.error(error.message),
        (_) => null,
      );

      if (saleUpsertResult.isLeft()) return;

      _notificationService.success("Refund Complete!");

      _activityService.insertActivity(
        activity: Activity(
          activity: ActivityFlags.REFUNDED_SALE.index,
          emp_id: _identityService.currentEmployee.id,
          term_num: _identityService.terminalNumber,
          sale_num: refundingSale.getOrElse(() => Sale.empty()).sale_number,
          str_data: "Refunded sale #${refundingSale.getOrElse(() => Sale.empty()).sale_number}. Resulting negative sale: #${newSaleRes.sale_number}",
          short_data: newSaleRes.sale_number,
        ),
      );
    } catch (err) {
      // Show the error and kick back into finish result flow
      processing.value = false;

      await showErrorDialog(error: err.toString());
    }

    if (refundAmount != 0) {
      _notificationService.success("Refund Complete!");
    } else {
      _notificationService.success("Exchange Complete!");
    }

    refundDialog.value = false;
    numPadController.text.value = none();
    processingResultText.value = "";

    if (reopen) {
      makeOpenCopy(sale: incomingSale);
    }

    Get.back();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<bool> showTransactionDialog({
    String changeDueString = "",
    bool giftCard = false,
    bool refund = false,
    String refundAmountString = "",
    String remainingGiftBalance = "",
  }) async {
    await Get.dialog<bool?>(
      ConfirmationDialog(
        confirmColor: R2Colors.primary500,
        declineColor: R2Colors.negativeRed,
        confirmText: refund ? "Confirm" : "Print",
        declineText: refund ? null : "Don't Print",
        title: const Text(
          "Transaction Complete",
          style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
        ),
        content: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: Text(
                "Refund Amount: \$$refundAmountString",
                style: const TextStyle(
                  fontSize: 34,
                ),
              ),
            ),
          ],
        ),
        noOptions: true,
      ),
    );
    return true;
  }

  ///
  ///
  ///
  ///
  ///
  /// Shows the error dialog.
  Future<void> showErrorDialog({String error = "Something went wrong!"}) async {
    String displayError = error;

    // Make connection error friendlier
    if (error.contains("SocketException")) {
      displayError = "Unable to connect to payment device";
    }

    await Get.dialog(
      ConfirmationDialog(
        confirmColor: R2Colors.primary500,
        declineColor: R2Colors.negativeRed,
        title: const Text(
          "Transaction Error!",
          style: TextStyle(fontWeight: FontWeight.w300, fontSize: 18),
        ),
        content: Column(
          children: <Widget>[
            Lottie.asset(
              "lib/assets/lottie/failed.json",
              frameRate: FrameRate.max,
              height: 300,
            ),
            Text(
              displayError,
              // currentError.value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
          ],
        ),
        noOptions: true,
      ),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> confirmPromisedTime() async {
    final int milliseconds = timeIntsToMilliseconds();

    final Sale? currSale = _saleController.currentSale.value.match((Sale t) => t, () => null);

    if (currSale == null) {
      _notificationService.error("No active sale to update");
      return;
    }

    currSale.document.saleHeader.promisedTime = milliseconds;

    final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
      sale: currSale,
      employee: _identityService.currentEmployee,
      terminalNumber: _identityService.terminalNumber,
    );

    upsertRes.match(
      (ServiceError l) => _notificationService.error(l.message),
      (Sale r) {
        _notificationService.success("Promised time updated!");
        _saleController.currentSale.refresh();
      },
    );
  }

  int timeIntsToMilliseconds() {
    int hour = promiseHourController.value;
    if (promisePM.value) hour += 12;
    final int minutes = (hour * 60) + promiseMinuteController.value;
    return Duration(minutes: minutes).inMilliseconds;
  }

  void millisecondsToTimeInts(int milliseconds) {
    final int minutesFromMS = Duration(milliseconds: milliseconds).inMinutes;
    final int minutes = minutesFromMS % 60;
    int hour = minutesFromMS ~/ 60;
    promisePM.value = hour > 11;
    if (hour > 12) {
      hour -= 12;
    }
    promiseMinuteController.value = minutes;
    promiseHourController.value = hour;
    currentPromisedTime = "$hour:${minutes < 10 ? "0" : ""}$minutes ${promisePM.value ? "PM" : "AM"}";
  }

  void expandDrawer() {
    _registerController.expandDrawer();
  }

  void shrinkDrawer() {
    _registerController.shrinkDrawer();
  }

  Future<void> getReasonCodes() async {
    try {
      final Either<ServiceError, JsonRecordReasonCode> reasonResp = await _reportService.getReasonCodeRecord();
      reasonResp.fold(
        (ServiceError l) {
          throw l;
        },
        (JsonRecordReasonCode r) {
          reasonCodeRecord = r;
          if (reasonCodeRecord.document.paidOut.isNotEmpty) {
            reasonCodeRecord.document.paidOut.sort();
          }
        },
      );
    } catch (e, stack) {
      _logger.shout('Error getting paid out reason codes', e, stack);
      _notificationService.error('Error getting paid out reason codes');
    }
  }
}
