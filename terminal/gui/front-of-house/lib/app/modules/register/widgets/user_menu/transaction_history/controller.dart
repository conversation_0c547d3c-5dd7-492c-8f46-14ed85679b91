// ignore_for_file: always_specify_types

import 'dart:async';

import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/card_data.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/payment.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/global_widgets/widget/confirmation_dialog.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/global_widgets/widget/thin_bottom_sheet.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/dialogs/tip_adjust/dialog.dart';
import 'package:desktop/app/modules/register/dialogs/transaction_history_filters/dialog.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
import 'package:lottie/lottie.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

final Logger _logger = Logger('TransactionHistoryController');

enum TransactionHistoryFocus { SEARCH_SALE, RECALL_SALE }

const int REFUND_PAGE_SIZE = 15;

class TransactionHistoryController extends GetxController {
  final ActivityService _activityService = Get.find();
  final PaymentService _paymentService = Get.find();
  final GraphqlService _graphqlService = Get.find();
  final CustomerService _customerService = Get.find();
  final IdentityService _identityService = Get.find();
  final NotificationService _notificationService = Get.find();
  final ConfigService _configService = Get.find();
  final SaleService _saleService = Get.find();
  final PrinterService _printerService = Get.find();

  final SaleController _saleController = Get.find();
  final UserMenuController _userMenuController = Get.find();
  final RegisterController _registerController = Get.find();

  final RxList<Sale> refundableSales = <Sale>[].obs;

  final Rx<Option<Sale>> focusedTransactionHistorySale = none<Sale>().obs;

  final RxBool processing = false.obs;

  final RxBool onlineSalesFilter = false.obs;

  final Rx<TransactionHistoryFocus> transactionHistoryFocus = TransactionHistoryFocus.SEARCH_SALE.obs;

  final Rx<DateTime> refundStartDate = DateTime(
    DateTime.now().subtract(const Duration(days: 4)).year,
    DateTime.now().subtract(const Duration(days: 4)).month,
    DateTime.now().subtract(const Duration(days: 4)).day,
  ).obs;

  final Rx<DateTime> refundEndDate = DateTime.now().add(const Duration(days: 1)).obs;

  final Rx<Option<String>> refundLaneNumber = none<String>().obs;
  final Rx<Option<String>> refundSaleNumber = none<String>().obs;
  final Rx<Option<String>> refundCardNumber = none<String>().obs;
  final Rx<Option<String>> refundCustomerFirstName = none<String>().obs;
  final Rx<Option<String>> refundCustomerLastName = none<String>().obs;
  final Rx<Option<String>> refundCustomerPhone = none<String>().obs;
  final Rx<Option<String>> refundCustomerAddress = none<String>().obs;
  final Rx<Option<String>> refundCustomerCompany = none<String>().obs;

  final ScrollController refundSaleScrollController = ScrollController();
  final ScrollController filterChipScrollController = ScrollController();

  Map<String, Customer> refundableSalesCustomers = <String, Customer>{};

  int refundCurrentPage = 0;
  bool refundIsLastPage = false;
  final RxBool filteredSalesIsFetching = false.obs;
  final RxBool refundIsLoading = false.obs;

  String saleName = "Sale";

  ///
  ///
  ///
  ///
  ///
  ///
  @override
  Future<void> onInit() async {
    if (_saleController.autoRefund.value) {
      // currentSettingScreen.value = RegisterMenuOptions.TRANSACTION_HISTORY;
      Future<void>.delayed(Duration.zero, () async {
        refundLaneNumber.value = some(_saleController.autoRefundBatch.value);
        refundSaleNumber.value = some(_saleController.autoRefundNumber.value);
        // await getRefundSale();
        _saleController.autoRefund.value = false;
        _saleController.autoRefundNumber.value = "";
        _saleController.autoRefundBatch.value = "";
      });
    }

    await loadSales();

    refundSaleScrollController.addListener(() async {
      if ((refundSaleScrollController.position.pixels > (refundSaleScrollController.position.maxScrollExtent * .8)) &&
          !filteredSalesIsFetching.value &&
          !refundIsLoading.value) {
        await loadSales();
      }
    });

    saleName = _saleController.saleName.value;

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> loadSales() async {
    if (refundCurrentPage == 0) resetSales();
    if (refundIsLastPage) return;
    if (filteredSalesIsFetching.value) return;

    final Either<ServiceError, List<Sale>> saleResult = await getFilteredSales(
      refundSaleNumber.value,
      refundLaneNumber.value,
      refundStartDate.value,
      refundEndDate.value,
      refundCardNumber.value,
      refundCustomerFirstName.value,
      refundCustomerLastName.value,
      refundCustomerPhone.value,
      refundCustomerAddress.value,
      refundCustomerCompany.value,
      _saleController.getProperSection().salesByEmployee ? _identityService.currentEmployee.id : null,
      onlineSalesFilter.value,
    );

    saleResult.fold((ServiceError l) => _notificationService.error(l.message), (List<Sale> r) async {
      final List<String> customerIds = <String>[];

      for (final Sale s in r) {
        if ((s.document.saleHeader.customer ?? "") != "") {
          customerIds.add(s.document.saleHeader.customer!);
        }
      }

      final Either<ServiceError, List<Customer>> customerResult = await _customerService.getCustomersByList(customerIds);

      customerResult.match(
        (ServiceError l) => _notificationService.error(l.message),
        (List<Customer> r) {
          for (final Customer c in r) {
            refundableSalesCustomers[c.customer] = c;
          }
        },
      );

      refundableSales.value += r;
      if (r.length < REFUND_PAGE_SIZE) refundIsLastPage = true;

      refundCurrentPage++;
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void resetSales() {
    refundableSales.value = <Sale>[];
    refundableSalesCustomers = <String, Customer>{};
    refundCurrentPage = 0;
    refundIsLastPage = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<Either<ServiceError, List<Sale>>> getFilteredSales(
    Option<String> saleNumber,
    Option<String> laneNumber,
    DateTime startDate,
    DateTime endDate,
    Option<String> cardNumber,
    Option<String> customerName,
    Option<String> customerName2,
    Option<String> customerPhone,
    Option<String> customerAddress,
    Option<String> customerCompany,
    int? currentEmployee,
    bool onlineSales,
  ) async {
    filteredSalesIsFetching.value = true;
    final StringBuffer queryVarStringBuffer = StringBuffer();
    final List<String> queryVarStrings = <String>[];

    final StringBuffer queryAndStringBuffer = StringBuffer();
    final List<String> queryAndStrings = <String>["{end_at: {_is_null: false}}"];

    final Map<String, dynamic> queryVariables = {};

    customerName.match(
      (String custName) {
        queryVarStrings.add("\$customer_name: String");
        queryAndStrings.add("{document: {_cast: {String: {_ilike: \$customer_name}}}}");
        queryVariables["customer_name"] = '%"firstName":%%$custName%';
      },
      () => null,
    );

    customerName2.match(
      (String custName2) {
        queryVarStrings.add("\$customer_name2: String");
        queryAndStrings.add("{document: {_cast: {String: {_ilike: \$customer_name2}}}}");
        queryVariables["customer_name2"] = '%"lastName":%%$custName2%';
      },
      () => null,
    );

    customerPhone.match(
      (String phone) {
        queryVarStrings.add("\$customer_phone: String");
        queryAndStrings.add("{document: {_cast: {String: {_ilike: \$customer_phone}}}}");
        queryVariables["customer_phone"] = '%"phone_":%%$phone%';
      },
      () => null,
    );

    customerAddress.match(
      (String address) {
        queryVarStrings.add("\$customer_address: String");
        queryAndStrings.add("{document: {_cast: {String: {_ilike: \$customer_address}}}}");
        queryVariables["customer_address"] = '%"address":%%$address%';
      },
      () => null,
    );

    customerCompany.match(
      (String company) {
        queryVarStrings.add("\$customer_company: String");
        queryAndStrings.add("{document: {_cast: {String: {_ilike: \$customer_company}}}}");
        queryVariables["customer_company"] = '%"company":%%$company%';
      },
      () => null,
    );

    /// if searching by customer info
    if (queryVarStrings.isNotEmpty) {
      queryVarStringBuffer.writeAll(queryVarStrings, ",");
      queryAndStringBuffer.writeAll(queryAndStrings, ",");

      final String customerString = '''
      query GET_FILTERED_CUSTOMERS($queryVarStringBuffer) {
        customer(where: {_and: [$queryAndStringBuffer]}, order_by: {updated_at: desc}) {
          created_at
          created_by
          customer
          updated_by
          search_customer_full_name
          document
          updated_at
        }
      }
  ''';

      List<Customer> incomingCustomers = <Customer>[];

      try {
        final QueryResult<Object?> customerResult = await _graphqlService.client.query(
          QueryOptions<Object?>(
            document: g.parseString(customerString),
            fetchPolicy: FetchPolicy.noCache,
            variables: queryVariables,
          ),
        );
        incomingCustomers =
            (customerResult.data!['customer'] as List<dynamic>).map((dynamic cust) => Customer.fromJson(cust as Map<String, dynamic>)).toList();
        filteredSalesIsFetching.value = false;
      } catch (err, stack) {
        _logger.severe(
          "Error fetching customers",
          err,
          stack,
        );
      }

      queryVarStringBuffer.clear();
      queryAndStringBuffer.clear();
      queryVarStrings.clear();
      queryAndStrings.clear();
      queryVariables.clear();

      if (incomingCustomers.isNotEmpty) {
        final List<String> customerList =
            incomingCustomers.map((Customer c) => '{document: {_cast: {String: {_ilike: "%${c.customer}%"}}}}').toList();

        queryAndStrings.add('{_or: [${customerList.join(", ")}]}');
      }
    }

    queryVariables["limit"] = REFUND_PAGE_SIZE;
    queryVariables["offset"] = refundCurrentPage * REFUND_PAGE_SIZE;
    queryVariables["start_date"] = startDate.toUtc().toString();
    queryVariables["end_date"] = endDate.toUtc().toString();

    // Default filter criteria
    queryVarStrings.addAll([
      "\$limit: Int",
      "\$offset: Int",
      "\$start_date: timestamptz",
      "\$end_date: timestamptz",
    ]);

    queryAndStrings.add("{end_at: {_gte: \$start_date, _lte: \$end_date}}");

    saleNumber.match(
      (String saleNum) {
        queryVarStrings.add("\$sale_number: Int");
        queryAndStrings.add("{sale_number: {_eq: \$sale_number}}");
        queryVariables["sale_number"] = int.parse(saleNum);
      },
      () => null,
    );

    // If showing only online sales, filter by created_by
    if (onlineSales) queryAndStrings.add('{created_by: {_eq: "${Constants.onlineOrderEmployee.employee}"}}');

    cardNumber.match(
      (String cardNum) {
        queryVarStrings.add("\$card_number_string: String");
        queryAndStrings.add(
          "{document: {_cast: {String: {_ilike: \$card_number_string}}}}",
        );
        queryVariables["card_number_string"] = '%"bogusAccountNum":%%$cardNum%';
      },
      () => null,
    );

    laneNumber.match(
      (String laneNum) {
        queryVarStrings.add("\$lane_number_string: String");
        queryAndStrings.add(
          "{document: {_cast: {String: {_ilike: \$lane_number_string}}}}",
        );
        queryVariables["lane_number_string"] = '%"settleTerminalNumber": $laneNum%';
      },
      () => null,
    );

    if (currentEmployee != null) {
      queryVarStrings.add("\$current_employee1: String");
      queryVarStrings.add("\$current_employee2: String");
      queryAndStrings
          .add("{_or: [{document: {_cast: {String: {_ilike: \$current_employee1}}}}, {document: {_cast: {String: {_ilike: \$current_employee2}}}}]}");
      queryVariables["current_employee1"] = '%"currentEmployeeNumber": $currentEmployee,%';
      queryVariables["current_employee2"] = '%"currentEmployeeNumber": $currentEmployee}%';
    }

    // queryAndStrings.addAll(<String>[
    //   "{ end_at: {_is_null: false}}",
    //   "{_or:[{document: {_contains: \$refundedHeader}},{document: {_contains: \$nonRefundableHeader}},{document: {_contains: \$paidOutHeader}}, {document: {_contains: \$completeHeader}}, {document: {_contains: \$canceledHeader}}, {document: {_contains: \$voidedHeader}}]}",
    // ]);

    queryVarStringBuffer.writeAll(queryVarStrings, ",");
    queryAndStringBuffer.writeAll(queryAndStrings, ",");

    final String queryString = '''
      query GET_FILTERED_SALES($queryVarStringBuffer) {
        sale(where: {_and: [$queryAndStringBuffer]}, limit: \$limit, offset: \$offset, order_by: {end_at: desc_nulls_last}) {
          end_at
          sale_number
          sale
          created_at
          created_by
          document
          suspended
          updated_at
          updated_by
        }
      }
  ''';

    try {
      final QueryResult<Object?> saleResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(queryString),
          fetchPolicy: FetchPolicy.noCache,
          variables: queryVariables,
        ),
      );
      if (saleResult.hasException) {
        throw saleResult.exception.toString();
      }

      final List<Sale> incomingSales = (saleResult.data!['sale'] as List<dynamic>).map((dynamic sale) {
        return Sale.fromJson(sale as Map<String, dynamic>);
      }).toList();
      filteredSalesIsFetching.value = false;
      return Right(incomingSales);
    } catch (err, stack) {
      _logger.severe(
        "Error fetching sales",
        err,
        stack,
      );
      filteredSalesIsFetching.value = false;
      return Left<ServiceError, List<Sale>>(
        ServiceError("Failed to get sales!"),
      );
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> openRefundFilterDialog() async {
    // Initialize and load up the controllers for the dialog

    final Rx<TextEditingController> refundSaleNumController = TextEditingController(
      text: refundSaleNumber.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundLaneNumController = TextEditingController(
      text: refundLaneNumber.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCardNumController = TextEditingController(
      text: refundCardNumber.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCustomerNameController = TextEditingController(
      text: refundCustomerFirstName.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCustomerName2Controller = TextEditingController(
      text: refundCustomerLastName.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCustomerPhoneController = TextEditingController(
      text: refundCustomerPhone.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCustomerAddressController = TextEditingController(
      text: refundCustomerAddress.value.match((String t) => t, () => null),
    ).obs;
    final Rx<TextEditingController> refundCustomerCompanyController = TextEditingController(
      text: refundCustomerCompany.value.match((String t) => t, () => null),
    ).obs;

    final Rx<DateRangePickerController> dateRangeController = DateRangePickerController().obs;

    dateRangeController.value.selectedRange = PickerDateRange(
      refundStartDate.value,
      refundEndDate.value.subtract(const Duration(days: 1)),
    );

    await Get.dialog<bool?>(
      TransactionHistoryFilters(
        dateRangeController: dateRangeController,
        refundSaleNumController: refundSaleNumController,
        refundLaneNumController: refundLaneNumController,
        refundCardNumController: refundCardNumController,
        refundCustomerNameController: refundCustomerNameController,
        refundCustomerName2Controller: refundCustomerName2Controller,
        refundCustomerPhoneController: refundCustomerPhoneController,
        refundCustomerAddressController: refundCustomerAddressController,
        refundCustomerCompanyController: refundCustomerCompanyController,
      ),
    );
  }

  ///
  ///
  ///
  ///
  ///
  /// Tries to cancel the currently running transaction
  Future<void> cancelTransaction() async {
    processing.value = false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> refundCheckDialog({
    required Option<Sale> sale,
    bool reopen = false,
  }) async {
    final List<int> coreMediaTypes = [PaymentMediaType.Cash.index, PaymentMediaType.Credit.index, PaymentMediaType.Gift.index];

    if (!sale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      return _notificationService.error("This is an incomplete sale!");
    }

    if (_configService.merchantConfig.document.defaultRefundMedia) {
      final List<int> tenderSet = sale.getOrElse(() => Sale.empty()).document.saleHeader.tenders.map((SaleTender t) => t.media ?? 0).toSet().toList();
      if (tenderSet.length == 1 && coreMediaTypes.contains(tenderSet.first)) {
        await _userMenuController.runRefund(
          refundingSale: focusedTransactionHistorySale.value,
          refundMedia: PaymentMediaType.values[tenderSet.first],
          reopen: reopen,
        );
        return;
      }
    }

    await Get.defaultDialog(
      title: "Refund Media Type",
      content: Padding(
        padding: const EdgeInsets.all(30.0),
        child: Row(
          children: <Widget>[
            DialogButton(
              buttonType: EDialogButtonType.CONFIRM,
              buttonText: "Cash",
              onTapped: () {
                Get.back(result: PaymentMediaType.Cash);
              },
            ),
            const SizedBox(width: 20),
            DialogButton(
              buttonType: EDialogButtonType.CONFIRM,
              buttonText: "Credit",
              onTapped: () {
                Get.back(result: PaymentMediaType.Credit);
              },
            ),
            if (_configService.merchantConfig.document.giftcards)
              Padding(
                padding: const EdgeInsets.only(left: 20),
                child: DialogButton(
                  buttonType: EDialogButtonType.CONFIRM,
                  buttonText: "Gift",
                  onTapped: () {
                    Get.back(result: PaymentMediaType.Gift);
                  },
                ),
              ),
          ],
        ),
      ),
    ).then((dynamic value) async {
      if (value is PaymentMediaType) {
        await _userMenuController.runRefund(
          refundingSale: focusedTransactionHistorySale.value,
          refundMedia: value,
          reopen: reopen,
        );
      }
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> voidCredit({
    required Option<Sale> voidingSale,
  }) async {
    await voidingSale.match(
      (Sale sale) async {
        if (sale.document.saleHeader.saleFlags.contains(SaleFlags.REFUNDED.index)) {
          return _notificationService.error("This sale has already been refunded!");
        }

        if (sale.document.saleHeader.saleFlags.contains(SaleFlags.NONREFUNDABLE.index)) {
          return _notificationService.error("This sale can not be refunded!");
        }

        if (sale.document.saleHeader.settleTerminalNumber == null) {
          return _notificationService.error("This sale has no settle terminal number!");
        }

        final List<SaleTender> notVoidedTenders =
            sale.document.saleHeader.tenders.where((SaleTender tender) => !tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index)).toList();

        if (notVoidedTenders.isEmpty) return _notificationService.error("All tenders have already been voided!");

        final bool voidableTenderCheck = notVoidedTenders.any(
          (SaleTender tender) => tender.media == PaymentMediaType.Credit.index,
        );

        final bool voidBlockingTenderCheck = notVoidedTenders.any(
          (SaleTender tender) => tender.media == PaymentMediaType.EBT.index || tender.media == PaymentMediaType.Gift.index,
        );

        final bool hasCashTender = notVoidedTenders.any(
          (SaleTender tender) => tender.media == PaymentMediaType.Cash.index,
        );

        final bool hasLegacyTender = notVoidedTenders.any(
          (SaleTender tender) => tender.media == PaymentMediaType.LegacyGift.index,
        );

        if (!voidableTenderCheck) {
          return _notificationService.error("This sale does not have a voidable credit tender!");
        }

        if (voidBlockingTenderCheck) {
          return _notificationService.error("Cannot void sale with EBT or Gift tender!");
        }

        // Check if the sale has a cash or legacy gift tender
        // If it does, we need to confirm that the cash or legacy gift tender is returned to the customer
        final String confirmTenderString = hasCashTender && hasLegacyTender
            ? "Cash and ${_configService.merchantConfig.document.legacyGiftName} tenders"
            : hasCashTender
                ? "Cash tender"
                : "Credit tender";

        final String confirmString = hasCashTender || hasLegacyTender ? "$confirmTenderString must be returned to customer. " : "";

        // Confirmation dialog
        final bool? confirm = await Get.dialog(
          ConfirmationDialog(
            title: const Text(
              "Voiding Sale",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: Text(
              "${confirmString}Are you sure?",
              style: TextStyle(color: R2Colors.negativeRed),
            ),
            confirmText: "Yes",
            declineText: "Cancel",
            onConfirm: () => Get.back(result: true),
            onDecline: () => Get.back(result: false),
          ),
        );

        if (confirm != true) return;

        unawaited(
          Get.dialog(
            ColoredBox(
              color: Colors.black.withOpacity(0.5),
              child: Lottie.asset(
                "packages/desktop/assets/lottie/loading-animation.json",
                frameRate: FrameRate.max,
                height: 150,
              ),
            ),
          ),
        );

        final List<SaleTender> creditTenders = notVoidedTenders.where((SaleTender tender) => tender.media == PaymentMediaType.Credit.index).toList();
        final List<SaleTender> noncreditTenders =
            notVoidedTenders.where((SaleTender tender) => tender.media != PaymentMediaType.Credit.index).toList();

        bool firstCredit = true;
        int voidFailCount = 0;
        for (final SaleTender tender in creditTenders) {
          bool failed = false;
          try {
            final String refID = tender.cardTransactionData!.refTxnID;
            if (tender.cardTransactionData?.entryMode == EntryMode.NMI.index) {
              final Either<ErrorResponse, NMIResponseData> resp = await _paymentService.nmiVoid(refTxnId: refID);
              failed = resp.fold(
                (ErrorResponse error) {
                  _logger.shout("Error while voiding online transaction ${error.message}");
                  return true;
                },
                (NMIResponseData success) {
                  _logger.info("Voided NMI online transaction: $success");
                  return false;
                },
              );
            } else {
              final Either<ErrorResponse, CardTransactionData> resp = await _paymentService.txnVoid(
                settledTerminalNumber: sale.document.saleHeader.settleTerminalNumber!,
                refTxnId: refID,
              );
              failed = resp.fold(
                (ErrorResponse error) {
                  _logger.shout("Error while voiding credit transaction ${error.message}");
                  return true;
                },
                (CardTransactionData success) {
                  _logger.info("Voided credit transaction: $success");
                  return false;
                },
              );
            }

            if (failed) {
              voidFailCount++;
              // If errored on first tender then break.
              if (firstCredit) break;
            } else {
              tender.saleTenderFlags.add(SaleTenderFlags.VOIDED.index);
            }

            firstCredit = false;
          } catch (e) {
            _logger.shout("Error while voiding credit transaction: $e");
          }
        }

        if (voidFailCount > 0) {
          // If errored on first tender or all tenders failed then return before sale is altered.
          if (firstCredit) {
            Get.back(); // Close the loading dialog
            return _notificationService.error("Error while voiding credit transaction${creditTenders.length > 1 ? "s" : ""}.");
          } else {
            // If errored on not first credit tender then show error message.
            // Message will be different if more than one tender errored.
            if (creditTenders.length > 1) {
              _notificationService.error("$voidFailCount credit tenders failed to void. Try again or refund these tenders with with negative sales.");
            } else {
              _notificationService.error("Credit tender failed to void. Try again or refund this tender with with a negative sale.");
            }
          }
        }

        for (final SaleTender tender in noncreditTenders) {
          tender.saleTenderFlags.add(SaleTenderFlags.VOIDED.index);
        }

        sale.document.saleHeader.saleFlags.add(SaleFlags.VOIDED.index);
        sale.document.saleHeader.saleFlags.remove(SaleFlags.COMPLETED.index);

        await _saleService.upsert(
          sale: sale,
          employee: _identityService.currentEmployee,
          terminalNumber: _identityService.terminalNumber,
        );

        focusedTransactionHistorySale.refresh();

        resetSales();
        await loadSales();

        Get.back();

        _notificationService.success("Sale voided!");

        if (hasCashTender) unawaited(_registerController.openCashDrawer());
      },
      () async => _notificationService.error("Error while voiding sale"),
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> transactionTipAdjust() async {
    await focusedTransactionHistorySale.value.match((Sale s) async {
      await _registerController
          .noSignOffBottomSheet(
        ThinBottomSheet(
          sideFlex: 1,
          child: TipAdjustDialog(
            transaction: s,
          ),
        ),
        isScrollControlled: true,
        ignoreSafeArea: true,
        isDismissible: false,
        enableDrag: false,
        backgroundColor: Colors.transparent,
        settings: const RouteSettings(
          name: "TIP ADJUST BOTTOM SHEET",
        ),
      )
          .then(
        (dynamic value) async {
          refundableSales.refresh();
          focusedTransactionHistorySale.refresh();
        },
      );
    }, () async {
      _notificationService.error("No ${_saleController.saleName} selected for tip adjust");
    });
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> reprintCheck({
    required Option<Sale> sale,
    int? copyOpts,
    bool authSlip = false,
  }) async {
    // copyOpts:
    // 1 - Print Customer and Merchant Copies
    // 2 - Print Merchant Only
    // 3 - Print Customer Only

    if (!sale.getOrElse(() => Sale.empty()).document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      return _notificationService.error("This is an incomplete sale!");
    }

    final Sale incomingSale = sale.match((Sale t) => t, () => Sale.empty());

    final List<Future<void>> printResults = <Future<void>>[];

    if (!authSlip) {
      // print customer copy
      if (copyOpts != 2) {
        printResults.add(
          _printerService.printSale(
            sale: incomingSale,
            showSeats: _saleController.getProperSection().trackBySeat,
            saleName: _saleController.saleName.value,
          ),
        );
      }
      // print merchant copy
      if (copyOpts != 3) {
        printResults.add(
          _printerService.printSale(
            sale: incomingSale,
            customerCopy: false,
            showSeats: _saleController.getProperSection().trackBySeat,
            saleName: _saleController.saleName.value,
          ),
        );
      }
    } else {
      final List<SaleTender> creditTenders =
          sale.getOrElse(() => Sale.empty()).document.saleHeader.tenders.where((SaleTender t) => t.media == PaymentMediaType.Credit.index).toList();

      for (final SaleTender t in creditTenders) {
        printResults.add(
          _printerService.printSale(
            sale: incomingSale,
            customerCopy: false,
            authSlip: authSlip,
            tenders: [t],
            showSeats: _saleController.getProperSection().trackBySeat,
            saleName: _saleController.saleName.value,
          ),
        );
      }
    }

    unawaited(
      _activityService.insertActivity(
        activity: Activity(
          activity: ActivityFlags.REPRINT_AUTH.index,
          created_at: DateTime.now(),
          emp_id: _identityService.currentEmployee.id,
          term_num: _identityService.terminalNumber,
          data1: printResults.length,
          sale_num: sale.getOrElse(() => Sale.empty()).sale_number,
          str_data:
              "${_identityService.currentEmployee.employee_full_name} reprinted ${printResults.length} auth slip(s) for sale ${sale.getOrElse(() => Sale.empty()).sale_number}",
        ),
      ),
    );

    await Future.wait(printResults);
  }
}
