import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/uom.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/modules/register/widgets/sale/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

PrinterService _printerService = Get.find<PrinterService>();

ItemGroup? lastNTGroup;
bool firstTO = true;

bool isFirstTakeout() {
  if (firstTO) {
    firstTO = false;
    if (lastNTGroup != null) lastNTGroup!.lastNonTakeout.value = true;
    return true;
  }
  return false;
}

ItemGroup setLastNT(ItemGroup group) {
  lastNTGroup = group;
  return group;
}

class RecallItems extends GetView<SaleController> {
  const RecallItems({
    required this.sale,
    this.isPrepBreakdown = false,
  });
  final Sale sale;
  // If component is used to display prep status/times
  final bool isPrepBreakdown;

  @override
  Widget build(BuildContext context) {
    final int seatCnt = (sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt;
    final List<List<SaleRow>> rowLists = controller.organizeBySeat(sale);
    return Column(
      children: <Widget>[
        for (int i = 0; i < seatCnt; i++)
          if (i < (sale.document.saleHeader.seatCnt ?? 1) && !sale.document.saleHeader.seatsSettled.contains(i + 1) && rowLists[i].isNotEmpty)
            setLastNT(
              ItemGroup(
                seat: i + 1,
                rowList: rowLists[i],
                sale: sale,
                isPrepBreakdown: isPrepBreakdown,
              ),
            )
          else if (i >= (sale.document.saleHeader.seatCnt ?? 1) &&
              !sale.document.saleHeader.seatsSettled.contains((sale.document.saleHeader.seatCnt ?? 1) - (i + 1)) &&
              rowLists[i].isNotEmpty)
            Column(
              children: <Widget>[
                if (isFirstTakeout())
                  Padding(
                    padding: const EdgeInsets.only(bottom: 2.0),
                    child: Container(width: double.infinity, height: 3, color: R2Colors.negativeRedDark),
                  ),
                ItemGroup(
                  seat: (sale.document.saleHeader.seatCnt ?? 1) - (i + 1),
                  rowList: rowLists[i],
                  sale: sale,
                  isPrepBreakdown: isPrepBreakdown,
                ),
              ],
            ),
      ],
    );
  }
}

class ItemGroup extends GetView<SaleController> {
  ItemGroup({
    required this.rowList,
    required this.seat,
    required this.sale,
    required this.isPrepBreakdown,
  });

  final List<SaleRow> rowList;
  final int seat;
  final Sale sale;
  final bool isPrepBreakdown;
  final RxBool lastNonTakeout = false.obs;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          if (controller.getProperSection().trackBySeat)
            Padding(
              padding: const EdgeInsets.only(bottom: 1),
              child: Column(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          "${seat < 0 ? "ToGo" : "Seat"} ${seat < 0 ? seat * -1 : seat}",
                          style: TextStyle(
                            fontSize: 18,
                            color: R2Colors.neutral900,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          Padding(
            padding: EdgeInsets.only(bottom: !lastNonTakeout.value ? 2 : 0),
            child: Column(
              children: <Widget>[
                for (int i = 0; i < rowList.length; i++)
                  RecallItem(
                    key: Key("${rowList[i].seatNumber}-${i + 1}"),
                    saleRow: rowList[i],
                    sale: sale,
                    isPrepBreakdown: isPrepBreakdown,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class RecallItem extends GetView<SaleController> {
  const RecallItem({
    required this.saleRow,
    required this.sale,
    required this.key,
    required this.isPrepBreakdown,
  });

  final SaleRow saleRow;
  final Sale sale;
  final bool isPrepBreakdown;
  @override
  // ignore: overridden_fields
  final Key key;

  @override
  Widget build(BuildContext context) {
    Color fontColor = R2Colors.neutral700;

    if (saleRow.flags.contains(SaleRowFlags.VOIDED.index)) {
      fontColor = R2Colors.neutral400;
    } else if (saleRow.parent < 0 && !saleRow.hasChildren && saleRow.basePrice < 0) {
      fontColor = R2Colors.negativeRed;
    }

    Widget weightSlot = Container();

    if (saleRow.flags.contains(SaleRowFlags.RANDOM_WEIGHT.index) || saleRow.isWeightedItem) {
      String uomText = "";
      String uomWeight = "";

      if (saleRow.UOM != null) {
        switch (UOM.values[saleRow.UOM!]) {
          case UOM.LB:
            uomText = UOM.LB.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(2);
            }
            break;
          case UOM.OZ:
            uomText = UOM.OZ.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(1);
            }
            break;
          case UOM.G:
            uomText = UOM.G.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(0);
            }
            break;
          case UOM.KG:
            uomText = UOM.KG.toDisplayString();
            if (saleRow.weight != null) {
              uomWeight = saleRow.weight!.toStringAsFixed(3);
            }
            break;
          case UOM.NA:
            // TODO: Handle this case.
            break;
        }
      }

      if (uomText != "") {
        weightSlot = Text(
          "gross ($uomWeight $uomText @ \$${Helpers.formatCurrency(saleRow.originalPrice.abs())}/$uomText)",
          style: TextStyle(
            color: R2Colors.neutral600,
            fontSize: 12,
          ),
        );
      }
    }

    int indent = 0;
    int displayPrice = saleRow.actualPrice;

    if ((saleRow.parent >= 0 || saleRow.hasChildren) && saleRow.isVisible) {
      final List<SaleRow> currentRows = sale.document.saleRows;
      final SaleRow parentRow = Helpers.getParentRow(saleRow, currentRows);
      final List<SaleRow> childRows = Helpers.getChildRows(parentRow, currentRows);

      for (final SaleRow childRow in childRows) {
        if (childRow.parent >= 0 && !childRow.flags.contains(SaleRowFlags.VOIDED.index)) {
          displayPrice += childRow.actualPrice;
        }
      }

      if (displayPrice < 0) {
        fontColor = R2Colors.negativeRed;
      }

      if (saleRow.parent >= 0) {
        indent = Helpers.getSaleRowIndent(saleRow: saleRow, rowList: currentRows);

        if (indent > 5) indent = 5;
      }

      // The display price, optionally adjusted by split data if there is any.
      final int newDisplayPrice = (displayPrice * (parentRow.splitData == null ? saleRow.qty : saleRow.qty / parentRow.splitData!.qty)).round();

      // If root sale row has split data, adjust the display price to account for the split.
      // Also subtract and kind of remainder as needed.
      if (parentRow.splitData != null && saleRow.flags.contains(SaleRowFlags.SPLIT_PARENT.index)) {
        final int remainderDisplay = (newDisplayPrice * parentRow.splitData!.qty) - (displayPrice * saleRow.qty);
        displayPrice = newDisplayPrice - remainderDisplay;
      } else {
        displayPrice = newDisplayPrice;
      }
    }

    return saleRow.parent >= 0
        ? Visibility(
            // In the prep breakdown, don't show anything but root sale rows and prep info.
            visible: !isPrepBreakdown && saleRow.isVisible,
            child: Row(
              children: <Widget>[
                Expanded(
                  child: DecoratedBox(
                    key: key,
                    decoration: BoxDecoration(
                      color: R2Colors.neutral100,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: <Widget>[
                          Container(width: (15 * indent).toDouble()),
                          if (saleRow.flags.contains(SaleRowFlags.COMMENT.index))
                            Text(
                              "! ",
                              style: TextStyle(
                                color: R2Colors.secondary500,
                              ),
                            ),
                          Expanded(
                            child: Text(
                              saleRow.receiptDescription,
                              style: TextStyle(
                                color: fontColor,
                                fontSize: 12,
                                decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                              ),
                            ),
                          ),
                          if (controller.showModPrices && saleRow.actualPrice != 0)
                            Padding(
                              padding: const EdgeInsets.only(
                                right: 2,
                              ),
                              child: Text(
                                "${saleRow.actualPrice > 0 ? "+" : "-"}\$${Helpers.formatCurrency(saleRow.actualPrice.abs())}",
                                textAlign: TextAlign.end,
                                style: TextStyle(
                                  fontFamily: "Roboto Mono",
                                  fontSize: saleRow.actualPrice > 9999 ? 10 : 12,
                                  fontWeight: saleRow.selected ? FontWeight.w600 : FontWeight.normal,
                                  color: saleRow.actualPrice > 0
                                      ? saleRow.selected
                                          ? R2Colors.white.withOpacity(0.5)
                                          : R2Colors.neutral400
                                      : R2Colors.negativeRedDisabled,
                                  decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        : Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Column(
              children: <Widget>[
                ColoredBox(
                  color: R2Colors.neutral100,
                  child: ListTile(
                    key: key,
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                    title: Column(
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            Expanded(
                              flex: 2,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: <Widget>[
                                  if (saleRow.flags.contains(SaleRowFlags.VOIDED.index))
                                    Text(
                                      "${saleRow.qty}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: fontColor,
                                        fontSize: saleRow.qty > 999
                                            ? 10
                                            : saleRow.qty > 99
                                                ? 11
                                                : 12,
                                        decoration: saleRow.flags.contains(
                                          SaleRowFlags.VOIDED.index,
                                        )
                                            ? TextDecoration.lineThrough
                                            : null,
                                      ),
                                    )
                                  else
                                    Text(
                                      "${saleRow.qty}",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: fontColor,
                                        fontSize: saleRow.qty > 999
                                            ? 10
                                            : saleRow.qty > 99
                                                ? 11
                                                : 12,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 11,
                              child: Text(
                                saleRow.receiptDescription + (saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? " **VOIDED**" : ""),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: saleRow.receiptDescription.length > 30
                                      ? 10
                                      : saleRow.receiptDescription.length > 20
                                          ? 12
                                          : 14,
                                  fontWeight: FontWeight.normal,
                                  color: fontColor,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 5,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  right: 2,
                                ),
                                child: Text(
                                  "${displayPrice < 0 ? "-" : ""}\$${Helpers.formatCurrency(displayPrice.abs())}",
                                  textAlign: TextAlign.end,
                                  style: TextStyle(
                                    fontFamily: "Roboto Mono",
                                    fontSize: displayPrice > 99999
                                        ? 10
                                        : displayPrice > 9999
                                            ? 12
                                            : 14,
                                    color: fontColor,
                                    decoration: saleRow.flags.contains(SaleRowFlags.VOIDED.index) ? TextDecoration.lineThrough : null,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: <Widget>[
                            weightSlot,
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                if (isPrepBreakdown) PrepBreakdown(saleRow),
              ],
            ),
          );
  }
}

class PrepBreakdown extends StatelessWidget {
  const PrepBreakdown(this.saleRow);
  final SaleRow saleRow;

  @override
  Widget build(BuildContext context) {
    // { Printer ID (Integer Index As String)  => Prep Time }
    final Map<String, DateTime?>? preppedAt = saleRow.preppedAt;
    // Bitwise integer representing which printers this saleRow was sent to.
    final int prep = saleRow.prep;
    final Map<String, DateTime?> prepMap = preppedAt ?? <String, DateTime?>{};
    // When an item is prepped all keys in preppedAt should be set. On error value is null.
    final bool needPrepped = preppedAt == null && prep > 0;

    if (needPrepped) {
      for (final PrepDevice device in _printerService.prepList) {
        if ((prep & (1 << device.idx)) == (1 << device.idx)) {
          prepMap[device.idx.toString()] = null; // Initialize with null if not prepped
        }
      }
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: prepMap.isEmpty
          ? <Widget>[
              Text(
                "No Prep Devices",
                style: TextStyle(color: R2Colors.secondary500),
              ),
            ]
          : prepMap.keys.map((String devIdx) {
              final PrepDevice device = _printerService.prepList.firstWhereOrNull(
                    (PrepDevice d) => d.idx.toString() == devIdx,
                  ) ??
                  PrepDevice.empty();

              final bool err = !needPrepped && prepMap[devIdx] == null;

              final DateFormat form = DateFormat("M/d/yy hh:mma");
              final String dateStr = prepMap[devIdx] == null ? (err ? 'Prep Error' : 'Not Prepped') : form.format(prepMap[devIdx]!.toLocal());

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: R2Colors.neutral200,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Expanded(
                        child: Center(child: Text(device.desc)),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: Center(
                          child: Text(
                            dateStr,
                            style: TextStyle(
                              color: prepMap[devIdx] == null
                                  ? err
                                      ? R2Colors.negativeRed
                                      : Colors.amber
                                  : R2Colors.successGreen,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
    );
  }
}
