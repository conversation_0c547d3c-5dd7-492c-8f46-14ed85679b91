import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/dialog_button_type.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/register_menu_options.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/global_widgets/radius_listener/widget.dart';
import 'package:desktop/app/global_widgets/widget/dialog_button.widget.dart';
import 'package:desktop/app/global_widgets/widget/thin_bottom_sheet.dart';
import 'package:desktop/app/modules/register/dialogs/sale_info/dialog.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/transaction_history/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/transaction_history/recall_items.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/widget.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';

const double bttnFontSize = 18;
const double bttnPaddingSize = 16;

// Voiding isn't restricted by voided flag. This allows for retrying after error.
final List<int> voidRestrictedFlags = Constants.restrictedFlags.where((int f) => f != SaleFlags.VOIDED.index).toList();

// ignore: must_be_immutable
class RegisterTransactionHistoryMenu extends GetView<TransactionHistoryController> {
  RegisterTransactionHistoryMenu({
    this.allowReprint,
    this.allowRefund,
    this.allowReopen,
    this.allowVoid,
    this.allowCopy,
    this.copyOpts,
    this.tipAdjust,
  });
  bool? allowReprint;
  bool? allowRefund;
  bool? allowVoid;
  bool? allowReopen;
  bool? allowCopy;
  int? copyOpts;
  bool? tipAdjust;

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final IdentityService _identityService = Get.find();
  final ConfigService _configService = Get.find();

  final UserMenuController _userMenuController = Get.find();

  @override
  Widget build(BuildContext context) {
    if (_identityService.employeeDrawer == null && _configService.merchantConfig.document.multiCashDrawer) {
      allowRefund = false;
      allowVoid = false;
      allowReopen = false;
    }
    return GetBuilder<TransactionHistoryController>(
      init: TransactionHistoryController(),
      builder: (TransactionHistoryController controller) {
        return Scaffold(
          key: _scaffoldKey,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Expanded(
                child: MenuBackButton(
                  title: "Transaction History",
                  onPressed: () {
                    _userMenuController.shrinkDrawer();
                    switch (controller.transactionHistoryFocus.value) {
                      case TransactionHistoryFocus.SEARCH_SALE:
                        _userMenuController.numPadController.text.value = none();
                        _userMenuController.currentSettingScreen.value = RegisterMenuOptions.ROOT;
                        _userMenuController.processingResultText.value = "";
                        _userMenuController.processingPromptWidget.value = Container();
                        _userMenuController.refundDialog.value = false;
                      case TransactionHistoryFocus.RECALL_SALE:
                        controller.focusedTransactionHistorySale.value = none();
                        controller.transactionHistoryFocus.value = TransactionHistoryFocus.SEARCH_SALE;
                    }
                  },
                ),
              ),
              Expanded(
                flex: 9,
                child: Obx(() {
                  switch (controller.processing.value) {
                    case true:
                      return Padding(
                        padding: const EdgeInsets.only(top: 50.0),
                        child: Column(
                          children: <Widget>[
                            const Text(
                              "Processing Transaction",
                              style: TextStyle(
                                fontSize: 15,
                              ),
                            ),
                            Lottie.asset(
                              "lib/assets/lottie/loading-animation.json",
                              frameRate: FrameRate.max,
                              height: 300,
                            ),
                            DialogButton(
                              buttonType: EDialogButtonType.CANCEL,
                              onTapped: () async {
                                await controller.cancelTransaction();
                              },
                            ),
                          ],
                        ),
                      );
                    case false:
                      return Obx(() {
                        switch (controller.transactionHistoryFocus.value) {
                          case TransactionHistoryFocus.SEARCH_SALE:
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              mainAxisSize: MainAxisSize.min,
                              children: <Widget>[
                                Row(
                                  children: <Widget>[
                                    const Spacer(),
                                    Expanded(
                                      flex: 10,
                                      child: RadiusListener(
                                        onTap: () async {
                                          await controller.openRefundFilterDialog();
                                        },
                                        child: TextButton(
                                          style: ButtonStyle(
                                            backgroundColor: MaterialStateProperty.all<Color?>(
                                              R2Colors.primary500,
                                            ),
                                          ),
                                          onPressed: () {},
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                              40,
                                              16.0,
                                              40,
                                              16.0,
                                            ),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: <Widget>[
                                                Text(
                                                  "Filters",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 22,
                                                  ),
                                                ),
                                                Padding(
                                                  padding: const EdgeInsets.only(
                                                    left: 8.0,
                                                  ),
                                                  child: FaIcon(
                                                    FontAwesomeIcons.solidFilter,
                                                    color: R2Colors.white,
                                                    size: 18,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                    left: 8.0,
                                    right: 8.0,
                                  ),
                                  child: SizedBox(
                                    height: 50,
                                    child: ListView(
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      controller: controller.filterChipScrollController,
                                      children: <Widget>[
                                        if (controller.refundSaleNumber.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundSaleNumber.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "${controller.saleName} Num.",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundLaneNumber.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundLaneNumber.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Terminal Num.",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCardNumber.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCardNumber.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Card Num.",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCustomerFirstName.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCustomerFirstName.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Customer First Name",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCustomerLastName.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCustomerLastName.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Customer Last Name",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCustomerPhone.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCustomerPhone.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Customer Phone",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCustomerAddress.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCustomerAddress.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Customer Address",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        if (controller.refundCustomerCompany.value.isSome())
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Chip(
                                              backgroundColor: R2Colors.primary500,
                                              deleteIcon: RadiusListener(
                                                onTap: () async {
                                                  controller.refundCustomerCompany.value = none();
                                                  controller.resetSales();
                                                  await controller.loadSales();
                                                },
                                                child: FaIcon(
                                                  FontAwesomeIcons.xmark,
                                                  color: R2Colors.white,
                                                  size: 24,
                                                ),
                                              ),
                                              onDeleted: () {},
                                              label: Padding(
                                                padding: const EdgeInsets.all(6.0),
                                                child: Text(
                                                  "Customer Company",
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (controller.refundableSales.isEmpty)
                                  if (controller.filteredSalesIsFetching.value)
                                    const Column(
                                      children: <Widget>[
                                        SizedBox(
                                          height: 50,
                                          width: 50,
                                          child: CircularProgressIndicator(),
                                        ),
                                      ],
                                    )
                                  else
                                    const Center(
                                      child: Text("No Sales Found"),
                                    )
                                else
                                  Expanded(
                                    child: Obx(
                                      () => Column(
                                        children: <Widget>[
                                          Expanded(
                                            child: ListView.builder(
                                              shrinkWrap: true,
                                              controller: controller.refundSaleScrollController,
                                              itemCount: controller.refundableSales.length,
                                              itemBuilder: (
                                                BuildContext context,
                                                int index,
                                              ) {
                                                return SaleOverviewCard(
                                                  sale: controller.refundableSales[index],
                                                  userMenuController: _userMenuController,
                                                  customer: controller
                                                      .refundableSalesCustomers[controller.refundableSales[index].document.saleHeader.customer],
                                                  onCardTap: () async {
                                                    final Sale tappedSale = controller.refundableSales[index];
                                                    controller.focusedTransactionHistorySale.value = some(
                                                      tappedSale,
                                                    );
                                                    _userMenuController.expandDrawer();

                                                    await Future<void>.delayed(
                                                      const Duration(
                                                        milliseconds: 200,
                                                      ),
                                                    ).then((void value) {
                                                      controller.transactionHistoryFocus.value = TransactionHistoryFocus.RECALL_SALE;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                          ),
                                          if (controller.filteredSalesIsFetching.value)
                                            const SizedBox(
                                              height: 70,
                                              width: 70,
                                              child: Padding(
                                                padding: EdgeInsets.all(20.0),
                                                child: CircularProgressIndicator(),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            );

                          case TransactionHistoryFocus.RECALL_SALE:
                            Customer? focusedCustomer;

                            final int tipTotal = controller.focusedTransactionHistorySale.value.match(
                              (Sale selectedSale) {
                                if ((selectedSale.document.saleHeader.customer ?? "") != "") {
                                  focusedCustomer = controller.refundableSalesCustomers[selectedSale.document.saleHeader.customer!];
                                }
                                int returnTotal = 0;
                                for (final SaleTender tender in selectedSale.document.saleHeader.tenders) {
                                  returnTotal += tender.tipAmount ?? 0;
                                }
                                return returnTotal;
                              },
                              () => 0,
                            );

                            final bool creditTender = controller.focusedTransactionHistorySale.value.match(
                              (Sale s) => s.document.saleHeader.tenders
                                  .where(
                                    (SaleTender tender) => tender.media == PaymentMediaType.Credit.index,
                                  )
                                  .isNotEmpty,
                              () => false,
                            );

                            final bool voidableTender = controller.focusedTransactionHistorySale.value.match(
                              (Sale s) => s.document.saleHeader.tenders.any(
                                (SaleTender tender) =>
                                    tender.media == PaymentMediaType.Credit.index && !tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index),
                              ),
                              () => false,
                            );

                            final bool voidBlockingTender = controller.focusedTransactionHistorySale.value.match(
                              (Sale s) => s.document.saleHeader.tenders.any(
                                (SaleTender tender) =>
                                    (tender.media == PaymentMediaType.EBT.index || tender.media == PaymentMediaType.Gift.index) &&
                                    !tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index),
                              ),
                              () => false,
                            );

                            final int? displayFlag =
                                Helpers.getDisplayFlag(controller.focusedTransactionHistorySale.value.match((Sale s) => s, () => Sale.empty()));

                            final bool restricted = displayFlag != SaleFlags.COMPLETED.index;

                            final bool dualPriced = controller.focusedTransactionHistorySale.value
                                .match(
                                  (Sale t) => t,
                                  () => Sale.empty(),
                                )
                                .document
                                .saleHeader
                                .saleFlags
                                .contains(
                                  SaleFlags.DUAL_PRICING.index,
                                );

                            final bool voidRestricted = controller.focusedTransactionHistorySale.value.match(
                              (Sale s) => s.document.saleHeader.saleFlags.any((int f) => voidRestrictedFlags.contains(f)),
                              () => false,
                            );

                            bool voidingProhibited = !voidableTender || voidRestricted || voidBlockingTender;

                            final int? termNumber = controller.focusedTransactionHistorySale.value.match(
                              (Sale t) => t.document.saleHeader.settleTerminalNumber,
                              () => null,
                            );

                            final bool isOnline = termNumber == Constants.onlineTerminalNumber;

                            bool tipAdjustProhibited = !creditTender || restricted;

                            final DateTime now = DateTime.now();

                            // Set the start time to look for sales from 5AM to now.
                            // If it's before 5AM today, use yesterday's 5AM as a start point
                            DateTime oneDay = DateTime(now.year, now.month, now.day, 5);
                            if (now.hour < 5) {
                              oneDay = oneDay.subtract(const Duration(days: 1));
                            }

                            controller.focusedTransactionHistorySale.value.match(
                              (Sale s) {
                                if ((s.end_at ?? oneDay).isBefore(oneDay)) {
                                  tipAdjustProhibited = true;
                                  voidingProhibited = true;
                                }
                              },
                              () => null,
                            );

                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ColoredBox(
                                color: R2Colors.white,
                                child: Row(
                                  children: <Widget>[
                                    const SizedBox(width: 12),
                                    Expanded(
                                      flex: 8,
                                      child: Column(
                                        children: <Widget>[
                                          Row(
                                            children: <Widget>[
                                              Expanded(child: Container()),
                                              Expanded(
                                                child: Column(
                                                  children: <Widget>[
                                                    Text(
                                                      "${controller.saleName} #${controller.focusedTransactionHistorySale.value.match((Sale t) => t.sale_number, () => "")}",
                                                      style: TextStyle(
                                                        color: R2Colors.black,
                                                        fontWeight: FontWeight.normal,
                                                        fontSize: 32,
                                                      ),
                                                    ),
                                                    if (termNumber != null)
                                                      Text(
                                                        termNumber == Constants.onlineTerminalNumber ? "Online" : "Terminal $termNumber",
                                                        style: TextStyle(
                                                          color: R2Colors.black,
                                                          fontWeight: FontWeight.normal,
                                                          fontSize: 20,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                              Expanded(
                                                child: Center(
                                                  child: Text(
                                                    displayFlag == null ? "N/A" : SaleFlags.values[displayFlag].friendlyString,
                                                    style: TextStyle(
                                                      fontSize: displayFlag == SaleFlags.NONREFUNDABLE.index ? 20 : 22,
                                                      color: displayFlag == null
                                                          ? R2Colors.neutral400
                                                          : displayFlag == SaleFlags.COMPLETED.index
                                                              ? Colors.green
                                                              : displayFlag == SaleFlags.PAID_OUT.index
                                                                  ? R2Colors.secondary500
                                                                  : displayFlag == SaleFlags.CANCEL.index || displayFlag == SaleFlags.COMBINED.index
                                                                      ? R2Colors.negativeRed
                                                                      : Colors.orange,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 20),
                                          Expanded(
                                            flex: 5,
                                            child: Row(
                                              children: <Widget>[
                                                Expanded(
                                                  flex: 10,
                                                  child: Container(
                                                    clipBehavior: Clip.hardEdge,
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                        color: R2Colors.neutral300,
                                                      ),
                                                      borderRadius: const BorderRadius.all(
                                                        Radius.circular(10),
                                                      ),
                                                    ),
                                                    child: Column(
                                                      children: <Widget>[
                                                        Expanded(
                                                          child: ClipRRect(
                                                            child: SingleChildScrollView(
                                                              child: RecallItems(
                                                                sale: controller.focusedTransactionHistorySale.value.match(
                                                                  (Sale s) => s,
                                                                  () => Sale.empty(),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 10),
                                                Expanded(
                                                  flex: focusedCustomer != null ? 5 : 4,
                                                  child: Column(
                                                    children: <Widget>[
                                                      Expanded(
                                                        child: Container(
                                                          clipBehavior: Clip.hardEdge,
                                                          decoration: BoxDecoration(
                                                            border: Border.all(
                                                              color: R2Colors.neutral300,
                                                            ),
                                                            borderRadius: const BorderRadius.all(
                                                              Radius.circular(10),
                                                            ),
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.stretch,
                                                            children: <Widget>[
                                                              DecoratedBox(
                                                                decoration: BoxDecoration(
                                                                  borderRadius: const BorderRadius.only(
                                                                    topRight: Radius.circular(
                                                                      10,
                                                                    ),
                                                                    topLeft: Radius.circular(
                                                                      10,
                                                                    ),
                                                                  ),
                                                                  color: R2Colors.neutral100,
                                                                ),
                                                                child: const Padding(
                                                                  padding: EdgeInsets.all(
                                                                    8.0,
                                                                  ),
                                                                  child: Center(
                                                                    child: Text(
                                                                      "Tenders",
                                                                      style: TextStyle(
                                                                        fontSize: 18,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Container(
                                                                height: 1,
                                                                color: R2Colors.neutral400,
                                                              ),
                                                              Expanded(
                                                                child: ClipRect(
                                                                  child: SingleChildScrollView(
                                                                    child: ListView.builder(
                                                                      shrinkWrap: true,
                                                                      itemCount: controller.focusedTransactionHistorySale.value.match(
                                                                        (Sale t) => t.document.saleHeader.tenders.length,
                                                                        () => null,
                                                                      ),
                                                                      itemBuilder: (
                                                                        BuildContext context,
                                                                        int index,
                                                                      ) {
                                                                        return Column(
                                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                          children: <Widget>[
                                                                            //TODO: Add chcekNum, memo, fullName
                                                                            Padding(
                                                                              padding: const EdgeInsets.all(
                                                                                8.0,
                                                                              ),
                                                                              child: Column(
                                                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                                children: <Widget>[
                                                                                  Column(
                                                                                    children: <Widget>[
                                                                                      Row(
                                                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                                                        children: <Widget>[
                                                                                          Text(
                                                                                            PaymentMediaType
                                                                                                .values[controller.focusedTransactionHistorySale.value
                                                                                                    .match(
                                                                                              (Sale t) => t.document.saleHeader.tenders[index].media!,
                                                                                              () => -1,
                                                                                            )]
                                                                                                .string,
                                                                                            style: TextStyle(
                                                                                              color: R2Colors.black,
                                                                                              fontWeight: FontWeight.normal,
                                                                                              fontSize: 16,
                                                                                            ),
                                                                                          ),
                                                                                          if (controller.focusedTransactionHistorySale.value.match(
                                                                                                (Sale t) {
                                                                                                  if (t.document.saleHeader.tenders[index]
                                                                                                          .cardTransactionData !=
                                                                                                      null) {
                                                                                                    return t.document.saleHeader.tenders[index]
                                                                                                        .cardTransactionData!.cardPAN;
                                                                                                  }
                                                                                                  return null;
                                                                                                },
                                                                                                () => null,
                                                                                              ) !=
                                                                                              null)
                                                                                            Text(
                                                                                              " (${controller.focusedTransactionHistorySale.value.match(
                                                                                                (Sale t) {
                                                                                                  if (t.document.saleHeader.tenders[index]
                                                                                                          .cardTransactionData !=
                                                                                                      null) {
                                                                                                    return t.document.saleHeader.tenders[index]
                                                                                                        .cardTransactionData!.cardPAN;
                                                                                                  }
                                                                                                  return null;
                                                                                                },
                                                                                                () => "",
                                                                                              )})",
                                                                                              style: TextStyle(
                                                                                                color: R2Colors.black,
                                                                                                fontWeight: FontWeight.normal,
                                                                                                fontSize: 12,
                                                                                              ),
                                                                                            ),
                                                                                        ],
                                                                                      ),
                                                                                      if (controller.focusedTransactionHistorySale.value.match(
                                                                                            (Sale t) {
                                                                                              if (t.document.saleHeader.tenders[index]
                                                                                                      .cardTransactionData !=
                                                                                                  null) {
                                                                                                return t.document.saleHeader.tenders[index]
                                                                                                    .cardTransactionData!.cardHolder
                                                                                                    .replaceAll(" ", "");
                                                                                              }
                                                                                              return "";
                                                                                            },
                                                                                            () => "",
                                                                                          ) !=
                                                                                          "")
                                                                                        Text(
                                                                                          controller.focusedTransactionHistorySale.value.match(
                                                                                            (Sale t) {
                                                                                              if (t.document.saleHeader.tenders[index]
                                                                                                      .cardTransactionData !=
                                                                                                  null) {
                                                                                                return t.document.saleHeader.tenders[index]
                                                                                                    .cardTransactionData!.cardHolder
                                                                                                    .replaceAll(" ", "");
                                                                                              }
                                                                                              return "n/a";
                                                                                            },
                                                                                            () => "",
                                                                                          ),
                                                                                          style: TextStyle(
                                                                                            color: R2Colors.neutral500,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            fontSize: 15,
                                                                                          ),
                                                                                        ),
                                                                                      if (controller.focusedTransactionHistorySale.value.match(
                                                                                            (Sale t) =>
                                                                                                t.document.saleHeader.tenders[index].memo ?? "",
                                                                                            () => "",
                                                                                          ) !=
                                                                                          "")
                                                                                        Text(
                                                                                          "(${controller.focusedTransactionHistorySale.value.match(
                                                                                            (Sale t) =>
                                                                                                t.document.saleHeader.tenders[index].memo ?? "",
                                                                                            () => "",
                                                                                          )})",
                                                                                          style: TextStyle(
                                                                                            color: R2Colors.neutral500,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            fontSize: 15,
                                                                                          ),
                                                                                        ),
                                                                                    ],
                                                                                  ),
                                                                                  Column(
                                                                                    children: <Widget>[
                                                                                      Text(
                                                                                        "\$${Helpers.formatCurrency(controller.focusedTransactionHistorySale.value.match((Sale t) => t.document.saleHeader.tenders[index].amount!, () => 0))}",
                                                                                        style: TextStyle(
                                                                                          color: R2Colors.black,
                                                                                          fontWeight: FontWeight.normal,
                                                                                          fontSize: 20,
                                                                                        ),
                                                                                      ),
                                                                                      if (controller.focusedTransactionHistorySale.value.match(
                                                                                            (Sale t) =>
                                                                                                t.document.saleHeader.tenders[index].tipAmount ?? 0,
                                                                                            () => 0,
                                                                                          ) !=
                                                                                          0)
                                                                                        Text(
                                                                                          " +Tip \$${Helpers.formatCurrency(controller.focusedTransactionHistorySale.value.match((Sale t) => t.document.saleHeader.tenders[index].tipAmount ?? 0, () => 0))}",
                                                                                          style: TextStyle(
                                                                                            color: R2Colors.black,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            fontSize: 16,
                                                                                          ),
                                                                                        ),
                                                                                      if (controller.focusedTransactionHistorySale.value.match(
                                                                                        (Sale t) => t
                                                                                            .document.saleHeader.tenders[index].saleTenderFlags
                                                                                            .contains(
                                                                                          SaleTenderFlags.VOIDED.index,
                                                                                        ),
                                                                                        () => false,
                                                                                      ))
                                                                                        const Text(
                                                                                          "voided",
                                                                                          style: TextStyle(
                                                                                            color: Colors.red,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            fontSize: 16,
                                                                                          ),
                                                                                        ),
                                                                                    ],
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ),
                                                                            Container(
                                                                              height: 1,
                                                                              color: R2Colors.neutral200,
                                                                            ),
                                                                          ],
                                                                        );
                                                                      },
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      if (focusedCustomer != null)
                                                        Padding(
                                                          padding: const EdgeInsets.only(
                                                            top: 5,
                                                          ),
                                                          child: Container(
                                                            width: double.infinity,
                                                            decoration: BoxDecoration(
                                                              borderRadius: const BorderRadius.all(
                                                                Radius.circular(10),
                                                              ),
                                                              border: Border.all(
                                                                color: R2Colors.neutral300,
                                                              ),
                                                              color: R2Colors.neutral100,
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.all(5.0),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                                children: <Widget>[
                                                                  const Padding(
                                                                    padding: EdgeInsets.all(2),
                                                                    child: Text(
                                                                      "Customer: ",
                                                                      style: TextStyle(
                                                                        fontWeight: FontWeight.bold,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsets.all(2),
                                                                    child: Text(
                                                                      "${focusedCustomer!.document.firstName} ${focusedCustomer!.document.lastName}",
                                                                      textAlign: TextAlign.center,
                                                                      softWrap: true,
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsets.all(2),
                                                                    child: Text(
                                                                      Helpers.formatPhoneNumber(
                                                                        focusedCustomer!.document.phone1 ?? "",
                                                                      ),
                                                                      textAlign: TextAlign.center,
                                                                      style: TextStyle(
                                                                        color: R2Colors.neutral500,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsets.all(2),
                                                                    child: Text(
                                                                      Helpers.formatCustomerAddress(
                                                                        focusedCustomer!,
                                                                      ),
                                                                      textAlign: TextAlign.center,
                                                                      softWrap: true,
                                                                      style: TextStyle(
                                                                        color: R2Colors.neutral500,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  if ((focusedCustomer!.document.company ?? "") != "")
                                                                    Padding(
                                                                      padding: const EdgeInsets.all(2),
                                                                      child: Text(
                                                                        focusedCustomer!.document.company!,
                                                                        textAlign: TextAlign.center,
                                                                        softWrap: true,
                                                                        style: TextStyle(
                                                                          color: R2Colors.neutral500,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 10),
                                          Expanded(
                                            child: Column(
                                              children: <Widget>[
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: <Widget>[
                                                    Text(
                                                      "Transacted:",
                                                      style: TextStyle(
                                                        color: R2Colors.neutral900,
                                                        fontWeight: FontWeight.normal,
                                                        fontSize: 20,
                                                      ),
                                                    ),
                                                    Text(
                                                      DateFormat.yMMMd('en_US').add_jm().format(
                                                            controller.focusedTransactionHistorySale.value.match(
                                                                  (Sale t) => t.end_at!.toLocal(),
                                                                  () => null,
                                                                ) ??
                                                                DateTime.now(),
                                                          ),
                                                      style: TextStyle(
                                                        color: R2Colors.neutral900,
                                                        fontWeight: FontWeight.normal,
                                                        fontSize: 20,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: <Widget>[
                                                    Text(
                                                      "Total:",
                                                      style: TextStyle(
                                                        color: R2Colors.black,
                                                        fontWeight: FontWeight.normal,
                                                        fontSize: 24,
                                                      ),
                                                    ),
                                                    if (dualPriced)
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.focusedTransactionHistorySale.value.match((Sale t) => t.document.saleHeader.cashTotal + tipTotal, () => 0))}",
                                                        style: TextStyle(
                                                          color: R2Colors.black,
                                                          fontWeight: FontWeight.normal,
                                                          fontSize: 24,
                                                        ),
                                                      )
                                                    else
                                                      Text(
                                                        "\$${Helpers.formatCurrency(controller.focusedTransactionHistorySale.value.match((Sale t) => t.document.saleHeader.total + tipTotal, () => 0))}",
                                                        style: TextStyle(
                                                          color: R2Colors.black,
                                                          fontWeight: FontWeight.normal,
                                                          fontSize: 24,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 20),
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                        children: <Widget>[
                                          RadiusListener(
                                            onTap: () async {
                                              await Get.bottomSheet(
                                                ThinBottomSheet(
                                                  child: SaleInfoDialog(
                                                    sale: controller.focusedTransactionHistorySale.value.getOrElse(
                                                      () => Sale.empty(),
                                                    ),
                                                    saleName: controller.saleName,
                                                  ),
                                                ),
                                                isScrollControlled: true,
                                              );
                                            },
                                            child: TextButton(
                                              style: ButtonStyle(
                                                backgroundColor: MaterialStateProperty.all<Color?>(
                                                  R2Colors.primary500,
                                                ),
                                              ),
                                              onPressed: () {},
                                              child: Padding(
                                                padding: const EdgeInsets.all(bttnPaddingSize),
                                                child: Text(
                                                  "${controller.saleName} Info",
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    color: R2Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: bttnFontSize,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          if (allowReprint == true)
                                            RadiusListener(
                                              onTap: () async {
                                                await controller.reprintCheck(
                                                  sale: controller.focusedTransactionHistorySale.value,
                                                  copyOpts: copyOpts,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Reprint Check",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (allowReprint == true)
                                            RadiusListener(
                                              onTap: () async {
                                                if (!creditTender || restricted) return;
                                                await controller.reprintCheck(
                                                  sale: controller.focusedTransactionHistorySale.value,
                                                  authSlip: true,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    !creditTender || restricted ? R2Colors.neutral400 : R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: !creditTender || restricted ? null : () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Reprint Auth Slip",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (tipAdjust == true)
                                            RadiusListener(
                                              onTap: () async {
                                                if (!creditTender || restricted) return;
                                                await controller.transactionTipAdjust();
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    !creditTender || restricted ? R2Colors.neutral400 : R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: tipAdjustProhibited ? null : () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Tip Adjust",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (allowRefund == true)
                                            RadiusListener(
                                              onTap: () async {
                                                if (restricted || isOnline) return;
                                                await controller.refundCheckDialog(
                                                  sale: controller.focusedTransactionHistorySale.value,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    restricted || isOnline ? R2Colors.neutral400 : R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: restricted || isOnline ? null : () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Refund",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (allowVoid == true)
                                            RadiusListener(
                                              onTap: () async {
                                                if (voidingProhibited) return;

                                                await controller.voidCredit(
                                                  voidingSale: controller.focusedTransactionHistorySale.value,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    voidingProhibited ? R2Colors.neutral400 : R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: voidingProhibited ? null : () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Void Credit",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (allowReopen == true)
                                            RadiusListener(
                                              onTap: () async {
                                                if (restricted) return;
                                                await controller.refundCheckDialog(
                                                  sale: controller.focusedTransactionHistorySale.value,
                                                  reopen: true,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    restricted ? R2Colors.neutral400 : R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: restricted ? null : () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Reopen  ${controller.saleName}",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          if (allowCopy == true)
                                            RadiusListener(
                                              onTap: () async {
                                                await controller.focusedTransactionHistorySale.value.match(
                                                  (Sale s) async {
                                                    await _userMenuController.makeOpenCopy(
                                                      sale: s,
                                                      reOpening: false,
                                                    );
                                                    _userMenuController.shrinkDrawer();
                                                    controller.focusedTransactionHistorySale.value = none();
                                                    controller.transactionHistoryFocus.value = TransactionHistoryFocus.SEARCH_SALE;
                                                    Get.back();
                                                  },
                                                  () => null,
                                                );
                                              },
                                              child: TextButton(
                                                style: ButtonStyle(
                                                  backgroundColor: MaterialStateProperty.all<Color?>(
                                                    R2Colors.primary500,
                                                  ),
                                                ),
                                                onPressed: () {},
                                                child: Padding(
                                                  padding: const EdgeInsets.all(bttnPaddingSize),
                                                  child: Text(
                                                    "Make Copy",
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: R2Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: bttnFontSize,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          default:
                            return Container();
                        }
                      });
                  }
                }),
              ),
            ],
          ),
        );
      },
    );
  }
}

class SaleOverviewCard extends StatelessWidget {
  const SaleOverviewCard({
    required this.sale,
    required this.userMenuController,
    required this.onCardTap,
    this.customer,
  });
  final Sale sale;
  final UserMenuController userMenuController;
  final Function onCardTap;
  final Customer? customer;

  @override
  Widget build(BuildContext context) {
    // Spin up an empty string buffer
    final List<String> tenderStrings = <String>[];
    final StringBuffer customerStringBuffer = StringBuffer();
    final List<String> customerStrings = <String>[];

    // Loop through tenders and convert media types to strings in an array, also get customers
    for (final SaleTender tender in sale.document.saleHeader.tenders) {
      if (!tenderStrings.contains(
        PaymentMediaType.values[tender.media!].string,
      )) {
        tenderStrings.add(
          PaymentMediaType.values[tender.media!].string,
        );
      }
      if (tender.cardTransactionData != null) {
        final String cardHolderName = tender.cardTransactionData!.cardHolder.replaceAll(" ", "");
        if (!customerStrings.contains(cardHolderName)) {
          customerStrings.add(cardHolderName);
        }
      }
    }

    int? displayFlag;

    for (final int f in sale.document.saleHeader.saleFlags) {
      if (Constants.restrictedFlags.contains(f)) {
        displayFlag = f;
        break;
      }
    }

    if (sale.document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      displayFlag ??= SaleFlags.COMPLETED.index;
    }

    String totalString = Helpers.formatCurrency(sale.document.saleHeader.total);

    if (sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
      totalString = Helpers.formatCurrency(sale.document.saleHeader.cashTotal);
    }

    // Write array contents to string buffer seperated by a comma
    final String tenderString = tenderStrings.isEmpty
        ? ""
        : tenderStrings.length > 1
            ? "${tenderStrings.length} Tenders"
            : tenderStrings.first;
    customerStringBuffer.writeAll(customerStrings, ",");

    if (displayFlag == null) return Container();

    return RadiusListener(
      onTap: onCardTap,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          side: BorderSide(color: R2Colors.neutral500),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            "#${sale.sale_number}",
                            style: TextStyle(
                              color: R2Colors.black,
                              fontWeight: FontWeight.normal,
                              fontSize: 20,
                            ),
                          ),
                          if (sale.document.saleHeader.settleTerminalNumber != null)
                            sale.document.saleHeader.settleTerminalNumber == Constants.onlineTerminalNumber
                                ? Text(
                                    "Online",
                                    style: TextStyle(
                                      color: R2Colors.secondary500,
                                      fontWeight: FontWeight.normal,
                                      fontSize: 12,
                                    ),
                                  )
                                : Text(
                                    "Term. ${sale.document.saleHeader.settleTerminalNumber}",
                                    style: TextStyle(
                                      color: R2Colors.black,
                                      fontWeight: FontWeight.normal,
                                      fontSize: 12,
                                    ),
                                  ),
                        ],
                      ),
                      Text(
                        tenderString,
                        style: TextStyle(
                          color: R2Colors.black,
                          fontWeight: FontWeight.normal,
                          fontSize: 15,
                        ),
                      ),
                      Text(
                        "\$$totalString",
                        style: TextStyle(
                          color: R2Colors.black,
                          fontWeight: FontWeight.normal,
                          fontSize: 18,
                        ),
                      ),
                      Column(
                        children: <Widget>[
                          Text(
                            sale.end_at != null
                                ? DateFormat.yMMMd('en_US').add_jm().format(sale.end_at!.toLocal())
                                : (sale.document.saleHeader.customerName ?? "") != ""
                                    ? sale.document.saleHeader.customerName!
                                    : (sale.document.saleHeader.tableDesc ?? "") != ""
                                        ? sale.document.saleHeader.tableDesc!
                                        : "                            ",
                            style: TextStyle(
                              color: R2Colors.neutral500,
                              fontWeight: FontWeight.normal,
                              fontSize: 13,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            SaleFlags.values[displayFlag].friendlyString,
                            style: TextStyle(
                              fontSize: displayFlag == SaleFlags.NONREFUNDABLE.index ? 12 : 14,
                              color: displayFlag == SaleFlags.COMPLETED.index
                                  ? Colors.green
                                  : displayFlag == SaleFlags.PAID_OUT.index
                                      ? R2Colors.secondary500
                                      : displayFlag == SaleFlags.CANCEL.index || displayFlag == SaleFlags.COMBINED.index
                                          ? R2Colors.negativeRed
                                          : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  if (customerStringBuffer.toString() != "")
                    Text(
                      customerStringBuffer.toString(),
                      style: TextStyle(
                        color: R2Colors.black,
                        fontWeight: FontWeight.normal,
                        fontSize: 13,
                      ),
                    ),
                ],
              ),
            ),
            // const Divider(),
            // for (int i = 0; i < sale.document.saleRows.length; i++)
            //   if (i < 3)
            //     Padding(
            //       padding: const EdgeInsets.only(bottom: 4.0),
            //       child: Row(
            //         children: <Widget>[
            //           const Spacer(),
            //           Expanded(
            //             child: Text(
            //               sale.document.saleRows[i].qty.toString(),
            //               style: const TextStyle(
            //                 color: Colors.black,
            //                 fontWeight: FontWeight.normal,
            //                 fontSize: 17,
            //               ),
            //             ),
            //           ),
            //           const Spacer(),
            //           //TODO: Clip the receipt description over a certain length
            //           //to keep everything in one line.
            //           Expanded(
            //             flex: 13,
            //             child: Text(
            //               sale.document.saleRows[i].receiptDescription,
            //               style: const TextStyle(
            //                 color: Colors.black,
            //                 fontWeight: FontWeight.normal,
            //                 fontSize: 17,
            //               ),
            //             ),
            //           ),
            //           const Spacer(),
            //           Expanded(
            //             flex: 4,
            //             child: Text(
            //               "\$${Helpers.formatCurrency(sale.document.saleRows[i].actualPrice)}",
            //               style: const TextStyle(
            //                 color: Colors.black,
            //                 fontWeight: FontWeight.normal,
            //                 fontSize: 17,
            //               ),
            //             ),
            //           ),
            //           const Spacer(),
            //         ],
            //       ),
            //     ),
            // if (sale.document.saleRows.length > 3)
            //   Padding(
            //     padding: const EdgeInsets.only(bottom: 8.0),
            //     child: Text(
            //       "${sale.document.saleRows.length - 3} more item${(sale.document.saleRows.length - 3) > 1 ? "s" : ""}",
            //       style: TextStyle(
            //         color: R2Colors.neutral500,
            //         fontWeight: FontWeight.normal,
            //         fontSize: 17,
            //       ),
            //     ),
            //   ),
            if (customer != null)
              Column(
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.only(top: 3),
                    child: Container(
                      height: 2,
                      width: double.infinity,
                      color: R2Colors.neutral200,
                    ),
                  ),
                  DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(10),
                        bottomLeft: Radius.circular(10),
                      ),
                      color: R2Colors.neutral100,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Column(
                        children: <Widget>[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              const Text(
                                "Customer: ",
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                "${customer!.document.firstName} ${customer!.document.lastName}",
                              ),
                              const Text(" - "),
                              Text(
                                (customer!.document.phone1 ?? "") == ""
                                    ? "(___)___-____"
                                    : Helpers.formatPhoneNumber(
                                        customer!.document.phone1!,
                                      ),
                              ),
                            ],
                          ),
                          if ((customer!.document.address ?? "") != "")
                            Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: Text(
                                Helpers.formatCustomerAddress(
                                  customer!,
                                ),
                                style: TextStyle(
                                  color: R2Colors.neutral500,
                                ),
                              ),
                            ),
                          if ((customer!.document.company ?? "") != "")
                            Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: Text(
                                "Company: ${customer!.document.company}",
                                style: TextStyle(
                                  color: R2Colors.neutral500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
