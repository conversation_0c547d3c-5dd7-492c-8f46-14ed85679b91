// ignore_for_file: avoid_dynamic_calls, must_be_immutable, always_specify_types, no_leading_underscores_for_local_identifiers
import 'package:desktop/app/data/enums/register_menu_options.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/global_widgets/fluent_settings/settings_list.dart';
import 'package:desktop/app/global_widgets/fluent_settings/settings_option.dart';
import 'package:desktop/app/global_widgets/fluent_settings/settings_section.dart';
import 'package:desktop/app/modules/register/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/assign_cashier.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/cashier_report/widget.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/controller.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/customer_attach.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/find_lost_sale.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/gift_inquiry/widget.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/gift_issuance/widget.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/open_price.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/paid_out.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/pick_up.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/pre_auth/widget.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/promised_time.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/recipe/dialog.dart';
import 'package:desktop/app/modules/register/widgets/user_menu/transaction_history/widget.dart';
import 'package:desktop/app/modules/time_clock/server_report_preview/server_report_preview.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class UserMenuWidget extends StatelessWidget {
  const UserMenuWidget({
    this.option,
    this.isSuperMenu = false,
    this.fields,
  });

  final RegisterMenuOptions? option;
  final bool isSuperMenu;
  final Map<String, dynamic>? fields;

  @override
  Widget build(BuildContext context) {
    final IdentityService _identityService = Get.find();
    final RegisterController _registerController = Get.find();

    return GetBuilder<UserMenuController>(
      init: UserMenuController(fields: fields),
      builder: (UserMenuController controller) {
        if (option != null) {
          controller.currentSettingScreen.value = option!;
          controller.fromToolbar = true;
        }
        return Obx(() {
          return Expanded(
            child: Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(child: Container()),
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeIn,
                      decoration: BoxDecoration(
                        color: R2Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: controller.superMenuOpen.value ? Colors.black.withOpacity(0.3) : Colors.transparent,
                            spreadRadius: 5,
                            blurRadius: 7,
                          ),
                        ],
                      ),
                      width: _registerController.drawerSize.value,
                      height: MediaQuery.of(context).size.height,
                      child: Obx(() {
                        if (controller.fromToolbar && controller.currentSettingScreen.value == RegisterMenuOptions.ROOT) {
                          Get.back();
                        }
                        if (!controller.menuLoaded.value) {
                          return const Text("Loading...");
                        } else {
                          _registerController.lastMenuScreen = option ?? controller.currentSettingScreen.value;
                          switch (option ?? controller.currentSettingScreen.value) {
                            case RegisterMenuOptions.ROOT:
                              controller.fields = null;
                              return RegisterRootMenu(isSuperMenu: isSuperMenu);
                            case RegisterMenuOptions.PRE_AUTH:
                              return RegisterPreAuthMenu();
                            case RegisterMenuOptions.GIFT_CARD_ISSUE:
                              return RegisterGiftCardIssueMenu();
                            case RegisterMenuOptions.GIFT_CARD_BALANCE:
                              return RegisterGiftCardInquiryMenu();
                            case RegisterMenuOptions.TRANSACTION_HISTORY:
                              return RegisterTransactionHistoryMenu(
                                allowReprint: controller.fields?["reprnt"] as bool?,
                                allowRefund: controller.fields?["refund"] as bool?,
                                allowReopen: controller.fields?["reOpen"] as bool?,
                                allowVoid: controller.fields?["void"] as bool?,
                                allowCopy: controller.fields?["copy"] as bool?,
                                copyOpts: controller.fields?["copyOpts"] as int?,
                                tipAdjust: controller.fields?["tipAdjust"] as bool?,
                              );
                            case RegisterMenuOptions.SALE_OPEN_PRICE_ITEM:
                              return OpenPriceMenu();
                            case RegisterMenuOptions.PAID_OUT:
                              return RegisterPaidOutMenu();
                            case RegisterMenuOptions.PICK_UP:
                              return PickUpMenu();
                            case RegisterMenuOptions.CUSTOMER_ATTACH:
                              return RegisterCustomerAttachMenu();
                            case RegisterMenuOptions.ASSIGN_CASHIER:
                              return RegisterAssingCashierMenu();
                            case RegisterMenuOptions.CASHIER_REPORT:
                              return CashierReportMenu();
                            case RegisterMenuOptions.PROMISED_TIME:
                              return PromisedTimeMenu();
                            case RegisterMenuOptions.FIND_LOST_SALE:
                              return FindLostSaleMenu();
                            case RegisterMenuOptions.SEARCH_RECIPE:
                              return SearchRecipeDialog();
                            case RegisterMenuOptions.SERVER_REPORT:
                              // ignore: avoid_print
                              print(controller.serverStart);
                              // ignore: avoid_print
                              print(controller.serverEnd);
                              return ServerReportPreview(
                                start: controller.serverStart.toString(),
                                end: controller.serverEnd.toString(),
                                empID: _identityService.currentEmployee.id,
                                isBOH: false,
                              );
                          }
                        }
                      }),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
      },
    );
  }
}

class RegisterRootMenu extends GetView<UserMenuController> {
  const RegisterRootMenu({
    this.isSuperMenu = false,
  });

  final bool isSuperMenu;

  @override
  Widget build(BuildContext context) {
    final RegisterController _registerController = Get.find();

    final List<RegisterMenusJsonRecordButton> menu = isSuperMenu
        ? _registerController.registerMenusConfig.value.document!.admin.menu
        : _registerController.registerMenusConfig.value.document!.user.menu;

    final bool customColorsBool = isSuperMenu
        ? _registerController.registerMenusConfig.value.document!.admin.custom_colors
        : _registerController.registerMenusConfig.value.document!.user.custom_colors;

    menu.sort((a, b) => a.index.compareTo(b.index));

    List<FluentSettingsOption> makeMenuList(String title) {
      return (!isSuperMenu && !_registerController.userMenuActive.value) || (isSuperMenu && !_registerController.adminMenuActive.value)
          ? []
          : menu
              .where(
              (RegisterMenusJsonRecordButton e) => e.section == title,
            )
              .map((RegisterMenusJsonRecordButton e) {
              return FluentSettingsOption(
                title: e.text,
                color: e.fGnd != null && customColorsBool ? Color(e.fGnd!) : null,
                backgroundColor: e.bGnd != null && customColorsBool ? Color(e.bGnd!) : null,
                onPressed: () async => await _registerController.switchFunctions(e),
              );
            }).toList();
    }

    final List<FluentSettingsOption> giftMenu = makeMenuList("GIFT");
    final List<FluentSettingsOption> saleMenu = makeMenuList("SALE");
    final List<FluentSettingsOption> globalMenu = makeMenuList("GLOBAL");
    final List<FluentSettingsOption> ebtMenu = makeMenuList("EBT");

    return Scaffold(
      body: FluentSettingsList(
        backgroundColor: isSuperMenu ? R2Colors.negativeRedDisabled : R2Colors.primary100,
        sections: <FluentSettingsSection>[
          FluentSettingsSection(
            heading: "Gift",
            visible: giftMenu.isNotEmpty,
            options: giftMenu,
          ),
          FluentSettingsSection(
            heading: "Sale",
            visible: saleMenu.isNotEmpty,
            options: saleMenu,
          ),
          FluentSettingsSection(
            heading: "Global",
            visible: globalMenu.isNotEmpty,
            options: globalMenu,
          ),
          FluentSettingsSection(
            heading: "EBT",
            visible: ebtMenu.isNotEmpty,
            options: ebtMenu,
          ),
        ],
      ),
    );
  }
}

class MenuButton extends StatelessWidget {
  const MenuButton({
    required this.onTap,
    required this.title,
  });

  final Function onTap;
  final String title;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(width: 0.5, color: R2Colors.neutral300),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                title,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MenuDrillDownButton extends StatelessWidget {
  const MenuDrillDownButton({
    required this.onTap,
    required this.title,
  });

  final Function onTap;
  final String title;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: DecoratedBox(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(width: 0.5, color: Colors.grey),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                title,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: FaIcon(
                FontAwesomeIcons.chevronRight,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MenuBackButton extends StatelessWidget {
  MenuBackButton({
    required this.title,
    required this.onPressed,
    this.rightWidget,
    this.text = "Back",
  });

  final String title;
  final Function onPressed;
  Widget? rightWidget;
  String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Expanded(
          flex: 5,
          child: Stack(
            children: <Widget>[
              Positioned(
                left: 0,
                child: Listener(
                  onPointerDown: (_) => onPressed(),
                  child: TextButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all<Color?>(
                        Colors.transparent,
                      ),
                    ),
                    onPressed: () {},
                    child: Padding(
                      padding: const EdgeInsets.all(25),
                      child: Row(
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: FaIcon(
                              FontAwesomeIcons.chevronLeft,
                              size: 20,
                              color: R2Colors.primary500,
                            ),
                          ),
                          Text(
                            text,
                            style: TextStyle(
                              color: R2Colors.primary500,
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Center(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
              ),
              Positioned(right: 15, top: 15, child: rightWidget ?? Container()),
            ],
          ),
        ),
      ],
    );
  }
}
