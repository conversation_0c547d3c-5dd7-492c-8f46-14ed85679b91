// ignore_for_file: avoid_dynamic_calls

import 'dart:async';
import 'dart:io';

import 'package:backoffice/main.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/permission.service.dart';
import 'package:desktop/app/data/services/pole_display.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/modules/sign_on/quick_sign_in/dialog.dart';
import 'package:desktop/app/routes/app_pages.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';

const String GET_EMPLOYEE_BY_PASSWORD_QUERY = '''
  query GET_EMPLOYEE_BY_PASSWORD(\$password: String!){
    employee(where:{password:{_eq:\$password}}){
      employee
      employee_class
      document
      created_at
      created_by
      updated_at
      updated_by
      password
      id
      is_active
    }
  }
''';

final Logger _logger = Logger("SignOnController");

class SignOnController extends GetxController {
  SignOnController();

  final GraphqlService _graphqlService = Get.find();

  final IdentityService _identityService = Get.find();
  final ConfigService _configService = Get.find();
  final NotificationService _notificationService = Get.find();
  final PoleDisplayService _poleDisplayService = Get.find();
  final ActivityService _activityService = Get.find();
  final EmployeeService _employeeService = Get.find();
  final PrinterService _printerService = Get.find();

  final RxString currentTime = "".obs;
  final Rx<MerchantJsonRecordModules> modules = MerchantJsonRecord.empty().document.modules!.obs;

  final File _versionFile = kDebugMode ? File("../../VERSION") : File("/opt/r2pos/VERSION");

  final RxString currentFork = "FORK".obs;
  final RxString currentVersion = "Debug".obs;

  RxString numPadValue = "".obs;
  final RxBool clearNumPadTrigger = false.obs;

  @override
  Future<void> onInit() async {
    await _identityService.getTerminalInfo();
    await _printerService.getRecords();
    await _configService.getRecords();

    final MerchantJsonRecordDocument merchantDoc = _configService.merchantConfig.document;
    if (merchantDoc.modules != null) {
      modules.value = merchantDoc.modules!;
    }

    CURRENT_EMPLOYEE.value = Employee.empty();

    _poleDisplayService.banner();

    currentTime.value = Constants.clockFormat.format(DateTime.now());

    Timer.periodic(const Duration(seconds: 1), (Timer t) {
      currentTime.value = Constants.clockFormat.format(DateTime.now());
    });

    try {
      final String version = await _versionFile.readAsString();
      // get first line of file

      final List<String> contents = version.split("\n");

      _logger.info("Version: ${contents[0]}");

      currentVersion.value = contents[0];
      currentFork.value = contents[1];
    } catch (e) {
      _logger.severe("Failed to read version file", e);
    }

    super.onInit();
  }

  @override
  Future<void> onReady() async {
    if (_identityService.terminalQuickSignIn) {
      final RxList<Employee> clockedInEmployees = (await _employeeService.getClockedInEmployees(
        quickSignIn: true,
        quickSignSection: _identityService.terminalQuickSignSection,
        quickSignMinutes: _identityService.terminalQuickSignMinutes,
      ))
          .obs;

      if (clockedInEmployees.isEmpty) return;

      await Get.defaultDialog<Employee?>(
        content: QuickSignInDialog(clockedInEmployees),
        title: "Quick Sign-In",
      ).then((Employee? val) {
        if (val != null) {
          signOn(
            () => Get.offAllNamed(AppRoutes.REGISTER),
            "Front Of House",
            employee: val,
          );
        }
      });
    }
    super.onReady();
  }

  Future<void> signOn(
    Function onPressed,
    String route, {
    bool bypassAuth = false,
    Employee? employee,
  }) async {
    final Activity insertActivity = Activity.empty();

    try {
      if (!bypassAuth) {
        if (employee == null) {
          if (numPadValue.value == "") {
            throw "Please enter a PIN!";
          }

          final QueryResult<Object?> employeeResult = await _graphqlService.client.query(
            QueryOptions<Object?>(
              document: g.parseString(GET_EMPLOYEE_BY_PASSWORD_QUERY),
              variables: <String, dynamic>{
                "password": numPadValue.value,
              },
            ),
          );

          if (employeeResult.hasException) {
            throw employeeResult.exception.toString();
          }
          if (employeeResult.data == null) throw "Invalid PIN!";

          final List<Employee> employees =
              // ignore: always_specify_types
              (employeeResult.data!['employee'] as List)
                  .map(
                    (dynamic employee) => Employee.fromJson(employee as Map<String, dynamic>),
                  )
                  .toList();

          if (employees.isEmpty) throw "Invalid PIN!";
          if (!employees.first.is_active) throw "Employee is inactive!";

          employee = employees.first;
        }

        final bool view = await PermissionService.enforce(
          employee.employee_class,
          "access",
          route,
          _graphqlService,
        );

        if (view) {
          if (route.toLowerCase() == "front of house") {
            final bool bypassSignOn = await PermissionService.enforce(
              employee.employee_class,
              "access",
              "Register Without Clock In",
              _graphqlService,
            );
            if (!bypassSignOn) {
              final QueryResult<Object?> cardQueryRes = await _graphqlService.client.query(
                QueryOptions<Object?>(
                  document: g.parseString('''
                  query GET_RECENT_TIMECARD {
                    timecard(order_by: {punch_at: desc}, where: {emp_id: {_eq: "${employee.id}"}}, limit: 1) {
                      timecard
                      round_at
                      punch_type
                      punch_at
                      job_code
                      emp_id
                      break_idx
                    }
                  }
                  '''),
                ),
              );
              if (cardQueryRes.hasException) {
                throw cardQueryRes.exception.toString();
              }
              final List<dynamic> cardQueryList = cardQueryRes.data?["timecard"] as List<dynamic>;
              if (cardQueryList.isNotEmpty) {
                final TimeCard recentTimeCard = TimeCard.fromJson(cardQueryList[0] as Map<String, dynamic>);
                if (recentTimeCard.punch_type == 2) {
                  throw "Must be clocked in!";
                }
              } else {
                throw "Must be clocked in!";
              }
            }
          }

          await _identityService.setCurrentUser(employee: employee);

          // ///
          // ///
          // /// insert Activity access granted
          // insertActivity.activity = ActivityFlags.ACCESS_GRANTED.index;
          // insertActivity.str_data = "${route.capitalizeFirst} access granted";
          // insertActivity.emp_id = _identityService.currentEmployee.id;
          // insertActivity.term_num = _identityService.terminalNumber;
          // await _activityService.insertActivity(activity: insertActivity);
          onPressed();
        } else {
          ///
          ///
          ///insert activity access denied (permissions)
          insertActivity.activity = ActivityFlags.ACCESS_DENIED.index;
          insertActivity.str_data = "${route.capitalizeFirst} access Denied";
          insertActivity.emp_id = _identityService.currentEmployee.id;
          insertActivity.term_num = _identityService.terminalNumber;
          await _activityService.insertActivity(activity: insertActivity);
          _notificationService.error("Access denied");
        }
      } else {
        onPressed();
      }
    } catch (err, stack) {
      ///
      ///
      /// inser activity invalid pin (null emp_id)
      insertActivity.activity = ActivityFlags.INVALID_PIN.index;
      insertActivity.str_data = "Invalid pin";
      insertActivity.emp_id = _identityService.currentEmployee.id;
      insertActivity.term_num = _identityService.terminalNumber;
      await _activityService.insertActivity(activity: insertActivity);
      _logger.shout("Failed to sign on", err, stack);
      _notificationService.error(err.toString());
    }

    clearNumPadTrigger.value = true;
  }
}
