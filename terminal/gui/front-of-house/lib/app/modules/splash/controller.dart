import 'dart:io';

import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/cash_drawer.service.dart';
import 'package:desktop/app/data/services/config.service.dart';
import 'package:desktop/app/data/services/connectivity.service.dart';
import 'package:desktop/app/data/services/customer.service.dart';
import 'package:desktop/app/data/services/department.service.dart';
import 'package:desktop/app/data/services/discount.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/item.service.dart';
import 'package:desktop/app/data/services/liquor_control.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/pole_display.service.dart';
import 'package:desktop/app/data/services/printer.service.dart';
import 'package:desktop/app/data/services/register.rpc.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:desktop/app/data/services/scale.service.dart';
import 'package:desktop/app/data/services/scanner.service.dart';
import 'package:desktop/app/data/services/system.service.dart';
import 'package:desktop/app/routes/app_pages.dart';
import 'package:desktop/main.dart';
import 'package:desktop_window/desktop_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('SplashController');

class SplashController extends GetxController {
  final RxBool isInitializing = true.obs;

  final Rx<NetworkInformation> networkInformation = NetworkInformation().obs;
  final Rx<SoftwareVersionInformation> softwareVersionInformation = SoftwareVersionInformation().obs;

  final String balenaDeviceUUID = Platform.environment["BALENA_DEVICE_UUID"] ?? "N/A";

  @override
  Future<void> onInit() async {
    if (fromBackOfHouse) isInitializing.value = false;

    if (kReleaseMode) {
      await DesktopWindow.setFullScreen(true);
    } else {
      await DesktopWindow.setWindowSize(const Size(1024, 768));
    }

    await initServices();

    Get.offAllNamed(AppRoutes.SIGN_ON);

    super.onInit();
  }

  Future<void> initServices() async {
    bool initSuccess = false;

    int logCounter = 0;
    while (!initSuccess) {
      try {
        logCounter++;

        try {
          networkInformation.value = await Get.find<SystemService>().loadNetworkInformation();
          softwareVersionInformation.value = await Get.find<SystemService>().loadSoftwareVersion();
        } catch (e) {
          throw Exception("failed to load network information or software version information");
        }

        Get.lazyPut(() => ScannerService());

        Get.lazyPut(() => LiquorControlService());

        Get.lazyPut(() => RegisterRpcService());

        Get.lazyPut(() => NotificationService());

        Get.lazyPut(() => SaleService());

        Get.lazyPut(() => DiscountService());

        Get.lazyPut(() => CashDrawerService());

        Get.lazyPut(() => ScaleService());

        Get.lazyPut(() => ItemService());

        Get.lazyPut(() => PoleDisplayService());

        Get.lazyPut(() => DepartmentService());

        Get.lazyPut(() => ActivityService());

        Get.lazyPut(() => CustomerService());

        Get.lazyPut(() => ReportService());

        Get.lazyPut(() => EmployeeService());

        try {
          final IdentityService identityService = await IdentityService().init();
          Get.put(identityService, permanent: true);
        } catch (e) {
          throw Exception("failed to initialize identity service $e");
        }

        try {
          final ConfigService configService = await ConfigService().init();
          Get.put(configService, permanent: true);
        } catch (e) {
          throw Exception("failed to initialize config service $e");
        }

        try {
          final PrinterService printerService = await PrinterService().init();
          Get.put(printerService, permanent: true);
        } catch (e) {
          throw Exception("failed to initialize printer service $e");
        }

        try {
          final ConnectivityService connectivityService = await ConnectivityService().init();
          Get.put(connectivityService, permanent: true);
        } catch (e) {
          throw Exception("failed to initialize connectivity service $e");
        }

        if (!fromBackOfHouse) {
          // this artifical delay will be here so it does jump to fast
          await Future<void>.delayed(const Duration(seconds: 1));

          isInitializing.value = false;

          await Future<void>.delayed(const Duration(seconds: 1));
        }

        initSuccess = true;
      } catch (err) {
        if (logCounter > 5) {
          _logger.severe("failed to initialize, going to retry", err);
          logCounter = 0;
        }

        await Future<void>.delayed(const Duration(seconds: 1));
      }
    }
  }
}
