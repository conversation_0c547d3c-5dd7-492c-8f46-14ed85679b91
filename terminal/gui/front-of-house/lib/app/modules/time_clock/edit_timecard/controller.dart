// ignore_for_file: avoid_dynamic_calls
import 'dart:async';

import 'package:backoffice/app/data/view_models/time_card_punch.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/pay_rate.dart';
import 'package:desktop/app/data/models/timecard.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report_engine.service.dart';
import 'package:desktop/app/modules/time_clock/controller.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// ignore: depend_on_referenced_packages
import 'package:gql/language.dart' as g;
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

final Logger _logger = Logger('ConfigService');

const String DELETE_PUNCH_MUTATION = '''
      mutation DELETE_TIMECARD(\$emp_id: smallint = "", \$punch_type: smallint = "", \$punch_at: timestamptz = "") {
        delete_timecard(where: {emp_id: {_eq: \$emp_id}, punch_type: {_eq: \$punch_type}, punch_at: {_eq: \$punch_at}}) {
          returning {
            round_at
            punch_at
            job_code
            break_idx
            emp_id
            punch_type
          }
        }
      }
    ''';
const String UPDATE_PUNCH_MUTATION = '''
      mutation UPDATE_TIMECARD(\$data: timecard_set_input = {}, \$emp_id: smallint = "", \$punch_type: smallint = "", \$punch_at: timestamptz = "") {
        update_timecard( _set: \$data , where: {emp_id: {_eq: \$emp_id}, punch_type: {_eq: \$punch_type}, punch_at: {_eq: \$punch_at}}) {
          returning {
            round_at
            punch_at
            job_code
            break_idx
            emp_id
            punch_type
            timecard
          }
        }
      }
    ''';

enum TimeCardEditType {
  ADD_PUNCH,
  EDIT_PUNCH,
  ADD_MISSING,
  ADD_BREAK,
  EDIT_BREAK,
}

class PunchSelection {
  PunchSelection({
    this.startDate,
    this.endDate,
    this.index,
    this.punchType,
    this.jobCode,
  });

  String? startDate;
  String? endDate;
  int? index;
  int? punchType;
  int? jobCode;
}

class EditTimeCardController extends GetxController with GetSingleTickerProviderStateMixin {
  EditTimeCardController();

  final GraphqlService _graphqlService = Get.find<GraphqlService>();
  final EmployeeService _employeeService = Get.find<EmployeeService>();
  final ReportEngineService _reportEngineService = Get.find();
  final NotificationService _notificationService = Get.find();

  final TimeCardController _timeCardController = Get.find();

  final Rx<PunchSelection> selectedPunch = PunchSelection().obs;
  final RxString employeeSearchPrompt = "".obs;
  final Rx<Employee> selectedEmployee = Employee.empty().obs;
  final RxList<TimeCardPunch> punchList = List<TimeCardPunch>.empty().obs;
  final RxBool editFiters = false.obs;
  final RxBool leastRecentFirst = true.obs;
  final RxBool isCurrentPayPeriod = true.obs;
  final RxBool reverseEmployeeOrder = false.obs;
  final RxBool onlyActiveUsers = false.obs;
  final RxBool editButtonDisabled = false.obs;
  final RxInt punchTypeController = 1.obs;
  ///////////////////////////////////////
  final Rx<DateTime> dateController = DateTime.now().obs;
  final RxBool pmTime = false.obs;
  final RxInt hourController = 12.obs;
  final RxInt minuteController = 0.obs;
  final RxInt secondController = 0.obs;
  ///////////////////////////////////////
  final Rx<DateTime> brkEndDateController = DateTime.now().obs;
  final RxBool brkEndPmTime = false.obs;
  final RxInt brkEndHrController = 12.obs;
  final RxInt brkEndMinController = 0.obs;
  final RxInt brkEndSecController = 0.obs;
  ///////////////////////////////////////
  final Rx<int> jobController = 1.obs;
  final Rx<int> breakController = 1.obs;
  final Rx<int> filterJob = (-1).obs;
  final RxList<Break> jobCodeBreaks = <Break>[].obs;
  final RxList<Employee> employeeList = List<Employee>.empty().obs;
  final RxList<SystemSettingJsonRecordJobCode> employeeJobCodeList = <SystemSettingJsonRecordJobCode>[].obs;
  final Rx<TimeCardEditType> editType = TimeCardEditType.ADD_PUNCH.obs;
  late PickerDateRange currentPayPeriod;

  // This variable hold the state of the "Only Missing Punch-Outs" checkbox.
  // When true, the punch list will only show missing punch-outs.
  final RxBool onlyMissingPunchOuts = false.obs;

  final TextEditingController searchEmployeesController = TextEditingController();
  final DateRangePickerController dateRangeController = DateRangePickerController();
  late TabController tabController;

  DateTime dateStartLimit = DateTime.now();
  DateTime dateEndLimit = DateTime.now();

  List<SystemSettingJsonRecordJobCode> jobCodeFilterList = <SystemSettingJsonRecordJobCode>[
    SystemSettingJsonRecordJobCode(
      index: -1,
      title: "Show All Job Codes",
      revCtr: 0,
      isActive: true,
      laborGroup: 0,
      isDeliveryDriver: false,
      promptForSeat: false,
      section: 0,
    ),
  ];

  @override
  Future<void> onInit() async {
    tabController = TabController(length: 2, vsync: this);
    jobCodeFilterList = <SystemSettingJsonRecordJobCode>[
      ...jobCodeFilterList,
      ..._timeCardController.jobCodeRecord,
    ];
    currentPayPeriod = PickerDateRange(
      DateTime.parse(
        _timeCardController.currentPayPeriod["startDate"] as String,
      ),
      DateTime.parse(
        _timeCardController.currentPayPeriod["endDate"] as String,
      ),
    );
    dateRangeController.selectedRange = currentPayPeriod;
    await getEmployees();

    super.onInit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void changeSelectedDateRange(
    DateRangePickerSelectionChangedArgs value,
  ) {
    dateRangeController.selectedRange = value.value as PickerDateRange;
    isCurrentPayPeriod.value = false;
    if (selectedEmployee.value.id != null) getPunches();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void resetDateRange() {
    dateRangeController.selectedRange = currentPayPeriod;
    isCurrentPayPeriod.value = true;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onTapEditSelected() async {
    if (!getMatchingJobCode()) return;
    editType.value = TimeCardEditType.EDIT_PUNCH;
    dateController.value = DateTime.parse(
      getSelectedPunchDate(selectedPunch.value),
    );
    updateTimeControllers();
    punchTypeController.value = selectedPunch.value.punchType ?? 1;
    jobController.value = selectedPunch.value.jobCode ?? employeeJobCodeList[0].index;
    tabController.animateTo(1);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onTapAddPunch() async {
    if (employeeJobCodeList.isEmpty) {
      _notificationService.error("No job codes for employee");
      return;
    }
    editType.value = TimeCardEditType.ADD_PUNCH;
    dateController.value = DateTime.now();
    updateTimeControllers();
    punchTypeController.value = 1;
    jobController.value = employeeJobCodeList[0].index;
    tabController.animateTo(1);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onTapAddBreak() async {
    if (employeeJobCodeList.isEmpty) {
      _notificationService.error("No job codes for employee");
      return;
    }
    final PunchSelection punch = selectedPunch.value;
    dateController.value = DateTime.parse(punch.startDate!).add(const Duration(seconds: 1));
    dateStartLimit = DateTime.parse(punch.startDate!);
    if ((punch.endDate ?? "") == "") {
      final int brkMins = _timeCardController.breakList.isEmpty ? 30 : _timeCardController.breakList.first.breakMins;
      brkEndDateController.value = dateController.value.add(Duration(minutes: brkMins));
      if (punch.index! > punchList.length - 2) {
        dateEndLimit = DateTime.now();
      } else {
        final TimeCardPunch nextPunch = punchList[punch.index! + 1];
        dateEndLimit = DateTime.parse(nextPunch.punchIn);
      }
    } else {
      brkEndDateController.value = DateTime.parse(punch.endDate!).subtract(const Duration(seconds: 1));
      dateEndLimit = DateTime.parse(punch.endDate!);
    }
    editType.value = TimeCardEditType.ADD_BREAK;
    updateTimeControllers();
    updateBreakControllers();
    tabController.animateTo(1);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onTapEditMissing() async {
    await getDateLimits();
    final int overEightHourDiff = dateEndLimit.difference(dateStartLimit).inHours;
    DateTime dateToUse = dateStartLimit.add(const Duration(minutes: 1));
    if (selectedPunch.value.punchType == 1) {
      if (overEightHourDiff > 8) {
        dateToUse = dateEndLimit.subtract(const Duration(hours: 8));
      }
    } else {
      if (overEightHourDiff > 8) {
        dateToUse = dateStartLimit.add(const Duration(hours: 8));
      } else {
        dateToUse = dateEndLimit.subtract(const Duration(minutes: 1));
      }
    }
    if (!getMatchingJobCode()) return;
    editType.value = TimeCardEditType.ADD_MISSING;
    dateController.value = dateToUse;
    updateTimeControllers();
    punchTypeController.value = selectedPunch.value.punchType ?? 1;
    jobController.value = selectedPunch.value.jobCode ?? employeeJobCodeList[0].index;
    tabController.animateTo(1);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onTapEditBreak() async {
    if (!getMatchingJobCode()) return;
    TimeCardPunch brkCard = TimeCardPunch();
    final PunchSelection punch = selectedPunch.value;
    if (punch.punchType == 3) {
      dateController.value = DateTime.parse(punch.endDate!);
      dateStartLimit = DateTime.parse(punch.startDate!);
      brkCard = punchList[punch.index! + 1];
      brkEndDateController.value = DateTime.parse(brkCard.punchOut);
      if (punchList[punch.index! + 2].punchOut == "") {
        if (punch.index! + 3 >= punchList.length) {
          dateEndLimit = DateTime.now();
        } else {
          dateEndLimit = DateTime.parse(punchList[punch.index! + 3].punchIn);
        }
      } else {
        dateEndLimit = DateTime.parse(punchList[punch.index! + 2].punchOut);
      }
    }
    if (selectedPunch.value.punchType == 4) {
      brkEndDateController.value = DateTime.parse(punch.startDate!);
      if ((punch.endDate ?? "") == "") {
        if (punch.index! + 1 >= punchList.length) {
          dateEndLimit = DateTime.now();
        } else {
          dateEndLimit = DateTime.parse(punchList[punch.index! + 1].punchIn);
        }
      } else {
        dateEndLimit = DateTime.parse(punch.endDate!);
      }
      brkCard = punchList[punch.index! - 1];
      dateController.value = DateTime.parse(brkCard.punchIn);
      dateStartLimit = DateTime.parse(punchList[punch.index! - 2].punchIn);
    }
    editType.value = TimeCardEditType.EDIT_BREAK;
    updateBreakControllers(brkCard: brkCard);
    updateTimeControllers();
    tabController.animateTo(1);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void updateBreakControllers({TimeCardPunch? brkCard}) {
    final int currentJob = brkCard == null ? selectedPunch.value.jobCode! : brkCard.jobCode!;
    final SystemSettingJsonRecordJobCode punchJob = _timeCardController.jobCodeRecord.firstWhere(
      (SystemSettingJsonRecordJobCode j) => j.index == currentJob,
    );
    jobCodeBreaks.value = _timeCardController.breakList
        .where(
          (Break b) => (punchJob.breaks & (1 << b.idx)) == (1 << b.idx),
        )
        .toList();
    if (jobCodeBreaks.isEmpty) {
      _notificationService.error("No break types found for jobCode");
      return;
    }
    if (brkCard == null || jobCodeBreaks.firstWhereOrNull((Break b) => b.idx == brkCard.idx) == null) {
      breakController.value = jobCodeBreaks.first.idx;
    } else {
      breakController.value = brkCard.breakIdx!;
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getDateLimits() async {
    int idx = selectedPunch.value.index ?? 0;
    int payPeriod = 0;
    List<TimeCardPunch> listToUse = punchList;
    final String pairedDateString = selectedPunch.value.punchType == 1 ? punchList[idx].punchOut : punchList[idx].punchIn;
    DateTime periodStart = DateTime.parse(
      _timeCardController.currentPayPeriod["startDate"] as String,
    );
    DateTime periodEnd = DateTime.parse(
      _timeCardController.currentPayPeriod["endDate"] as String,
    );
    final DateTime pairedDate = DateTime.parse(
      "${pairedDateString.substring(0, 10)} ${pairedDateString.substring(11, 19)}.000",
    );

    // if missing punch is first or last in array
    // get pay period and search for next and last punches
    if (idx < 1 || idx > listToUse.length - 2) {
      // if not in current pay period find which one punch is in
      if (periodStart.isAfter(pairedDate)) {
        final Duration difference = periodEnd.difference(pairedDate);
        payPeriod = 0 - (difference.inDays / 7).floor();
      }

      // get new array of punches from pay period
      final Map<String, dynamic> report = await _reportEngineService.request(<String, int?>{
        "reportID": 2,
        "empID": selectedEmployee.value.id,
        "payPeriod": payPeriod,
      });

      periodStart = DateTime.parse(
        report["startDate"] as String,
      );
      periodEnd = DateTime.parse(
        report["endDate"] as String,
      );

      listToUse = ((report["${selectedEmployee.value.id}"]["punches"] ?? <dynamic>[]) as List<dynamic>)
          .map((dynamic e) => TimeCardPunch.fromDynamic(e))
          .toList();

      //
      // Match selected missing punch in new array
      for (int i = 0; i < listToUse.length; i++) {
        final TimeCardPunch val = listToUse[i];
        if (selectedPunch.value.punchType == 1) {
          if (val.punchIn == "" && val.punchOut == pairedDateString) {
            idx = i;
            i = listToUse.length;
          }
        } else {
          if (val.punchOut == "" && val.punchIn == pairedDateString) {
            idx = i;
            i = listToUse.length;
          }
        }
      }
    }

    //
    // get start limit
    if (selectedPunch.value.punchType == 2) {
      dateStartLimit = pairedDate;
    } else if (idx < 1) {
      final Map<String, dynamic> previousReport = await _reportEngineService.request(<String, int?>{
        "reportID": 2,
        "empID": selectedEmployee.value.id,
        "payPeriod": payPeriod - 1,
      });
      final List<TimeCardPunch> previousList = ((previousReport["${selectedEmployee.value.id}"]["punches"] ?? <dynamic>[]) as List<dynamic>)
          .map((dynamic e) => TimeCardPunch.fromDynamic(e))
          .toList();
      if (previousList.isEmpty) {
        dateStartLimit = DateTime.parse(previousReport["startDate"] as String);
      } else {
        if (listToUse[idx].punchOut == previousList.last.punchOut && previousList.last.punchIn == "") {
          if (previousList.length > 1) {
            dateStartLimit = DateTime.parse(
              previousList[previousList.length - 2].punchOut,
            );
          } else {
            dateStartLimit = DateTime.parse(previousReport["startDate"] as String);
          }
        } else {
          dateStartLimit = DateTime.parse(previousList.last.punchOut);
        }
      }
    } else {
      final TimeCardPunch previousRow = listToUse[idx - 1];
      dateStartLimit = DateTime.parse(previousRow.punchOut);
    }

    //
    // get end limit
    if (selectedPunch.value.punchType == 1) {
      dateEndLimit = pairedDate;
    } else if (idx > (listToUse.length - 2)) {
      if (payPeriod < 0) {
        final Map<String, dynamic> nextReport = await _reportEngineService.request(<String, int?>{
          "reportID": 2,
          "empID": selectedEmployee.value.id,
          "payPeriod": payPeriod - 1,
        });
        final List<dynamic> nextList = (nextReport["${selectedEmployee.value.id}"]["punches"] ?? <dynamic>[]) as List<dynamic>;
        if (nextList.isEmpty) {
          dateEndLimit = payPeriod < -1 ? DateTime.parse(nextReport["endDate"] as String) : DateTime.now().add(const Duration(minutes: 1));
        } else {
          dateEndLimit = DateTime.parse(nextList.last["in"] as String);
        }
      } else {
        dateEndLimit = DateTime.now().add(const Duration(minutes: 1));
      }
    } else {
      final TimeCardPunch nextRow = listToUse[idx + 1];
      dateEndLimit = DateTime.parse(nextRow.punchIn);
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void updateTimeControllers() {
    _updateTimeHandler(
      dateController.value,
      pmTime,
      hourController,
      minuteController,
      secondController,
    );
    if (editType.value == TimeCardEditType.EDIT_BREAK || editType.value == TimeCardEditType.ADD_BREAK) {
      _updateTimeHandler(
        brkEndDateController.value,
        brkEndPmTime,
        brkEndHrController,
        brkEndMinController,
        brkEndSecController,
      );
    }
  }

  void _updateTimeHandler(
    DateTime date,
    RxBool pm,
    RxInt hour,
    RxInt minute,
    RxInt second,
  ) {
    pm.value = date.hour > 11;
    if (date.hour != 0 && date.hour != 12) {
      hour.value = pm.value ? date.hour - 12 : date.hour;
    } else {
      hour.value = 12;
    }
    minute.value = date.minute;
    second.value = date.second;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> onSubmit() async {
    tabController.animateTo(0);
    selectedPunch.value = PunchSelection();
    await getPunches();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  String getCurrentRangeString() {
    String res = DateFormat('MM/dd/yyyy').format(dateRangeController.selectedRange?.startDate ?? DateTime.now());
    if (dateRangeController.selectedRange?.endDate != null) {
      res += " - ${DateFormat('MM/dd/yyyy').format(dateRangeController.selectedRange?.endDate ?? DateTime.now())}";
    }
    return res;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getEmployees() async {
    // ignore: non_constant_identifier_names
    final String GET_EMPLOYEE_QUERY = '''
      query GET_EMPLOYEE {
        employee(order_by: {employee_full_name: ${reverseEmployeeOrder.value ? "desc" : "asc"}}, 
          where: {is_active: {${onlyActiveUsers.value ? "_eq: true" : ""}}, 
          employee_full_name: {${employeeSearchPrompt.value != "" ? '_ilike: "%${employeeSearchPrompt.value}%"' : ""}}}) 
          {
            document
            created_by
            created_at
            updated_at
            updated_by
            password
            is_active
            id
            employee_class
            employee_full_name
            employee
          }
        }
      ''';

    try {
      final QueryResult<Object?> employeeResult = await _graphqlService.client.query(
        QueryOptions<Object?>(
          document: g.parseString(GET_EMPLOYEE_QUERY),
          fetchPolicy: FetchPolicy.noCache,
        ),
      );

      if (employeeResult.hasException) {
        throw employeeResult.exception.toString();
      }

      employeeList.value = (employeeResult.data!['employee']! as List<dynamic>)
          .map(
            (dynamic employee) => Employee.fromJson(
              employee as Map<String, dynamic>,
            ),
          )
          .toList();
    } catch (err, stack) {
      _logger.severe(
        "Error fetching employees",
        err,
        stack,
      );
      _notificationService.error("Error getting employees!");
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getMissingPunchOuts() async {
    // Clear the employee list to start fresh
    employeeList.clear();

    try {
      employeeList.value = await _employeeService.getClockedInEmployees(
        start: dateRangeController.selectedRange?.startDate,
        end: dateRangeController.selectedRange?.endDate,
      );
    } catch (err, stack) {
      _logger.severe('Error fetching timecards with missing punch-outs', err, stack);
      _notificationService.error('Error getting employees!');
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> getPunches() async {
    TimeCardPunch recentPunch = TimeCardPunch();

// ignore: non_constant_identifier_names
    final String GET_RECENT_TIMECARD = '''
      query GET_RECENT_TIMECARD {
        timecard(order_by: {punch_at: desc}, where: {emp_id: {_eq: "${selectedEmployee.value.id}"}}, limit: 1) {
          timecard
          round_at
          punch_type
          punch_at
          job_code
          emp_id
          break_idx
        }
      }
      ''';

    try {
      final QueryResult<Object?> cardQueryRes = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(GET_RECENT_TIMECARD),
        ),
      );

      if (cardQueryRes.hasException) {
        throw cardQueryRes.exception.toString();
      }

      final List<dynamic> cardQueryList = cardQueryRes.data?["timecard"] as List<dynamic>;

      if (cardQueryList.isNotEmpty) {
        final TimeCard recentTimeCard = TimeCard.fromJson(cardQueryList[0] as Map<String, dynamic>);
        recentPunch = TimeCardPunch(
          punchIn: recentTimeCard.punch_type == 1 ? recentTimeCard.punch_at ?? "" : "",
          punchOut: recentTimeCard.punch_type == 2 ? recentTimeCard.punch_at ?? "" : "",
          jobCode: recentTimeCard.job_code,
        );
      }

      final DateTime startDate = DateTime.parse(Helpers.getReportStartString(dateRangeController)).toLocal();
      final DateTime endDate = DateTime.parse(Helpers.getReportEndString(dateRangeController)).toLocal();

      final Map<String, dynamic> report = await _reportEngineService.request(<String, Object?>{
        "reportID": 1,
        "empID": selectedEmployee.value.id,
        "startDate": startDate.toString(),
        "endDate": endDate.toString(),
      });
      if (report.isEmpty || report["${selectedEmployee.value.id}"] == null) {
        punchList.value = <TimeCardPunch>[];
        return;
      }

      final List<TimeCardPunch> newList =
          (report["${selectedEmployee.value.id}"]["punches"] as List<dynamic>).map((dynamic e) => TimeCardPunch.fromDynamic(e)).toList();

      if (recentPunch.punchIn != "") {
        final DateTime inDate = DateTime.parse(recentPunch.punchIn);
        if (inDate.isAfter(startDate) && inDate.isBefore(endDate)) {
          newList.add(recentPunch);
        }
      }

      if (newList.isNotEmpty) {
        final TimeCardPunch lastPunch = newList.last;
        if (lastPunch.isBreak) {
          final TimeCardPunch newLastPunch = TimeCardPunch(
            punchIn: lastPunch.punchOut,
            jobCode: lastPunch.jobCode,
            idx: (lastPunch.idx ?? 0) + 1,
          );
          newList.add(newLastPunch);
        }
        if (newList.first.punchIn == "") {
          await _lookForMissingPunchIn(newList);
        }
        if (newList.last.punchOut == "") {
          await _lookForMissingPunchOut(newList);
        }
      }

      for (int i = 0; i < newList.length; i++) {
        newList[i].idx = i;
      }

      punchList.value = newList;
    } catch (err, stack) {
      _logger.severe(
        "Error adding punch",
        err,
        stack,
      );
    }
  }

  Future<void> _lookForMissingPunchIn(List<TimeCardPunch> punches) async {
    if (dateRangeController.selectedRange?.startDate == null) return;
    final TimeCardPunch first = punches.first;
    final Map<String, dynamic> report = await _reportEngineService.request(<String, Object?>{
      "reportID": 1,
      "empID": selectedEmployee.value.id,
      "startDate": dateRangeController.selectedRange?.startDate!.subtract(const Duration(days: 7)).toString(),
      "endDate": dateRangeController.selectedRange?.startDate.toString(),
    });
    if (report.isEmpty || report["${selectedEmployee.value.id}"] == null) {
      return;
    }
    final List<TimeCardPunch> reportList =
        (report["${selectedEmployee.value.id}"]["punches"] as List<dynamic>).map((dynamic e) => TimeCardPunch.fromDynamic(e)).toList();
    if (reportList.isEmpty) return;
    bool breakAdded = false;
    for (int i = reportList.length - 1; i >= 0; i--) {
      if (reportList[i].isBreak) {
        if (!breakAdded) {
          first.punchIn = reportList[i].punchOut;
          breakAdded = true;
        }
        punches.insert(0, reportList[i]);
        continue;
      }
      if (reportList[i].punchOut != "") {
        if (breakAdded) {
          punches.insert(0, reportList[i]);
          first.jobCode = reportList[i].jobCode;
        }
        break;
      }
      if (reportList[i].punchIn != "") {
        if (breakAdded) {
          punches.insert(0, reportList[i]);
        } else {
          first.punchIn = reportList[i].punchIn;
        }
        first.jobCode = reportList[i].jobCode;
        break;
      }
    }
  }

  Future<void> _lookForMissingPunchOut(List<TimeCardPunch> punches) async {
    if (dateRangeController.selectedRange?.startDate == null) return;
    final TimeCardPunch last = punches.last;
    final Map<String, dynamic> report = await _reportEngineService.request(<String, Object?>{
      "reportID": 1,
      "empID": selectedEmployee.value.id,
      "startDate":
          (dateRangeController.selectedRange?.endDate ?? dateRangeController.selectedRange?.startDate)!.add(const Duration(days: 1)).toString(),
      "endDate":
          (dateRangeController.selectedRange?.endDate ?? dateRangeController.selectedRange?.startDate)!.add(const Duration(days: 8)).toString(),
    });
    if (report.isEmpty || report["${selectedEmployee.value.id}"] == null) {
      return;
    }
    final List<TimeCardPunch> reportList =
        (report["${selectedEmployee.value.id}"]["punches"] as List<dynamic>).map((dynamic e) => TimeCardPunch.fromDynamic(e)).toList();
    if (reportList.isEmpty) return;
    bool breakAdded = false;
    for (int i = 0; i < reportList.length; i++) {
      if (reportList[i].isBreak) {
        if (!breakAdded) {
          last.punchOut = reportList[i].punchIn;
          breakAdded = true;
        }
        punches.add(reportList[i]);
        continue;
      }
      if (reportList[i].punchIn != "") {
        if (breakAdded) {
          punches.add(reportList[i]);
        }
        break;
      }
      if (reportList[i].punchOut != "") {
        if (breakAdded) {
          punches.add(reportList[i]);
        } else {
          last.punchOut = reportList[i].punchOut;
        }
        break;
      }
    }
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addPunch() async {
    try {
      final QueryResult<Object?> addPunchResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(_timeCardController.INSERT_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object?>{
              "punch_at": getDateStringFromInputs(),
              "job_code": jobController.value,
              "break_idx": 0,
              "emp_id": selectedEmployee.value.id,
              "punch_type": punchTypeController.value,
            },
          },
        ),
      );

      if (addPunchResult.hasException) {
        throw addPunchResult.exception.toString();
      }

      _notificationService.success("Punch added!");
    } catch (err, stack) {
      _logger.severe(
        "Error adding punch",
        err,
        stack,
      );
      _notificationService.error("Error adding punch!");
    }
    onSubmit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updatePunch() async {
    try {
      final QueryResult<Object?> updatePunchResult = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPDATE_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object>{
              "punch_at": getDateStringFromInputs(),
              "job_code": jobController.value,
              "punch_type": punchTypeController.value,
            },
            "emp_id": selectedEmployee.value.id,
            "punch_type": selectedPunch.value.punchType,
            "punch_at": getSelectedPunchDate(selectedPunch.value),
          },
        ),
      );
      if (updatePunchResult.hasException) {
        throw updatePunchResult.exception.toString();
      }
      if (updatePunchResult.data != null) {
        if ((updatePunchResult.data!["update_timecard"]["returning"] as List<dynamic>).isEmpty) {
          _notificationService.error("Punch(es) missing. Try widening date range.");
          return;
        }
      }
      if (jobController.value != selectedPunch.value.jobCode) {
        final QueryResult<Object?> jobUpdateResult = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(UPDATE_PUNCH_MUTATION),
            variables: <String, dynamic>{
              "data": <String, Object>{
                "job_code": jobController.value,
              },
              "emp_id": selectedEmployee.value.id,
              "punch_type": selectedPunch.value.punchType == 1 ? 2 : 1,
              "punch_at": selectedPunch.value.punchType == 1 ? selectedPunch.value.endDate : selectedPunch.value.startDate,
            },
          ),
        );
        if (jobUpdateResult.hasException) {
          throw jobUpdateResult.exception.toString();
        }
      }
      _notificationService.success("Punch updated!");
    } catch (err, stack) {
      _logger.severe(
        "Error updating punch",
        err,
        stack,
      );
      _notificationService.error("Error updating punch!");
    }
    onSubmit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> updateBreak() async {
    try {
      final PunchSelection punch = selectedPunch.value;
      if (punch.index == null) throw "No index on punch selection.";
      final int brkPunchIdx = punch.punchType == 3 ? punch.index! + 1 : punch.index! - 1;
      final TimeCardPunch brkPunch = punchList[brkPunchIdx];
      if (!brkPunch.isBreak) {
        throw "Attempted to update break but the punch found is not a break.";
      }
      final QueryResult<Object?> updatePunchRes1 = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPDATE_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object>{
              "punch_at": getDateStringFromInputs(),
              "break_idx": breakController.value,
            },
            "emp_id": selectedEmployee.value.id,
            "punch_type": 3,
            "punch_at": brkPunch.punchIn,
          },
        ),
      );
      if (updatePunchRes1.hasException) {
        throw updatePunchRes1.exception.toString();
      }
      final QueryResult<Object?> updatePunchRes2 = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(UPDATE_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object>{
              "punch_at": getDateStringFromInputs(breakEnd: true),
              "break_idx": breakController.value,
            },
            "emp_id": selectedEmployee.value.id,
            "punch_type": 4,
            "punch_at": brkPunch.punchOut,
          },
        ),
      );
      if (updatePunchRes2.hasException) {
        throw updatePunchRes2.exception.toString();
      }
      _notificationService.success("Break updated!");
    } catch (err, stack) {
      _logger.severe(
        "Error updating break",
        err,
        stack,
      );
      _notificationService.error("Error updating break!");
    }
    onSubmit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> addBreak() async {
    try {
      final PunchSelection punch = selectedPunch.value;
      final QueryResult<Object?> addPunchRes1 = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(_timeCardController.INSERT_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object?>{
              "punch_at": getDateStringFromInputs(),
              "job_code": punch.jobCode,
              "break_idx": breakController.value,
              "emp_id": selectedEmployee.value.id,
              "punch_type": 3,
            },
          },
        ),
      );
      if (addPunchRes1.hasException) {
        throw addPunchRes1.exception.toString();
      }
      final QueryResult<Object?> addPunchRes2 = await _graphqlService.client.mutate(
        MutationOptions<Object?>(
          document: g.parseString(_timeCardController.INSERT_PUNCH_MUTATION),
          variables: <String, dynamic>{
            "data": <String, Object?>{
              "punch_at": getDateStringFromInputs(breakEnd: true),
              "job_code": punch.jobCode,
              "break_idx": breakController.value,
              "emp_id": selectedEmployee.value.id,
              "punch_type": 4,
            },
          },
        ),
      );
      if (addPunchRes2.hasException) {
        throw addPunchRes2.exception.toString();
      }
      _notificationService.success("Break Added!");
    } catch (err, stack) {
      _logger.severe(
        "Error adding break",
        err,
        stack,
      );
      _notificationService.error("Error adding break!");
    }
    onSubmit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  Future<void> deletePunch() async {
    final String typeStr = (selectedPunch.value.punchType ?? 1) < 3 ? "punch" : "break";
    try {
      final PunchSelection punch = selectedPunch.value;
      if (punch.punchType == null) throw "No type on punch selection.";
      if (punch.punchType! < 3) {
        final QueryResult<Object?> deletePunchResult = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(DELETE_PUNCH_MUTATION),
            variables: <String, dynamic>{
              "emp_id": selectedEmployee.value.id,
              "punch_type": punch.punchType,
              "punch_at": getSelectedPunchDate(punch),
            },
          ),
        );
        if (deletePunchResult.hasException) {
          throw deletePunchResult.exception.toString();
        }
      } else {
        if (punch.index == null) throw "No index on punch selection.";
        final int brkPunchIdx = punch.punchType == 3 ? punch.index! + 1 : punch.index! - 1;
        final TimeCardPunch brkPunch = punchList[brkPunchIdx];
        if (!brkPunch.isBreak) {
          throw "Attempted to delete break but the punch found is not a break.";
        }
        final QueryResult<Object?> deletePunchRes1 = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(DELETE_PUNCH_MUTATION),
            variables: <String, dynamic>{
              "emp_id": selectedEmployee.value.id,
              "punch_type": 3,
              "punch_at": brkPunch.punchIn,
            },
          ),
        );
        if (deletePunchRes1.hasException) {
          throw deletePunchRes1.exception.toString();
        }
        final QueryResult<Object?> deletePunchRes2 = await _graphqlService.client.mutate(
          MutationOptions<Object?>(
            document: g.parseString(DELETE_PUNCH_MUTATION),
            variables: <String, dynamic>{
              "emp_id": selectedEmployee.value.id,
              "punch_type": 4,
              "punch_at": brkPunch.punchOut,
            },
          ),
        );
        if (deletePunchRes2.hasException) {
          throw deletePunchRes2.exception.toString();
        }
      }
      _notificationService.success(
        "Deleted $typeStr!",
      );
    } catch (err, stack) {
      _logger.severe(
        "Error deleting $typeStr",
        err,
        stack,
      );
      _notificationService.error("Error deleting $typeStr!");
    }
    onSubmit();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  String formatDateSlashes(String? date) {
    if ((date ?? "") == "") return "N/A";
    return DateFormat('MM/dd/yy hh:mm a').format(DateTime.parse(date!));
  }

  ///
  ///
  ///
  ///
  ///
  ///
  SystemSettingJsonRecordJobCode? getJobRecordFromIdx(int jobIdx) {
    return _timeCardController.getJobRecordfromIdx(jobIdx);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  String getSelectedPunchDate(PunchSelection? punch) {
    if (punch == null) return "";
    return punch.punchType == 1 ? punch.startDate ?? "" : punch.endDate ?? "";
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void checkIfEditButtonDisabled() {
    if (selectedEmployee.value.employee == "") {
      editButtonDisabled.value = true;
      return;
    }
    final DateTime inputDate = DateTime.parse(getDateStringFromInputs(forQuery: false));
    if (editType.value == TimeCardEditType.ADD_MISSING) {
      if (inputDate.toLocal().isBefore(dateStartLimit) ||
          inputDate.toLocal().isAtSameMomentAs(dateStartLimit) ||
          inputDate.toLocal().isAfter(dateEndLimit) ||
          inputDate.toLocal().isAtSameMomentAs(dateEndLimit)) {
        editButtonDisabled.value = true;
        return;
      }
    } else if (editType.value == TimeCardEditType.ADD_BREAK || editType.value == TimeCardEditType.EDIT_BREAK) {
      if (jobCodeBreaks.isEmpty || _breakOverLimits(inputDate)) {
        editButtonDisabled.value = true;
        return;
      }
    }
    if (inputDate.isAfter(DateTime.now())) {
      editButtonDisabled.value = true;
      return;
    }
    editButtonDisabled.value = false;
  }

  bool _breakOverLimits(DateTime date1) {
    final Break punchBreak = _timeCardController.breakList.firstWhere((Break b) => b.idx == breakController.value);
    final DateTime date2 = DateTime.parse(
      getDateStringFromInputs(forQuery: false, breakEnd: true),
    );
    if (date1.toLocal().isBefore(
          dateStartLimit.add(Duration(minutes: punchBreak.minsToQualify)),
        )) return true;
    if (date1.toLocal().isAtSameMomentAs(
          dateStartLimit,
        )) return true;
    if (date2.isBefore(date1.add(Duration(minutes: punchBreak.breakMins)))) {
      return true;
    }
    if (date2.toLocal().isAfter(dateEndLimit)) return true;
    if (date2.toLocal().isAtSameMomentAs(dateEndLimit)) return true;
    if (date2.isAfter(DateTime.now())) return true;
    if (date2.difference(date1).inMinutes < punchBreak.breakMins) return true;
    return false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool checkIfCanEditBreak() {
    final PunchSelection punch1 = selectedPunch.value;
    if (punch1.index == null) return false;
    if (punch1.jobCode == null) return false;
    final int punch2Idx = punch1.punchType == 3 ? punch1.index! + 2 : punch1.index! - 2;
    if (punch2Idx < 0 || punch2Idx > punchList.length - 1) return false;
    final TimeCardPunch punch2Card = punchList[punch2Idx];
    final PunchSelection punch2 = PunchSelection(
      startDate: punch2Card.punchIn,
      endDate: punch2Card.punchOut,
      punchType: punch1.punchType == 3 ? 4 : 3,
      index: punch2Card.idx,
      jobCode: punch2Card.jobCode,
    );
    if (punch2.index == null) return false;
    if (punch2.jobCode == null) return false;
    return _checkBreakPunch(punch1) && _checkBreakPunch(punch2);
  }

  bool _checkBreakPunch(PunchSelection punch) {
    final int punchType = punch.punchType ?? 0;
    if (punchType == 3 && (punch.startDate ?? "") == "") return false;
    if (punchType == 4) {
      if (punchList[punch.index! - 2].punchIn == "") return false;
    }
    return true;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool checkIfAddBreakDisabled() {
    if (_timeCardController.breakList.isEmpty) return true;
    if (selectedPunch.value.index == null) return true;
    if (selectedPunch.value.jobCode == null) return true;
    if (selectedEmployee.value.employee == "") return true;
    if ((selectedPunch.value.punchType ?? 3) > 2) return true;
    if ((selectedPunch.value.startDate ?? "") == "") return true;
    final int checkCardIdx = selectedPunch.value.punchType == 1 ? selectedPunch.value.index! + 1 : selectedPunch.value.index! - 1;
    if (selectedPunch.value.punchType == 1) {
      if (checkCardIdx < 0) return true;
      if (checkCardIdx > punchList.length - 1) return false;
    } else {
      if (checkCardIdx < 0) return false;
    }
    if (checkCardIdx < 0 || checkCardIdx > punchList.length - 1) return true;
    return false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  String getDateStringFromInputs({
    bool forQuery = true,
    bool breakEnd = false,
  }) {
    final DateTime date = !breakEnd ? dateController.value : brkEndDateController.value;
    final int hour = !breakEnd ? hourController.value : brkEndHrController.value;
    final int minute = !breakEnd ? minuteController.value : brkEndMinController.value;
    final int second = !breakEnd ? secondController.value : brkEndSecController.value;
    final bool pm = !breakEnd ? pmTime.value : brkEndPmTime.value;
    String hrString = hour.toString();
    if (pm) {
      if (hour != 12) {
        hrString = "${hour + 12}";
      }
    } else if (hour == 12) {
      hrString = "00";
    }
    if (hrString.length < 2) {
      hrString = "0$hrString";
    }
    if (forQuery) {
      return "${date.toString().substring(0, 10)}T$hrString:${minute.toString().length < 2 ? "0" : ""}$minute:${second.toString().length < 2 ? "0" : ""}$second+00:00";
    }
    return "${date.toString().substring(0, 10)} $hrString:${minute.toString().length < 2 ? "0" : ""}$minute:${second.toString().length < 2 ? "0" : ""}$second.000";
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool checkIfPunchMissing() {
    final List<int> punchIns = <int>[1, 4];
    final List<int> punchOuts = <int>[2, 3];
    if (selectedPunch.value.punchType != null) {
      if ((punchIns.contains(selectedPunch.value.punchType) && (selectedPunch.value.startDate ?? "") == "") ||
          (punchOuts.contains(selectedPunch.value.punchType) && (selectedPunch.value.endDate ?? "") == "")) {
        return true;
      }
    }
    return false;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  bool getMatchingJobCode() {
    if (selectedPunch.value.jobCode != null) {
      final SystemSettingJsonRecordJobCode matchedCode = employeeJobCodeList.firstWhere(
        (SystemSettingJsonRecordJobCode e) => e.index == selectedPunch.value.jobCode,
        orElse: () => SystemSettingJsonRecordJobCode.empty(),
      );
      if (matchedCode.index < 1) {
        _notificationService.error("Job code on punch not found for employee");
        return false;
      }
    } else if (employeeJobCodeList.isEmpty) {
      _notificationService.error("No job codes for employee");
      return false;
    }
    return true;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  void getEmployeeJobCodes() {
    final List<int> employeeJobCodeIdxs = selectedEmployee.value.document.payRates.map((PayRate rate) => rate.jobCode).toList();

    employeeJobCodeList.value = _timeCardController.jobCodeRecord
        .where(
          (SystemSettingJsonRecordJobCode job) => employeeJobCodeIdxs.contains(job.index) && job.isActive,
        )
        .toList()
        .obs;
  }
}
