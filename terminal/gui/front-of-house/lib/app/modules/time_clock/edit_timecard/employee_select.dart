// ignore_for_file: avoid_dynamic_calls

import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/global_widgets/virtual_keyboard/widget.dart';
import 'package:desktop/app/modules/time_clock/edit_timecard/controller.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class EditTimeCardEmployeeSelect extends GetView<EditTimeCardController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.all(10),
        child: Container(
          constraints: const BoxConstraints.expand(),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: R2Colors.neutral200,
              width: 2,
            ),
            color: R2Colors.neutral100,
          ),
          child: Column(
            children: <Widget>[
              ColoredBox(
                color: R2Colors.neutral200,
                child: VirtualKeyboardWrapper(
                  isBOH: false,
                  textEditingController: controller.searchEmployeesController,
                  onConfirm: (String value) {
                    controller.employeeSearchPrompt.value = controller.searchEmployeesController.text;
                    controller.getEmployees();
                  },
                  child: TextField(
                    controller: controller.searchEmployeesController,
                    style: const TextStyle(
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      border: UnderlineInputBorder(),
                      hintText: '           Search',
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(6.0),
                        child: FaIcon(
                          FontAwesomeIcons.magnifyingGlass,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: controller.employeeList
                        .map(
                          (Employee employee) => Column(
                            children: <Widget>[
                              GestureDetector(
                                onTap: () async {
                                  controller.selectedPunch.value = PunchSelection();
                                  controller.selectedEmployee.value = employee;
                                  await controller.getPunches();
                                  controller.getEmployeeJobCodes();
                                },
                                child: ColoredBox(
                                  color: controller.selectedEmployee.value.id == employee.id ? R2Colors.primary300 : Colors.transparent,
                                  child: Padding(
                                    padding: const EdgeInsets.all(
                                      10.0,
                                    ),
                                    child: SizedBox(
                                      height: 20,
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: <Widget>[
                                          Text(
                                            "${employee.id} - ",
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: R2Colors.neutral500,
                                            ),
                                          ),
                                          Text(
                                            "${employee.document.firstName ?? ""} ${employee.document.lastName ?? ""}",
                                            style: const TextStyle(
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                height: 1,
                                color: R2Colors.neutral200,
                              ),
                            ],
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
              ColoredBox(
                color: R2Colors.neutral200,
                child: Padding(
                  padding: const EdgeInsets.all(5.0),
                  child: Column(
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          TextButton(
                            style: ButtonStyle(
                              backgroundColor: MaterialStatePropertyAll<Color>(
                                R2Colors.primary100,
                              ),
                              overlayColor: MaterialStatePropertyAll<Color>(
                                R2Colors.primary200,
                              ),
                            ),
                            onPressed: () async {
                              controller.reverseEmployeeOrder.value = !controller.reverseEmployeeOrder.value;
                              await controller.getEmployees();
                            },
                            child: Row(
                              children: <Widget>[
                                Text(
                                  controller.reverseEmployeeOrder.value ? "Z-A" : "A-Z",
                                  style: TextStyle(
                                    color: R2Colors.primary500,
                                  ),
                                ),
                                Icon(
                                  controller.reverseEmployeeOrder.value
                                      ? Icons.keyboard_double_arrow_up_sharp
                                      : Icons.keyboard_double_arrow_down_sharp,
                                  color: R2Colors.primary500,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            "Show Active Only",
                            style: TextStyle(
                              fontSize: 12,
                              color: controller.onlyActiveUsers.value ? R2Colors.primary500 : R2Colors.neutral700,
                            ),
                          ),
                          Checkbox(
                            value: controller.onlyActiveUsers.value,
                            onChanged: (bool? newValue) async {
                              if (newValue != null) {
                                controller.onlyActiveUsers.value = newValue;
                                await controller.getEmployees();
                              }
                            },
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            "Show Missing Punch-Outs",
                            style: TextStyle(
                              fontSize: 12,
                              color: controller.onlyMissingPunchOuts.value ? R2Colors.primary500 : R2Colors.neutral700,
                            ),
                          ),
                          Checkbox(
                            value: controller.onlyMissingPunchOuts.value,
                            onChanged: (bool? newValue) async {
                              // When checked, show only missing punch-outs
                              if (newValue == true) {
                                controller.onlyMissingPunchOuts.value = newValue!;
                                await controller.getMissingPunchOuts();
                              }
                              // When unchecked, show all employees
                              if (newValue == false) {
                                controller.onlyMissingPunchOuts.value = newValue!;
                                await controller.getEmployees();
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
