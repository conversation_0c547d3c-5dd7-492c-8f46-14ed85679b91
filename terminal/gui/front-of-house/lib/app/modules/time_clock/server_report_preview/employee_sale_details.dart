import 'dart:async';

import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/report.service.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

ReportService _reportService = Get.find<ReportService>();
NotificationService _notificationService = Get.find();

// ignore: must_be_immutable
class EmployeeSaleBreakdown extends StatelessWidget {
  const EmployeeSaleBreakdown({
    required this.employeeID,
    required this.startDate,
    required this.endDate,
  });

  final int employeeID;
  final String startDate;
  final String endDate;

  @override
  Widget build(BuildContext context) {
    final RxList<Sale> sales = <Sale>[].obs;
    unawaited(getEmployeeSales(sales, startDate, endDate, employeeID));

    return Obx(
      () => Column(
        children: sales.map((Sale s) {
          final int? displayIdx = Helpers.getDisplayFlag(s);
          final SaleFlags? displayFlag = displayIdx == null ? null : SaleFlags.values[displayIdx];
          final String title = Helpers.getSaleTitle(s, null);
          final bool dualPriced = s.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index);
          return Column(
            children: <Widget>[
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: R2Colors.neutral600,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Text(displayFlag?.friendlyString ?? 'N/A'),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Text(s.end_at == null ? "-" : DateFormat("yyyy-MM-dd h:mm a").format(s.end_at!.toLocal())),
                    ),
                  ),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text("total: \$${Helpers.formatCurrency(dualPriced ? s.document.saleHeader.cashTotal : s.document.saleHeader.total)}"),
                    ),
                  ),
                ],
              ),
              ...s.document.saleHeader.tenders.map(
                (SaleTender t) => Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: ColoredBox(
                    color: R2Colors.primary300.withOpacity(0.3),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Expanded(
                          child: Center(
                            child: Text("${PaymentMediaType.values[t.media ?? 0].string} Tender"),
                          ),
                        ),
                        Expanded(
                          child: Center(
                            child: Text("Amount: ${Helpers.formatCurrency(t.amount ?? 0)}"),
                          ),
                        ),
                        Expanded(
                          child: Center(
                            child: Text("Tip: ${Helpers.formatCurrency(t.tipAmount ?? 0)}"),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }
}

Future<void> getEmployeeSales(
  RxList<Sale> sales,
  String startDate,
  String endDate,
  int employeeID,
) async {
  final Either<ServiceError, List<Sale>> res = await _reportService.getEmployeeCompletedSalesAll(
    startDate: startDate,
    endDate: endDate,
    employeeID: employeeID,
  );
  res.fold(
    (ServiceError l) => _notificationService.error(l.message),
    (List<Sale> r) => sales.value = r,
  );
}
