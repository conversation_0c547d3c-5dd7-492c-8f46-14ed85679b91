import 'dart:async';

import 'package:backoffice/app/data/enums/dialog_button_type.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/global_widgets/custom_table.dart';
import 'package:backoffice/app/global_widgets/dialog_button.dart';
import 'package:backoffice/app/theme/theme.dart' as bohtheme;
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/modules/time_clock/server_report_preview/controller.dart';
import 'package:desktop/app/modules/time_clock/server_report_preview/employee_sale_details.dart';
import 'package:desktop/app/theme/theme.dart' as fohtheme;
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
// ignore: depend_on_referenced_packages

// ignore: must_be_immutable
class ServerReportPreview extends GetView<ServerReportPreviewController> {
  const ServerReportPreview({
    required this.start,
    required this.end,
    this.empID,
    this.selectedTerminal,
    this.flex = 9,
    this.showPrintBtn = true,
    this.physics,
    this.isBOH = true,
  });

  final String start;
  final String end;
  final int? empID;
  final int? selectedTerminal;
  final int flex;
  final bool showPrintBtn;
  final ScrollPhysics? physics;
  final bool isBOH;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServerReportPreviewController>(
      init: ServerReportPreviewController(),
      builder: (ServerReportPreviewController controller) {
        unawaited(controller.getServerReport(start, end, empID, selectedTerminal));
        return Material(
          color: isBOH ? bohtheme.R2Colors.white : fohtheme.R2Colors.white,
          child: Obx(
            () => controller.isLoading.value
                ? Center(
                    child: Lottie.asset(
                      'lib/assets/lottie/loading-animation.json',
                      height: 100,
                    ),
                  )
                : controller.empRecordList.isEmpty
                    ? const Center(
                        child: Text("No transactions to report"),
                      )
                    : Flex(
                        direction: Axis.vertical,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Flexible(
                            flex: flex,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 10, left: 10),
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: physics,
                                itemCount: controller.empRecordList.length,
                                itemBuilder: (BuildContext context, int index) {
                                  int serverDeptCount = 0;
                                  int serverDeptTotal = 0;
                                  int serverMediaTotal = 0;
                                  int serverGratTotal = 0;
                                  int serverMediaTipTotal = 0;
                                  int serverTipGratTotal = 0;
                                  int serverTipBDTipTotal = 0;

                                  int serverTaxTotal = 0;

                                  return Flex(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    direction: Axis.vertical,
                                    mainAxisSize: MainAxisSize.min,
                                    children: <Widget>[
                                      Flexible(
                                        flex: 2,
                                        child: Flex(
                                          direction: Axis.vertical,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: <Widget>[
                                            Text(
                                              "Employee: ${controller.empRecordList[index].employee.document.lastName}, ${controller.empRecordList[index].employee.document.firstName}",
                                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            ),
                                            Text(
                                              "ID: ${controller.empRecordList[index].employeeID}",
                                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Flexible(
                                        flex: 13,
                                        child: DecoratedBox(
                                          decoration: BoxDecoration(
                                            color: isBOH ? bohtheme.R2Colors.neutral100 : fohtheme.R2Colors.neutral100,
                                            border: Border.all(
                                              color: isBOH ? bohtheme.R2Colors.neutral300 : fohtheme.R2Colors.neutral300,
                                            ),
                                            borderRadius: const BorderRadius.all(
                                              Radius.circular(10),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(5),
                                            child: Flex(
                                              crossAxisAlignment: CrossAxisAlignment.stretch,
                                              mainAxisSize: MainAxisSize.min,
                                              direction: Axis.vertical,
                                              children: <Widget>[
                                                CustomTable(
                                                  headerTextColor: isBOH ? null : fohtheme.R2Colors.neutral600,
                                                  columnConfig: const <ColumnConfig>[
                                                    ColumnConfig('Department'),
                                                    ColumnConfig('Count', alignment: Alignment.center),
                                                    ColumnConfig('Amount', alignment: Alignment.centerRight),
                                                  ],
                                                  rows: controller.empRecordList[index].salesByDeptList.isEmpty
                                                      ? <CustomTableRow>[
                                                          CustomTableRow(
                                                            children: <Text>[
                                                              const Text("          -"),
                                                              const Text('0'),
                                                              Text("\$${Helpers.formatCurrency(0)}"),
                                                            ],
                                                          ),
                                                          CustomTableRow(
                                                            divider: true,
                                                            dividerColor: isBOH ? null : fohtheme.R2Colors.black,
                                                            style: CustomRowStyle.total,
                                                            textColor: isBOH ? null : fohtheme.R2Colors.neutral700,
                                                            children: <Text>[
                                                              const Text("Total:"),
                                                              const Text('0'),
                                                              Text(Helpers.formatCurrency(0)),
                                                            ],
                                                          ),
                                                        ]
                                                      : List<CustomTableRow>.generate(
                                                            controller.empRecordList[index].salesByDeptList.length,
                                                            (int deptIndex) {
                                                              serverDeptCount += controller.empRecordList[index].salesByDeptList[deptIndex].count;
                                                              serverDeptTotal +=
                                                                  controller.empRecordList[index].salesByDeptList[deptIndex].actual_price;
                                                              return CustomTableRow(
                                                                children: <Text>[
                                                                  Text(
                                                                    controller.empRecordList[index].salesByDeptList[deptIndex].department,
                                                                  ),
                                                                  Text(
                                                                    Helpers.formatWholeNumber(
                                                                      controller.empRecordList[index].salesByDeptList[deptIndex].count,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    "\$${Helpers.formatCurrency(controller.empRecordList[index].salesByDeptList[deptIndex].actual_price)}",
                                                                  ),
                                                                ],
                                                              );
                                                            },
                                                          ) +
                                                          <CustomTableRow>[
                                                            CustomTableRow(
                                                              divider: true,
                                                              dividerColor: isBOH ? null : fohtheme.R2Colors.black,
                                                              children: <Text>[
                                                                const Text("Dept Total:"),
                                                                Text(Helpers.formatWholeNumber(serverDeptCount)),
                                                                Text("\$${Helpers.formatCurrency(serverDeptTotal)}"),
                                                              ],
                                                            ),
                                                          ] +
                                                          List<CustomTableRow>.generate(controller.empRecordList[index].taxRows.length,
                                                              (int taxIndex) {
                                                            serverTaxTotal += controller.empRecordList[index].taxRows[taxIndex].tax_amount;
                                                            return CustomTableRow(
                                                              children: <Text>[
                                                                Text(controller.empRecordList[index].taxRows[taxIndex].description),
                                                                const Text(""),
                                                                Text(
                                                                  "\$${Helpers.formatCurrency(controller.empRecordList[index].taxRows[taxIndex].tax_amount)}",
                                                                ),
                                                              ],
                                                            );
                                                          }) +
                                                          <CustomTableRow>[
                                                            CustomTableRow(
                                                              children: <Widget>[
                                                                const Text("Takeout Fees:"),
                                                                const Text(""),
                                                                Text(
                                                                  controller.empRecordList[index].takeoutFeesTotal < 0
                                                                      ? "-\$${Helpers.formatCurrency(controller.empRecordList[index].takeoutFeesTotal)}"
                                                                      : "\$${Helpers.formatCurrency(controller.empRecordList[index].takeoutFeesTotal)}",
                                                                ),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              style: CustomRowStyle.total,
                                                              textColor: isBOH ? null : fohtheme.R2Colors.neutral700,
                                                              children: <Text>[
                                                                const Text("Total:"),
                                                                const Text(""),
                                                                Text(
                                                                  "\$${Helpers.formatCurrency(serverDeptTotal + serverTaxTotal + controller.empRecordList[index].takeoutFeesTotal)}",
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                ),
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 15, bottom: 5),
                                                  child: Text(
                                                    "Media Breakdown",
                                                    style: TextStyle(fontSize: 16),
                                                  ),
                                                ),
                                                CustomTable(
                                                  headerTextColor: isBOH ? null : fohtheme.R2Colors.neutral600,
                                                  columnConfig: const <ColumnConfig>[
                                                    ColumnConfig('Media'),
                                                    ColumnConfig('Total', alignment: Alignment.centerRight),
                                                    ColumnConfig('Tips', alignment: Alignment.centerRight),
                                                    ColumnConfig('Tot. w Tips', alignment: Alignment.centerRight),
                                                  ],
                                                  rows: controller.empRecordList[index].mediaBreakdownList.isEmpty
                                                      ? <CustomTableRow>[
                                                          CustomTableRow(
                                                            children: <Text>[
                                                              const Text("          -"),
                                                              const Text(""),
                                                              const Text('0'),
                                                              Text(Helpers.formatCurrency(0)),
                                                            ],
                                                          ),
                                                          CustomTableRow(
                                                            divider: true,
                                                            style: CustomRowStyle.total,
                                                            children: <Text>[
                                                              const Text("Total:"),
                                                              const Text('0'),
                                                              const Text('0'),
                                                              Text("\$${Helpers.formatCurrency(0)}"),
                                                            ],
                                                          ),
                                                        ]
                                                      : List<CustomTableRow>.generate(
                                                            controller.empRecordList[index].mediaBreakdownList.length,
                                                            (int mediaIndex) {
                                                              final int currentMediatip = controller.empRecordList[index].tipBreakdownList
                                                                      .firstWhereOrNull(
                                                                        (EmployeeTipBreakdown element) =>
                                                                            element.tender_media ==
                                                                            int.parse(
                                                                              controller.empRecordList[index].mediaBreakdownList[mediaIndex].media!,
                                                                            ),
                                                                      )
                                                                      ?.tip_amount ??
                                                                  0;
                                                              serverMediaTotal +=
                                                                  controller.empRecordList[index].mediaBreakdownList[mediaIndex].net_total;
                                                              serverMediaTipTotal += currentMediatip;
                                                              return CustomTableRow(
                                                                children: <Text>[
                                                                  Text(
                                                                    controller.empRecordList[index].mediaBreakdownList[mediaIndex].media == "7"
                                                                        ? "Legacy(${Helpers.formatWholeNumber(controller.empRecordList[index].mediaBreakdownList[mediaIndex].count)}):"
                                                                        : "${Helpers.mediaTypeAsString(
                                                                            PaymentMediaType.values[int.parse(
                                                                              controller.empRecordList[index].mediaBreakdownList[mediaIndex].media!,
                                                                            )],
                                                                          )}(${Helpers.formatWholeNumber(controller.empRecordList[index].mediaBreakdownList[mediaIndex].count)}):",
                                                                  ),
                                                                  Text(
                                                                    "\$${Helpers.formatCurrency(controller.empRecordList[index].mediaBreakdownList[mediaIndex].net_total)}",
                                                                  ),
                                                                  Text(
                                                                    "\$${Helpers.formatCurrency(currentMediatip)}",
                                                                  ),
                                                                  Text(
                                                                    "\$${Helpers.formatCurrency(controller.empRecordList[index].mediaBreakdownList[mediaIndex].net_total + currentMediatip)}",
                                                                  ),
                                                                ],
                                                              );
                                                            },
                                                          ) +
                                                          <CustomTableRow>[
                                                            CustomTableRow(
                                                              divider: true,
                                                              dividerColor: isBOH ? null : fohtheme.R2Colors.black,
                                                              style: CustomRowStyle.total,
                                                              textColor: isBOH ? null : fohtheme.R2Colors.neutral700,
                                                              children: <Text>[
                                                                const Text("Total:"),
                                                                Text(
                                                                  "\$${Helpers.formatCurrency(serverMediaTotal)}",
                                                                ),
                                                                Text("\$${Helpers.formatCurrency(serverMediaTipTotal)}"),
                                                                Text("\$${Helpers.formatCurrency(serverMediaTotal + serverMediaTipTotal)}"),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              children: const <Text>[
                                                                Text(""),
                                                                Text(""),
                                                                Text(""),
                                                                Text(""),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              children: <Text>[
                                                                const Text("Cash Total:"),
                                                                const Text(""),
                                                                const Text(""),
                                                                Text("\$${Helpers.formatCurrency(controller.empRecordList[index].serverCashTotal)}"),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              children: <Text>[
                                                                const Text("Credit Tip Total:"),
                                                                const Text(""),
                                                                const Text(""),
                                                                Text("\$${Helpers.formatCurrency(controller.empRecordList[index].ccTips)}"),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              children: <Text>[
                                                                const Text("Credit Gratuity Total:"),
                                                                const Text(""),
                                                                const Text(""),
                                                                Text("\$${Helpers.formatCurrency(controller.empRecordList[index].ccGratuity)}"),
                                                              ],
                                                            ),
                                                            CustomTableRow(
                                                              style: CustomRowStyle.total,
                                                              textColor: isBOH ? null : fohtheme.R2Colors.neutral700,
                                                              children: <Text>[
                                                                const Text("Adjusted Cash:"),
                                                                const Text(""),
                                                                const Text(""),
                                                                Text(
                                                                  "\$${Helpers.formatCurrency(controller.empRecordList[index].serverCashTotal - controller.empRecordList[index].ccTips - controller.empRecordList[index].ccGratuity)}",
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                ),
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 15, bottom: 5),
                                                  child: Text(
                                                    "Tip Breakdown",
                                                    style: TextStyle(fontSize: 16),
                                                  ),
                                                ),
                                                CustomTable(
                                                  headerTextColor: isBOH ? null : fohtheme.R2Colors.neutral600,
                                                  columnConfig: const <ColumnConfig>[
                                                    ColumnConfig('Media'),
                                                    ColumnConfig('Gratuity', alignment: Alignment.centerRight),
                                                    ColumnConfig('Tips', alignment: Alignment.centerRight),
                                                    ColumnConfig('Total', alignment: Alignment.centerRight),
                                                  ],
                                                  rows: List<CustomTableRow>.generate(
                                                        controller.empRecordList[index].tipBreakdownList.length,
                                                        (int tipIndex) {
                                                          serverGratTotal += controller.empRecordList[index].tipBreakdownList[tipIndex].grat_amount;
                                                          // ignore: avoid_print
                                                          print(controller.empRecordList[index].tipBreakdownList[tipIndex].tip_amount);
                                                          serverTipBDTipTotal +=
                                                              controller.empRecordList[index].tipBreakdownList[tipIndex].tip_amount;
                                                          serverTipGratTotal +=
                                                              controller.empRecordList[index].tipBreakdownList[tipIndex].grat_amount +
                                                                  controller.empRecordList[index].tipBreakdownList[tipIndex].tip_amount;
                                                          return CustomTableRow(
                                                            children: <Text>[
                                                              Text(
                                                                Helpers.mediaTypeAsString(
                                                                  PaymentMediaType.values[
                                                                      controller.empRecordList[index].tipBreakdownList[tipIndex].tender_media],
                                                                ),
                                                              ),
                                                              Text(
                                                                "\$${Helpers.formatCurrency(controller.empRecordList[index].tipBreakdownList[tipIndex].grat_amount)}",
                                                              ),
                                                              Text(
                                                                "\$${Helpers.formatCurrency(controller.empRecordList[index].tipBreakdownList[tipIndex].tip_amount)}",
                                                              ),
                                                              Text(
                                                                "\$${Helpers.formatCurrency(controller.empRecordList[index].tipBreakdownList[tipIndex].grat_amount + controller.empRecordList[index].tipBreakdownList[tipIndex].tip_amount)}",
                                                              ),
                                                            ],
                                                          );
                                                        },
                                                      ) +
                                                      <CustomTableRow>[
                                                        CustomTableRow(
                                                          divider: true,
                                                          dividerColor: isBOH ? null : fohtheme.R2Colors.black,
                                                          style: CustomRowStyle.total,
                                                          textColor: isBOH ? null : fohtheme.R2Colors.neutral700,
                                                          children: <Text>[
                                                            const Text("Total:"),
                                                            Text("\$${Helpers.formatCurrency(serverGratTotal)}"),
                                                            Text("\$${Helpers.formatCurrency(serverTipBDTipTotal)}"),
                                                            Text("\$${Helpers.formatCurrency(serverTipGratTotal)}"),
                                                          ],
                                                        ),
                                                      ],
                                                ),
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 15, bottom: 5),
                                                  child: Text(
                                                    "Sales Statistics",
                                                    style: TextStyle(fontSize: 16),
                                                  ),
                                                ),
                                                CustomTable(
                                                  headerTextColor: isBOH ? null : fohtheme.R2Colors.neutral600,
                                                  columnConfig: const <ColumnConfig>[
                                                    ColumnConfig('Stat'),
                                                    ColumnConfig('Count', alignment: Alignment.center),
                                                    ColumnConfig('Total', alignment: Alignment.centerRight),
                                                  ],
                                                  rows: <CustomTableRow>[
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("#Guest \$AVG:"),
                                                        Text(Helpers.formatWholeNumber(controller.empRecordList[index].empStats.guest_count)),
                                                        Text("\$${Helpers.formatCurrency(controller.empRecordList[index].empStats.guest_average)}"),
                                                      ],
                                                    ),
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("#Check \$AVG:"),
                                                        Text(Helpers.formatWholeNumber(controller.empRecordList[index].empStats.check_count)),
                                                        Text("\$${Helpers.formatCurrency(controller.empRecordList[index].empStats.check_average)}"),
                                                      ],
                                                    ),
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("AVG turn time:"),
                                                        Text(controller.empRecordList[index].empStats.duration),
                                                        const Text(""),
                                                      ],
                                                    ),
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("Refunded Sales"),
                                                        Text(Helpers.formatWholeNumber(controller.empRecordList[index].empStats.refunded_count)),
                                                        Text("\$${Helpers.formatCurrency(controller.empRecordList[index].empStats.refunded_amount)}"),
                                                      ],
                                                    ),
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("Cancelled Sales"),
                                                        Text(Helpers.formatWholeNumber(controller.empRecordList[index].empStats.cancel_sale_count)),
                                                        Text(
                                                          "\$${Helpers.formatCurrency(controller.empRecordList[index].empStats.cancel_sale_amount)}",
                                                        ),
                                                      ],
                                                    ),
                                                    CustomTableRow(
                                                      children: <Text>[
                                                        const Text("No Sales"),
                                                        Text(Helpers.formatWholeNumber(controller.empRecordList[index].empStats.no_sale_count)),
                                                        const Text(""),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                const Padding(
                                                  padding: EdgeInsets.only(top: 15, bottom: 5),
                                                  child: Text(
                                                    "Sale Breakdown",
                                                    style: TextStyle(fontSize: 16),
                                                  ),
                                                ),
                                                Column(
                                                  children: <Widget>[
                                                    EmployeeSaleBreakdown(
                                                      employeeID: controller.empRecordList[index].employeeID,
                                                      startDate: start,
                                                      endDate: end,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                          if (showPrintBtn)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: DialogButton(
                                color: isBOH ? null : fohtheme.R2Colors.primary500,
                                buttonType: EDialogButtonType.ADD,
                                buttonText: "Print Report",
                                fontSize: 22,
                                onTapped: () async {
                                  await controller.printServerReport(start, end);
                                },
                              ),
                            ),
                        ],
                      ),
          ),
        );
      },
    );
  }
}
