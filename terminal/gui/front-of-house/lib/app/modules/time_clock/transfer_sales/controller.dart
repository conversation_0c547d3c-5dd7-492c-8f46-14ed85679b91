import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/data/models/service_error.dart';
import 'package:desktop/app/data/services/activity.service.dart';
import 'package:desktop/app/data/services/employee_service.dart';
import 'package:desktop/app/data/services/identity.service.dart';
import 'package:desktop/app/data/services/notification.service.dart';
import 'package:desktop/app/data/services/sale.service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:get/get.dart';
import 'package:logging/logging.dart';

final Logger _logger = Logger('TransferSalesController');

class TransferSalesController extends GetxController {
  TransferSalesController({required this.openSales});

  final EmployeeService _employeeService = Get.find();
  final IdentityService _identityService = Get.find();
  final ActivityService _activityService = Get.find();
  final SaleService _saleService = Get.find();
  final NotificationService _notificationService = Get.find();

  final List<Sale> openSales;
  final RxList<Sale> selectedSales = <Sale>[].obs;
  final Rx<Employee> selectedEmployee = Employee.empty().obs;
  final RxMap<int, List<Sale>> moveMap = <int, List<Sale>>{}.obs;
  final RxList<Employee> clockedInEmployees = <Employee>[].obs;
  final RxList<Sale> toMove = <Sale>[].obs;
  final RxBool initialPrompt = true.obs;

  @override
  Future<void> onInit() async {
    toMove.value = <Sale>[...openSales];
    await getClockedInEmployees();
    super.onInit();
  }

  Future<void> getClockedInEmployees() async {
    clockedInEmployees.value = (await _employeeService.getClockedInEmployees())
        .where(
          (Employee e) => e.id != null && e.id != _identityService.currentEmployee.id,
        )
        .toList();
    for (final Employee e in clockedInEmployees) {
      moveMap[e.id!] = <Sale>[];
    }
  }

  void onTapMove() {
    (moveMap[selectedEmployee.value.id] ?? <Sale>[]).addAll(selectedSales);
    toMove.removeWhere((Sale s) => selectedSales.contains(s));
    selectedSales.value = <Sale>[];
  }

  void onTapUndo() {
    toMove.addAll(moveMap[selectedEmployee.value.id] ?? <Sale>[]);
    moveMap[selectedEmployee.value.id!] = <Sale>[];
  }

  Future<void> transferSales() async {
    try {
      final List<Sale> returnVal = <Sale>[];
      for (final int employee in moveMap.keys) {
        for (final Sale s in moveMap[employee]!) {
          returnVal.add(s);
          s.document.saleHeader.currentEmployeeNumber = employee;
          final Either<ServiceError, Sale> upsertRes = await _saleService.upsert(
            sale: s,
            employee: _identityService.currentEmployee,
            terminalNumber: _identityService.terminalNumber,
          );
          if (upsertRes.isLeft()) {
            throw upsertRes.match(
              (ServiceError l) => l.message,
              (Sale r) => "",
            );
          }
        }
      }
      Get.back(result: returnVal);
      for (final int employee in moveMap.keys) {
        if (moveMap[employee]!.isNotEmpty) {
          final Employee emp = clockedInEmployees.firstWhereOrNull((Employee emp) => emp.id == employee) ?? Employee.empty();
          final Either<ServiceError, Activity> insertRes = await _activityService.insertActivity(
            activity: Activity(
              emp_id: _identityService.currentEmployee.id,
              term_num: _identityService.terminalNumber,
              activity: ActivityFlags.TRANSFER_SALES.index,
              str_data: "${moveMap[employee]!.length} Transferred to ${emp.document.firstName} ${emp.document.lastName}",
              sale_num: moveMap[employee]!.first.sale_number,
              data1: employee,
            ),
          );
          insertRes.match(
            (ServiceError l) {
              _logger.severe(l.message);
            },
            (Activity r) => null,
          );
        }
      }
    } catch (err, stack) {
      _notificationService.error("Transfer error: Unable to clock out");
      _logger.severe(
        "Transfer sale error",
        err,
        stack,
      );
      Get.back();
    }
  }

  Future<void> moveSales() async {}
}
