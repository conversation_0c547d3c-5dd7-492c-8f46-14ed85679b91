// ignore_for_file: avoid_dynamic_calls, unused_field, cast_nullable_to_non_nullable

import 'package:backoffice/app/data/enums/gift_type.dart';
import 'package:backoffice/app/data/enums/order_type_print_options.dart';
import 'package:backoffice/app/data/models/cashier_discounts.dart';
import 'package:backoffice/app/data/models/cashier_media.dart';
import 'package:backoffice/app/data/models/cashier_sales.dart';
import 'package:backoffice/app/data/models/cashier_stats.dart';
import 'package:backoffice/app/data/models/emp_discounts.dart';
import 'package:backoffice/app/data/models/emp_record.dart';
import 'package:backoffice/app/data/models/emp_tip_breakdown.dart';
import 'package:backoffice/app/data/models/gift_summary_report.dart';
import 'package:backoffice/app/data/models/major_report.dart';
import 'package:backoffice/app/data/models/media_report.dart';
import 'package:backoffice/app/data/models/order_type_report.dart';
import 'package:backoffice/app/data/models/report.dart';
import 'package:backoffice/app/data/models/sales_by_dept.dart';
import 'package:backoffice/app/data/models/tax_report.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:backoffice/app/data/view_models/hourly_report.dart';
import 'package:backoffice/app/data/view_models/monthly_report.dart';
import 'package:backoffice/app/data/view_models/sales_by_dept_summary.dart';
import 'package:backoffice/app/data/view_models/time_card_punch.dart';
import 'package:backoffice/app/modules/reports/children/paid_out/controller.dart';
import 'package:desktop/app/data/enums/activity_flags.dart';
import 'package:desktop/app/data/enums/order_types.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/prep_print_sizing.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/enums/sale_tender_flags.dart';
import 'package:desktop/app/data/enums/uom.dart';
import 'package:desktop/app/data/models/activity.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:desktop/app/data/models/print_job.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class PrinterInterface {
  static final PjRows _emptyLine = PjRows(
    font: 0,
    fontSize: 0,
    specOpts: 0,
    text: "",
  );

  static final PjRows _thickLine = PjRows(
    font: 0,
    fontSize: 0,
    specOpts: 2,
    text: "L2",
  );

  static final PjRows _thinLine = PjRows(
    font: 0,
    fontSize: 0,
    specOpts: 2,
    text: "L1",
  );

  static final PjRows _cutLine = PjRows(
    font: 0,
    fontSize: 0,
    specOpts: 3,
    text: "",
  );
  static final PjRows _underLine = PjRows(
    font: 0,
    fontSize: 0,
    specOpts: 0,
    // text: "-------------------------------------------",
    text: "___________________________________________",
  );

  static final List<PjRows> _hours = <PjRows>[
    _emptyLine,
    // PjRows(
    //   font: 0,
    //   fontSize: 0,
    //   specOpts: 1,
    //   text: "
    //Thursday 8:00 AM-5:30 PM",
    // ),
    // PjRows(
    //   font: 0,
    //   fontSize: 0,
    //   specOpts: 1,
    //   text: "Friday-Saturday 8:00 AM-6:00 PM",
    // ),
    // PjRows(
    //   font: 0,
    //   fontSize: 0,
    //   specOpts: 1,
    //   text: "Sunday CLOSED",
    // ),
  ];

  /// Builds  sales receipt
  static PrintJob buildReceipt({
    required Employee currentEmployee,
    required Employee currentUser,
    required Sale sale,
    required String legacyGiftName,
    required bool showCommentRows,
    required bool showSaleDesc,
    bool condensedAuth = false,
    Map<int, String> terminalDescs = const <int, String>{},
    String saleName = "",
    bool customerCopy = true,
    bool refundReceiptSignatureLine = false,
    Map<int, List<int>> subtotals = const <int, List<int>>{},
    bool showSeats = true,
    bool individualSeat = false,
    List<String>? receiptHeader,
    bool isMerchantDualPricing = false,
    List<Tax>? taxList,
    bool tipping = false,
    Customer? customer,
    String? qrCodeUrl = "",
    List<SaleTender>? tenders,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    final List<PjRows> tenderPjRows = <PjRows>[];
    final List<PjRows> refundPjRows = <PjRows>[];
    final List<PjRows> storeHeaderPjRows = <PjRows>[];

    bool containsHouseTender = false;
    final bool dualPriced = sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index);
    final bool saleCompleted = sale.document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index) && tenders == null;

    final List<SaleTender> tenderList = tenders ?? sale.document.saleHeader.tenders;

    final bool hasGratuity = sale.document.saleHeader.gratuityPercent > 0 || (sale.document.saleHeader.addedGratuity ?? 0) > 0;

    final bool hasCreditTender = tenderList.any((SaleTender t) => t.media == PaymentMediaType.Credit.index);
    final bool hasNonCreditTender = tenderList.any((SaleTender t) => t.media != PaymentMediaType.Credit.index);

    final bool useTipping = tipping && hasCreditTender && !hasNonCreditTender;

    final bool useCondensed = !customerCopy && useTipping && condensedAuth;

    if (!useCondensed) {
      for (final String headerRow in receiptHeader ?? <String>[]) {
        final PjRows storeHeader = PjRows(
          text: headerRow,
          font: 0,
          fontSize: 0,
          specOpts: 1,
        );
        storeHeaderPjRows.add(storeHeader);
      }
    }

    for (final SaleTender tender in tenderList) {
      String typeString = "Unknown Media Type";

      switch (tender.media) {
        case 0:
          typeString = "Cash";
        case 1:
          typeString = "Check";
        case 2:
          typeString = "Credit";
        case 3:
          typeString = "Debit";
        case 4:
          typeString = "EBT";
        case 5:
          typeString = "Gift";
        case 6:
          typeString = "House";
          containsHouseTender = true;
        case 7:
          typeString = legacyGiftName;
        default:
      }

      String displayType = typeString;

      if (tender.saleTenderFlags.contains(SaleTenderFlags.REFUNDED.index)) {
        displayType = "$typeString Refunded";
      }

      if (tender.amount! < 0 && !tender.saleTenderFlags.contains(SaleTenderFlags.REFUNDED.index)) {
        displayType = "Change Due";
      }

      if (tender.saleTenderFlags.contains(SaleTenderFlags.VOIDED.index)) {
        displayType = "$typeString Voided";
      }

      tenderPjRows.addAll(<PjRows>[
        PjRows(
          leftText: displayType,
          rightText: "\$${Helpers.formatCurrency(tender.amount!.abs())}",
          font: 0,
          fontSize: useCondensed ? 16 : 0,
          specOpts: 0,
        ),
        if (tender.cardTransactionData != null)
          PjRows(
            leftText: "   Account: ${tender.cardTransactionData!.cardPAN}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        if (useCondensed && tender.cardTransactionData != null)
          PjRows(
            leftText: "   Auth Response: ${tender.cardTransactionData!.authCode}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        // if (useCondensed && tender.giftTransactionData != null)
        //   PjRows(
        //     leftText: "   Gift ID: ${tender.giftTransactionData!.identification}",
        //     font: 0,
        //     fontSize: 0,
        //     specOpts: 0,
        //   ),
        if (tender.memo != null && tender.memo!.isNotEmpty)
          PjRows(
            leftText: "   Memo: ${tender.memo}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
      ]);
    }

    pjRows.addAll(<PjRows>[
      if (!useCondensed)
        PjRows(
          font: 0,
          fontSize: 0,
          specOpts: 2,
          text: "SL",
        ),
      if (!useCondensed)
        PjRows(
          font: 0,
          fontSize: 16,
          specOpts: 1,
          text: "",
        ),
      ...storeHeaderPjRows,
      PjRows(
        leftText: saleName,
        rightText: "#${sale.sale_number}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      if ((sale.document.saleHeader.settleTerminalNumber ?? 0) != 0)
        PjRows(
          leftText:
              "Settled Terminal: ${terminalDescs[sale.document.saleHeader.settleTerminalNumber!] ?? sale.document.saleHeader.settleTerminalNumber!.toString()}",
          font: 0,
          fontSize: 14,
          specOpts: 0,
        ),
      if ((sale.document.saleHeader.tableDesc ?? "") != "")
        PjRows(
          leftText: "Table: ${sale.document.saleHeader.tableDesc!}",
          font: 0,
          fontSize: 14,
          specOpts: 0,
        ),
      if (showSaleDesc && (sale.document.saleHeader.saleDescription ?? "") != "")
        PjRows(
          leftText: "Description: ${sale.document.saleHeader.saleDescription!}",
          font: 0,
          fontSize: 14,
          specOpts: 0,
        ),
      PjRows(
        leftText: "Server: ${currentEmployee.document.firstName}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);

    if (showSeats && !useCondensed) {
      pjRows.add(
        PjRows(
          leftText: "Order Type: ${OrderType.values[sale.document.saleHeader.orderType].friendlyString}",
          fontSize: 14,
          specOpts: 0,
        ),
      );
    }

    if (sale.document.saleHeader.promisedTime != null && !useCondensed) {
      final String timeString = Helpers.millisecondsToTimeString(
        sale.document.saleHeader.promisedTime!,
      );

      pjRows.add(
        PjRows(
          leftText: "PROMISED TIME:",
          rightText: timeString,
          font: 0,
          fontSize: 15,
          specOpts: 0,
        ),
      );
    }

    if (customer != null) {
      pjRows.addAll(<PjRows>[
        PjRows(
          leftText: "Customer: ${customer.document.firstName} ${customer.document.lastName}",
          fontSize: 15,
          specOpts: 0,
        ),
      ]);
      if (sale.document.saleHeader.orderType == OrderType.TAKEOUT.index || sale.document.saleHeader.orderType == OrderType.DELIVERY.index) {
        pjRows.addAll(<PjRows>[
          if ((customer.document.phone1 ?? "") != "")
            PjRows(
              leftText: Helpers.formatPhoneNumber(customer.document.phone1!),
              fontSize: 15,
              specOpts: 0,
            ),
          if ((customer.document.phone2 ?? "") != "")
            PjRows(
              leftText: Helpers.formatPhoneNumber(customer.document.phone2!),
              fontSize: 15,
              specOpts: 0,
            ),
        ]);
      }
      if (sale.document.saleHeader.orderType == OrderType.DELIVERY.index) {
        pjRows.addAll(<PjRows>[
          if ((customer.document.address ?? "") != "")
            PjRows(
              leftText: Helpers.formatCustomerAddress(customer),
              fontSize: 14,
              specOpts: 0,
            ),
          if ((customer.document.company ?? "") != "")
            PjRows(
              leftText: customer.document.company,
              fontSize: 15,
              specOpts: 0,
            ),
        ]);
      }
    }

    if (!useCondensed) pjRows.add(_thickLine);

    final List<List<SaleRow>> rowLists = <List<SaleRow>>[];
    final int endIdx = (sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt;

    for (int i = 0; i < endIdx; i++) {
      final int index = i >= (sale.document.saleHeader.seatCnt ?? 1) ? (sale.document.saleHeader.seatCnt ?? 1) - (i + 1) : i + 1;
      rowLists.add(
        sale.document.saleRows
            .where(
              (SaleRow row) => (row.seatNumber == index) || (i == 0 && row.seatNumber == 0),
            )
            .toList(),
      );
    }

    final List<PjRows> insertRows = <PjRows>[];

    for (final List<SaleRow> saleRows in rowLists) {
      if (saleRows.isEmpty) continue;
      final int seatNum = (saleRows[0].seatNumber) == 0 ? 1 : saleRows[0].seatNumber;
      final bool toGo = seatNum < 0;
      if (showSeats) {
        insertRows.addAll(<PjRows>[
          if (toGo)
            PjRows(
              text: "",
              leftText: "<<<< To-Go ${seatNum.abs()} >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",
            )
          else
            PjRows(
              text: "",
              leftText: "---[ Seat $seatNum ]-----------------------------",
            ),
          if (isMerchantDualPricing) _emptyLine,
          if (isMerchantDualPricing)
            PjRows(
              leftText: "Item",
              rightText: "Cash    Card",
            ),
        ]);
      }
      for (final SaleRow saleRow in saleRows) {
        if (!saleRow.isVisible) continue;
        if (!showCommentRows && saleRow.flags.contains(SaleRowFlags.COMMENT.index)) continue;
        if (saleRow.flags.contains(SaleRowFlags.VOIDED.index)) continue;

        int displayPriceGross = saleRow.grossPrice;
        int cashDisplayPriceGross = saleRow.cashGrossPrice;
        int displayPriceBase = saleRow.basePrice;
        int cashDisplayPriceBase = saleRow.cashBasePrice;
        int indent = 0;
        List<SaleRow> childRows = <SaleRow>[saleRow];
        final bool isComment = saleRow.flags.contains(SaleRowFlags.COMMENT.index);

        if (saleRow.parent < 0 && saleRow.hasChildren) {
          childRows = Helpers.getChildRows(saleRow, saleRows);
          for (final SaleRow childRow in childRows) {
            if (childRow.parent >= 0) {
              if (!childRow.flags.contains(SaleRowFlags.VOIDED.index)) {
                displayPriceGross += childRow.grossPrice;
                cashDisplayPriceGross += childRow.cashGrossPrice;
                displayPriceBase += childRow.basePrice;
                cashDisplayPriceBase += childRow.cashBasePrice;
              }
            }
          }
        }

        if (saleRow.parent >= 0) {
          indent = Helpers.getSaleRowIndent(saleRow: saleRow, rowList: saleRows);
          if (indent > 5) indent = 5;
        }

        if (isMerchantDualPricing) {
          insertRows.add(
            PjRows(
              qty: 1,
              text: "",
              price: displayPriceGross,
              fontSize: isComment ? 16 : null,
              leftText: saleRow.parent < 0
                  ? "${saleRow.qty} ${saleRow.receiptDescription}"
                  : "${List<String>.filled(indent, isComment ? " " : "  ").join()}${saleRow.flags.contains(SaleRowFlags.COMMENT.index) ? "!" : ""}${saleRow.receiptDescription}",
              rightText:
                  saleRow.parent < 0 ? "\$${Helpers.formatCurrency(cashDisplayPriceGross)}    \$${Helpers.formatCurrency(displayPriceGross)}" : "",
            ),
          );
        } else {
          insertRows.add(
            PjRows(
              qty: 1,
              text: "",
              price: displayPriceGross,
              fontSize: isComment ? 16 : null,
              leftText: saleRow.parent < 0
                  ? "${saleRow.qty} ${saleRow.receiptDescription}"
                  : "${List<String>.filled(indent, isComment ? " " : "  ").join()}${saleRow.flags.contains(SaleRowFlags.COMMENT.index) ? "!" : ""}${saleRow.receiptDescription}",
              rightText: saleRow.parent < 0 ? "\$${Helpers.formatCurrency(displayPriceGross)}" : "",
            ),
          );
        }

        // insertRows.add(
        //   PjRows(
        //     qty: 1,
        //     text: "",
        //     price: displayPriceGross,
        //     leftText: saleRow.parent! < 0
        //         ? "${saleRow.qty} ${saleRow.receiptDescription}"
        //         : "${List<String>.filled(indent, "  ").join()} ${saleRow.receiptDescription}",
        //     rightText: saleRow.parent! < 0
        //         ? "\$${Helpers.formatCurrency(displayPriceGross - Helpers.getDualPriceDiff(displayPriceGross, 4))} \$${Helpers.formatCurrency(displayPriceGross)}"
        //         : "",
        //   ),
        // );

        if (saleRow.isWeightedItem && saleRow.parent < 0) {
          insertRows.add(
            PjRows(
              qty: 1,
              text: "",
              leftText: "  (${saleRow.qty} @ \$${Helpers.formatCurrency(dualPriced ? cashDisplayPriceBase : displayPriceBase)})",
              rightText: "",
            ),
          );
        }

        if (saleRow.isWeightedItem) {
          String uomText = "";
          switch (UOM.values[saleRow.UOM!]) {
            case UOM.LB:
              uomText = UOM.LB.toDisplayString();
            case UOM.OZ:
              uomText = UOM.OZ.toDisplayString();
            case UOM.G:
              uomText = UOM.G.toDisplayString();
            case UOM.KG:
              uomText = UOM.KG.toDisplayString();
            case UOM.NA:
              // TODO: Handle this case.
              break;
          }

          if (uomText != "") {
            insertRows.add(
              PjRows(
                qty: 1,
                text: "",
                leftText:
                    "  gross (${saleRow.weight?.toStringAsFixed(2)} $uomText @ \$${Helpers.formatCurrency(dualPriced ? saleRow.cashOriginalPrice : saleRow.originalPrice)}/$uomText)",
                rightText: "",
              ),
            );
          }
        }

        if (saleRow.parent < 0) {
          for (final SaleRowDiscount discount in saleRow.discounts) {
            final int useAmount = dualPriced ? discount.cashAmount : discount.amount;
            int useValue = 0;
            for (final SaleRow child in childRows) {
              useValue = useValue +
                  (child.discounts.firstWhereOrNull((SaleRowDiscount d) => d.title == discount.title && d.type == discount.type)?.value ?? 0);
            }
            final String discountText = discount.type == 0 ? "$useAmount%" : "\$${Helpers.formatCurrency(useValue)}";
            if (discount.value != 0) {
              insertRows.add(
                PjRows(
                  qty: 1,
                  text: "",
                  leftText: "  ${discount.title} - $discountText",
                  rightText: dualPriced
                      ? "-\$${Helpers.formatCurrency(useValue)}    -\$${Helpers.formatCurrency(discount.value)}"
                      : "-\$${Helpers.formatCurrency(useValue)}",
                ),
              );
            }
          }
        }
      }

      if (showSeats) {
        if (subtotals.isNotEmpty) {
          insertRows.addAll(<PjRows>[
            PjRows(
              text: "",
              leftText: "Seat Total:",
              rightText: isMerchantDualPricing
                  ? "\$${Helpers.formatCurrency(subtotals[saleRows[0].seatNumber == 0 ? 1 : saleRows[0].seatNumber]![1])}    \$${Helpers.formatCurrency(subtotals[saleRows[0].seatNumber == 0 ? 1 : saleRows[0].seatNumber]![0])}"
                  : "\$${Helpers.formatCurrency(subtotals[saleRows[0].seatNumber == 0 ? 1 : saleRows[0].seatNumber]![1])}",
            ),
          ]);
        }
      }
    }

    if (!useCondensed) pjRows.addAll(insertRows);

    final int saleSubTotal = sale.document.saleHeader.subTotal;
    final int cashSaleSubTotal = sale.document.saleHeader.cashSubTotal;
    final int saleTotal = sale.document.saleHeader.total;
    final int cashSaleTotal = sale.document.saleHeader.cashTotal;
    int amountPaid = sale.document.saleHeader.total;

    // Check if dual pricing is happening then modify the total
    if (sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index)) {
      // saleSubTotal = sale.document.saleHeader.dualPricingSubTotal;
      // saleTaxTotal = sale.document.saleHeader.dualPricingTaxTotal;
      amountPaid = sale.document.saleHeader.cashTotal;
    }

    if (useCondensed) {
      pjRows.addAll(<PjRows>[_emptyLine, ...tenderPjRows, _emptyLine]);
    } else {
      pjRows.addAll(<PjRows>[
        _thickLine,
        PjRows(
          leftText: "SubTotal",
          rightText: isMerchantDualPricing
              ? "\$${Helpers.formatCurrency(cashSaleSubTotal)}    \$${Helpers.formatCurrency(saleSubTotal)}"
              : "\$${Helpers.formatCurrency(saleSubTotal)}",
          // rightText: "\$${Helpers.formatCurrency(sale.document.saleHeader.subTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      ]);
    }

    if (isMerchantDualPricing && !useCondensed) {
      for (int i = 0; i < sale.document.saleHeader.taxTotals.length; i++) {
        if (sale.document.saleHeader.taxTotals[i] != 0) {
          pjRows.add(
            PjRows(
              leftText: taxList != null ? taxList[i].desc : "Tax",
              rightText:
                  "\$${Helpers.formatCurrency(sale.document.saleHeader.cashTaxTotals[i])}     \$${Helpers.formatCurrency(sale.document.saleHeader.taxTotals[i])}",
              // rightText: "\$${Helpers.formatCurrency(sale.document.saleHeader.total - sale.document.saleHeader.subTotal)}",
              font: 0,
              fontSize: 0,
              specOpts: 0,
            ),
          );
        }
      }
    } else {
      for (int i = 0; i < sale.document.saleHeader.taxTotals.length; i++) {
        if (sale.document.saleHeader.taxTotals[i] != 0 && !useCondensed) {
          pjRows.add(
            PjRows(
              leftText: taxList != null ? taxList[i].desc : "Tax",
              rightText: "\$${Helpers.formatCurrency(sale.document.saleHeader.taxTotals[i])}",
              // rightText: "\$${Helpers.formatCurrency(sale.document.saleHeader.total - sale.document.saleHeader.subTotal)}",
              font: 0,
              fontSize: 0,
              specOpts: 0,
            ),
          );
        }
      }
    }

    if (hasGratuity) {
      if (useCondensed) pjRows.add(_emptyLine);
      pjRows.add(
        PjRows(
          leftText: "Gratuity",
          rightText: dualPriced
              ? "\$${Helpers.formatCurrency(sale.document.saleHeader.cashGratuityTotal)}    \$${Helpers.formatCurrency(sale.document.saleHeader.gratuityTotal)}"
              : "\$${Helpers.formatCurrency(sale.document.saleHeader.gratuityTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }

    if (sale.document.saleHeader.takeOutSurchargeTotal > 0 && !useCondensed) {
      pjRows.add(
        PjRows(
          leftText: "Takeout Fees:",
          rightText: dualPriced
              ? "\$${Helpers.formatCurrency(sale.document.saleHeader.takeOutSurchargeTotal)}    \$${Helpers.formatCurrency(sale.document.saleHeader.takeOutSurchargeTotal)}"
              : "\$${Helpers.formatCurrency(sale.document.saleHeader.takeOutSurchargeTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }

    if (!useCondensed) {
      if (sale.document.saleHeader.saleFlags.contains(SaleFlags.DUAL_PRICING.index) || isMerchantDualPricing) {
        pjRows.addAll(<PjRows>[
          PjRows(
            leftText: "Total",
            rightText: "\$${Helpers.formatCurrency(cashSaleTotal)}    \$${Helpers.formatCurrency(saleTotal)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);
      } else {
        pjRows.addAll(<PjRows>[
          _thickLine,
          PjRows(
            leftText: "Total",
            rightText: "\$${Helpers.formatCurrency(dualPriced ? cashSaleTotal : saleTotal)}",
            font: 0,
            fontSize: saleCompleted ? 0 : 16,
            specOpts: 0,
          ),
        ]);
      }
      if (saleCompleted) {
        pjRows.addAll(<PjRows>[
          _thickLine,
          PjRows(
            leftText: "Amount Paid",
            rightText: "\$${Helpers.formatCurrency(amountPaid)}",
            font: 0,
            fontSize: 17,
            specOpts: 0,
          ),
        ]);
      }
    }

    int tipAmount = 0;

    for (final SaleTender tender in tenderList) {
      if ((tender.tipAmount ?? 0) > 0) {
        tipAmount += tender.tipAmount!;
      }
    }

    pjRows.addAll(<PjRows>[
      if (tipAmount > 0 && !useCondensed) ...<PjRows>[
        PjRows(
          leftText: "Tip Added",
          rightText: "\$${Helpers.formatCurrency(tipAmount)}",
          font: 0,
          fontSize: useCondensed ? 16 : 17,
          specOpts: 0,
        ),
        PjRows(
          leftText: "w/ Tips",
          rightText: "\$${Helpers.formatCurrency(tipAmount + (dualPriced ? cashSaleTotal : saleTotal))}",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
      ],
      if (!useCondensed) ...tenderPjRows,
      ...refundPjRows,
      if (useTipping) ...<PjRows>[
        _emptyLine,
        PjRows(
          leftText: "${hasGratuity ? "Additional " : ""}Tip:",
          rightText: useCondensed ? "\$____________" : "\$__________________",
          font: 0,
          fontSize: useCondensed ? 16 : 0,
          specOpts: 0,
        ),
        _emptyLine,
        _emptyLine,
        PjRows(
          leftText: "Total:",
          rightText: useCondensed ? "\$____________" : "\$__________________",
          font: 0,
          fontSize: useCondensed ? 16 : 0,
          specOpts: 0,
        ),
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          leftText: "X________________________________________",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "SIGNATURE",
          rightText: "",
          font: 0,
          fontSize: useCondensed ? 16 : 0,
          specOpts: 0,
        ),
        _emptyLine,
        _emptyLine,
      ],

      // For paid outs
      if (sale.document.saleHeader.saleFlags.contains(SaleFlags.PAID_OUT.index)) ...<PjRows>[
        if (!customerCopy) ...<PjRows>[
          PjRows(
            leftText: "X________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          PjRows(
            leftText: "SIGNATURE",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
        ],
        _emptyLine,
        PjRows(
          leftText: "Memo: ${sale.document.saleHeader.saleDescription ?? ""}",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      ],
      // For refunds
      if ((sale.document.saleRows.any((SaleRow r) => r.flags.contains(SaleRowFlags.REFUNDED.index)) ||
              sale.document.saleHeader.saleFlags.contains(SaleFlags.REFUNDED.index)) &&
          refundReceiptSignatureLine) ...<PjRows>[
        if (!customerCopy) ...<PjRows>[
          _emptyLine,
          PjRows(
            leftText: "Refund Reason: _________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "_________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          _emptyLine,
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "CUSTOMER NAME (PRINT)",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          PjRows(
            leftText: "_________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "CUSTOMER SIGNATURE",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          PjRows(
            leftText: "X________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "AUTHORIZED SIGNATURE",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          PjRows(
            leftText: "X________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
        ],
      ],
      // For sales that contain house account tender
      if (containsHouseTender) ...<PjRows>[
        if (!customerCopy) ...<PjRows>[
          _emptyLine,
          _emptyLine,
          PjRows(
            leftText: "X________________________________________",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
          PjRows(
            leftText: "HOUSE ACCOUNT SIGNATURE",
            rightText: "",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
        ],
      ],
      if (!useCondensed && showSeats)
        PjRows(
          leftText: "Your server was ${currentEmployee.document.firstName}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      PjRows(
        leftText: "Started:",
        rightText:
            "${DateFormat('yyyy-MM-dd').format(sale.created_at?.toLocal() ?? DateTime.now())} ${DateFormat('hh:mma').format(sale.created_at?.toLocal() ?? DateTime.now())}",
        font: 0,
        fontSize: 14,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Printed:",
        rightText: "${DateFormat('yyyy-MM-dd').format(DateTime.now())} ${DateFormat('hh:mma').format(DateTime.now())}",
        font: 0,
        fontSize: 14,
        specOpts: 0,
      ),
      if (showSeats)
        PjRows(
          leftText: "************* ${customerCopy ? "CUSTOMER" : "MERCHANT"} COPY *************",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
    ]);

    if ((qrCodeUrl ?? "").isNotEmpty) {
      pjRows.addAll(<PjRows>[
        PjRows(
          font: 0,
          fontSize: 20,
          specOpts: 4,
          text: qrCodeUrl,
        ),
      ]);
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      // PjRows(
      //   font: 0,
      //   fontSize: 20,
      //   specOpts: 4,
      //   text: "https://pay.round2pos.com/landing/index.html?saleNumber=${sale.sale_number}",
      // ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        saleNum: sale.sale_number,
        empName: currentUser.document.firstName,
        saleDesc: "",
        saleTotal: dualPriced ? cashSaleTotal : saleTotal,
        termNum: sale.document.saleHeader.startTerminalNumber,
        orderType: sale.document.saleHeader.orderType,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  static PrintJob buildPickUpReceipt({
    required Employee currentEmployee,
    required Employee currentUser,
    required Sale sale,
    required int cashDrawer,
    required String legacyGiftName,
    Map<int, String> terminalDescs = const <int, String>{},
    String headerText = "",
    List<String>? receiptHeader,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    final int saleTotal = sale.document.saleHeader.total;
    final int saleSubTotal = sale.document.saleHeader.subTotal;

    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 16,
        specOpts: 1,
        text: "",
      ),
      _emptyLine,
      PjRows(
        leftText: headerText,
        rightText: "#${sale.sale_number}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        rightText: DateFormat('hh:mma').format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      if ((sale.document.saleHeader.settleTerminalNumber ?? 0) != 0)
        PjRows(
          leftText:
              "Settled Terminal: ${terminalDescs[sale.document.saleHeader.settleTerminalNumber!] ?? sale.document.saleHeader.settleTerminalNumber!.toString()}",
          font: 0,
          fontSize: 14,
          specOpts: 0,
        ),
      PjRows(
        leftText: "Cash Drawer: ${cashDrawer + 1}",
        font: 0,
        fontSize: 14,
        specOpts: 0,
      ),
    ]);

    pjRows.add(_thickLine);

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "SubTotal",
        rightText: "\$${Helpers.formatCurrency(saleSubTotal)}",
        font: 0,
        fontSize: 14,
        specOpts: 0,
      ),
    ]);

    pjRows.addAll(<PjRows>[
      _thickLine,
      PjRows(
        leftText: "Total",
        rightText: "\$${Helpers.formatCurrency(saleTotal)}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
    ]);

    pjRows.addAll(
      <PjRows>[
        _emptyLine,
        PjRows(
          leftText: "EMPLOYEE SIGNATURE",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "X________________________________________",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "AUTHORIZED SIGNATURE",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "X________________________________________",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      ],
    );
    pjRows.addAll(<PjRows>[
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();
    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        saleNum: sale.sale_number,
        empName: currentUser.document.firstName,
        saleDesc: "",
        //saleTotal: dualPriced ? cashSaleTotal : saleTotal,
        termNum: sale.document.saleHeader.startTerminalNumber,
        orderType: sale.document.saleHeader.orderType,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /// Builds prep ticket for kitchen printer
  static PrintJob buildPrepTicket({
    required Sale sale,
    required String saleName,
    required Employee currentEmployee,
    required Employee currentUser,
    required int printNum,
    required PrepDevice prepDev,
    required int orderTypePrintOpt,
    required int prepPrintSizing,
    required bool printPrepFooter,
    required bool markModifiersRed,
    required bool markCommentsRed,
    required bool markToGoRed,
    required bool markOrderTypeRed,
    required bool markPromisedTimeRed,
    bool showSeats = true,
    Customer? customer,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    const int wideFont = 16;
    const int thinFont = 15;

    final bool widePrint = prepPrintSizing == PrepPrintSizing.Wide.index;

    if (sale.document.saleHeader.promisedTime != null) {
      final String timeString = Helpers.millisecondsToTimeString(
        sale.document.saleHeader.promisedTime!,
      );

      pjRows.add(
        PjRows(
          leftText: "PROMISED TIME:",
          rightText: timeString,
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
          printRed: markPromisedTimeRed ? 1 : 0,
        ),
      );
      pjRows.add(
        _thinLine,
      );
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "$saleName: #${sale.sale_number}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      if ((sale.document.saleHeader.saleDescription ?? "") != "")
        PjRows(
          leftText: "Sale Desc: ${sale.document.saleHeader.saleDescription}",
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
        ),
      if (orderTypePrintOpt == OrderTypePrintOptions.TOP_AND_BOTTOM.index || orderTypePrintOpt == OrderTypePrintOptions.TOP_ONLY.index)
        PjRows(
          leftText: "Order Type: ${OrderType.values[sale.document.saleHeader.orderType].friendlyString}",
          fontSize: thinFont,
          specOpts: 0,
          printRed: markOrderTypeRed ? 1 : 0,
        ),
      if ((sale.document.saleHeader.tableDesc ?? "") != "")
        PjRows(
          leftText: "Table: ${sale.document.saleHeader.tableDesc!}",
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
        ),
      if ((sale.document.saleHeader.customerName ?? "") != "" || customer != null)
        PjRows(
          leftText: "Customer: ${sale.document.saleHeader.customerName ?? (customer!.document.firstName + customer.document.lastName)}",
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
        ),
      if (customer != null && customer.document.phone1 != "")
        PjRows(
          leftText: "Phone: ${customer.document.phone1}",
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
        ),
      if (customer != null && customer.document.email != "")
        PjRows(
          leftText: "Email: ${customer.document.email}",
          font: 0,
          fontSize: thinFont,
          specOpts: 0,
        ),
      PjRows(
        leftText: "Server: ${currentEmployee.document.firstName} ${currentEmployee.document.lastName}",
        font: 0,
        fontSize: thinFont,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat("M/d/yy hh:mma").format(DateTime.now()),
        font: 0,
        fontSize: wideFont,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    final List<List<SaleRow>> parentLists = <List<SaleRow>>[];
    final List<List<SaleRow>> rowLists = <List<SaleRow>>[];
    final int endIdx = (sale.document.saleHeader.seatCnt ?? 1) + sale.document.saleHeader.toGoSeatCnt;

    for (int i = 0; i < endIdx; i++) {
      final int index = i >= (sale.document.saleHeader.seatCnt ?? 1) ? (sale.document.saleHeader.seatCnt ?? 1) - (i + 1) : i + 1;
      parentLists.add(
        sale.document.saleRows
            .where(
              (SaleRow row) => row.parent < 0 && ((row.seatNumber == index) || (i == 0 && row.seatNumber == 0)),
            )
            .toList(),
      );
      rowLists.add(<SaleRow>[]);
    }

    for (int i = 0; i < parentLists.length; i++) {
      for (final SaleRow pRow in parentLists[i]) {
        rowLists[i].addAll(Helpers.getChildRows(pRow, sale.document.saleRows));
      }
    }

    final List<PjRows> insertRows = <PjRows>[];

    for (final List<SaleRow> saleRows in rowLists) {
      if (saleRows.isEmpty) continue;
      final int seatNum = saleRows[0].seatNumber == 0 ? 1 : saleRows[0].seatNumber;
      final bool toGo = seatNum < 0;
      if (showSeats) {
        insertRows.addAll(<PjRows>[
          if (toGo)
            PjRows(
              text: "",
              leftText: "<<< ToGo ${seatNum.abs()} >>>",
              fontSize: wideFont,
              printRed: markToGoRed ? 1 : 0,
            )
          else
            PjRows(
              text: "",
              leftText: "--[ Seat $seatNum ]--",
              fontSize: wideFont,
            ),
        ]);
      }
      for (final SaleRow saleRow in saleRows) {
        if (!saleRow.isVisible) continue;
        if (saleRow.flags.contains(SaleRowFlags.VOIDED.index)) continue;

        int printInRed = saleRow.flags.contains(SaleRowFlags.PRINT_RED.index) ? 1 : 0;

        int indent = 0;

        if (saleRow.parent >= 0) {
          indent = Helpers.getSaleRowIndent(saleRow: saleRow, rowList: saleRows);
          if (indent > 5) indent = 5;
        }

        final bool isComment = saleRow.flags.contains(SaleRowFlags.COMMENT.index);
        if (isComment && markCommentsRed) {
          printInRed = 1;
        }
        if (markModifiersRed && saleRow.parent >= 0) {
          printInRed = 1;
        }

        insertRows.add(
          prepDev.type == 2
              ? PjRows(
                  text: "",
                  leftText: saleRow.parent < 0
                      ? saleRow.receiptDescription
                      : "${List<String>.filled(indent, isComment ? " " : "  ").join()}${isComment ? "!" : ""}${saleRow.receiptDescription}",
                  fontSize: widePrint
                      ? isComment
                          ? thinFont
                          : wideFont
                      : isComment
                          ? wideFont
                          : thinFont,
                  qty: saleRow.qty,
                  dept: saleRow.department,
                  parent: saleRow.parent,
                  printRed: printInRed,
                )
              : PjRows(
                  text: "",
                  leftText: saleRow.parent < 0
                      ? "${saleRow.qty} ${saleRow.receiptDescription}"
                      : "${List<String>.filled(indent, isComment ? " " : "  ").join()}${isComment ? "!" : ""}${saleRow.receiptDescription}",
                  fontSize: widePrint
                      ? isComment
                          ? thinFont
                          : wideFont
                      : isComment
                          ? wideFont
                          : thinFont,
                  qty: saleRow.qty,
                  dept: saleRow.department,
                  parent: saleRow.parent,
                  printRed: printInRed,
                ),
        );
      }
    }

    pjRows.addAll(insertRows);

    if (orderTypePrintOpt == OrderTypePrintOptions.TOP_AND_BOTTOM.index || orderTypePrintOpt == OrderTypePrintOptions.BOTTOM_ONLY.index) {
      final int printInRed = markOrderTypeRed ? 1 : 0;
      pjRows.addAll(<PjRows>[
        _thinLine,
        PjRows(
          leftText: OrderType.values[sale.document.saleHeader.orderType].friendlyString,
          font: 0,
          fontSize: wideFont,
          specOpts: 0,
          printRed: printInRed,
        ),
      ]);
    }

    if (printPrepFooter) {
      pjRows.addAll(<PjRows>[
        // Sale number
        PjRows(
          leftText: "Sale #${sale.sale_number}",
          fontSize: wideFont,
        ),
        if ((sale.document.saleHeader.tableDesc ?? "") != "")
          PjRows(
            leftText: "Table: ${sale.document.saleHeader.tableDesc!}",
            fontSize: wideFont,
          ),
        PjRows(
          leftText: "Server: ${currentEmployee.document.firstName} ${currentEmployee.document.lastName}",
          fontSize: wideFont,
        ),
      ]);
    }

    if (prepDev.type != 2) {
      pjRows.addAll(<PjRows>[
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _cutLine,
      ]);
    }

    if (printNum < 1) {
      for (final PjRows r in pjRows) {
        if ((r.fontSize ?? 0) > 0) r.fontSize = r.fontSize! + 1;
      }
    }

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        saleNum: sale.sale_number,
        empName: currentUser.document.firstName,
        saleDesc: "",
        saleTotal: sale.document.saleHeader.total,
        prnNum: printNum,
        termNum: sale.document.saleHeader.startTerminalNumber,
        orderType: sale.document.saleHeader.orderType,
        tableIdx: sale.document.saleHeader.tableIdx,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static PrintJob buildPrepVoided({
    required Sale sale,
    required List<SaleRow> voidedRows,
    required List<SaleRow> addedRows,
    required String headerText,
    required Employee currentEmployee,
    required int printNum,
    bool showSeats = true,
    List<SaleRow>? parentRows,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "$headerText: #${sale.sale_number}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      if ((sale.document.saleHeader.saleDescription ?? "") != "")
        PjRows(
          leftText: sale.document.saleHeader.saleDescription,
          font: 0,
          fontSize: 15,
          specOpts: 0,
        ),
      if ((sale.document.saleHeader.tableDesc ?? "") != "")
        PjRows(
          leftText: "Table: ${sale.document.saleHeader.tableDesc!}",
          font: 0,
          fontSize: 15,
          specOpts: 0,
        ),
      PjRows(
        leftText: "Server: ${currentEmployee.document.firstName} ${currentEmployee.document.lastName}",
        font: 0,
        fontSize: 15,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat("M/d/yy hh:mma").format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      _emptyLine,
      _thickLine,
    ]);

    final List<SaleRow> voided = voidedRows.where((SaleRow r) => r.isVisible).toList();
    final List<SaleRow> added = addedRows.where((SaleRow r) => r.isVisible).toList();
    final List<int> addedIdxs = added.map((SaleRow r) => r.index).toList();
    final List<int> selectedIdxs = sale.document.saleRows.where((SaleRow r) => r.selected).map((SaleRow r) => r.index).toList();

    if (voided.isNotEmpty) {
      pjRows.addAll(
        <PjRows>[
          PjRows(
            leftText: "${voided.length} ${parentRows != null ? "MOD(S) REMOVED FROM" : "ITEM(S) CANCELED"}!",
            font: 0,
            fontSize: 15,
            specOpts: 0,
          ),
          _emptyLine,
        ],
      );

      if (parentRows != null) {
        for (final SaleRow parentRow in parentRows) {
          final List<SaleRow> children = Helpers.getChildRows(parentRow, sale.document.saleRows);
          for (final SaleRow child in children) {
            if (selectedIdxs.contains(child.index) || addedIdxs.contains(child.index)) break;
            int indent = Helpers.getSaleRowIndent(
                  saleRow: child,
                  rowList: sale.document.saleRows,
                ) -
                1;
            if (indent > 5) indent = 5;
            pjRows.add(
              PjRows(
                leftText: "${List<String>.filled(indent, "  ").join()}${child.receiptDescription}",
                rightText: showSeats && indent < 1 ? "Seat ${child.seatNumber}" : "",
                font: 0,
                fontSize: 14,
                specOpts: 0,
              ),
            );
          }
          pjRows.add(_thinLine);
        }
      }

      for (final SaleRow r in voided) {
        pjRows.addAll(<PjRows>[
          PjRows(
            leftText: "${parentRows == null ? "${r.qty} " : ""}${r.receiptDescription}",
            font: 0,
            fontSize: 14,
            specOpts: 0,
          ),
        ]);
      }
    }

    pjRows.add(_emptyLine);

    if (added.isNotEmpty) {
      if (parentRows != null && voided.isEmpty) {
        pjRows.addAll(
          <PjRows>[
            PjRows(
              leftText: "${added.length} MODS ADDED TO:",
              font: 0,
              fontSize: 16,
              specOpts: 0,
            ),
            _emptyLine,
          ],
        );
        for (final SaleRow parentRow in parentRows) {
          // final List<SaleRow> children = Helpers.getChildRows(parentRow, sale.document.saleRows);
          // for (final SaleRow child in children) {
          //   int indent = Helpers.getSaleRowIndent(
          //         saleRow: child,
          //         rowList: sale.document.saleRows,
          //       ) -
          //       1;
          //   if (indent > 5) indent = 5;
          //   pjRows.add(
          //     PjRows(
          //       leftText: "${List<String>.filled(indent, "  ").join()}${child.receiptDescription}",
          //       rightText: showSeats && indent < 1 ? "${child.seatNumber < 0 ? "ToGo" : "Seat"} ${child.seatNumber.abs()}" : "",
          //       font: 0,
          //       fontSize: 14,
          //       specOpts: 0,
          //     ),
          //   );
          //   if (selectedIdxs.contains(child.index)) break;
          // }

          pjRows.add(
            PjRows(
              leftText: parentRow.receiptDescription,
              rightText: showSeats ? "${parentRow.seatNumber < 0 ? "ToGo" : "Seat"} ${parentRow.seatNumber.abs()}" : "",
              font: 0,
              fontSize: 14,
              specOpts: 0,
            ),
          );

          pjRows.add(_thinLine);
        }
      } else {
        pjRows.addAll(<PjRows>[
          _thickLine,
          PjRows(
            leftText: "${added.length} ADDED!",
            font: 0,
            fontSize: 15,
            specOpts: 0,
          ),
          _thinLine,
        ]);
      }

      int subtractIndent = 0;
      for (final SaleRow r in added) {
        int indent = Helpers.getSaleRowIndent(
          saleRow: r,
          rowList: sale.document.saleRows,
        );
        if (subtractIndent < 1) {
          subtractIndent = indent;
          indent = 0;
        } else {
          indent -= subtractIndent;
        }
        if (indent > 5) indent = 5;
        pjRows.add(
          PjRows(
            leftText: "${List<String>.filled(indent, "  ").join()}${parentRows == null ? "${r.qty} " : ""}${r.receiptDescription}",
            font: 0,
            fontSize: 14,
            specOpts: 0,
          ),
        );
      }
    }

    // pjRows.add(_thickLine);
    // pjRows.add(_emptyLine);
    // pjRows.add(_emptyLine);

    // final List<List<SaleRow>> rowLists = <List<SaleRow>>[];
    // final int endIdx = sale.document.saleHeader.seatCnt ?? 1;

    // for (int i = 0; i < endIdx; i++) {
    //   rowLists.add(
    //     sale.document.saleRows
    //         .where(
    //           (SaleRow row) => row.seatNumber == i + 1 || (i == 0 && ((row.seatNumber ?? 0) == 0)),
    //         )
    //         .toList(),
    //   );
    // }

    // final List<PjRows> insertRows = <PjRows>[];

    // for (final List<SaleRow> saleRows in rowLists) {
    //   if (saleRows.isEmpty) continue;
    //   if (showSeats) {
    //     insertRows.addAll(<PjRows>[
    //       PjRows(
    //         text: "",
    //         leftText: "---{ Seat #${(saleRows[0].seatNumber ?? 0) == 0 ? 1 : saleRows[0].seatNumber} }---",
    //         fontSize: 16,
    //       ),
    //       _emptyLine
    //     ]);
    //   }
    //   for (final SaleRow saleRow in saleRows) {
    //     if (!saleRow.isVisible) continue;
    //     if (saleRow.flags.contains(SaleRowFlags.VOIDED.index)) continue;

    //     saleRow.parent ??= -1;

    //     int indent = 0;

    //     if (saleRow.parent! >= 0) {
    //       indent = Helpers.getSaleRowIndent(saleRow: saleRow, rowList: saleRows);
    //       if (indent > 5) indent = 5;
    //     }

    //     insertRows.add(
    //       PjRows(
    //         text: "",
    //         leftText: saleRow.parent! < 0
    //             ? "${saleRow.qty} ${saleRow.receiptDescription}"
    //             : "${List<String>.filled(indent, "  ").join()} ${saleRow.flags.contains(SaleRowFlags.COMMENT.index) ? "!" : ""}${saleRow.receiptDescription}",
    //         fontSize: 15,
    //       ),
    //     );
    //   }
    // }

    pjRows.addAll(<PjRows>[
      // ...insertRows,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        saleNum: sale.sale_number,
        empName: currentEmployee.document.firstName,
        saleDesc: "",
        saleTotal: sale.document.saleHeader.total,
        prnNum: printNum,
        termNum: sale.document.saleHeader.startTerminalNumber,
        orderType: sale.document.saleHeader.orderType,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /// Builds prep notification for sale cancellation
  static PrintJob saleCancelPrep({
    required Sale sale,
    required String headerText,
    required Employee currentEmployee,
    required int printNum,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "CANCELLED ${headerText.toUpperCase()}!",
        font: 0,
        fontSize: 18,
        specOpts: 0,
      ),
      _thickLine,
      PjRows(
        leftText: "$headerText: #${sale.sale_number}",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      if ((sale.document.saleHeader.saleDescription ?? "") != "")
        PjRows(
          leftText: sale.document.saleHeader.saleDescription,
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
      PjRows(
        leftText: DateFormat("M/d/yy hh:mma").format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
    ]);

    pjRows.addAll(<PjRows>[
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        saleNum: sale.sale_number,
        empName: currentEmployee.document.firstName,
        saleDesc: "",
        saleTotal: sale.document.saleHeader.total,
        prnNum: printNum,
        termNum: sale.document.saleHeader.startTerminalNumber,
        orderType: sale.document.saleHeader.orderType,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

///////////////////////////////////////////////////// hourly report //////////////////////////////////////////////////////////////////
  static PrintJob buildReport({
    required List<HourlyReport> reports,
    required int paidOutTotal,
    required int giftRedeemedTotal,
    required int houseChargeTotal,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
    required int tipTotal,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Hourly Sales Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _thickLine,
      _emptyLine,
      _emptyLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Time  Count   Subtotal     Tax",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    final List<PjRows> reportPrjList = <PjRows>[];
    int totalTransactions = 0;
    int totalDailySales = 0;
    int totalTax = 0;
    int totalBasePrice = 0;

    int taxTotalSpacing = 16;
    int totalSpacing = 9;

    //42 CHARACTERS PER LINE

    for (final HourlyReport report in reports) {
      int timePeriodSpacing = 4;
      int transactionSpacing = 5;
      int basePriceSpacing = 9;
      int taxPriceSpacing = 6;
      int actualPriceSpacing = 9;
      report.transactions ??= 0;
      if (report.hourly_actual_price_total == null) {
        report.hourly_actual_price_total = 0;
        report.hourly_tax_total = 0;
        report.hourly_base_price_total = 0;
      }
      totalTransactions += report.transactions as int;
      totalBasePrice += report.hourly_base_price_total as int;
      totalTax += report.hourly_tax_total as int;
      totalDailySales += report.hourly_actual_price_total as int;

      final String hourRange = DateFormat('ha').format(report.bottom_hour!.toLocal());

      transactionSpacing = transactionSpacing - report.transactions.toString().length;
      taxPriceSpacing = taxPriceSpacing - report.hourly_tax_total.toString().length;
      basePriceSpacing = basePriceSpacing - report.hourly_base_price_total.toString().length;
      actualPriceSpacing = actualPriceSpacing - report.hourly_actual_price_total.toString().length;
      timePeriodSpacing = timePeriodSpacing - hourRange.length;

      if (report.hourly_actual_price_total == 0) {
        actualPriceSpacing = 6;
      }

      if (report.hourly_tax_total != 0) {
        taxPriceSpacing = taxPriceSpacing + 2;
      }

      reportPrjList.add(
        PjRows(
          qty: 1,
          text: "",
          price: 0,
          leftText: "$hourRange${" " * timePeriodSpacing}${" " * transactionSpacing}${report.transactions}",
          rightText:
              "\$${Helpers.formatCurrency(report.hourly_base_price_total ?? 0)}${" " * taxPriceSpacing}\$${Helpers.formatCurrency(report.hourly_tax_total ?? 0)}${" " * actualPriceSpacing}\$${Helpers.formatCurrency(report.hourly_actual_price_total ?? 0)}",
        ),
      );
    }

    pjRows.addAll(reportPrjList);

    if (totalDailySales > 4) {
      totalSpacing = totalSpacing - 2;
    }

    taxTotalSpacing = taxTotalSpacing - totalSpacing - totalTax.toString().length;
    totalSpacing = totalSpacing - totalDailySales.toString().length;
    pjRows.addAll(<PjRows>[
      _emptyLine,
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total: $totalTransactions",
        rightText:
            "\$${Helpers.formatCurrency(totalBasePrice)}${" " * taxTotalSpacing}\$${Helpers.formatCurrency(totalTax)}${" " * totalSpacing}\$${Helpers.formatCurrency(totalDailySales)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Paid Out:",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-House Charges:",
        rightText: "\$${Helpers.formatCurrency(houseChargeTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Gift Card Sales:",
        rightText: "\$${Helpers.formatCurrency(giftRedeemedTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Total Accountable:",
        rightText: "\$${Helpers.formatCurrency(totalDailySales - (paidOutTotal + giftRedeemedTotal + houseChargeTotal))}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  //////////////////////////////////////////////////////// monthly breakdown///////////////////////////////////////////////////
  static PrintJob buildMonthlyBreakdownReport({
    required List<MonthlyReport> reports,
    required int paidOutTotal,
    required int giftRedeemedTotal,
    required int houseChargeTotal,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Monthly Breakdown Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
      _emptyLine,
      _emptyLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Date    Count  Subtotal    Tax",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    final List<PjRows> reportPrjList = <PjRows>[];
    int totalTransactions = 0;
    int totalDailySales = 0;
    int totalTax = 0;
    int totalBasePrice = 0;

    int taxTotalSpacing = 16;
    int totalSpacing = 9;

    for (final MonthlyReport report in reports) {
      int timePeriodSpacing = 4;
      int transactionSpacing = 4;
      const int taxPriceSpacing = 3;
      int actualPriceSpacing = 6;
      report.transactions ??= 0;

      if (report.daily_actual_price_total == null) {
        report.daily_actual_price_total = 0;
        report.daily_base_price = 0;
        report.daily_tax_total = 0;
      }

      totalTransactions += report.transactions as int;
      totalBasePrice += report.daily_base_price as int;
      totalTax += report.daily_tax_total as int;
      totalDailySales += report.daily_actual_price_total as int;

      final String day = DateFormat('MMMd').format(report.bottom_day!);

      if (report.daily_actual_price_total.toString().length > 4) {
        actualPriceSpacing = actualPriceSpacing - 1;
      }
      if (report.daily_actual_price_total.toString().length > 5) {
        actualPriceSpacing = actualPriceSpacing - 2;
      }
      transactionSpacing = transactionSpacing - report.transactions.toString().length;

      timePeriodSpacing = timePeriodSpacing - day.length;
      reportPrjList.add(
        PjRows(
          qty: 1,
          text: "",
          price: 0,
          leftText: "$day ${" " * transactionSpacing}${report.transactions}",
          rightText:
              "\$${Helpers.formatCurrency(report.daily_base_price ?? 0)}${" " * taxPriceSpacing}\$${Helpers.formatCurrency(report.daily_tax_total ?? 0)}${" " * actualPriceSpacing}\$${Helpers.formatCurrency(report.daily_actual_price_total ?? 0)}",
        ),
      );
    }

    pjRows.addAll(reportPrjList);
    taxTotalSpacing = taxTotalSpacing - totalSpacing - totalTax.toString().length;
    totalSpacing = totalSpacing - totalDailySales.toString().length;
    pjRows.addAll(<PjRows>[
      _emptyLine,
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total: $totalTransactions",
        rightText:
            "\$${Helpers.formatCurrency(totalBasePrice)}${" " * taxTotalSpacing}\$${Helpers.formatCurrency(totalTax)}${" " * totalSpacing}\$${Helpers.formatCurrency(totalDailySales)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Paid Out:",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-House Charges:",
        rightText: "\$${Helpers.formatCurrency(houseChargeTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Gift Card Sales:",
        rightText: "\$${Helpers.formatCurrency(giftRedeemedTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Total Accountable:",
        rightText: "\$${Helpers.formatCurrency(totalDailySales - (giftRedeemedTotal + paidOutTotal + houseChargeTotal))}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////////////////////////////////////////daily sales by department summary ///////////////////////////////////
  static PrintJob buildSalesByDeptSumReport({
    required List<MajorReport> majorRows,
    required RxList<SalesByDeptSummaryReport> reports,
    required int taxTotal,
    required List<TaxReport> taxList,
    required int giftCardSales,
    required int giftCardCount,
    required int paidOutTotal,
    required int paidOutCount,
    required int houseChargeTotal,
    required int houseChargeCount,
    required int count,
    required int total,
    required int takeoutFees,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    int majorTotals = 0;
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Sales By Department Summary",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "Department       Units",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    for (int m = 0; m < majorRows.length; m++) {
      majorTotals += majorRows[m].major_actual_total;
      pjRows.add(
        PjRows(
          qty: 1,
          text: "",
          fontSize: 16,
          price: 0,
          leftText: majorRows[m].major_group,
          rightText: "",
        ),
      );
      for (final SalesByDeptSummaryReport report in reports) {
        if (report.major == majorRows[m].major_group) {
          int departmentSpacing = 20;
          departmentSpacing = departmentSpacing - report.department.toString().length;

          pjRows.add(
            PjRows(
              qty: 1,
              text: "",
              fontSize: 0,
              price: 0,
              leftText: '${report.department}${" " * departmentSpacing}${report.item_count}',
              rightText: "\$${Helpers.formatCurrency(report.dept_actual_price!)}",
            ),
          );
        }
      }
      pjRows.add(
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: "${majorRows[m].major_group} total:",
          rightText: "\$${Helpers.formatCurrency(majorRows[m].major_actual_total)}",
        ),
      );
      pjRows.add(_emptyLine);
    }

    const int departmentTotalSpacing = 16;
    const int giftCardSpacing = 5;
    const int paidOutSpacing = 12;
    const int houseChargeSpacing = 7;

    final int totalAccountable = (((total + taxTotal + takeoutFees) - giftCardSales) - paidOutTotal) - houseChargeTotal;

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Dept Totals:         $count ",
        rightText: "\$${Helpers.formatCurrency(majorTotals)}",
        font: 0,
        specOpts: 0,
      ),
    ]);

    // if (dualPricingTotal != 0) {
    //   pjRows.add(
    //     PjRows(
    //       leftText: "-Cash Disc. Total:${" " * dualPricingSpacing}$dualPricingCount",
    //       rightText: "\$${Helpers.formatCurrency(dualPricingTotal)}",
    //       font: 0,
    //       specOpts: 0,
    //     ),
    //   );
    // }

    for (int i = 0; i < taxList.length; i++) {
      pjRows.add(
        PjRows(
          leftText: taxList[i].description ?? "Tax ${taxList[i].index}:",
          rightText: "\$${Helpers.formatCurrency(taxList[i].tax_amount)}",
          font: 0,
          specOpts: 0,
        ),
      );
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Total:${" " * departmentTotalSpacing}$count ",
        rightText: "\$${Helpers.formatCurrency(total + taxTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Takeout Fees",
        rightText: "\$${Helpers.formatCurrency(takeoutFees)}",
        font: 0,
        specOpts: 0,
      ),
      // PjRows(
      //   leftText: "Tips:",
      //   rightText: "\$${Helpers.formatCurrency(tipTotal)}",
      //   font: 0,
      //   specOpts: 0,
      // ),
      PjRows(
        leftText: "-Paid Out:${" " * paidOutSpacing}$paidOutCount ",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-House Charges:${" " * houseChargeSpacing}$houseChargeCount ",
        rightText: "\$${Helpers.formatCurrency(houseChargeTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Gift Card Sales:${" " * giftCardSpacing}$giftCardCount",
        rightText: "\$${Helpers.formatCurrency(giftCardSales)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Total Accountable:    $count ",
        rightText: "\$${Helpers.formatCurrency(totalAccountable)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////////////////////////////////// Sales by department(full)
  static PrintJob buildSalesByRangeReport({
    required List<SalesByDeptReport> reports,
    required int total,
    required int countTotal,
    required int taxTotal,
    required List<TaxReport> taxList,
    required int houseChargeTotal,
    required int houseChargeCount,
    required int tipTotal,
    required int paidOutTotal,
    required int paidOutCount,
    required int giftCardSales,
    required int giftCardSalesCount,
    required int takeoutFees,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Sales By Department",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: '',
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        text: '',
        leftText: 'Description      Units',
        rightText: "Amount  % Sls",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);

    final List<PjRows> dailySalesByRangeReportList = <PjRows>[];
    for (int i = 0; i < reports.length; i++) {
      final SalesByDeptReport report = reports[i];

      int actualPriceTotalSpacing = 13;
      int percentageTotalSpacing = 7;
      int actualPriceSpacing = 13;
      int percentageSpacing = 7;

      actualPriceSpacing = actualPriceSpacing - report.item_actual_price.toString().length;
      percentageSpacing = percentageSpacing - report.percentage.toString().length;
      actualPriceTotalSpacing = actualPriceTotalSpacing - report.dept_actual_price.toString().length;
      percentageTotalSpacing = percentageTotalSpacing - report.percentage.toString().length;

      if (report.item_actual_price.toString().length == 6) {
        actualPriceSpacing = actualPriceSpacing - 1;
        actualPriceTotalSpacing = actualPriceTotalSpacing - 1;
      }

      if (report.dept_header != null) {
        dailySalesByRangeReportList.addAll(<PjRows>[
          PjRows(
            text: "",
            leftText: report.dept_header,
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);
      } else {
        dailySalesByRangeReportList.addAll(<PjRows>[
          PjRows(
            text: "",
            leftText: report.item_description != null
                ? ' ${report.item_description!.substring(0, report.item_description!.length > 11 ? 11 : report.item_description!.length)}'
                : 'Dept Total:',
            rightText: report.item_actual_price != null
                ? report.item_actual_price! >= 0
                    ? '${report.item_count}${" " * actualPriceSpacing}\$${Helpers.formatCurrency(report.item_actual_price!)}${" " * percentageSpacing}${report.percentage}'
                    : '-${(report.item_count!).abs()}${" " * actualPriceSpacing}\$${Helpers.formatCurrency(report.item_actual_price!)}${" " * percentageSpacing}${report.percentage}'
                : '${report.dept_count}${" " * actualPriceTotalSpacing}\$${Helpers.formatCurrency(report.dept_actual_price!)}${" " * percentageTotalSpacing}${report.percentage!.toStringAsFixed(2)}',
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);

        if (report.item_description == null && report != reports.last) {
          dailySalesByRangeReportList.add(
            PjRows(
              text: "________________________________________",
              leftText: '',
              rightText: "",
              font: 0,
              fontSize: 0,
              specOpts: 0,
            ),
          );
          dailySalesByRangeReportList.add(_emptyLine);
        }
      }
    }
    final int totalAccountable = (total + taxTotal + tipTotal + takeoutFees) - paidOutTotal - houseChargeTotal - giftCardSales;
    pjRows.addAll(dailySalesByRangeReportList);

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
    ]);
    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Dept Totals:   ",
        rightText: "\$${Helpers.formatCurrency(total)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    for (int i = 0; i < taxList.length; i++) {
      pjRows.add(
        PjRows(
          leftText: taxList[i].description != null ? "${taxList[i].description}:" : "Tax ${taxList[i].index}:",
          rightText: "\$${Helpers.formatCurrency(taxList[i].tax_amount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Net Total:   ",
        rightText: "\$${Helpers.formatCurrency(total + taxTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Takeout fees",
        rightText: "\$${Helpers.formatCurrency(takeoutFees)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),

      PjRows(
        leftText: "-Paid Out:   ",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-House Charges:   ",
        rightText: "\$${Helpers.formatCurrency(houseChargeTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Gift Card Sales:   ",
        rightText: "\$${Helpers.formatCurrency(giftCardSales)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),

      PjRows(
        leftText: "Total Accountable:   ",
        rightText: "\$${Helpers.formatCurrency(totalAccountable)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      // PjRows(
      //   leftText: "Tax Total:",
      //   rightText: "\$${Helpers.formatCurrency(taxTotal)}",
      //   font: 0,
      //   fontSize: 0,
      //   specOpts: 0,
      // ),
      // PjRows(
      //   leftText: "Total:            ${countTotal - refundCount}",
      //   rightText: "\$${Helpers.formatCurrency(total - refundTotal.abs())}",
      //   font: 0,
      //   fontSize: 0,
      //   specOpts: 0,
      // ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

////////////////////////////////////media breakdown////////////
  static PrintJob buildMediaBreakdownReport({
    required List<MediaReport> reports,
    required int paidOutCount,
    required int paidOutTotal,
    required int tipTotal,
    required int cashTotal,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Media Breakdown",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "Type          Total",
        rightText: "Tips   To. W/Tip",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    final List<PjRows> mediaBreakdownReportList = <PjRows>[];
    int mediaTotals = 0;
    int countTotal = 0;
    int mediaTip = 0;
    int mediaTotalNoTip = 0;

    for (final MediaReport report in reports) {
      int countSpacingAmount = 14;
      int countSpacingTip = 13;
      final int total = report.amount + report.tips;
      countSpacingAmount =
          (countSpacingAmount - Helpers.mediaTypeAsString(PaymentMediaType.values[int.parse(report.media)]).length) - report.amount.toString().length;
      countSpacingTip = countSpacingTip - total.toString().length - report.tips.toString().length;
      countTotal += report.count;
      mediaTotals += report.amount + report.tips;
      mediaTip += report.tips;
      mediaTotalNoTip += report.amount;

      mediaBreakdownReportList.addAll(
        <PjRows>[
          (PjRows(
            qty: 1,
            text: "",
            fontSize: 0,
            price: 0,
            leftText:
                "${Helpers.mediaTypeAsString(PaymentMediaType.values[int.parse(report.media)])}(${report.count})${" " * countSpacingAmount}\$${Helpers.formatCurrency(report.amount)}",
            rightText: "\$${Helpers.formatCurrency(report.tips)}${" " * countSpacingTip}\$${Helpers.formatCurrency(report.amount + report.tips)}",
          )),
        ],
      );
    }
    int countSpacingAmount = 10;
    int countSpacingTip = 13;
    countSpacingAmount = countSpacingAmount - countTotal.toString().length - mediaTotalNoTip.toString().length;
    countSpacingTip = countSpacingTip - mediaTotals.toString().length - mediaTip.toString().length;

    pjRows.addAll(mediaBreakdownReportList);

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Media($countTotal):${" " * countSpacingAmount}\$${Helpers.formatCurrency(mediaTotalNoTip)}",
        rightText: "\$${Helpers.formatCurrency(mediaTip)}${" " * countSpacingTip}\$${Helpers.formatCurrency(mediaTotals)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      PjRows(
        leftText: "Cash Total:",
        rightText: "\$${Helpers.formatCurrency(cashTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-C/C Tips:",
        rightText: "\$${Helpers.formatCurrency(tipTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Paid Out:             $paidOutCount ",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Adjusted Cash:      ",
        rightText: "\$${Helpers.formatCurrency((cashTotal - tipTotal) - paidOutTotal)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////////////////////////////////// Gift card issue receipt
  static PrintJob buildGiftIssue({
    required Employee currentEmployee,
    required SaleRow saleRow,
    required int saleNumber,
    List<String>? receiptHeader,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    final List<PjRows> storeHeaderPjRows = <PjRows>[];

    for (final String headerRow in receiptHeader ?? <String>[]) {
      final PjRows storeHeader = PjRows(
        text: headerRow,
        font: 0,
        fontSize: 0,
        specOpts: 1,
      );
      storeHeaderPjRows.add(storeHeader);
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 16,
        specOpts: 1,
        text: "",
      ),
      ...storeHeaderPjRows,
      _emptyLine,
      PjRows(
        leftText: "Gift Issued",
        rightText: "#$saleNumber",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        rightText: DateFormat('hh:mma').format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      _thickLine,
      PjRows(
        qty: 1,
        text: "",
        price: saleRow.originalPrice,
        leftText: saleRow.receiptDescription,
        rightText: "\$${Helpers.formatCurrency(saleRow.originalPrice)}",
      ),
      _thickLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
        empName: currentEmployee.document.firstName,
        saleDesc: saleRow.receiptDescription,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////job_code_breakdown report////////////
  static PrintJob buildJobCodeBreakdownReport({
    required List<Report> reports,
    required int wageTotal,
    required double hourTotal,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Labor by Job Code",
      ),
      _emptyLine,
      PjRows(
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        rightText: DateFormat('hh:mma').format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "Job Code            Wages",
        rightText: "Hours",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    final List<PjRows> jobCodeBreakdownReportList = <PjRows>[];

    int totalsCountSpacing = 16;
    totalsCountSpacing = totalsCountSpacing - hourTotal.toString().length;

    for (final Report report in reports) {
      int countSpacing = 16;

      countSpacing = countSpacing - report.hours!.toString().length;

      jobCodeBreakdownReportList.addAll(
        <PjRows>[
          PjRows(
            qty: 1,
            text: "",
            fontSize: 0,
            price: 0,
            leftText: '${report.title}',
            rightText: '\$${Helpers.formatCurrency(report.wages!)}${" " * countSpacing}${report.hours!}',
          ),
        ],
      );
    }

    pjRows.addAll(jobCodeBreakdownReportList);

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Totals:",
        rightText: "\$${Helpers.formatCurrency(wageTotal)}${" " * totalsCountSpacing}$hourTotal",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

/////////////////////////////////Paid Out Report//////////////////////////
  static PrintJob buildPaidOutReport({
    required List<PaidOutGroup> paidOutReports,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    final List<PjRows> paidOutReportList = <PjRows>[];
    int paidOutTotals = 0;

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Paid Out Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "Reason Code",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    // memoSpacing = (memoSpacing - mediaType.length) - paidOutReports.count.toString().length;

    for (final PaidOutGroup paidOutGroup in paidOutReports) {
      // countTotal += report.count!;
      paidOutTotals += paidOutGroup.total;

      paidOutReportList.addAll(
        <PjRows>[
          (PjRows(
            qty: 1,
            text: "",
            fontSize: 0,
            price: 0,
            leftText: paidOutGroup.reasonCode,
            rightText: "\$${Helpers.formatCurrency(paidOutGroup.total)}",
          )),
        ],
      );
    }

    pjRows.addAll(paidOutReportList);

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total:",
        rightText: "\$${Helpers.formatCurrency(paidOutTotals)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////////////////Gift Summary//////////////////////////
  static PrintJob buildGiftReport({
    required int giftIssued,
    required int giftRedeemed,
    required List<GiftSummaryReport> giftRows,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Gift Summary Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Sale #       Card",
        rightText: "issue    redeem",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    for (final GiftSummaryReport row in giftRows) {
      row.giftType == GiftType.ISSUE.index
          ? pjRows.add(
              PjRows(
                leftText: "${row.saleNumber} ${row.description}",
                rightText: "\$${Helpers.formatCurrency(row.amount)}         ",
                font: 0,
                specOpts: 0,
              ),
            )
          : pjRows.add(
              PjRows(
                leftText: "${row.saleNumber} ${row.description}",
                rightText: "\$${Helpers.formatCurrency(row.amount)}",
                font: 0,
                specOpts: 0,
              ),
            );
    }

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total:",
        rightText: "\$${Helpers.formatCurrency(giftIssued)}   \$${Helpers.formatCurrency(giftRedeemed)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////TimeCard////////////////////////////////
  ///
  static PrintJob buildTimeCard({
    required Map<String, dynamic> timeCardTotals,
    required List<dynamic> wagesList,
    required List<TimeCardPunch> punchList,
    required String startDate,
    required String endDate,
    required String now,
    required Employee currentEmployee,
    required List<SystemSettingJsonRecordJobCode> jobRecord,
    required List<Break> breakList,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      PjRows(
        specOpts: 2,
        text: "SL",
      ),
      _emptyLine,
      PjRows(
        text: "Employee Timecard Report for",
      ),
      PjRows(
        leftText:
            "${(currentEmployee.document.firstName ?? "").toUpperCase()} ${(currentEmployee.document.lastName ?? "").toUpperCase()} (${currentEmployee.id})",
        rightText: "",
      ),
      PjRows(
        leftText: "From $startDate",
        rightText: "",
      ),
      PjRows(
        leftText: "To $endDate",
        rightText: "",
      ),
      PjRows(
        leftText: "as of $now",
        rightText: "",
      ),
      _emptyLine,
      PjRows(
        leftText: "In-Date Time Job",
        rightText: "Out-Date Time Total",
      ),
      _thickLine,
    ]);

    for (final TimeCardPunch punch in punchList) {
      final String jobTitle = jobRecord
          .firstWhere(
            (SystemSettingJsonRecordJobCode code) => code.index == punch.jobCode,
          )
          .title;

      final String breakDesc = punch.isBreak
          ? breakList
              .firstWhere(
                (Break bk) => bk.idx == (punch.breakIdx ?? 0),
              )
              .desc
          : "";
      pjRows.addAll(<PjRows>[
        if (punch.isBreak)
          PjRows(
            text: "",
            leftText:
                "${punch.punchIn != "" ? "[Break Start]" : "N/A   N/A   "} ${breakDesc.length > 8 ? "${breakDesc.substring(0, 6)}..." : breakDesc}",
            rightText: punch.punchOut != "" ? "[Break End]  ${punch.punchIn != "" ? punch.total : "N/A"}" : "N/A   N/A   N/A",
          )
        else
          PjRows(
            text: "",
            leftText:
                "${punch.punchIn != "" ? "${punch.punchIn.substring(5, 10)}  ${DateFormat("hh:mma").format(DateTime.parse(punch.punchIn)).substring(0, 6)} " : "N/A   N/A   "}${jobTitle.length > 8 ? "${jobTitle.substring(0, 6)}..." : jobTitle}",
            rightText: punch.punchOut != ""
                ? "${punch.punchOut.substring(5, 10)}  ${DateFormat("hh:mma").format(DateTime.parse(punch.punchOut)).substring(0, 6)}  ${punch.punchIn != "" ? punch.total : "N/A"}"
                : "N/A   N/A   N/A",
          ),
      ]);
    }

    pjRows.addAll(
      <PjRows>[
        _thickLine,
        PjRows(
          leftText: "Total Hours",
          rightText: timeCardTotals["totalHrs"] != null && timeCardTotals["totalHrs"] != "" ? timeCardTotals["totalHrs"] as String : "N/A",
          fontSize: 2,
        ),
        _emptyLine,
        PjRows(
          text: "LABOR SUMMARY",
          specOpts: 1,
        ),
        _emptyLine,
        PjRows(
          leftText: "Job Code     Reg Hours   OT Hrs     Meal",
          rightText: "",
        ),
        _thickLine,
      ],
    );

    for (final dynamic jc in wagesList) {
      final String jobTitle = jobRecord
          .firstWhere(
            (SystemSettingJsonRecordJobCode code) => code.index == jc["jobCode"],
          )
          .title;
      pjRows.addAll(<PjRows>[
        PjRows(
          text: "",
          // ignore: prefer_interpolation_to_compose_strings
          leftText: "${jobTitle.length > 8 ? "${jobTitle.substring(0, 6)}..." : jobTitle}    " +
              "${jc["regHrs"] != "" ? jc["regHrs"] as String : "N/A"}        " +
              "${jc["OTHrs"] != "" ? jc["OTHrs"] as String : "N/A"}        " +
              (jc["mealHrs"] != "" ? jc["mealHrs"] as String : "N/A"),
          rightText: "",
          font: 0,
          specOpts: 0,
        ),
      ]);
    }

    pjRows.addAll(
      <PjRows>[
        _thickLine,
        PjRows(
          text: "",
          // ignore: prefer_interpolation_to_compose_strings
          leftText: "Total:       " +
              "${timeCardTotals["regHrs"] != "" && timeCardTotals["regHrs"] != null ? timeCardTotals["regHrs"] as String : "N/A"}        " +
              "${timeCardTotals["OTHrs"] != "" && timeCardTotals["OTHrs"] != null ? timeCardTotals["OTHrs"] as String : "N/A"}        " +
              "${timeCardTotals["mealHrs"] != "" && timeCardTotals["mealHrs"] != null ? timeCardTotals["mealHrs"] as String : "N/A"}        ",
          rightText: "",
        ),
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 2,
          text: "R2",
        ),
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 3,
          text: "",
        ),
      ],
    );

    final PrintJob printJob = PrintJob.empty();
    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );
    return printJob;
  }

  /////////////////////TimeCardAllEmps////////////////////////////////
  ///
  static PrintJob printTimeCardAllEmps({
    required List<Timecard> reportData,
    required List<Employee> currentEmployeeList,
    required String startDate,
    required String endDate,
    required List<SystemSettingJsonRecordJobCode> jobCodesList,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Employee Timecard Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $startDate",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $endDate",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _thickLine,
    ]);

    for (final Timecard timecard in reportData) {
      final Employee currEmp = timecard.employee;
      final Map<DateTime, List<TimecardPunch>> weeklyGroupedPunches = Helpers.groupPunchesByCalendarWeek(timecard.timecardPunches);
      int overallTotalMinutes = 0;

      // Employee Header
      pjRows.addAll(<PjRows>[
        _emptyLine,
        PjRows(
          leftText: "${currEmp.document.firstName ?? ""} ${currEmp.document.lastName ?? ""}",
          fontSize: 2,
        ),
        _emptyLine,
      ]);

      int weekIndex = 1;
      for (final DateTime weekStart in weeklyGroupedPunches.keys) {
        final List<TimecardPunch> punches = weeklyGroupedPunches[weekStart]!;
        final int weeklyTotalMinutes = punches.fold(0, (int total, TimecardPunch punch) {
          if (punch.total.isEmpty) return total;
          final List<String> split = punch.total.split(":");
          if (split.length != 2) return total;
          final int hours = int.tryParse(split[0]) ?? 0;
          final int minutes = int.tryParse(split[1]) ?? 0;
          return total + (hours * 60) + minutes;
        });

        // Weekly Header
        pjRows.add(
          PjRows(
            leftText: "Week $weekIndex",
            fontSize: 1,
            font: 0,
          ),
        );

        // Punch Rows
        for (final TimecardPunch punch in punches) {
          // ignore: always_specify_types
          final String jobCodeTitle = jobCodesList.firstWhereOrNull((jc) => jc.index == punch.jobCode)?.title ?? "N/A";
          pjRows.add(
            PjRows(
              leftText:
                  "${punch.punchIn.isNotEmpty ? "${punch.punchIn.substring(5, 10)}  ${DateFormat("hh:mma").format(DateTime.parse(punch.punchIn)).substring(0, 6)} " : "N/A   N/A   "}${jobCodeTitle.length > 8 ? "${jobCodeTitle.substring(0, 6)}..." : jobCodeTitle}",
              rightText: punch.punchOut.isNotEmpty
                  ? "${punch.punchOut.substring(5, 10)}  ${DateFormat("hh:mma").format(DateTime.parse(punch.punchOut)).substring(0, 6)}  ${punch.total.isNotEmpty ? punch.total : "N/A"}"
                  : "N/A   N/A   N/A",
            ),
          );
        }

        // Weekly Total
        pjRows.add(
          PjRows(
            leftText: "Week $weekIndex Total:",
            rightText: "${weeklyTotalMinutes ~/ 60}:${(weeklyTotalMinutes % 60).toString().padLeft(2, '0')}",
            fontSize: 1,
          ),
        );

        overallTotalMinutes += weeklyTotalMinutes;
        weekIndex++;
      }

      // Overall Total
      pjRows.addAll(<PjRows>[
        _thinLine,
        PjRows(
          leftText: "Two-week Total:",
          rightText: "${overallTotalMinutes ~/ 60}:${(overallTotalMinutes % 60).toString().padLeft(2, '0')}",
          fontSize: 1,
        ),
      ]);
    }

    pjRows.addAll(
      <PjRows>[
        _thickLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 2,
          text: "R2",
        ),
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 3,
          text: "",
        ),
      ],
    );

    final PrintJob printJob = PrintJob.empty();
    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );
    return printJob;
  }

  /////////////////////wagesAllEmps////////////////////////////////
  ///
  static PrintJob printWagesAllEmps({
    required List<Timecard> reportData,
    required List<Employee> currentEmployeeList,
    required String startDate,
    required String endDate,
    required List<SystemSettingJsonRecordJobCode> jobCodesList,
    required JsonRecordReports reportRecord,
    // Organized emplyee tips breakdown by employee ID
    required Map<int, EmployeeTipBreakdown> empTips,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    String hoursString(String hours) {
      if (reportRecord.document.wageDecimalHours) {
        return Helpers.hoursAndMinToDecimal(hours);
      }
      return hours;
    }

    pjRows.addAll(<PjRows>[
      PjRows(
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Employee Wage Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Report Period: from ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(startDate))}",
        rightText: "",
      ),
      PjRows(
        leftText: "               to   ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(endDate))}",
        rightText: "",
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
      ),
      _emptyLine,
    ]);

    for (int i = 0; i < reportData.length; i++) {
      //current employee interation id
      final String currentEmpID = reportData[i].employee.id.toString();
      //current employee wages
      final List<TimecardWage> wages = reportData[i].timecardWages;

      final RxInt totalGrossPay = 0.obs;

      final EmployeeTipBreakdown? tips = empTips[reportData[i].employee.id];

      pjRows.addAll(<PjRows>[
        PjRows(
          leftText: "${currentEmployeeList.firstWhere((Employee element) => element.id == int.parse(currentEmpID)).employee_full_name}",
          rightText: "ID: ${currentEmployeeList.firstWhere((Employee element) => element.id == int.parse(currentEmpID)).id}",
          fontSize: 17,
        ),
      ]);
      for (int p = 0; p < wages.length; p++) {
        final TimecardWage wage = wages[p];

        final String grossPay = Helpers.calculateGrossPay(
          wage.regWages.toString(),
          wage.otWages.toString(),
        );
        totalGrossPay.value += int.parse(grossPay);
        final String jobCodeTitle = jobCodesList
            .firstWhere(
              (SystemSettingJsonRecordJobCode element) => element.index == int.parse(wage.jobCode.toString()),
            )
            .title;
        pjRows.addAll(
          <PjRows>[
            _emptyLine,
            PjRows(leftText: jobCodeTitle, fontSize: 16),
            PjRows(
              leftText: "Reg wages:     ${hoursString(wage.regHrs)}     ${Helpers.formatCurrency(int.parse(wage.regRate.toString()))}",
              rightText: Helpers.formatCurrency(
                int.parse(wage.regWages.toString()),
              ),
            ),
            PjRows(
              leftText: "OT Wages:      ${hoursString(wage.otHrs)}     ${Helpers.formatCurrency(int.parse(wage.otRate.toString()))}",
              rightText: Helpers.formatCurrency(int.parse(wage.otWages.toString())),
            ),
            PjRows(leftText: "Meal Hours:    ${hoursString(wage.mealHrs)}"),
            PjRows(
              leftText: 'Gross Pay:',
              rightText: Helpers.formatCurrency(int.parse(grossPay)),
            ),
            if (wage == wages.last) ...<PjRows>[
              _thickLine,
              PjRows(
                leftText: "Totals:",
                rightText: Helpers.formatCurrency(totalGrossPay.value),
                fontSize: 15,
              ),
              _emptyLine,
            ],
          ],
        );
      }
      if (tips != null) {
        pjRows.addAll(
          <PjRows>[
            PjRows(leftText: "Tip/Gratuity", fontSize: 16),
            PjRows(
              leftText: "Tips:",
              rightText: Helpers.formatCurrency(tips.tip_amount),
            ),
            PjRows(
              leftText: "Gratuity:",
              rightText: Helpers.formatCurrency(tips.grat_amount),
            ),
            PjRows(
              leftText: 'Tip + Grat:',
              rightText: Helpers.formatCurrency(tips.tip_amount + tips.grat_amount),
            ),
            PjRows(
              leftText: "w/ Gross:",
              rightText: Helpers.formatCurrency(totalGrossPay.value + tips.tip_amount + tips.grat_amount),
              fontSize: 15,
            ),
            _emptyLine,
          ],
        );
      }
    }

    pjRows.addAll(
      <PjRows>[
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 2,
          text: "R2",
        ),
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 3,
          text: "",
        ),
      ],
    );

    final PrintJob printJob = PrintJob.empty();
    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );
    return printJob;
  }

  static PrintJob buildEmployeeSalesReport({
    required List<Sale> saleList,
    required String startDate,
    required String endDate,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Electronic Journal",
      ),
      _emptyLine,
      PjRows(
        leftText: "Report Period: from ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(startDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(endDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
    ]);

    /////////////Business  Rows////////////////
    ///
    ///
    pjRows.addAll(<PjRows>[
      _thickLine,
      PjRows(
        leftText: "Sale   Server ",
        rightText: "Time             Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);

    for (final Sale sale in saleList) {
      int space = 10;
      if (sale.document.saleHeader.total < 100 && sale.document.saleHeader.total >= 0) {
        space = 7;
      } else {
        space = 10 - sale.document.saleHeader.total.toString().length;
      }

      pjRows.addAll(<PjRows>[
        PjRows(
          leftText: "${sale.document.saleHeader.saleNumber}     ${sale.document.saleHeader.settleEmployeeNumber}",
          rightText:
              "${DateFormat('M/d/yy hh:mma').format(sale.end_at!.toLocal())}${" " * space}\$${Helpers.formatCurrency(sale.document.saleHeader.total)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      ]);
    }
    pjRows.addAll(
      <PjRows>[
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 2,
          text: "R2",
        ),
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        _emptyLine,
        PjRows(
          specOpts: 3,
          text: "",
        ),
      ],
    );
    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////Revenue Report////////////////////////////////
  static PrintJob builRevenueReport({
    required int grossTotal,
    required int refundsTotal,
    required int discountTotal,
    required int netTotal,
    required int taxTotal,
    required List<TaxReport> taxList,
    required int giftCardsIssued,
    required int paidOutTotal,
    required int houseChargeTotal,
    required int totalAccountable,
    required int dualPricingTotal,
    required int tipTotal,
    required int gratTotal,
    required String scope,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Revenue Report($scope)",
      ),
      _emptyLine,
      PjRows(
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 17,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        rightText: DateFormat('hh:mma').format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    pjRows.addAll(<PjRows>[
      PjRows(
        text: "",
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);

    /////////////Business  Rows////////////////
    ///

    pjRows.addAll(<PjRows>[
      PjRows(
        qty: 1,
        text: "",
        fontSize: 0,
        price: 0,
        leftText: 'Gross Total:',
        rightText: "\$${Helpers.formatCurrency(grossTotal)}",
      ),
      _thickLine,
      PjRows(
        qty: 1,
        text: "",
        fontSize: 0,
        price: 0,
        leftText: '-Refund Total:',
        rightText: "\$${Helpers.formatCurrency(refundsTotal.abs())}",
      ),
      PjRows(
        qty: 1,
        text: "",
        fontSize: 0,
        price: 0,
        leftText: '-Discount Total:',
        rightText: "\$${Helpers.formatCurrency(discountTotal)}",
      ),
      PjRows(
        qty: 1,
        text: "",
        fontSize: 0,
        price: 0,
        leftText: '-Cash Disc. Total:',
        rightText: "\$${Helpers.formatCurrency(dualPricingTotal)}",
      ),
      _thickLine,
      PjRows(
        qty: 1,
        text: "",
        fontSize: 0,
        price: 0,
        leftText: 'Net Total:',
        rightText: "\$${Helpers.formatCurrency(netTotal - dualPricingTotal)}",
      ),
      _thickLine,
    ]);

    for (int i = 0; i < taxList.length; i++) {
      pjRows.add(
        PjRows(
          qty: 1,
          fontSize: 0,
          price: 0,
          leftText: taxList[i].description != null ? "${taxList[i].description}:" : "Tax ${taxList[i].index}:",
          rightText: "\$${Helpers.formatCurrency(taxList[i].tax_amount)}",
        ),
      );
    }

    pjRows.addAll(
      <PjRows>[
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: 'Tips:',
          rightText: "\$${Helpers.formatCurrency(tipTotal)}",
        ),
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: 'Gratuity:',
          rightText: "\$${Helpers.formatCurrency(gratTotal)}",
        ),
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: '-Paid Out Total:',
          rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        ),
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: '-House Charges:',
          rightText: "\$${Helpers.formatCurrency(houseChargeTotal)}",
        ),
        PjRows(
          qty: 1,
          text: "",
          fontSize: 0,
          price: 0,
          leftText: '-Gift Card Sales:',
          rightText: "\$${Helpers.formatCurrency(giftCardsIssued)}",
        ),
      ],
    );

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total:",
        rightText: "\$${Helpers.formatCurrency(((totalAccountable + tipTotal) + gratTotal) - dualPricingTotal)}",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  static PrintJob buildServerPrintJob({
    required List<EmployeeRecord> empRecords,
    required String scope,
    required String startDate,
    required String endDate,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Server Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Report Period: from ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(startDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(endDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
    ]);

    /////////////Business  Rows////////////////
    ///
    ///
    for (final EmployeeRecord record in empRecords) {
      /// Department variables
      final RxInt deptPriceTotal = 0.obs;
      final RxInt deptCountTotal = 0.obs;
      const int deptTotalSpacing = 10;
      final RxInt taxTotal = 0.obs;

      /// Media variables
      final RxInt mediaTotal = 0.obs;
      final RxInt mediaCountTotal = 0.obs;
      final RxInt mediaTipTotalServer = 0.obs;

      ///Adjusted Cash Total Variables
      final RxInt cashTotal = 0.obs;
      final RxInt creditTipTotal = 0.obs;
      final RxInt creditGratTotal = 0.obs;
      final RxInt adjustedCashTotal = 0.obs;

      ///Media Spacing variables
      int mediaTotalSpacing = 16;
      int mediaCountSpacing = 1;

      final RxInt gratTotal = 0.obs;
      final RxInt tipTotal = 0.obs;

      /// Stats variables
      const int guestSpacing = 12;
      const int checkSpacing = 12;
      const int durationSpacing = 8;
      const int refundedSpacing = 9;
      const int cancelledSpacing = 8;
      const int noSalesSpacing = 15;

      pjRows.addAll(<PjRows>[
        PjRows(
          text: "${record.employee.document.lastName}, ${record.employee.document.firstName}",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        PjRows(
          text: "ID:${record.employee.id}",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "Sales by Department",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Department         Count",
          rightText: "Total",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
      ]);

      for (int i = 0; i < record.salesByDeptList.length; i++) {
        /// Count Spacing Variable
        int deptCountSpacing = 15;
        deptPriceTotal.value += record.salesByDeptList[i].actual_price;
        deptCountTotal.value += record.salesByDeptList[i].count;
        deptCountSpacing = deptCountSpacing - record.salesByDeptList[i].count.toString().length;

        pjRows.addAll(<PjRows>[
          PjRows(
            leftText: record.salesByDeptList[i].department,
            rightText:
                "${record.salesByDeptList[i].count}${" " * deptCountSpacing}\$${Helpers.formatCurrency(record.salesByDeptList[i].actual_price)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);
      }

      pjRows.addAll(<PjRows>[
        _thickLine,
        PjRows(
          leftText: "Dept Total:${" " * deptTotalSpacing}${deptCountTotal.value}",
          rightText: "\$${Helpers.formatCurrency(deptPriceTotal.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        // if (record.cashDiscAmount != 0)
        //   PjRows(
        //     leftText: "-Cash disc. total:",
        //     rightText: "\$${Helpers.formatCurrency(record.cashDiscAmount)}",
        //     font: 0,
        //     fontSize: 0,
        //     specOpts: 0,
        //   ),
      ]);

      for (int i = 0; i < record.taxRows.length; i++) {
        taxTotal.value += record.taxRows[i].tax_amount;
        pjRows.add(
          PjRows(
            leftText: "${record.taxRows[i].description}:",
            rightText: "\$${Helpers.formatCurrency(record.taxRows[i].tax_amount)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        );
      }

      pjRows.addAll(<PjRows>[
        PjRows(
          leftText: "Takeout Fee:",
          rightText: "\$${Helpers.formatCurrency(record.takeoutFeesTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Total:",
          rightText: "\$${Helpers.formatCurrency(deptPriceTotal.value + taxTotal.value + record.takeoutFeesTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "Media Breakdown",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Media Type     Total",
          rightText: "Tip    To. w Tip",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
      ]);

      for (int i = 0; i < record.mediaBreakdownList.length; i++) {
        mediaCountSpacing = 15;
        mediaTotalSpacing = 8;
        final RxInt tipAmount = 0.obs;
        final RxInt creditTipLength = 0.obs;

        mediaTotal.value += record.mediaBreakdownList[i].net_total;
        mediaTipTotalServer.value += record.tipBreakdownList[i].tip_amount;
        mediaCountTotal.value += record.mediaBreakdownList[i].count;

        if (Helpers.mediaTypeAsString(
              PaymentMediaType.values.firstWhere(
                (PaymentMediaType element) => element.index == int.parse(record.mediaBreakdownList[i].media!),
              ),
            ) ==
            "Cash") {
          cashTotal.value = record.mediaBreakdownList[i].net_total;
        }
        for (final EmployeeTipBreakdown x in record.tipBreakdownList) {
          if (record.mediaBreakdownList[i].media == x.tender_media.toString()) {
            tipAmount.value = x.tip_amount;
          }
          if (Helpers.mediaTypeAsString(
                PaymentMediaType.values.firstWhere(
                  (PaymentMediaType element) => element.index == x.tender_media,
                ),
              ) ==
              "Credit") {
            creditTipTotal.value = x.tip_amount;
            creditTipLength.value = x.tip_amount.toString().length;
          }
        }
        if (Helpers.mediaTypeAsString(
              PaymentMediaType.values.firstWhere(
                (PaymentMediaType element) => element.index == int.parse(record.mediaBreakdownList[i].media!),
              ),
            ) ==
            "Credit") {
          mediaTotalSpacing = mediaTotalSpacing - record.mediaBreakdownList[i].net_total.toString().length;
          mediaCountSpacing =
              mediaCountSpacing - mediaTotalSpacing - record.mediaBreakdownList[i].net_total.toString().length - creditTipLength.value;
        } else {
          mediaTotalSpacing = mediaTotalSpacing - record.mediaBreakdownList[i].net_total.toString().length;
          mediaCountSpacing = mediaCountSpacing - mediaTotalSpacing - record.mediaBreakdownList[i].net_total.toString().length;
        }
        pjRows.addAll(<PjRows>[
          PjRows(
            text: "",
            leftText: record.mediaBreakdownList[i].media == "7"
                ? "Legacy:(${record.mediaBreakdownList[i].count})"
                : "${Helpers.mediaTypeAsString(
                    PaymentMediaType.values.firstWhere(
                      (PaymentMediaType element) => element.index == int.parse(record.mediaBreakdownList[i].media!),
                    ),
                  )}:(${record.mediaBreakdownList[i].count})",
            rightText:
                "\$${Helpers.formatCurrency(record.mediaBreakdownList[i].net_total)}${" " * mediaCountSpacing}\$${Helpers.formatCurrency(tipAmount.value)}${" " * mediaTotalSpacing}\$${Helpers.formatCurrency(record.mediaBreakdownList[i].net_total + tipAmount.value)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);
      }
      mediaCountSpacing = 8;
      mediaTotalSpacing = 7;
      mediaCountSpacing = mediaCountSpacing - mediaTipTotalServer.value.toString().length;
      mediaTotalSpacing = mediaTotalSpacing - mediaTotal.value.toString().length;
      for (int i = 0; i < record.tipBreakdownList.length; i++) {
        if (Helpers.mediaTypeAsString(
              PaymentMediaType.values.firstWhere(
                (PaymentMediaType element) => element.index == record.tipBreakdownList[i].tender_media,
              ),
            ) ==
            "Credit") {
          creditGratTotal.value = record.tipBreakdownList[i].grat_amount;
        }
      }
      adjustedCashTotal.value = cashTotal.value - creditTipTotal.value - creditGratTotal.value;
      pjRows.addAll(<PjRows>[
        _thickLine,
        PjRows(
          text: "",
          leftText: "Total:(${mediaCountTotal.value})    ",
          rightText:
              "\$${Helpers.formatCurrency(mediaTotal.value)}${" " * mediaCountSpacing}\$${Helpers.formatCurrency(mediaTipTotalServer.value)}${" " * mediaTotalSpacing}\$${Helpers.formatCurrency(mediaTotal.value + mediaTipTotalServer.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "",
          leftText: "Cash Total:",
          rightText: "\$${Helpers.formatCurrency(cashTotal.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "",
          leftText: "Credit Tip Total:",
          rightText: "\$${Helpers.formatCurrency(creditTipTotal.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "",
          leftText: "Credit Gratuity Total:",
          rightText: "\$${Helpers.formatCurrency(creditGratTotal.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "",
          leftText: "Adjusted Cash:",
          rightText: "\$${Helpers.formatCurrency(adjustedCashTotal.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "Tip Breakdown",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Media Type     Grat",
          rightText: " Tip       Total",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
      ]);
      for (int i = 0; i < record.tipBreakdownList.length; i++) {
        int col1Spacing = 18;

        tipTotal.value += record.tipBreakdownList[i].tip_amount;
        gratTotal.value += record.tipBreakdownList[i].grat_amount;
        col1Spacing = (col1Spacing -
                Helpers.mediaTypeAsString(
                  PaymentMediaType.values.firstWhere(
                    (PaymentMediaType element) => element.index == record.tipBreakdownList[i].tender_media,
                  ),
                ).length) -
            Helpers.formatCurrency(record.tipBreakdownList[i].tip_amount).length;

        pjRows.addAll(<PjRows>[
          PjRows(
            text: "",
            leftText: "${Helpers.mediaTypeAsString(
              PaymentMediaType.values.firstWhere(
                (PaymentMediaType element) => element.index == record.tipBreakdownList[i].tender_media,
              ),
            )}${" " * col1Spacing}\$${Helpers.formatCurrency(record.tipBreakdownList[i].grat_amount)}",
            rightText:
                "\$${Helpers.formatCurrency(record.tipBreakdownList[i].tip_amount)}${"       "}\$${Helpers.formatCurrency(record.tipBreakdownList[i].tip_amount)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ]);
      }
      pjRows.addAll(<PjRows>[
        _thickLine,
        _emptyLine,
        PjRows(
          text: "Statistics",
          leftText: "",
          rightText: "",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        _thickLine,
        PjRows(
          leftText: "#Guest \$AVG${" " * guestSpacing}${record.empStats.guest_count}",
          rightText: "\$${Helpers.formatCurrency(record.empStats.guest_average)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "#Check \$AVG${" " * checkSpacing}${record.empStats.check_count}",
          rightText: "\$${Helpers.formatCurrency(record.empStats.check_average)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "AVG Turn Time${" " * durationSpacing}${record.empStats.duration}",
          rightText: "-",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Refunded Sales${" " * refundedSpacing}${record.empStats.refunded_count}",
          rightText: "\$${Helpers.formatCurrency(record.empStats.refunded_amount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Cancelled Sales${" " * cancelledSpacing}${record.empStats.cancel_sale_count}",
          rightText: "\$${Helpers.formatCurrency(record.empStats.cancel_sale_amount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "No Sales${" " * noSalesSpacing}${record.empStats.no_sale_count}",
          rightText: "-",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
        _emptyLine,
      ]);
    }

    pjRows.addAll(<PjRows>[
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  static PrintJob printCashierReport({
    required String cashier,
    required String periodStart,
    required String periodEnd,
    required int terminalNumber,
    required List<CashierSales> cashierSales,
    required List<CashierStats> cashierStats,
    required List<CashierMedia> cashierMedia,
    required List<CashierDiscounts> cashierDiscounts,
    required int paidOutTotal,
    required int pickUpTotal,
    required int loanTotal,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    int mediaTotal = 0;
    int mediaTotalWOTips = 0;
    int mediaTips = 0;

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Cashier Report",
      ),
      _emptyLine,
      PjRows(
        leftText: cashier,
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Terminal: $terminalNumber",
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      PjRows(
        leftText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        rightText: DateFormat('hh:mma').format(DateTime.now()),
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      PjRows(
        leftText: " from: ${DateFormat('yyyy-MM-dd hh:mma').format(DateTime.parse(periodStart))}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "   to: ${DateFormat('yyyy-MM-dd hh:mma').format(DateTime.parse(periodEnd).toLocal())}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Sales",
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    /////////////Business  Rows////////////////
    pjRows.add(
      PjRows(
        leftText: "Check#  Emp       Total",
        rightText: "w/tip    Media",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    );

    int total = 0;
    int withTipTotal = 0;
    int discountTotal = 0;

    for (final CashierSales dataRow in cashierSales) {
      int saleNumberSpacing = 7;
      int totalSpacing = 10;
      int mediaSpacing = 8;
      total += dataRow.total;
      withTipTotal += dataRow.total_with_tip;

      saleNumberSpacing = saleNumberSpacing - dataRow.sale_number.toString().length;
      totalSpacing = totalSpacing - dataRow.total.toString().length;
      mediaSpacing = mediaSpacing - Helpers.mediaTypeAsString(PaymentMediaType.values[dataRow.media!]).length;

      pjRows.add(
        PjRows(
          leftText:
              "${dataRow.sale_number}${" " * saleNumberSpacing}${dataRow.employee}${" " * totalSpacing}\$${Helpers.formatCurrency(dataRow.total)}",
          rightText: dataRow.media == 7
              ? "\$${Helpers.formatCurrency(dataRow.total_with_tip)}${" " * 2}Legacy"
              : "\$${Helpers.formatCurrency(dataRow.total_with_tip)}${" " * mediaSpacing}${Helpers.mediaTypeAsString(PaymentMediaType.values[dataRow.media!])}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }
    pjRows.add(_thickLine);

    pjRows.add(
      PjRows(
        leftText: "Total ",
        rightText: "\$${Helpers.formatCurrency(total)}    \$${Helpers.formatCurrency(withTipTotal)}         ",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    );
    pjRows.add(_emptyLine);
    pjRows.add(
      PjRows(
        leftText: "Media Breakdown",
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
    );
    pjRows.add(_emptyLine);
    pjRows.add(
      PjRows(
        leftText: "Media        Total",
        rightText: "Tip    To. w Tip",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    );
    int cashAmount = 0;
    int ccTips = 0;
    int mediaSpacing = 0;
    int totalSpacing = 0;
    for (final CashierMedia element in cashierMedia) {
      totalSpacing = 10;
      mediaSpacing = 11;
      mediaSpacing = mediaSpacing - Helpers.mediaTypeAsString(PaymentMediaType.values[element.media!]).length;
      mediaTips += element.tips;
      mediaTotalWOTips += element.sum;
      mediaTotal += element.sum + element.tips;
      totalSpacing = totalSpacing - (element.sum + element.tips).toString().length;
      if (element.media == 0) {
        cashAmount = element.sum;
      }
      if (element.media == 2) {
        ccTips = element.tips;
      }
      pjRows.add(
        PjRows(
          leftText: element.media == 7
              ? "Legacy${" " * 5}\$${Helpers.formatCurrency(element.sum)}"
              : "${Helpers.mediaTypeAsString(PaymentMediaType.values[element.media!])}${" " * mediaSpacing}\$${Helpers.formatCurrency(element.sum)}",
          rightText: "\$${Helpers.formatCurrency(element.tips)}${" " * totalSpacing}\$${Helpers.formatCurrency(element.sum + element.tips)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }
    totalSpacing = 10;
    mediaSpacing = 11;
    mediaSpacing = mediaSpacing - "Total".length;
    totalSpacing = totalSpacing - mediaTotal.toString().length;
    pjRows.add(_thickLine);
    pjRows.addAll(<PjRows>[
      PjRows(
        leftText: "Total${" " * mediaSpacing}\$${Helpers.formatCurrency(mediaTotalWOTips)}",
        rightText: "\$${Helpers.formatCurrency(mediaTips)}${" " * totalSpacing}\$${Helpers.formatCurrency(mediaTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "",
        rightText: "",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Cash Total",
        rightText: "\$${Helpers.formatCurrency(cashAmount)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Loan",
        rightText: "\$${Helpers.formatCurrency(loanTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-C/C Tips",
        rightText: "\$${Helpers.formatCurrency(ccTips)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Pick Up",
        rightText: "\$${Helpers.formatCurrency(pickUpTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Paid Out",
        rightText: "\$${Helpers.formatCurrency(paidOutTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Adjusted Cash",
        rightText: "\$${Helpers.formatCurrency(cashAmount + loanTotal - ccTips - paidOutTotal - pickUpTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _thickLine,
      _emptyLine,
    ]);

    pjRows.addAll(
      <PjRows>[
        PjRows(
          leftText: "Stats",
          font: 0,
          fontSize: 16,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Cancelled Sales",
          rightText: "\$${Helpers.formatCurrency(cashierStats.isNotEmpty ? cashierStats.first.cancel_amount : 0)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Refunds",
          rightText: "\$${Helpers.formatCurrency(cashierStats.isNotEmpty ? cashierStats.first.refunded_total : 0)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Voided Items",
          rightText: "\$${Helpers.formatCurrency(cashierStats.isNotEmpty ? cashierStats.first.voided_total : 0)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      ],
    );
    pjRows.add(_emptyLine);
    pjRows.add(_thickLine);
    pjRows.add(_emptyLine);

    pjRows.add(
      PjRows(
        leftText: "Discounts",
        font: 0,
        fontSize: 16,
        specOpts: 0,
      ),
    );
    pjRows.add(_emptyLine);

    for (final CashierDiscounts element in cashierDiscounts) {
      discountTotal += element.sum;
      pjRows.addAll(
        <PjRows>[
          PjRows(
            leftText: element.title,
            rightText: "\$${Helpers.formatCurrency(element.sum)}",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
        ],
      );
    }
    pjRows.add(_emptyLine);

    pjRows.add(
      PjRows(
        leftText: "Total",
        rightText: "\$${Helpers.formatCurrency(discountTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    );

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  static PrintJob buildDailySalesPrintJob({
    required List<SalesByDeptSummaryReport> deptRecords,
    required List<MajorReport> majorRecords,
    required List<MediaReport> mediaRecords,
    required List<EmployeeDiscount> discRecords,
    required List<TaxReport> taxReport,
    required int noSaleCount,
    required int giftSales,
    required int giftSalesCount,
    required int houseChargeSales,
    required int houseChargeCount,
    required int paidOutCount,
    required int paidOut,
    required int cancelSaleCount,
    required int cancelSaleAmount,
    required int refundedAmount,
    required int refundedCount,
    required int reopenedTotal,
    required int reopenedCount,
    required String startDate,
    int dualPriceTotal = 0,
    int dualPriceCount = 0,
    int cashTotal = 0,
    int ccTips = 0,
    required int takeoutFees,
    required String endDate,
    required List<OrderTypeReport> orderTypeList,
    required int orderTypeTotal,
    required int orderTypeCount,
  }) {
    final List<PjRows> pjRows = <PjRows>[];
    final RxList<String> discountTitles = <String>[].obs;

    for (final EmployeeDiscount element in discRecords) {
      if (!discountTitles.contains(element.title)) {
        discountTitles.add(element.title);
      }
    }

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Daily Sales",
      ),
      _emptyLine,
      PjRows(
        leftText: "Report Period: from ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(startDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   ${DateFormat('M/d/yy hh:mma').format(DateTime.parse(endDate).toLocal())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    /////////////Business  Rows////////////////
    ///
    ///
    /// Department variables
    final RxInt deptFinalTotal = 0.obs;
    final RxInt deptFinalCount = 0.obs;
    final RxInt deptFinalSpacing = 12.obs;
    final RxInt deptTaxTotal = 0.obs;

    /// Media variables
    final RxInt mediaFinalTotal = 0.obs;
    final RxInt mediaFinalCount = 0.obs;
    RxInt mediaFinalTips = 0.obs;
    RxInt mediaFinalTotalNoTips = 0.obs;

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        leftText: "Department          Count",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    for (final MajorReport majorRec in majorRecords) {
      pjRows.add(
        PjRows(
          rightText: "",
          leftText: majorRec.major_group,
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );

      for (final SalesByDeptSummaryReport element in deptRecords) {
        if (element.major == majorRec.major_group) {
          final RxInt deptSpacing = 20.obs;
          if (element.department!.length >= 20) {
            element.department = element.department!.replaceRange(15, element.department!.length, "...");
          }
          deptFinalCount.value += element.item_count!;
          deptFinalTotal.value += element.dept_actual_price!;
          deptSpacing.value = deptSpacing.value - Helpers.formatCurrency(element.dept_actual_price ?? 0).length;
          pjRows.add(
            PjRows(
              rightText: "${element.item_count}${" " * deptSpacing.value}\$${Helpers.formatCurrency(element.dept_actual_price!)}",
              leftText: "${element.department}",
              font: 0,
              fontSize: 0,
              specOpts: 0,
            ),
          );
        }
      }
      pjRows.addAll(
        <PjRows>[
          PjRows(
            rightText: "\$${Helpers.formatCurrency(majorRec.major_actual_total)}",
            leftText: "Major Total:",
            font: 0,
            fontSize: 0,
            specOpts: 0,
          ),
          _emptyLine,
        ],
      );
    }

    pjRows.addAll(<PjRows>[
      _thickLine,
      PjRows(
        rightText: "${deptFinalCount.value}${" " * deptFinalSpacing.value}\$${Helpers.formatCurrency(deptFinalTotal.value)}",
        leftText: "Dept. Total:",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    ]);
    for (final TaxReport element in taxReport) {
      deptTaxTotal.value += element.tax_amount;
      pjRows.add(
        PjRows(
          rightText: "\$${Helpers.formatCurrency(element.tax_amount)}",
          leftText: element.description,
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }
    pjRows.add(
      PjRows(
        rightText:
            "${deptFinalCount.value}${" " * deptFinalSpacing.value}\$${Helpers.formatCurrency(deptFinalTotal.value + deptTaxTotal.value - dualPriceTotal)}",
        leftText: "Total:",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
    );

    pjRows.addAll(
      <PjRows>[
        PjRows(
          leftText: "Takeout fees",
          rightText: "\$${Helpers.formatCurrency(takeoutFees)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "-Paid Out:",
          rightText: "\$${Helpers.formatCurrency(paidOut)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "-Gift Card Sales:",
          rightText: "\$${Helpers.formatCurrency(giftSales)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "-House Charges:",
          rightText: "\$${Helpers.formatCurrency(houseChargeSales)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Total Accountable:",
          rightText: "\$${Helpers.formatCurrency(deptFinalTotal.value - giftSales - paidOut - houseChargeSales + deptTaxTotal.value + takeoutFees)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "Media Breakdown",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "__________________________________________",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Media        Total",
          rightText: "Tips   To. W/Tip",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
      ],
    );
    final RxInt mediaFinalSpacing = 5.obs;
    for (final MediaReport element in mediaRecords) {
      final RxInt mediaTotalSpacingAmount = 11.obs;
      final RxInt mediaTotalSpacingTips = 10.obs;
      final String media = Helpers.mediaTypeAsString(
        PaymentMediaType.values[int.parse(element.media)],
      );
      int count = 0;
      if (element.count < 10) {
        count = 1;
      } else if (element.count < 100) {
        count = 2;
      } else if (element.count < 1000) {
        count = 3;
      } else if (element.count < 10000) {
        count = 4;
      } else if (element.count < 100000) {
        count = 5;
      } else if (element.count < 1000000) {
        count = 6;
      }

      mediaTotalSpacingTips.value = mediaTotalSpacingTips.value - Helpers.formatCurrency(element.amount + element.tips).length;
      mediaTotalSpacingAmount.value = mediaTotalSpacingAmount.value - media.length - count;
      mediaFinalCount.value += element.count;
      mediaFinalTotal.value += element.amount + element.tips;
      mediaFinalTotalNoTips += element.amount;
      mediaFinalTips += element.tips;

      pjRows.add(
        PjRows(
          leftText:
              "${Helpers.mediaTypeAsString(PaymentMediaType.values[int.parse(element.media)])}(${element.count})${" " * mediaTotalSpacingAmount.value}\$${Helpers.formatCurrency(element.amount)}",
          rightText:
              "\$${Helpers.formatCurrency(element.tips)}${" " * mediaTotalSpacingTips.value}\$${Helpers.formatCurrency(element.amount + element.tips)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }
    final RxInt mediaTotalSpacingTips = 10.obs;

    mediaTotalSpacingTips.value = mediaTotalSpacingTips.value - Helpers.formatCurrency(mediaFinalTotal.value).length;
    mediaFinalSpacing.value = mediaFinalSpacing.value - Helpers.formatCurrency(mediaFinalCount.value).length;
    pjRows.addAll(<PjRows>[
      _thickLine,
      PjRows(
        leftText: "Total(${mediaFinalCount.value}):${" " * mediaFinalSpacing.value}\$${Helpers.formatCurrency(mediaFinalTotalNoTips.value)}",
        rightText:
            "\$${Helpers.formatCurrency(mediaFinalTips.value)}${" " * mediaTotalSpacingTips.value}\$${Helpers.formatCurrency(mediaFinalTotal.value)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      PjRows(
        leftText: "Cash Total:",
        rightText: "\$${Helpers.formatCurrency(cashTotal)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-Paid Out:",
        rightText: "\$${Helpers.formatCurrency(paidOut)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "-C/C Tips:",
        rightText: "\$${Helpers.formatCurrency(ccTips)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Adjusted Cash:",
        rightText: "\$${Helpers.formatCurrency(cashTotal - ccTips - paidOut)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      PjRows(
        text: "Order Type Breakdown",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      PjRows(
        text: "__________________________________________",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      PjRows(
        leftText: "Type              Count",
        rightText: "Total",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _thickLine,
    ]);

    for (final OrderTypeReport element in orderTypeList) {
      final RxInt rowSpacing = 20.obs;
      rowSpacing.value = rowSpacing.value - Helpers.formatCurrency(element.total).length;
      pjRows.add(
        PjRows(
          leftText: OrderType.values[element.type].friendlyString,
          rightText: "${element.qty}${" " * rowSpacing.value}\$${Helpers.formatCurrency(element.total)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }
    final RxInt totalSpacing = 20.obs;
    totalSpacing.value = totalSpacing.value - Helpers.formatCurrency(orderTypeTotal).length;

    pjRows.addAll(
      <PjRows>[
        _thickLine,
        PjRows(
          leftText: "Total:",
          rightText: "$orderTypeCount${" " * totalSpacing.value}\$${Helpers.formatCurrency(orderTypeTotal)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          text: "Statistics",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "__________________________________________",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Stat               Count",
          rightText: "Total",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
        PjRows(
          leftText: "Cancelled Sales      $cancelSaleCount",
          rightText: "\$${Helpers.formatCurrency(cancelSaleAmount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Refunded Sales       $refundedCount",
          rightText: "\$${Helpers.formatCurrency(refundedAmount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Reopened Sales       $reopenedCount",
          rightText: "\$${Helpers.formatCurrency(refundedAmount)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "Paid Out            $paidOutCount",
          rightText: "\$${Helpers.formatCurrency(paidOut)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          leftText: "No Sales            $noSaleCount",
          rightText: "-",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
        _emptyLine,
        PjRows(
          text: "Discounts",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        PjRows(
          text: "__________________________________________",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _emptyLine,
        PjRows(
          leftText: "Discount",
          rightText: "Total",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
        _thickLine,
      ],
    );
    final RxInt discountFinalTotal = 0.obs;
    for (int i = 0; i < discountTitles.length; i++) {
      final RxInt currentAmount = 0.obs;

      for (final EmployeeDiscount element in discRecords) {
        if (element.title == discountTitles[i]) {
          currentAmount.value += element.discount_total;
        }
      }
      discountFinalTotal.value += currentAmount.value;
      pjRows.add(
        PjRows(
          leftText: discountTitles[i],
          rightText: "\$${Helpers.formatCurrency(currentAmount.value)}",
          font: 0,
          fontSize: 0,
          specOpts: 0,
        ),
      );
    }

    pjRows.addAll(<PjRows>[
      _thickLine,
      PjRows(
        leftText: "Total:",
        rightText: "\$${Helpers.formatCurrency(discountFinalTotal.value)}",
        font: 0,
        fontSize: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _cutLine,
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }

  /////////////////////////////////Activity Report//////////////////////////
  static PrintJob buildActivityReport({
    required List<Activity> activityRows,
    required String scope,
    required String reportPeriodStart,
    required String reportPeriodEnd,
  }) {
    final List<PjRows> pjRows = <PjRows>[];

    pjRows.addAll(<PjRows>[
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "SL",
      ),
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 1,
        text: "Activity Report",
      ),
      _emptyLine,
      PjRows(
        leftText: "Scope: $scope",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "Report Period: from $reportPeriodStart",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               to   $reportPeriodEnd",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      PjRows(
        leftText: "               as of ${DateFormat('M/d/yy hh:mma').format(DateTime.now())}",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Time        Emp ID",
        rightText: "Terminal      Activity",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
    ]);

    for (final Activity row in activityRows) {
      int terminalSpacing = 18;
      terminalSpacing = terminalSpacing - ActivityFlags.values[row.activity!].friendlyString.length;
      pjRows.add(
        PjRows(
          leftText: "${DateFormat('hh:mm a').format(DateTime.parse(row.created_at!.toLocal().toString()))}    ${row.emp_id}",
          rightText: " ${row.term_num}${" " * terminalSpacing}${ActivityFlags.values[row.activity!].friendlyString}",
          font: 0,
          specOpts: 0,
        ),
      );
    }

    pjRows.addAll(<PjRows>[
      _thickLine,
      _emptyLine,
      PjRows(
        leftText: "Total:",
        rightText: "",
        font: 0,
        specOpts: 0,
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 2,
        text: "R2",
      ),
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      _emptyLine,
      PjRows(
        font: 0,
        fontSize: 0,
        specOpts: 3,
        text: "",
      ),
    ]);

    final PrintJob printJob = PrintJob.empty();

    printJob.document = PrintJobDocument(
      pjHeader: PjHeader(
        dateTime: DateTime.now().toString(),
        rowsUsed: pjRows.length,
      ),
      pjRows: pjRows,
    );

    return printJob;
  }
}
