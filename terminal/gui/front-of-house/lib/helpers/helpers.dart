import 'dart:io';
import 'dart:math';

import 'package:backoffice/app/data/models/all_sale_windows.dart';
import 'package:backoffice/app/data/models/sale_window.dart';
import 'package:backoffice/app/data/models/timecard.dart';
import 'package:desktop/app/data/constants.dart';
import 'package:desktop/app/data/enums/payment_media_type.dart';
import 'package:desktop/app/data/enums/sale_flags.dart';
import 'package:desktop/app/data/enums/sale_row_flags.dart';
import 'package:desktop/app/data/models/batch_window.dart';
import 'package:desktop/app/data/models/customer.dart';
import 'package:desktop/app/data/models/employee.dart';
import 'package:desktop/app/data/models/sale.dart';
import 'package:desktop/app/modules/register/dialogs/tables/chart_view/elements/flow_element.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

final Logger _logger = Logger('Helpers');

class Helpers {
  static NumberFormat _oCcy = new NumberFormat("#,##0.00", "en_US");
  static NumberFormat formatNumber = new NumberFormat.decimalPattern("en_us");

  static RxBool runningMacro = false.obs;
  static bool blockAutoSignout = false;
  static final Rx<DateTime> lastActivityTime = DateTime.now().obs;

  static bool isFlagTrue(String envName) {
    final String? value = Platform.environment[envName];

    if (value == null) {
      return false;
    }

    return <String>["true", "1", "yes", "on"].contains(value.toLowerCase());
  }

  static SaleHeader makeDeepSaleHeaderCopy(Sale sale) {
    final Map<String, dynamic> jsonCopy = sale.document.saleHeader.toJson();
    jsonCopy["tenders"] = <dynamic>[];
    final SaleHeader headerCopy = SaleHeader.fromJson(jsonCopy);
    headerCopy.tenders = <SaleTender>[...sale.document.saleHeader.tenders];
    return headerCopy;
  }

  static String formatCurrency(int value) {
    return _oCcy.format(value / 100);
  }

  static String formatPrice(int value) {
    return _oCcy.format(value);
  }

  static String formatWholeNumber(int value) {
    return formatNumber.format(value);
  }

  static String formatCustomerAddress(Customer customer) {
    final StringBuffer buffer = StringBuffer(customer.document.address ?? "");
    buffer.writeAll(<String>[
      if ((customer.document.city ?? "") != "") " ${customer.document.city}",
      if ((customer.document.city ?? "") != "" && ((customer.document.state ?? "") != "")) ",",
      if ((customer.document.state ?? "") != "") " ${customer.document.state}",
      if ((customer.document.zip ?? "") != "") " ${customer.document.zip}",
    ]);
    return buffer.toString();
  }

  static int? formatInputCurrency(dynamic value) {
    int? formattedValue = 0;
    switch (value.runtimeType) {
      case double:
        formattedValue = ((value * 100) as double).toInt();
        break;
      case String:
        formattedValue = ((value * 100) as int?)!;
        _logger.finest("$value is a string");
        break;
      case int:
        _logger.finest("$value is an int");
        break;
      default:
    }
    return formattedValue;
  }

  // 0200500202007
  // 0        2              00500  2                  0200   7
  // nothing  random weight  ITEM#  price check digit  price  global check digit
  static bool isRandomWeightUPC(String upc) {
    if (upc.startsWith("2")) return true;

    return false;
  }

  static bool isUPC(String upc) {
    if (upc.length == 12) return true;

    return false;
  }

  static Map<String, dynamic> sanitizeEntity<T>(
    Map<String, dynamic> entity,
    List keysToRemove,
  ) {
    entity.removeWhere((key, value) => keysToRemove.contains(key));
    // for (var i = 0; i < keysToRemove.length; i++) {
    // }
    return entity;
  }

  /// Accepts a double and a number of places to round after the decimal
  static double roundDouble(double val, int places) {
    final num mod = pow(10.0, places);
    return (val * mod).round().toDouble() / mod;
  }

  static bool validateEmail(String? value) {
    const String pattern = r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]"
        r"{0,253}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]"
        r"{0,253}[a-zA-Z0-9])?)*$";
    final RegExp regex = RegExp(pattern);
    if (value == null || value.isEmpty || !regex.hasMatch(value)) {
      return false;
    } else {
      return true;
    }
  }

  static String addTime(String time1, String time2) {
    double.tryParse(time1);
    double.tryParse(time2);

    final String businessLogic = (double.parse(time1.replaceAll(":", ".")) + double.parse(time2.replaceAll(":", "."))).toStringAsFixed(2);

    return businessLogic.replaceAll(".", ":");
  }

  static String calculateGrossPay(String regWage, String otWage) {
    final String result = (int.parse(regWage) + int.parse(otWage)).toString();
    return result;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static int getCashPrice({required int? cashAmount, required int? creditAmount, required double dualPercent, required int roundAmount}) {
    int num = cashAmount ?? 0;
    if (num == 0) {
      if ((creditAmount ?? 0) != 0) {
        num = creditAmount!;
        if (dualPercent > 0) {
          num = ((num * ((100 - dualPercent) / 100)).round() / roundAmount).round() * roundAmount;
        }
      }
    }
    return num == 0 && (creditAmount ?? 0) != 0 ? creditAmount ?? num : num;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static int getCreditPrice({required int? cashAmount, required int? creditAmount, required double dualPercent, required int roundAmount}) {
    int num = creditAmount ?? 0;
    if (num == 0) {
      if ((cashAmount ?? 0) != 0) {
        num = cashAmount!;
        if (dualPercent > 0) {
          num = ((num * ((100 + dualPercent) / 100)).round() / roundAmount).ceil() * roundAmount;
        }
      }
    }
    return num == 0 && (cashAmount ?? 0) != 0 ? cashAmount ?? num : num;
  }

  ///
  ///
  ///
  ///
  ///
  /// Returns the string representation of a MediaType
  static String mediaTypeAsString(PaymentMediaType type) {
    String typeString = "Unknown";

    switch (type) {
      case PaymentMediaType.Cash:
        typeString = "Cash";
        break;
      case PaymentMediaType.Check:
        typeString = "Check";
        break;
      case PaymentMediaType.Credit:
        typeString = "Credit";
        break;
      case PaymentMediaType.Debit:
        typeString = "Debit";
        break;
      case PaymentMediaType.EBT:
        typeString = "EBT";
        break;
      case PaymentMediaType.Gift:
        typeString = "Gift";
        break;
      case PaymentMediaType.House:
        typeString = "House";
        break;
      case PaymentMediaType.LegacyGift:
        typeString = "Legacy Gift";
        break;
      default:
    }
    return typeString;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static int timeToMilliseconds(DateTime dateTime) {
    final int minutes = (dateTime.hour * 60) + dateTime.minute;
    return Duration(minutes: minutes).inMilliseconds;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static SaleRow getParentRow(
    SaleRow saleRow,
    List<SaleRow> rowList,
  ) {
    if (saleRow.parent < 0) return saleRow;
    final SaleRow? parentRow = rowList.firstWhereOrNull((SaleRow sr) => sr.index == saleRow.parent);
    if (parentRow == null) {
      _logger.severe("Parent row not found: index ${saleRow.index}, parent ${saleRow.parent}");
      return saleRow;
    }
    return getParentRow(parentRow, rowList);
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static List<SaleRow> getChildRows(
    SaleRow saleRow,
    List<SaleRow> rowList,
  ) {
    final List<SaleRow> aggrigate = <SaleRow>[saleRow];

    final List<SaleRow> childRows = rowList.where((SaleRow sr) => sr.parent == saleRow.index).toList();

    for (final SaleRow child in childRows) {
      aggrigate.addAll(getChildRows(child, rowList));
    }

    return aggrigate;
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static int getSaleRowIndent({
    required SaleRow saleRow,
    required List<SaleRow> rowList,
    int indent = 1,
  }) {
    if (saleRow.parent < 0 || rowList.isEmpty) return indent;

    final SaleRow? parent = rowList.firstWhereOrNull((SaleRow sr) => sr.index == saleRow.parent);

    if (parent == null) return indent;

    int newIndent = indent;

    if (parent.isVisible) {
      newIndent += 1;
    }

    return getSaleRowIndent(
      saleRow: parent,
      indent: newIndent,
      rowList: rowList,
    );
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static String formatPhoneNumber(String number) {
    final String filteredNum = number.replaceAll(RegExp('[^0-9]'), '');
    const String emptyString = "(___)___-____";
    if (filteredNum.length < 10) {
      _logger.severe(
        "Not enough characters to format as phone number.",
      );
      return emptyString;
    }
    if (filteredNum.length > 11) {
      _logger.severe(
        "Too many characters to format as phone number.",
      );
      return emptyString;
    }
    final StringBuffer buffer = StringBuffer("");
    final int addInt = filteredNum.length > 10 ? 1 : 0;
    buffer.writeAll(
      <String>[
        // Add country code if present
        if (filteredNum.length > 10) "${filteredNum.substring(0, 1)} ",
        "(",
        filteredNum.substring(0 + addInt, 3 + addInt),
        ")",
        filteredNum.substring(3 + addInt, 6 + addInt),
        "-",
        filteredNum.substring(6 + addInt),
      ],
    );
    return buffer.toString();
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static String millisecondsToTimeString(int milliseconds) {
    bool pm = false;
    final int minutesfromMS = Duration(milliseconds: milliseconds).inMinutes;
    final int minutes = minutesfromMS % 60;
    int hour = minutesfromMS ~/ 60;

    if (hour > 11) pm = true;

    if (hour > 12) {
      hour -= 12;
    }

    return "$hour:${minutes < 10 ? "0" : ""}$minutes ${pm ? "PM" : "AM"}";
  }

  ///
  ///
  ///
  ///
  ///
  ///
  static BatchWindow getBatchWindow(DateTime now) {
    final DateFormat timezoneFormatter = DateFormat("yyyy-MM-ddTHH:mm:ss");

    // Set the start time to look for sales from 5AM to now.
    // If it's before 5AM today, use yesterday's 5AM as a start point
    DateTime startDateTime = DateTime(now.year, now.month, now.day, 5);
    if (now.hour < 5) {
      startDateTime = startDateTime.subtract(const Duration(days: 1));
    }

    final String startString = timezoneFormatter.format(startDateTime.toUtc());
    final String endString = timezoneFormatter.format(now.toUtc());

    return BatchWindow(start: startString, end: endString);
  }

  static SaleWindow getReportWindow(
    DateTime selectedDate, {
    required String scope,
  }) {
    final SaleWindow saleWindow = SaleWindow.empty();
    final DateTime now = DateTime.now();

    switch (scope) {
      case "Day":
        final DateTime currentDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedDate.day == now.day ? now.hour : 5,
        );
        if (currentDateTime.hour < 5) {
          saleWindow.start = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day - 1,
            5,
          ).toUtc().toString();
          saleWindow.end = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day,
            4,
            59,
            59,
          ).toUtc().toString();
        } else {
          saleWindow.start = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day,
            5,
          ).toUtc().toString();
          saleWindow.end = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day + 1,
            4,
            59,
            59,
          ).toUtc().toString();
        }

        break;
      case "Week":
        final DateTime currentDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedDate.day == now.day ? now.hour : 5,
        );
        if (currentDateTime.weekday == 7 && currentDateTime.hour <= 5) {
          saleWindow.start = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day,
            5,
          ).toUtc().toString();
          final DateTime weekEnd = selectedDate.add(const Duration(days: 7));
          saleWindow.end = DateTime(weekEnd.year, weekEnd.month, weekEnd.day, 4, 59, 59).toUtc().toString();
        } else {
          final DateTime weekStart = selectedDate.subtract(Duration(days: selectedDate.weekday));
          saleWindow.start = DateTime(weekStart.year, weekStart.month, weekStart.day, 5).toUtc().toString();
          final DateTime weekEnd = selectedDate.add(Duration(days: DateTime.daysPerWeek - selectedDate.weekday));
          saleWindow.end = DateTime(weekEnd.year, weekEnd.month, weekEnd.day, 4, 59, 59).toUtc().toString();
        }

        break;

      case "Month":
        final DateTime currentDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          1,
          selectedDate.day == now.day ? now.hour : 5,
        );
        if (selectedDate.day == 1 && currentDateTime.hour < 5) {
          saleWindow.start = DateTime(
            currentDateTime.year,
            currentDateTime.month - 1,
            currentDateTime.day,
            5,
          ).toUtc().toString();
          saleWindow.end = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day,
            4,
            59,
            59,
          ).toUtc().toString();
        } else {
          saleWindow.start = DateTime(
            currentDateTime.year,
            currentDateTime.month,
            currentDateTime.day,
            5,
          ).toUtc().toString();
          saleWindow.end = DateTime(
            currentDateTime.year,
            currentDateTime.month + 1,
            1,
            4,
            59,
            59,
          ).toUtc().toString();
        }

        break;

      case "Year":
        final DateTime currentDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedDate.day == now.day ? now.hour : 5,
        );
        if (currentDateTime.month == 1 && currentDateTime.day == 1 && currentDateTime.hour < 5) {
          saleWindow.start = DateTime(currentDateTime.year - 1, 1, 1, 5).toUtc().toString();
          saleWindow.end = DateTime(currentDateTime.year, 1, 1, 4, 59, 59).toUtc().toString();
        } else {
          saleWindow.start = DateTime(currentDateTime.year, 1, 1, 5).toUtc().toString();
          saleWindow.end = DateTime(currentDateTime.year + 1, 1, 1, 4, 59, 59).toUtc().toString();
        }

        break;
      default:
    }
    return saleWindow;
  }

  static AllSaleWindows getAllReportWindows({required DateTime selectedDate}) {
    final AllSaleWindows saleWindow = AllSaleWindows.empty();
    final DateTime now = DateTime.now();

    final DateTime currentDateTime = DateTime(selectedDate.year, selectedDate.month, selectedDate.day, selectedDate.day == now.day ? now.hour : 5);
    if (currentDateTime.hour < 5) {
      saleWindow.startDate = DateTime(currentDateTime.year, currentDateTime.month, currentDateTime.day - 1, 5).toUtc().toString();
      saleWindow.endDate = DateTime(currentDateTime.year, currentDateTime.month, currentDateTime.day, 4, 59, 59).toUtc().toString();
    } else {
      saleWindow.startDate = DateTime(currentDateTime.year, currentDateTime.month, currentDateTime.day, 5).toUtc().toString();
      saleWindow.endDate = DateTime(currentDateTime.year, currentDateTime.month, currentDateTime.day + 1, 4, 59, 59).toUtc().toString();
    }

    final DateTime currentWeekTime = DateTime(selectedDate.year, selectedDate.month, selectedDate.day, selectedDate.day == now.day ? now.hour : 5);
    if (currentWeekTime.weekday == 7 && currentWeekTime.hour <= 5) {
      saleWindow.startWeek = DateTime(currentWeekTime.year, currentWeekTime.month, currentWeekTime.day, 5).toUtc().toString();
      final DateTime weekEnd = selectedDate.add(const Duration(days: 7));
      saleWindow.endWeek = DateTime(weekEnd.year, weekEnd.month, weekEnd.day, 4, 59, 59).toUtc().toString();
    } else {
      final DateTime weekStart = selectedDate.subtract(Duration(days: selectedDate.weekday));
      saleWindow.startWeek = DateTime(weekStart.year, weekStart.month, weekStart.day, 5).toUtc().toString();
      final DateTime weekEnd = selectedDate.add(Duration(days: DateTime.daysPerWeek - selectedDate.weekday));
      saleWindow.endWeek = DateTime(weekEnd.year, weekEnd.month, weekEnd.day, 4, 59, 59).toUtc().toString();
    }

    final DateTime currentMonthTime = DateTime(selectedDate.year, selectedDate.month, selectedDate.day, selectedDate.day == now.day ? now.hour : 5);
    if (selectedDate.day == 1 && currentMonthTime.hour < 5) {
      saleWindow.startMonth = DateTime(currentMonthTime.year, currentMonthTime.month - 1, currentMonthTime.day, 5).toUtc().toString();
      saleWindow.endMonth = DateTime(currentMonthTime.year, currentMonthTime.month, currentMonthTime.day, 4, 59, 59).toUtc().toString();
    } else {
      saleWindow.startMonth = DateTime(currentMonthTime.year, currentMonthTime.month, 1, 5).toUtc().toString();
      saleWindow.endMonth = DateTime(currentMonthTime.year, currentMonthTime.month, currentMonthTime.day + 1, 4, 59, 59).toUtc().toString();
    }

    final DateTime currentYearTime = DateTime(selectedDate.year, selectedDate.month, selectedDate.day, selectedDate.day == now.day ? now.hour : 5);
    if (currentYearTime.month == 1 && currentYearTime.day == 1 && currentYearTime.hour < 5) {
      saleWindow.startYear = DateTime(currentYearTime.year - 1, 1, 1, 5).toUtc().toString();
      saleWindow.endYear = DateTime(currentYearTime.year, 1, 1, 4, 59, 59).toUtc().toString();
    } else {
      saleWindow.startYear = DateTime(currentYearTime.year, 1, 1, 5).toUtc().toString();
      saleWindow.endYear = DateTime(currentYearTime.year, currentYearTime.month, currentYearTime.day + 1, 4, 59, 59).toUtc().toString();
    }
    print("=======");
    print("start day: ${saleWindow.startDate}");
    print("end day: ${saleWindow.endDate}");
    print("=======");
    print("start week: ${saleWindow.startWeek}");
    print("end week: ${saleWindow.endWeek}");
    print("=======");
    print("start month: ${saleWindow.startMonth}");
    print("end month: ${saleWindow.endMonth}");
    print("=======");
    print("start year: ${saleWindow.startYear}");
    print("end year: ${saleWindow.endYear}");
    print("=======");

    return saleWindow;
  }

  static int getDualPriceDiff(int price, double percent) {
    final double percentChange = percent / 100;
    final double newPrice = (price * (percentChange / 100)) * 100;

    return newPrice.round();
  }

  static String removeGiftPrice(String description) {
    final List<String> words = description.split(" ");
    final String newDesc = words.sublist(1, words.length).join(" ");

    return newDesc;
  }

  static String giftCardMask(String description) {
    final List<String> words = description.split(" ");
    final String newDesc = words.sublist(3, words.length).join(" ");

    return newDesc;
  }

  static String getReportStartString(DateRangePickerController rangeController) {
    return getReportWindow(rangeController.selectedRange?.startDate ?? DateTime.now(), scope: "Day").start!;
  }

  static String getReportEndString(DateRangePickerController rangeController) {
    final DateTime endDateTime = rangeController.selectedRange?.endDate == null
        ? (rangeController.selectedRange?.startDate ?? DateTime.now()).add(const Duration(days: 1))
        : rangeController.selectedRange!.endDate!.add(const Duration(days: 1));

    return getReportWindow(endDateTime, scope: "Day").start!;
  }

  static String getEmployeeLastNameFirst(Employee employee) {
    final String employeeLastNameFirst = '${employee.document.lastName ?? ""}, ${employee.document.firstName ?? ""}';
    return employeeLastNameFirst;
  }

  static Map<DateTime, List<TimecardPunch>> groupPunchesByCalendarWeek(List<TimecardPunch> punches) {
    final Map<DateTime, List<TimecardPunch>> groupedPunches = <DateTime, List<TimecardPunch>>{};

    for (final TimecardPunch punch in punches) {
      if (punch.punchIn.isEmpty) continue;

      // Parse punch-in date
      final DateTime punchDate = DateTime.parse(punch.punchIn);

      // Find the start of the week (Sunday)
      final DateTime weekStart = punchDate.subtract(Duration(days: punchDate.weekday % 7));

      // Align to midnight for consistent grouping
      final DateTime alignedWeekStart = DateTime(weekStart.year, weekStart.month, weekStart.day);

      // Add punch to the corresponding week
      if (!groupedPunches.containsKey(alignedWeekStart)) {
        groupedPunches[alignedWeekStart] = <TimecardPunch>[];
      }
      groupedPunches[alignedWeekStart]!.add(punch);
    }

    return groupedPunches;
  }

  static int calculateTotalMinutes(List<TimecardPunch> punches) {
    return punches.fold(0, (int totalMinutes, TimecardPunch punch) {
      if (punch.total.isEmpty) return totalMinutes;
      final List<String> split = punch.total.split(":");
      if (split.length != 2) return totalMinutes;
      final int hours = int.tryParse(split[0]) ?? 0;
      final int minutes = int.tryParse(split[1]) ?? 0;
      return totalMinutes + (hours * 60) + minutes;
    });
  }

  static SaleRow makeRowCopy({
    required SaleRow row,
    required int newIdx,
    required int qty,
    int? seatNum,
    int? newParentIdx,
    bool voidedCopy = false,
    bool withTransactionFlags = false,
    bool withPrepPrintedFlag = false,
  }) {
    newParentIdx ??= row.parent < 0 ? -1 : row.parent + (newIdx - row.index);
    final List<SaleRowDiscount> discounts = [...row.discounts];
    final SplitData? splitData = row.splitData == null ? null : SplitData(qty: row.splitData!.qty, key: row.splitData!.key);
    row.discounts = <SaleRowDiscount>[];
    row.splitData = null;
    final SaleRow newRow = SaleRow.fromJson(row.toJson());
    row.discounts = discounts;
    row.splitData = splitData;
    newRow.flags = [
      ...withPrepPrintedFlag ? row.flags : row.flags.where((int f) => f != SaleRowFlags.PREP_PRINTED.index).toList(),
      if (voidedCopy && !row.flags.contains(SaleRowFlags.VOIDED.index)) SaleRowFlags.VOIDED.index,
    ];
    if (seatNum != null) newRow.seatNumber = seatNum;
    if (!withTransactionFlags) newRow.transactionFlags = <int>[];
    newRow.index = newIdx;
    newRow.parent = newParentIdx;
    newRow.qty = qty;
    newRow.selected = false;
    newRow.discounts = discounts;
    newRow.splitData = splitData;
    if (!withPrepPrintedFlag) newRow.preppedAt = null;
    return newRow;
  }

  static int? getDisplayFlag(Sale s) {
    for (final int f in s.document.saleHeader.saleFlags) {
      if (Constants.restrictedFlags.contains(f)) {
        return f;
      }
    }
    if (s.document.saleHeader.saleFlags.contains(SaleFlags.COMPLETED.index)) {
      return SaleFlags.COMPLETED.index;
    }
    return null;
  }

  static String getSaleTitle(Sale sale, String? saleName) {
    final SaleHeader header = sale.document.saleHeader;
    String title = "${saleName != null ? "$saleName " : ""}#${sale.sale_number}";
    if ((header.tableDesc ?? "") != "") title = "Tbl ${header.tableDesc!}";
    if ((header.saleDescription ?? "") != "") title = header.saleDescription!;
    if ((header.customerName ?? "") != "") title = header.customerName!;
    return title;
  }

  static String hoursAndMinToDecimal(String hoursAndMin) {
    final List<String> parts = hoursAndMin.split(":");
    if (parts.length != 2) {
      return "0.00";
    }
    final int hours = int.tryParse(parts[0]) ?? 0;
    final int minutes = int.tryParse(parts[1]) ?? 0;
    final double decimalHours = hours + (minutes / 60);
    return decimalHours.toStringAsFixed(2);
  }

  static bool saleNeedsPrinted(Sale sale) {
    return sale.document.saleRows.any((SaleRow row) => rowNeedsPrinted(row));
  }

  static bool rowNeedsPrinted(SaleRow row) {
    return row.prep > 0 && !row.flags.contains(SaleRowFlags.PREP_PRINTED.index) && !row.flags.contains(SaleRowFlags.HOLD_AND_FIRE.index);
  }

  static String getTimeDifferenceString(DateTime start, DateTime end) {
    String pad(int n) => n.toString().padLeft(2, '0');
    final Duration duration = end.difference(start);
    final int hours = duration.inHours;
    final String minutes = pad(duration.inMinutes.remainder(60));
    final String seconds = pad(duration.inSeconds.remainder(60));
    return '$hours:$minutes:$seconds';
  }

  static TableStatus getTableStatus(Sale sale) {
    if (sale.sale_number < 1) return TableStatus.open;
    if (sale.document.saleHeader.saleFlags.contains(SaleFlags.RECEIPT_PRINTED.index)) {
      return TableStatus.printed;
    } else if (sale.document.saleHeader.saleFlags.contains(SaleFlags.PREP_PRINTED.index)) {
      return TableStatus.ordered;
    } else {
      return TableStatus.seated;
    }
  }
}
