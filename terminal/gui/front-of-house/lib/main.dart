import 'dart:async';
import 'dart:ui';

import 'package:bot_toast/bot_toast.dart';
import 'package:desktop/app/data/services/bridge.service.dart';
import 'package:desktop/app/data/services/graphql.service.dart';
import 'package:desktop/app/data/services/system.service.dart';
import 'package:desktop/app/global_widgets/drag_bar/dialog.dart';
import 'package:desktop/app/routes/app_pages.dart';
import 'package:desktop/app/theme/theme.dart';
import 'package:desktop/helpers/helpers.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:logging/logging.dart';
// ignore: depend_on_referenced_packages
import 'package:logging_appenders/logging_appenders.dart';
import 'package:window_manager/window_manager.dart';

final Logger mainLogger = Logger('main');

bool fromBackOfHouse = false;

class Round2ScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => <PointerDeviceKind>{
        PointerDeviceKind.mouse,
        PointerDeviceKind.touch,
        PointerDeviceKind.stylus,
        PointerDeviceKind.unknown,
      };
}

// ignore: non_constant_identifier_names
Future<void> RunDesktop() async {
  fromBackOfHouse = true;

  main();
}

Future<void> main() async {
  PrintAppender.setupLogging();

  mainLogger.finest("begin run");

  WidgetsFlutterBinding.ensureInitialized();

  await windowManager.ensureInitialized();

  final GraphqlService graphqlService = await GraphqlService().init("foh");
  Get.lazyPut(() => graphqlService);

  Get.lazyPut(() => BridgeService());

  Get.lazyPut(() => SystemService());

  final TransitionBuilder botToastBuilder = BotToastInit();

  final bool isTablet = await SystemService.computeIsTablet();

  runApp(
    GraphQLProvider(
      client: graphqlService.valueNotifierClient,
      child: GetMaterialApp(
        title: "Application",
        initialRoute: AppRoutes.SPLASH,
        builder: (BuildContext context, Widget? child) {
          return isTablet
              ? DragDownWrapper(
                  child: botToastBuilder(
                    context,
                    child,
                  ),
                )
              : ScreenSaverWrapper(
                  child: botToastBuilder(
                    context,
                    child,
                  ),
                );
        },
        getPages: AppPages.pages,
        key: const Key("frontOfHouse"),
        theme: R2Theme.theme,
        debugShowCheckedModeBanner: false,
        defaultTransition: Transition.noTransition,
        scrollBehavior: Round2ScrollBehavior(),
        navigatorObservers: <NavigatorObserver>[
          BotToastNavigatorObserver(),
        ],
      ),
    ),
  );
}

class ScreenSaverWrapper extends StatefulWidget {
  const ScreenSaverWrapper({super.key, required this.child});
  final Widget child;

  @override
  State<ScreenSaverWrapper> createState() => _ScreenSaverWrapperState();
}

class _ScreenSaverWrapperState extends State<ScreenSaverWrapper> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Timer? _screenSaverTimer;
  final Duration _screenSaverTimeout = const Duration(minutes: 10);
  bool _isScreenSaverOn = false;

  double _x = 50.0; // Initial position
  double _y = 50.0; // Initial position
  double _dx = 2.0; // Direction and speed in x
  double _dy = 4.0; // Direction and speed in y

  double logoWidth = 250.0;
  double logoHeight = 250.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 5), // High frequency for smooth movement
      vsync: this,
    )..addListener(_moveLogo);
    _restartTimer();
  }

  void _restartTimer() {
    _stopScreenSaver();
    _screenSaverTimer?.cancel();
    _screenSaverTimer = Timer(_screenSaverTimeout, _startScreenSaver);
    Helpers.lastActivityTime.value = DateTime.now();
  }

  void _startScreenSaver() {
    // If not yet mount or running beerbeercash macro, prevent screen saver
    if (!mounted || Helpers.runningMacro.value) return;
    setState(() {
      _isScreenSaverOn = true;
      _controller.repeat();
    });
  }

  void _stopScreenSaver() {
    if (_isScreenSaverOn) {
      setState(() {
        _isScreenSaverOn = false;
        _controller.stop(canceled: false);
      });
    }
  }

  void _moveLogo() {
    final Size size = MediaQuery.of(context).size;

    setState(() {
      _x += _dx;
      _y += _dy;

      if (_x <= 0 || _x + logoWidth >= size.width) {
        _dx = -_dx;
      }
      if (_y <= 0 || _y + logoHeight >= size.height) {
        _dy = -_dy;
      }
    });
  }

  @override
  void dispose() {
    _screenSaverTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) => _restartTimer(),
      child: Obx(
        () => Stack(
          children: <Widget>[
            widget.child,
            if (Helpers.runningMacro.value)
              Material(
                color: Colors.black.withOpacity(0.4),
                child: Column(
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.all(30.0),
                          child: GestureDetector(
                            child: Text(
                              "X",
                              style: TextStyle(
                                color: R2Colors.white,
                                fontSize: 30,
                                shadows: const <Shadow>[
                                  Shadow(
                                    offset: Offset(1.0, 1.0),
                                    blurRadius: 2.0,
                                  ),
                                ],
                              ),
                            ),
                            onTap: () {
                              Helpers.runningMacro.value = false;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            if (_isScreenSaverOn) ...<Widget>[
              const SizedBox.expand(
                child: ColoredBox(color: Colors.black),
              ),
              Positioned(
                left: _x,
                top: _y,
                child: Image.asset(
                  "lib/assets/img/r2_color_symbol.png",
                  width: logoWidth,
                  height: logoHeight,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
