name: desktop
version: 1.0.0+1
publish_to: none
description: A new Flutter project.
environment:
  # This pins dart.
  sdk: "3.3.2"
  flutter: "3.19.4"

dependencies:
  analog_clock: 0.1.1
  auto_size_text: 3.0.0
  backoffice:
    path: ../back-of-house
  bot_toast: 4.1.3
  collection: 1.18.0
  cross_scroll: 0.0.76
  currency_text_input_formatter: 2.2.2
  desktop_window: 0.4.0
  equatable: ^2.0.5
  event_bus: 2.0.0
  flutter:
    sdk: flutter
  font_awesome_flutter: 9.0.0
  fpdart: 0.2.0
  get: 4.6.6
  graphql_flutter: 5.1.2
  intl: 0.18.1
  json_annotation: ^4.8.1
  logging: 1.2.0
  logging_appenders: 1.2.0+1
  lottie: 1.4.3
  mask_text_input_formatter: 2.9.0
  numberpicker: 2.1.2
  settings_ui: 2.0.2
  sliver_tools: ^0.2.9
  syncfusion_flutter_datepicker: 25.1.42
  test: ^1.24.9
  window_manager: 0.3.8

dev_dependencies:
  build_runner: 2.4.8
  clock: ^1.1.1
  flutter_test:
    sdk: flutter
  json_serializable: 6.7.1
  lint: 2.3.0

dependency_overrides:
  font_awesome_flutter:
    path: ../shared/flutter_plugins/font_awesome_flutter

flutter:
  uses-material-design: true

  assets:
    - lib/assets/lottie/success.json
    - lib/assets/lottie/failed.json
    - lib/assets/lottie/burger.json
    - lib/assets/lottie/loading-animation.json
    - lib/assets/lottie/loading-animation-v2.json
    - lib/assets/lottie/weight-measure.json
    - lib/assets/img/weight.png
    - lib/assets/img/r2_blk_left.png
    - lib/assets/img/r2_blk_stacked.png
    - lib/assets/img/r2_color_left.png
    - lib/assets/img/r2_color_stacked.png
    - lib/assets/img/scale_white.png
    - lib/assets/img/scale_purple.png
    - lib/assets/img/weight-scale-img.png
    - lib/assets/img/r2_color_symbol.png
    - lib/assets/img/r2_color.png
    - lib/assets/img/r2_color_dark.png
    - lib/assets/img/ebt_purple.png
    - lib/assets/img/ebt_white.png

  fonts:
    - family: Inter
      fonts:
        - asset: lib/assets/fonts/Inter-Black.ttf
        - asset: lib/assets/fonts/Inter-ExtraBold.ttf
        - asset: lib/assets/fonts/Inter-Bold.ttf
        - asset: lib/assets/fonts/Inter-SemiBold.ttf
        - asset: lib/assets/fonts/Inter-Medium.ttf
        - asset: lib/assets/fonts/Inter-Regular.ttf
        - asset: lib/assets/fonts/Inter-Light.ttf
        - asset: lib/assets/fonts/Inter-ExtraLight.ttf
        - asset: lib/assets/fonts/Inter-Thin.ttf
    - family: Roboto Mono
      fonts:
        - asset: lib/assets/fonts/RobotoMono-VariableFont_wght.ttf
