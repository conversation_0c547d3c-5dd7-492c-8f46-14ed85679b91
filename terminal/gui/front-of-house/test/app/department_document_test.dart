// test/department_document_test.dart
import 'package:clock/clock.dart';
import 'package:desktop/app/data/models/department.dart';
import 'package:desktop/app/data/models/json_record.dart';
import 'package:test/test.dart';

void main() {
  // Helper: build a fixed DateTime on today's date but at hh:mm.
  DateTime todayAt(int hour, int minute) {
    final DateTime now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  // Use concrete days of the week
  DateTime sundayAt(int hour, int minute, [int second = -1, int millisecond = -1]) {
    return DateTime(2025, 1, 5, hour, minute, second, millisecond);
  }

  DateTime mondayAt(int hour, int minute, [int second = -1, int millisecond = -1]) {
    return DateTime(2025, 1, 6, hour, minute, second, millisecond);
  }

  DateTime tuesdayAt(int hour, int minute, [int second = -1, int millisecond = -1]) {
    return DateTime(2025, 1, 7, hour, minute, second, millisecond);
  }

  /// ms since midnight
  int toMs(int hour, int minute) => (hour * 60 + minute) * 60 * 1000;

  group('isWithinTimeWindow', () {
    test('returns true when restrict is disabled', () {
      final DepartmentDocument doc = DepartmentDocument(restrictEnabled: false, taxFlags: 0);
      expect(doc.isWithinTimeWindow(), isTrue);
    });

    test('same‑day window (08:00‑17:00)', () {
      final DepartmentDocument doc = DepartmentDocument(
        taxFlags: 0,
        restrictEnabled: true,
        timeWindows: <String, ActiveWindow>{
          'mon': ActiveWindow(open: toMs(8, 0), close: toMs(17, 0)),
        },
      );
      // Monday 12:00  → inside
      withClock(Clock.fixed(mondayAt(12, 0)), () {
        expect(doc.isWithinTimeWindow(), isTrue);
      });
      // Monday 18:00  → outside
      withClock(Clock.fixed(mondayAt(18, 0)), () {
        expect(doc.isWithinTimeWindow(), isFalse);
      });
    });

    test('cross‑midnight window (22:00‑02:00)', () {
      final DepartmentDocument doc = DepartmentDocument(
        taxFlags: 0,
        restrictEnabled: true,
        timeWindows: <String, ActiveWindow>{
          'sun': ActiveWindow(open: toMs(22, 0), close: toMs(2, 0)),
        },
      );
      // Sunday 23:00 → inside (pre‑midnight slice)
      withClock(Clock.fixed(sundayAt(23, 0)), () {
        expect(doc.isWithinTimeWindow(), isTrue);
      });
      // Monday 01:00 → inside (post‑midnight slice)
      withClock(Clock.fixed(mondayAt(1, 0)), () {
        expect(doc.isWithinTimeWindow(), isTrue);
      });
      // Monday 03:00 → outside
      withClock(Clock.fixed(mondayAt(3, 0)), () {
        expect(doc.isWithinTimeWindow(), isFalse);
      });
    });

    test('always‑open sentinel (open==-1 && close==-1)', () {
      final DepartmentDocument doc = DepartmentDocument(
        taxFlags: 0,
        restrictEnabled: true,
        timeWindows: <String, ActiveWindow>{'tue': ActiveWindow(open: -1, close: -1)},
      );
      withClock(Clock.fixed(tuesdayAt(5, 0)), () {
        expect(doc.isWithinTimeWindow(), isTrue);
      });
    });

    test('no window defined for today → allowed', () {
      final DepartmentDocument doc = DepartmentDocument(
        taxFlags: 0,
        restrictEnabled: true,
        timeWindows: <String, ActiveWindow>{'wed': ActiveWindow(open: toMs(9, 0), close: toMs(12, 0))},
      );
      withClock(Clock.fixed(todayAt(10, 0)), () {
        expect(doc.isWithinTimeWindow(), isTrue);
      });
    });
  });
}
