# Builder stage
FROM golang:alpine AS builder

RUN apk add --no-cache \
    build-base \
    libc-dev 

WORKDIR /go/src/app

COPY ./services/bridge/go.mod ./services/bridge/go.sum ./
RUN go mod download
COPY ./services/bridge/ .

RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o /go/bin/server cmd/server/main.go && \
    CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o /go/bin/manager cmd/cli/main.go

# Production stage
FROM alpine:3.20

ENV ENV=production \
    PATH="/go/bin:${PATH}"

# Copy necessary SSL certificates from builder to ensure secure connections
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

RUN apk add --no-cache iproute2 postgresql16-client

# Copy necessary files from builder
COPY --from=builder /go/bin /go/bin
COPY --from=builder /go/src/app/public/assets/libs /usr/lib
# This should be passed in as "--build-context root=PROJECT_ROOT/terminal" or in
# docker-compose.yml as "additional_contexts: root: PROJECT_ROOT/terminal"
COPY ./VERSION /opt/r2pos/VERSION

# CMD sleep infinity
ENTRYPOINT ["/go/bin/server"]


# Production stage
# FROM debian:bullseye-slim

# ENV ENV=production \
#     PATH="/go/bin:${PATH}"

# # Copy necessary SSL certificates from builder to ensure secure connections
# COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# RUN apt-get update && apt-get install -y \
#     iproute2 \
#     postgresql16-client \
#     && rm -rf /var/lib/apt/lists/*

# # Copy necessary files from builder
# COPY --from=builder /go/bin /go/bin
# COPY --from=builder /go/src/app/public/assets/libs /usr/lib

# # CMD sleep infinity
# ENTRYPOINT ["/go/bin/server"]
