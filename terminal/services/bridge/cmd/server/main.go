package main

import (
	"net/http"
	"os"
	"runtime"
	"time"

	"github.com/jordan<PERSON><PERSON>/gobalena"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"github.com/r2pos/bridge/features/customer"
	"github.com/r2pos/bridge/features/database"
	"github.com/r2pos/bridge/features/department"
	"github.com/r2pos/bridge/features/device"
	"github.com/r2pos/bridge/features/item"
	"github.com/r2pos/bridge/features/mesh"
	"github.com/r2pos/bridge/features/record"
	"github.com/r2pos/bridge/features/sale"

	"github.com/r2pos/bridge/features/helpers"
	"github.com/r2pos/bridge/internal"
	"github.com/r2pos/bridge/internal/aws"
	"github.com/r2pos/bridge/internal/config"
	"github.com/r2pos/bridge/internal/scheduler"
	"github.com/r2pos/bridge/internal/startup"
)

var (
	buildInfo = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "pos",
			Subsystem: "bridge",
			Name:      "build_info",
			Help:      "Build information for Bridge Server.",
			ConstLabels: map[string]string{
				"version":   internal.GetVersionSemVerOrDie(),
				"goversion": runtime.Version(),
			},
		},
		[]string{},
	)
)

func init() {
	output := zerolog.ConsoleWriter{
		Out:        os.Stderr,
		TimeFormat: zerolog.TimeFormatUnix,
	}

	zerolog.SetGlobalLevel(zerolog.InfoLevel)
	log.Logger = log.Output(output)

	prometheus.MustRegister(buildInfo)

	buildInfo.With(prometheus.Labels{}).Set(1)
}

func main() {
	// WARNING: Please try not to add any crash/fatal logs here in the main
	// function.
	// The bridge server is a critical service and should not crash.
	// The crashing of the bridge server might brick some CRM/device manager
	// functionality.

	cfg, err := config.Load()
	if err != nil {
		log.Fatal().Err(err).Msg("error reading config file")
	}

	log.Info().Interface("c", cfg).Msg("config file read, creating lockfile")

	if cfg.Env == config.EnvDevelopment {
		internal.Unlock(config.BalenaLockFile)
	}

	err = internal.Lock(config.BalenaLockFile)
	if err != nil {
		log.Error().Err(err).Msg("error locking lockfile")
	}

	log.Debug().Msg("lockfile created, starting server")

	e := echo.New()
	e.HideBanner = true

	e.Use(middleware.Recover())
	e.Use(middleware.GzipWithConfig(middleware.GzipConfig{
		Skipper: func(c echo.Context) bool {
			return c.Path() == "/metrics"
		},
	}))
	e.Use(middleware.CORS())
	e.Use(middleware.Secure())
	e.Use(middleware.RequestLoggerWithConfig(middleware.RequestLoggerConfig{
		LogURI:    true,
		LogStatus: true,
		LogMethod: true,
		LogError:  true,
		LogValuesFunc: func(c echo.Context, v middleware.RequestLoggerValues) error {
			if v.Error != nil || v.Status >= http.StatusBadRequest {
				log.Warn().
					Str("URI", v.URI).
					Int("status", v.Status).
					Str("method", v.Method).
					Err(v.Error).
					Msg("REQUEST")
			} else {
				log.Debug().
					Str("URI", v.URI).
					Int("status", v.Status).
					Str("method", v.Method).
					Msg("REQUEST")
			}

			return nil
		},
	}))

	e.Use(middleware.Recover())

	scheduler.Start(cfg)

	log.Info().Msg("scheduler started")

	// setup external client
	balenaLocalClient := gobalena.NewLocalClient(
		cfg.BalenaApiKey,
		cfg.BalenaSupervisorURL,
		cfg.BalenaSupervisorKey,
		cfg.BalenaAppID,
	)
	awsClient := aws.NewClient(
		cfg.AWSAccessKey, cfg.AWSSecretKey, cfg.AWSS3Bucket,
	)

	// database
	dbService := database.NewPostgresService(awsClient, cfg)
	dbHandler := database.NewHandler(cfg, dbService)

	dsn := database.BuildConnectionString(
		"localhost:5432",
		cfg.PostgresUser,
		cfg.PostgresPassword,
		cfg.PostgresDB,
	)
	log.Info().Str("dsn", dsn).Msg("database connection string")

	dbTimeout := 30 * time.Second
	if cfg.PostgresTimeout != "" {
		dbTimeout, err = time.ParseDuration(cfg.PostgresTimeout)
		if err != nil {
			// Don't fail because we don't want to break the server if the timeout is not set.
			log.Error().Err(err).Msg("error parsing database timeout")
		}
	}

	db := helpers.CreateDBConnOrDie(dsn)
	defer db.Close()

	// startup
	balenaCloudClient := gobalena.NewCloudClient(cfg.BalenaApiKey, cfg.BalenaAPIURL)
	startup.CheckEnvToResetSaleNumber(cfg, dbService, balenaCloudClient)

	// device
	deviceService := device.NewService(balenaLocalClient, cfg, dbService)
	deviceHandler := device.NewHandler(cfg, deviceService)

	// record
	recordRepo := record.NewRepo(db, dbTimeout)
	recordService := record.NewService(recordRepo)
	recordHandler := record.NewHandler(recordService)

	// customer
	customerRepo := customer.NewRepo(db, dbTimeout)
	customerService := customer.NewService(customerRepo)
	customerHandler := customer.NewHandler(customerService)

	// department
	departmentRepo := department.NewRepo(db, dbTimeout)
	departmentService := department.NewService(departmentRepo)
	departmentHandler := department.NewHandler(departmentService)

	// item
	itemRepo := item.NewRepo(db, dbTimeout, departmentService)
	itemService := item.NewService(itemRepo, recordService, departmentService)
	itemHandler := item.NewHandler(itemService)

	// sale
	nmiService := sale.NewNMIService(recordService)
	saleNumberRepo := sale.NewNumberRepo(db, dbTimeout)
	saleUpdatedRepo := sale.NewUpdatedRepo(db, dbTimeout)
	saleRepo := sale.NewRepo(db, dbTimeout)
	saleService := sale.NewService(saleRepo, saleNumberRepo, saleUpdatedRepo, recordService, itemService, customerService, nmiService)
	saleHandler := sale.NewHandler(saleService, nmiService)

	// mesh
	meshHandler := mesh.NewHandler()

	e.GET("/health", func(c echo.Context) error {
		return c.String(http.StatusOK, "OK")
	})

	v1 := e.Group("/api/v1")

	v1.GET("/database/backups", dbHandler.ListBackups)
	v1.POST("/database/backups", dbHandler.PerformBackup)
	v1.GET("/database/backups/:file", dbHandler.DownloadBackup)
	v1.GET("/database/terminals", dbHandler.GetCurrentJsonRecordTerminals)
	v1.POST("/database/terminals", dbHandler.AddJsonRecordTerminal)
	v1.GET("/database/seed", dbHandler.FetchCurrentSeed)
	v1.POST("/database/demo/reset", dbHandler.ResetDemoData)
	v1.GET("/database/demo/dump", dbHandler.DumpDemoData)
	v1.POST("/database/demo/restore", dbHandler.RestoreDemoData)

	v1.GET("/device/status", deviceHandler.Status)
	v1.POST("/device/printer/graphics", deviceHandler.UpdateGraphic)

	v1.GET("/identify", meshHandler.Identify)

	v1.GET("/logs", deviceHandler.ListLogs)
	v1.GET("/logs/:file", deviceHandler.DownloadLog)
	v1.GET("/logs/events", deviceHandler.StreamLogs)

	v1.POST("/system/reboot", deviceHandler.Reboot)
	v1.POST("/system/shutdown", deviceHandler.Shutdown)
	v1.POST("/system/update", deviceHandler.UpdateVersion)
	v1.GET("/system/metrics", deviceHandler.Metrics)
	v1.GET("/system/metrics/events", deviceHandler.StreamMetrics)
	v1.POST("/system/purge", deviceHandler.Purge)
	v1.GET("/system/power", deviceHandler.Power)

	v1.GET("/services/state", deviceHandler.ServicesState)
	v1.GET("/services/status", deviceHandler.ServicesStatus)
	v1.POST("/services/:service/restart", deviceHandler.RestartService)
	v1.POST("/services/:service/stop", deviceHandler.StopService)
	v1.POST("/services/:service/start", deviceHandler.StartService)

	v1.GET("/record", recordHandler.GetAllRecords)
	v1.GET("/record/:key", recordHandler.GetByKey)

	v1.GET("/customer", customerHandler.GetAll)
	v1.GET("/customer/:customerID", customerHandler.GetByID)

	v1.GET("/department", func(c echo.Context) error {
		return departmentHandler.GetAll(c, false)
	})
	v1.GET("/department/:departmentID", func(c echo.Context) error {
		return departmentHandler.GetByID(c, false)
	})
	v1.GET("/department/online", func(c echo.Context) error {
		return departmentHandler.GetAll(c, true)
	})
	v1.GET("/department/online/:departmentID", func(c echo.Context) error {
		return departmentHandler.GetByID(c, true)
	})

	v1.GET("/item", func(c echo.Context) error {
		return itemHandler.GetAll(c, false)
	})
	v1.POST("/item", func(c echo.Context) error {
		return itemHandler.GetManyByIDs(c)
	})
	v1.GET("/item/:itemID", func(c echo.Context) error {
		return itemHandler.GetByID(c, false)
	})
	v1.POST("/item/:itemID", func(c echo.Context) error {
		return itemHandler.AddItem(c)
	})
	v1.GET("/item/:departmentID/bydepartment", func(c echo.Context) error {
		return itemHandler.GetByDepartment(c, false)
	})
	v1.GET("/item/online", func(c echo.Context) error {
		return itemHandler.GetAll(c, true)
	})
	v1.GET("/item/online/menu", itemHandler.GetMenu)
	v1.GET("/item/online/:itemID", func(c echo.Context) error {
		return itemHandler.GetByID(c, true)
	})
	v1.GET("/item/online/:departmentID/bydepartment", func(c echo.Context) error {
		return itemHandler.GetByDepartment(c, true)
	})

	v1.POST("/sale/new", saleHandler.NewSale)
	v1.PUT("/sale/:saleID/starttendergroup", saleHandler.StartTenderGroup)
	v1.PUT("/sale/:saleID/recordtender", saleHandler.RecordTender)
	v1.PUT("/sale/:saleID/completetendergroup", saleHandler.CompleteTenderGroup)
	v1.GET("/sale/online/health", saleHandler.OnlineHealth)
	v1.POST("/sale/online/totals", saleHandler.OnlineOrderTotals)
	v1.POST("/sale/online/order", saleHandler.OnlineOrder)

	v1.POST("/sale/online/nmi/validate", saleHandler.OnlineNMIValidate)
	v1.POST("/sale/online/nmi/void", saleHandler.VoidNMITransaction)

	v1.PUT("/sale/:saleID/unlock", func(c echo.Context) error {
		return saleHandler.LockSale(c, true)
	})
	v1.PUT("/sale/:saleID/lock/:termNumber", func(c echo.Context) error {
		return saleHandler.LockSale(c, false)
	})

	e.GET("/metrics", echo.WrapHandler(promhttp.Handler()))

	// Start the server
	if err = e.Start(config.APIServerPort); err != nil {
		log.Fatal().Err(err).Msg("error starting api server")
	}
}
