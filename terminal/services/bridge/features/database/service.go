package database

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"net"
	"os"
	"os/exec"
	"strings"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	_ "github.com/lib/pq"

	"github.com/r2pos/bridge/features/mesh"

	"github.com/r2pos/bridge/internal/aws"
	"github.com/r2pos/bridge/internal/config"
)

type BackupFormat string

var (
	BackupFormatSQL BackupFormat = "sql"
	BackupFormatRaw BackupFormat = "raw"
)

type PostgresService struct {
	awsClient aws.Client

	config *config.Config
}

func NewPostgresService(
	awsClient aws.Client,
	config *config.Config,
) PostgresService {
	return PostgresService{awsClient, config}
}

func (s *PostgresService) GetConnectionString() string {
	return BuildConnectionString(
		s.config.PostgresHost, s.config.PostgresUser,
		s.config.PostgresPassword, s.config.PostgresDB,
	)
}

func (s *PostgresService) Backup(
	ctx context.Context, format BackupFormat, cloud bool,
) (name string, dest string, err error) {
	backupFileName := GenerateBackupFileName()
	backupDestination := FormatBackupFileDestination(
		s.config.PostgresBackupsSearchPath,
		backupFileName,
	)

	var args []string = []string{
		"-a",
		"-h", "localhost",
		"-U", "postgres",
		"-n", "public",
		"-f", backupDestination,
		"r2pos",
	}

	// if format is sql, then we need to prepend "--inserts"
	switch format {
	case BackupFormatSQL:
		args = append([]string{"--inserts", "--on-conflict-do-nothing"}, args...)
	case BackupFormatRaw:
		args = append([]string{"-Fc"}, args...)
		// do nothing
	default:
		return "", "", fmt.Errorf("invalid format")
	}

	dumpCommand := exec.CommandContext(ctx, "pg_dump", args...)
	var dumpOut bytes.Buffer
	var dumpErr bytes.Buffer
	dumpCommand.Stdout = &dumpOut
	dumpCommand.Stderr = &dumpErr
	err = dumpCommand.Run()
	if err != nil {
		return "", "", fmt.Errorf("pg_dump err: %w %s", err, dumpErr.String())
	}

	if cloud {
		cloudStoreKey := aws.FormatDeviceBackupsKey(
			s.config.BalenaDeviceUUID,
			backupFileName,
		)

		err = s.awsClient.Upload(cloudStoreKey, backupDestination)
		if err != nil {
			return "", "", fmt.Errorf("error uploading backup to cloud: %w", err)
		}
	}

	return backupFileName, backupDestination, nil
}

func (s *PostgresService) Seed(ctx context.Context, ip, mac string) error {
	if ip == "" || mac == "" {
		return fmt.Errorf("ip and mac are required")
	}

	seedFile, err := s.fetchSeedFile(
		config.AwsS3DataSeedFile,
		config.PostgresTmpDataSeedFile,
	)
	if err != nil {
		return fmt.Errorf("error fetching seed %s file: %w", config.AwsS3DataSeedFile, err)
	}

	db, err := sqlx.ConnectContext(ctx, "postgres", s.GetConnectionString())
	if err != nil {
		return fmt.Errorf("error opening database: %w", err)
	}
	defer db.Close()

	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("error beginning transaction: %w", err)
	}

	_, err = tx.QueryContext(ctx, string(seedFile))
	if err != nil {
		return fmt.Errorf("error executing seed file: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("error committing transaction: %w", err)
	}

	_, err = s.AddSystemDevice(ctx, ip, mac)
	if err != nil {
		return fmt.Errorf("error adding self to json_record devices: %w", err)
	}

	return nil
}

func (s *PostgresService) AddSystemDevice(
	ctx context.Context,
	ip, mac string,
) (*TerminalObject, error) {
	// mac and ip are required
	if ip == "" {
		return nil, fmt.Errorf("mac and/or ip is required")
	}

	if net.ParseIP(ip) == nil {
		return nil, fmt.Errorf("invalid IP address: %s", ip)
	}

	currentTerminals, err := s.GetSystemDevices(ctx)
	if err != nil {
		return nil, err
	}

	var idx = 98
	if len(currentTerminals) > 0 {
		idx = len(currentTerminals)
	}

	if mac == "" {
		networkInfo, err := mesh.GetRemoteNetworkInformation(ctx, ip)
		if err != nil {
			return nil, fmt.Errorf("error getting remote network information: %w", err)
		}

		mac = networkInfo.MAC
	}

	// check if terminal already exists by IP and mac
	for _, terminal := range currentTerminals {
		if terminal.IP == ip || terminal.Mac == mac {
			return nil, fmt.Errorf("terminal already exists with same Mac or IP Address")
		}
	}

	seedFile, err := s.fetchSeedFile(
		config.AwsS3TerminalSeedFile,
		config.PostgresTmpTerminalSeedFile,
	)
	if err != nil {
		return nil, fmt.Errorf("error downloading seed file: %w", err)
	}

	seedFileContents := string(seedFile)

	t, err := template.New("query").Parse(seedFileContents)
	if err != nil {
		return nil, fmt.Errorf("error parsing seed file: %w", err)
	}

	data := TerminalTemplateData{
		TerminalIP:  ip,
		TerminalMAC: mac,
		TerminalIdx: idx,
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, data); err != nil {
		return nil, fmt.Errorf("error executing seed file: %w", err)
	}

	resultingQuery := tpl.String()

	db, err := sqlx.ConnectContext(ctx, "postgres", s.GetConnectionString())
	if err != nil {
		return nil, fmt.Errorf("error opening database: %w", err)
	}
	defer db.Close()

	tx, err := db.Begin()
	if err != nil {
		return nil, fmt.Errorf("error beginning transaction: %w", err)
	}

	rows, err := tx.Query(resultingQuery)
	if err != nil {
		return nil, fmt.Errorf("error executing seed file: %w", err)
	}
	defer rows.Close()

	var tempSystemDeviceJsonRecord TerminalObject
	for rows.Next() {
		err = rows.Scan(&tempSystemDeviceJsonRecord)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("error committing transaction: %w", err)
	}

	return &tempSystemDeviceJsonRecord, nil
}

func (s *PostgresService) GetSystemDevices(ctx context.Context) ([]Terminal, error) {
	localDB, err := sqlx.ConnectContext(ctx, "postgres", s.GetConnectionString())
	if err != nil {
		return nil, fmt.Errorf("failed to connect to postgres: %w", err)
	}
	defer localDB.Close()

	var tempSystemDeviceJsonRecord JsonRecord
	err = localDB.GetContext(ctx, &tempSystemDeviceJsonRecord, "SELECT * FROM public.json_record WHERE record_key = 'systemDevice';")
	if err != nil {
		return nil, fmt.Errorf("failed to get systemDevice json record: %w", err)
	}

	return tempSystemDeviceJsonRecord.Document.Terminal, nil
}

func (s *PostgresService) DeleteFrom(ctx context.Context, tables []string) error {
	tbls := tables
	if len(tbls) == 0 {
		return fmt.Errorf("table is required")
	}

	if len(tbls) == 1 && tbls[0] == "*" {
		var err error
		tbls, err = s.GetAllTables(ctx)
		if err != nil {
			return fmt.Errorf("error getting all tables: %w", err)
		}
	}

	db, err := sqlx.Connect("postgres", s.GetConnectionString())
	if err != nil {
		return fmt.Errorf("error opening database")
	}
	defer db.Close()

	for i, tbl := range tbls {
		tbls[i] = pq.QuoteIdentifier(tbl)
	}
	truncateStmt := "TRUNCATE TABLE " + strings.Join(tbls, ", ") + " RESTART IDENTITY CASCADE;"
	_, err = db.ExecContext(ctx, truncateStmt)
	if err != nil {
		return fmt.Errorf("error truncating tables: %w", err)
	}

	return nil
}

func (s *PostgresService) ResetSaleNumber(ctx context.Context) error {

	db, err := sqlx.Connect("postgres", s.GetConnectionString())
	if err != nil {
		return fmt.Errorf("error opening database")
	}
	defer db.Close()

	_, err = db.QueryContext(ctx, `UPDATE "sale_number" SET "current"=0;`)
	if err != nil {
		return fmt.Errorf(`error resetting "sale_number" to 0: %w`, err)
	}

	return nil
}

func (s *PostgresService) DumpFrom(ctx context.Context, tables []string) (string, error) {
	tbls := tables
	if len(tbls) == 0 {
		return "", fmt.Errorf("table is required")
	}

	if len(tbls) == 1 && tbls[0] == "*" {
		var err error
		tbls, err = s.GetAllTables(ctx)
		if err != nil {
			return "", fmt.Errorf("error getting all tables: %w", err)
		}
	}

	var args []string
	args = []string{
		"-a",
		"--inserts",
		"--on-conflict-do-nothing",
		"-h", "localhost",
		"-U", "postgres",
		"-n", "public",
		"r2pos",
	}
	for _, table := range tbls {
		args = append(args, "-t")
		args = append(args, table)
	}

	fmt.Println(args)
	dumpCommand := exec.CommandContext(ctx, "pg_dump", args...)
	var dumpOut bytes.Buffer
	var dumpErr bytes.Buffer
	dumpCommand.Stdout = &dumpOut
	dumpCommand.Stderr = &dumpErr
	err := dumpCommand.Run()
	if err != nil {
		return "", fmt.Errorf("pg_dump err: %w %s", err, dumpErr.String())
	}

	return dumpOut.String(), nil
}

func (s *PostgresService) Restore(ctx context.Context, data []byte) error {
	db, err := sqlx.ConnectContext(ctx, "postgres", s.GetConnectionString())
	if err != nil {
		return fmt.Errorf("error opening database")
	}
	defer db.Close()

	_, err = db.ExecContext(ctx, string(data))
	if err != nil {
		return fmt.Errorf("error executing data: %w", err)
	}

	return nil
}

func (s *PostgresService) GetSelf(ctx context.Context, selfMac string) (*Terminal, error) {
	terminals, err := s.GetSystemDevices(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting system devices: %w", err)
	}

	for _, terminal := range terminals {
		if terminal.Mac == selfMac {
			return &terminal, nil
		}
	}

	return nil, fmt.Errorf("self not found")
}

func (s *PostgresService) GetAllTables(ctx context.Context) ([]string, error) {
	db, err := sqlx.ConnectContext(ctx, "postgres", s.GetConnectionString())
	if err != nil {
		return nil, fmt.Errorf("error opening database")
	}
	defer db.Close()

	var tables []string
	err = db.SelectContext(ctx, &tables, "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';")
	if err != nil {
		return nil, fmt.Errorf("error getting tables: %w", err)
	}

	return tables, nil
}

func (s *PostgresService) FetchCurrentSeed(ctx context.Context) ([]byte, error) {
	seedDataFile, err := s.fetchSeedFile(
		config.AwsS3DataSeedFile,
		config.PostgresTmpDataSeedFile,
	)
	if err != nil {
		return nil, fmt.Errorf("error fetching seed %s file: %w", config.AwsS3DataSeedFile, err)
	}

	return seedDataFile, nil
}

func (s *PostgresService) fetchSeedFile(key, dest string) ([]byte, error) {
	err := s.awsClient.Download(key, dest)
	if err != nil {
		return nil, fmt.Errorf("error downloading s3 file: %w", err)
	}

	seedFile, err := os.ReadFile(dest)
	if err != nil {
		return nil, fmt.Errorf("error reading s3=4 file: %w", err)
	}

	return seedFile, nil
}

func (s *PostgresService) FetchReportA(ctx context.Context) ([]map[string]interface{}, error) {
	return []map[string]interface{}{
		{
			"Id":   "1234",
			"Name": "John Doe",
		},
		{
			"Id":   "5678",
			"Name": "Bob Smith",
		},
		{
			"Id":   "9101",
			"Name": "Frank Furter",
		},
	}, nil
}
