package department

import "time"

type Department struct {
	Department string    `json:"department" db:"department"`
	Title      string    `json:"title" db:"title"`
	Document   Document  `json:"document" db:"document"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	CreatedBy  string    `json:"created_by" db:"created_by"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
	UpdatedBy  string    `json:"updated_by" db:"updated_by"`
}

type BasicDepartment struct {
	Department string  `json:"department" db:"department"`
	Title      string  `json:"title" db:"title"`
	MajorGroup *string `json:"majorGroup" db:"majorGroup"`
}

type Document struct {
	IsTaxable     *bool   `json:"isTaxable" db:"isTaxable"`
	ColorHash     *int    `json:"colorHash" db:"colorHash"`
	Order         *int    `json:"order" db:"order"`
	MajorGroup    *string `json:"majorGroup" db:"majorGroup"`
	Prep          int     `json:"prep" db:"prep"`
	TaxFlags      int     `json:"taxFlags" db:"taxFlags"`
	FriendlyTitle *string `json:"friendlyTitle" db:"friendlyTitle"`
	ShowOnline    *bool   `json:"showOnline" db:"showOnline"`
}

func (d *Document) GetShowOnline() bool {
	if d == nil || d.ShowOnline == nil {
		return true
	}
	return *d.ShowOnline
}
