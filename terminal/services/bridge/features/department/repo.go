package department

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"errors"

	"sort"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/r2pos/bridge/features/helpers"
)

type Repository interface {
	FindByID(ids []string) (*[]*Department, error)
	GetAll() (*[]*Department, error)
	DeleteByID(id string) (*Department, error)
	Upsert(department *Department) (*sql.Rows, error)
}

//nolint:revive // This is to prevent revive linting issues for the DepartmentRepo struct
type DepartmentRepo struct {
	DB        *sqlx.DB
	dbTimeout time.Duration
}

func NewRepo(db *sqlx.DB, dbTimeout time.Duration) *DepartmentRepo {
	return &DepartmentRepo{
		DB:        db,
		dbTimeout: dbTimeout,
	}
}

func (s *DepartmentRepo) FindByID(ids []string) (*[]*Department, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	qryStr := "SELECT * FROM department WHERE department = ANY(:ids);"
	sqlRows, err := helpers.DBQuery(s.DB, qryStr, map[string]any{"ids": pq.Array(ids)}, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &[]*Department{}
	err = helpers.DBRowsToStructs(sqlRows, result)
	if err != nil {
		return nil, err
	}

	sort.Slice(*result, func(i, j int) bool {
		return *(*result)[i].Document.Order < *(*result)[j].Document.Order
	})

	return result, nil
}

func (s *DepartmentRepo) GetAll() (*[]*Department, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	sqlRows, err := helpers.DBQuery(s.DB, "SELECT * FROM department;", nil, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &[]*Department{}
	err = helpers.DBRowsToStructs(sqlRows, result)
	if err != nil {
		return nil, err
	}

	sort.Slice(*result, func(i, j int) bool {
		return *(*result)[i].Document.Order < *(*result)[j].Document.Order
	})

	return result, nil
}

func (s *DepartmentRepo) DeleteByID(id string) (*Department, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	query := "DELETE FROM department WHERE department = :id;"
	params := map[string]any{"id": id}
	sqlRows, err := helpers.DBQuery(s.DB, query, params, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &Department{}
	err = helpers.DBRowToStruct(sqlRows, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s *DepartmentRepo) Upsert(dept *Department) (*Department, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	result, err := helpers.Upsert(s.DB, ctxTimeout, "department", dept, "department")
	if err != nil {
		return nil, fmt.Errorf("error in upsert: %w", err)
	}

	department, ok := result.(*Department)
	if !ok {
		return nil, errors.New("type assertion to *Department failed")
	}
	return department, nil
}
