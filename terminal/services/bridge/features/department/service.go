package department

import "errors"

//nolint:revive // This is to prevent revive linting issues for the DepartmentRepo struct
type DepartmentService struct {
	repo *DepartmentRepo
}

func NewService(
	repo *DepartmentRepo,
) *DepartmentService {
	return &DepartmentService{
		repo,
	}
}

func (s *DepartmentService) GetAll() (*[]*Department, error) {
	result, err := s.repo.GetAll()
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (s *DepartmentService) GetAllBasic() (*[]*BasicDepartment, error) {
	result, err := s.repo.GetAll()
	if err != nil {
		return nil, err
	}

	basicDepts := []*BasicDepartment{}
	for _, dept := range *result {
		if dept.Document.GetShowOnline() {
			basicDepts = append(basicDepts, s.DeptToBasicDept(dept))
		}
	}
	return &basicDepts, nil
}

func (s *DepartmentService) GetByID(deptID string) (*Department, error) {
	result, err := s.repo.FindByID([]string{deptID})
	if err != nil {
		return nil, err
	}
	if len(*result) == 0 {
		return nil, errors.New("Department not found")
	}

	return (*result)[0], nil
}

func (s *DepartmentService) GetByIDBasic(deptID string) (*BasicDepartment, error) {
	result, err := s.repo.FindByID([]string{deptID})
	if err != nil {
		return nil, err
	}
	if len(*result) == 0 {
		return nil, errors.New("Department not found")
	}

	return s.DeptToBasicDept((*result)[0]), nil
}

func (s *DepartmentService) DeptToBasicDept(dept *Department) *BasicDepartment {
	title := dept.Title
	if dept.Document.FriendlyTitle != nil {
		title = *dept.Document.FriendlyTitle
	}
	return &BasicDepartment{
		Department: dept.Department,
		Title:      title,
		MajorGroup: dept.Document.MajorGroup,
	}
}
