package helpers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"net/url"
	"reflect"
	"slices"
	"strconv"
	"strings"
	"time"

	xml2json "github.com/basgys/goxml2json"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/rs/zerolog/log"
)

//nolint:gochecknoglobals // I need this to be global.
var SAEmployee = "00000000-0000-0000-0000-000000000001"

//nolint:gochecknoglobals // I need this to be global.
var OnlineOrderEmployee = "00000000-0000-0000-0000-000000000002"

func GetJsonTags(input any) ([]string, map[string]reflect.Type) {
	res := []string{}
	types := make(map[string]reflect.Type)
	val := reflect.ValueOf(input)

	//nolint:intrange // this is fine.
	for i := 0; i < val.Type().NumField(); i++ {
		t := val.Type().Field(i)
		fieldName := t.Name

		switch jsonTag := t.Tag.Get("json"); jsonTag {
		case "-":
		case "":
			res = append(res, fieldName)
			types[fieldName] = t.Type
		default:
			parts := strings.Split(jsonTag, ",")
			name := parts[0]
			if name == "" {
				name = fieldName
			}
			res = append(res, name)
			types[name] = t.Type
		}
	}
	return res, types
}

func GetValues(input any, rows []string, types map[string]reflect.Type) ([]string, error) {
	res := []string{}
	bytes1, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}
	jsonMap := map[string]any{}
	err = json.Unmarshal(bytes1, &jsonMap)
	if err != nil {
		return nil, err
	}

	var sb strings.Builder
	for _, row := range rows {
		value := jsonMap[row]

		bytes2, err := json.Marshal(value)
		if err != nil {
			return nil, err
		}

		if types[row].Name() == "" {
			types[row] = reflect.TypeOf(value)
		}

		if types[row].Name() == "Document" {
			sb.WriteString("'" + string(bytes2) + "'")
		} else if types[row].Name() == "string" {
			sb.WriteString("'" + removeFirstAndLastChar(string(bytes2)) + "'")
		} else {
			sb.WriteString(string(bytes2))
		}

		res = append(res, sb.String())
		sb.Reset()
	}
	return res, nil
}

func GetRowsAndValues(input any, fieldsToSanitize *[]string) ([]string, []string, error) {
	rowList, types := GetJsonTags(input)

	if fieldsToSanitize != nil {
		rowList = RemoveStrings(rowList, *fieldsToSanitize)
	}

	valList, err := GetValues(input, rowList, types)
	if err != nil {
		return nil, nil, err
	}
	return rowList, valList, nil
}

func removeFirstAndLastChar(str string) string {
	return str[1 : len(str)-1]
}

func RemoveStrings(slice []string, toRemove []string) []string {
	result := []string{}
	for _, value := range slice {
		if !slices.Contains(toRemove, value) {
			result = append(result, value)
		}
	}
	return result
}

// This is required by Go to create literals for constructors that expect
// pointers.
//
// Example:
//
// ```go
//
//	func main() {
//		var x *int
//
//		// Assigning a literal (0) to x takes two steps.
//		x = new(int)
//		*x = 0
//
//
//		// This is invalid in Go.
//		x = &0
//
//
//		var y *int
//
//		// With Ptr, it's just one step.
//		y = Ptr(0)
//	}
//
// ```
//
// Makes things succinter. This is a common pattern in Go.
func Ptr[T any](v T) *T {
	return &v
}

// sqlx/pq sometimes returns an error with a position in the query; this
// helper converts that position to a line and column.
func positionToLineColumn(query string, pos int) (int, int) {
	// pos is 1-based index
	// Convert to zero-based for indexing in Go
	idx := pos - 1
	if idx < 0 || idx >= len(query) {
		return 1, 1 // fallback, or handle error
	}

	line := 1
	col := 1
	for i := 0; i < idx; i++ {
		if query[i] == '\n' {
			line++
			col = 1
		} else {
			col++
		}
	}
	return line, col
}

// Wrap a DB error with additional information and logging. Use this on errors returned from sqlx.
func WrapDBError(dbErr error, sql string) error {
	if pqErr, ok := dbErr.(*pq.Error); ok {

		lineInfo := "N/A"
		if pqErr.Position != "" {
			position, err := strconv.Atoi(pqErr.Position)
			if err != nil {
				return fmt.Errorf("error while extracting database error, %w; original error: %v", err, dbErr)
			}

			line, col := positionToLineColumn(sql, position)

			lineInfo = fmt.Sprintf("line %d, column %d", line, col)
		}

		json, err := json.Marshal(pqErr)
		if err != nil {
			return fmt.Errorf("error while extracting database error, %w; original error: %v", err, dbErr)
		}
		log.Error().Str("pqErr", string(json)).Str("lineInfo", lineInfo).Str("sql", sql).Msg("DB error")

		return fmt.Errorf("error querying db: %w, line info: %v", dbErr, lineInfo)
	}
	return dbErr
}

// Utility function to create a DB connection.
func CreateDBConnOrDie(dsn string) *sqlx.DB {

	// We create the sqlx.DB in two steps, because sqlx.ConnectContext() attempts
	// an actual connection and will fail if the network is down or the connection
	// string is wrong etc. at startup instead of at runtime, which is typical for
	// pools.
	//
	// Since our initialization thread is meant not to crash, we use sql.Open().
	//
	// Supposedly, err here fails only if something more catastrophic happens than
	// just a failed connection attempt.
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		log.Fatal().Err(err).Msg("error creating DB connection")
	}

	dbx := sqlx.NewDb(db, "postgres")

	return dbx
}

// Query the DB, using named arguments, and with a timeout context.
//
// Example:
//
// ```go
// ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), time.Second*3)
// defer ctxCancel()
//
// db, err := ...
// ...
// result, err := DBQuery(db, "SELECT * FROM table WHERE id = :id", map[string]any{"id": 1}, ctxTimeout)
// ```
func DBQuery(db *sqlx.DB, sql string, namedValues map[string]any, ctxTimeout context.Context) (*sqlx.Rows, error) {
	result, err := db.NamedQueryContext(ctxTimeout, sql, namedValues)
	if err != nil {
		return nil, fmt.Errorf("error querying db: %w", WrapDBError(err, sql))
	}
	return result, nil
}

// Represents a column value in a DB row, with metadata for constructing an SQL query.
type PGColValueMeta struct {
	// The column name in the DB.
	ColName string
	// The type, for casting if necessary. E.g. 'TEXT', 'INTEGER', etc.
	PGType string
	// E.g '$1', or ':name'
	VarLitName string
	// The underlying value.
	Value any
}

// Convert a struct, that represents a row in the DB, into something you can use
// to construct an SQL query.
//
// This assumes a struct's fields are equivalent to columns in a database.
// Example: sale.models.Sale.
//
// Primitive fields are converted to their corresponding Postgres types.
//
// Struct fields are converted to JSON strings, and the PGType is set to 'JSON'.
//
// See StructToDBCols for the reverse operation, and corresponding documentation.
//
// Parameters:
//   - dbRowStruct: The struct that represents a row in the DB. Pass the struct in
//     by value into the any. Must have field tags with key "db" indicating the
//     column name.
//   - sanitizedFields: Fields to ignore when converting the struct to DB columns.
//   - namedValues: A map to store the named values. If nil, the named values
//     are not stored.
//   - nameCounter: A counter to generate the $1, $2, ... values. If nil, the
//     counter is not used. Exactly one of nameCounter and namePrefix must be
//     used; they are mutually exclusive.
//   - namePrefix: A prefix to generate the :prefixname values. If nil, the
//     prefix is not used. Exactly one of nameCounter and namePrefix must be
//     used; they are mutually exclusive. Usually you'll use an empty string.
//
// Returns:
//   - A list of PGColValueMeta, which can be used to construct an SQL query.
//   - An error if the conversion fails.
func StructToDBCols(dbRowStructPtr any,
	sanitizedFields []string,
	namedValues *map[string]any,
	nameCounter *int,
	namePrefix *string,
) ([]PGColValueMeta, error) {

	if !isPointerType(reflect.TypeOf(dbRowStructPtr)) {
		return nil, fmt.Errorf("error getting pg value: dbRowStructPtr must be a pointer")
	}

	if (nameCounter == nil) == (namePrefix == nil) {
		return nil, fmt.Errorf("error getting pg value: nameCounter and namePrefix must be mutually exclusive")
	}

	valueReflected := reflect.ValueOf(dbRowStructPtr).Elem()
	t := valueReflected.Type()
	columns := []PGColValueMeta{}
	for i := 0; i < t.NumField(); i++ {
		fieldType := t.Field(i)
		fieldName := fieldType.Name
		fieldTag := fieldType.Tag.Get("db")
		fieldValue := valueReflected.Field(i).Interface()

		if fieldTag == "-" {
			continue
		}
		if fieldTag == "" {
			continue
		}
		if (fieldTag == "created_by" || fieldTag == "updated_by") && fieldValue == "" {
			fieldValue = OnlineOrderEmployee
		}

		if slices.Contains(sanitizedFields, fieldTag) {
			continue
		}

		// Ensure that fieldTag is not already in values
		if namedValues != nil {
			if _, ok := (*namedValues)[fieldTag]; ok {
				return nil, fmt.Errorf("error getting pg value for %v: value with name already exists", fieldName)
			}
		}

		varLitName := fieldTag
		varName := fieldTag
		if nameCounter != nil {
			varLitName = fmt.Sprintf("$%v", *nameCounter)
			// var name makes no sense if we are using counters.
			varName = ""
			*nameCounter++
		} else if namePrefix != nil {
			varLitName = fmt.Sprintf(":%v%v", *namePrefix, fieldTag)
			varName = fmt.Sprintf("%v%v", *namePrefix, fieldTag)
		}

		if fieldType.Type.Kind() == reflect.Struct {
			json, err := json.Marshal(fieldValue)
			if err != nil {
				return nil, fmt.Errorf("error getting pg value for %v, while marshalling to json: %w", fieldName, err)
			}

			columns = append(columns, PGColValueMeta{
				ColName:    fieldTag,
				PGType:     "JSON",
				VarLitName: varLitName,
				Value:      string(json),
			})
			if namedValues != nil && namePrefix != nil {
				(*namedValues)[varName] = string(json)
			}
		} else {
			pgType, err := getPGType(fieldType.Type)
			if err != nil {
				return nil, fmt.Errorf("error getting pg value for %v: %w", fieldName, err)
			}
			columns = append(columns, PGColValueMeta{
				ColName:    fieldTag,
				PGType:     pgType,
				VarLitName: varLitName,
				Value:      &fieldValue,
			})
			if namedValues != nil && namePrefix != nil {
				(*namedValues)[varName] = fieldValue
			}
		}
	}
	return columns, nil
}

func isIntegerType(t reflect.Type) bool {
	return t.Kind() == reflect.Int || t.Kind() == reflect.Int64 || t.Kind() == reflect.Int32 || t.Kind() == reflect.Int16 || t.Kind() == reflect.Int8
}

// Assignment, but for opaque types.
//
// Has some extra conversions for special cases, that are typical for PG <=> Go.
//
// fieldPtr must be a pointer to destination variable.
func setFieldValue(fieldPtr any, newValue any) error {
	fieldPtrType := reflect.TypeOf(fieldPtr)
	if !isPointerType(fieldPtrType) {
		return fmt.Errorf("error setting value: fieldPtr is not a pointer; it is a %v", fieldPtrType.Kind())
	}

	fieldType := reflect.TypeOf(fieldPtr).Elem()
	resultType := fieldType

	// The non-optional non-pointer type.
	if fieldType.Kind() == reflect.Ptr {
		// This is an optional field. Result type is the underlying type.
		resultType = fieldType.Elem()
	}

	result := reflect.New(resultType).Elem()
	if newValue == nil {
		// Leaving the field as the default value.
	} else {
		// We need to do some introspection here for certain PG returned types, because PG returns weird types for some things.
		reflNewValue := reflect.ValueOf(newValue)

		if result.Type() != reflNewValue.Type() {
			resultIsString := result.Kind() == reflect.String
			valueIsBytes := reflNewValue.Kind() == reflect.Slice && reflNewValue.Type().Elem().Kind() == reflect.Uint8
			resultIsInteger := isIntegerType(fieldType)
			valueIsInteger := isIntegerType(reflNewValue.Type())
			if resultIsString && valueIsBytes {
				// Note: For some reason, strings sometimes come back as a slice of uint8s.

				// If the destination is a string and the value is a byte slice, convert the
				// byte slice to a string.
				vBytes, ok := newValue.([]byte)
				if !ok {
					return fmt.Errorf("error setting value: error converting %v to bytes for string conversion", newValue)
				}
				result.SetString(string(vBytes))
			} else if resultIsInteger && valueIsInteger {
				// If the destination is an integer and the value is an integer, convert the
				// value to the destination type.
				result.SetInt(reflNewValue.Int())
			} else {
				return fmt.Errorf("error setting value: types do not match, %v != %v", result.Type(), reflNewValue.Type())
			}
		} else {
			result.Set(reflNewValue)
		}
	}

	if fieldType.Kind() == reflect.Ptr {
		reflect.ValueOf(fieldPtr).Elem().Set(result.Addr())
	} else {
		reflect.ValueOf(fieldPtr).Elem().Set(result)
	}

	return nil
}

// Convert a row from the DB into a struct that corresponds to the row.
//
// See StructToDBCols for the reverse operation, and corresponding documentation.
func DBRowToStruct(rows *sqlx.Rows, resultPtr any) error {
	if !isPointerType(reflect.TypeOf(resultPtr)) {
		return fmt.Errorf("error structing from db row: result must not be a pointer")
	}

	resultNamedValues := map[string]any{}

	v := reflect.ValueOf(resultPtr).Elem()
	t := v.Type()

	err := rows.MapScan(resultNamedValues)
	if err != nil {
		return fmt.Errorf("error structing from db row: error scanning row: %w", err)
	}
	for i := 0; i < t.NumField(); i++ {
		fieldType := t.Field(i)
		fieldTag := fieldType.Tag.Get("db")
		if fieldTag == "-" {
			continue
		}
		if fieldTag == "" {
			continue
		}
		fieldPtr := v.Field(i).Addr().Interface()

		if _, ok := resultNamedValues[fieldTag]; !ok {
			// Setting to nil sets it to the default value.
			err = setFieldValue(fieldPtr, nil)
			if err != nil {
				return fmt.Errorf("error structing from db row: error setting value on field %v: %w", fieldType.Name, err)
			}
			continue
		}

		if fieldType.Type.Kind() == reflect.Struct && fieldType.Type.String() != "time.Time" {
			// If the field is a struct, it is usually stored in the DB as a json string, e.g SaleDocument.
			jsonBytes, ok := resultNamedValues[fieldTag].([]byte)
			if !ok {
				return fmt.Errorf("error structing from db row: error setting value on field %v: error converting %v to bytes for unmarshalling", fieldType.Name, resultNamedValues[fieldTag])
			}
			err = json.Unmarshal(jsonBytes, fieldPtr)
			if err != nil {
				return fmt.Errorf("error structing from db row: error setting value on field %v: error unmarshalling json: %w", fieldType.Name, err)
			}
		} else {
			// Otherwise set the value directly.
			err = setFieldValue(fieldPtr, resultNamedValues[fieldTag])
			if err != nil {
				return fmt.Errorf("error structing from db row: error setting value on field %v: %w", fieldType.Name, err)
			}
		}

	}
	return nil
}

// rows, err := db.Queryx("SELECT * FROM sale")
// ...
// sales := []Sale{}
// err := DBRowsToStructs(rows, &sales)
// ...
func DBRowsToStructs(rows *sqlx.Rows, resultsPtrListPtr any) error {
	resultsPtrListRfl := reflect.ValueOf(resultsPtrListPtr).Elem()
	// list type.
	lt := resultsPtrListRfl.Type()
	// list element type (should be a pointer type)
	let := lt.Elem()
	// struct type
	t := let.Elem()

	for rows.Next() {
		resultPtr := createAndPtr(t)
		err := DBRowToStruct(rows, resultPtr)
		if err != nil {
			return fmt.Errorf("error structing from db rows: %w", err)
		}
		resultsPtrListRfl.Set(reflect.Append(resultsPtrListRfl, reflect.ValueOf(resultPtr)))
	}
	if err := rows.Err(); err != nil {
		return fmt.Errorf("error structing from db rows: %w", err)
	}
	return nil
}

// getPGType returns the Postgres type for a given Go type.
// It returns an error if the type is not supported.
func getPGType(t reflect.Type) (string, error) {
	// if t is a pointer, get the underlying type
	if isPointerType(t) {
		t = t.Elem()
	}

	switch t.Kind() {
	case reflect.String:
		return "TEXT", nil
	case reflect.Bool:
		return "BOOLEAN", nil
	case reflect.Int:
		return "INTEGER", nil
	case reflect.Int64:
		return "INTEGER", nil
	case reflect.Float64:
		return "FLOAT", nil
	// Check if its time.Time
	case reflect.Struct:
		if t.String() == "time.Time" {
			return "TIMESTAMP", nil
		}
		return "", fmt.Errorf("error getting pg type for %v: unsupported struct type", t)
	case reflect.Slice:
		innerType := t.Elem()
		if innerType.Kind() == reflect.Uint8 {
			return "BYTEA", nil
		}

		innerPgType, err := getPGType(innerType)
		if err != nil {
			return "", fmt.Errorf("error getting pg type for %v while getting pg type for %v: %w", innerType, t, err)
		}
		return innerPgType + "[]", nil
	}
	return "", fmt.Errorf("error getting pg type for %v: unsupported type", t)
}

// Return an any of the field in the struct that has the tag searchFieldTag.
func getDBRowStructField(dbRowStructPtr any, searchFieldTag string) (any, error) {
	if !isPointerType(reflect.TypeOf(dbRowStructPtr)) {
		panic("dbRowStructPtr must be a pointer")
	}

	v := reflect.ValueOf(dbRowStructPtr).Elem()
	t := v.Type()

	for i := 0; i < t.NumField(); i++ {
		fieldType := t.Field(i)
		fieldTag := fieldType.Tag.Get("db")
		if fieldTag == searchFieldTag {
			return v.Field(i).Interface(), nil
		}
	}
	return nil, fmt.Errorf("error getting pk: %v not found in struct", searchFieldTag)
}

func isPointerType(t reflect.Type) bool {
	return t.Kind() == reflect.Ptr
}

// Return true if the value is the default value for its type.
func isDefaultAny(v any) bool {
	if v == nil {
		return true
	}

	lhs := reflect.ValueOf(v)
	rhs := reflect.Zero(lhs.Type())
	return reflect.DeepEqual(lhs.Interface(), rhs.Interface())
}

// Shortcut for creating a reflected struct or other type, and returning a
// pointer to it, wrapped in an any.
func createAndPtr(t reflect.Type) any {
	return reflect.New(t).Interface()
}

// All the error handling boilerplate necessary to extract a row from sqlx.Rows.
func AdvanceOneRow(rows *sqlx.Rows) error {
	if !rows.Next() {
		if err := rows.Err(); err != nil {
			return fmt.Errorf("error advancing one row: %w", err)
		}
		return fmt.Errorf("error advancing one row: no rows returned")
	}
	return nil
}

// A generic upsert function for a struct that represents a row in the DB.
//
// The column information is expected to be in the struct's field tags, with the
// key "db".
//
// Some other details/notes:
//
//   - It is expected for the table to have {updated_at, created_at} fields.
//   - If the primary key is a default value, it is interpreted to mean that the
//     that this is an insertion, and the primary key field is ignored (and the
//     DB generates a new value).
//
// Parameters:
//   - db: The DB connection.
//   - ctxTimeout: The context timeout.
//   - table: The table name.
//   - dbRowStructPtr: A pointer to the struct that represents the row in the DB.
//   - pkFieldTag: The "db" field tag of the primary key.
//
// Returns:
//   - Result is an any of a pointer to a new struct that represents the
//     inserted/updated row in the DB.
func Upsert(db *sqlx.DB,
	ctxTimeout context.Context,
	table string,
	dbRowStructPtr any,
	pkFieldTag string,
) (any, error) {

	// Panic if the struct is not a pointer.
	if !isPointerType(reflect.TypeOf(dbRowStructPtr)) {
		panic("dbRowStructPtr must be a pointer")
	}

	dbRowStructTypeName := reflect.TypeOf(dbRowStructPtr).Elem().Name()
	pkValue, err := getDBRowStructField(dbRowStructPtr, pkFieldTag)
	if err != nil {
		return nil, fmt.Errorf("error in upsert: error getting primary key: %w", err)
	}

	fieldsToSanitize := []string{"updated_at"}
	// Check if pkValue is default value, and if so, we will sanitize the pkField, so that the DB can generate a new value.
	if isDefaultAny(pkValue) {
		fieldsToSanitize = append(fieldsToSanitize, pkFieldTag, "created_at")
	}

	// Construct a string that represents the row, for {error, logging} purposes.
	rowName := fmt.Sprintf("%v (%v=%v)", dbRowStructTypeName, pkFieldTag, pkValue)

	namedValues := make(map[string]any)
	cols, err := StructToDBCols(dbRowStructPtr, fieldsToSanitize, &namedValues, nil, Ptr(""))
	if err != nil {
		return nil, fmt.Errorf("error in upsert: for row %v error converting struct to pg cols: %w", rowName, err)
	}

	var sb strings.Builder

	sb.WriteString(fmt.Sprintf("INSERT INTO %s (", table))

	for i, colMeta := range cols {
		sb.WriteString(colMeta.ColName)
		if i != len(cols)-1 {
			sb.WriteString(", ")
		}
	}

	sb.WriteString(")\nVALUES (")

	for i, colMeta := range cols {
		if i != 0 {
			sb.WriteString(", ")
		}
		sb.WriteString(colMeta.VarLitName)
	}

	sb.WriteString(fmt.Sprintf(")\nON CONFLICT (%s) DO UPDATE\nSET", table))

	for i, colMeta := range cols {
		sb.WriteString("  " + colMeta.ColName + " = " + colMeta.VarLitName)

		if i != len(cols)-1 {
			sb.WriteString(",\n")
		}
	}

	sb.WriteString("\nRETURNING *;")

	sql := sb.String()

	sqlRows, err := DBQuery(db, sql, namedValues, ctxTimeout)
	if err != nil {
		return nil, fmt.Errorf("error in upsert: for row %v: failed to query db: %w", rowName, err)
	}
	defer sqlRows.Close()

	err = AdvanceOneRow(sqlRows)
	if err != nil {
		return nil, fmt.Errorf("error in upsert: could not advance one row: for row %v: %w", rowName, err)
	}

	// Create a struct of the same type as dbRowStructPtr.
	//
	// We have to do this way because we don't have the actual time, meaning it needs to be reflected.
	resultPtr := createAndPtr(reflect.TypeOf(dbRowStructPtr).Elem())
	err = DBRowToStruct(sqlRows, resultPtr)
	if err != nil {
		return nil, err
	}

	return resultPtr, nil
}

func SafeInt64ToInt(value int64) (int, error) {
	if value < math.MinInt || value > math.MaxInt {
		return 0, fmt.Errorf("value %d is out of range for int", value)
	}
	return int(value), nil
}

func TimeToMillis(time time.Time) int {
	hour, minute, second := time.Clock()

	ms := (hour * 3600000) +
		(minute * 60000) +
		(second * 1000)

	return ms
}

func MillisToTime(ms int) time.Time {
	// Break down the total milliseconds into hours, minutes, seconds
	hours := ms / 3_600_000
	ms %= 3_600_000
	minutes := ms / 60_000
	ms %= 60_000
	seconds := ms / 1_000

	// Grab today's date and location
	now := GetCurrentTime()
	year, month, day := now.Date()
	loc := now.Location()

	// Construct a time.Time with that exact hour/minute/second today
	return time.Date(year, month, day, hours, minutes, seconds, 0, loc)
}

func MilitaryIntToTime(militaryTime int) time.Time {
	// Extract hour and minute
	hour := militaryTime / 100
	minute := militaryTime % 100

	// Get current date and location
	now := GetCurrentTime()
	year, month, day := now.Date()
	loc := now.Location()

	// Construct a time.Time for that hour/minute today
	return time.Date(year, month, day, hour, minute, 0, 0, loc)
}

func MilitaryIntToMillis(militaryTime int) int {
	hour := militaryTime / 100
	minute := militaryTime % 100

	// Convert the hour and minute to milliseconds
	return (hour * 3600000) + (minute * 60000)
}

func GetCurrentTime() time.Time {
	edt := time.FixedZone("EDT", -4*60*60)
	return time.Now().In(edt)
}

func XMLToJSON(xmlRaw string) (*map[string]interface{}, error) {
	jsonData, err := xml2json.Convert(strings.NewReader(xmlRaw))
	if err != nil {
		return nil, err
	}
	var jsonMap map[string]interface{} // or map[string]any
	if err := json.Unmarshal(jsonData.Bytes(), &jsonMap); err != nil {
		return nil, fmt.Errorf("unmarshalling JSON: %w", err)
	}
	return &jsonMap, nil
}

func NMIResponseToJSON(raw string) (*map[string]interface{}, error) {
	values, err := url.ParseQuery(raw)
	if err != nil {
		return nil, err
	}

	flat := make(map[string]interface{}, len(values))
	for k, v := range values {
		if len(v) > 0 {
			flat[k] = v[0]
		} else {
			flat[k] = ""
		}
	}

	return &flat, nil
}
