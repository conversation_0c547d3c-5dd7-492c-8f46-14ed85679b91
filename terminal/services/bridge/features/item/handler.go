package item

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type Handler struct {
	itemService *ItemService
}

func NewHandler(
	itemService *ItemService,
) *Handler {
	return &Handler{
		itemService,
	}
}

func (h *Handler) GetAll(c echo.Context, online bool) error {
	log.Info().Msg("getting all items")

	result, err := h.itemService.GetAll(online, false)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("got all items")

	return c.String(http.StatusOK, fmt.Sprintf(`{"items": %v}`, string(bytes)))
}

func (h *<PERSON><PERSON>) GetManyByIDs(c echo.Context) error {
	var req struct {
		IDs []string `json:"ids"`
	}

	err := c.Bind(&req)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	result, err := h.itemService.GetManyByIDs(req.IDs)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) GetByDepartment(c echo.Context, online bool) error {
	log.Info().Msg("getting items by department")

	departmentID := c.Param("departmentID")

	result, err := h.itemService.GetByDepartment(departmentID, online, false)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("Got items by department %v", departmentID)

	return c.String(http.StatusOK, fmt.Sprintf(`{"items": %v}`, string(bytes)))
}

func (h *Handler) GetByID(c echo.Context, online bool) error {
	log.Info().Msg("getting item by ID")

	itemID := c.Param("itemID")
	result, err := h.itemService.GetByID(itemID, online)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("got item by ID %v", itemID)

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) GetMenu(c echo.Context) error {
	log.Info().Msg("getting store menu")

	result, err := h.itemService.GetMenu()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("got store menu")

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) AddItem(c echo.Context) error {
	item := new(Item)

	if err := c.Bind(item); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err)
	}

	result, err := h.itemService.Upsert(item)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}
