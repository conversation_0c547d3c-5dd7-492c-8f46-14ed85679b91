package item

import (
	"time"

	"github.com/r2pos/bridge/features/department"
	"github.com/r2pos/bridge/features/record"
)

type Item struct {
	Item                   string                 `json:"item" db:"item"`
	LongDesc               string                 `json:"long_desc" db:"long_desc"`
	Upc                    string                 `json:"upc" db:"upc"`
	Department             string                 `json:"department" db:"department"`
	Document               Document               `json:"document" db:"document"`
	CreatedAt              time.Time              `json:"created_at" db:"created_at"`
	CreatedBy              string                 `json:"created_by" db:"created_by"`
	UpdatedAt              time.Time              `json:"updated_at" db:"updated_at"`
	UpdatedBy              string                 `json:"updated_by" db:"updated_by"`
	DepartmentByDepartment *department.Department `json:"departmentByDepartment" db:"-"`
}

type Document struct {
	Pricing          map[string]int     `json:"pricing" db:"pricing"`
	IsWeighted       bool               `json:"isWeighted" db:"isWeighted"`
	IsOpenPrice      bool               `json:"isOpenPrice" db:"isOpenPrice"`
	Uom              *int               `json:"UOM" db:"UOM"`
	ReceiptDesc      *string            `json:"receiptDesc" db:"receiptDesc"`
	ModifierDesc     *string            `json:"modifierDesc" db:"modifierDesc"`
	OnlineDesc       *string            `json:"onlineDesc" db:"onlineDesc"`
	AllowEbt         bool               `json:"allowEbt" db:"allowEbt"`
	PromptForPrice   bool               `json:"promptForPrice" db:"promptForPrice"`
	PinToTop         bool               `json:"pinToTop" db:"pinToTop"`
	NegativeItem     bool               `json:"negativeItem" db:"negativeItem"`
	IsModifier       bool               `json:"isModifier" db:"isModifier"`
	MultiModLists    bool               `json:"multiModLists" db:"multiModLists"`
	ModMaxSel        int                `json:"modMaxSel" db:"modMaxSel"`
	ModMinSel        int                `json:"modMinSel" db:"modMinSel"`
	Modifiers        map[string]ModData `json:"modifiers" db:"modifiers"`
	PassDesc         bool               `json:"passDesc" db:"passDesc"`
	OverridePricing  bool               `json:"overridePricing" db:"overridePricing"`
	DefModPricing    map[string]int     `json:"defModPricing" db:"defModPricing"`
	Count            *int               `json:"count" db:"count"`
	Prep             int                `json:"prep" db:"prep"`
	TakeOutSurcharge bool               `json:"takeOutSurcharge" db:"takeOutSurcharge"`
	PrintSeparate    bool               `json:"printSeparate" db:"printSeparate"`
	DetailedDesc     *string            `json:"detailedDesc" db:"detailedDesc"`
	ShowOnline       *bool              `json:"showOnline" db:"showOnline"`
}

func (d *Document) GetCount() int {
	if d == nil || d.Count == nil {
		return -1
	}
	return *d.Count
}

func (d *Document) GetShowOnline() bool {
	if d == nil || d.ShowOnline == nil {
		return true
	}
	return *d.ShowOnline
}

type ModData struct {
	Pricing      map[string]int `json:"pricing" db:"pricing"`
	Idx          int            `json:"idx" db:"idx"`
	ForceVisible bool           `json:"forceVisible" db:"forceVisible"`
}

type StoreMenu struct {
	StoreInfo   StoreInfo                     `json:"storeInfo"`
	Departments []*department.BasicDepartment `json:"departments"`
	Items       map[string][]*BasicItem       `json:"items"`
}

type StoreInfo struct {
	Name         string              `json:"name"`
	Address      string              `json:"address"`
	City         string              `json:"city"`
	State        string              `json:"state"`
	Phone        string              `json:"phone"`
	OnlineActive bool                `json:"onlineActive"`
	StoreHours   *record.ActiveHours `json:"storeHours"`
	Delivery     *record.Delivery    `json:"delivery"`
}

type BasicItem struct {
	Item         string  `json:"item"`
	Price        int     `json:"price"`
	Desc         string  `json:"desc"`
	Department   string  `json:"department"`
	Count        int     `json:"count"`
	DetailedDesc *string `json:"detailedDesc"`
}

type DetailedItem struct {
	Item          string         `json:"item"`
	Price         int            `json:"price"`
	Desc          string         `json:"desc"`
	ModifierDesc  *string        `json:"modifierDesc"`
	MultiModLists bool           `json:"multiModLists"`
	ModMaxSel     int            `json:"modMaxSel"`
	ModMinSel     int            `json:"modMinSel"`
	Count         int            `json:"count"`
	Selected      bool           `json:"selected"`
	Qty           int            `json:"qty"`
	Modifiers     []DetailedItem `json:"modifiers"`
	IsVisible     bool           `json:"isVisible"`
}
