package item

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"errors"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/r2pos/bridge/features/department"
	"github.com/r2pos/bridge/features/helpers"
)

type Repository interface {
	FindByID(ids []string) (*[]*Item, error)
	FindByDepartment(id string) (*[]*Item, error)
	GetAll() (*[]*Item, error)
	DeleteByID(id string) (*Item, error)
	Upsert(item *Item) (*sql.Rows, error)
}

type ItemRepo struct {
	DB                *sqlx.DB
	dbTimeout         time.Duration
	departmentService *department.DepartmentService
}

func NewRepo(
	db *sqlx.DB,
	dbTimeout time.Duration,
	departmentService *department.DepartmentService,
) *ItemRepo {
	return &ItemRepo{
		DB:                db,
		dbTimeout:         dbTimeout,
		departmentService: departmentService,
	}
}

func (s *ItemRepo) FindByID(ids []string) (*[]*Item, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	qryStr := "SELECT * FROM item WHERE item = ANY(:ids);"
	sqlRows, err := helpers.DBQuery(s.DB, qryStr, map[string]any{"ids": pq.Array(ids)}, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	items := &[]*Item{}
	err = helpers.DBRowsToStructs(sqlRows, items)
	if err != nil {
		return nil, err
	}

	result := &[]*Item{}
	for _, item := range *items {
		dept, err := s.departmentService.GetByID(item.Department)
		if err != nil {
			return nil, err
		}
		item.DepartmentByDepartment = dept
		*result = append(*result, item)
	}

	return result, nil
}

func (s *ItemRepo) FindByDepartment(id string) (*[]*Item, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	qryStr := "SELECT * FROM item WHERE department = :id;"
	sqlRows, err := helpers.DBQuery(s.DB, qryStr, map[string]any{"id": id}, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &[]*Item{}
	err = helpers.DBRowsToStructs(sqlRows, result)
	if err != nil {
		return nil, err
	}

	returnList, err := s.attachDepartmentByDepartment(result)
	if err != nil {
		return nil, err
	}

	return returnList, nil
}

func (s *ItemRepo) GetAll() (*[]*Item, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	sqlRows, err := helpers.DBQuery(s.DB, "SELECT * FROM item;", nil, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &[]*Item{}
	err = helpers.DBRowsToStructs(sqlRows, result)
	if err != nil {
		return nil, err
	}

	returnList, err := s.attachDepartmentByDepartment(result)
	if err != nil {
		return nil, err
	}

	return returnList, nil
}

func (s *ItemRepo) DeleteByID(id string) (*Item, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	qryStr := "DELETE FROM item WHERE item = :id;"
	sqlRows, err := helpers.DBQuery(s.DB, qryStr, map[string]any{"id": id}, ctxTimeout)
	if err != nil {
		return nil, err
	}
	if err = sqlRows.Err(); err != nil {
		return nil, err
	}

	result := &Item{}

	err = helpers.DBRowToStruct(sqlRows, result)
	if err != nil {
		return nil, err
	}

	returnList, err := s.attachDepartmentByDepartment(&[]*Item{result})
	if err != nil {
		return nil, err
	}

	if len(*returnList) == 0 {
		return nil, errors.New("no items found in update result")
	}

	return (*returnList)[0], nil
}

func (s *ItemRepo) Upsert(item *Item) (*Item, error) {
	ctxTimeout, ctxCancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer ctxCancel()

	result, err := helpers.Upsert(s.DB, ctxTimeout, "item", item, "item")
	if err != nil {
		return nil, fmt.Errorf("error in upsert: %w", err)
	}

	item, ok := result.(*Item)
	if !ok {
		return nil, errors.New("type assertion to *Item failed")
	}
	return item, nil
}

func (s *ItemRepo) attachDepartmentByDepartment(items *[]*Item) (*[]*Item, error) {
	allDepts, err := s.departmentService.GetAll()
	if err != nil {
		return nil, err
	}

	deptMap := make(map[string]department.Department)
	for _, dept := range *allDepts {
		deptMap[dept.Department] = *dept
	}

	returnList := []*Item{}
	for i, item := range *items {
		dept := deptMap[item.Department]
		(*items)[i].DepartmentByDepartment = &dept
		returnList = append(returnList, item)
	}

	return &returnList, nil
}
