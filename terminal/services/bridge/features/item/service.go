package item

import (
	"fmt"
	"sort"
	"time"

	"errors"

	"github.com/r2pos/bridge/features/department"
	"github.com/r2pos/bridge/features/helpers"
	"github.com/r2pos/bridge/features/record"
)

type ItemService struct {
	repo              *ItemRepo
	recordService     *record.RecordService
	departmentService *department.DepartmentService
}

func NewService(
	repo *ItemRepo,
	recordService *record.RecordService,
	departmentService *department.DepartmentService,
) *ItemService {
	return &ItemService{
		repo,
		recordService,
		departmentService,
	}
}

func (s *ItemService) GetMenu() (*StoreMenu, error) {
	records, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, err
	}

	isActive, err := s.CheckIfOnlineActive(helpers.GetCurrentTime())
	if err != nil {
		return nil, err
	}

	departments, err := s.departmentService.GetAllBasic()
	if err != nil {
		return nil, err
	}

	items := map[string][]*BasicItem{}

	for _, department := range *departments {
		//nolint:govet // This is a false positive, the err is being handled
		departmentItems, err := s.GetByDepartment(department.Department, true, true)
		if err != nil {
			return nil, err
		}
		departmentItemsList, ok := departmentItems.([]*BasicItem)
		if !ok {
			return nil, fmt.Errorf("failed to assert type []*BasicItem for department %s", department.Department)
		}
		items[department.Department] = departmentItemsList
	}

	var activeHours *record.ActiveHours

	if records.EcomSetting != nil {
		activeHours = records.EcomSetting.ActiveHours
	}

	var delivery *record.Delivery

	if records.EcomSetting != nil {
		delivery = &records.EcomSetting.Delivery
	}

	return &StoreMenu{
		StoreInfo: StoreInfo{
			Name:         records.SystemSetting.StoreName,
			Address:      records.SystemSetting.StoreAddress1,
			City:         records.SystemSetting.StoreCity,
			State:        records.SystemSetting.StoreState,
			Phone:        records.SystemSetting.StorePhone,
			OnlineActive: isActive,
			StoreHours:   activeHours,
			Delivery:     delivery,
		},
		Departments: *departments,
		Items:       items,
	}, nil
}

func (s *ItemService) GetAll(online bool, filterMods bool) (interface{}, error) {
	result, err := s.repo.GetAll()
	if err != nil {
		return nil, err
	}
	if online {
		records, err := s.recordService.GetAllRecordsCached()
		if err != nil {
			return nil, fmt.Errorf("failed to get all records cached: %w", err)
		}
		return s.ItemsToBasicItems(result, filterMods, records.EcomSetting), nil
	}
	return result, nil
}

func (s *ItemService) GetByDepartment(departmentID string, online bool, filterMods bool) (interface{}, error) {
	result, err := s.repo.FindByDepartment(departmentID)
	if err != nil {
		return nil, err
	}
	if online {
		records, err := s.recordService.GetAllRecordsCached()
		if err != nil {
			return nil, fmt.Errorf("failed to get all records cached: %w", err)
		}
		return s.ItemsToBasicItems(result, filterMods, records.EcomSetting), nil
	}
	return result, nil
}

func (s *ItemService) GetManyByIDs(itemIDs []string) (*[]*Item, error) {
	result, err := s.repo.FindByID(itemIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get many items by ids; error: %s", err)
	}

	return result, nil
}

func (s *ItemService) GetByID(itemID string, online bool) (interface{}, error) {
	result, err := s.repo.FindByID([]string{itemID})
	if err != nil {
		return nil, err
	}
	if len(*result) < 1 {
		return nil, errors.New("Item not found")
	}
	if online {
		var ezList []DetailedItem
		ezList, err = s.ItemsToDetailedItems(result)
		if err != nil {
			return nil, err
		}
		if len(ezList) < 1 {
			return nil, errors.New("Error converting Item to DetailedItem")
		}
		return ezList[0], nil
	}
	return (*result)[0], nil
}

func (s *ItemService) GetMultipleByID(itemIDs []string, online bool) (interface{}, error) {
	result, err := s.repo.FindByID(itemIDs)
	if err != nil {
		return nil, err
	}
	if online {
		var detailedList []DetailedItem
		detailedList, err = s.ItemsToDetailedItems(result)
		if err != nil {
			return nil, err
		}
		return detailedList, nil
	}
	return result, nil
}

func (s *ItemService) Upsert(item *Item) (*Item, error) {
	item, err := s.repo.Upsert(item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (s *ItemService) itemtoDetailedItem(
	item *Item,
	parentItem *Item,
	grandParentItem *Item,
	depth int, // current depth in the modifier chain
) (*DetailedItem, error) {
	const maxDepth = 10

	records, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, fmt.Errorf("failed to get all records cached: %w", err)
	}

	detailedItem := newDetailedItem(item, parentItem, grandParentItem, records.EcomSetting)

	if len(item.Document.Modifiers) > 0 && depth <= maxDepth {
		var modKeys []string
		for key := range item.Document.Modifiers {
			modKeys = append(modKeys, key)
		}
		modItems, err := s.repo.FindByID(modKeys)
		if err != nil {
			return nil, err
		}

		// Sort mod items by their index in the parent item modData
		sort.Slice(*modItems, func(i, j int) bool {
			return item.Document.Modifiers[((*modItems)[i]).Item].Idx < item.Document.Modifiers[((*modItems)[j]).Item].Idx
		})

		for _, modItem := range *modItems {
			if modItem.Document.IsOpenPrice {
				continue // Skip open price items
			}
			modDetailedItem, err := s.itemtoDetailedItem(
				modItem,
				item,
				grandParentItem,
				depth+1,
			)
			if err != nil {
				return nil, err
			}

			detailedItem.Modifiers = append(detailedItem.Modifiers, *modDetailedItem)
		}
	}
	return detailedItem, nil
}

func (s *ItemService) ItemsToDetailedItems(items *[]*Item) ([]DetailedItem, error) {
	var returnList = []DetailedItem{}
	for _, item := range *items {
		if item.Document.IsOpenPrice {
			continue // Skip open price items
		}
		detailedItem, err := s.itemtoDetailedItem(item, nil, nil, 0)
		if err != nil {
			return nil, err
		}
		if detailedItem != nil {
			returnList = append(returnList, *detailedItem)
		}
	}
	return returnList, nil
}

func (s *ItemService) ItemsToBasicItems(items *[]*Item, filterMods bool, ecomConfig *record.EcomSettingDocument) []*BasicItem {
	var returnList []*BasicItem
	for _, item := range *items {
		if item.Document.IsOpenPrice {
			continue // Skip open price items
		}
		if (!filterMods || !item.Document.IsModifier) && item.Document.GetShowOnline() && (item.DepartmentByDepartment != nil && item.DepartmentByDepartment.Document.GetShowOnline()) {
			returnList = append(returnList, newBasicItem(item, ecomConfig))
		}
	}
	return returnList
}

func (s *ItemService) CheckIfOnlineActive(timeToCheck time.Time) (bool, error) {
	records, err := s.recordService.GetAllRecords()
	if err != nil {
		return false, fmt.Errorf("failed to check if online ordering is active: %w", err)
	}

	doc := records.EcomSetting

	if doc == nil || !doc.EcomEnabled || doc.ActiveHours == nil {
		return false, nil
	}

	nowMilis := helpers.TimeToMillis(timeToCheck)
	todayInt := int(timeToCheck.Weekday())
	yestInt := todayInt - 1
	if yestInt < 0 {
		yestInt = 6
	}
	days := [][]record.ActiveWindow{doc.ActiveHours.Sun, doc.ActiveHours.Mon, doc.ActiveHours.Tue, doc.ActiveHours.Wed, doc.ActiveHours.Thu, doc.ActiveHours.Fri, doc.ActiveHours.Sat}
	holidays := map[string][]record.ActiveWindow{"Christmas Day": doc.ActiveHours.Christmas, "Christmas Eve": doc.ActiveHours.ChristmasEve, "New Year's Day": doc.ActiveHours.NewYears, "New Year's Eve": doc.ActiveHours.NewYearsEve, "Thanksgiving Day": doc.ActiveHours.Thanksgiving, "Independence Day": doc.ActiveHours.IndependenceDay, "Labor Day": doc.ActiveHours.LaborDay, "Memorial Day": doc.ActiveHours.MemorialDay, "Columbus Day": doc.ActiveHours.ColumbusDay, "Veterans Day": doc.ActiveHours.VeteransDay, "MLK Day": doc.ActiveHours.MlkDay, "Presidents Day": doc.ActiveHours.PresidentsDay}

	// Find out if today or yesterday is a holiday
	todayHoliday, yestHoliday := FindIfHolidays(timeToCheck)

	// check holiday hours if today or yesterday is a holiday
	if todayHoliday != "" {
		if FindMatchToday(nowMilis, holidays[todayHoliday]) {
			return true, nil
		}
	} else {
		if FindMatchToday(nowMilis, days[todayInt]) {
			return true, nil
		}
	}
	if yestHoliday != "" {
		if FindMatchYesterday(nowMilis, holidays[yestHoliday]) {
			return true, nil
		}
	} else {
		if FindMatchYesterday(nowMilis, days[yestInt]) {
			return true, nil
		}
	}

	return false, nil
}
