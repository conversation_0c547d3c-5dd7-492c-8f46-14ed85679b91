package item

import (
	"time"

	"fmt"

	"github.com/r2pos/bridge/features/record"
	"github.com/rickar/cal/v2"
	"github.com/rickar/cal/v2/us"
)

func newDetailedItem(item *Item, parentItem *Item, grandParentItem *Item, ecomConfig *record.EcomSettingDocument) *DetailedItem {
	desc := item.LongDesc
	price := getOnlinePrice(item.Document.Pricing, ecomConfig, false)

	if item.Document.ReceiptDesc != nil {
		desc = *item.Document.ReceiptDesc
	}
	if item.Document.OnlineDesc != nil {
		desc = *item.Document.OnlineDesc
	}

	if parentItem != nil {
		if parentItem.Document.PassDesc && item.Document.ModifierDesc != nil {
			desc = *parentItem.Document.ModifierDesc + " - " + desc
		}
		if parentItem.Document.OverridePricing {
			price = GetOverridePrice(item, parentItem, price, false, ecomConfig)
		}
	}

	var isVisible bool = parentItem == nil || price > 0 || len(item.Document.Modifiers) == 0 || !item.Document.IsModifier

	if !isVisible {
		if parentItem != nil && parentItem.Document.Modifiers[item.Item].ForceVisible {
			isVisible = true
		}
		if grandParentItem != nil && grandParentItem.Document.MultiModLists {
			isVisible = true
		}
	}

	newDetailed := DetailedItem{
		Item:          item.Item,
		Price:         price,
		Desc:          desc,
		ModifierDesc:  item.Document.ModifierDesc,
		MultiModLists: item.Document.MultiModLists,
		ModMaxSel:     item.Document.ModMaxSel,
		ModMinSel:     item.Document.ModMinSel,
		Count:         item.Document.GetCount(),
		Modifiers:     []DetailedItem{},
		Selected:      false,
		Qty:           1,
		IsVisible:     isVisible,
	}
	return &newDetailed
}

func GetOverridePrice(item *Item, parentItem *Item, defaultPrice int, cash bool, ecomConfig *record.EcomSettingDocument) int {
	price := defaultPrice
	if len(parentItem.Document.DefModPricing) == 0 {
		price = getOnlinePrice(parentItem.Document.DefModPricing, ecomConfig, cash)
	}
	modData := parentItem.Document.Modifiers[item.Item]
	if modData.Pricing != nil {
		price = getOnlinePrice(parentItem.Document.Modifiers[item.Item].Pricing, ecomConfig, cash)
	}
	return price
}

func newBasicItem(item *Item, ecomConfig *record.EcomSettingDocument) *BasicItem {
	desc := item.LongDesc
	price := getOnlinePrice(item.Document.Pricing, ecomConfig, false)

	if item.Document.ReceiptDesc != nil {
		desc = *item.Document.ReceiptDesc
	}
	if item.Document.OnlineDesc != nil {
		desc = *item.Document.OnlineDesc
	}

	newBasic := BasicItem{
		Item:         item.Item,
		Price:        price,
		Desc:         desc,
		Department:   item.Department,
		Count:        item.Document.GetCount(),
		DetailedDesc: item.Document.DetailedDesc,
	}
	return &newBasic
}

func FindMatchToday(now int, windows []record.ActiveWindow) bool {
	for _, window := range windows {
		if window.Close > window.Open {
			if now >= window.Open && now <= window.Close {
				return true
			}
		}
	}
	return false
}

func FindMatchYesterday(now int, windows []record.ActiveWindow) bool {
	for _, window := range windows {
		if window.Close < window.Open {
			if now <= window.Close {
				return true
			}
		}
	}
	return false
}

func FindIfHolidays(now time.Time) (string, string) {
	tomorrow := now.AddDate(0, 0, 1)
	yesterDay := now.AddDate(0, 0, -1)
	todayHoliday := ""
	yestHoliday := ""

	c := cal.NewBusinessCalendar()
	c.AddHoliday(us.ChristmasDay, us.ThanksgivingDay, us.IndependenceDay, us.LaborDay, us.MemorialDay, us.NewYear, us.ColumbusDay, us.VeteransDay, us.MlkDay, us.PresidentsDay)

	if actual, _, h := c.IsHoliday(now); actual {
		todayHoliday = h.Name
		if h.Name == "Christmas Day" {
			yestHoliday = "Christmas Eve"
		}
		if h.Name == "New Year's Day" {
			yestHoliday = "New Year's Eve"
		}
	} else {
		if actual, _, h := c.IsHoliday(tomorrow); actual {
			if h.Name == "Christmas Day" {
				todayHoliday = "Christmas Eve"
			}
			if h.Name == "New Year's Day" {
				todayHoliday = "New Year's Eve"
			}
		}
	}
	if actual, _, h := c.IsHoliday(yesterDay); actual {
		yestHoliday = h.Name
	}

	return todayHoliday, yestHoliday
}

func getOnlinePrice(pricing map[string]int, ecomConfig *record.EcomSettingDocument, cash bool) int {
	priceLevel := 0
	if ecomConfig != nil && ecomConfig.EcomPriceLevel > 0 {
		priceLevel = ecomConfig.EcomPriceLevel
	}

	creditKey := "S0L" + fmt.Sprint(priceLevel) + "C0"
	cashKey := "S0L" + fmt.Sprint(priceLevel) + "C1"

	if pricing[creditKey] == 0 {
		creditKey = "S0L0C0"
		cashKey = "S0L0C1"
	}

	price := pricing[creditKey]

	if cash && pricing[cashKey] != 0 {
		price = pricing[cashKey]
	}

	return price
}
