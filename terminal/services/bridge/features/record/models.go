package record

import (
	"encoding/json"

	"github.com/r2pos/bridge/features/helpers"
)

type Record struct {
	RecordKey string  `json:"record_key" db:"record_key"`
	UpdatedAt string  `json:"updated_at" db:"updated_at"`
	Document  []uint8 `json:"document" db:"document"`
}

type EmployeeClass struct {
	EmployeeClass string                `json:"employee_class"`
	Title         string                `json:"title"`
	Clearance     *int                  `json:"clearance"`
	Document      EmployeeClassDocument `json:"document"`
	CreatedAt     *string               `json:"created_at"`
	CreatedBy     string                `json:"created_by"`
	UpdatedAt     *string               `json:"updated_at"`
	UpdatedBy     string                `json:"updated_by"`
}

type EmployeeClassDocument struct {
	DailyOTLimit  int `json:"dailyOTLimit"`
	WeeklyOTLimit int `json:"weeklyOTLimit"`
	MealDedLimit  int `json:"mealDedLimit"`
	MealDedMins   int `json:"mealDedMins"`
}

// Keep in sync with https://github.com/Round2POS/hyperion/blob/f0a3cff65d6333d096552e50a1544f5166a967f9/terminal/gui/front-of-house/lib/app/data/models/json_record.dart#L1449
type Tax struct {
	Idx            *int    `json:"idx"`
	Desc           *string `json:"desc"`
	IsVat          bool    `json:"isVat"`
	TaxType        int     `json:"taxType"`
	RowsUsed       *int    `json:"rowsUsed"`
	RoundType      *int    `json:"roundType"`
	NoTaxUnder     *int    `json:"noTaxUnder"`
	TaxPercent     *int    `json:"taxPercent"`
	AddOddPenny    *bool   `json:"addOddPenny"`
	ForgiveTakeout *bool   `json:"forgiveTakeout"`
}

type Break struct {
	Idx           int    `json:"idx"`
	Desc          string `json:"desc"`
	IsPaid        int    `json:"isPaid"`
	BreakMins     int    `json:"breakMins"`
	MinsToQualify int    `json:"minsToQualify"`
}

type RoomsText struct {
	X        float64  `json:"x"`
	Y        float64  `json:"y"`
	Idx      int      `json:"idx"`
	Bgnd     int      `json:"bgnd"`
	Desc     string   `json:"desc"`
	Fgnd     int      `json:"fgnd"`
	Font     string   `json:"font"`
	Width    float64  `json:"width"`
	Height   float64  `json:"height"`
	Orient   int      `json:"orient"`
	RoomIdx  int      `json:"roomIdx"`
	Rotation *float64 `json:"rotation"`
}

type RoomsRoom struct {
	Desc     string `json:"desc"`
	PrcLevel int    `json:"prcLevel"`
	Idx      int    `json:"idx"`
}

type RoomsTable struct {
	Desc     string  `json:"desc"`
	X        float64 `json:"x"`
	Y        float64 `json:"y"`
	Width    float64 `json:"width"`
	Height   float64 `json:"height"`
	Rotation float64 `json:"rotation"`
	Idx      int     `json:"idx"`
	RoomIdx  int     `json:"roomIdx"`
	Shape    int     `json:"shape"`
	SeatCnt  int     `json:"seatCnt"`
	SectIdx  int     `json:"sectIdx"`
}

type SystemSettingJobCode struct {
	Index            int    `json:"index"`
	Title            string `json:"title"`
	RevCtr           int    `json:"revCtr"`
	IsActive         bool   `json:"isActive"`
	IsDeliveryDriver bool   `json:"isDeliveryDriver"`
	LaborGroup       int    `json:"laborGroup"`
	PromptForSeat    bool   `json:"promptForSeat"`
	Section          int    `json:"section"`
	Breaks           int    `json:"breaks"`
}

type PriceLevel struct {
	Idx  int    `json:"idx"`
	Desc string `json:"desc"`
}

type PriceSchedule struct {
	Idx          int    `json:"idx"`
	Desc         string `json:"desc"`
	Days         int    `json:"days"`
	StartTime    int    `json:"startTime"`
	EndTime      int    `json:"endTime"`
	IsSeasonal   bool   `json:"isSeasonal"`
	SeasStMonth  int    `json:"seasStMonth"`
	SeasStDay    int    `json:"seasStDay"`
	SeasEndMonth int    `json:"seasEndMonth"`
	SeasEndDay   int    `json:"seasEndDay"`
}

type PrepDevice struct {
	Idx      int    `json:"idx"`
	Desc     string `json:"desc"`
	Type     int    `json:"type"`
	IP       string `json:"IP"`
	RcptTerm int    `json:"rcptTerm"`
	ReRoute  int    `json:"reRoute"`
}

type Terminal struct {
	IP               string                    `json:"IP"`
	MAC              string                    `json:"MAC"`
	Desc             string                    `json:"desc"`
	Idx              int                       `json:"idx"`
	SkipDevices      int                       `json:"skipDevices"`
	Section          int                       `json:"section"`
	PriceLevel       int                       `json:"priceLevel"`
	QuickSignIn      bool                      `json:"quickSignIn"`
	QuickSignMinutes int                       `json:"quickSignMinutes"`
	QuickSignSection int                       `json:"quickSignSection"`
	Scale            SystemDeviceScale         `json:"scale"`
	RcptPrn          SystemDeviceRcptPrn       `json:"rcptPrn"`
	PoleDisplay      SystemDevicePoleDisplay   `json:"poleDisplay"`
	PaymentDevice    SystemDevicePaymentDevice `json:"paymentDevice"`
	CashDrawers      []TerminalDrawer          `json:"cashDrawers"`
}

type SystemDeviceScale struct {
	Port     int `json:"port"`
	Type     int `json:"type"`
	PortType int `json:"portType"`
}

type SystemDeviceRcptPrn struct {
	Baud     int `json:"baud"`
	Port     int `json:"port"`
	Type     int `json:"type"`
	PortType int `json:"portType"`
}

type SystemDevicePoleDisplay struct {
	Port     int `json:"port"`
	Type     int `json:"type"`
	PortType int `json:"portType"`
}

type SystemDevicePaymentDevice struct {
	IP     string `json:"IP"`
	Port   string `json:"port"`
	Epi    string `json:"epi"`
	AppID  string `json:"appID"`
	AppKey string `json:"appKey"`
}

type TerminalDrawer struct {
	Idx  int `json:"idx"`
	Type int `json:"type"`
	Port int `json:"port"`
}

type MerchantModules struct {
	TimeClock        MerchantTimeClock        `json:"timeClock"`
	ButtonManagement MerchantButtonManagement `json:"buttonManagement"`
}

type MerchantTimeClock struct {
	IsActive bool `json:"isActive"`
}

type MerchantButtonManagement struct {
	IsActive bool `json:"isActive"`
}

type GiftCardProgram struct {
	ClientId        *string `json:"clientId"`
	LocationId      *string `json:"locationId"`
	TerminalId      *string `json:"terminalId"`
	InitiatorId     *string `json:"initiatorId"`
	InitiatorPass   *string `json:"initiatorPass"`
	IntegrationAuth *string `json:"integrationAuth"`
	IntegrationPass *string `json:"integrationPass"`
}

type Gateway struct {
	TokenizationKey string `json:"tokenizationKey"`
	DirectKey       string `json:"directKey"`
}

type Section struct {
	Desc                    string   `json:"desc"`
	Idx                     int      `json:"idx"`
	ForceCustCount          bool     `json:"forceCustCount"`
	AutoAddSeats            bool     `json:"autoAddSeats"`
	ForceTblNum             bool     `json:"forceTblNum"`
	ForceSaleDesc           bool     `json:"forceSaleDesc"`
	RedAfterMins            *float64 `json:"redAfterMins"`
	BlinkAfterMins          *float64 `json:"blinkAfterMins"`
	PriceLevel              *int     `json:"priceLevel"`
	TrackBySeat             *bool    `json:"trackBySeat"`
	SaleName                *string  `json:"saleName"`
	OpenSalesList           *bool    `json:"openSalesList"`
	ForceOrdType            bool     `json:"forceOrdType"`
	ForceCustIfTogo         bool     `json:"forceCustIfTogo"`
	ForceCustIfDelivery     bool     `json:"forceCustIfDelivery"`
	CustCountOption         int      `json:"custCountOption"`
	DefOrdType              int      `json:"defOrdType"`
	MinGratCustCnt          *int     `json:"minGratCustCnt"`
	CalcGratWDiscs          *bool    `json:"calcGratWDiscs"`
	GratAmt                 float64  `json:"gratAmt"`
	SalesByEmployee         bool     `json:"salesByEmployee"`
	ClockOutWithOpenSales   *bool    `json:"clockOutWithOpenSales"`
	AllowTransferSales      *bool    `json:"allowTransferSales"`
	PrintServerReport       bool     `json:"printServerReport"`
	ShowPromisedTimeAndType *bool    `json:"showPromisedTimeAndType"`
}

func NewSectionDefault() *Section {
	return &Section{
		Desc: "",
		Idx:  0,
	}
}

func NewSectionsDocument(bites []uint8, doc *SectionsDocument) error {
	err := json.Unmarshal(bites, &doc)
	if err != nil {
		return err
	}
	for i := range doc.Sections {
		if doc.Sections[i].TrackBySeat == nil {
			doc.Sections[i].TrackBySeat = helpers.Ptr(true)
		}
		if doc.Sections[i].SaleName == nil {
			doc.Sections[i].SaleName = helpers.Ptr("Sale")
		}
		if doc.Sections[i].OpenSalesList == nil {
			doc.Sections[i].OpenSalesList = helpers.Ptr(true)
		}
		if doc.Sections[i].MinGratCustCnt == nil {
			doc.Sections[i].MinGratCustCnt = helpers.Ptr(1)
		}
		if doc.Sections[i].CalcGratWDiscs == nil {
			doc.Sections[i].CalcGratWDiscs = helpers.Ptr(true)
		}
		if doc.Sections[i].ClockOutWithOpenSales == nil {
			doc.Sections[i].ClockOutWithOpenSales = helpers.Ptr(true)
		}
		if doc.Sections[i].AllowTransferSales == nil {
			doc.Sections[i].AllowTransferSales = helpers.Ptr(true)
		}
		if doc.Sections[i].ShowPromisedTimeAndType == nil {
			doc.Sections[i].ShowPromisedTimeAndType = helpers.Ptr(true)
		}
	}
	return nil
}

type RegisterToolbar struct {
	Menu         []RegisterButton `json:"menu"`
	CustomColors bool             `json:"custom_colors"`
}

type RegisterUser struct {
	Menu         []RegisterButton `json:"menu"`
	CustomColors bool             `json:"custom_colors"`
	IsActive     bool             `json:"is_active"`
}

type RegisterAdmin struct {
	Menu         []RegisterButton `json:"menu"`
	CustomColors bool             `json:"custom_colors"`
	IsActive     bool             `json:"is_active"`
}
type RegisterButton struct {
	Index   int                     `json:"index"`
	Text    string                  `json:"text"`
	Action  string                  `json:"action"`
	Section string                  `json:"section"`
	FGnd    *int                    `json:"fgnd"`
	BGnd    *int                    `json:"bgnd"`
	Config  *map[string]interface{} `json:"config"`
}

type ClassDocument struct {
	Classes []EmployeeClass `json:"classes"`
}

type SalesTaxDocument struct {
	Taxes []Tax `json:"taxes"`
}

type BreaksDocument struct {
	Breaks []Break `json:"breaks"`
}

type RoomsDocument struct {
	Text   []RoomsText  `json:"text"`
	Rooms  []RoomsRoom  `json:"rooms"`
	Tables []RoomsTable `json:"tables"`
}

type SystemSettingDocument struct {
	PpdType             int                    `json:"ppdType"`
	TareUOM             bool                   `json:"tareUOM"`
	JobCodes            []SystemSettingJobCode `json:"jobCodes"`
	StoreURL            string                 `json:"storeURL"`
	StoreZip            string                 `json:"storeZip"`
	BannerMsg           string                 `json:"bannerMsg"`
	CashMedia           int                    `json:"cashMedia"`
	DayDivide           string                 `json:"dayDivide"`
	StoreCity           string                 `json:"storeCity"`
	StoreName           string                 `json:"storeName"`
	PpdRefDate          string                 `json:"ppdRefDate"`
	ScaleTares          []interface{}          `json:"scaleTares"`
	StorePhone          string                 `json:"storePhone"`
	StoreState          string                 `json:"storeState"`
	CashRegMode         int                    `json:"cashRegMode"`
	NoCCSlipUnder       int                    `json:"noCCSlipUnder"`
	StoreAddress1       string                 `json:"storeAddress1"`
	PEBHasChkDigit      int                    `json:"PEBHasChkDigit"`
	RevenueCenters      []interface{}          `json:"revenueCenters"`
	WaitForDrawerClosed int                    `json:"waitForDrawerClosed"`
	PriceLevels         []PriceLevel           `json:"priceLevels"`
}

type EcomSettingDocument struct {
	ActiveHours     *ActiveHours `json:"activeHours"`
	EcomEnabled     bool         `json:"ecomEnabled"`
	PrintTerminal   int          `json:"printTerminal"`
	ExpediteSlip    bool         `json:"expediteSlip"`
	CustomerReceipt bool         `json:"customerReceipt"`
	MerchantReceipt bool         `json:"merchantReceipt"`
	EcomPriceLevel  int          `json:"ecomPriceLevel"`
	Delivery        Delivery     `json:"delivery"`
}

type Delivery struct {
	Enabled     bool `json:"enabled"`
	DeliveryFee int  `json:"deliveryFee"`
}

type ActiveHours struct {
	Mon             []ActiveWindow `json:"mon"`
	Tue             []ActiveWindow `json:"tue"`
	Wed             []ActiveWindow `json:"wed"`
	Thu             []ActiveWindow `json:"thu"`
	Fri             []ActiveWindow `json:"fri"`
	Sat             []ActiveWindow `json:"sat"`
	Sun             []ActiveWindow `json:"sun"`
	Christmas       []ActiveWindow `json:"xmas"`
	ChristmasEve    []ActiveWindow `json:"xmasEve"`
	NewYears        []ActiveWindow `json:"nwYrs"`
	NewYearsEve     []ActiveWindow `json:"nwYrsEve"`
	Thanksgiving    []ActiveWindow `json:"thanks"`
	IndependenceDay []ActiveWindow `json:"ind"`
	LaborDay        []ActiveWindow `json:"labor"`
	MemorialDay     []ActiveWindow `json:"memor"`
	ColumbusDay     []ActiveWindow `json:"colum"`
	VeteransDay     []ActiveWindow `json:"vets"`
	PresidentsDay   []ActiveWindow `json:"pres"`
	MlkDay          []ActiveWindow `json:"mlk"`
}

type ActiveWindow struct {
	Open  int `json:"open"`
	Close int `json:"close"`
}

type PriceScheduleDocument struct {
	Discs []PriceSchedule `json:"discs"`
}

type ReportsDocument struct {
	ReportList           []string `json:"reportList"`
	ReportEmailAddresses []string `json:"reportEmailAddresses"`
}

type CashierDocument struct {
	Cashiers       []string `json:"cashiers"`
	CurrentCashier *string  `json:"currentCashier"`
}

type SystemDeviceDocument struct {
	Prep     []PrepDevice `json:"prep"`
	Terminal []Terminal   `json:"terminal"`
}

type MerchantDocument struct {
	Ebt                        bool             `json:"ebt"`
	Tipping                    bool             `json:"tipping"`
	GiftCards                  bool             `json:"giftCards"`
	ReceiptHeader              []string         `json:"receiptHeader"`
	DualPricing                bool             `json:"dualPricing"`
	PreAuthAmount              *int             `json:"preAuthAmount"`
	DualPricingPercent         float64          `json:"dualPricingPercent"`
	DualPricingRoundAmount     *int             `json:"dualPricingRoundAmount"`
	RefundReceiptSignatureLine bool             `json:"refundReceiptSignatureLine"`
	House                      bool             `json:"house"`
	CashConfirmation           bool             `json:"cashConfirmation"`
	ShowModPrice               *bool            `json:"showModPrice"`
	PrintMerchantOnCash        *bool            `json:"printMerchantOnCash"`
	PrintMerchantOnNonCash     *bool            `json:"printMerchantOnNonCash"`
	PrintMerchantOnHouse       *bool            `json:"printMerchantOnHouse"`
	PrepOnSaleChange           *bool            `json:"prepOnSaleChange"`
	PrepOnSignOut              *bool            `json:"prepOnSignOut"`
	PrepOnCancel               *bool            `json:"prepOnCancel"`
	LegacyGiftTender           bool             `json:"legacyGiftTender"`
	LegacyGiftName             *string          `json:"legacyGiftName"`
	Modules                    *MerchantModules `json:"modules"`
	GiftCardProgram            *GiftCardProgram `json:"giftCardProgram"`
	TakeOutSurchargeAmt        int              `json:"takeOutSurchargeAmt"`
	TipLinesOnCustCopy         *bool            `json:"tipLinesOnCustCopy"`
	OrderTypePrintOpt          int              `json:"orderTypePrintOpt"`
	CustomerCopyPrintOpt       int              `json:"customerCopyPrintOpt"`
	PaymentDeviceType          *int             `json:"paymentDeviceType"`
	MultiCashDrawer            bool             `json:"multiCashDrawer"`
	CommentRowsOnReceipt       bool             `json:"commentRowsOnReceipt"`
	SaleDescOnReceipt          bool             `json:"saleDescOnReceipt"`
	PaidOutPrintCustomer       bool             `json:"paidOutPrintCustomer"`
	TipConfirmLimit            *int             `json:"tipConfirmLimit"`
	PrepPrintSizing            int              `json:"prepPrintSizing"`
	CondensedAuthSlip          bool             `json:"condensedAuthSlip"`
	DefaultRefundMedia         bool             `json:"defaultRefundMedia"`
	QrPayEnabled               bool             `json:"qrPayEnabled"`
	MarkModifiersRed           bool             `json:"markModifiersRed"`
	MarkCommentsRed            bool             `json:"markCommentsRed"`
	MarkToGoRed                bool             `json:"markToGoRed"`
	MarkOrderTypeRed           bool             `json:"markOrderTypeRed"`
	MarkPromisedTimeRed        bool             `json:"markPromisedTimeRed"`
	Gateway                    *Gateway         `json:"gateway"`
}

func NewMerchantDocument(bites []uint8, doc *MerchantDocument) error {
	err := json.Unmarshal(bites, doc)
	if err != nil {
		return err
	}
	if doc.PreAuthAmount == nil {
		doc.PreAuthAmount = helpers.Ptr(100)
	}
	if doc.DualPricingRoundAmount == nil {
		doc.DualPricingRoundAmount = helpers.Ptr(1)
	}
	if doc.ShowModPrice == nil {
		doc.ShowModPrice = helpers.Ptr(true)
	}
	if doc.PrintMerchantOnCash == nil {
		doc.PrintMerchantOnCash = helpers.Ptr(true)
	}
	if doc.PrintMerchantOnNonCash == nil {
		doc.PrintMerchantOnNonCash = helpers.Ptr(true)
	}
	if doc.PrintMerchantOnHouse == nil {
		doc.PrintMerchantOnHouse = helpers.Ptr(true)
	}
	if doc.PrepOnSaleChange == nil {
		doc.PrepOnSaleChange = helpers.Ptr(true)
	}
	if doc.PrepOnSignOut == nil {
		doc.PrepOnSignOut = helpers.Ptr(true)
	}
	if doc.PrepOnCancel == nil {
		doc.PrepOnCancel = helpers.Ptr(true)
	}
	if doc.LegacyGiftName == nil {
		doc.LegacyGiftName = helpers.Ptr("Legacy Gift")
	}
	if doc.TipLinesOnCustCopy == nil {
		doc.TipLinesOnCustCopy = helpers.Ptr(true)
	}
	if doc.PaymentDeviceType == nil {
		doc.PaymentDeviceType = helpers.Ptr(2)
	}
	if doc.TipConfirmLimit == nil {
		doc.TipConfirmLimit = helpers.Ptr(-1)
	}
	return nil
}

type SectionsDocument struct {
	Sections []Section `json:"sections"`
}

type RegisterMenusDocument struct {
	Toolbar RegisterToolbar `json:"toolbar"`
	User    RegisterUser    `json:"user"`
	Admin   RegisterAdmin   `json:"admin"`
}

type RecordDocumentSet struct {
	Class         ClassDocument          `json:"class"`
	SalesTax      SalesTaxDocument       `json:"salesTax"`
	Breaks        BreaksDocument         `json:"breaks"`
	Rooms         RoomsDocument          `json:"rooms"`
	SystemSetting SystemSettingDocument  `json:"systemSetting"`
	PriceSchedule PriceScheduleDocument  `json:"priceSchedule"`
	Reports       ReportsDocument        `json:"reports"`
	Cashier       CashierDocument        `json:"cashier"`
	SystemDevice  SystemDeviceDocument   `json:"systemDevice"`
	Merchant      MerchantDocument       `json:"merchant"`
	Sections      SectionsDocument       `json:"sections"`
	RegisterMenus *RegisterMenusDocument `json:"registerMenus"`
	EcomSetting   *EcomSettingDocument   `json:"ecomSetting"`
}
