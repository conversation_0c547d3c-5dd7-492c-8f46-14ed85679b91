package record

import (
	"fmt"
	"sync"
	"time"

	"github.com/r2pos/bridge/features/helpers"
	"github.com/rs/zerolog/log"
)

type RecordService struct {
	repo *RecordRepo

	ttl time.Duration

	mutex *sync.Mutex
	// Only access this via mutex.
	_records   *RecordDocumentSet
	_updatedAt time.Time
}

func NewService(
	repo *RecordRepo,
) *RecordService {
	ttl := 5 * time.Minute

	s := &RecordService{
		repo,
		ttl,
		&sync.Mutex{},
		nil,
		time.Time{},
	}

	return s
}

func (s *RecordService) GetAllRecordsCached() (*RecordDocumentSet, error) {
	getCached := func() *RecordDocumentSet {
		s.mutex.Lock()
		defer s.mutex.Unlock()

		delta := time.Since(s._updatedAt)
		if delta < s.ttl {
			return s._records
		}
		log.Info().Msg(fmt.Sprintf("cached records is stale, last updated: %v, delta: %v, ttl: %v", s._updatedAt, delta, s.ttl))
		return nil
	}

	records := getCached()

	if records != nil {
		return records, nil
	}

	log.Info().Msg("refreshing cached records")

	records, err := s.GetAllRecords()
	if err != nil {
		return nil, fmt.Errorf("unable to refresh cached records: %w", err)
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	s._records = records
	s._updatedAt = helpers.GetCurrentTime()

	return records, nil
}

func (s *RecordService) GetAllRecords() (*RecordDocumentSet, error) {
	result, err := s.repo.GetAll()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *RecordService) GetRecordByKey(key string) (*Record, error) {
	result, err := s.repo.FindByKey(key)
	if err != nil {
		return nil, err
	}
	return result, nil
}
