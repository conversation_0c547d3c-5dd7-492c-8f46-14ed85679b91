package sale

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

type Handler struct {
	saleService *SaleService
	NMIService  *NMIService
}

func NewHandler(
	saleService *SaleService,
	nmiService *NMIService,
) *Handler {
	return &Handler{
		saleService,
		nmiService,
	}
}

func (h *Handler) OnlineOrderTotals(c echo.Context) error {
	log.Info().Msg("Inserting and completing online order")

	var onlineOrderTotalsRequest OnlineOrderTotalsRequest
	err := c.Bind(&onlineOrderTotalsRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	if len(*onlineOrderTotalsRequest.Items) == 0 {
		return echo.NewHTTPError(http.StatusInternalServerError, "No items in online order totals request")
	}

	log.Info().Interface("value", onlineOrderTotalsRequest).Msg("onlineOrderTotalsRequest for online order totals")

	totals, err := h.saleService.OnlineOrderTotals(&onlineOrderTotalsRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msg("Successfully got order totals")

	bytes, err := json.Marshal(totals)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) OnlineOrder(c echo.Context) error {
	log.Info().Msg("Inserting and completing online order")

	var onlineOrderRequest OnlineOrderRequest
	err := c.Bind(&onlineOrderRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	if len(*onlineOrderRequest.DetailedItems) == 0 {
		return echo.NewHTTPError(http.StatusInternalServerError, "No items in online order request")
	}

	log.Info().Interface("value", onlineOrderRequest).Msg("onlineOrderRequest for online order")

	onlineOrderReturn, err := h.saleService.OnlineOrder(&onlineOrderRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("Online order resulted in sale %v", onlineOrderReturn.SaleID)

	bytes, err := json.Marshal(onlineOrderReturn)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) OnlineHealth(c echo.Context) error {
	isOk, err := h.saleService.CheckOnlineHealth()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	bytes, err := json.Marshal(map[string]bool{"ok": isOk})
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) StartTenderGroup(c echo.Context) error {
	log.Info().Msg("starting tender group")

	var startTenderGroupRequest StartTenderGroupRequest
	err := c.Bind(&startTenderGroupRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	saleID := c.Param("saleID")

	var result *TenderGroupInfo
	result, err = h.saleService.StartTenderGroup(saleID, startTenderGroupRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("started tender group for sale %v", result.SaleID)

	bytes, err := json.Marshal(result)

	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

//nolint:dupl // False positive
func (h *Handler) RecordTender(c echo.Context) error {
	log.Info().Msg("adding tender")

	var recordTenderRequest RecordTenderRequest

	err := c.Bind(&recordTenderRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	saleID := c.Param("saleID")

	log.Info().Interface("value", recordTenderRequest).Msgf("recordTenderRequest for sale %v", saleID)

	var tgi *TenderGroupInfo
	tgi, err = h.saleService.RecordTender(saleID, recordTenderRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("Added tender to sale %v", tgi.SaleID)

	bytes, err := json.Marshal(tgi)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

//nolint:dupl // False positive
func (h *Handler) CompleteTenderGroup(c echo.Context) error {
	log.Info().Msg("completing tender group")

	var completeTenderGroupRequest CompleteTenderGroupRequest

	err := c.Bind(&completeTenderGroupRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	saleID := c.Param("saleID")

	log.Info().Interface("value", completeTenderGroupRequest).Msgf("completeTenderGroupRequest for sale %v", saleID)

	var tgi *TenderGroupInfo
	tgi, err = h.saleService.CompleteTenderGroup(saleID, completeTenderGroupRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("Completing tender group for sale %v", tgi.SaleID)

	bytes, err := json.Marshal(tgi)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

// func (h *Handler) CompleteSale(c echo.Context) error {
// 	log.Info().Msg("completing sale")

// 	var completeSaleRequest CompleteSaleRequest

// 	err := c.Bind(&completeSaleRequest)
// 	if err != nil {
// 		return echo.NewHTTPError(http.StatusInternalServerError, err)
// 	}

// 	saleID := c.Param("saleID")

// 	log.Info().Interface("value", completeSaleRequest).Msgf("completeSaleRequest for sale %v", saleID)

// 	result, err := h.saleService.CompleteSale(saleID, completeSaleRequest, nil)
// 	if err != nil {
// 		return echo.NewHTTPError(http.StatusInternalServerError, err)
// 	}

// 	log.Info().Msgf("Completed sale %v", result.SaleNumber)

// 	return c.String(http.StatusOK, fmt.Sprintf(`{"completedSale": "%v"}`, result.Sale))
// }

func (h *Handler) NewSale(c echo.Context) error {
	log.Info().Msg("starting new sale")

	result, err := h.saleService.NewSale()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("started sale %v", result.SaleNumber)

	return c.String(http.StatusOK, fmt.Sprintf(`{"newSale": "%v"}`, result.Sale))
}

func (h *Handler) LockSale(c echo.Context, unlock bool) error {
	log.Info().Msg("getting and locking sale")

	saleID := c.Param("saleID")

	result, err := h.saleService.LockSale(saleID, unlock)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("locked sale %v", result.SaleNumber)

	return c.String(http.StatusOK, fmt.Sprintf(`{"currentTerminalNumber": %v}`, result.Document.SaleHeader.CurrentTerminalNumber))
}

func (h *Handler) OnlineNMIValidate(c echo.Context) error {
	log.Info().Msg("validating NMI transaction")

	var nmiRequest NMIRequest
	err := c.Bind(&nmiRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	if nmiRequest.TransactionID == "" {
		return echo.NewHTTPError(http.StatusInternalServerError, "No transaction ID in NMI validate request")
	}

	log.Info().Interface("value", nmiRequest).Msg("nmiRequest for online NMI validate")

	isOk, err := h.NMIService.validateTransaction(nmiRequest.TransactionID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	log.Info().Msgf("Validated NMI transaction %v", nmiRequest.TransactionID)

	bytes, err := json.Marshal(map[string]bool{"ok": isOk})
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}

func (h *Handler) VoidNMITransaction(c echo.Context) error {
	log.Info().Msg("voiding NMI transaction")

	var nmiRequest NMIRequest
	err := c.Bind(&nmiRequest)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	if nmiRequest.TransactionID == "" {
		return echo.NewHTTPError(http.StatusInternalServerError, "No transaction ID in NMI void request")
	}

	log.Info().Interface("value", nmiRequest).Msg("nmiRequest for voiding NMI transaction")

	result, err := h.NMIService.Void(nmiRequest.TransactionID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	if result == nil || (*result)["response_code"] != "100" {
		log.Info().Msgf("Error voiding NMI transaction %v", nmiRequest.TransactionID)
	} else {
		log.Info().Msgf("Voided NMI transaction %v", nmiRequest.TransactionID)
	}

	bytes, err := json.Marshal(result)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err)
	}

	return c.String(http.StatusOK, string(bytes))
}
