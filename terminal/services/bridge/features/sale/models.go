package sale

import (
	"bytes"
	"encoding/json"
	"time"

	"github.com/r2pos/bridge/features/item"
)

type CompleteSaleRequest struct {
	EmployeeNumber int `json:"employeeNumber" db:"employeeNumber"`
	TerminalNumber int `json:"terminalNumber" db:"terminalNumber"`
}

type OnlineOrderTotalsRequest struct {
	Items        *[]*item.DetailedItem `json:"items" db:"items"`
	PromisedTime *int                  `json:"promisedTime" db:"promisedTime"`
	OrderType    *OrderType            `json:"orderType" db:"orderType"`
}

type OnlineOrderRequest struct {
	DetailedItems     *[]*item.DetailedItem `json:"detailedItems" db:"detailedItems"`
	Amount            int                   `json:"amount" db:"amount"`
	Memo              *string               `json:"memo" db:"memo"`
	TipAmount         *int                  `json:"tipAmount" db:"tipAmount"`
	CustomerFirstName string                `json:"customerFirstName" db:"customerFirstName"`
	CustomerLastName  string                `json:"customerLastName" db:"customerLastName"`
	CustomerPhone     string                `json:"customerPhone" db:"customerPhone"`
	CustomerEmail     *string               `json:"customerEmail" db:"customerEmail"`
	AuthCode          *string               `json:"authCode" db:"authCode"`
	CardPan           *string               `json:"cardPan" db:"cardPan"`
	CardHolder        *string               `json:"cardHolder" db:"cardHolder"`
	RefTxnID          *string               `json:"refTxnID" db:"refTxnID"`
	PromisedTime      *int                  `json:"promisedTime" db:"promisedTime"`
	OrderType         *OrderType            `json:"orderType" db:"orderType"`
}

type QRPaymentRequest struct {
	Amount              int            `json:"amount" db:"amount"`
	Memo                *string        `json:"memo" db:"memo"`
	TipAmount           *int           `json:"tipAmount" db:"tipAmount"`
	CardTransactionData *CardTransData `json:"cardTransactionData" db:"cardTransactionData"`
}
type NMIRequest struct {
	TransactionID string `json:"transactionID" db:"transactionID"`
}

type OnlineOrderTotals struct {
	DiscountTotal         int                   `json:"discountTotal" db:"discountTotal"`
	GratuityTotal         int                   `json:"gratuityTotal" db:"gratuityTotal"`
	SubTotal              int                   `json:"subTotal" db:"subTotal"`
	TakeOutSurchargeTotal int                   `json:"takeOutSurchargeTotal" db:"takeOutSurchargeTotal"`
	DeliveryFee           int                   `json:"deliveryFee" db:"deliveryFee"`
	TaxTotal              int                   `json:"taxTotal" db:"taxTotal"`
	TaxTotals             []int                 `json:"taxTotals" db:"taxTotals"`
	Total                 int                   `json:"total" db:"total"`
	Items                 *[]*item.DetailedItem `json:"items" db:"items"`
	BlockedReasons        []string              `json:"blockedReason" db:"blockedReason"`
	PromisedTime          *int                  `json:"promisedTime" db:"promisedTime"`
}

type OnlineOrderReturn struct {
	SaleID       string       `json:"saleID" db:"saleID"`
	Tenders      []TenderInfo `json:"tenders" db:"tenders"`
	Customer     string       `json:"customer" db:"customer"`
	Total        int          `json:"total" db:"total"`
	TipAmount    *int         `json:"tipAmount" db:"tipAmount"`
	PromisedTime *int         `json:"promisedTime" db:"promisedTime"`
}

type QRPaymentReturn struct {
	SaleID    string       `json:"saleID" db:"saleID"`
	Tenders   []TenderInfo `json:"tenders" db:"tenders"`
	Total     int          `json:"total" db:"total"`
	TipAmount *int         `json:"tipAmount" db:"tipAmount"`
}

// See also sale.Service.StartTenderGroup() TenderGroupInfo and TenderInfo.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L49
type StartTenderGroupRequest struct {
	// List of seats that are currently being tendered. Negative integers are used
	// to represent to-go orders. An empty list is a special value that represents
	// all seats.
	Seats []int `json:"seats" db:"seats"`
	// Employee number of the employee starting the tender group.
	EmployeeNumber int `json:"employeeNumber" db:"employeeNumber"`
	// Terminal number of the terminal starting the tender group.
	TerminalNumber int `json:"terminalNumber" db:"terminalNumber"`
	// Section index of the section that the tender group is associated with for gratuity.
	GratSectIdx *int `json:"gratSectIdx" db:"gratSectIdx"`
}

// See also sale.Service.GetTenderGroupInfo(), TenderGroupInfo and TenderInfo.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/1e0e257fe1d09b94efaa73c7667f6c6b9581efb9/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L69
type GetTenderGroupInfoRequest struct {
	// Sale ID of the sale that the tender group is associated with.
	SaleID string `json:"saleID" db:"saleID"`
	// Terminal number of the terminal that the tender group is associated with.
	TerminalNumber int `json:"terminalNumber" db:"terminalNumber"`
	// Section index of the section that the tender group is associated with for gratuity.
	GratSectIdx *int `json:"gratSectIdx" db:"gratSectIdx"`
}

// See also sale.Service.RecordTenderGroup(), TenderGroupInfo and TenderInfo.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L99
type RecordTenderRequest struct {
	// Payment media type of the tender. See PaymentMediaType.
	Media int `json:"media" db:"media"`
	// Should be a PaymentRoute type.
	Route int `json:"paymentRoute" db:"paymentRoute"`
	// Amount of the tender. In cents.
	Amount int `json:"amount" db:"amount"`
	// A flag to disable tagging the tender as a refund.
	//
	// `DisableRefund` is a nightmare, so here is an explanation. 1. with cash,
	// `DisableRefund` is set to false when when overpaid, and then it is expected
	// that a followup tender, for the change handed back, will have
	// `DisableRefund` set to true. 2. with legacy gift card, an overpayment is
	// called with `DisableRefund` set to true, and the entire TenderGroup is
	// allowed to be completed with a negative balance.
	DisableRefund bool `json:"disableRefund" db:"disableRefund"`
	// Check number of the tender, if applicable.
	CheckNum *string `json:"checkNum" db:"checkNum"`
	// Memo attached to the tender, if applicable.
	Memo *string `json:"memo" db:"memo"`
	// Card transaction data of the tender.
	CardTransactionData *CardTransData `json:"cardTransactionData" db:"cardTransactionData"`
	// Gift transaction data of the tender.
	GiftTransactionData *GiftTransData `json:"giftTransactionData" db:"giftTransactionData"`
	// Employee number of the employee recording the tender.
	EmployeeNumber int `json:"employeeNumber" db:"employeeNumber"`
	// Terminal number of the terminal recording the tender.
	TerminalNumber int `json:"terminalNumber" db:"terminalNumber"`
	// Cash drawer number of the cash drawer that the tender is associated with.
	//
	// Usually provided when `media` is cash or check.
	//
	// This number is indexed into the terminal's cash drawer list.
	CashDrawer *int `json:"cashDrawer" db:"cashDrawer"`
	// Section index of the section that the tender group is associated with for gratuity.
	GratSectIdx *int `json:"gratSectIdx" db:"gratSectIdx"`
	TipAmount   *int `json:"tipAmount" db:"tipAmount"`
	TipMedia    *int `json:"tipMedia" db:"tipMedia"`
}

// See also sale.Service.CompleteTenderGroup(), TenderGroupInfo and TenderInfo.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L131
type CompleteTenderGroupRequest struct {
	// Name of the current cashier.
	CurrentCashier *string `json:"currentCashier" db:"currentCashier"`
	// Employee number of the employee completing the tender group.
	EmployeeNumber int `json:"employeeNumber" db:"employeeNumber"`
	// Terminal number of the terminal completing the tender group.
	TerminalNumber int `json:"terminalNumber" db:"terminalNumber"`
	// Employee number of the employee settling the tender group.
	SettleEmployeeNumber int `json:"settleEmployeeNumber" db:"settleEmployeeNumber"`
	// Terminal number of the terminal settling the tender group.
	SettleTerminalNumber int `json:"settleTerminalNumber" db:"settleTerminalNumber"`
	// Section index of the section that the tender group is associated with for gratuity.
	GratSectIdx *int `json:"gratSectIdx" db:"gratSectIdx"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L123
type Sale struct {
	Document SaleDocument `json:"document" db:"document"`
	Sale     string       `json:"sale" db:"sale"`
	// Note that this is redundant with Sale.sale_number. They should thus always be the same.
	SaleNumber int        `json:"sale_number" db:"sale_number"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
	CreatedBy  string     `json:"created_by" db:"created_by"`
	UpdatedBy  string     `json:"updated_by" db:"updated_by"`
	EndAt      *time.Time `json:"end_at" db:"end_at"`
	Suspended  bool       `json:"suspended" db:"suspended"`
}

func (s *Sale) Equal(s2 *Sale) (bool, error) {
	// return reflect.DeepEqual(s, s2)
	sBytes, err := json.Marshal(s)
	if err != nil {
		return false, err
	}
	s2Bytes, err := json.Marshal(s2)
	if err != nil {
		return false, err
	}
	return bytes.Equal(sBytes, s2Bytes), nil
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L123
type SaleHeader struct {
	AddedGratuity         *float64     `json:"addedGratuity" db:"addedGratuity"`
	CashDiscountTotal     int          `json:"cashDiscountTotal" db:"cashDiscountTotal"`
	CashDrawer            *int         `json:"cashDrawer" db:"cashDrawer"`
	CashGratuityTotal     int          `json:"cashGratuityTotal" db:"cashGratuityTotal"`
	CashSubTotal          int          `json:"cashSubTotal" db:"cashSubTotal"`
	CashTaxTotal          int          `json:"cashTaxTotal" db:"cashTaxTotal"`
	CashTaxTotals         []int        `json:"cashTaxTotals" db:"cashTaxTotals"`
	CashTotal             int          `json:"cashTotal" db:"cashTotal"`
	CurrentCashier        *string      `json:"currentCashier" db:"currentCashier"`
	CurrentEmployeeNumber int          `json:"currentEmployeeNumber" db:"currentEmployeeNumber"`
	CurrentTerminalNumber int          `json:"currentTerminalNumber" db:"currentTerminalNumber"`
	Customer              *string      `json:"customer" db:"customer"`
	CustomerCount         int          `json:"customerCount" db:"customerCount"`
	CustomerName          *string      `json:"customerName" db:"customerName"`
	DiscountReas          *int         `json:"discountReas" db:"discountReas"`
	DiscountTotal         int          `json:"discountTotal" db:"discountTotal"`
	DualPricingAmount     int          `json:"dualPricingAmount" db:"dualPricingAmount"`
	DualPricingPercent    float64      `json:"dualPricingPercent" db:"dualPricingPercent"`
	DueRound              *int         `json:"dueRound" db:"dueRound"`
	GratuityPercent       float64      `json:"gratuityPercent" db:"gratuityPercent"`
	GratuityTotal         int          `json:"gratuityTotal" db:"gratuityTotal"`
	OrderType             int          `json:"orderType" db:"orderType"`
	OriginalSale          *string      `json:"originalSale" db:"originalSale"`
	OvertAmount           *int         `json:"overtAmount" db:"overtAmount"`
	OvertMedia            *int         `json:"overtMedia" db:"overtMedia"`
	PriceLevel            int          `json:"priceLevel" db:"priceLevel"`
	PromisedTime          *int         `json:"promisedTime" db:"promisedTime"`
	RefundType            *int         `json:"refundType" db:"refundType"`
	RoomIdx               *int         `json:"roomIdx" db:"roomIdx"`
	SaleDescription       *string      `json:"saleDescription" db:"saleDescription"`
	SaleFlags             []int        `json:"saleFlags" db:"saleFlags"`
	SaleNumber            int          `json:"saleNumber" db:"saleNumber"`
	SeatCnt               int          `json:"seatCnt" db:"seatCnt"`
	SeatsSettled          []int        `json:"seatsSettled" db:"seatsSettled"`
	SeatsTendered         []int        `json:"seatsTendered" db:"seatsTendered"`
	SectIdx               *int         `json:"sectIdx" db:"sectIdx"`
	SettleEmployeeNumber  *int         `json:"settleEmployeeNumber" db:"settleEmployeeNumber"`
	SettleTerminalNumber  *int         `json:"settleTerminalNumber" db:"settleTerminalNumber"`
	StartEmployeeNumber   int          `json:"startEmployeeNumber" db:"startEmployeeNumber"`
	StartTerminalNumber   int          `json:"startTerminalNumber" db:"startTerminalNumber"`
	SubTotal              int          `json:"subTotal" db:"subTotal"`
	TableDesc             *string      `json:"tableDesc" db:"tableDesc"`
	TableIdx              int          `json:"tableIdx" db:"tableIdx"`
	TakeOutSurchargeTotal int          `json:"takeOutSurchargeTotal" db:"takeOutSurchargeTotal"`
	DeliveryFee           int          `json:"deliveryFee" db:"deliveryFee"`
	TaxTotal              int          `json:"taxTotal" db:"taxTotal"`
	TaxTotals             []int        `json:"taxTotals" db:"taxTotals"`
	Tenders               []SaleTender `json:"tenders" db:"tenders"`
	ToGoSeatCnt           int          `json:"toGoSeatCnt" db:"toGoSeatCnt"`
	Total                 int          `json:"total" db:"total"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L523
type SaleRow struct {
	Uom                 *int              `json:"UOM" db:"UOM"`
	VATAmount           *int              `json:"VATAmount" db:"VATAmount"`
	ActualPrice         int               `json:"actualPrice" db:"actualPrice"`
	BasePrice           int               `json:"basePrice" db:"basePrice"`
	CashBasePrice       int               `json:"cashBasePrice" db:"cashBasePrice"`
	CashGrossPrice      int               `json:"cashGrossPrice" db:"cashGrossPrice"`
	CashOriginalPrice   int               `json:"cashOriginalPrice" db:"cashOriginalPrice"`
	CashPrice           int               `json:"cashPrice" db:"cashPrice"`
	CreditPrice         int               `json:"creditPrice" db:"creditPrice"`
	Department          string            `json:"department" db:"department"`
	Discounts           []SaleRowDiscount `json:"discounts" db:"discounts"`
	Employee            *int              `json:"employee" db:"employee"`
	ExtData             *any              `json:"extData" db:"extData"`
	Flags               []int             `json:"flags" db:"flags"`
	GiftTransactionData *GiftTransData    `json:"giftTransactionData" db:"giftTransactionData"`
	GrossPrice          int               `json:"grossPrice" db:"grossPrice"`
	HasChildren         bool              `json:"hasChildren" db:"hasChildren"`
	Index               int               `json:"index" db:"index"`
	IsVisible           bool              `json:"isVisible" db:"isVisible"`
	IsWeightedItem      bool              `json:"isWeightedItem" db:"isWeightedItem"`
	Item                string            `json:"item" db:"item"`
	ItemPricing         map[string]int    `json:"itemPricing" db:"itemPricing"`
	OrigTaxFlags        int               `json:"origTaxFlags" db:"origTaxFlags"`
	OriginalPrice       int               `json:"originalPrice" db:"originalPrice"`
	// NOTE for go: that this defaults to 0, but that would make it parented to the 0th sale; use -1 to indicate no parent.
	Parent        int  `json:"parent" db:"parent"`
	Prep          int  `json:"prep" db:"prep"`
	PrintSeparate bool `json:"printSeparate" db:"printSeparate"`
	// NOTE for go: that this defaults to 0, but should usually be 1 or more.
	Qty                 int        `json:"qty" db:"qty"`
	ReceiptDescription  string     `json:"receiptDescription" db:"receiptDescription"`
	SeatNumber          int        `json:"seatNumber" db:"seatNumber"`
	Selected            bool       `json:"selected" db:"selected"`
	SendDtTm            *int       `json:"sendDtTm" db:"sendDtTm"`
	SoldDtTm            *int       `json:"soldDtTm" db:"soldDtTm"`
	SplitData           *SplitData `json:"splitData" db:"splitData"`
	TakeOutSurcharge    bool       `json:"takeOutSurcharge" db:"takeOutSurcharge"`
	TakeOutSurchargeAmt int        `json:"takeOutSurchargeAmt" db:"takeOutSurchargeAmt"`
	TaxFlags            int        `json:"taxFlags" db:"taxFlags"`
	TransactionFlags    []int      `json:"transactionFlags" db:"transactionFlags"`
	Upc                 string     `json:"upc" db:"upc"`
	VoidDtTm            *int       `json:"voidDtTm" db:"voidDtTm"`
	VoidedEmployee      *int       `json:"voidedEmployee" db:"voidedEmployee"`
	VoidedReason        *int       `json:"voidedReason" db:"voidedReason"`
	Weight              *float64   `json:"weight" db:"weight"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L99
type SaleDocument struct {
	SaleHeader SaleHeader `json:"saleHeader" db:"saleHeader"`
	SaleRows   []SaleRow  `json:"saleRows" db:"saleRows"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L831
type SaleTender struct {
	Amount              int            `json:"amount" db:"amount"`
	Media               *int           `json:"media" db:"media"`
	CheckNum            *string        `json:"checkNum" db:"checkNum"`
	Memo                *string        `json:"memo" db:"memo"`
	SaleTenderFlags     []int          `json:"saleTenderFlags" db:"saleTenderFlags"`
	TipAmount           *int           `json:"tipAmount" db:"tipAmount"`
	TipMedia            *int           `json:"tipMedia" db:"tipMedia"`
	CardTransactionData *CardTransData `json:"cardTransactionData" db:"cardTransactionData"`
	GiftTransactionData *GiftTransData `json:"giftTransactionData" db:"giftTransactionData"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L984
type CardTransData struct {
	Provider        int    `json:"provider" db:"provider"`
	Uid             string `json:"uid" db:"uid"`
	Amount          int    `json:"amount" db:"amount"`
	AuthCode        string `json:"authCode" db:"authCode"`
	BatchNo         string `json:"batchNo" db:"batchNo"`
	CardExp         string `json:"cardExp" db:"cardExp"`
	CardPan         string `json:"cardPan" db:"cardPan"`
	CardHolder      string `json:"cardHolder" db:"cardHolder"`
	CardBrand       int    `json:"cardBrand" db:"cardBrand"`
	EntryMode       int    `json:"entryMode" db:"entryMode"`
	RefECRID        string `json:"refECRID" db:"refECRID"`
	RefCustomID     string `json:"refCustomID" db:"refCustomID"`
	RefTxnID        string `json:"refTxnID" db:"refTxnID"`
	TenderType      int    `json:"tenderType" db:"tenderType"`
	TransactionMode int    `json:"transactionMode" db:"transactionMode"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1138
type GiftTransData struct {
	StandardHeader StandardHeader `json:"standardHeader" db:"standardHeader"`
	Identification Identification `json:"identification" db:"identification"`
	ExpirationDate string         `json:"expirationDate" db:"expirationDate"`
	Balances       Balances       `json:"balances" db:"balances"`
	HostMessage    string         `json:"hostMessage" db:"hostMessage"`
	PrintCodes     string         `json:"printCodes" db:"printCodes"`
	ErrorMessage   ErrorMessage   `json:"errorMessage" db:"errorMessage"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1200
type StandardHeader struct {
	Text              string `json:"text" db:"text"`
	RequestId         string `json:"requestId" db:"requestId"`
	LocaleId          string `json:"localeId" db:"localeId"`
	SystemnId         string `json:"systemId" db:"systemId"`
	ClientId          string `json:"clientId" db:"clientId"`
	LocationId        string `json:"locationId" db:"locationId"`
	TerminalId        string `json:"terminalId" db:"terminalId"`
	TerminalDateTime  string `json:"terminalDateTime" db:"terminalDateTime"`
	InitiatorType     string `json:"initiatorType" db:"initiatorType"`
	InitiatorId       string `json:"initiatorId" db:"initiatorId"`
	InitiatorPassword string `json:"initiatorPassword" db:"initiatorPassword"`
	ExternalId        string `json:"externalId" db:"externalId"`
	BatchId           string `json:"batchId" db:"batchId"`
	BatchReference    string `json:"batchReference" db:"batchReference"`
	Channel           string `json:"channel" db:"channel"`
	SubChannel        string `json:"subChannel" db:"subChannel"`
	Status            string `json:"status" db:"status"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1312
type Identification struct {
	TransactionId string `json:"transactionId" db:"transactionId"`
	ApprovalCode  string `json:"approvalCode" db:"approvalCode"`
	Demonstration string `json:"demonstration" db:"demonstration"`
	CardNumber    string `json:"cardNumber" db:"cardNumber"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1359
type Balances struct {
	Balances []Balance `json:"balances" db:"balances"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1389
type Balance struct {
	ValueCode    string `json:"valueCode" db:"valueCode"`
	Amount       string `json:"amount" db:"amount"`
	Difference   string `json:"difference" db:"difference"`
	ExchangeRate string `json:"exchangeRate" db:"exchangeRate"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L1436
type ErrorMessage struct {
	RejectionId    string `json:"rejectionId" db:"rejectionId"`
	ErrorCode      string `json:"errorCode" db:"errorCode"`
	BriefMessage   string `json:"briefMessage" db:"briefMessage"`
	InDepthMessage string `json:"inDepthMessage" db:"inDepthMessage"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L927
type SaleRowDiscount struct {
	Type       int     `json:"type" db:"type"`
	Amount     int     `json:"amount" db:"amount"`
	CashAmount int     `json:"cashAmount" db:"cashAmount"`
	Value      int     `json:"value" db:"value"`
	CashValue  int     `json:"cashValue" db:"cashValue"`
	Title      *string `json:"Title" db:"Title"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/sale.dart#L812
type SplitData struct {
	Qty int    `json:"qty" db:"qty"`
	Key string `json:"key" db:"key"`
}

// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/tender_group_info.dart#L128
type TenderInfo struct {
	Media    string `json:"media" db:"media"`
	Amount   int    `json:"amount" db:"amount"`
	Voided   bool   `json:"voided" db:"voided"`
	Refunded bool   `json:"refunded" db:"refunded"`
}

// This is what the sale service returns to clients for the tender group API.
//
// A Tender Group is a collection of tenders that are associated with a sale.
//
// The tenders are grouped by the seats that they are associated with. If a
// TenderGroup is created with an empty list of seats, it means that the
// TenderGroup is associated with all seats in the sale.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/c21b2fb00680212ad51fa788c16129cac1f2a499/terminal/gui/front-of-house/lib/app/data/models/tender_group_info.dart#L15
type TenderGroupInfo struct {
	// The sale ID of the current (original) sale. Upon completion of the Tender
	// Group, this sale might be split off to a new sale (when
	// `TenderServiceBase.completeTenderGroup` is called). In that case, the new
	// sale will have an new ID, and can be found in
	// `TenderGroupInfo.CompletedTenderGroupSaleID`.
	SaleID string `json:"saleID" db:"saleID"`
	// The seats that are being tendered in this TenderGroup. An empty list is a
	// special case that means all seats.
	Seats []int `json:"seats" db:"seats"`
	/// Are (all) the items in this sale eligible for EBT? This list contains descriptions of the items that are not EBT-eligible.
	EBTFailures []string `json:"ebtFailures" db:"ebtFailures"`
	// Total owed by the seats in this TenderGroup.
	Total int `json:"total" db:"total"`
	// Same as total, but the cash price for dual pricing.
	CashTotal int `json:"cashTotal" db:"cashTotal"`
	// The tenders that are associated with this TenderGroup.
	Tenders []TenderInfo `json:"tenders" db:"tenders"`
	// AKA the "credit" balance, this is the balance if isDualPricingEnabled is
	// false.
	Balance int `json:"balance" db:"balance"`
	// AKA the cash balance, this is the balance if isDualPricingEnabled is true.
	DualPricingBalance int `json:"dualPricingBalance" db:"dualPricingBalance"`
	// The balance of the sale, after taking into account dual pricing etc.
	EffectiveBalance int `json:"effectiveBalance" db:"effectiveBalance"`
	// True if none of the existing tenders invalidates the cash-only price.
	//
	// False if a credit payment or other invalidating tender has been applied.
	IsDualPricingEnabled bool `json:"isDualPricingEnabled" db:"isDualPricingEnabled"`
	// True if the balance is negative, with some business logic exceptions (e.g
	// the last tender is a legacy gift).
	IsRefundMode bool `json:"isRefundMode" db:"isRefundMode"`
	// If the TenderGroup cannot be completed, this will hold the reason.
	//
	// Null if the business logic says that this sale able to be completed. With
	// some exceptions, this happens when effectiveBalance is 0.
	TenderGroupBlockerReason  *string `json:"tenderGroupBlockerReason" db:"tenderGroupBlockerReason"`
	SaleCompleteBlockerReason *string `json:"saleCompleteBlockerReason" db:"saleCompleteBlockerReason"`
	// If the TenderGroup is not completed, this will be empty string.
	//
	// If the TenderGroup is completed, this will be the final sale of the
	// TenderGroup when `CompleteTenderGroup()` is called.
	//
	// This might be different from the `SaleID`, if the TenderGroup is split off
	// to a new sale, because there are still seats to be tendered.
	CompletedTenderGroupSaleID string `json:"completedTenderGroupSaleID" db:"completedTenderGroupSaleID"`
}
