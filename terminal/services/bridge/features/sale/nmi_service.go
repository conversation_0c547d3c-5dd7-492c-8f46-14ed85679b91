package sale

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/r2pos/bridge/features/helpers"
	"github.com/r2pos/bridge/features/record"
)

type NMIService struct {
	recordService *record.RecordService
}

func NewNMIService(
	recordService *record.RecordService,
) *NMIService {
	return &NMIService{
		recordService: recordService,
	}
}

// Void implements PaymentService.
func (s NMIService) Void(refTxnID string) (*map[string]interface{}, error) {
	form := url.Values{
		"type":          {"void"},
		"transactionid": {refTxnID},
	}

	result, err := s.callAPI(form, "https://secure.nmi.com/api/transact.php", false)
	if err != nil {
		return nil, fmt.Errorf("voiding transaction: %w", err)
	}

	return result, nil
}

// validateTransaction lets you verify a past transaction using query.php
func (s NMIService) validateTransaction(transactionID string) (bool, error) {

	form := url.Values{
		"transaction_id": {transactionID},
	}

	jsonMap, err := s.callAPI(form, "https://secure.nmi.com/api/query.php", true)
	if err != nil {
		return false, fmt.Errorf("querying transaction %s: %w", transactionID, err)
	}

	// Drills into json response to check transaction status.
	isOk := false
	if jsonMap != nil {
		nmResponse, ok := (*jsonMap)["nm_response"].(map[string]interface{})
		if ok && nmResponse["transaction"] != nil {
			transaction, ok := nmResponse["transaction"].(map[string]interface{})
			if ok && (transaction["condition"] == "pendingsettlement" || transaction["status"] == "complete") {
				isOk = true
			}
		}
	}

	return isOk, nil
}

// callAPI posts form data to /api/transact.php and returns the json map.
func (s NMIService) callAPI(data url.Values, endpoint string, isXML bool) (*map[string]interface{}, error) {
	records, err := s.recordService.GetAllRecords()
	if err != nil {
		return nil, fmt.Errorf("getting merchant document: %w", err)
	}

	if records.Merchant.Gateway == nil || records.Merchant.Gateway.DirectKey == "" {
		return nil, fmt.Errorf("merchant NMI key not set")
	}

	data.Set("security_key", records.Merchant.Gateway.DirectKey)

	req, err := http.NewRequest(http.MethodPost, endpoint, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("building request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/xml")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("posting to gateway: %w", err)
	}
	defer resp.Body.Close()

	bytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("reading response: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("gateway returned %s: %s", resp.Status, bytes)
	}

	result := &map[string]interface{}{}

	if isXML {
		result, err = helpers.XMLToJSON(string(bytes))
		if err != nil {
			return nil, fmt.Errorf("converting XML to JSON: %w", err)
		}
	} else {
		result, err = helpers.NMIResponseToJSON(string(bytes))
		if err != nil {
			return nil, fmt.Errorf("converting NMI response to JSON: %w", err)
		}
	}

	return result, nil
}
