package sale

import (
	"context"
	"database/sql"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/r2pos/bridge/features/helpers"
)

type UpdatedRepository interface {
	Update() (*sql.Rows, error)
}

// This service is responsible for updating the sale_updated table, which tracks
// a single timestamp for the last time any sale was updated; this is used for
// clients that poll periodically to see if anything has changed (e.g the
// register controller).
type SaleUpdatedRepo struct {
	db        *sqlx.DB
	dbTimeout time.Duration
}

func NewUpdatedRepo(db *sqlx.DB, dbTimeout time.Duration) *SaleUpdatedRepo {
	return &SaleUpdatedRepo{
		db:        db,
		dbTimeout: dbTimeout,
	}
}

func (s *SaleUpdatedRepo) Update() error {
	ctxTimeout, cancel := context.WithTimeout(context.Background(), s.dbTimeout)
	defer cancel()

	sqlRows, err := helpers.DBQuery(s.db,
		"UPDATE sale_updated SET updated_at = :updated_at",
		map[string]any{"updated_at": helpers.GetCurrentTime().UTC()},
		ctxTimeout)
	if err != nil {
		return err
	}
	defer sqlRows.Close()
	if err = sqlRows.Err(); err != nil {
		return err
	}
	if err = sqlRows.Err(); err != nil {
		return err
	}

	return nil
}
