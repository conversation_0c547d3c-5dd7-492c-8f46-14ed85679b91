package sale

import (
	"encoding/json"
	"errors"
	"fmt"
	"slices"

	"github.com/r2pos/bridge/features/customer"
	"github.com/r2pos/bridge/features/helpers"
	"github.com/r2pos/bridge/features/item"
	"github.com/r2pos/bridge/features/record"
	"github.com/rs/zerolog/log"
)

// This implements methods used in the public API for sale manipulation.
type SaleService struct {
	repo            *SaleRepo
	numberRepo      *SaleNumberRepo
	updatedRepo     *SaleUpdatedRepo
	recordService   *record.RecordService
	itemService     *item.ItemService
	customerService *customer.CustomerService
	NMIService      *NMIService
}

func NewService(
	repo *SaleRepo,
	numberRepo *SaleNumberRepo,
	updatedRepo *SaleUpdatedRepo,
	recordService *record.RecordService,
	itemService *item.ItemService,
	customerService *customer.CustomerService,
	NMIService *NMIService,
) *SaleService {
	return &SaleService{
		repo,
		numberRepo,
		updatedRepo,
		recordService,
		itemService,
		customerService,
		NMIService,
	}
}

func getTenderAppliedAmount(sale *Sale) int {
	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L247
	applied := 0
	for _, t := range ActiveTenders(sale) {
		applied += t.Amount
	}
	return applied
}

func getBalance(sale *Sale) int {
	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L246
	applied := getTenderAppliedAmount(sale)
	return sale.Document.SaleHeader.Total - applied
}

func getDualPricingBalance(sale *Sale) int {
	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L266
	applied := getTenderAppliedAmount(sale)
	return sale.Document.SaleHeader.CashTotal - applied
}

// Shorthand for a lot of conditionals.
//
// Returns the correct balance, based on if dual pricing is applicable.
func getEffectiveBalance(sale *Sale, merchantConfig *record.RecordDocumentSet) int {
	balance := getBalance(sale)
	dualPricingBalance := getDualPricingBalance(sale)
	isDualPricingEnabled := IsDualPricingEnabled(sale, merchantConfig.Merchant.DualPricing)
	if isDualPricingEnabled {
		return dualPricingBalance
	}
	return balance
}

func isLastTenderIsLegacyGift(sale *Sale) bool {
	// Original logic: https://github.com/Round2POS/hyperion/blob/15e3058a48e4e886332682065f9d64af528433d4/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1379

	saleTenders := sale.Document.SaleHeader.Tenders
	if len(saleTenders) == 0 {
		return false
	}

	lastTender := saleTenders[len(saleTenders)-1]
	if lastTender.Media == nil {
		return false
	}
	media := PaymentMediaType(*lastTender.Media)

	return media == PMT_LEGACY_GIFT
}

// {Return, reconstruct a temporary implied sale} that only contains the seats
// that are currently being tendered.
//
// When the TenderGroup is complete (e.g balance is 0) and CompleteTenderGroup()
// is called, this implied sale will be submitted to the DB, either
// updating the original sale, or submitting a new sale split out of the
// original sale.
//
// In the case that the tenderGroupSale != sale, the tenderGroupSale is a
// placeholder, holding saleRows, saleTenders relevant to the seats being
// currently tendered.
func (s *SaleService) getTenderGroupSale(dbSale *Sale, salesTaxList []record.Tax, section *record.Section, merchantConfig *record.RecordDocumentSet) (*Sale, error) {
	err := CheckSale(dbSale, salesTaxList, section, merchantConfig)

	if err != nil {
		return nil, fmt.Errorf("failed to get tender group sale: %w", err)
	}

	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L113
	// If the sale is not being split out, then the original sale is going to be the final sale.
	if len(dbSale.Document.SaleHeader.SeatsTendered) == 0 {
		dbSale, err = Clone(dbSale)
		if err != nil {
			return nil, fmt.Errorf("failed to clone sale: %w", err)
		}
		return dbSale, nil
	}

	// Split out the sale to only contain the associated sale rows.
	tenderGroupSale, err := Clone(dbSale)
	if err != nil {
		return nil, fmt.Errorf("failed to clone sale: %w", err)
	}

	// {sale,sale_number,saleNumber} will be generated in the Upsert() in
	// CompleteTenderGroup().
	originalSaleID := tenderGroupSale.Sale
	tenderGroupSale.Sale = ""
	tenderGroupSale.SaleNumber = 0
	tenderGroupSale.Document.SaleHeader.SaleNumber = 0
	seatsTendering := dbSale.Document.SaleHeader.SeatsTendered

	// Compute the relevant sale rows, to this tender group (based on the seats).
	relevantSaleRows := []SaleRow{}
	for _, sr := range dbSale.Document.SaleRows {
		if slices.Contains(seatsTendering, sr.SeatNumber) {
			relevantSaleRows = append(relevantSaleRows, sr)
		}
	}

	// We clobber the saleRows here, but it will be fixed at the end of this function with a call to CanonicalizedSale().
	tenderGroupSale.Document.SaleRows = relevantSaleRows
	// This is redudant, but here to highlight that we are using seatsTendered of the tenderGroupSale to track the seats it is relevant to.
	tenderGroupSale.Document.SaleHeader.SeatsTendered = seatsTendering
	// NOTE(real-z-r2): tenderGroupSale.document.saleHeader.seatsSettled is kind of meaningless in the split off sale, but we will leave it as is.
	tenderGroupSale.Document.SaleHeader.OriginalSale = &originalSaleID
	tenderGroupSale, err = CanonicalizedSale(tenderGroupSale, salesTaxList, section, merchantConfig, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to get tender group sale: %w", err)
	}
	err = CheckSale(tenderGroupSale, salesTaxList, section, merchantConfig)

	if err != nil {
		return nil, fmt.Errorf("failed to get tender group sale: %w", err)
	}

	return tenderGroupSale, nil
}

// "isRefundMode" is a property of the TenderGroup, used by the client. See
// `TenderGroupInfo.IsRefundMode`.
func isRefundMode(effectiveBalance int, isLastTenderIsLegacyGift bool,
	tenderGroupBlockerReason *string) bool {
	if tenderGroupBlockerReason == nil {
		// We are never in refund mode if the tender group is completed.
		return false
	}

	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L516
	if isLastTenderIsLegacyGift {
		return false
	}

	return effectiveBalance < 0
}

// "tenderGroupBlockerReason" is a property of the TenderGroup, used by the client. See
// `TenderGroupInfo.TenderGroupBlockerReason`.
func tenderGroupBlockerReason(tenderGroupSale *Sale, merchantConfig *record.RecordDocumentSet) *string {
	effectiveBalance := getEffectiveBalance(tenderGroupSale, merchantConfig)
	// Original logic: https://github.com/Round2POS/hyperion/blob/15e3058a48e4e886332682065f9d64af528433d4/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1379
	if isLastTenderIsLegacyGift(tenderGroupSale) {
		// This is the way some merchants handle gift certificates: if you go over, they manually make a new certificate or pay the difference in cash.
		if effectiveBalance <= 0 {
			return nil
		}
		return helpers.Ptr("Balance is positive for legacy gift")
	}
	// A sale is completed when the balance is 0.
	if effectiveBalance == 0 {
		return nil
	}
	return helpers.Ptr("Balance is not 0. It is currently " + fmt.Sprintf("%d", effectiveBalance))
}

// "saleBlockerReason" is a property of the TenderGroup, used by the client. See
// `TenderGroupInfo.SaleBlockerReason`.
func saleBlockerReason(dbSale *Sale, merchantConfig *record.RecordDocumentSet) *string {
	effectiveBalance := getEffectiveBalance(dbSale, merchantConfig)

	if isLastTenderIsLegacyGift(dbSale) {
		if effectiveBalance <= 0 {
			return nil
		}
		return helpers.Ptr("Balance is positive for legacy gift")
	}

	if effectiveBalance == 0 {
		return nil
	}

	return helpers.Ptr("Balance is not 0. It is currently " + fmt.Sprintf("%d", effectiveBalance))
}

// Convenience function to test if two lists of seats are the same.
func isSameSeats(lhs []int, rhs []int) bool {
	// Make a copy and sort
	lhsCopy := append([]int{}, lhs...)
	rhsCopy := append([]int{}, rhs...)
	slices.Sort(lhsCopy)
	slices.Sort(rhsCopy)
	return slices.Equal(lhsCopy, rhsCopy)
}

// Convenience function to compute the TenderGroupInfo, which is implicit from the dbSale.
func (s *SaleService) getTenderGroupInfo(tenderGroupSale *Sale, dbSale *Sale, merchantConfig *record.RecordDocumentSet) *TenderGroupInfo {
	isLastTenderIsLegacyGift := isLastTenderIsLegacyGift(tenderGroupSale)
	ebtFailures := GetEBTFailures(tenderGroupSale)
	balance := getBalance(tenderGroupSale)
	dualPricingBalance := getDualPricingBalance(tenderGroupSale)
	isDualPricingEnabled := IsDualPricingEnabled(tenderGroupSale, merchantConfig.Merchant.DualPricing)
	effectiveBalance := getEffectiveBalance(tenderGroupSale, merchantConfig)
	saleCompleteBlockerReason := saleBlockerReason(dbSale, merchantConfig)
	tenderGroupBlockerReason := tenderGroupBlockerReason(tenderGroupSale, merchantConfig)
	isRefundMode := isRefundMode(effectiveBalance, isLastTenderIsLegacyGift, tenderGroupBlockerReason)

	completedTenderGroupSaleID := ""
	if slices.Contains(tenderGroupSale.Document.SaleHeader.SaleFlags, int(SF_COMPLETED)) {
		completedTenderGroupSaleID = tenderGroupSale.Sale
	}

	tenders := []TenderInfo{}
	for _, t := range tenderGroupSale.Document.SaleHeader.Tenders {
		var media string
		if t.Media != nil {
			media = PaymentMediaType(*t.Media).String()
		}

		tenders = append(tenders, TenderInfo{
			Media:    media,
			Amount:   t.Amount,
			Voided:   slices.Contains(t.SaleTenderFlags, int(SF_VOIDED)),
			Refunded: slices.Contains(t.SaleTenderFlags, int(STF_REFUNDED)),
		})
	}

	return &TenderGroupInfo{
		SaleID:                     dbSale.Sale,
		Seats:                      tenderGroupSale.Document.SaleHeader.SeatsTendered,
		EBTFailures:                ebtFailures,
		Total:                      tenderGroupSale.Document.SaleHeader.Total,
		CashTotal:                  tenderGroupSale.Document.SaleHeader.CashTotal,
		Tenders:                    tenders,
		Balance:                    balance,
		DualPricingBalance:         dualPricingBalance,
		EffectiveBalance:           effectiveBalance,
		IsDualPricingEnabled:       isDualPricingEnabled,
		IsRefundMode:               isRefundMode,
		TenderGroupBlockerReason:   tenderGroupBlockerReason,
		SaleCompleteBlockerReason:  saleCompleteBlockerReason,
		CompletedTenderGroupSaleID: completedTenderGroupSaleID,
	}
}

// Check locks, and basic sanity of the sale.
func lockSaleOr(sale *Sale, terminalNumber int) error {
	if sale == nil {
		return errors.New("sale is nil")
	}

	if slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_COMPLETED)) {
		return errors.New("sale is already completed")
	}

	switch sale.Document.SaleHeader.CurrentTerminalNumber {
	case 0:
		// It is currently unlocked; so lock it.
		sale.Document.SaleHeader.CurrentTerminalNumber = terminalNumber
	case terminalNumber:
		// This is the same terminal, so we don't need to do anything.
	default:
		return fmt.Errorf("sale is locked by another terminal, saleID: %v, currentTerminalNumber: %v, terminalNumber: %v", sale.Sale, sale.Document.SaleHeader.CurrentTerminalNumber, terminalNumber)
	}

	return nil
}

// Convenience function to find a sale, lock it, and canonicalize it.
func (s *SaleService) findSale(saleID string, terminalNumber int, salesTaxList []record.Tax, section *record.Section, merchantConfig *record.RecordDocumentSet) (*Sale, error) {
	sale, err := s.repo.FindByID(saleID)
	if err != nil {
		return nil, fmt.Errorf("failed to find sale: %w", err)
	}

	err = lockSaleOr(sale, terminalNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to find sale, sale was in an unexpected state: %w", err)
	}

	sale, err = CanonicalizedSale(sale, salesTaxList, section, merchantConfig, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to find sale: could not canonicalize the sale: %w", err)
	}

	return sale, nil
}

// Turn a section index into a section.
func (s *SaleService) findSection(sectIdx *int, merchantConfig *record.RecordDocumentSet) *record.Section {
	// Original logic: https://github.com/Round2POS/hyperion/blob/a0c119527f38d61790fd0bd66c8d9347cc3b94f8/terminal/gui/front-of-house/lib/app/modules/register/widgets/sale/controller.dart#L306

	if sectIdx == nil {
		return record.NewSectionDefault()
	}

	for _, sect := range merchantConfig.Sections.Sections {
		if sect.Idx == *sectIdx {
			return &sect
		}
	}

	return record.NewSectionDefault()
}

// This is a public API function. This is the first step in starting a
// tender group. See TenderGroupInfo.
//
// StartTenderGroup starts a new tender group.
//
// Parameters:
//   - `saleID` is the saleID of the original sale that this tender group is
//     associated with. The tender group will sometimes end up creating a new
//     sale, depending if there are any more seats to be tendered after the
//     group is complete.
//   - `request` See StartTenderGroupRequest for more information.
//
// Returns a `TenderGroupInfo` object that represents the current state of the tender group.
//
// See also:
// - `TenderGroupInfo`
// - `RecordTender`
// - `CompleteTenderGroup`
// - `GetTenderGroupInfo`
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L49
func (s *SaleService) StartTenderGroup(saleID string, request StartTenderGroupRequest) (*TenderGroupInfo, error) {
	merchantConfig, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, fmt.Errorf("failed to start tender, unable to get merchant config: %w", err)
	}

	gratSection := s.findSection(request.GratSectIdx, merchantConfig)

	dbSale, err := s.findSale(saleID, request.TerminalNumber, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig)

	if err != nil {
		return nil, fmt.Errorf("failed to start tender, unable to obtain sale: %w", err)
	}

	//////////////////////////////////////////////////////////////////////////////
	// Check if the sale is already being tendered.

	// `SaleHeader.SeatsTendered` tracks seats that are currently being tendered.

	// NOTE: There is unfortunately no way to tell if a tender group was started
	// in the case where all seats were already being tendered.
	if len(dbSale.Document.SaleHeader.SeatsTendered) > 0 {
		// check if SeatsTendered is equal to request.seats
		if isSameSeats(dbSale.Document.SaleHeader.SeatsTendered, request.Seats) {
			// We are continuing a previously started tender group.
		} else {
			return nil, fmt.Errorf("sale with %v is already being tendered, to seats: %v", saleID, dbSale.Document.SaleHeader.SeatsTendered)
		}
	}

	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1331
	// NOTE: Q: Why did the old code have to do this at each tender instead of once when the `sale` object (AKA `tenderGroupSale` in the
	// new code) was created (now in `startTenderGroup()`?
	// NOTE: A: Because we only wanted to save the state if something was already tendered. But it doesn't really matter all that much, and so I
	// changed the behavior, because it is cleaner w.r.t the TenderGroup abstraction.
	// NOTE: This was originally set in _setTender(), (and not in onInit(), which is where most of the logic of this function is from), but
	// logically it should be set when the TenderGroup is started.
	// currentSale.document.saleHeader.seatsTendered = <int>[...seats];
	dbSale.Document.SaleHeader.SeatsTendered = append([]int{}, request.Seats...)

	dbSale, err = s.UpsertSale(*dbSale, request.EmployeeNumber, request.TerminalNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to start tender, unable to save sale: %w", err)
	}

	tgs, err := s.getTenderGroupSale(
		dbSale,
		merchantConfig.SalesTax.Taxes,
		gratSection,
		merchantConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to start tender, unable to get tender group sale: %w", err)
	}

	tgi := s.getTenderGroupInfo(tgs, dbSale, merchantConfig)

	return tgi, nil
}

// Get TenderGroupInfo for an ongoing tender group.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/1e0e257fe1d09b94efaa73c7667f6c6b9581efb9/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L69
func (s *SaleService) GetTenderGroupInfo(saleID string, request GetTenderGroupInfoRequest) (*TenderGroupInfo, error) {
	merchantConfig, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, fmt.Errorf("failed to get tender group info, unable to get merchant config: %w", err)
	}

	gratSection := s.findSection(request.GratSectIdx, merchantConfig)

	dbSale, err := s.findSale(saleID, request.TerminalNumber, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to get tender group info, unable to obtain sale: %w", err)
	}

	tgs, err := s.getTenderGroupSale(
		dbSale,
		merchantConfig.SalesTax.Taxes,
		gratSection,
		merchantConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get tender group info, unable to get tender group sale: %w", err)
	}

	tgi := s.getTenderGroupInfo(tgs, dbSale, merchantConfig)

	return tgi, nil
}

// This is a public API function. This is the second step in starting a
// tendering group. See TenderGroupInfo.
//
// Call this as many times as needed to record all tenders.
//
// Record a tender (payment) - that has been or will be separately processed -
// to the current tender group.
//
// This must be called after `StartTenderGroup()` and before
// `CompleteTenderGroup()`.
//
// Parameters:
//   - `saleID` is the saleID of the original sale that this tender group is
//     associated with.
//   - `request` See RecordTenderRequest for more information.
//
// Returns an `TenderGroupInfo` object that represents the current state of the
// tender group after the tender is added.
//
// See also:
// - `TenderGroupInfo`
// - `StartTenderGroup`
// - `CompleteTenderGroup`
// - `GetTenderGroupInfo`
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L99
func (s *SaleService) RecordTender(saleID string, request RecordTenderRequest) (*TenderGroupInfo, error) {
	log.Info().Msg(fmt.Sprintf("record tender (saleID: %v, request: %v)", saleID, request))

	merchantConfig, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, fmt.Errorf("failed to record tender, unable to get merchant config: %w", err)
	}

	gratSection := s.findSection(request.GratSectIdx, merchantConfig)

	dbSale, err := s.findSale(saleID, request.TerminalNumber, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to record tender, unable to obtain sale: %w", err)
	}

	//////////////////////////////////////////////////////////////////////////////
	newTender := SaleTender{
		Amount:              request.Amount,
		Media:               &request.Media,
		CheckNum:            request.CheckNum,
		Memo:                request.Memo,
		SaleTenderFlags:     []int{},
		TipAmount:           request.TipAmount,
		TipMedia:            request.TipMedia,
		CardTransactionData: request.CardTransactionData,
		GiftTransactionData: request.GiftTransactionData,
	}

	if request.Amount < 0 && !request.DisableRefund {
		// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1296

		log.Info().Msg(fmt.Sprintf("tendering refund (amount: %d)", request.Amount))

		newTender.SaleTenderFlags = append(newTender.SaleTenderFlags, int(STF_REFUNDED))
		// NOTE: Some of the original logic was moved into `CanonicalizedSale`.
	}

	dbSale.Document.SaleHeader.Tenders = append(dbSale.Document.SaleHeader.Tenders, newTender)

	// For the new online ordering and QR pay, we want to record the source of the
	// payment in the sale flags.
	switch PaymentRoute(request.Route) {
	case PR_REGISTER:
		break
	case PR_QR_PAY:
		dbSale.Document.SaleHeader.SaleFlags = append(dbSale.Document.SaleHeader.SaleFlags, int(SF_QR_PAY))
		newTender.SaleTenderFlags = append(newTender.SaleTenderFlags, int(STF_QR))
	case PR_ONLINE_ORDER:
		dbSale.Document.SaleHeader.SaleFlags = append(dbSale.Document.SaleHeader.SaleFlags, int(SF_ONLINE_ORDERED))
		newTender.SaleTenderFlags = append(newTender.SaleTenderFlags, int(STF_ECOM))
	}

	dbSale, err = CanonicalizedSale(dbSale, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to record tender, unable to compute sale: %w", err)
	}

	dbSale, err = s.UpsertSale(*dbSale, request.EmployeeNumber, request.TerminalNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to record tender, unable to save sale: %w", err)
	}
	//////////////////////////////////////////////////////////////////////////////
	tgs, err := s.getTenderGroupSale(
		dbSale,
		merchantConfig.SalesTax.Taxes,
		gratSection,
		merchantConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to record tender, unable to get tender group sale: %w", err)
	}

	tgi := s.getTenderGroupInfo(tgs, dbSale, merchantConfig)
	//////////////////////////////////////////////////////////////////////////////

	return tgi, nil
}

// Completes the current tender group.
//
// When the client is done tendering, CompleteTenderGroup() is called. This
// function will finalize the TenderGroup, and either update the original sale,
// or create a new sale, if the original sale is being split out.
//
// It is an error to call this if the TenderGroup is unable to be completed (see
// `TenderGroupInfo.TenderGroupBlockerReason`).
//
// It is an error to call this while the TenderGroup still has
// `TenderGroupInfo.TenderGroupBlockerReason` set.
//
// Parameters:
//   - `saleID` is the saleID of the original sale that this tender group is associated with.
//   - `request` See CompleteTenderGroupRequest for more information.
//
// Returns a `TenderGroupInfo` object that represents the current state of the tender group after the tender group is completed.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/131a062c0f471ccc9333d4391ab515cc32759744/terminal/gui/front-of-house/lib/app/data/services/tender.service.dart#L131
//
//nolint:funlen // This function is long, but we like that.
func (s *SaleService) CompleteTenderGroup(saleID string, request CompleteTenderGroupRequest) (*TenderGroupInfo, error) {
	merchantConfig, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, fmt.Errorf("failed to complete tender group, unable to get merchant config: %w", err)
	}

	gratSection := s.findSection(request.GratSectIdx, merchantConfig)

	dbSale, err := s.findSale(saleID, request.TerminalNumber, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig)

	if err != nil {
		return nil, fmt.Errorf("failed to complete tender group, unable to obtain sale: %w", err)
	}

	tgs, err := s.getTenderGroupSale(
		dbSale,
		merchantConfig.SalesTax.Taxes,
		gratSection,
		merchantConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to complete tender group, unable to get tender group sale: %w", err)
	}

	// Check if the tender group is unable to be completed.
	{
		tgi := s.getTenderGroupInfo(tgs, dbSale, merchantConfig)

		if tgi.TenderGroupBlockerReason != nil {
			return nil, fmt.Errorf("failed to complete tender group, reason: %v", *tgi.TenderGroupBlockerReason)
		}
	}

	// Now finish up computing fields in tenderGroupSale and the original sale (if they are not the same) and upsert them to the DB.
	{
		// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1173
		tgs.Document.SaleHeader.CurrentCashier = request.CurrentCashier

		tgs.Document.SaleHeader.SaleFlags = append(tgs.Document.SaleHeader.SaleFlags, int(SF_COMPLETED))

		tgs.Document.SaleHeader.SettleEmployeeNumber = &request.SettleEmployeeNumber
		tgs.Document.SaleHeader.SettleTerminalNumber = &request.SettleTerminalNumber
		// currentTerminalNumber is a lock, so unlock it.
		tgs.Document.SaleHeader.CurrentTerminalNumber = 0
		tgs.EndAt = helpers.Ptr(helpers.GetCurrentTime().UTC())

		// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1186
		tgs.Document.SaleHeader.DualPricingPercent = merchantConfig.Merchant.DualPricingPercent
		shouldSaleDualPricingEnabled := IsDualPricingEnabled(tgs, merchantConfig.Merchant.DualPricing)
		// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1189
		SetFlag(&tgs.Document.SaleHeader.SaleFlags, SF_DUAL_PRICING, shouldSaleDualPricingEnabled, nil)

		seats := &dbSale.Document.SaleHeader.SeatsTendered
		tenders := &tgs.Document.SaleHeader.Tenders

		// If the sale is being split out (because it is being partially tendered):
		if dbSale.Sale != tgs.Sale {
			// seats here is always non-empty, because if we were tendering the entire sale, then tenderGroupSale == currentSale.

			// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1258

			// Since we are splitting out the sale: Remove the saleRows that are being settled; they have been moved to the new sale.

			// Compute the relevant sale rows, that remain relevant to the original sale (based on the seats).
			relevantSaleRows := []SaleRow{}
			for _, sr := range dbSale.Document.SaleRows {
				if !slices.Contains(*seats, sr.SeatNumber) {
					relevantSaleRows = append(relevantSaleRows, sr)
				}
			}

			dbSale.Document.SaleRows = relevantSaleRows

			dbSale.Document.SaleRows, err = FixedSaleRows(dbSale.Document.SaleRows, nil)
			if err != nil {
				return nil, fmt.Errorf("failed to complete tender group, unable to fix sale rows: %w", err)
			}

			// In the original sale, record the seats that have been settled. This is so that the original sale never reuses these seat numbers.
			dbSale.Document.SaleHeader.SeatsSettled = append(*seats, dbSale.Document.SaleHeader.SeatsSettled...)

			// Since these seats are settled, we can clear the tendered seats; since `seatsTendered` is just a state variable that tracks current
			// TenderGroup's seats so that it can be resumed; since we are ending the TenderGroup, there is no longer anything to be resumed.
			dbSale.Document.SaleHeader.SeatsTendered = []int{}
			// Since we just settled all these tenders, we can clear them. This is because we are splitting out all the tenders to the new sale.
			dbSale.Document.SaleHeader.Tenders = []SaleTender{}
			// Do not unlock the original sale, because it is still being tendered.
			// dbSale.Document.SaleHeader.CurrentTerminalNumber = 0

			dbSale, err = CanonicalizedSale(dbSale, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig, nil)
			if err != nil {
				return nil, fmt.Errorf("failed to complete tender group, unable to compute sale: %w", err)
			}
		}

		// We don't want to alter seatsSettled, because that is about masking out
		// seats from [1...seatsCnt], and this sale inherits that correctly from the
		// original sale.
		// tgs.Document.SaleHeader.SeatsSettled = append([]int{}, *seats...)

		// Since these seats are settled, we can clear the tendered seats; since `seatsTendered` tracks currently tendering seats.
		tgs.Document.SaleHeader.SeatsTendered = []int{}
		// This is redundant, but is here to highlight that this field is used to track the tenders that are settled in this tender group.
		tgs.Document.SaleHeader.Tenders = append([]SaleTender{}, *tenders...)
		////////////////////////////////////////////////////////////////////////////
		tgs, err = CanonicalizedSale(tgs, merchantConfig.SalesTax.Taxes, gratSection, merchantConfig, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to complete tender group, unable to compute sale: %w", err)
		}
	}

	tgs, err = s.UpsertSale(*tgs, request.EmployeeNumber, request.TerminalNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to complete tender group, unable to save TenderGroup sale: %w", err)
	}

	if dbSale.Sale != tgs.Sale {
		dbSale, err = s.UpsertSale(*dbSale, request.EmployeeNumber, request.TerminalNumber)
		if err != nil {
			return nil, fmt.Errorf("failed to complete tender group, unable to save original sale: %w", err)
		}
	}

	tgi := s.getTenderGroupInfo(tgs, dbSale, merchantConfig)

	return tgi, nil
}

func (s *SaleService) ForkSale(saleID string, seats []int) (*Sale, error) {
	sale, err := s.repo.FindByID(saleID)
	if err != nil {
		return nil, err
	}

	return sale, nil
}

// func (s *SaleService) CompleteSale(saleID string, request CompleteSaleRequest, sale *Sale) (*Sale, error) {
// 	records, err := s.recordService.GetAllRecordsCached()
// 	if err != nil {
// 		return nil, err
// 	}

// 	dualPricingPercent := records.Merchant.DualPricingPercent

// 	if sale == nil {
// 		sale, err = s.repo.FindByID(saleID)
// 		if err != nil {
// 			return nil, err
// 		}
// 	}

// 	dualPriceEligible := true

// 	tenderTotal := 0

// 	for _, t := range sale.Document.SaleHeader.Tenders {
// 		tenderTotal += t.Amount
// 		if *t.Media == int(PMT_CREDIT) || *t.Media == int(PMT_DEBIT) {
// 			dualPriceEligible = false
// 		}
// 	}

// 	if (!dualPriceEligible && tenderTotal < sale.Document.SaleHeader.Total) || (dualPriceEligible && tenderTotal < sale.Document.SaleHeader.CashTotal) {
// 		return nil, errors.New("cannot complete sale: tender total is less than sale total")
// 	}

// 	sale.Document.SaleHeader.SettleTerminalNumber = &request.TerminalNumber
// 	sale.Document.SaleHeader.SettleEmployeeNumber = &request.EmployeeNumber

// 	if records.Merchant.DualPricing {
// 		sale.Document.SaleHeader.DualPricingPercent = dualPricingPercent
// 		if dualPriceEligible {
// 			if !slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_DUAL_PRICING)) {
// 				sale.Document.SaleHeader.SaleFlags = append(sale.Document.SaleHeader.SaleFlags, int(SF_DUAL_PRICING))
// 			}
// 			for i := range sale.Document.SaleRows {
// 				sale.Document.SaleRows[i].ActualPrice = sale.Document.SaleRows[i].CashPrice
// 			}
// 		}
// 	}

// 	if !slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_COMPLETED)) {
// 		sale.Document.SaleHeader.SaleFlags = append(sale.Document.SaleHeader.SaleFlags, int(SF_COMPLETED))
// 	}

// 	if slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_SUSPENDED)) {
// 		sale.Document.SaleHeader.SaleFlags = RemoveInts(sale.Document.SaleHeader.SaleFlags, []int{int(SF_SUSPENDED)})
// 	}

// 	sale.Document.SaleHeader.CurrentTerminalNumber = 0
// 	sale.EndAt = helpers.Ptr(time.Now().UTC())

// 	result, err := s.UpsertSale(*sale, request.EmployeeNumber, request.TerminalNumber)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return result, nil
// }

func (s *SaleService) NewSale() (*Sale, error) {
	newSale := Sale{
		Document: SaleDocument{
			SaleHeader: SaleHeader{},
			SaleRows:   []SaleRow{},
		},
	}

	result, err := s.repo.Upsert(&newSale)
	if err != nil {
		return nil, err
	}

	log.Info().Msg("Finished upsert")

	err = s.updatedRepo.Update()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *SaleService) UpsertSale(sale Sale, employeeNumber int, terminalNumber int) (*Sale, error) {
	isNewSale := sale.Sale == ""

	if isNewSale {
		newNum, err := s.numberRepo.GetNewNumber()
		if err != nil {
			return nil, err
		}
		sale.SaleNumber = newNum
		sale.Document.SaleHeader.SaleNumber = newNum
		sale.Document.SaleHeader.StartTerminalNumber = terminalNumber
		sale.Document.SaleHeader.StartEmployeeNumber = employeeNumber
		sale.Document.SaleHeader.CurrentEmployeeNumber = employeeNumber
	}

	sale.Suspended = slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_SUSPENDED)) || sale.Document.SaleHeader.CurrentTerminalNumber != 0

	result, err := s.repo.Upsert(&sale)
	if err != nil {
		return nil, err
	}

	log.Info().Msg("Finished upsert")

	err = s.updatedRepo.Update()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *SaleService) LockSale(saleID string, unlock bool) (*Sale, error) {
	sale, err := s.repo.FindByID(saleID)
	if err != nil {
		return nil, err
	}
	if unlock {
		sale.Document.SaleHeader.CurrentTerminalNumber = 0
	} else {
		sale.Document.SaleHeader.CurrentTerminalNumber = -1
	}

	result, err := s.repo.Upsert(sale)
	if err != nil {
		return nil, err
	}

	log.Info().Msg("Finished upsert")

	err = s.updatedRepo.Update()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *SaleService) UnlockSale(saleID string) (*Sale, error) {
	sale, err := s.repo.FindByID(saleID)
	if err != nil {
		return nil, err
	}

	sale.Document.SaleHeader.CurrentTerminalNumber = 0

	result, err := s.repo.Upsert(sale)
	if err != nil {
		return nil, err
	}

	log.Info().Msg("Finished upsert")

	err = s.updatedRepo.Update()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *SaleService) DetailedItemsToSaleRows(dItems *[]*item.DetailedItem, parentRow *SaleRow, parentItem *item.Item, grandParentItem *item.Item, ecomConfig *record.EcomSettingDocument) (*[]SaleRow, error) {
	returnList := []SaleRow{}

	if dItems == nil {
		return &returnList, nil
	}
	if len(*dItems) == 0 {
		return &returnList, nil
	}

	for _, dItem := range *dItems {
		log.Info().Msg(dItem.Desc)

		if !dItem.Selected {
			continue
		}

		newRow, newItem, err := s.newSaleRowFromDetailed(dItem, parentRow, parentItem, grandParentItem, ecomConfig)
		if err != nil {
			return nil, err
		}

		parentIdx := -1
		if parentRow != nil {
			parentIdx = parentRow.Index
		}
		newRow.Index = len(returnList) + parentIdx + 1
		returnList = append(returnList, *newRow)

		ptrList := []*item.DetailedItem{}
		for i := range dItem.Modifiers {
			if dItem.Modifiers[i].Selected {
				ptrList = append(ptrList, &dItem.Modifiers[i])
			}
		}
		if len(ptrList) > 0 {
			var modRows *[]SaleRow
			modRows, err = s.DetailedItemsToSaleRows(&ptrList, newRow, newItem, parentItem, ecomConfig)
			if err != nil {
				return nil, err
			}
			returnList = append(returnList, *modRows...)
		}
	}
	return &returnList, nil
}

func (s *SaleService) newSaleRowFromDetailed(dItem *item.DetailedItem, parentRow *SaleRow, parentItem *item.Item, grandParentItem *item.Item, ecomConfig *record.EcomSettingDocument) (*SaleRow, *item.Item, error) {

	flags := []int{}
	fullItemInterface, err := s.itemService.GetByID(dItem.Item, false)
	parentIdx := -1
	if err != nil {
		return nil, nil, err
	}

	fullItem, ok := fullItemInterface.(*item.Item)
	if !ok {
		return nil, nil, errors.New("failed to cast item to item.Item")
	}

	receiptDesc := fullItem.LongDesc
	creditPrice := fullItem.Document.Pricing["S0L0C0"]
	cashPrice := creditPrice

	if fullItem.Document.ReceiptDesc != nil && *fullItem.Document.ReceiptDesc != "" {
		receiptDesc = *fullItem.Document.ReceiptDesc
	}
	if fullItem.DepartmentByDepartment.Document.IsTaxable != nil && *fullItem.DepartmentByDepartment.Document.IsTaxable {
		flags = append(flags, int(SR_TAXABLE))
	}
	if fullItem.Document.AllowEbt {
		flags = append(flags, int(SR_ALLOW_EBT))
	}
	if fullItem.Document.PromptForPrice || fullItem.Document.IsOpenPrice {
		flags = append(flags, int(SR_OPEN_PRICE))
	}
	if fullItem.Document.Pricing["S0L0C1"] != 0 {
		cashPrice = fullItem.Document.Pricing["S0L0C1"]
	}
	if parentItem != nil && parentItem.Document.OverridePricing {
		creditPrice = item.GetOverridePrice(fullItem, parentItem, creditPrice, false, ecomConfig)
		cashPrice = item.GetOverridePrice(fullItem, parentItem, cashPrice, true, ecomConfig)
	}
	if parentRow != nil {
		parentIdx = parentRow.Index
	}

	var isVisible bool = parentRow == nil || parentItem == nil || dItem.Price > 0 || len(dItem.Modifiers) == 0 || !fullItem.Document.IsModifier

	if !isVisible {
		if parentItem != nil && parentItem.Document.Modifiers[fullItem.Item].ForceVisible {
			isVisible = true
		}
		if grandParentItem != nil && grandParentItem.Document.MultiModLists {
			isVisible = true
		}
	}

	prep := fullItem.DepartmentByDepartment.Document.Prep
	if fullItem.Document.Prep > 0 {
		prep = fullItem.Document.Prep
	}

	newRow := SaleRow{
		Item:               dItem.Item,
		IsVisible:          isVisible,
		OriginalPrice:      creditPrice,
		CashOriginalPrice:  cashPrice,
		BasePrice:          creditPrice,
		CashBasePrice:      cashPrice,
		Flags:              flags,
		Qty:                dItem.Qty,
		Department:         fullItem.DepartmentByDepartment.Title,
		ItemPricing:        fullItem.Document.Pricing,
		Parent:             parentIdx,
		Employee:           nil,
		ReceiptDescription: receiptDesc,
		TakeOutSurcharge:   fullItem.Document.TakeOutSurcharge,
		TaxFlags:           fullItem.DepartmentByDepartment.Document.TaxFlags,
		OrigTaxFlags:       fullItem.DepartmentByDepartment.Document.TaxFlags,
		TransactionFlags:   []int{},
		Upc:                fullItem.Upc,
		Prep:               prep,
	}
	return &newRow, fullItem, nil
}

func (s *SaleService) AddDetailedRowsToSale(dItems *[]*item.DetailedItem, sale *Sale, orderType OrderType) (*Sale, error) {
	records, err := s.recordService.GetAllRecordsCached()
	if err != nil {
		return nil, err
	}
	if len(*dItems) == 0 {
		return nil, errors.New("no items found")
	}
	if sale == nil {
		sale = &Sale{}
	}

	sale.Document.SaleHeader.SeatCnt = 1

	newRows, err := s.DetailedItemsToSaleRows(dItems, nil, nil, nil, records.EcomSetting)
	if err != nil {
		return nil, err
	}
	bytes, err := json.Marshal(newRows)
	if err != nil {
		return nil, err
	}
	log.Info().Msg(string(bytes))
	addIdx := len(sale.Document.SaleRows)
	for _, row := range *newRows {
		row.Index += addIdx
		if row.Parent != -1 {
			row.Parent += addIdx
		}
		sale.Document.SaleRows = append(sale.Document.SaleRows, row)
	}
	sale.Document.SaleHeader.OrderType = int(orderType)
	result, err := CanonicalizedSale(sale, records.SalesTax.Taxes, nil, records, nil)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *SaleService) OnlineOrderTotals(onlineOrderTotalsRequest *OnlineOrderTotalsRequest) (*OnlineOrderTotals, error) {
	sale, err := s.AddDetailedRowsToSale(onlineOrderTotalsRequest.Items, nil, OT_TAKEOUT)
	if err != nil {
		return nil, err
	}

	sale.Document.SaleHeader.OrderType = int(OT_TAKEOUT)

	blockedReasons := s.checkItemCountUsingSaleRows(sale.Document.SaleRows)

	isActive, err := s.itemService.CheckIfOnlineActive(helpers.GetCurrentTime())
	if err != nil {
		return nil, err
	}
	if !isActive {
		blockedReasons = append(blockedReasons, "online ordering is not active")
	}

	if onlineOrderTotalsRequest.PromisedTime != nil && isActive {
		isActive, err = s.itemService.CheckIfOnlineActive(helpers.MilitaryIntToTime(*onlineOrderTotalsRequest.PromisedTime))
		if err != nil {
			return nil, err
		}
		if !isActive {
			blockedReasons = append(blockedReasons, "promised time is outside of online ordering hours")
		}
	}

	onlineOrderTotals := OnlineOrderTotals{
		DiscountTotal:         sale.Document.SaleHeader.DiscountTotal,
		GratuityTotal:         sale.Document.SaleHeader.GratuityTotal,
		SubTotal:              sale.Document.SaleHeader.SubTotal,
		TakeOutSurchargeTotal: sale.Document.SaleHeader.TakeOutSurchargeTotal,
		TaxTotal:              sale.Document.SaleHeader.TaxTotal,
		TaxTotals:             sale.Document.SaleHeader.TaxTotals,
		Total:                 sale.Document.SaleHeader.Total,
		Items:                 onlineOrderTotalsRequest.Items,
		BlockedReasons:        blockedReasons,
		PromisedTime:          onlineOrderTotalsRequest.PromisedTime,
	}

	return &onlineOrderTotals, nil
}

func (s *SaleService) QRPayment(saleID string, qrPaymentRequest *QRPaymentRequest) (*QRPaymentReturn, error) {
	var tipMedia *int = nil

	if qrPaymentRequest.TipAmount != nil && *qrPaymentRequest.TipAmount > 0 {
		tipMedia = helpers.Ptr(int(PMT_CREDIT))
	}

	recordTenderRequest := RecordTenderRequest{
		Route:               int(PR_QR_PAY),
		Media:               int(PMT_CREDIT),
		Amount:              qrPaymentRequest.Amount,
		TipAmount:           qrPaymentRequest.TipAmount,
		TipMedia:            tipMedia,
		CardTransactionData: qrPaymentRequest.CardTransactionData,
		Memo:                qrPaymentRequest.Memo,
	}

	tgi, err := s.RecordTender(saleID, recordTenderRequest)
	if err != nil {
		return nil, err
	}
	if tgi.TenderGroupBlockerReason != nil {
		return nil, fmt.Errorf("error recording tender for sale %v: %v", &saleID, *tgi.TenderGroupBlockerReason)
	}

	tgi, err = s.CompleteTenderGroup(saleID, CompleteTenderGroupRequest{
		EmployeeNumber:       recordTenderRequest.EmployeeNumber,
		TerminalNumber:       recordTenderRequest.TerminalNumber,
		CurrentCashier:       nil,
		SettleEmployeeNumber: -2,
		SettleTerminalNumber: -2,
	})
	if err != nil {
		return nil, err
	}
	if tgi.SaleCompleteBlockerReason != nil {
		return nil, fmt.Errorf("error completing sale %v: %v", &saleID, *tgi.SaleCompleteBlockerReason)
	}

	qrPaymentReturn := QRPaymentReturn{Total: tgi.Total, SaleID: tgi.SaleID, Tenders: tgi.Tenders, TipAmount: qrPaymentRequest.TipAmount}

	return &qrPaymentReturn, nil
}

//nolint:funlen // This function is long, but it's cool.
func (s *SaleService) OnlineOrder(onlineOrderRequest *OnlineOrderRequest) (*OnlineOrderReturn, error) {
	isActive, err := s.itemService.CheckIfOnlineActive(helpers.GetCurrentTime())
	if err != nil {
		return nil, err
	}
	if !isActive {
		return nil, errors.New("online ordering is not active")
	}
	if onlineOrderRequest.PromisedTime != nil {
		isActive, err = s.itemService.CheckIfOnlineActive(helpers.MilitaryIntToTime(*onlineOrderRequest.PromisedTime))
		if err != nil {
			return nil, err
		}
		if !isActive {
			return nil, errors.New("promised time is outside of online ordering hours")
		}
	}

	if onlineOrderRequest.RefTxnID == nil || *onlineOrderRequest.RefTxnID == "" {
		return nil, errors.New("refTxnID is required for online orders")
	}

	isValid, err := s.NMIService.validateTransaction(*onlineOrderRequest.RefTxnID)
	if err != nil {
		return nil, fmt.Errorf("failed to validate transaction: %w", err)
	}

	if !isValid {
		return nil, errors.New("invalid transaction")
	}

	var tipMedia *int = nil

	if onlineOrderRequest.TipAmount != nil && *onlineOrderRequest.TipAmount > 0 {
		tipMedia = helpers.Ptr(int(PMT_CREDIT))
	}

	recordTenderRequest := RecordTenderRequest{
		Route:     int(PR_ONLINE_ORDER),
		Media:     int(PMT_CREDIT),
		Amount:    onlineOrderRequest.Amount,
		TipAmount: onlineOrderRequest.TipAmount,
		TipMedia:  tipMedia,
		CardTransactionData: &CardTransData{
			Provider:        int(PP_NMI),
			EntryMode:       int(EM_NMI),
			TransactionMode: int(TM_NMI),
			AuthCode:        *onlineOrderRequest.AuthCode,
			CardPan:         *onlineOrderRequest.CardPan,
			CardHolder:      *onlineOrderRequest.CardHolder,
			RefTxnID:        *onlineOrderRequest.RefTxnID,
		},
		Memo: onlineOrderRequest.Memo,
	}

	sale, err := s.AddDetailedRowsToSale(onlineOrderRequest.DetailedItems, nil, OT_TAKEOUT)
	if err != nil {
		return nil, err
	}

	err = s.subtractFromItemCountUsingSaleRows(sale.Document.SaleRows)
	if err != nil {
		return nil, err
	}

	customerSearchRes, err := s.customerService.GetByNameAndPhone(onlineOrderRequest.CustomerFirstName, onlineOrderRequest.CustomerLastName, onlineOrderRequest.CustomerPhone)
	if err != nil {
		return nil, err
	}

	customerUpsert := true
	customer := &customer.Customer{Document: customer.Document{FirstName: &onlineOrderRequest.CustomerFirstName, LastName: &onlineOrderRequest.CustomerLastName, Phone1: &onlineOrderRequest.CustomerPhone}}

	if len(*customerSearchRes) != 0 {
		customer = (*customerSearchRes)[0]
		customerUpsert = false
	}

	if customer.Document.Email == nil || *customer.Document.Email == "" {
		customer.Document.Email = onlineOrderRequest.CustomerEmail
		customerUpsert = true
	}

	if customerUpsert {
		customer, err = s.customerService.UpsertCustomer(customer)
		if err != nil {
			return nil, fmt.Errorf("failed to upsert new online order customer: %w", err)
		}
	}

	customerName := onlineOrderRequest.CustomerFirstName + " " + onlineOrderRequest.CustomerLastName
	sale.Document.SaleHeader.CustomerName = &customerName
	sale.Document.SaleHeader.Customer = &customer.Customer

	if onlineOrderRequest.PromisedTime != nil {
		millis := helpers.MilitaryIntToMillis(*onlineOrderRequest.PromisedTime)
		sale.Document.SaleHeader.PromisedTime = &millis
	}

	if !slices.Contains(sale.Document.SaleHeader.SaleFlags, int(SF_SUSPENDED)) {
		sale.Document.SaleHeader.SaleFlags = append(sale.Document.SaleHeader.SaleFlags, int(SF_SUSPENDED))
	}

	dbSale, err := s.UpsertSale(*sale, -2, -2)
	if err != nil {
		return nil, fmt.Errorf("failed to upsert new online order sale: %w", err)
	}

	tgi, err := s.RecordTender(dbSale.Sale, recordTenderRequest)
	if err != nil {
		return nil, err
	}
	if tgi.TenderGroupBlockerReason != nil {
		return nil, fmt.Errorf("error recording tender for sale %v: %v", &dbSale.Sale, *tgi.TenderGroupBlockerReason)
	}

	tgi, err = s.CompleteTenderGroup(dbSale.Sale, CompleteTenderGroupRequest{
		EmployeeNumber:       recordTenderRequest.EmployeeNumber,
		TerminalNumber:       recordTenderRequest.TerminalNumber,
		CurrentCashier:       nil,
		SettleEmployeeNumber: -2,
		SettleTerminalNumber: -2,
	})
	if err != nil {
		return nil, err
	}
	if tgi.SaleCompleteBlockerReason != nil {
		return nil, fmt.Errorf("error completing sale %v: %v", &dbSale.Sale, *tgi.SaleCompleteBlockerReason)
	}

	onlineOrderReturn := OnlineOrderReturn{Total: tgi.Total, SaleID: tgi.SaleID, Tenders: tgi.Tenders, Customer: customer.Customer, TipAmount: onlineOrderRequest.TipAmount, PromisedTime: onlineOrderRequest.PromisedTime}

	return &onlineOrderReturn, nil
}

// func (s *SaleService) subtractFromItemCountUsingItemUUIDAndQty(uuid string, qty int) error {
// 	err := s.addToItemCountUsingSaleRows([]SaleRow{
// 		{
// 			Item:               uuid,
// 			Upc:                "",
// 			ReceiptDescription: "",
// 			Department:         "",
// 			TransactionFlags:   []int{},
// 			Flags:              []int{},
// 			BasePrice:          0,
// 			OriginalPrice:      0,
// 			Qty:                qty * -1,
// 		},
// 	})
// 	if err != nil {
// 		return fmt.Errorf("failed to subtract from item count: %w", err)
// 	}
// 	return nil
// }

// func (s *SaleService) addToItemCountUsingItemUUIDAndQty(uuid string, qty int) error {
// 	err := s.addToItemCountUsingSaleRows([]SaleRow{
// 		{
// 			Item:               uuid,
// 			Upc:                "",
// 			ReceiptDescription: "",
// 			Department:         "",
// 			TransactionFlags:   []int{},
// 			Flags:              []int{},
// 			BasePrice:          0,
// 			OriginalPrice:      0,
// 			Qty:                qty,
// 		},
// 	})
// 	if err != nil {
// 		return fmt.Errorf("failed to add to item count: %w", err)
// 	}
// 	return nil
// }

func (s *SaleService) subtractFromItemCountUsingSaleRows(saleRows []SaleRow) error {
	negativeList := []SaleRow{}
	for _, sr := range saleRows {
		negativeList = append(negativeList, SaleRow{
			Item:               sr.Item,
			Upc:                "",
			ReceiptDescription: "",
			Department:         "",
			TransactionFlags:   []int{},
			Flags:              []int{},
			BasePrice:          0,
			OriginalPrice:      0,
			Qty:                sr.Qty * -1,
		})
	}

	err := s.addToItemCountUsingSaleRows(negativeList)
	if err != nil {
		return fmt.Errorf("failed to subtract from item count: %w", err)
	}
	return nil
}

func (s *SaleService) addToItemCountUsingSaleRows(saleRows []SaleRow) error {
	if len(saleRows) == 0 {
		return nil
	}

	rowMap := make(map[string]int)

	for _, sr := range saleRows {
		if _, exists := rowMap[sr.Item]; !exists {
			rowMap[sr.Item] = sr.Qty
		} else {
			rowMap[sr.Item] += sr.Qty
		}
	}

	delete(rowMap, "00000000-0000-0000-0000-000000000001")
	delete(rowMap, "00000000-0000-0000-0000-000000000002")
	delete(rowMap, "00000000-0000-0000-0000-000000000003")

	keys := []string{}

	for key := range rowMap {
		keys = append(keys, key)
	}

	itemInterfaces, err := s.itemService.GetMultipleByID(keys, false)
	if err != nil {
		return fmt.Errorf("failed to get items: %w", err)
	}

	itemRes, ok := itemInterfaces.(*[]*item.Item)
	if !ok {
		return errors.New("failed to cast item result to []item.Item")
	}

	itemList := []item.Item{}
	for _, item := range *itemRes {
		if item.Document.GetCount() >= 0 {
			itemList = append(itemList, *item)
		}
	}

	for _, item := range itemList {
		if item.Document.GetCount()+rowMap[item.Item] < 0 {
			return fmt.Errorf("Item count too low for %v", item.LongDesc)
		}
	}

	for _, item := range itemList {
		count := item.Document.GetCount() + rowMap[item.Item]
		item.Document.Count = &count
		_, err := s.itemService.Upsert(&item)
		if err != nil {
			return fmt.Errorf("failed to update item: %w", err)
		}
	}
	return nil
}

func (s *SaleService) checkItemCountUsingSaleRows(saleRows []SaleRow) []string {
	if len(saleRows) == 0 {
		return nil
	}

	rowMap := make(map[string]int)

	for _, sr := range saleRows {
		if _, exists := rowMap[sr.Item]; !exists {
			rowMap[sr.Item] = sr.Qty
		} else {
			rowMap[sr.Item] += sr.Qty
		}
	}

	delete(rowMap, "00000000-0000-0000-0000-000000000001")
	delete(rowMap, "00000000-0000-0000-0000-000000000002")
	delete(rowMap, "00000000-0000-0000-0000-000000000003")

	keys := []string{}

	for key := range rowMap {
		keys = append(keys, key)
	}

	blockedReasons := []string{}

	itemInterfaces, err := s.itemService.GetMultipleByID(keys, false)
	if err != nil {
		blockedReasons = append(blockedReasons, "Failed to get item(s)")
	}

	itemRes, ok := itemInterfaces.(*[]*item.Item)
	if !ok {
		blockedReasons = append(blockedReasons, "Failed to cast item")
	}

	itemList := []item.Item{}
	for _, item := range *itemRes {
		if item.Document.GetCount() >= 0 {
			itemList = append(itemList, *item)
		}
	}

	for _, item := range itemList {
		if item.Document.GetCount()-rowMap[item.Item] < 0 {
			blockedReasons = append(blockedReasons, fmt.Sprintf("Item count too low for %v", item.LongDesc))
		}
	}

	return blockedReasons
}

func (s *SaleService) CheckOnlineHealth() (bool, error) {
	onlineActive, err := s.itemService.CheckIfOnlineActive(helpers.GetCurrentTime())
	if err != nil {
		return false, err
	}

	return onlineActive, nil
}
