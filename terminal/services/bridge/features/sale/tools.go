package sale

import (
	"encoding/json"
	"fmt"
	"math"
	"slices"
	"strconv"

	"github.com/jmoiron/sqlx"
	"github.com/r2pos/bridge/features/record"
	"github.com/rs/zerolog/log"
)

func RemoveInts(slice []int, toRemove []int) []int {
	result := []int{}
	for _, value := range slice {
		if !slices.Contains(toRemove, value) {
			result = append(result, value)
		}
	}
	return result
}

// Deprecated: Use helpers.tools.DBRowToStruct.
func rowsToSale(rows *sqlx.Rows) (*Sale, error) {
	var sale Sale
	for rows.Next() {
		var doc []byte
		err := rows.Scan(&sale.Sale, &sale.SaleNumber, &doc, &sale.CreatedAt, &sale.UpdatedAt, &sale.CreatedBy, &sale.UpdatedBy, &sale.EndAt, &sale.Suspended)
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(doc, &sale.Document)
		if err != nil {
			return nil, err
		}
	}
	return &sale, nil
}

// Deprecated: Use helpers.tools.DBRowToStruct.
func rowsToSales(rows *sqlx.Rows) (*[]Sale, error) {
	var result []Sale
	for rows.Next() {
		var sale Sale
		var doc []byte
		err := rows.Scan(&sale.Sale, &sale.SaleNumber, &doc, &sale.CreatedAt, &sale.UpdatedAt, &sale.CreatedBy, &sale.UpdatedBy, &sale.EndAt, &sale.Suspended)
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(doc, &sale.Document)
		if err != nil {
			return nil, err
		}
		result = append(result, sale)
	}
	return &result, nil
}

func calculateSalesTax(price int, taxRate float64) float64 {
	return (float64(price) * ((taxRate / 100.0) / 100.0)) * 100.0
}

func ActiveTenders(sale *Sale) []*SaleTender {
	var activeTenders []*SaleTender
	for i := range sale.Document.SaleHeader.Tenders {
		tender := &sale.Document.SaleHeader.Tenders[i]
		if !slices.Contains(tender.SaleTenderFlags, int(STF_VOIDED)) {
			activeTenders = append(activeTenders, tender)
		}
	}
	return activeTenders
}

func ActiveSaleRows(sale *Sale) []*SaleRow {
	var activeSaleRows []*SaleRow
	for i := range sale.Document.SaleRows {
		row := &sale.Document.SaleRows[i]
		if !slices.Contains(row.Flags, int(SR_VOIDED)) {
			activeSaleRows = append(activeSaleRows, row)
		}
	}
	return activeSaleRows
}

// Original logic: https://github.com/Round2POS/hyperion/blob/7860ea1210bcbe37ef2862cc6e1b2b27b8909e23/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L508
func IsCashPrice(media PaymentMediaType) bool {
	return media != PMT_CREDIT && media != PMT_DEBIT
}

// Original logic: https://github.com/Round2POS/hyperion/blob/7860ea1210bcbe37ef2862cc6e1b2b27b8909e23/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L493C12-L493C32
func IsDualPricingEnabled(sale *Sale, merchantDualPricing bool) bool {
	if !merchantDualPricing {
		return false
	}
	for _, tenderPtr := range ActiveTenders(sale) {
		if tenderPtr.Media == nil {
			continue
		}
		media := PaymentMediaType(*tenderPtr.Media)
		if !IsCashPrice(media) {
			return false
		}
	}
	return true
}

func Clone[T any](v *T) (*T, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return nil, fmt.Errorf("error marshalling sale, %w", err)
	}

	var clone T
	err = json.Unmarshal(bytes, &clone)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling sale, %w", err)
	}
	return &clone, nil
}

// Returns the root row of a given row, by following SaleRow.parent until it reaches a row with no parent.
//
// If the parent of a row is not found, the row itself is returned.
func getRootRow(saleRow *SaleRow, allSaleRows []SaleRow) *SaleRow {
	if saleRow.Parent < 0 {
		return saleRow
	}
	for i := range allSaleRows {
		row := &allSaleRows[i]
		if row.Index == saleRow.Parent {
			return getRootRow(row, allSaleRows)
		}
	}
	return saleRow
}

// Returns a list of all descendant rows of a given row, including the row itself.
func getDescendantRows(saleRow *SaleRow, allSaleRows []SaleRow) []*SaleRow {
	descendants := []*SaleRow{saleRow}
	for i := range allSaleRows {
		row := &allSaleRows[i]
		if row.Parent == saleRow.Index {
			descendants = append(descendants, getDescendantRows(row, allSaleRows)...)
		}
	}
	return descendants
}

func roundToPlace(value float64, place int) (int, error) {
	str := strconv.FormatFloat(value, 'f', place, 64)
	value, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0, fmt.Errorf("error parsing float, %w", err)
	}

	return int(math.Round(value)), nil
}

// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/widgets/sale/controller.dart#L1611
// Original logic: https://github.com/Round2POS/hyperion/blob/4e89552c20c5d20182819cf5fa896d838a577b90/terminal/gui/front-of-house/lib/helpers/helpers.dart#L133
//
// This method will fix saleHeader.saleRows after it has been modified, after
// row(s) has been removed. If the removed row(s) has children remaining in the
// list, they will be removed as well. The SaleRow.{index,parent,hasChildren}
// fields of the rows are fixed.
//
// Note: this does not modify the list in place, it returns a new list.
//
//nolint:gocognit,funlen // this function is long, but we like that kind of thing.
func FixedSaleRows(saleRows []SaleRow, issues *[]string) ([]SaleRow, error) {
	if issues == nil {
		issues = &[]string{}
	}

	fixed := []SaleRow{}
	for i := range saleRows {
		row := &saleRows[i]
		clone, err := Clone(row)
		if err != nil {
			return nil, fmt.Errorf("error cloning sale row, %w", err)
		}

		fixed = append(fixed, *clone)
	}

	// We call row.index a "name" because it might not match the actual index in the list when this function is called.
	//
	// This map is used to find the index of a row by its name.
	name2ChildrenName := map[int][]int{}
	name2Idx := map[int]int{}

	// Find all the "names" and store them in the map.
	for idx := range fixed {
		row := &fixed[idx]
		if row.Index != idx {
			*issues = append(*issues, "row $index has row.Index ${row.index}, which is not the same as the actual index")
		}

		if _, alreadyExists := name2Idx[row.Index]; alreadyExists {
			*issues = append(*issues, "row $index has a duplicate index")
		}

		name2Idx[row.Index] = idx

		if row.Parent >= 0 {
			name2ChildrenName[row.Parent] = append(name2ChildrenName[row.Parent], row.Index)
		}
	}
	// Find all the rows that need to be removed, because their parents are not in the list. We will remove them later, and all their descendants.
	danglingRowNames := make(map[int]struct{})

	// Find dangling nodes.
	for i := range fixed {
		row := &fixed[i]
		// This node never had a parent.
		if row.Parent == -1 {
			continue
		}

		if _, exists := name2Idx[row.Parent]; !exists {
			for _, descendantPtr := range getDescendantRows(row, fixed) {
				danglingRowNames[descendantPtr.Index] = struct{}{}
			}
		}
	}

	// Get rid of rows that need to be completely removed, IOW, anything that is dangling.
	fixed = func(rows []SaleRow, danglingRowNames map[int]struct{}) []SaleRow {
		filtered := []SaleRow{}
		for i := range rows {
			row := &rows[i]
			if _, exists := danglingRowNames[row.Index]; exists {
				continue
			}
			filtered = append(filtered, *row)
		}
		return filtered
	}(fixed, danglingRowNames)

	// Now that everything is removed, the correct row.index can be computed from its actual index in the list.
	oldName2FixedIndex := func(fixed []SaleRow) map[int]int {
		result := map[int]int{}
		for idx := range fixed {
			row := &fixed[idx]
			if row.Index != idx {
				*issues = append(*issues, fmt.Sprintf("row %v has row.Index %v", idx, row.Index))
			}

			row.Index = idx
			result[row.Index] = idx
		}
		return result
	}(fixed)

	// Now that everything is removed, the correct row.parent can be computed from its actual index in the list.
	func(fixed []SaleRow, oldName2FixedIndex map[int]int) {
		for idx := range fixed {
			row := &fixed[idx]
			if row.Parent >= 0 {
				fixedParent := oldName2FixedIndex[row.Parent]
				if row.Parent != fixedParent {
					*issues = append(*issues, fmt.Sprintf("row %v has parent %v which has been fixed to %v", idx, row.Parent, fixedParent))
				}
				row.Parent = fixedParent
			}
		}
	}(fixed, oldName2FixedIndex)

	// We need to compute SaleRow.hasChildren, so we need to know which rows have children.
	// {row indices}
	row2HasChildren := make(map[int]struct{})
	for idx := range fixed {
		row := &fixed[idx]
		if row.Parent >= 0 {
			row2HasChildren[row.Parent] = struct{}{}
		}
	}

	// Now set SaleRow.hasChildren for all rows.
	for idx := range fixed {
		row := &fixed[idx]
		_, rowHasChildren := row2HasChildren[idx]
		if rowHasChildren != row.HasChildren {
			*issues = append(*issues, fmt.Sprintf("row %v has row.hasChildren %v but children %v", idx, row.HasChildren, rowHasChildren))
		}
		row.HasChildren = rowHasChildren
	}

	return fixed, nil
}

// Remove an element from a slice. Does not modify the original slice. Returns a new slice that does not contain the element.
//
// All matching elements are removed.
//
// Element is matched by comparing with `==` (not deep equal), hence "shallow".
func RemovedShallow[T comparable](slice []T, search T) []T {
	result := []T{}
	for _, value := range slice {
		if value != search {
			result = append(result, value)
		}
	}
	return result
}

type enum interface {
	~int
	String() string
}

// Convenience method to {set,unset} a flag and warn if something had to be changed.
func SetFlag[T enum](flags *[]int, flag T, set bool, issues *[]string) {
	name := flag.String()
	flagValue := int(flag)

	//nolint:nestif // this is fine.
	if set {
		if !slices.Contains(*flags, flagValue) {
			*flags = append(*flags, flagValue)
			if issues != nil {
				*issues = append(*issues, fmt.Sprintf("setting flag %v", name))
			}
		}
	} else {
		if slices.Contains(*flags, flagValue) {
			*flags = RemovedShallow(*flags, flagValue)
			if issues != nil {
				*issues = append(*issues, fmt.Sprintf("unsetting flag %v", name))
			}
		}
	}
}

// Sort and remove duplicate flags.
func CanonicalizeFlags(flags *[]int) {
	slices.Sort(*flags)
	j := 0

	for i := 1; i < len(*flags); i++ {
		inputFlag := (*flags)[i]
		lastOutputFlag := (*flags)[j]
		if inputFlag == lastOutputFlag {
			continue
		}

		j++
		(*flags)[j] = inputFlag
	}
}

func getOverallCashBase(rootSaleRow *SaleRow, sale *Sale) int {
	overallCashBase := 0
	for _, descendant := range getDescendantRows(rootSaleRow, sale.Document.SaleRows) {
		overallCashBase += descendant.CashBasePrice
	}
	return overallCashBase
}

func getOverallCreditBase(rootSaleRow *SaleRow, sale *Sale) int {
	overallCreditBase := 0
	for _, descendant := range getDescendantRows(rootSaleRow, sale.Document.SaleRows) {
		overallCreditBase += descendant.BasePrice
	}
	return overallCreditBase
}

// {checks,computes} invariants for a sale, things that are non-normalized
// (redundant) and instead computed, and stored e.g in the saleHeader, from the
// e.g saleRows or tenders.
//
// NOTE: This function does not modify the sale in place, it returns a new sale.
//
// Keep in sync with Dart: https://github.com/Round2POS/hyperion/blob/4e89552c20c5d20182819cf5fa896d838a577b90/terminal/gui/front-of-house/lib/helpers/helpers.dart#L422
//
//nolint:cyclop,gocyclo,gocognit,funlen // this function is long, and that's A-OK.
func CanonicalizedSale(sale *Sale, salesTaxList []record.Tax, gratuitySection *record.Section, merchantConfig *record.RecordDocumentSet, issues *[]string) (*Sale, error) {
	if issues == nil {
		issues = &[]string{}
	}

	fixed, err := Clone(sale)
	if err != nil {
		return nil, fmt.Errorf("error cloning sale, %w", err)
	}
	// Original logic: https://github.com/Round2POS/hyperion/blob/4e89552c20c5d20182819cf5fa896d838a577b90/terminal/gui/front-of-house/lib/helpers/helpers.dart#L364

	// TODO: Check seatCnt,toGoSeatCnt against seatsTendered, seatsSettled, seatNumber.

	// REFUNDED flag.
	{
		// Original logic: https://github.com/Round2POS/hyperion/blob/15e3058a48e4e886332682065f9d64af528433d4/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1199
		// final bool hasNegativeSubtotal = copy.document.saleHeader.subTotal < 0;
		hasNegativeSubtotal := fixed.Document.SaleHeader.SubTotal < 0

		// Original logic: https://github.com/Round2POS/hyperion/blob/15e3058a48e4e886332682065f9d64af528433d4/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1301
		// Jordan, Blaine and Cole OKAYed not caring about media \in {CASH,CREDIT}.
		hasRefundedTender := false
		for _, tenderPtr := range ActiveTenders(fixed) {
			if slices.Contains(tenderPtr.SaleTenderFlags, int(STF_REFUNDED)) {
				hasRefundedTender = true
				break
			}
		}

		// Cole: For complex reasons, sometimes a Refunded Sale is constructed, and doesn't end up with a negative sale, so we need to make the REFUNDED
		// flag sticky: Once it is set on a sale, it stays, even though there is no hint that it is a REFUNDED sale aside from the flag.
		//
		// Therefore: REFUNDED is a sticky computed flag.
		alreadyHasRefundFlag := slices.Contains(fixed.Document.SaleHeader.SaleFlags, int(SF_REFUNDED))

		shouldHaveRefundedFlag := alreadyHasRefundFlag || hasNegativeSubtotal || hasRefundedTender
		SetFlag(&fixed.Document.SaleHeader.SaleFlags, SF_REFUNDED, shouldHaveRefundedFlag, issues)
	}

	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/dialogs/tender/new.controller.dart#L1309
	shouldHaveGiftRedemptionFlag := false
	for _, tenderPtr := range ActiveTenders(fixed) {
		if tenderPtr.Media != nil && *tenderPtr.Media == int(PMT_GIFT) {
			shouldHaveGiftRedemptionFlag = true
			break
		}
	}
	SetFlag(&fixed.Document.SaleHeader.SaleFlags, SF_GIFT_REDEMPTION, shouldHaveGiftRedemptionFlag, issues)

	// Original logic: https://github.com/Round2POS/hyperion/blob/b65d787e821450fdb1fc342df9fcddabeb3d0038/terminal/gui/front-of-house/lib/app/modules/register/widgets/sale/controller.dart#L2328
	// Original logic: https://github.com/Round2POS/hyperion/blob/4e89552c20c5d20182819cf5fa896d838a577b90/terminal/gui/front-of-house/lib/helpers/helpers.dart#L500

	newTotal := 0
	cashNewTotal := 0
	takeOutSurchargeTotal := 0
	taxSubtotals := []int{}
	taxSubtotalsAsDouble := []float64{}
	cashTaxSubtotals := []int{}
	cashTaxSubtotalsAsDouble := []float64{}

	// Filter out voided rows
	activeSaleRows := ActiveSaleRows(fixed)

	// Check if any row is tax-forgiven
	taxForgiven := false
	for i := range activeSaleRows {
		saleRowPtr := activeSaleRows[i]
		if slices.Contains(saleRowPtr.Flags, int(SR_FORGIVE_TAX)) {
			taxForgiven = true
			break
		}
	}

	// Check if there are any tax forgiven rows and adjust the sale header accoringly
	SetFlag(&sale.Document.SaleHeader.SaleFlags, SF_TAX_FORGIVEN, taxForgiven, issues)

	for _, saleRowPtr := range activeSaleRows {
		rootSaleRowPtr := getRootRow(saleRowPtr, fixed.Document.SaleRows)
		qty := float64(saleRowPtr.Qty)
		if rootSaleRowPtr.SplitData != nil {
			qty = float64(saleRowPtr.Qty) / float64(rootSaleRowPtr.SplitData.Qty)
		}

		saleRowPtr.CreditPrice = int(math.Round(float64(qty) * float64(saleRowPtr.BasePrice)))
		saleRowPtr.CashPrice = int(math.Round(float64(qty) * float64(saleRowPtr.CashBasePrice)))

		if rootSaleRowPtr.SplitData != nil && slices.Contains(saleRowPtr.Flags, int(SR_SPLIT_PARENT)) {
			originalTotalCredit := saleRowPtr.BasePrice * saleRowPtr.Qty
			originalTotalCash := saleRowPtr.CashBasePrice * saleRowPtr.Qty
			remainderCredit := (saleRowPtr.CreditPrice * rootSaleRowPtr.SplitData.Qty) - originalTotalCredit
			remainderCash := (saleRowPtr.CashPrice * rootSaleRowPtr.SplitData.Qty) - originalTotalCash
			saleRowPtr.CreditPrice -= remainderCredit
			saleRowPtr.CashPrice -= remainderCash
		}

		if slices.Contains(fixed.Document.SaleHeader.SaleFlags, int(SF_DUAL_PRICING)) {
			saleRowPtr.ActualPrice = saleRowPtr.CashPrice
		} else {
			saleRowPtr.ActualPrice = saleRowPtr.CreditPrice
		}

		newTotal += saleRowPtr.CreditPrice
		cashNewTotal += saleRowPtr.CashPrice

		itemDiscountTotal := 0
		cashItemDiscountTotal := 0
		for _, discount := range saleRowPtr.Discounts {
			itemDiscountTotal += discount.Value
			cashItemDiscountTotal += discount.CashValue
		}

		isRoot := saleRowPtr.Parent < 0
		isRowToGo := saleRowPtr.SeatNumber < 0
		isFixedToGo := fixed.Document.SaleHeader.OrderType == int(OT_TAKEOUT)
		isToGo := isRowToGo || isFixedToGo
		descendants := getDescendantRows(saleRowPtr, fixed.Document.SaleRows)
		takeoutSurcharge := false
		if isRoot && isToGo {
			for _, descPtr := range descendants {
				if descPtr.TakeOutSurcharge && !slices.Contains(descPtr.Flags, int(SR_VOIDED)) {
					takeoutSurcharge = true
					break
				}
			}
		}

		if isRoot {
			overallCreditBase := getOverallCreditBase(saleRowPtr, fixed)
			overallCashBase := getOverallCashBase(saleRowPtr, fixed)
			isRefunded := overallCashBase < 0 || overallCreditBase < 0
			for _, descendantPtr := range descendants {
				SetFlag(&descendantPtr.Flags, SR_REFUNDED, isRefunded, issues)
			}
		}

		// If takeout surcharge applies update salerow surcharge amount and add to surcharge total
		if takeoutSurcharge {
			saleRowPtr.TakeOutSurchargeAmt = int(math.Ceil(float64(merchantConfig.Merchant.TakeOutSurchargeAmt) * float64(qty)))
			if saleRowPtr.BasePrice < 0 {
				saleRowPtr.TakeOutSurchargeAmt *= -1
			}
			takeOutSurchargeTotal += saleRowPtr.TakeOutSurchargeAmt
		} else {
			saleRowPtr.TakeOutSurchargeAmt = 0
		}

		/*
		 * JOE CHANGED THIS AGAIN FOR ZERO PRICED ITEMS AND TO FIX ROUNDING
		 * ERROR THAT LEADS TO INCORRECT GROSS PRICE SOMETIMES.
		 */

		// Use a temp originalPrice to replace 0 price random weight barcode pricing
		originalPrice := saleRowPtr.BasePrice + itemDiscountTotal
		cashOriginalPrice := saleRowPtr.CashBasePrice + cashItemDiscountTotal
		saleRowPtr.GrossPrice = int(math.Round(float64(originalPrice) * float64(qty)))
		saleRowPtr.CashGrossPrice = int(math.Round(float64(cashOriginalPrice) * float64(qty)))

		if rootSaleRowPtr.SplitData != nil && slices.Contains(saleRowPtr.Flags, int(SR_SPLIT_PARENT)) {
			originalGrossCredit := originalPrice * saleRowPtr.Qty
			originalGrossCash := cashOriginalPrice * saleRowPtr.Qty
			remainderGrossCredit := (saleRowPtr.GrossPrice * rootSaleRowPtr.SplitData.Qty) - originalGrossCredit
			remainderGrossCash := (saleRowPtr.CashGrossPrice * rootSaleRowPtr.SplitData.Qty) - originalGrossCash
			saleRowPtr.GrossPrice -= remainderGrossCredit
			saleRowPtr.CashGrossPrice -= remainderGrossCash
		}

		/*
		 * END JOE
		 */

		// Loop through salesTaxList length for all possible taxes
		if slices.Contains(saleRowPtr.Flags, int(SR_TAXABLE)) {
			//nolint:intrange // false positive.
			for taxIdx := 0; taxIdx < len(salesTaxList); taxIdx++ {
				checkDigit := 1 << taxIdx
				// If the current index doesnt exist on the subtotal list, create it
				tax := &salesTaxList[taxIdx]

				// The following lines were part of the original code, but were commented out
				// because of tax rounding errors
				// if (taxSubtotals.length == i) {
				// taxSubtotals.add(0);
				// cashTaxSubtotals.add(0);
				//}

				for len(taxSubtotalsAsDouble) <= taxIdx {
					taxSubtotalsAsDouble = append(taxSubtotalsAsDouble, 0)
					cashTaxSubtotalsAsDouble = append(cashTaxSubtotalsAsDouble, 0)
				}

				isTakeout := (fixed.Document.SaleHeader.OrderType == int(OT_TAKEOUT)) || (saleRowPtr.SeatNumber < 0)
				shouldForgiveTakeout := false
				if tax.ForgiveTakeout != nil {
					shouldForgiveTakeout = isTakeout && *tax.ForgiveTakeout
				}

				// If the current tax is enabled, calculate it and add it to the subtotals array
				if (saleRowPtr.TaxFlags&checkDigit == checkDigit) && !shouldForgiveTakeout {
					taxPercent := tax.TaxPercent
					if taxPercent != nil {
						taxRate := float64(*taxPercent) / 10000.0
						taxSubtotalsAsDouble[taxIdx] += calculateSalesTax(int(math.Round(float64(saleRowPtr.BasePrice)*float64(qty))), taxRate)
						cashTaxSubtotalsAsDouble[taxIdx] += calculateSalesTax(int(math.Round(float64(saleRowPtr.CashBasePrice)*float64(qty))), taxRate)
					}
				}
			}
		}
	}

	// Add all the subtotals together to get one tax total
	taxableTotal := 0
	cashTaxableTotal := 0

	//nolint:intrange // false positive.
	for i := 0; i < len(taxSubtotalsAsDouble); i++ {
		// Old code that caused rounding errors in tax calculations
		// for (int i = 0; i < taxSubtotals.length; i++) {
		//   taxableTotal += taxSubtotals[i];
		//   cashTaxableTotal += cashTaxSubtotals[i];
		// }
		//nolint:govet // false positive, err is being handled.
		rounded, err := roundToPlace(taxSubtotalsAsDouble[i], 3)
		if err != nil {
			return nil, fmt.Errorf("error rounding tax subtotals, %w", err)
		}
		roundedCash, err := roundToPlace(cashTaxSubtotalsAsDouble[i], 3)
		if err != nil {
			return nil, fmt.Errorf("error rounding cash tax subtotals, %w", err)
		}

		// set tax totals for traditional or card prices
		taxableTotal += rounded
		taxSubtotals = append(taxSubtotals, rounded)

		// set cash price tax totals
		cashTaxableTotal += roundedCash
		cashTaxSubtotals = append(cashTaxSubtotals, roundedCash)
	}

	// Calculate gratuity
	sectionGrat := false
	if gratuitySection != nil && gratuitySection.GratAmt > 0 {
		minGratCustCnt := 1
		if gratuitySection.MinGratCustCnt != nil {
			minGratCustCnt = *gratuitySection.MinGratCustCnt
		}
		sectionGrat = minGratCustCnt <= fixed.Document.SaleHeader.CustomerCount
	}
	addedGrat := 0.0
	if fixed.Document.SaleHeader.AddedGratuity != nil {
		addedGrat = *fixed.Document.SaleHeader.AddedGratuity
	}

	if sectionGrat || addedGrat > 0 {
		calcTotal := newTotal
		cashCalcTotal := cashNewTotal
		if gratuitySection.CalcGratWDiscs == nil || !*gratuitySection.CalcGratWDiscs {
			calcTotal = newTotal - fixed.Document.SaleHeader.DiscountTotal
			cashCalcTotal = cashNewTotal - fixed.Document.SaleHeader.CashDiscountTotal
		}

		percentToUse := 0.0
		if sectionGrat {
			percentToUse += gratuitySection.GratAmt
		}
		percentToUse += addedGrat

		fixed.Document.SaleHeader.GratuityTotal = int(math.Round((percentToUse / 100.0) * float64(calcTotal)))
		fixed.Document.SaleHeader.CashGratuityTotal = int(math.Round((percentToUse / 100.0) * float64(cashCalcTotal)))
		fixed.Document.SaleHeader.GratuityPercent = percentToUse
	} else {
		fixed.Document.SaleHeader.GratuityPercent = 0
		fixed.Document.SaleHeader.GratuityTotal = 0
		fixed.Document.SaleHeader.CashGratuityTotal = 0
	}

	fixed.Document.SaleHeader.SubTotal = newTotal
	fixed.Document.SaleHeader.CashSubTotal = cashNewTotal
	fixed.Document.SaleHeader.TaxTotals = taxSubtotals
	fixed.Document.SaleHeader.CashTaxTotals = cashTaxSubtotals
	fixed.Document.SaleHeader.TaxTotal = taxableTotal
	fixed.Document.SaleHeader.CashTaxTotal = cashTaxableTotal
	fixed.Document.SaleHeader.TakeOutSurchargeTotal = takeOutSurchargeTotal
	fixed.Document.SaleHeader.Total = fixed.Document.SaleHeader.SubTotal +
		fixed.Document.SaleHeader.GratuityTotal +
		fixed.Document.SaleHeader.TaxTotal +
		fixed.Document.SaleHeader.TakeOutSurchargeTotal +
		fixed.Document.SaleHeader.DeliveryFee

	fixed.Document.SaleHeader.CashTotal = fixed.Document.SaleHeader.CashSubTotal +
		fixed.Document.SaleHeader.CashGratuityTotal +
		fixed.Document.SaleHeader.CashTaxTotal +
		fixed.Document.SaleHeader.TakeOutSurchargeTotal +
		fixed.Document.SaleHeader.DeliveryFee

	fixed.Document.SaleHeader.DualPricingAmount = fixed.Document.SaleHeader.SubTotal - fixed.Document.SaleHeader.CashSubTotal

	// Now canonicalize all the flags.
	CanonicalizeFlags(&fixed.Document.SaleHeader.SaleFlags)

	for i := range fixed.Document.SaleRows {
		srPtr := &fixed.Document.SaleRows[i]
		CanonicalizeFlags(&srPtr.Flags)
	}

	for i := range fixed.Document.SaleHeader.Tenders {
		stPtr := &fixed.Document.SaleHeader.Tenders[i]
		CanonicalizeFlags(&stPtr.SaleTenderFlags)
	}

	if fixed.SaleNumber != fixed.Document.SaleHeader.SaleNumber {
		*issues = append(*issues, fmt.Sprintf("sale number %v does not match saleHeader.saleNumber %v", fixed.SaleNumber, fixed.Document.SaleHeader.SaleNumber))
	}

	if isEqual, err := fixed.Equal(sale); err != nil {
		return nil, fmt.Errorf("error comparing sale view models, %w", err)
	} else if !isEqual {
		log.Warn().
			Any("sale", sale).
			Any("fixed", fixed).
			Msg("sale view models are not equal to the expected values")
		*issues = append(*issues, "sale view models are not equal to the expected values")
	}

	return fixed, nil
}

func CheckSale(sale *Sale, salesTaxList []record.Tax, gratuitySection *record.Section, merchantConfig *record.RecordDocumentSet) error {
	issues := []string{}
	_, err := CanonicalizedSale(sale, salesTaxList, gratuitySection, merchantConfig, &issues)
	if err != nil {
		return fmt.Errorf("error checking sale, error computing sale %w", err)
	}

	if len(issues) > 0 {
		return fmt.Errorf("error checking sale, issues found: %v", issues[0])
	}

	return nil
}

// Computes if Sale is eligible for EBT based on computed flags.
func GetEBTFailures(tenderGroupSale *Sale) []string {
	failures := []string{}
	for _, saleRow := range tenderGroupSale.Document.SaleRows {
		if !slices.Contains(saleRow.Flags, int(SR_ALLOW_EBT)) {
			failures = append(failures, saleRow.ReceiptDescription)
		}
	}

	return failures
}
