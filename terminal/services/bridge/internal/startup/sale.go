package startup

import (
	"context"
	"os"

	"github.com/jordanlumley/gobalena"
	"github.com/r2pos/bridge/features/database"
	"github.com/r2pos/bridge/internal/config"
	"github.com/rs/zerolog/log"
)

func CheckEnvToResetSaleNumber(
	c *config.Config,
	dbService database.PostgresService,
	balenaCloudClient gobalena.CloudClient,
) {
	child := log.With().Logger()

	ctx := context.Background()

	log.Info().Msg("checking if sales number to be reset")

	resetSaleNumber := os.Getenv("RESET_SALE_NUMBER")
	if resetSaleNumber == "" {
		child.Info().Msg("RESET_SALE_NUMBER environment variable not set")
		return
	}

	if resetSaleNumber == "false" {
		child.Info().Str("RESET_SALE_NUMBER", resetSaleNumber).
			Msg("skipping reset sale number")
		return
	}

	if resetSaleNumber != "true" {
		child.Info().Str("RESET_SALE_NUMBER", resetSaleNumber).
			Msg(`environment variable value is invalid. Must be "true" or "false"`)
		return
	}

	balenaDevice, err := balenaCloudClient.GetDevice(
		ctx, c.BalenaDeviceUUID,
	)
	if err != nil {
		child.Error().Err(err).Msg("error getting balena device")
		return
	}

	balenaDeviceID := balenaDevice.ID

	child.Debug().
		Int("balenaDeviceID", balenaDeviceID).
		Msg("balena device ID")

	envVarID, err := balenaCloudClient.GetDeviceEnvVarID(
		ctx, balenaDeviceID, "RESET_SALE_NUMBER",
	)
	if err != nil {
		child.Error().
			Err(err).
			Msg("error getting environment variable ID")
		return
	}

	child.Debug().
		Int("envVarID", envVarID).
		Msg("got environment variable ID")

	err = dbService.ResetSaleNumber(ctx)
	if err != nil {
		child.Error().Err(err).Msg("error resetting sales data")
		return
	}

	child.Info().Msg("resetting sale number complete")

	err = balenaCloudClient.UpdateDeviceEnvVar(
		ctx, balenaDeviceID, envVarID, "false",
	)
	if err != nil {
		child.Error().Err(err).Msg("error updating environment variable")
		return
	}
}
