package internal

import (
	"fmt"
	"os"
	"strings"

	"github.com/rs/zerolog/log"
)

type version struct {
	SemVer string
	Fork   string
}

func parseVersion(versionFileContents string) (*version, error) {
	lines := strings.Split(versionFileContents, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("invalid version file, expected 2 lines, got %d", len(lines))
	}

	semVer := strings.TrimSpace(lines[0])
	fork := strings.TrimSpace(lines[1])

	return &version{SemVer: semVer, Fork: fork}, nil
}

func GetVersionSemVerOrDie() string {

	if _, err := os.Stat("../../VERSION"); !os.IsNotExist(err) {
		versionFileContents, err := os.ReadFile("../../VERSION")
		if err != nil {
			log.Fatal().Err(err).Msgf("error reading version file: %s", err)
		}
		version, err := parseVersion(string(versionFileContents))
		if err != nil {
			log.Fatal().Err(err).Msgf("error parsing version file: %s", err)
		}
		return version.SemVer
	}

	// otherwise, if "/opt/r2pos/VERSION" exists, use it
	if _, err := os.Stat("/opt/r2pos/VERSION"); !os.IsNotExist(err) {
		versionFileContents, err := os.ReadFile("/opt/r2pos/VERSION")
		if err != nil {
			log.Fatal().Err(err).Msgf("error reading version file: %s", err)
		}
		version, err := parseVersion(string(versionFileContents))
		if err != nil {
			log.Fatal().Err(err).Msgf("error parsing version file: %s", err)
		}
		return version.SemVer
	}

	log.Fatal().Msg("version file not found")
	return ""
}
