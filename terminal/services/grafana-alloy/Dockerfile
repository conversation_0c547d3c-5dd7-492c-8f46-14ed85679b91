FROM grafana/alloy:v1.7.5



RUN rm -rf /etc/alloy/config.alloy
COPY ./grafana-alloy-config.alloy /etc/alloy/config.alloy

# Test:
#
# docker build . -t grafana-alloy-local
#
# reset && docker run --privileged -e DEVICE_ID=realz-laptop \
#   -e PROMETHEUS_TARGET=${PROMETHEUS_TARGET} \
#   -e LOKI_TARGET=${LOKI_TARGET} \
#   grafana-alloy-local \
#   run \
#     --server.http.listen-addr=0.0.0.0:12345 \
#     --storage.path=/var/lib/alloy/data \
#     /etc/alloy/config.alloy
