

logging {
	level = "warn"
}


discovery.relabel "metrics_default_bridge_service" {
	targets = [{
		__address__ = "localhost:8880",
	}]

	rule {
		source_labels = ["__address__"]
		target_label  = "instance"
		replacement   = sys.env("BALENA_DEVICE_UUID") + ":8880"
	}
}

discovery.relabel "metrics_default_grafana_alloy" {
	targets = [{
		__address__ = "localhost:12345",
	}]

	rule {
		source_labels = ["__address__"]
		target_label  = "instance"
		replacement   = sys.env("BALENA_DEVICE_UUID") + ":12345"
	}
}

discovery.relabel "metrics_default_node_exporter" {
	targets = [{
		__address__ = "localhost:19100",
	}]

	rule {
		source_labels = ["__address__"]
		target_label  = "instance"
		replacement   = sys.env("BALENA_DEVICE_UUID") + ":19100"
	}
}


prometheus.scrape "metrics_default_bridge_service" {
	targets         = discovery.relabel.metrics_default_bridge_service.output
	forward_to      = [prometheus.remote_write.metrics_default.receiver]
	job_name        = "bridge-service"
	scrape_interval = "15s"
}

prometheus.scrape "metrics_default_grafana_alloy" {
	targets         = discovery.relabel.metrics_default_grafana_alloy.output
	forward_to      = [prometheus.remote_write.metrics_default.receiver]
	job_name        = "grafana-alloy"
	scrape_interval = "15s"
}

prometheus.scrape "metrics_default_node_exporter" {
	targets         = discovery.relabel.metrics_default_node_exporter.output
	forward_to      = [prometheus.remote_write.metrics_default.receiver]
	job_name        = "node-exporter"
	scrape_interval = "15s"
}


prometheus.remote_write "metrics_default" {
	external_labels = {
		device_id    = sys.env("DEVICE_ID"),
		balena_device_id    = sys.env("BALENA_DEVICE_UUID"),
		store_id = sys.env("STORE_ID"),
	}

	endpoint {
		name = "default-5ae485"
		url  = sys.env("PROMETHEUS_TARGET")

		basic_auth {
			username = "staging"
			password = "hand-sanitizer"
		}

		queue_config { }

		metadata_config { }
	}
}

// discovery.docker "logs_integrations_docker" {
//   host = sys.env("DOCKER_HOST")
// 	refresh_interval = "5s"
// }

// discovery.relabel "logs_integrations_docker" {
// 	targets = []


// 	rule {
// 			target_label = "job"
// 			replacement = "integrations/docker"
// 	}


// 	rule {
// 			target_label = "instance"
// 			replacement = sys.env("BALENA_DEVICE_UUID")
// 	}


// 	rule {
// 			source_labels = ["__meta_docker_container_name"]
// 			regex = "/(.*)"
// 			target_label = "container"
// 	}


// 	rule {
// 			source_labels = ["__meta_docker_container_id"]
// 			regex = "/(.*)"
// 			target_label = "container_id"
// 	}


// 	rule {
// 			source_labels = ["__meta_docker_container_log_stream"]
// 			target_label = "stream"
// 	}
// }

// loki.source.docker "logs_integrations_docker" {
//   host       = sys.env("DOCKER_HOST")
//   targets    = discovery.docker.logs_integrations_docker.targets
//   // labels     = {"app" = "docker"}
// 	labels = {
// 		device_id    = sys.env("DEVICE_ID"),
// 		balena_device_id    = sys.env("BALENA_DEVICE_UUID"),
// 		store_id = sys.env("STORE_ID"),
// 	}
// 	relabel_rules = discovery.relabel.logs_integrations_docker.rules
//   forward_to = [loki.relabel.docker_relabel.receiver]
// }

// loki.relabel "docker_relabel" {
// 	// rule {
// 	// 	source_labels = ["__meta_docker_container_name"]
// 	// 	target_label  = "container"
// 	// }
// 	// rule {
// 	// 	source_labels = ["__meta_docker_container_image"]
// 	// 	target_label  = "image"
// 	// }
//   forward_to = [loki.write.remote_loki.receiver]
// }

loki.write "remote_loki" {

  endpoint {
    url = sys.env("LOKI_TARGET")
  }
}
