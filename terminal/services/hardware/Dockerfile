# Note: this entire builder-stage is an embedding of
# <common/hardware-builder/1.0.0/Dockerfile>.
#
# The reason we are just dumping that stage here is because we needed to update
# buster to bullseye, but we did not want to bother with a push to dockerhub.
#
# However, this will slow down the build process a bit, as the stage is no
# longer prebuilt.
#
# Note: We had to update the base image from buster (10) to bullseye (11),
# because buster went defunct.
#
# However, bookworm (12) causes all sorts of pandemonium with the
# hardware-service, so to mitigate, we are going to stick with bullseye (11)
# for now.
#
# See: [R2P-1007: Debian Buster is Busted](https://ces1.atlassian.net/browse/R2P-1007)
# for more details.
FROM balenalib/genericx86-64-ext-debian:bullseye as hardware-builder

ENV HOME=/home \
    DEBIAN_FRONTEND=noninteractive

# pre reqs
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
    clang \
    make \
    cmake \
    pkg-config \
    libgtk-3-dev \
    libpq-dev \
    postgresql-client \
    ca-certificates \
    gcc \
    libusb-1.0-0-dev \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && git clone https://github.com/json-c/json-c.git \
    && cd json-c \
    && git checkout json-c-0.15-20200726 \
    && cd .. \
    && mkdir json-c-build \
    && cd json-c-build \
    && cmake ../json-c \
    && make \
    && make test \
    && make install \
    && rm -rf json-c-build \
    && rm -rf json-c \
    && mkdir /opt/r2pos \
    && mkdir /opt/r2pos/bin \
    && mkdir /opt/r2pos/data \
    && mkdir /opt/r2pos/logfiles

FROM hardware-builder as builder

WORKDIR /home/<USER>

COPY . .

WORKDIR /home/<USER>/libR2
RUN make clean && make
RUN cp ./libR2.so /usr/lib/libR2.so && chmod 0755 /usr/lib/libR2.so && ldconfig

WORKDIR /home/<USER>/R2BrecknellScale/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2BrecknellScale /opt/r2pos/bin

WORKDIR /home/<USER>/R2BergDriver/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2BergDriver /opt/r2pos/bin

WORKDIR /home/<USER>/R2DataLogicScale/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2DataLogicScale /opt/r2pos/bin

WORKDIR /home/<USER>/R2DayEnd
RUN make clean && make 
RUN cp ./R2DayEnd /opt/r2pos/bin

WORKDIR /home/<USER>/R2Helper/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2Helper /opt/r2pos/bin

WORKDIR /home/<USER>/R2PoleDisplay/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2PoleDisplay /opt/r2pos/bin

WORKDIR /home/<USER>/R2ProcMgr/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2ProcMgr /opt/r2pos/bin

WORKDIR /home/<USER>/R2SerialCashDrawer/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2SerialCashDrawer /opt/r2pos/bin

WORKDIR /home/<USER>/R2SettingsChanged/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2SettingsChanged /opt/r2pos/bin

WORKDIR /home/<USER>/R2ReportEngine/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2ReportEngine /opt/r2pos/bin

WORKDIR /home/<USER>/R2CASSerialScale/Debug
RUN make -f ../makefile clean && make -f ../makefile
RUN cp ./R2CASSerialScale /opt/r2pos/bin

RUN ldconfig


FROM balenalib/genericx86-64-ext-debian:bullseye

ENV HOME=/home \
    TZ=America/New_York \
    DEBIAN_FRONTEND=noninteractive

RUN apt-get update \
    && apt-get install --no-install-recommends -y \
    libpq-dev \
    procps \
    curl \
    tzdata \
    netcat-traditional \ 
    tini \
    libusb-1.0-0-dev \
    postgresql-client \
    arping \
    lm-sensors \
    ethtool \
    ntpsec-ntpdate \
    && rm -rf /var/lib/apt/lists/* 

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /home/<USER>

COPY --from=builder /usr/local/include/json-c/ /usr/local/include/json-c/
COPY --from=builder /usr/local/lib/libjson-c* /usr/local/lib/
COPY --from=builder /usr/local/lib/cmake/json-c/ /usr/local/lib/cmake/json-c/

COPY --from=builder /usr/lib/libR2.so /usr/lib/libR2.so
COPY --from=builder /opt/r2pos /opt/r2pos

RUN ldconfig

WORKDIR /usr/src
COPY entry.sh /usr/src
RUN chmod +x entry.sh

ENTRYPOINT [ "/usr/src/entry.sh" ]

CMD /opt/r2pos/bin/R2ProcMgr 2 && sleep infinity