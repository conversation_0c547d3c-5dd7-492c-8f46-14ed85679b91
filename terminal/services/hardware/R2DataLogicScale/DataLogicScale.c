////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This Module was created to support the DataLogic Magellan 9300i Scanner/Scale.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#include "R2DataLogicScale.h"
#include "Externs.h"


// Defines used only in this module
#define PE_BARCODE_LENGTH1	12
#define PE_BARCODE_LENGTH2	13
#define PE_BARCODE_START1	'2'
#define PE_BARCODE_START2	"02"


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function is the thread that handles Real-Time Requests to the Brecknell PS-USB scale.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void HandleDataLogicScale(void)
{
     struct sockaddr_in  client;

	char				szResponse[BUFFER_SIZE];
	char				szBuf[128];

	int				iClient;
	int				iSize;
	int				iRetVal;
	int				iReqNum;

     BOOL                bItemOK = TRUE;


	// Attempt to accept a connection for a weight request
	iSize = sizeof(client);
	if ((iClient = accept(iDataLogicSocket, (struct sockaddr *) &client, &iSize)) != -1)
	{
		// Since all you can do is Read (Poll) the Weight on the Scale, we don't really need a Request Packet
		// Poll the Scale and Return the Results
		memset(szResponse, '\0', sizeof(szResponse));
		PollDataLogicScale(szResponse);
		if(send(iClient, szResponse, strlen(szResponse) + 1, 0) < 0)
			R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, "Send Failed");

	     close(iClient);
	}

	// See if we have a barcode to send to the POS
	if (DLReadBarcode() || szDLBarcode[0])
	{
          // If the barcode isn't in the item table, make the scale beep
/*          bItemOK = IsValidItem(szDLBarcode);
          if (! bItemOK)
               BeepScale();*/

		// Attempt to send the barcode that was just scanned to the POS
		DLSendBarcode();
		memset(szDLBarcode, '\0', sizeof(szDLBarcode));

          // Need to re-enable the scale manually if we got a bad barcode
/*          if (! bItemOK)
          {
               sleep(1);
               EnableScanner();
          }*/
	}
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function polls the DataLogic Magellan 9300i scale and parses out the current reading.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void PollDataLogicScale(char *szResponse)
{
	// DataLogic Magellan only supports Pounds and Kilograms
	char		szBuf[MAX_PATH];
	char		szErrMsg[MAX_PATH];
	char		szWeight[16];

	int		iUOM[2] = {UOM_POUNDS, UOM_KILOGRAMS};
	int		iWeight;

	BOOL		bBadRead = FALSE;


	// Get the current weight from the scale
	memset(szBuf, '\0', sizeof(szBuf));
     strcpy(szWeight, "0.0");

     // DEBUG
//	R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, "About to call DLSendCmd");

	if (DLSendCmd(DL_GET_WEIGHT, sizeof(DL_GET_WEIGHT) - 1, szBuf))
	{
          // DEBUG
//	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, "DLSendCmd returned Success");

		// Check for the Prefix Byte
		if (szBuf[0] == 'S')
		{
			// See what the status of the scale is
			switch (szBuf[3])
			{
				case '0':
					//puts("Scale Stable with Weight");	// This return never happens
					break;
				case '1':
					strcpy(szErrMsg, "Scale is UnStable");
					bBadRead = TRUE;
					break;
				case '2':
					strcpy(szErrMsg, "Scale is Over Capacity");
					bBadRead = TRUE;
					break;
				case '3':
					strcpy(szErrMsg, "Scale Stable with Zero Weight");
				case '4':
					iWeight = atoi(szBuf + 4);
					if (bDLIsMetric)
						sprintf(szWeight, "%d.%02d", iWeight / 1000, iWeight % 1000);	// Kilograms have 3 decimal places
					else
						sprintf(szWeight, "%d.%02d", iWeight / 100, iWeight % 100);		// Pounds has 2 decimal places
					break;
				case '5':
					strcpy(szErrMsg, "Scale is Below Zero Weight");
					bBadRead = TRUE;
					break;
			}
		}
	}
	else
	{
          // DEBUG
	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, "DLSendCmd failed");

		strcpy(szErrMsg, "DLSendCmd for DL_GET_WEIGHT failed");
		bBadRead = TRUE;
	}

	// Format the response
     if (bBadRead)
     	sprintf(szResponse, "{\"result\":0,\"message\":\"%s\",\"weight\":%s,\"uom\":\"%d\"}", szErrMsg, szWeight, iUOM[bDLIsMetric]);
     else
     	sprintf(szResponse, "{\"result\":1,\"message\":\"\",\"weight\":%s,\"uom\":\"%d\"}", szWeight, iUOM[bDLIsMetric]);

     // DEBUG
//     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, "Returning");
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function sends the given command and reads the response.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
BOOL	DLSendCmd(char *szCmd, int iSize, char *szResponse)
{
     char      szBuf[MAX_PATH];

	int		iRetVal;
	int		iIndex = 0;


	// Send the Command to the Scanner Scale
	if (iSize)
		write(iDataLogicPort, szCmd, iSize);
	
	// Attempt to read the response to our command
//	szResponse[0] = '\0';
	memset(szBuf, '\0', sizeof(szBuf));
	do {
		iRetVal = read(iDataLogicPort, szBuf + iIndex, 1);
		if (szBuf[iIndex] == DL_TERM_CHAR)
		{
			szBuf[iIndex] = '\0';

			// Is this a barcode?
			if (! strncmp(szBuf, "08", 2))
			{
				memset(szDLBarcode, '\0', sizeof(szDLBarcode));
				strcpy(szDLBarcode, szBuf + 3);
				memset(szBuf, '\0', sizeof(szBuf));
				iIndex = 0;
				continue;
			}
			else
				break;
		}
		++iIndex;
	} while (iRetVal > 0);

     // Copy the local buffer to the response string
     strcpy(szResponse, szBuf);

	return(iIndex);
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function attempts to read a barcode scan from the scanner.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
BOOL DLReadBarcode(void)
{
	char		szBuf[MAX_PATH];

	int		iIndex = 0;
	int		iBytesPending;
	int		iRetVal;


	// See if we have any data pending to be read
	iRetVal = ioctl(iDataLogicPort, FIONREAD, &iBytesPending);
	if (! iRetVal && ! iBytesPending)
		return(FALSE);

     // If ioctl returned -1, the port handle is no longer valid and we need to restart
     if (iRetVal == -1)
     {
          // Log the error and exit so the R2ProcMgr can restart us
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, "DATALOGIC PORT FILE HANDLE IS INVALID!");
          bAppExiting = TRUE;
     }

	// Looks like we have data coming in, so read it and attempt to get the barcode out of it
	memset(szDLBarcode, '\0', sizeof(szDLBarcode));
	iRetVal = read(iDataLogicPort, szBuf, 1);
	if (iRetVal && szBuf[0] == 'S')
	{
		do {
			iRetVal = read(iDataLogicPort, szBuf + iIndex, 1);
			++iIndex;
		} while (iRetVal > 0 && szBuf[iIndex - 1] != DL_TERM_CHAR);
		szBuf[iIndex - 1] = '\0';
		strcpy(szDLBarcode, szBuf + 3);
		
		// DEBUG
		sprintf(szBuf, "Just got barcode [%s]", szDLBarcode);
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOGFILE, szBuf);
		return(TRUE);
	}

	return(FALSE);
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function attempts to send a barcode to the POS.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void DLSendBarcode(void)
{
	struct sockaddr_in  sa;

	int		iSocket;


	// Create a Socket
     if ((iSocket = socket(AF_INET, SOCK_STREAM, 0)) == -1)
     {
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, "Could not create socket");
          return;
     }

     // Init the sockaddr_in structure and attempt to connect
	memset(&sa, '\0', sizeof(sa));
     sa.sin_addr.s_addr = inet_addr(LOOPBACK_IP);
	sa.sin_family = AF_INET;
	sa.sin_port = htons(POS_BARCODE_PORT);

	// Connect to POS
	if (connect(iSocket, (struct sockaddr *)&sa, sizeof(struct sockaddr_in)))
	{
          close(iSocket);
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, "Connect Failed");
		return;
	}

	// Send the barcode
	if(send(iSocket, szDLBarcode, strlen(szDLBarcode) + 1, 0) < 0)
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, "Send Failed");

	// Close the socket
     close(iSocket);
}

/*
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function determines if the given barcode is in the item table.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
BOOL IsValidItem(char *szDLBarcode)
{
     PGresult       *res;

     char      szSQLCmd[MAX_PATH];

     int       iRowsReturned;
     int		iStrLen;


     // If this is a Price Embedded barcode, parse out the Price and Item UPC
     // Depending on how the scanner is configured, it can either start "2" and be 12 characters or "02" and be 13 characters
     iStrLen = strlen(szDLBarcode);
     if (iStrLen == PE_BARCODE_LENGTH1 && szDLBarcode[0] == PE_BARCODE_START1)
		sprintf(szSQLCmd, "SELECT Long_Desc FROM %s WHERE upc = '%4.4s'\n", ITEM_TABLE, szDLBarcode + 3);
	else if (iStrLen == PE_BARCODE_LENGTH2 && ! strncmp(szDLBarcode, PE_BARCODE_START2, 2))
		sprintf(szSQLCmd, "SELECT Long_Desc FROM %s WHERE upc = '%4.4s'\n", ITEM_TABLE, szDLBarcode + 4);
     else
		sprintf(szSQLCmd, "SELECT Long_Desc FROM %s WHERE upc = '%s'\n", ITEM_TABLE, szDLBarcode);

	// Since we are assuming at this point that it's an item, see if it's in the database
     res = PQexec(conn, szSQLCmd);
     if (PQresultStatus(res) != PGRES_TUPLES_OK)
	{
		R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, "UPC lookup failed");
     	PQclear(res);
		return(FALSE);
	}
	iRowsReturned = PQntuples(res);
	PQclear(res);

     // Did we get an item?
	if (! iRowsReturned)
		return(FALSE);

     return(TRUE);
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function makes the scale beep the "Not On File" error.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void BeepScale(void)
{
     char      szBuf[MAX_PATH];


	DLSendCmd(DL_NOT_ON_FILE, sizeof(DL_NOT_ON_FILE) - 1, szBuf);
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function enables the scanner.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void EnableScanner(void)
{
     char      szBuf[MAX_PATH];

	DLSendCmd(DL_ENABLE_SCANNER, sizeof(DL_ENABLE_SCANNER) - 1, szBuf);
}
*/

