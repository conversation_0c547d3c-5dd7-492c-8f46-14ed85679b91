#include "R2Helper.h"
#include "Globals.h"
#include </usr/include/libusb-1.0/libusb.h>    


// Defines used only in this module


// Globals used only in this module
char           szLine[MAX_LINE_LEN];


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Where everything starts and ends...
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
int main(int argc, char *argv[])
{
	R2DateTime 	dt;

	char			szBuf[128];

	INT4			iDayEndDate;

#ifdef RUN_AS_DAEMON
	pid_t 		pid;
	pid_t		sid;
#endif
	

     // Log that we started running and Init some variables
     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_NODB, "Begin Run");
     bAppExiting = FALSE;

	// Make sure we aren't already running
	if (InstanceCount(APP_NAME) > 1)
	{
		sprintf(szBuf, "\"%s\" is already running - Exiting", APP_NAME);
	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_ALL, szBuf);
		return(TRUE);
	}

     // Assign a handler for SIGTERM and SIGINT
     signal(SIGTERM, SignalHandler);
     signal(SIGINT, SignalHandler);

#ifdef RUN_AS_DAEMON
	// Fork off the parent process
	pid = fork();
	if (pid < 0)
	{
		sprintf(szBuf, "\"%s\" Fork off the parent process FAILED!", APP_NAME);
	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_ALL, szBuf);
		exit(EXIT_FAILURE);
	}
		
	// If we got a good PID, then we can exit the parent process.
	if (pid > 0)
		exit(EXIT_SUCCESS);

	// Change the file mode mask
	umask(0);

	// Create a new SID for the child process
	sid = setsid();
	if (sid < 0)
	{
		sprintf(szBuf, "\"%s\" Create a new SID for the child process FAILED!", APP_NAME);
	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_ALL, szBuf);
		exit(EXIT_FAILURE);
	}

	// Change the current working directory
	if ((chdir("/")) < 0)
	{
		sprintf(szBuf, "\"%s\" Change the current working directory FAILED!", APP_NAME);
	     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_ALL, szBuf);
	     exit(EXIT_FAILURE);
	}

	// Close out the standard file descriptors
	close(STDIN_FILENO);
	close(STDOUT_FILENO);
//	close(STDERR_FILENO);    // Need to leave this stream open for Docker error logging
#endif

     // Initialize the R2Helper
	if (argc > 1)
		StrLower(argv[1]);
     if (! InitR2Helper(argc, argv))
	     return(FALSE);

     // If we're being invoked to run a Day End, just do that and exit
/*     if (argc > 1 && ! strcmp(argv[1], "-dayend"))
     {
     	if (! argv[2])
     	{
     		printf("Usage: %s -dayend YYYY-MM-DD\n", argv[0]);
     	}
     	else
     	{
			iDayEndDate = SQLDateToInt4(argv[2]);
			PerformDayEnd(iDayEndDate);
     	}
          CleanUpR2Helper();
          exit(0);
     }*/

     // Start all the Child Threads
     InitThreads();

	// Main Loop
#ifdef RUN_AS_DAEMON
	while (! bAppExiting)
#else
	while (! kbhit() && ! bAppExiting)
#endif	// RUN_AS_DAEMON
	{
		// Perform whatever tasks are due to be performed
		NowToR2DT(&dt);
		DoSecondTasks(&dt);
		DoMinuteTasks(&dt);
		DoHourTasks(&dt);
		DoDailyTasks(&dt);

		// See if we've been requested to exit by the R2ProcMgr
		if (CheckForKillChildProcsFile())
		{
			bAppExiting = TRUE;
			break;
		}
			
		// Just to throttle how much CPU we're using (sleep a half second)
		usleep(500000);
	}

     // Close/free whatever we need to
	CleanUpThreads();
     CleanUpR2Helper();

     R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_NODB, "End Run");

     return(TRUE);
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function performs Tasks that are done once a Second.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void DoSecondTasks(R2DateTime *dt)
{
	static int		iLastSec = -1;

	if (dt->iSec != iLastSec)
	{
		iLastSec = dt->iSec;

		// See if there are any unexpected processes running or the lack of ones we do expect.
		CheckProcesses();

		// Check our Child Threads to make sure they're all still running
		CheckChildThreads();
	}
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function performs Tasks that are done once a Minute.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void DoMinuteTasks(R2DateTime *dt)
{
#ifdef RUN_AS_DAEMON
     PGresult       *res;

     char           szSQLCmd[MAX_PATH];
     char			szBuf[MAX_PATH];
	char			szDateTime[32];

	INT8			iHourAgo;
#endif	// RUN_AS_DAEMON

	static int		iLastMin = -1;


	if (dt->iMin != iLastMin)
	{
		iLastMin = dt->iMin;

		// Make sure we're not overheating
		CheckCPUTemp();

		// See if print jobs are backing up (printer issue)
		CheckForBackedUpPrintJobs();
		
		// If a Harddrive test was run, check the results
//		CheckSmartCtlTest();

// Only delete old Print Jobs/Print Job Triggers if we're running as a Daemon
#ifdef RUN_AS_DAEMON
		// It's probably safe to assume that if the Print Job is more than an hour old that nobody is expecting it
		// to print at this point.  So rather than cause confusion we just delete everything more than an hour old.
		iHourAgo = NowToInt8(TRUE) - MSECS_IN_AN_HOUR;
		Int8ToString(iHourAgo, I8TS_SQL_FMT, szDateTime);
		sprintf(szSQLCmd, "DELETE FROM Print_Job WHERE Created_At < '%s';", szDateTime);
		res = R2PQexec(cti[R2T_MAIN_THREAD].conn, szSQLCmd);
		if (PQresultStatus(res) != PGRES_COMMAND_OK)
		{
			sprintf(szBuf, "DELETE old Print Jobs failed: %s", PQerrorMessage(cti[R2T_MAIN_THREAD].conn));
			R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);
		}
		PQclear(res);
#endif	// RUN_AS_DAEMON
	}
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function performs Tasks that are done once an Hour.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void DoHourTasks(R2DateTime *dt)
{
	char			szBuf[MAX_PATH];
	
	int			iTotalMem;
	int			iMemAvail;

	static int		iLastHour = -1;


	if (dt->iHour != iLastHour)
	{
		// Set the computer's time from the internet
		GetInternetTime();
		
		// See if we're getting low on memory (less than 20% free)
		iMemAvail = GetAvailMemory(&iTotalMem);
		if ((double)iMemAvail <= (double)iTotalMem * 0.2)
		{
			sprintf(szBuf, "LOW MEMORY DETECTED: Total Memory = %dK, Memory Available = %dK", iTotalMem, iMemAvail);
			R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);
			
			// Do a ps -AF so we can determine who the memory hog is
			LogLinuxCommand(APP_NAME, PS_AF, ELM_LOG_SILENT);
		}
		
		iLastHour = dt->iHour;
	}
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function performs Tasks that are done Daily (at the Day Divide).
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void DoDailyTasks(R2DateTime *dt)
{
     PGresult      	*res;

     char           szBuf[128];

     INT8           iCurrDT;

     INT4           iToday;

     static int     iLastMinute = -1;


     // See if the minute has changed
     if (dt->iMin != iLastMinute)
     {
		// Log any network errors that have occurred
		CheckNetworkErrors();

          iCurrDT = NowToInt8(TRUE);
          iToday = TodayToInt4();

          // If we're past the last Date a Day End was run and we're past the Day Divide, Purge Old Data and do a Day End
          if (iToday > iLastDayEnd && (INT8)sysSet.iDayDivide < iCurrDT % DATE_OFFSET)
          {
               // Purge Old Data
               PurgeOldData();

               // Do a Day End
               PerformDayEnd(iToday - 1);

               // Run a harddrive self-test if we're not running on Raspbian (do the long test on the first of every month)
#ifndef RASPBERRYPI
//               RunSmartCtlTest((dt->iDay == 1) ? TRUE : FALSE);
#endif
               // Update the Last Day End date in the JSON table
               sprintf(szBuf, "UPDATE %s SET document='%d' WHERE Record_Key = '%s'", JSON_TABLE, iToday, KEY_DAY_END);
               res = R2PQexec(cti[R2T_MAIN_THREAD].conn, szBuf);
               if (PQresultStatus(res) != PGRES_COMMAND_OK)
               {
                    sprintf(szBuf, "Unable to Update '%s' Record, %s", KEY_DAY_END, PQerrorMessage(cti[R2T_MAIN_THREAD].conn));
		          R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);
               }
               iLastDayEnd = iToday;

               // Attempt to update Linux (apt-get update, apt-get upgrade)
			// This was removed because Linux updates can update PostgreSQL, which causes all sorts of problems.
			// The solution is that we update the Docker container as a whole via the CRM
//               UpdateLinux();

          	// Log our current memory usage for diagnostic purposes
          	LogLinuxCommand(APP_NAME, PS_GREP_R2, ELM_LOG_SILENT);
          	
			// See if we have any unexpected users
			CheckUsers();
          }

          // Update the last minute so we don't come back here until the minute changes
          iLastMinute = dt->iMin;
     }
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function is the called when the application is being shut down by Ctrl-C (or the OS is rebooting).
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void SignalHandler(int iSigNum)
{
	char		szBuf[MAX_PATH];
	
     // Log that we were asked to terminate and then set bAppExit so we shut down clean
     sprintf(szBuf, "Recieved Signal %d, Exiting", iSigNum);
	R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);

	bAppExiting = TRUE;
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// This function verifies that all our Child Threads are still running and responsive.
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void CheckChildThreads(void)
{
	char		szBuf[MAX_PATH];

	int		iTest = 0;
	int		iRetVal;
	int		iVar;


	for (iVar = 0; iVar < MAX_R2_THREADS; iVar++)
	{
		// Only check on a thread if it's active
		if (cti[iVar].bThreadActive)
		{
			// If the Flag isn't set, increment the No Checkin Count
			if (! cti[iVar].bCheckin)
			{
				++cti[iVar].iNoChkinCnt;
				if (cti[iVar].iNoChkinCnt > MAX_MISSED_CHKINS)
				{
					sprintf(szBuf, "Thread %s has Failed to Check In - Restarting!", cti[iVar].szThreadDesc);
					R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);

					bAppExiting = TRUE;		// Just exit R2Helper, because the R2ProcMgr will restart us

/*
					// Attempt to kill the child thread in case it's hung
					if ((iRetVal = pthread_cancel(cti[iVar].thrd)))
					{
						sprintf(szBuf, "Error calling pthread_cancel for Thread %s, %s", cti[iVar].szThreadDesc, strerror(errno));
						R2LogError(APP_NAME, __LINE__, __FILE__, __func__, 0, ELM_LOG_SILENT, szBuf);
					}

					// Restart the hung/crashed Child Thread
					if (pthread_create(&cti[iVar].thrd, NULL, (void *)cti[iVar].ThreadFunc, (void *) &iTest) != 0)
					{
						sprintf(szBuf, "pthread_create failed for Child Thread %s", cti[iVar].szThreadDesc);
						R2LogError(APP_NAME, __LINE__, __FILE__, __func__, errno, ELM_LOG_SILENT, szBuf);
					}*/
				}
			}
			else
			{
				// The Thread appears to have Checked In, so reset the Checked In Flag and Missed Count
				pthread_mutex_lock(&cti[iVar].mutex);
				cti[iVar].bCheckin = FALSE;
				pthread_mutex_unlock(&cti[iVar].mutex);
				cti[iVar].iNoChkinCnt = 0;
			}
		}
	}
}

