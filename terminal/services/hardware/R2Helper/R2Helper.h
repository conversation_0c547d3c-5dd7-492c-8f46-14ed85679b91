#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h> 		// Contains file controls like O_RDWR
#include <termios.h> 	// Contains POSIX terminal control definitions
#include <unistd.h> 	// write(), read(), close()
#include <libgen.h>
#include <netdb.h>
#include <netinet/in.h>
#include <signal.h>
#include <stdarg.h>
#include <time.h>
#include <arpa/inet.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/types.h>

// Header file for libUSB
#include <libusb-1.0/libusb.h>

// Header file for PostgreSQL
#include <libpq-fe.h>

// Header file for the json-c library
#include <json-c/json.h>

// R2POS Includes
#include <Project.h>
#include <libR2.h>
#include <Epson.h>

#include <Department.h>
#include <PrintJob.h>
#include <Room.h>
#include <Sale.h>
#include <SystemDevice.h>
#include <SystemSetting.h>


// Defines that change program behavior
//#define PRN_DEBUG_OUTPUT			// Writes printer specific debug info to the log file

// General Defines
#define APP_NAME				"R2Helper"
#define PS_GREP_R2				"/bin/ps -AF | /bin/grep R2"
#define PS_AF					"/bin/ps -AF"
#ifdef RASPBERRYPI
#define GET_CPU_TEMPS			"/usr/bin/vcgencmd measure_temp"
#else
#define GET_CPU_TEMPS			"/usr/bin/sensors"
#endif
#define GET_DISK_FREE			"/bin/df"
#define GET_WHOAMI				"/usr/bin/whoami"
#define GET_WHO				"/usr/bin/who"
#define GET_NETWORK_ERRORS		"/bin/netstat -i"
//#define SMARTCTL_INIT			"/usr/sbin/smartctl --smart=on -a /dev/sda"
//#define SMARTCTL_SHORT_TEST		"/usr/sbin/smartctl -t short /dev/sda"
//#define SMARTCTL_LONG_TEST		"/usr/sbin/smartctl -t long /dev/sda"
//#define SMARTCTL_TEST_RESULTS		"/usr/sbin/smartctl -l selftest /dev/sda"
#define UPDATE_LINUX			"/usr/bin/apt-get update"
#define UGRADE_LINUX			"/usr/bin/apt-get upgrade -y"
#define GET_FREE_MEMORY			"/usr/bin/free -m"
#define REBOOT_COMMAND			"/sbin/reboot &"	// The & on the end tells the OS not to block for the "system" call to complete
#define LOW_MEMORY_LIMIT			0.25				// Minimum % of memory that should be Available (25%)
#define LOW_DISK_LIMIT			95				// Percent of Disk Space Full before we start logging warnings.

#define BUFFER_SIZE				4096		// Size of String Buffer(s) used for Json packets
#define MAX_LINE_LEN          	1024
#define MAX_DATA_PTRS			20

// Defines for R2 Child Threads
#define R2T_MAIN_THREAD			0	// Main Thread - Only here because it needs a DB connection
#define R2T_LED_REQUESTS			1	// Thread that handles LED lighting requests
#define R2T_RCPT_PRINT_JOBS		2	// Thread that looks for Receipt Printer Print Jobs in the PrintJobs table
#define R2T_PREP_PRINT_JOBS		3	// Thread that looks for Prep Printer Print Jobs in the PrintJobs table
#define R2T_RT_LAN_PRINT_REQ		4	// Thread that accepts Real-Time Printer Requests from the LAN
#define R2T_RT_LOOP_PRINT_REQ		5	// Thread that accepts Real-Time Printer Requests on the Loopback IP
#define R2T_DB_UPDATES			6	// Thread that handles database updates from the Server
#define MAX_R2_THREADS			7

// Defines for PostgreSQL Column Data Types (for some reason pg_type.h is nowhere to be found!)
#define OID_BOOLEAN				16
#define OID_BYTEA				17
#define OID_SMALLINT			21
#define OID_INTEGER				23
#define OID_TEXT				25
#define OID_JSON				114
#define OID_VARCHAR				1043
#define OID_TIMESTAMP			1114
#define OID_TIMESTAMP_TZ			1184		// Timestamp with Time Zone
#define OID_UUID				2950
#define OID_JSONB				3802

#define MAX_MISSED_CHKINS		10	// Number of missed checkins for a Thread that will trigger a restart of that thread.

#define BOOL                       int


// Typedef's
typedef struct {
	char 	szProcName[MAX_PATH];
	int		iMemUsed;
} KnownProcs;

typedef struct {
	pthread_t 		thrd;
	void 			(*ThreadFunc)(int *unused);
	char 			*szThreadDesc;
	pthread_mutex_t	mutex;
	BOOL				bCheckin;
	int				iNoChkinCnt;
	BOOL				bThreadActive;
	PGconn        		*conn;
	BOOL				bNeedsDB;		// If this is FALSE we don't init a DB connection for this thread
} ChildThreadInfo;

typedef struct {
	char 		*ptr;
	int			iSize;
} R2PrintStream;

typedef struct {
     char		*pBigStr;
     int		iSize;
} BigString;


// Functions found in R2Helper.c (aside from main())
void DoSecondTasks(R2DateTime *dt);
void DoMinuteTasks(R2DateTime *dt);
void DoHourTasks(R2DateTime *dt);
void DoDailyTasks(R2DateTime *dt);
void SignalHandler(int iSigNum);
void CheckChildThreads(void);

// Functions found in DBUpdates.c
void HandleDBUpdates(int *unused);
BOOL ProcessUpdate(void);
BOOL GetPrimaryKey(char *szTable, char *szPrimKey);
BOOL InsertSQLRecord(PGconn *svrConn, PGresult *resSvr, char *szPrimKey);
BOOL UpdateSQLRecord(PGconn *svrConn, PGresult *resSvr, char *szPrimKey);
BOOL ArpPing(char *szIP);

// Functions found in Functions.c
BOOL InitR2Helper(int argc, char *argv[]);
void CleanUpR2Helper(void);
PGresult *R2PQexec(PGconn *conn, const char *query);
void InitThreads(void);
void CleanUpThreads(void);
void CheckCPUTemp(void);
void CheckNetworkErrors(void);
//void InitSmartCtl(void);
//void RunSmartCtlTest(BOOL bLongTest);
//void CheckSmartCtlTest(void);
int GetAvailMemory(int *iTotalMem);
void CheckDiskFree(void);
void CheckUsers(void);
void GetInternetTime(void);
void CheckForBackedUpPrintJobs(void);
libusb_device_handle * LIBUSB_CALL R2libusb_open_device_with_vid_pid(libusb_context *ctx, uint16_t vendor_id, uint16_t product_id);

// Functions found in LEDs.c
void HandleLEDRequests(int *unused);
void DoMoodLight(void);
void AdjustColor(int *iColor, char *iColorDir);

// Functions found in LogicControlsVDU.c
BOOL HandleLogicControlsPJ(PrintJob *pj, char *szUuid);
void AddToBigStr(BigString *bs, const char *fmt, ...);
void AddStringToBigStr(BigString *bs, const char *szString);
void FreeBigStr(BigString *bs);
BOOL LoadRooms(PGconn *conn, RoomStruct *room, char *szErrMsg);
char *EscapeXMLString(char *szSrcStr);

// Functions found in MiscPrinters.c
BOOL OpenD10USBPrinter(void);
int	ReadD10USBPrinter(char *data, int iSize);
int	WriteD10USBPrinter(char *data, int iSize);
void CloseD10USBPrinter(void);
void PrintSeikoQRCode(char *szQRText);
BOOL OpenR180USBPrinter(void);
int	ReadR180USBPrinter(char *data, int iSize);
int	WriteR180USBPrinter(char *data, int iSize);
void CloseR180USBPrinter(void);

// Functions found in PrepPrintJobs.c
void HandlePrepPrintJobs(int *unused);
BOOL PrintPrepPrintJob(char *szJson, PrintJob *pj, char *szUuid);
void HandleExistingPrepPrintJobs(void);
BOOL OpenNullPrepPrinter(int);
int	ReadNullPrepPrinter(char *data, int iSize);
int	WriteNullPrepPrinter(char *data, int iSize);
void CloseNullPrepPrinter(void);
BOOL OpenU220IPPrinter(int);
int ReadU220IPPrinter(char *data, int iSize);
int WriteU220IPPrinter(char *data, int iSize);
void CloseU220IPPrinter(void);
void MakeAsteriskMsg(char *szMsg, int iLineLen, char *szDestMsg);
BOOL UpdatePJReprint(char *szJson, PrintJob *pj, char *szUuid);

// Functions found in PrintRequest.c
void HandleLanPrintRequests(int *unused);
void HandleLoopPrintRequests(int *unused);
int ParsePrnRequest(char *szRequest, char *szResponse);
void PerformPrnRequest(PGconn *conn, int iReqNum, char *szResponse);
int GetPrnStatus(char *cDrawerStat, char *cPaperStat, int *iCtsStatus, char *szErrMsg);
BOOL GetKeyCodeList(char *szList, char *szErrMsg);
void DeleteAllNVLogos(void);
void UpdatePrnLogos(PGconn *conn);
void StoreNVLogo(char *szKeyCode);
BOOL AddDataToPrintStream(R2PrintStream *r2hs, char *sData, int iSize);
BOOL AddStringToPrintStream(R2PrintStream *r2hs, char *szString);
BOOL SprintfToPrintStream(R2PrintStream *r2hs, const char *fmt, ...);
void SendPSToPrinter(R2PrintStream *r2hs);
BOOL AddBGFileToGPB(char *szBGFile, int iJustify);
BOOL AddBGImageToGPB(char *szBGFile, int iSize, int iJustify);
BOOL LoadBitGraphic(char *szFile, BitGraphic *bg);
void OpenCashDrawer(int iDrawerIdx);
int R2DecodePQHex(char *szHexStr, char *szResult);
void strupr(char *p);
void strlwr(char *p);

// Functions found in RcptPrintJobs.c
void HandleRcptPrintJobs(int *unused);
BOOL JsonToPrintJob(char *szJson, BOOL bWantDT, char *szUuid, PrintJob *pj);
BOOL PrintRcptPrintJob(PrintJob *pj);
BOOL UpdatePJRetries(PGconn *conn, char *szJson, PrintJob *pj, char *szUuid);
char *R2EscapeString(PGconn *conn, const char *szStr);
BOOL DeletePrintJob(PGconn *conn, char *szUuid);
void HandleExistingRcptPrintJobs(void);
BOOL OpenNullRcptPrinter(void);
int	ReadNullRcptPrinter(char *data, int iSize);
int	WriteNullRcptPrinter(char *data, int iSize);
void CloseNullRcptPrinter(void);
BOOL OpenT88USBPrinter(void);
int	ReadT88USBPrinter(char *data, int iSize);
int	WriteT88USBPrinter(char *data, int iSize);
void CloseT88USBPrinter(void);
BOOL OpenT88SerialPrinter(void);
int	ReadT88SerialPrinter(char *data, int iSize);
int	WriteT88SerialPrinter(char *data, int iSize);
void CloseT88SerialPrinter(void);
void PrintNVLogo(char *szKeyCode);
BOOL IsOldPrintJob(char *szDateTime);

// Functions found in ScheduledTasks.c
void PurgeOldData(void);
void PerformDayEnd(INT4 iDayEndDate);

// Functions found in StarMicronics.c
BOOL OpenTSP654USBPrinter(void);
int	ReadTSP654USBPrinter(char *data, int iSize);
int	WriteTSP654USBPrinter(char *data, int iSize);
void CloseTSP654USBPrinter(void);

// Functions found in WatchProcesses.c
void LoadProcessTable(void);
BOOL LoadProcessList(void);
void CheckProcesses(void);
int ParseBufferFlds(char *dataBuf, char **szFields);
void SafeFree(int iLine, const char *szFunc, char *ptr);


