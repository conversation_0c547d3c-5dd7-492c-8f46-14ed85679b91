CREATE OR REPLACE FUNCTION public.get_sales_by_range(
    start_date timestamp without time zone,
    end_date timestamp without time zone,
    terminal_index integer
)
RETURNS SETOF sales_by_range_view
LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  WITH sale_rows AS (
    SELECT
      sale.sale,
      sale.sale_number,
      sale.end_at,
      sale.suspended,
      jsonb_array_elements(sale.document -> 'saleRows') AS row_item
    FROM
      sale
    WHERE
      end_at >= start_date
      AND end_at < end_date
      AND ((sale.document -> 'saleHeader' -> 'saleFlags') @> '[0]'::jsonb)
      AND CASE
        WHEN cast(terminal_index AS integer) IS NOT NULL THEN
          cast((sale.document -> 'saleHeader' ->> 'settleTerminalNumber') AS integer) = terminal_index
        ELSE
          cast((sale.document -> 'saleHeader' ->> 'settleTerminalNumber') AS integer) IS NOT NULL
      END
    ORDER BY
      sale.end_at
  ),
  sale_items AS (
    SELECT
      row_item -> 'department' AS department,
      sum(CASE
        WHEN row_item -> 'flags' @> '[14]' THEN 0
        ELSE cast(row_item -> 'qty' AS integer)
      END) AS count,
      row_item -> 'item' AS item_uuid,
      sum(cast(row_item -> 'actualPrice' AS integer)) AS item_price,
      sum(cast(row_item -> 'grossPrice' AS integer)) AS gross_price,
      row_item -> 'receiptDescription' AS item
    FROM
      sale_rows
    WHERE
      NOT (row_item -> 'flags' @> '[7]')
      AND NOT (row_item -> 'flags' @> '[10]')
    GROUP BY
      row_item -> 'department',
      row_item -> 'item',
      row_item -> 'receiptDescription'
    ORDER BY
      row_item -> 'department'
  ),
  sale_items_refunded AS (
    SELECT
      row_item -> 'department' AS department,
      sum(CASE
        WHEN row_item -> 'flags' @> '[14]' THEN 0
        ELSE cast(row_item -> 'qty' AS integer)
      END) AS count,
      row_item -> 'item' AS item_uuid,
      sum(cast(row_item -> 'actualPrice' AS integer)) AS item_price,
      sum(cast(row_item -> 'grossPrice' AS integer)) AS gross_price,
      row_item -> 'receiptDescription' AS item
    FROM
      sale_rows
    WHERE
      row_item -> 'flags' @> '[7]'
    GROUP BY
      row_item -> 'department',
      row_item -> 'item',
      row_item -> 'receiptDescription'
    ORDER BY
      row_item -> 'department'
  ),
  data_rows AS (
    SELECT * FROM sale_items
    UNION
    SELECT * FROM sale_items_refunded
  )
  SELECT 
    item,
    count,
    department,
    gross_price,
    item_price AS price,
    trunc(
      count::numeric / (
        SELECT sum(count) FROM data_rows
      ) * 100, 2
    ) AS percentage
  FROM data_rows
  ORDER BY 1, 4 DESC;
END;
$function$;
