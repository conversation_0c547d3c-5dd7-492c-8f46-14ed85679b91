FROM golang:1.23.10-alpine3.22 AS builder

RUN apk add --no-cache gcc musl-dev

WORKDIR /go/src/app

COPY go.mod go.sum ./
RUN go mod download
COPY . .

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build -ldflags="-w -s" -o /go/bin/payments cmd/api/main.go

FROM scratch
ENV ENV=production

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /go/bin /go/bin

ENTRYPOINT [ "/go/bin/payments" ]