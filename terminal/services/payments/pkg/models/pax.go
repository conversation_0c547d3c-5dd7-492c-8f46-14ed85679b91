package models

import (
	"strings"
)

type ResponseAccountInfo struct {
	AccountNo            string
	EntryMode            string
	Exp                  string
	EBTType              string
	VoucherNo            string
	NewAccountNo         string
	CardType             string
	CardHolder           string
	CVDApprovalCode      string
	CVDMessage           string
	CardPresentIndicator string
	GiftCardType         string
	DebitAccountType     string
	HostAccount          string
	HostCardType         string
	Track1Data           string
	Track2Data           string
	Track3Data           string
}

func DecodeResponseAccountInfo(data string, seperator byte) (ResponseAccountInfo, error) {
	info := ResponseAccountInfo{}
	parts := strings.Split(data, string(seperator))
	for i, part := range parts {
		switch i {
		case 0:
			info.AccountNo = part
		case 1:
			info.EntryMode = part
		case 2:
			info.Exp = part
		case 3:
			info.EBTType = part
		case 4:
			info.VoucherNo = part
		case 5:
			info.NewAccountNo = part
		case 6:
			info.CardType = part
		case 7:
			info.CardHolder = part
		case 8:
			info.CVDApprovalCode = part
		case 9:
			info.CVDMessage = part
		case 10:
			info.CardPresentIndicator = part
		case 11:
			info.GiftCardType = part
		case 12:
			info.DebitAccountType = part
		case 13:
			info.HostAccount = part
		case 14:
			info.HostCardType = part
		case 15:
			info.Track1Data = part
		case 16:
			info.Track2Data = part
		case 17:
			info.Track3Data = part
		}
	}

	return info, nil
}

type ResponseHostInfo struct {
	HostResponseCode          string
	HostResponseMessage       string
	AuthCode                  string
	HostReferenceNo           string
	TraceNo                   string
	BatchNo                   string
	TransactionID             string
	GatewayTransactionID      string
	HostDetailedMessage       string
	TransactionIntegrityClass string
	RetrievalReferenceNo      string
	IssuerResponseCode        string
	PaymentAccountRefID       string
}

func DecodeResponseHostInfo(data string, seperator byte) (ResponseHostInfo, error) {
	info := ResponseHostInfo{}
	parts := strings.Split(data, string(seperator))
	for i, part := range parts {
		switch i {
		case 0:
			info.HostResponseCode = part
		case 1:
			info.HostResponseMessage = part
		case 2:
			info.AuthCode = part
		case 3:
			info.HostReferenceNo = part
		case 4:
			info.TraceNo = part
		case 5:
			info.BatchNo = part
		case 6:
			info.TransactionID = part
		case 7:
			info.GatewayTransactionID = part
		case 8:
			info.HostDetailedMessage = part
		case 9:
			info.TransactionIntegrityClass = part
		case 10:
			info.RetrievalReferenceNo = part
		case 11:
			info.IssuerResponseCode = part
		case 12:
			info.PaymentAccountRefID = part
		}
	}

	return info, nil
}

type ResponseTraceInfo struct {
	TransactionNo         string
	ECRRefNo              string
	TimeStamp             string
	InvNo                 string
	PS2000                string
	AuthorizationResponse string
	ECRTransID            string
	HostTimeStamp         string
}

func DecodeResponseTraceInfo(data string, seperator byte) (ResponseTraceInfo, error) {
	info := ResponseTraceInfo{}
	parts := strings.Split(data, string(seperator))
	for i, part := range parts {
		switch i {
		case 0:
			info.TransactionNo = part
		case 1:
			info.ECRRefNo = part
		case 2:
			info.TimeStamp = part
		case 3:
			info.InvNo = part
		case 4:
			info.PS2000 = part
		case 5:
			info.AuthorizationResponse = part
		case 6:
			info.ECRTransID = part
		case 7:
			info.HostTimeStamp = part
		}
	}

	return info, nil
}

type ResponseAmountInfo struct {
	ApprovedAmount             string
	AmountDue                  string
	TipAmount                  string
	CashBackAmount             string
	MerchantFee                string
	TaxAmount                  string
	Balance1                   string
	Balance2                   string
	ServiceFee                 string
	TransactionRemainingAmount string
	ApprovedTipAmount          string
	ApprovedCashBackAmount     string
	ApprovedMerchantFee        string
	ApprovedTaxAmount          string
}

func DecodeResponseAmountInfo(data string, seperator byte) (ResponseAmountInfo, error) {
	info := ResponseAmountInfo{}
	parts := strings.Split(data, string(seperator))
	for i, part := range parts {
		switch i {
		case 0:
			info.ApprovedAmount = part
		case 1:
			info.AmountDue = part
		case 2:
			info.TipAmount = part
		case 3:
			info.CashBackAmount = part
		case 4:
			info.MerchantFee = part
		case 5:
			info.TaxAmount = part
		case 6:
			info.Balance1 = part
		case 7:
			info.Balance2 = part
		case 8:
			info.ServiceFee = part
		case 9:
			info.TransactionRemainingAmount = part
		case 10:
			info.ApprovedTipAmount = part
		case 11:
			info.ApprovedCashBackAmount = part
		case 12:
			info.ApprovedMerchantFee = part
		case 13:
			info.ApprovedTaxAmount = part
		}
	}

	return info, nil
}

type ResponseAdditionalInformation struct {
	EDCType       string
	CreditCardBin string
	ProgramType   string
	SerialNo      string
	GlobalUUID    string
	TC            string
	TVR           string
	AID           string
	TSI           string
	ATC           string
	AppLab        string
	IAD           string
	ARC           string
	CID           string
	CVM           string
	UserLang      string
}

func DecodeResponseAdditionalInformation(data string, seperator byte) (ResponseAdditionalInformation, error) {
	info := ResponseAdditionalInformation{}
	parts := strings.Split(data, string(seperator))
	for i, part := range parts {
		switch i {
		case 1:
			info.EDCType = strings.ReplaceAll(part, "EDCTYPE=", "")
		case 2:
			info.CreditCardBin = strings.ReplaceAll(part, "CARDBIN=", "")
		case 3:
			info.ProgramType = strings.ReplaceAll(part, "PROGRAMTYPE=", "")
		case 4:
			info.SerialNo = strings.ReplaceAll(part, "SN=", "")
		case 5:
			info.GlobalUUID = strings.ReplaceAll(part, "GLOBALUID=", "")
		case 6:
			info.TC = strings.ReplaceAll(part, "TC=", "")
		case 7:
			info.TVR = strings.ReplaceAll(part, "TVR=", "")
		case 8:
			info.AID = strings.ReplaceAll(part, "AID=", "")
		case 9:
			info.TSI = strings.ReplaceAll(part, "TSI=", "")
		case 10:
			info.ATC = strings.ReplaceAll(part, "ATC=", "")
		case 11:
			info.AppLab = strings.ReplaceAll(part, "APPLAB=", "")
		case 12:
			info.IAD = strings.ReplaceAll(part, "IAD=", "")
		case 13:
			info.ARC = strings.ReplaceAll(part, "ARC=", "")
		case 14:
			info.CID = strings.ReplaceAll(part, "CID=", "")
		case 15:
			info.CVM = strings.ReplaceAll(part, "CVM=", "")
		case 16:
			info.UserLang = strings.ReplaceAll(part, "userLanguageStatus=", "")
		}
	}

	return info, nil
}

type RequestAccountInfo struct {
	AccountNo       string
	Exp             string
	CVV             string
	EBTType         string
	VoucherNo       string
	DupeOverride    string
	FirstName       string
	LastName        string
	CountryCode     string
	State           string
	City            string
	Email           string
	GiftCardType    string
	CVVBypassReason string
	GiftTenderType  string
}

func (a *RequestAccountInfo) Encode(seperator byte) string {
	return strings.Join([]string{
		a.AccountNo,
		a.Exp,
		a.CVV,
		a.EBTType,
		a.VoucherNo,
		a.DupeOverride,
		a.FirstName,
		a.LastName,
		a.CountryCode,
		a.State,
		a.City,
		a.Email,
		a.GiftCardType,
		a.CVVBypassReason,
		a.GiftTenderType,
	}, string(seperator))
}

type RequestAmountInfo struct {
	TransactionAmount string
	TipAmount         string
	CashBackAmount    string
	MerchantFee       string
	TaxAmount         string
	FuelAmount        string
	ServiceFee        string
}

func (a *RequestAmountInfo) Encode(seperator byte) string {
	return strings.Join([]string{
		a.TransactionAmount,
		a.TipAmount,
		a.CashBackAmount,
		a.MerchantFee,
		a.TaxAmount,
		a.FuelAmount,
		a.ServiceFee,
	}, string(seperator))
}

type RequestTraceInfo struct {
	ECRRefNo         string
	InvNo            string
	AuthCode         string
	TransactionNo    string
	TimeStamp        string
	ECRTransID       string
	OrigECRRefNo     string
	OrigPS2000       string
	OrigAuthResponse string
	OrigTraceNo      string
	OrigTransID      string
}

func (a *RequestTraceInfo) Encode(seperator byte) string {
	return strings.Join([]string{
		a.ECRRefNo,
		a.InvNo,
		a.AuthCode,
		a.TransactionNo,
		a.TimeStamp,
		a.ECRTransID,
		a.OrigECRRefNo,
		a.OrigPS2000,
		a.OrigAuthResponse,
		a.OrigTraceNo,
		a.OrigTransID,
	}, string(seperator))
}

type Response struct {
	Status          string
	Command         string
	Version         string
	ResponseCode    string
	ResponseMessage string
	AdditionalData  []string
}

type TransactionResponse struct {
	Response

	HostInformation       ResponseHostInfo
	TransactionType       string
	AmountInformation     ResponseAmountInfo
	AccountInformation    ResponseAccountInfo
	TraceInformation      ResponseTraceInfo
	AVSInformation        string
	CommercialInformation string
	MOTOEcommerce         string
	AdditionalInformation ResponseAdditionalInformation
	VASInformation        string
	TORInformation        string
	HostCredential        string
	PayloadData           string
}

type NMIResponse struct {
	Response
}
